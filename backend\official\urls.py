from django.urls import path
from . import views

urlpatterns = [
    # Official Sent Certificate URLs
    path('official/sent/', views.OfficialSentListCreateView.as_view(), name='official-sent-list-create'),
    path('official/sent/<int:pk>/', views.OfficialSentDetailView.as_view(), name='official-sent-detail'),
    
    # Official Received Certificate URLs
    path('official/received/', views.OfficialReceivedListCreateView.as_view(), name='official-received-list-create'),
    path('official/received/<int:pk>/', views.OfficialReceivedDetailView.as_view(), name='official-received-detail'),
    
    # Statistics and Search URLs
    path('official/statistics/', views.official_statistics, name='official-statistics'),
    path('official/search/', views.search_by_tracking_number, name='official-search-tracking'),

    # Public URLs for tracking (no authentication required)
    path('public/official/sent/', views.PublicOfficialSentListView.as_view(), name='public-official-sent-list'),
    path('public/official/received/', views.PublicOfficialReceivedListView.as_view(), name='public-official-received-list'),
    path('public/official/stats/', views.public_certificate_stats, name='public-certificate-stats'),
    path('public/official/search/', views.public_search_by_tracking_number, name='public-official-search-tracking'),
]
