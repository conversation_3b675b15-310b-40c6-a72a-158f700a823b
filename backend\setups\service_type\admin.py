from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import ServiceType


@admin.register(ServiceType)
class ServiceTypeAdmin(admin.ModelAdmin):
    """Admin interface for ServiceType model."""
    
    list_display = [
        'name', 'fee_display', 'url_display', 'is_active_display', 'document_types_count_display',
        'active_document_types_count_display', 'created_at', 'updated_at'
    ]
    
    list_filter = [
        'is_active', 'created_at', 'updated_at', 'document_types'
    ]
    
    search_fields = [
        'name', 'description', 'document_types__name'
    ]
    
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'document_types_count',
        'active_document_types_count'
    ]
    
    filter_horizontal = ['document_types']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'name', 'description', 'fee', 'url', 'is_active')
        }),
        ('Associated Document Types', {
            'fields': ('document_types', 'document_types_count', 'active_document_types_count'),
            'description': 'Select certificate types that are associated with this service type.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ['name']
    list_per_page = 25
    
    def fee_display(self, obj):
        """Display fee with currency symbol."""
        return f"{obj.fee:,.2f} ETB"
    fee_display.short_description = 'Fee'
    fee_display.admin_order_field = 'fee'

    def url_display(self, obj):
        """Display URL with link if available."""
        if obj.url:
            return format_html(
                '<a href="{}" target="_blank" style="color: blue;">🔗 Link</a>',
                obj.url
            )
        return format_html('<span style="color: gray;">No URL</span>')
    url_display.short_description = 'URL'
    url_display.admin_order_field = 'url'
    
    def is_active_display(self, obj):
        """Display active status with colored indicator."""
        if obj.is_active:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Active</span>'
            )
        else:
            return format_html(
                '<span style="color: red; font-weight: bold;">✗ Inactive</span>'
            )
    is_active_display.short_description = 'Status'
    is_active_display.admin_order_field = 'is_active'
    
    def document_types_count_display(self, obj):
        """Display count of associated document types."""
        count = obj.document_types_count
        if count > 0:
            return format_html(
                '<span style="color: blue; font-weight: bold;">{}</span>',
                count
            )
        return '0'
    document_types_count_display.short_description = 'Document Types'
    
    def active_document_types_count_display(self, obj):
        """Display count of active associated document types."""
        count = obj.active_document_types_count
        total = obj.document_types_count
        if total > 0:
            if count == total:
                color = 'green'
            elif count > 0:
                color = 'orange'
            else:
                color = 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{}/{}</span>',
                color, count, total
            )
        return '0/0'
    active_document_types_count_display.short_description = 'Active/Total'
    
    def get_queryset(self, request):
        """Optimize queryset with prefetch_related."""
        return super().get_queryset(request).prefetch_related('document_types')
    
    actions = ['activate_service_types', 'deactivate_service_types']
    
    def activate_service_types(self, request, queryset):
        """Bulk activate service types."""
        updated = queryset.update(is_active=True)
        self.message_user(
            request,
            f'{updated} service type(s) were successfully activated.'
        )
    activate_service_types.short_description = 'Activate selected service types'
    
    def deactivate_service_types(self, request, queryset):
        """Bulk deactivate service types."""
        updated = queryset.update(is_active=False)
        self.message_user(
            request,
            f'{updated} service type(s) were successfully deactivated.'
        )
    deactivate_service_types.short_description = 'Deactivate selected service types'
