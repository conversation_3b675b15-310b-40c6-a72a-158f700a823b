#!/usr/bin/env python
"""
Test script for ServiceRequest API endpoints.
"""
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def get_auth_headers():
    """Create a test user and get JW<PERSON> token for authentication."""
    try:
        # Try to get existing test user
        user = User.objects.get(username='testuser')
    except User.DoesNotExist:
        # Create test user
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    # Generate JWT token
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)

    return {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

def test_api_endpoints():
    """Test the ServiceRequest API endpoints."""
    base_url = 'http://localhost:8000/api'
    headers = get_auth_headers()
    
    print("🧪 Testing ServiceRequest API Endpoints")
    print("=" * 50)
    
    # Test 1: Get lookup data (should work without auth)
    print("\n1. Testing Lookup Endpoints (No Auth Required)")
    
    lookup_endpoints = [
        '/lookups/service-types/',
        '/lookups/admission-types/',
        '/lookups/study-programs/',
        '/lookups/colleges/',
        '/lookups/departments/',
        '/lookups/certificate-types/'
    ]
    
    for endpoint in lookup_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"   ✅ {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: Error - {e}")
    
    # Test 2: Get service requests (requires auth)
    print("\n2. Testing Service Requests Endpoint (Auth Required)")
    
    try:
        response = requests.get(f"{base_url}/service-requests/", headers=headers)
        print(f"   ✅ /service-requests/: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 Found {len(data.get('results', []))} service requests")
    except Exception as e:
        print(f"   ❌ /service-requests/: Error - {e}")
    
    # Test 3: Get statistics (requires auth)
    print("\n3. Testing Statistics Endpoint (Auth Required)")
    
    try:
        response = requests.get(f"{base_url}/service-requests/statistics/", headers=headers)
        print(f"   ✅ /service-requests/statistics/: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 Statistics: {json.dumps(data, indent=2)}")
    except Exception as e:
        print(f"   ❌ /service-requests/statistics/: Error - {e}")
    
    # Test 4: Test without authentication (should fail)
    print("\n4. Testing Without Authentication (Should Fail)")
    
    try:
        response = requests.get(f"{base_url}/service-requests/")
        print(f"   ✅ Expected failure: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 API Endpoint Tests Completed")

if __name__ == '__main__':
    test_api_endpoints()
