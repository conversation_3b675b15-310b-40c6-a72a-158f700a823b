"""
Enhanced security middleware for Django applications
"""
import time
import json
import hashlib
import logging
from typing import Dict, Any, Optional
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.core.cache import cache
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.middleware.csrf import get_token
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import ipaddress

logger = logging.getLogger(__name__)

class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Add comprehensive security headers to all responses
    """
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        # Security headers
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        }
        
        # Content Security Policy
        csp_directives = {
            'default-src': "'self'",
            'script-src': "'self' 'unsafe-inline'",  # Remove unsafe-inline in production
            'style-src': "'self' 'unsafe-inline'",
            'img-src': "'self' data: blob:",
            'font-src': "'self'",
            'connect-src': "'self' http://localhost:8000 http://localhost:8080",
            'media-src': "'self'",
            'object-src': "'none'",
            'frame-src': "'none'",
            'base-uri': "'self'",
            'form-action': "'self'",
        }
        
        csp_header = '; '.join([f"{directive} {value}" for directive, value in csp_directives.items()])
        security_headers['Content-Security-Policy'] = csp_header
        
        # Apply headers
        for header, value in security_headers.items():
            response[header] = value
        
        return response

class RateLimitMiddleware(MiddlewareMixin):
    """
    Advanced rate limiting middleware with different limits for different endpoints
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.rate_limits = {
            'default': {'requests': 100, 'window': 60},  # 100 requests per minute
            'auth': {'requests': 10, 'window': 60},      # 10 auth attempts per minute (increased)
            'token_refresh': {'requests': 20, 'window': 60},  # 20 token refresh per minute
            'upload': {'requests': 10, 'window': 60},    # 10 uploads per minute
            'api': {'requests': 200, 'window': 60},      # 200 API calls per minute
            'admin': {'requests': 500, 'window': 60},    # 500 admin requests per minute (higher limit)
        }
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        # Skip rate limiting for Django admin interface
        if request.path.startswith('/admin/'):
            return None

        # Skip rate limiting for static files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return None

        # Get client IP
        client_ip = self.get_client_ip(request)

        # Determine rate limit category
        category = self.get_rate_limit_category(request)

        # Check rate limit
        if not self.is_rate_limit_allowed(client_ip, category):
            logger.warning(f"Rate limit exceeded for IP {client_ip} on {request.path}")
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'retry_after': self.get_retry_after(client_ip, category)
            }, status=429)

        return None
    
    def get_client_ip(self, request: HttpRequest) -> str:
        """Get the real client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        
        # Validate IP address
        try:
            ipaddress.ip_address(ip)
            return ip
        except ValueError:
            return '127.0.0.1'
    
    def get_rate_limit_category(self, request: HttpRequest) -> str:
        """Determine the rate limit category based on the request"""
        path = request.path.lower()

        # Admin interface gets higher limits
        if path.startswith('/admin/'):
            return 'admin'
        elif '/token/refresh/' in path:
            return 'token_refresh'
        elif 'auth' in path or 'login' in path or 'token' in path:
            return 'auth'
        elif 'upload' in path or request.method == 'POST' and 'file' in request.content_type:
            return 'upload'
        elif path.startswith('/api/'):
            return 'api'
        else:
            return 'default'
    
    def is_rate_limit_allowed(self, client_ip: str, category: str) -> bool:
        """Check if the request is within rate limits"""
        limits = self.rate_limits.get(category, self.rate_limits['default'])
        cache_key = f"rate_limit:{category}:{client_ip}"
        
        # Get current request count
        current_requests = cache.get(cache_key, 0)
        
        if current_requests >= limits['requests']:
            return False
        
        # Increment counter
        cache.set(cache_key, current_requests + 1, limits['window'])
        return True
    
    def get_retry_after(self, client_ip: str, category: str) -> int:
        """Get the retry-after time in seconds"""
        limits = self.rate_limits.get(category, self.rate_limits['default'])
        return limits['window']

class RequestValidationMiddleware(MiddlewareMixin):
    """
    Validate and sanitize incoming requests
    """
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        # Check request size
        if hasattr(request, 'content_length') and request.content_length:
            max_size = getattr(settings, 'MAX_REQUEST_SIZE', 10 * 1024 * 1024)  # 10MB default
            if request.content_length > max_size:
                logger.warning(f"Request size {request.content_length} exceeds limit {max_size}")
                return JsonResponse({'error': 'Request too large'}, status=413)
        
        # Validate content type for POST requests
        if request.method in ['POST', 'PUT', 'PATCH']:
            content_type = request.content_type
            allowed_types = [
                'application/json',
                'application/x-www-form-urlencoded',
                'multipart/form-data',
                'text/plain'
            ]
            
            if not any(allowed_type in content_type for allowed_type in allowed_types):
                logger.warning(f"Invalid content type: {content_type}")
                return JsonResponse({'error': 'Invalid content type'}, status=400)
        
        # Check for suspicious patterns in URL
        suspicious_patterns = [
            '../', '..\\', '<script', 'javascript:', 'vbscript:', 'onload=', 'onerror='
        ]
        
        for pattern in suspicious_patterns:
            if pattern.lower() in request.path.lower():
                logger.warning(f"Suspicious pattern '{pattern}' detected in URL: {request.path}")
                return JsonResponse({'error': 'Invalid request'}, status=400)
        
        return None

class CSRFTokenMiddleware(MiddlewareMixin):
    """
    Enhanced CSRF protection with token management
    """
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        # Skip CSRF for safe methods
        if request.method in ['GET', 'HEAD', 'OPTIONS', 'TRACE']:
            return None
        
        # Skip CSRF for public endpoints
        public_endpoints = [
            '/api/public/',
            '/api/alumni-applications/public/',
            '/api/applications/form1/',  # Public alumni application form1 endpoints
            '/api/applications/form2/',  # Public alumni application form2 endpoints
            '/api/lookups/',  # Public lookup endpoints for dropdowns
            '/api/token/refresh/',  # JWT token refresh endpoint
            '/api/settings/organization/public/',  # Public organization settings
            '/api/settings/quick-links/public/',  # Public quick links
            '/api/settings/social-media/public/',  # Public social media links
            '/api/service-types/public/',  # Public service types
        ]
        
        if any(request.path.startswith(endpoint) for endpoint in public_endpoints):
            return None
        
        # Check CSRF token
        csrf_token = request.META.get('HTTP_X_CSRF_TOKEN')
        if not csrf_token:
            csrf_token = request.POST.get('csrfmiddlewaretoken')
        
        if not csrf_token:
            logger.warning(f"Missing CSRF token for {request.path}")
            return JsonResponse({
                'error': 'CSRF token missing',
                'csrf_token': get_token(request)
            }, status=403, headers={'X-CSRF-Error': 'missing'})
        
        return None

class SecurityAuditMiddleware(MiddlewareMixin):
    """
    Log security-relevant events for auditing
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.security_logger = logging.getLogger('security')
    
    def process_request(self, request: HttpRequest) -> None:
        # Log authentication attempts
        if 'auth' in request.path.lower() or 'login' in request.path.lower():
            self.log_security_event(request, 'auth_attempt', {
                'method': request.method,
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'ip': self.get_client_ip(request)
            })
        
        # Log file upload attempts
        if request.method == 'POST' and 'upload' in request.path.lower():
            self.log_security_event(request, 'file_upload_attempt', {
                'content_type': request.content_type,
                'content_length': getattr(request, 'content_length', 0),
                'ip': self.get_client_ip(request)
            })
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        # Log failed authentication
        if response.status_code in [401, 403] and 'auth' in request.path.lower():
            self.log_security_event(request, 'auth_failure', {
                'status_code': response.status_code,
                'ip': self.get_client_ip(request)
            }, severity='medium')
        
        # Log suspicious activity
        if response.status_code in [400, 403, 429]:
            self.log_security_event(request, 'suspicious_request', {
                'status_code': response.status_code,
                'path': request.path,
                'method': request.method,
                'ip': self.get_client_ip(request)
            }, severity='low')
        
        return response
    
    def get_client_ip(self, request: HttpRequest) -> str:
        """Get the real client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip
    
    def log_security_event(self, request: HttpRequest, event_type: str, 
                          metadata: Dict[str, Any], severity: str = 'low') -> None:
        """Log a security event"""
        event_data = {
            'timestamp': time.time(),
            'event_type': event_type,
            'severity': severity,
            'path': request.path,
            'method': request.method,
            'metadata': metadata
        }
        
        self.security_logger.info(json.dumps(event_data))
        
        # Store in cache for real-time monitoring
        cache_key = f"security_events:{int(time.time() // 60)}"  # Group by minute
        events = cache.get(cache_key, [])
        events.append(event_data)
        cache.set(cache_key, events, 3600)  # Keep for 1 hour

class FileUploadSecurityMiddleware(MiddlewareMixin):
    """
    Enhanced security for file uploads
    """
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        if request.method != 'POST' or not request.FILES:
            return None
        
        for field_name, uploaded_file in request.FILES.items():
            # Check file size
            max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 10 * 1024 * 1024)  # 10MB
            if uploaded_file.size > max_size:
                logger.warning(f"File upload size {uploaded_file.size} exceeds limit {max_size}")
                return JsonResponse({'error': 'File too large'}, status=413)
            
            # Check file extension
            allowed_extensions = getattr(settings, 'ALLOWED_UPLOAD_EXTENSIONS', 
                                       ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'])
            file_extension = '.' + uploaded_file.name.split('.')[-1].lower()
            if file_extension not in allowed_extensions:
                logger.warning(f"Invalid file extension: {file_extension}")
                return JsonResponse({
                    'error': f'File type not allowed. Allowed types: {", ".join(allowed_extensions)}'
                }, status=400)
            
            # Check for suspicious file names
            suspicious_names = ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*']
            if any(char in uploaded_file.name for char in suspicious_names):
                logger.warning(f"Suspicious file name: {uploaded_file.name}")
                return JsonResponse({'error': 'Invalid file name'}, status=400)
        
        return None
