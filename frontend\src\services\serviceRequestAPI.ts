import axios from 'axios';
import { API_BASE_URL } from '@/config';
import {
  ServiceRequest,
  ServiceRequestFormData,
  ServiceRequestListResponse,
  ServiceRequestStatistics,
  ServiceRequestFilters,
  DocumentUpload,
  ServiceTypeLookup,
  AdmissionTypeLookup,
  StudyProgramLookup,
  CollegeLookup,
  DepartmentLookup,
  CertificateTypeLookup
} from '@/types/serviceRequest';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Add request interceptor to include auth token if available
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// ServiceRequest API
export const serviceRequestAPI = {
  // CRUD operations
  async getAll(filters?: ServiceRequestFilters): Promise<ServiceRequestListResponse> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v.toString()));
          } else {
            params.append(key, value.toString());
          }
        }
      });
    }
    
    const response = await api.get(`/service-requests/?${params.toString()}`);
    return response.data;
  },

  async getById(id: string): Promise<ServiceRequest> {
    const response = await api.get(`/service-requests/${id}/`);
    return response.data;
  },

  async create(data: ServiceRequestFormData): Promise<ServiceRequest> {
    const response = await api.post('/service-requests/', data);
    return response.data;
  },

  async update(id: string, data: Partial<ServiceRequestFormData>): Promise<ServiceRequest> {
    const response = await api.patch(`/service-requests/${id}/`, data);
    return response.data;
  },

  async delete(id: string): Promise<void> {
    await api.delete(`/service-requests/${id}/`);
  },

  async softDelete(id: string): Promise<void> {
    await api.post(`/service-requests/${id}/soft_delete/`);
  },

  async restore(id: string): Promise<void> {
    await api.post(`/service-requests/${id}/restore/`);
  },

  // Status management
  async updateStatus(id: string, status: string): Promise<ServiceRequest> {
    const response = await api.patch(`/service-requests/${id}/update_status/`, { status });
    return response.data;
  },

  // Statistics
  async getStatistics(): Promise<ServiceRequestStatistics> {
    const response = await api.get('/service-requests/statistics/');
    return response.data;
  },

  // Required documents
  async getRequiredDocuments(id: string): Promise<CertificateTypeLookup[]> {
    const response = await api.get(`/service-requests/${id}/required_documents/`);
    return response.data;
  }
};

// Document Upload API
export const documentUploadAPI = {
  async getAll(serviceRequestId?: string): Promise<DocumentUpload[]> {
    const params = serviceRequestId ? `?service_request=${serviceRequestId}` : '';
    const response = await api.get(`/document-uploads/${params}`);
    return response.data.results || response.data;
  },

  async getById(id: string): Promise<DocumentUpload> {
    const response = await api.get(`/document-uploads/${id}/`);
    return response.data;
  },

  async upload(data: FormData): Promise<DocumentUpload> {
    const response = await api.post('/document-uploads/', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async delete(id: string): Promise<void> {
    await api.delete(`/document-uploads/${id}/`);
  },

  async verify(id: string, verificationNotes?: string): Promise<DocumentUpload> {
    const response = await api.patch(`/document-uploads/${id}/verify/`, {
      verification_notes: verificationNotes
    });
    return response.data;
  }
};

// Lookup APIs for dropdown population
export const lookupAPI = {
  async getServiceTypes(): Promise<ServiceTypeLookup[]> {
    const response = await api.get('/lookups/service-types/');
    return response.data;
  },

  async getAdmissionTypes(): Promise<AdmissionTypeLookup[]> {
    const response = await api.get('/lookups/admission-types/');
    return response.data;
  },

  async getStudyPrograms(): Promise<StudyProgramLookup[]> {
    const response = await api.get('/lookups/study-programs/');
    return response.data;
  },

  async getColleges(): Promise<CollegeLookup[]> {
    const response = await api.get('/lookups/colleges/');
    return response.data;
  },

  async getDepartments(collegeId?: number): Promise<DepartmentLookup[]> {
    const params = collegeId ? `?college=${collegeId}` : '';
    const response = await api.get(`/lookups/departments/${params}`);
    return response.data;
  },

  async getCertificateTypes(): Promise<CertificateTypeLookup[]> {
    const response = await api.get('/lookups/certificate-types/');
    return response.data;
  }
};

// Utility functions
export const serviceRequestUtils = {
  // Format phone number
  formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Add country code if not present
    if (cleaned.length === 9 && !cleaned.startsWith('251')) {
      return `+251${cleaned}`;
    } else if (cleaned.length === 12 && cleaned.startsWith('251')) {
      return `+${cleaned}`;
    }
    
    return phone;
  },

  // Validate email
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate phone number
  validatePhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?251\d{9}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  },

  // Get status color
  getStatusColor(status: string): string {
    const statusColors = {
      pending: 'orange',
      processing: 'blue',
      completed: 'green',
      rejected: 'red'
    };
    return statusColors[status as keyof typeof statusColors] || 'gray';
  },

  // Get status label
  getStatusLabel(status: string): string {
    const statusLabels = {
      pending: 'Pending',
      processing: 'Processing',
      completed: 'Completed',
      rejected: 'Rejected'
    };
    return statusLabels[status as keyof typeof statusLabels] || status;
  },

  // Check if service type requires specific fields
  requiresMailingAddress(serviceTypeName: string): boolean {
    return serviceTypeName.toLowerCase().includes('official transcript');
  },

  requiresGraduationYear(serviceTypeName: string): boolean {
    return serviceTypeName.toLowerCase().includes('original degree');
  },

  requiresStudentStatus(serviceTypeName: string): boolean {
    return !serviceTypeName.toLowerCase().includes('original degree');
  },

  // Format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Generate form validation errors
  validateForm(data: ServiceRequestFormData, serviceTypeName?: string): Record<string, string> {
    const errors: Record<string, string> = {};

    // Required fields validation
    if (!data.first_name.trim()) errors.first_name = 'First name is required';
    if (!data.last_name.trim()) errors.last_name = 'Last name is required';
    if (!data.email.trim()) errors.email = 'Email is required';
    else if (!this.validateEmail(data.email)) errors.email = 'Invalid email format';
    if (!data.mobile.trim()) errors.mobile = 'Mobile number is required';
    else if (!this.validatePhoneNumber(data.mobile)) errors.mobile = 'Invalid phone number format';
    if (!data.service_type) errors.service_type = 'Service type is required';
    if (!data.admission_type) errors.admission_type = 'Admission type is required';
    if (!data.degree) errors.degree = 'Degree is required';

    // College validation
    if (data.is_college_other) {
      if (!data.college_other.trim()) errors.college_other = 'College name is required';
    } else {
      if (!data.college) errors.college = 'College selection is required';
    }

    // Department validation
    if (data.is_department_other) {
      if (!data.department_other.trim()) errors.department_other = 'Department name is required';
    } else {
      if (!data.department) errors.department = 'Department selection is required';
    }

    // Service-specific validation
    if (serviceTypeName) {
      if (this.requiresMailingAddress(serviceTypeName)) {
        if (!data.mailing_destination) errors.mailing_destination = 'Mailing destination is required';
        if (data.mailing_destination === 'external') {
          if (!data.institute_name.trim()) errors.institute_name = 'Institute name is required';
          if (!data.institute_country.trim()) errors.institute_country = 'Institute country is required';
          if (!data.institute_address.trim()) errors.institute_address = 'Institute address is required';
        }
        if (data.mailing_agent === 'other' && !data.mailing_agent_other.trim()) {
          errors.mailing_agent_other = 'Please specify the mailing agent';
        }
      }

      if (this.requiresGraduationYear(serviceTypeName)) {
        if (!data.graduation_year_ec && !data.graduation_year_gc) {
          errors.graduation_year_ec = 'Either Ethiopian or Gregorian graduation year is required';
        }
      }

      if (this.requiresStudentStatus(serviceTypeName)) {
        if (!data.student_status) errors.student_status = 'Student status is required';
        if (!data.year_type) errors.year_type = 'Year type is required';
        if (!data.year_ec && !data.year_gc) {
          errors.year_ec = 'Either Ethiopian or Gregorian year is required';
        }
      }
    }

    return errors;
  }
};
