import os
import json
import django
import sys

# Add the project root directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models
from GraduateVerification.models import VerificationProgram

def clear_and_load_programs():
    print("Clearing existing programs...")
    VerificationProgram.objects.all().delete()
    
    print("Loading programs with unique codes...")
    with open('seed_data/verification_programs.json', 'r', encoding='utf-8') as f:
        programs_data = json.load(f)
    
    for program_data in programs_data:
        # Create the program
        VerificationProgram.objects.create(
            name=program_data['name'],
            code=program_data['code']
        )
    
    print(f"Loaded {len(programs_data)} programs")

if __name__ == '__main__':
    clear_and_load_programs()
