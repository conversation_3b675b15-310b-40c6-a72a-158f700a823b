import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import {
  User,
  School,
  FileText,
  MapPin,
  ArrowLeft,
  ArrowRight,
  Check,
  Upload,
  Shield,
  Loader2,
  CheckCircle,
  AlertCircle,
  X
} from 'lucide-react';
import {
  publicAlumniApplicationsAPI,
  AlumniApplication,
  AlumniApplicationMini,
  ServiceType,
  College,
  Department,
  DocumentType
} from '@/services/alumniApplicationsAPI';
import { FileSecurityValidator, UploadRateLimiter, DEFAULT_SECURITY_CONFIG } from '@/utils/fileSecurityUtils';
import { useAlumniFormData } from '@/hooks/useAlumniFormData';
import { useFormValidation, ValidationRules } from '@/hooks/useFormValidation';


interface AdvancedAlumniApplicationFormProps {
  formType: 'form1' | 'form2';
}

// Validation rules based on exact model structure
const getValidationRules = (formType: 'form1' | 'form2'): ValidationRules => ({
  // Personal Information - exact model validation
  first_name: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z\s\-'\.]+$/,
    custom: (value) => {
      if (!value || typeof value !== 'string') return 'First name is required';
      const trimmed = value.trim();
      if (!trimmed) return 'First name cannot be empty';
      if (!/^[a-zA-Z\s\-'\.]+$/.test(trimmed)) {
        return 'First name must contain only letters, spaces, hyphens, apostrophes, and periods';
      }
      return null;
    }
  },
  father_name: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z\s\-'\.]+$/,
    custom: (value) => {
      if (!value || typeof value !== 'string') return 'Father name is required';
      const trimmed = value.trim();
      if (!trimmed) return 'Father name cannot be empty';
      if (!/^[a-zA-Z\s\-'\.]+$/.test(trimmed)) {
        return 'Father name must contain only letters, spaces, hyphens, apostrophes, and periods';
      }
      return null;
    }
  },
  last_name: {
    required: true,
    minLength: 1,
    maxLength: 50,
    pattern: /^[a-zA-Z\s\-'\.]+$/,
    custom: (value) => {
      if (!value || typeof value !== 'string') return 'Last name is required';
      const trimmed = value.trim();
      if (!trimmed) return 'Last name cannot be empty';
      if (!/^[a-zA-Z\s\-'\.]+$/.test(trimmed)) {
        return 'Last name must contain only letters, spaces, hyphens, apostrophes, and periods';
      }
      return null;
    }
  },
  student_id: {
    required: false,
    maxLength: 20,
    custom: (value) => {
      if (!value) return null; // Optional field
      if (typeof value !== 'string') return 'Invalid student ID format';
      if (!/^[a-zA-Z0-9/]+$/.test(value)) {
        return 'Student ID must be alphanumeric with forward slashes (e.g., uog/1254/21)';
      }
      return null;
    }
  },
  phone_number: {
    required: true,
    maxLength: 20,
    custom: (value) => {
      if (!value || typeof value !== 'string') return 'Phone number is required';
      if (!/^\+?[1-9]\d{1,14}$/.test(value)) {
        return 'Phone number must be in international format with country code';
      }
      return null;
    }
  },
  email: {
    required: true,
    email: true,
    maxLength: 100,
    custom: (value) => {
      if (!value || typeof value !== 'string') return 'Email is required';
      const trimmed = value.trim();
      if (!trimmed) return 'Email cannot be empty';
      if (trimmed.length > 100) return 'Email must be 100 characters or less';
      // Enhanced email validation matching Django EmailValidator
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(trimmed)) {
        return 'Please enter a valid email address';
      }
      return null;
    }
  },

  // Academic Information - required fields
  admission_type: {
    required: true,
    custom: (value) => {
      const validTypes = ['Regular', 'Evening', 'Summer', 'Distance'];
      if (!value) return 'Admission type is required';
      if (!validTypes.includes(value)) return 'Invalid admission type';
      return null;
    }
  },
  degree_type: {
    required: true,
    custom: (value) => {
      const validTypes = ['Diploma', 'Degree', "Master's", 'PHD', 'Sp.Certificate'];
      if (!value) return 'Degree type is required';
      if (!validTypes.includes(value)) return 'Invalid degree type';
      return null;
    }
  },
  student_status: {
    required: true,
    custom: (value) => {
      const validStatuses = ['Active', 'Inactive', 'Graduated'];
      if (!value) return 'Student status is required';
      if (!validStatuses.includes(value)) return 'Invalid student status';
      return null;
    }
  },

  // College/Department validation - REQUIRED fields, cannot be null
  college: {
    required: true,
    custom: (value, formData) => {
      if (formData.is_other_college) {
        if (value) return 'Cannot select both system college and other college';
        return null;
      }
      if (!value || value === '' || value === null || value === undefined) {
        return 'College selection is required';
      }
      return null;
    }
  },
  department: {
    required: true,
    custom: (value, formData) => {
      if (formData.is_other_college) {
        if (value) return 'Cannot select both system department and other department';
        return null;
      }
      if (!value || value === '' || value === null || value === undefined) {
        return 'Department selection is required';
      }
      return null;
    }
  },
  other_college_name: {
    maxLength: 100,
    custom: (value, formData) => {
      if (!formData.is_other_college) {
        if (value) return 'Cannot specify other college name when system college is selected';
        return null;
      }
      // Required when other college is selected
      if (!value || !value.trim()) return 'College name is required when "College is not in the list" is selected';
      if (value.trim().length > 100) return 'College name must be 100 characters or less';
      return null;
    }
  },
  other_department_name: {
    maxLength: 100,
    custom: (value, formData) => {
      if (!formData.is_other_college) {
        if (value) return 'Cannot specify other department name when system department is selected';
        return null;
      }
      // Required when other college is selected
      if (!value || !value.trim()) return 'Department name is required when "College is not in the list" is selected';
      if (value.trim().length > 100) return 'Department name must be 100 characters or less';
      return null;
    }
  },

  // Conditional academic fields based on student_status - exact model logic
  current_year: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Active') return null;
      if (!value) return 'Current year is required when student status is Active';
      const validYears = ['1st year', '2nd year', '3rd year', '4th year', '5th year', '6th year', '7th year', '8th year'];
      if (!validYears.includes(value)) return 'Invalid current year selection';
      return null;
    }
  },
  year_of_leaving_ethiopian: {
    maxLength: 10,
    custom: (value, formData) => {
      if (formData.student_status !== 'Inactive') return null;
      // Either Ethiopian or Gregorian year is required for Inactive status
      if (!value && !formData.year_of_leaving_gregorian) {
        return 'Either Ethiopian or Gregorian year of leaving is required when student status is Inactive';
      }
      if (value && typeof value === 'string') {
        const trimmed = value.trim();
        if (trimmed.length > 10) return 'Ethiopian year must be 10 characters or less';
        // Basic validation for Ethiopian year format (should be numeric)
        if (!/^\d{4}$/.test(trimmed)) {
          return 'Ethiopian year must be a 4-digit number';
        }
        const year = parseInt(trimmed);
        if (year < 1954 || year > 2017) { // Ethiopian calendar constraint: 1954-2017 (current)
          return 'Ethiopian year must be between 1954 and 2017';
        }
      }
      return null;
    }
  },
  year_of_leaving_gregorian: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Inactive') return null;
      // Either Ethiopian or Gregorian year is required for Inactive status
      if (!value && !formData.year_of_leaving_ethiopian) {
        return 'Either Ethiopian or Gregorian year of leaving is required when student status is Inactive';
      }
      if (value) {
        const year = parseInt(value);
        if (isNaN(year) || year < 1960 || year > 2025) { // Gregorian calendar constraint: 1960-2025 (current)
          return 'Gregorian year must be between 1960 and 2025';
        }
      }
      return null;
    }
  },
  year_of_graduation_ethiopian: {
    maxLength: 10,
    custom: (value, formData) => {
      if (formData.student_status !== 'Graduated') return null;
      // Either Ethiopian or Gregorian year is required for Graduated status
      if (!value && !formData.year_of_graduation_gregorian) {
        return 'Either Ethiopian or Gregorian year of graduation is required when student status is Graduated';
      }
      if (value && typeof value === 'string') {
        const trimmed = value.trim();
        if (trimmed.length > 10) return 'Ethiopian year must be 10 characters or less';
        // Basic validation for Ethiopian year format (should be numeric)
        if (!/^\d{4}$/.test(trimmed)) {
          return 'Ethiopian year must be a 4-digit number';
        }
        const year = parseInt(trimmed);
        if (year < 1954 || year > 2017) { // Ethiopian calendar constraint: 1954-2017 (current)
          return 'Ethiopian year must be between 1954 and 2017';
        }
      }
      return null;
    }
  },
  year_of_graduation_gregorian: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Graduated') return null;
      // Either Ethiopian or Gregorian year is required for Graduated status
      if (!value && !formData.year_of_graduation_ethiopian) {
        return 'Either Ethiopian or Gregorian year of graduation is required when student status is Graduated';
      }
      if (value) {
        const year = parseInt(value);
        if (isNaN(year) || year < 1960 || year > 2025) { // Gregorian calendar constraint: 1960-2025 (current)
          return 'Gregorian year must be between 1960 and 2025';
        }
      }
      return null;
    }
  },

  // Service type validation
  service_type: {
    required: true,
    custom: (value) => {
      if (!value) return 'Service type is required';
      return null;
    }
  },



  // Form1 specific destination validation - REQUIRED fields, cannot be null
  ...(formType === 'form1' && {
    is_uog_destination: {
      required: true,
      custom: (value) => {
        if (value === null || value === undefined) return 'Destination type selection is required';
        return null;
      }
    },
    // Internal destination fields - ONLY REQUIRED when UoG destination is selected
    uog_college: {
      required: false, // Conditionally required
      custom: (value, formData) => {
        if (formData.is_uog_destination === false) return null; // Not required for external destination
        if (formData.is_uog_destination === true) {
          if (!value || value === '' || value === null || value === undefined) {
            return 'UoG college selection is required for internal destination';
          }
        }
        return null;
      }
    },
    uog_department: {
      required: false, // Conditionally required
      custom: (value, formData) => {
        if (formData.is_uog_destination === false) return null; // Not required for external destination
        if (formData.is_uog_destination === true) {
          if (!value || value === '' || value === null || value === undefined) {
            return 'UoG department selection is required for internal destination';
          }
        }
        return null;
      }
    },
    // External destination fields - ONLY REQUIRED when external destination is selected
    order_type: {
      required: false, // Conditionally required
      custom: (value, formData) => {
        if (formData.is_uog_destination === true) return null; // Not required for internal destination
        if (formData.is_uog_destination === false) {
          if (!value || value === '' || value === null || value === undefined) {
            return 'Order type selection is required for external destination';
          }
          const validTypes = ['Local', 'International', 'Legal Delegate'];
          if (!validTypes.includes(value)) return 'Invalid order type selection';
        }
        return null;
      }
    },
    institution_name: {
      required: false, // Conditionally required
      maxLength: 100,
      custom: (value, formData) => {
        if (formData.is_uog_destination === true) return null; // Not required for internal destination
        if (formData.is_uog_destination === false) {
          if (!value || !value.trim()) {
            return 'Institution name is required for external destination';
          }
          if (value.trim().length > 100) {
            return 'Institution name must be 100 characters or less';
          }
        }
        return null;
      }
    },
    country: {
      required: false, // Conditionally required
      maxLength: 100,
      custom: (value, formData) => {
        if (formData.is_uog_destination === true) return null; // Not required for internal destination
        if (formData.is_uog_destination === false) {
          if (!value || !value.trim()) {
            return 'Country is required for external destination';
          }
          if (value.trim().length > 100) {
            return 'Country must be 100 characters or less';
          }
        }
        return null;
      }
    },
    institution_address: {
      required: false, // Conditionally required
      maxLength: 500,
      custom: (value, formData) => {
        if (formData.is_uog_destination === true) return null; // Not required for internal destination
        if (formData.is_uog_destination === false) {
          if (!value || !value.trim()) {
            return 'Institution address is required for external destination';
          }
          if (typeof value === 'string' && value.length > 500) {
            return 'Institution address must be 500 characters or less';
          }
        }
        return null;
      }
    },
    mailing_agent: {
      required: false, // Conditionally required
      custom: (value, formData) => {
        if (formData.is_uog_destination === true) return null; // Not required for internal destination
        if (formData.is_uog_destination === false) {
          if (!value || value === '' || value === null || value === undefined) {
            return 'Mailing agent selection is required for external destination';
          }
          const validAgents = ['Normal Postal', 'DHL', 'SMS'];
          if (!validAgents.includes(value)) return 'Invalid mailing agent selection';
        }
        return null;
      }
    }
  })
});

const AdvancedAlumniApplicationForm: React.FC<AdvancedAlumniApplicationFormProps> = memo(({ formType }) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [submitting, setSubmitting] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<{[key: string]: File}>({});
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [submissionResult, setSubmissionResult] = useState<{
    transaction_id: string;
    applicant_name: string;
    service_type: string;
    service_fee: string;
  } | null>(null);

  // Get pre-selected service type from URL parameters
  const [searchParams] = useSearchParams();
  const preSelectedServiceType = searchParams.get('service_type');

  // Initialize form data with default values matching exact model structure
  const initialFormData = useMemo(() => {
    const baseData = {
      // Personal Information Section
      first_name: '',
      father_name: '',
      last_name: '',
      student_id: '', // Optional field
      phone_number: '',
      email: '',

      // Academic Information Section
      admission_type: '',
      degree_type: '',
      is_other_college: false,
      college: '', // ForeignKey to College
      other_college_name: '', // CharField for other college
      department: '', // ForeignKey to Department
      other_department_name: '', // CharField for other department
      student_status: '',

      // Conditional Academic Fields (based on student_status)
      current_year: '', // Required when status is Active
      year_of_leaving_ethiopian: '', // CharField for Ethiopian calendar
      year_of_leaving_gregorian: '', // IntegerField for Gregorian calendar
      year_of_graduation_ethiopian: '', // CharField for Ethiopian calendar
      year_of_graduation_gregorian: '', // IntegerField for Gregorian calendar

      // Service Information
      service_type: '', // ForeignKey to ServiceType

      // System fields (auto-managed by backend)
      application_status: 'Pending', // Default value from model
      payment_status: 'Pending', // Default value from model
      // transaction_id is auto-generated by backend
    };

    // Add Form1 specific destination fields
    if (formType === 'form1') {
      return {
        ...baseData,
        // Destination Logic Section (ONLY for Form1)
        is_uog_destination: null, // Boolean field - null initially to force selection

        // Internal UoG destination fields
        uog_college: '', // ForeignKey to College for destination
        uog_department: '', // ForeignKey to Department for destination

        // External destination fields
        order_type: '', // CharField with choices
        institution_name: '', // CharField max_length=100
        country: '', // CharField max_length=100
        institution_address: '', // TextField max_length=500
        mailing_agent: '', // CharField with choices
      };
    }

    return baseData;
  }, [formType]);

  // Use custom hooks for optimized data management
  const {
    serviceTypes,
    colleges,
    departments,
    requiredDocuments,
    loading,
    loadDepartments,
    loadRequiredDocuments
  } = useAlumniFormData();

  // Use form validation hook
  const validationRules = useMemo(() => getValidationRules(formType), [formType]);
  const {
    formData,
    errors: validationErrors,
    updateField,
    validateForm,
    isValid: isFormValid,
    resetForm
  } = useFormValidation(initialFormData, validationRules, {
    debounceMs: 300,
    validateOnChange: true,
    sanitizeInputs: true
  });

  // Security components
  const [fileValidator] = useState(() => new FileSecurityValidator(DEFAULT_SECURITY_CONFIG));
  const [rateLimiter] = useState(() => new UploadRateLimiter(10, 60000));

  const isForm1 = formType === 'form1';

  // Get required documents for current service type (moved up to avoid hoisting issues)
  const currentRequiredDocuments = useMemo(() => {
    if (!formData.service_type) return [];
    return requiredDocuments[formData.service_type as string] || [];
  }, [formData.service_type, requiredDocuments]);

  // Custom validation for documents and destination (separate from form validation hook)
  const isDocumentValidationPassed = useMemo(() => {
    if (!formData.service_type || currentRequiredDocuments.length === 0) {
      console.log('Document validation: No service type or no required documents');
      return true; // No documents required
    }

    const uploadedCount = Object.keys(uploadedDocuments).length;
    if (uploadedCount === 0) {
      console.log('Document validation: No documents uploaded');
      return false; // No documents uploaded
    }

    // Check if all required documents are uploaded
    const missingDocs = currentRequiredDocuments.filter(doc => !uploadedDocuments[doc.name]);
    const result = missingDocs.length === 0;
    console.log('Document validation:', {
      requiredDocs: currentRequiredDocuments.map(d => d.name),
      uploadedDocs: Object.keys(uploadedDocuments),
      missingDocs: missingDocs.map(d => d.name),
      result
    });
    return result;
  }, [formData.service_type, currentRequiredDocuments, uploadedDocuments]);

  const isDestinationValidationPassed = useMemo(() => {
    if (!isForm1) {
      console.log('Destination validation: Form2 - no destination required');
      return true; // Form2 doesn't have destination
    }

    const alumniFormData = formData as AlumniApplication;

    // Destination type must be selected
    if (alumniFormData.is_uog_destination === null || alumniFormData.is_uog_destination === undefined) {
      console.log('Destination validation: No destination type selected');
      return false;
    }

    // Internal destination validation
    if (alumniFormData.is_uog_destination === true) {
      const result = !!(alumniFormData.uog_college && alumniFormData.uog_department);
      console.log('Destination validation (Internal):', {
        uog_college: alumniFormData.uog_college,
        uog_department: alumniFormData.uog_department,
        result
      });
      return result;
    }

    // External destination validation
    if (alumniFormData.is_uog_destination === false) {
      const result = !!(
        alumniFormData.order_type &&
        alumniFormData.institution_name?.trim() &&
        alumniFormData.country?.trim() &&
        alumniFormData.institution_address?.trim() &&
        alumniFormData.mailing_agent
      );
      console.log('Destination validation (External):', {
        order_type: alumniFormData.order_type,
        institution_name: alumniFormData.institution_name,
        country: alumniFormData.country,
        institution_address: alumniFormData.institution_address,
        mailing_agent: alumniFormData.mailing_agent,
        result
      });
      return result;
    }

    console.log('Destination validation: Unknown state');
    return false;
  }, [isForm1, formData]);

  // Comprehensive manual validation for submit button
  const isSubmitReady = useMemo(() => {
    // 1. Personal Information validation
    const hasPersonalInfo = !!(
      formData.first_name?.trim() &&
      formData.father_name?.trim() &&
      formData.last_name?.trim() &&
      formData.phone_number?.trim() &&
      formData.email?.trim()
    );

    // 2. Academic Information validation
    const hasAcademicInfo = !!(
      formData.admission_type &&
      formData.degree_type &&
      formData.student_status &&
      (formData.is_other_college ?
        (formData.other_college_name?.trim() && formData.other_department_name?.trim()) :
        (formData.college && formData.department)
      )
    );

    // 3. Student status specific validation
    let hasStatusSpecificInfo = true;
    if (formData.student_status === 'Active') {
      hasStatusSpecificInfo = !!formData.current_year;
    } else if (formData.student_status === 'Inactive') {
      hasStatusSpecificInfo = !!(formData.year_of_leaving_ethiopian || formData.year_of_leaving_gregorian);
    } else if (formData.student_status === 'Graduated') {
      hasStatusSpecificInfo = !!(formData.year_of_graduation_ethiopian || formData.year_of_graduation_gregorian);
    }

    // 4. Service Information validation
    const hasServiceInfo = !!formData.service_type;

    // 5. Form1 destination validation
    let hasDestinationInfo = true;
    if (isForm1) {
      const alumniFormData = formData as AlumniApplication;
      if (alumniFormData.is_uog_destination === true) {
        hasDestinationInfo = !!(alumniFormData.uog_college && alumniFormData.uog_department);
      } else if (alumniFormData.is_uog_destination === false) {
        hasDestinationInfo = !!(
          alumniFormData.order_type &&
          alumniFormData.institution_name?.trim() &&
          alumniFormData.country?.trim() &&
          alumniFormData.institution_address?.trim() &&
          alumniFormData.mailing_agent
        );
      } else {
        hasDestinationInfo = false; // Destination type not selected
      }
    }

    const result = hasPersonalInfo && hasAcademicInfo && hasStatusSpecificInfo && hasServiceInfo && hasDestinationInfo && isDocumentValidationPassed;

    console.log('Comprehensive validation check:', {
      hasPersonalInfo,
      hasAcademicInfo,
      hasStatusSpecificInfo,
      hasServiceInfo,
      hasDestinationInfo,
      isDocumentValidationPassed,
      isSubmitReady: result
    });

    return result;
  }, [formData, isForm1, isDocumentValidationPassed]);

  // Define steps configuration
  const steps = [
    {
      id: 'personal',
      title: 'Personal Information',
      icon: User,
      description: 'Basic personal details',
      color: 'blue'
    },
    {
      id: 'academic',
      title: 'Academic Information',
      icon: School,
      description: 'Educational background',
      color: 'blue'
    },
    {
      id: 'service',
      title: 'Service Information',
      icon: FileText,
      description: 'Service selection and documents',
      color: 'blue'
    },
    ...(isForm1 ? [{
      id: 'destination',
      title: 'Destination',
      icon: MapPin,
      description: 'Delivery information',
      color: 'blue'
    }] : []),
    {
      id: 'summary',
      title: 'Review & Submit',
      icon: Check,
      description: 'Review application and submit',
      color: 'green'
    }
  ];

  const totalSteps = steps.length;

  // Generate transaction ID for display (6-character alphanumeric)
  const [transactionId, setTransactionId] = useState<string>('');

  useEffect(() => {
    // Generate transaction ID when reaching summary step
    if (currentStep === totalSteps - 1 && !transactionId) {
      const generateTransactionId = () => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
      };
      setTransactionId(generateTransactionId());
    }
  }, [currentStep, totalSteps, transactionId]);

  // Handle pre-selected service type from URL parameters
  useEffect(() => {
    if (preSelectedServiceType && serviceTypes.length > 0 && !formData.service_type) {
      // Find the service type by ID
      const selectedService = serviceTypes.find(st => st.id.toString() === preSelectedServiceType);
      if (selectedService) {
        console.log('Pre-selected service type found:', selectedService);
        updateField('service_type', preSelectedServiceType);
        loadRequiredDocuments(preSelectedServiceType);
      }
    }
  }, [preSelectedServiceType, serviceTypes, formData.service_type, updateField, loadRequiredDocuments]);

  // Step validation function
  const validateCurrentStep = (): boolean => {
    const validation = validateForm();

    // Get fields relevant to current step
    let stepFields: string[] = [];

    switch (currentStep) {
      case 0: // Personal Information
        stepFields = ['first_name', 'father_name', 'last_name', 'phone_number', 'email'];
        break;
      case 1: // Academic Information
        stepFields = ['admission_type', 'degree_type', 'student_status'];

        // College and Department are ALWAYS required - either system or other
        if (!formData.is_other_college) {
          stepFields.push('college', 'department');
          // Additional validation: ensure both college and department are selected
          if (!formData.college || formData.college === '' || formData.college === null) {
            return false;
          }
          if (!formData.department || formData.department === '' || formData.department === null) {
            return false;
          }
        } else {
          stepFields.push('other_college_name', 'other_department_name');
          // Additional validation: ensure both other college and department names are provided
          if (!formData.other_college_name || !formData.other_college_name.trim()) {
            return false;
          }
          if (!formData.other_department_name || !formData.other_department_name.trim()) {
            return false;
          }
        }

        // Student status conditional validation
        if (formData.student_status === 'Active') {
          stepFields.push('current_year');
        } else if (formData.student_status === 'Inactive') {
          // Either Ethiopian or Gregorian year is required
          if (!formData.year_of_leaving_ethiopian && !formData.year_of_leaving_gregorian) {
            return false;
          }
        } else if (formData.student_status === 'Graduated') {
          // Either Ethiopian or Gregorian year is required
          if (!formData.year_of_graduation_ethiopian && !formData.year_of_graduation_gregorian) {
            return false;
          }
        }
        break;
      case 2: // Service Information
        stepFields = ['service_type'];

        // Document upload validation - use the memoized validation
        if (!isDocumentValidationPassed) {
          console.log('Document validation failed - documents not properly uploaded');
          return false;
        }
        break;
      case 3: // Destination (Form1 only) or Summary (Form2)
        if (isForm1) {
          stepFields = ['is_uog_destination'];

          // Use the memoized destination validation
          if (!isDestinationValidationPassed) {
            console.log('Destination validation failed - destination information incomplete');
            return false;
          }
        }
        // For Form2, step 3 is the summary step - no additional validation needed
        break;
      case 4: // Summary step (Form1 only)
        if (isForm1) {
          // Summary step - validate entire form
          return validation.isValid;
        }
        break;
    }

    // Check if any step-specific fields have errors
    const hasStepErrors = stepFields.some(field => validation.errors[field]);

    return !hasStepErrors;
  };

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < totalSteps - 1) {
      // Validate current step before proceeding
      if (!validateCurrentStep()) {
        toast.error('Please complete all required fields in this step before proceeding');
        return;
      }

      // Mark current step as completed
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };



  // Get departments for current college
  const currentDepartments = useMemo(() => {
    if (!formData.college || formData.is_other_college) return [];
    return departments[formData.college as string] || [];
  }, [formData.college, formData.is_other_college, departments]);

  // Get UoG departments for destination (Form1 only)
  const uogDepartments = useMemo(() => {
    if (!isForm1) return [];
    const alumniFormData = formData as AlumniApplication;
    if (!alumniFormData.uog_college || !alumniFormData.is_uog_destination) return [];
    return departments[alumniFormData.uog_college as string] || [];
  }, [isForm1, formData, departments]);

  // Handle input changes with validation and performance optimization
  const handleInputChange = useCallback((field: string, value: any) => {
    updateField(field, value);

    // Trigger dependent data loading
    if (field === 'college' && !formData.is_other_college) {
      loadDepartments(value);
    } else if (field === 'service_type') {
      loadRequiredDocuments(value);
      // Only clear uploaded documents if service type actually changes to a different value
      if (formData.service_type && formData.service_type !== value) {
        console.log('Service type changed - clearing uploaded documents:', {
          oldServiceType: formData.service_type,
          newServiceType: value,
          currentUploads: Object.keys(uploadedDocuments)
        });
        setUploadedDocuments({});
      }
    } else if (field === 'uog_college' && formData.is_uog_destination) {
      loadDepartments(value);
    }
  }, [updateField, formData.is_other_college, formData.is_uog_destination, loadDepartments, loadRequiredDocuments]);

  // Enhanced file upload with comprehensive security
  const handleFileUpload = useCallback(async (documentTypeName: string, file: File) => {
    try {
      // Check rate limiting
      if (!rateLimiter.canUpload()) {
        const timeUntilReset = Math.ceil(rateLimiter.getTimeUntilReset() / 1000);
        toast.error(`Upload rate limit exceeded. Please wait ${timeUntilReset} seconds before trying again.`);
        return;
      }

      // Show loading state
      toast.loading('Validating file...', { id: `validate-${documentTypeName}` });

      // Comprehensive security validation
      const validation = await fileValidator.validateFile(file);

      toast.dismiss(`validate-${documentTypeName}`);

      if (!validation.isValid) {
        toast.error(`Security validation failed: ${validation.errors.join(', ')}`);
        return;
      }

      // Show warnings if any
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          toast.warning(warning);
        });
      }

      // Record upload attempt
      rateLimiter.recordAttempt();

      // Store file with security metadata
      const fileWithMetadata = Object.assign(file, {
        clientHash: validation.fileInfo.hash,
        uploadTimestamp: new Date().toISOString(),
        sanitizedName: validation.fileInfo.sanitizedName,
        securityScore: validation.fileInfo.securityScore,
        securityValidated: true,
        validationResult: validation
      });

      setUploadedDocuments(prev => {
        const newState = {
          ...prev,
          [documentTypeName]: fileWithMetadata
        };
        console.log('Document uploaded - updating state:', {
          documentTypeName,
          previousState: prev,
          newState,
          totalUploaded: Object.keys(newState).length
        });
        return newState;
      });

      toast.success(`${documentTypeName} validated and ready for upload`, {
        description: `Security score: ${validation.fileInfo.securityScore}/100 (${validation.performance.validationTime.toFixed(0)}ms)`
      });

    } catch (error) {
      console.error('Error in file upload validation:', error);
      toast.error('Failed to validate file. Please try again.');
    }
  }, [fileValidator, rateLimiter]);

  // Remove uploaded file
  const removeUploadedFile = (documentTypeName: string) => {
    setUploadedDocuments(prev => {
      const newUploads = { ...prev };
      delete newUploads[documentTypeName];
      return newUploads;
    });
  };

  // Handle form submission with exact model compliance
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSubmitting(true);

      // Validate form
      const validation = validateForm();
      console.log('Pre-submission validation:', JSON.stringify(validation, null, 2));
      console.log('Form data before submission:', JSON.stringify(formData, null, 2));

      // Manual check for college and department fields (critical validation)
      if (!formData.is_other_college) {
        if (!formData.college || formData.college.toString().trim() === '') {
          console.log('College validation failed - college is required but empty');
          toast.error('Please select a college before submitting');
          return;
        }
        if (!formData.department || formData.department.toString().trim() === '') {
          console.log('Department validation failed - department is required but empty');
          toast.error('Please select a department before submitting');
          return;
        }
      } else {
        if (!formData.other_college_name || !formData.other_college_name.trim()) {
          console.log('Other college name validation failed - required when other college is selected');
          toast.error('Please enter the college name');
          return;
        }
        if (!formData.other_department_name || !formData.other_department_name.trim()) {
          console.log('Other department name validation failed - required when other college is selected');
          toast.error('Please enter the department name');
          return;
        }
      }

      // Document upload validation - REQUIRED for all service types
      if (formData.service_type && currentRequiredDocuments.length > 0) {
        const uploadedCount = Object.keys(uploadedDocuments).length;
        if (uploadedCount === 0) {
          console.log('Document validation failed - no documents uploaded');
          toast.error('Please upload all required documents before submitting');
          return;
        }
        // Check if all required documents are uploaded
        const missingDocs = currentRequiredDocuments.filter(doc => !uploadedDocuments[doc.name]);
        if (missingDocs.length > 0) {
          console.log('Document validation failed - missing required documents:', missingDocs.map(d => d.name));
          toast.error(`Please upload the following required documents: ${missingDocs.map(d => d.name).join(', ')}`);
          return;
        }
      }

      // Form1 destination validation - ALL destination fields REQUIRED
      if (isForm1) {
        const alumniFormData = formData as AlumniApplication;

        if (alumniFormData.is_uog_destination === null || alumniFormData.is_uog_destination === undefined) {
          console.log('Destination validation failed - destination type not selected');
          toast.error('Please select a destination type (Internal UoG or External Institution)');
          return;
        }

        if (alumniFormData.is_uog_destination === true) {
          if (!alumniFormData.uog_college || alumniFormData.uog_college === '') {
            console.log('UoG destination validation failed - college not selected');
            toast.error('Please select a UoG college for internal destination');
            return;
          }
          if (!alumniFormData.uog_department || alumniFormData.uog_department === '') {
            console.log('UoG destination validation failed - department not selected');
            toast.error('Please select a UoG department for internal destination');
            return;
          }
        } else if (alumniFormData.is_uog_destination === false) {
          if (!alumniFormData.order_type || alumniFormData.order_type === '') {
            console.log('External destination validation failed - order type not selected');
            toast.error('Please select an order type for external destination');
            return;
          }
          if (!alumniFormData.institution_name || !alumniFormData.institution_name.trim()) {
            console.log('External destination validation failed - institution name not provided');
            toast.error('Please enter the institution name for external destination');
            return;
          }
          if (!alumniFormData.country || !alumniFormData.country.trim()) {
            console.log('External destination validation failed - country not provided');
            toast.error('Please enter the country for external destination');
            return;
          }
          if (!alumniFormData.institution_address || !alumniFormData.institution_address.trim()) {
            console.log('External destination validation failed - institution address not provided');
            toast.error('Please enter the institution address for external destination');
            return;
          }
          if (!alumniFormData.mailing_agent || alumniFormData.mailing_agent === '') {
            console.log('External destination validation failed - mailing agent not selected');
            toast.error('Please select a mailing agent for external destination');
            return;
          }
        }
      }

      if (!validation.isValid) {
        toast.error('Please fix all validation errors before submitting');
        // Scroll to first error
        const firstErrorField = Object.keys(validation.errors)[0];
        const errorElement = document.getElementById(firstErrorField);
        if (errorElement) {
          errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return;
      }



      // Prepare data for submission according to model structure
      const submissionData = { ...formData };

      // Clean and validate names (trim whitespace as per model clean method)
      if (submissionData.first_name) {
        submissionData.first_name = submissionData.first_name.trim();
      }
      if (submissionData.father_name) {
        submissionData.father_name = submissionData.father_name.trim();
      }
      if (submissionData.last_name) {
        submissionData.last_name = submissionData.last_name.trim();
      }

      // Handle optional student_id (can be null)
      if (!submissionData.student_id || submissionData.student_id.trim() === '') {
        submissionData.student_id = null;
      }

      // Convert Gregorian years to integers (model expects IntegerField)
      if (submissionData.year_of_leaving_gregorian) {
        const year = parseInt(submissionData.year_of_leaving_gregorian as string);
        submissionData.year_of_leaving_gregorian = isNaN(year) ? null : year;
      } else {
        submissionData.year_of_leaving_gregorian = null;
      }

      if (submissionData.year_of_graduation_gregorian) {
        const year = parseInt(submissionData.year_of_graduation_gregorian as string);
        submissionData.year_of_graduation_gregorian = isNaN(year) ? null : year;
      } else {
        submissionData.year_of_graduation_gregorian = null;
      }

      // Handle Ethiopian calendar years (CharField, can be null)
      if (!submissionData.year_of_leaving_ethiopian || submissionData.year_of_leaving_ethiopian.trim() === '') {
        submissionData.year_of_leaving_ethiopian = null;
      }
      if (!submissionData.year_of_graduation_ethiopian || submissionData.year_of_graduation_ethiopian.trim() === '') {
        submissionData.year_of_graduation_ethiopian = null;
      }

      // Handle college/department logic according to model validation
      if (submissionData.is_other_college) {
        // Clear system college/department when using other college
        submissionData.college = null;
        submissionData.department = null;
        // Ensure other college/department names are provided
        if (!submissionData.other_college_name?.trim()) {
          submissionData.other_college_name = null;
        }
        if (!submissionData.other_department_name?.trim()) {
          submissionData.other_department_name = null;
        }
      } else {
        // Clear other college/department when using system college
        submissionData.other_college_name = null;
        submissionData.other_department_name = null;
        // Convert college/department IDs to integers (only if they have valid values)
        if (submissionData.college && submissionData.college.toString().trim() !== '') {
          const collegeId = parseInt(submissionData.college as string);
          submissionData.college = isNaN(collegeId) ? null : collegeId;
        } else {
          submissionData.college = null;
        }

        if (submissionData.department && submissionData.department.toString().trim() !== '') {
          const departmentId = parseInt(submissionData.department as string);
          submissionData.department = isNaN(departmentId) ? null : departmentId;
        } else {
          submissionData.department = null;
        }
      }

      // Handle conditional academic fields based on student_status
      if (submissionData.student_status !== 'Active') {
        submissionData.current_year = null;
      }
      if (submissionData.student_status !== 'Inactive') {
        submissionData.year_of_leaving_ethiopian = null;
        submissionData.year_of_leaving_gregorian = null;
      }
      if (submissionData.student_status !== 'Graduated') {
        submissionData.year_of_graduation_ethiopian = null;
        submissionData.year_of_graduation_gregorian = null;
      }

      // Keep service_type as UUID string (no conversion needed)
      // Service types use UUID primary keys, not integers

      // Handle Form1 destination logic according to model validation
      if (isForm1) {
        const form1Data = submissionData as AlumniApplication;

        if (form1Data.is_uog_destination) {
          // Clear external fields for internal destination
          form1Data.order_type = null;
          form1Data.institution_name = null;
          form1Data.country = null;
          form1Data.institution_address = null;
          form1Data.mailing_agent = null;

          // Convert UoG college/department IDs to integers
          if (form1Data.uog_college) {
            form1Data.uog_college = parseInt(form1Data.uog_college as string);
          }
          if (form1Data.uog_department) {
            form1Data.uog_department = parseInt(form1Data.uog_department as string);
          }
        } else {
          // Clear internal fields for external destination
          form1Data.uog_college = null;
          form1Data.uog_department = null;

          // Ensure external fields are properly set
          if (!form1Data.institution_name?.trim()) form1Data.institution_name = null;
          if (!form1Data.country?.trim()) form1Data.country = null;
          if (!form1Data.institution_address?.trim()) form1Data.institution_address = null;
        }
      }

      // Remove system-managed fields
      delete (submissionData as any).transaction_id; // Auto-generated
      delete (submissionData as any).created_at;
      delete (submissionData as any).updated_at;
      delete (submissionData as any).created_by;
      delete (submissionData as any).updated_by;

      console.log('Submitting data:', JSON.stringify(submissionData, null, 2));
      const validationResult = validateForm();
      console.log('Form validation result:', JSON.stringify(validationResult, null, 2));
      console.log('Is Form1:', isForm1);
      console.log('Current step:', currentStep);
      console.log('Total steps:', totalSteps);

      // Submit application and capture the response with actual transaction ID
      let response;
      if (isForm1) {
        response = await publicAlumniApplicationsAPI.createApplication(submissionData);
      } else {
        response = await publicAlumniApplicationsAPI.createMiniApplication(submissionData);
      }

      // Update the transaction ID with the actual one from the backend
      if (response?.data?.transaction_id) {
        setTransactionId(response.data.transaction_id);
        console.log('Backend transaction ID received:', response.data.transaction_id);
      }

      // Upload documents if any are present
      if (Object.keys(uploadedDocuments).length > 0 && response?.data?.id) {
        console.log('Uploading documents to application:', response.data.id);

        try {
          const documentUploadPromises = Object.entries(uploadedDocuments).map(async ([documentTypeName, file]) => {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('document_type_name', documentTypeName);

            console.log('Uploading document:', documentTypeName, 'for application:', response.data.id);

            if (isForm1) {
              return await publicAlumniApplicationsAPI.uploadDocumentToApplication(response.data.id, formData);
            } else {
              return await publicAlumniApplicationsAPI.uploadDocumentToMiniApplication(response.data.id, formData);
            }
          });

          // Wait for all document uploads to complete
          const documentResponses = await Promise.all(documentUploadPromises);
          console.log('All documents uploaded successfully:', documentResponses.length);

          // Show success message for document uploads
          toast.success(`Application submitted with ${documentResponses.length} document(s) uploaded successfully!`);
        } catch (documentError) {
          console.error('Error uploading documents:', documentError);
          // Don't fail the entire submission if document upload fails
          toast.warning('Application submitted successfully, but some documents failed to upload. Please contact support if needed.');
        }
      }

      // Prepare submission result for modal
      const selectedServiceType = serviceTypes.find(st => st.id.toString() === formData.service_type);
      const applicantName = `${formData.first_name} ${formData.father_name} ${formData.last_name}`;

      setSubmissionResult({
        transaction_id: response?.data?.transaction_id || transactionId,
        applicant_name: applicantName,
        service_type: selectedServiceType?.name || 'Unknown Service',
        service_fee: selectedServiceType?.fee || '0.00'
      });

      // Show success modal instead of redirecting
      setShowSuccessModal(true);

    } catch (error: any) {
      console.error('Error submitting application:', error);
      console.error('Error response status:', error.response?.status);
      console.error('Error response data:', JSON.stringify(error.response?.data, null, 2));
      console.error('Error response headers:', error.response?.headers);

      if (error.response?.data) {
        const errorData = error.response.data;

        // Handle specific error cases
        console.log('Checking email error:', errorData.email);
        console.log('Checking student_id error:', errorData.student_id);

        if (errorData.email && Array.isArray(errorData.email) && errorData.email.some((msg: string) => msg.includes('already exists'))) {
          console.log('Email already exists error detected');
          toast.error('Application Already Exists', {
            description: `An application with the email "${formData.email}" already exists. Please use a different email address or contact support if you need to update your existing application.`,
            duration: 8000
          });
        } else if (errorData.student_id && Array.isArray(errorData.student_id) && errorData.student_id.some((msg: string) => msg.includes('already exists'))) {
          console.log('Student ID already exists error detected');
          toast.error('Student ID Already Used', {
            description: `An application with the Student ID "${formData.student_id}" already exists. Please check your Student ID or contact support if you need to update your existing application.`,
            duration: 8000
          });
        } else {
          // Handle field-specific validation errors
          const errorMessages: string[] = [];
          Object.entries(errorData).forEach(([field, messages]) => {
            if (Array.isArray(messages)) {
              messages.forEach(msg => {
                // Make field names more user-friendly
                const friendlyFieldName = field
                  .replace(/_/g, ' ')
                  .replace(/\b\w/g, l => l.toUpperCase());
                errorMessages.push(`${friendlyFieldName}: ${msg}`);
              });
            } else if (typeof messages === 'string') {
              const friendlyFieldName = field
                .replace(/_/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());
              errorMessages.push(`${friendlyFieldName}: ${messages}`);
            }
          });

          if (errorMessages.length > 0) {
            toast.error('Validation Errors Found', {
              description: errorMessages.slice(0, 2).join(' • ') + (errorMessages.length > 2 ? ` • +${errorMessages.length - 2} more errors` : ''),
              duration: 10000
            });

            // Scroll to the first error field if possible
            const firstErrorField = Object.keys(errorData)[0];
            const errorElement = document.getElementById(firstErrorField);
            if (errorElement) {
              errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
              errorElement.focus();
            }
          } else {
            toast.error('Submission Failed', {
              description: 'Unable to submit your application. Please check your information and try again.',
              duration: 6000
            });
          }
        }
      } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        toast.error('Network Connection Error', {
          description: 'Unable to connect to the server. Please check your internet connection and try again.',
          duration: 8000
        });
      } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        toast.error('Request Timeout', {
          description: 'The request took too long to complete. Please try again.',
          duration: 8000
        });
      } else {
        toast.error('Submission Failed', {
          description: 'An unexpected error occurred while submitting your application. Please try again or contact support if the problem persists.',
          duration: 8000
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (loading.serviceTypes || loading.colleges) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="bg-gradient-to-br from-[#1a73c0] to-[#155a9c] p-4 rounded-2xl w-20 h-20 mx-auto mb-6 flex items-center justify-center">
            <Loader2 className="h-10 w-10 animate-spin text-white" />
          </div>
          <p className="text-gray-600 text-lg font-medium">Loading application form...</p>
          <p className="text-gray-500 text-sm mt-2">Please wait while we prepare your form</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 max-w-5xl py-8">
        {/* Enhanced Header */}
        <div className="mb-8">
          <Button
            variant="outline"
            onClick={() => navigate('/services')}
            className="mb-6 border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0] hover:text-white transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Button>

          {/* Enhanced Form Header Card */}
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] text-white relative overflow-hidden">
              <div className="relative z-10 flex items-center space-x-4">
                <div className="p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-2xl font-bold text-white mb-2">
                    Alumni Application
                  </CardTitle>
                  <CardDescription className="text-blue-100 text-base">
                    {isForm1
                      ? 'Application form for official transcripts and comprehensive academic services'
                      : 'Application form for basic services and certificate requests'
                    }
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Enhanced Multi-Step Form */}
        <Card className="border-0 shadow-2xl bg-white rounded-3xl overflow-hidden">
          <CardContent className="p-8">
            {/* Progress Indicator */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Step {currentStep + 1} of {totalSteps}
                </h3>
                <span className="text-sm text-gray-500">
                  {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
                </span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
                <div
                  className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
                ></div>
              </div>

              {/* Step Indicators */}
              <div className="flex items-center justify-between">
                {steps.map((step, index) => {
                  const StepIcon = step.icon;
                  const isActive = index === currentStep;
                  const isCompleted = completedSteps.includes(index);
                  const isAccessible = index <= currentStep || completedSteps.includes(index);

                  return (
                    <div key={step.id} className="flex flex-col items-center">
                      <button
                        type="button"
                        onClick={() => isAccessible && goToStep(index)}
                        disabled={!isAccessible}
                        className={`
                          w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-200
                          ${isActive
                            ? 'bg-gradient-to-r from-[#1a73c0] to-[#155a9c] text-white shadow-lg scale-110'
                            : isCompleted
                            ? 'bg-green-500 text-white shadow-md hover:shadow-lg'
                            : isAccessible
                            ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          }
                        `}
                      >
                        {isCompleted && !isActive ? (
                          <Check className="h-5 w-5" />
                        ) : (
                          <StepIcon className="h-5 w-5" />
                        )}
                      </button>
                      <span className={`text-xs text-center max-w-20 leading-tight ${
                        isActive ? 'text-[#1a73c0] font-semibold' :
                        isCompleted ? 'text-green-600 font-medium' :
                        'text-gray-500'
                      }`}>
                        {step.title}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Form Content */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="min-h-[400px]">
                {/* Personal Information Step */}
                {currentStep === 0 && (
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                      <h3 className="text-lg font-semibold text-[#1a73c0] mb-4 flex items-center">
                        <User className="h-5 w-5 mr-2" />
                        Personal Information
                      </h3>



                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="first_name" className="text-sm font-medium text-gray-700">
                            First Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="first_name"
                            value={formData.first_name || ''}
                            onChange={(e) => updateField('first_name', e.target.value)}
                            required
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            placeholder="Enter your first name"
                          />
                          {validationErrors.first_name && (
                            <p className="text-red-500 text-sm">{validationErrors.first_name}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="father_name" className="text-sm font-medium text-gray-700">
                            Father Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="father_name"
                            value={formData.father_name || ''}
                            onChange={(e) => updateField('father_name', e.target.value)}
                            required
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            placeholder="Enter your father's name"
                          />
                          {validationErrors.father_name && (
                            <p className="text-red-500 text-sm">{validationErrors.father_name}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="last_name" className="text-sm font-medium text-gray-700">
                            Last Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="last_name"
                            value={formData.last_name || ''}
                            onChange={(e) => updateField('last_name', e.target.value)}
                            required
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            placeholder="Enter your last name"
                          />
                          {validationErrors.last_name && (
                            <p className="text-red-500 text-sm">{validationErrors.last_name}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="student_id" className="text-sm font-medium text-gray-700">
                            Student ID <span className="text-gray-400">(Optional)</span>
                          </Label>
                          <Input
                            id="student_id"
                            value={formData.student_id || ''}
                            onChange={(e) => updateField('student_id', e.target.value)}
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            placeholder="Enter your student ID"
                          />
                          {validationErrors.student_id && (
                            <p className="text-red-500 text-sm">{validationErrors.student_id}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="phone_number" className="text-sm font-medium text-gray-700">
                            Phone Number <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="phone_number"
                            value={formData.phone_number || ''}
                            onChange={(e) => updateField('phone_number', e.target.value)}
                            required
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            placeholder="Enter your phone number"
                          />
                          {validationErrors.phone_number && (
                            <p className="text-red-500 text-sm">{validationErrors.phone_number}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                            Email Address <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="email"
                            type="email"
                            value={formData.email || ''}
                            onChange={(e) => updateField('email', e.target.value)}
                            required
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            placeholder="Enter your email address"
                          />
                          {validationErrors.email && (
                            <p className="text-red-500 text-sm">{validationErrors.email}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Academic Information Step */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                      <h3 className="text-lg font-semibold text-[#1a73c0] mb-6 flex items-center">
                        <School className="h-5 w-5 mr-2" />
                        Academic Information
                      </h3>



                      {/* Basic Academic Info */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="space-y-2">
                          <Label htmlFor="admission_type" className="text-sm font-medium text-gray-700">
                            Admission Type <span className="text-red-500">*</span>
                          </Label>
                          <Select value={formData.admission_type || ''} onValueChange={(value) => updateField('admission_type', value)}>
                            <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                              <SelectValue placeholder="Select admission type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Regular">Regular</SelectItem>
                              <SelectItem value="Evening">Evening</SelectItem>
                              <SelectItem value="Summer">Summer</SelectItem>
                              <SelectItem value="Distance">Distance</SelectItem>
                            </SelectContent>
                          </Select>
                          {validationErrors.admission_type && (
                            <p className="text-red-500 text-sm">{validationErrors.admission_type}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="degree_type" className="text-sm font-medium text-gray-700">
                            Degree Type <span className="text-red-500">*</span>
                          </Label>
                          <Select value={formData.degree_type || ''} onValueChange={(value) => updateField('degree_type', value)}>
                            <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                              <SelectValue placeholder="Select degree type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Diploma">Diploma</SelectItem>
                              <SelectItem value="Degree">Degree</SelectItem>
                              <SelectItem value="Master's">Master's</SelectItem>
                              <SelectItem value="PHD">PHD</SelectItem>
                              <SelectItem value="Sp.Certificate">Sp.Certificate</SelectItem>
                            </SelectContent>
                          </Select>
                          {validationErrors.degree_type && (
                            <p className="text-red-500 text-sm">{validationErrors.degree_type}</p>
                          )}
                        </div>
                      </div>

                      {/* College Selection */}
                      <div className="space-y-4 mb-6">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="is_other_college"
                            checked={formData.is_other_college || false}
                            onCheckedChange={(checked) => {
                              updateField('is_other_college', checked);
                              if (checked) {
                                updateField('college', '');
                                updateField('department', '');
                              } else {
                                updateField('other_college_name', '');
                                updateField('other_department_name', '');
                              }
                            }}
                          />
                          <Label htmlFor="is_other_college" className="text-sm font-medium text-gray-700">
                            College is not in the list
                          </Label>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {!formData.is_other_college ? (
                            <>
                              <div className="space-y-2">
                                <Label htmlFor="college" className="text-sm font-medium text-gray-700">
                                  College <span className="text-red-500">*</span>
                                </Label>
                                <Select
                                  value={formData.college || ''}
                                  onValueChange={(value) => {
                                    updateField('college', value);
                                    loadDepartments(value);
                                  }}
                                >
                                  <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                    <SelectValue placeholder="Select college" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {colleges.map((college) => (
                                      <SelectItem key={college.id} value={college.id.toString()}>
                                        {college.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                {validationErrors.college && (
                                  <p className="text-red-500 text-sm">{validationErrors.college}</p>
                                )}
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="department" className="text-sm font-medium text-gray-700">
                                  Department <span className="text-red-500">*</span>
                                </Label>
                                <Select value={formData.department || ''} onValueChange={(value) => updateField('department', value)}>
                                  <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                    <SelectValue placeholder="Select department" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {currentDepartments.map((department) => (
                                      <SelectItem key={department.id} value={department.id.toString()}>
                                        {department.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                {validationErrors.department && (
                                  <p className="text-red-500 text-sm">{validationErrors.department}</p>
                                )}
                              </div>
                            </>
                          ) : (
                            <>
                              <div className="space-y-2">
                                <Label htmlFor="other_college_name" className="text-sm font-medium text-gray-700">
                                  College Name <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="other_college_name"
                                  value={formData.other_college_name || ''}
                                  onChange={(e) => updateField('other_college_name', e.target.value)}
                                  className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                  placeholder="Enter college name"
                                  maxLength={100}
                                />
                                {validationErrors.other_college_name && (
                                  <p className="text-red-500 text-sm">{validationErrors.other_college_name}</p>
                                )}
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="other_department_name" className="text-sm font-medium text-gray-700">
                                  Department Name <span className="text-red-500">*</span>
                                </Label>
                                <Input
                                  id="other_department_name"
                                  value={formData.other_department_name || ''}
                                  onChange={(e) => updateField('other_department_name', e.target.value)}
                                  className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                  placeholder="Enter department name"
                                  maxLength={100}
                                />
                                {validationErrors.other_department_name && (
                                  <p className="text-red-500 text-sm">{validationErrors.other_department_name}</p>
                                )}
                              </div>
                            </>
                          )}
                        </div>
                      </div>

                      {/* Student Status */}
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="student_status" className="text-sm font-medium text-gray-700">
                            Student Status <span className="text-red-500">*</span>
                          </Label>
                          <Select value={formData.student_status || ''} onValueChange={(value) => updateField('student_status', value)}>
                            <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                              <SelectValue placeholder="Select student status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Active">Active</SelectItem>
                              <SelectItem value="Inactive">Inactive</SelectItem>
                              <SelectItem value="Graduated">Graduated</SelectItem>
                            </SelectContent>
                          </Select>
                          {validationErrors.student_status && (
                            <p className="text-red-500 text-sm">{validationErrors.student_status}</p>
                          )}
                        </div>

                        {/* Conditional Fields Based on Student Status */}
                        {formData.student_status === 'Active' && (
                          <div className="space-y-2">
                            <Label htmlFor="current_year" className="text-sm font-medium text-gray-700">
                              Current Year <span className="text-red-500">*</span>
                            </Label>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                              {['1st year', '2nd year', '3rd year', '4th year', '5th year', '6th year', '7th year', '8th year'].map((year) => (
                                <label key={year} className="flex items-center space-x-2 cursor-pointer">
                                  <input
                                    type="radio"
                                    name="current_year"
                                    value={year}
                                    checked={formData.current_year === year}
                                    onChange={(e) => updateField('current_year', e.target.value)}
                                    className="text-[#1a73c0] focus:ring-[#1a73c0]"
                                  />
                                  <span className="text-sm text-gray-700">{year}</span>
                                </label>
                              ))}
                            </div>
                            {validationErrors.current_year && (
                              <p className="text-red-500 text-sm">{validationErrors.current_year}</p>
                            )}
                          </div>
                        )}

                        {formData.student_status === 'Inactive' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="year_of_leaving_ethiopian" className="text-sm font-medium text-gray-700">
                                Year of Leaving (Ethiopian Calendar)
                              </Label>
                              <Input
                                id="year_of_leaving_ethiopian"
                                value={formData.year_of_leaving_ethiopian || ''}
                                onChange={(e) => updateField('year_of_leaving_ethiopian', e.target.value)}
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                placeholder="e.g., 2010 (1954-2017)"
                                maxLength={10}
                              />
                              {validationErrors.year_of_leaving_ethiopian && (
                                <p className="text-red-500 text-sm">{validationErrors.year_of_leaving_ethiopian}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="year_of_leaving_gregorian" className="text-sm font-medium text-gray-700">
                                Year of Leaving (Gregorian Calendar)
                              </Label>
                              <Input
                                id="year_of_leaving_gregorian"
                                type="number"
                                value={formData.year_of_leaving_gregorian || ''}
                                onChange={(e) => updateField('year_of_leaving_gregorian', e.target.value)}
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                placeholder="e.g., 2020 (1960-2025)"
                                min={1960}
                                max={2025}
                              />
                              {validationErrors.year_of_leaving_gregorian && (
                                <p className="text-red-500 text-sm">{validationErrors.year_of_leaving_gregorian}</p>
                              )}
                            </div>
                          </div>
                        )}

                        {formData.student_status === 'Graduated' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="year_of_graduation_ethiopian" className="text-sm font-medium text-gray-700">
                                Year of Graduation (Ethiopian Calendar)
                              </Label>
                              <Input
                                id="year_of_graduation_ethiopian"
                                value={formData.year_of_graduation_ethiopian || ''}
                                onChange={(e) => updateField('year_of_graduation_ethiopian', e.target.value)}
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                placeholder="e.g., 2010 (1954-2017)"
                                maxLength={10}
                              />
                              {validationErrors.year_of_graduation_ethiopian && (
                                <p className="text-red-500 text-sm">{validationErrors.year_of_graduation_ethiopian}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="year_of_graduation_gregorian" className="text-sm font-medium text-gray-700">
                                Year of Graduation (Gregorian Calendar)
                              </Label>
                              <Input
                                id="year_of_graduation_gregorian"
                                type="number"
                                value={formData.year_of_graduation_gregorian || ''}
                                onChange={(e) => updateField('year_of_graduation_gregorian', e.target.value)}
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                placeholder="e.g., 2020 (1960-2025)"
                                min={1960}
                                max={2025}
                              />
                              {validationErrors.year_of_graduation_gregorian && (
                                <p className="text-red-500 text-sm">{validationErrors.year_of_graduation_gregorian}</p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Service Information Step */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                      <h3 className="text-lg font-semibold text-[#1a73c0] mb-6 flex items-center">
                        <FileText className="h-5 w-5 mr-2" />
                        Service Information
                      </h3>



                      {/* Service Type Selection */}
                      <div className="space-y-2 mb-6">
                        <Label htmlFor="service_type" className="text-sm font-medium text-gray-700">
                          Service Type <span className="text-red-500">*</span>
                        </Label>

                        {preSelectedServiceType ? (
                          // Show pre-selected service type as read-only display
                          <div className="relative">
                            <div className="h-12 border-2 border-green-200 bg-green-50 rounded-xl flex items-center px-4 transition-all duration-200">
                              <div className="flex items-center justify-between w-full">
                                <div className="flex items-center space-x-3">
                                  <div className="p-1 bg-green-500 rounded-full">
                                    <svg className="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  </div>
                                  <span className="font-medium text-gray-800">
                                    {serviceTypes.find(st => st.id.toString() === preSelectedServiceType)?.name || 'Selected Service'}
                                  </span>
                                </div>
                                <span className="text-green-600 font-bold text-lg">
                                  ${serviceTypes.find(st => st.id.toString() === preSelectedServiceType)?.fee || '0.00'}
                                </span>
                              </div>
                            </div>
                            <div className="absolute top-2 right-2">
                              <div className="px-2 py-1 bg-green-500 text-white text-xs font-bold rounded-full">
                                PRE-SELECTED
                              </div>
                            </div>
                          </div>
                        ) : (
                          // Show dropdown for manual selection
                          <Select
                            value={formData.service_type || ''}
                            onValueChange={(value) => {
                              updateField('service_type', value);
                              loadRequiredDocuments(value);
                              // Only clear uploaded documents if service type actually changes to a different value
                              if (formData.service_type && formData.service_type !== value) {
                                console.log('Service type dropdown changed - clearing uploaded documents:', {
                                  oldServiceType: formData.service_type,
                                  newServiceType: value,
                                  currentUploads: Object.keys(uploadedDocuments)
                                });
                                setUploadedDocuments({});
                              }
                            }}
                          >
                            <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                              <SelectValue placeholder="Select service type" />
                            </SelectTrigger>
                            <SelectContent>
                              {serviceTypes.map((serviceType) => (
                                <SelectItem key={serviceType.id} value={serviceType.id.toString()}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>{serviceType.name}</span>
                                    <span className="text-green-600 font-semibold ml-2">${serviceType.fee}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}

                        {validationErrors.service_type && (
                          <p className="text-red-500 text-sm">{validationErrors.service_type}</p>
                        )}

                        {preSelectedServiceType && (
                          <p className="text-green-600 text-sm flex items-center">
                            <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Service type selected from services page
                          </p>
                        )}
                      </div>

                      {/* Required Documents */}
                      {currentRequiredDocuments.length > 0 && (
                        <div className="space-y-4">
                          <div className="flex items-center space-x-2 mb-4">
                            <Shield className="h-5 w-5 text-[#1a73c0]" />
                            <h4 className="text-md font-semibold text-gray-800">Required Documents</h4>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {currentRequiredDocuments.map((docType) => {
                              const isUploaded = uploadedDocuments[docType.name];
                              const uploadId = `upload-${docType.name.replace(/\s+/g, '-').toLowerCase()}`;

                              return (
                                <div key={docType.id} className="border-2 border-dashed border-gray-300 rounded-xl p-4 hover:border-[#1a73c0] transition-all duration-200">
                                  <div className="flex items-center justify-between mb-2">
                                    <h5 className="font-medium text-gray-800">{docType.name}</h5>
                                    {isUploaded ? (
                                      <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-5 w-5 text-green-500" />
                                        <button
                                          type="button"
                                          onClick={() => removeUploadedFile(docType.name)}
                                          className="text-red-500 hover:text-red-700"
                                        >
                                          <X className="h-4 w-4" />
                                        </button>
                                      </div>
                                    ) : (
                                      <AlertCircle className="h-5 w-5 text-orange-500" />
                                    )}
                                  </div>

                                  {docType.description && (
                                    <p className="text-sm text-gray-600 mb-3">{docType.description}</p>
                                  )}

                                  {isUploaded ? (
                                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                                      <div className="flex items-center space-x-2">
                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                        <span className="text-sm text-green-700 font-medium">
                                          {isUploaded.name}
                                        </span>
                                      </div>
                                      <p className="text-xs text-green-600 mt-1">
                                        Security Score: {(isUploaded as any).securityScore || 'N/A'}/100
                                      </p>
                                    </div>
                                  ) : (
                                    <div className="space-y-2">
                                      <input
                                        id={uploadId}
                                        type="file"
                                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                        onChange={(e) => {
                                          const file = e.target.files?.[0];
                                          if (file) {
                                            handleFileUpload(docType.name, file);
                                          }
                                        }}
                                        className="hidden"
                                      />
                                      <label
                                        htmlFor={uploadId}
                                        className="flex items-center justify-center space-x-2 bg-white border-2 border-gray-300 rounded-lg p-3 cursor-pointer hover:border-[#1a73c0] hover:bg-blue-50 transition-all duration-200"
                                      >
                                        <Upload className="h-4 w-4 text-gray-500" />
                                        <span className="text-sm text-gray-700">Click to upload</span>
                                      </label>
                                      <p className="text-xs text-gray-500">
                                        Supported: PDF, JPG, PNG, DOC, DOCX (Max 10MB)
                                      </p>
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>

                          {/* Upload Progress Summary */}
                          <div className="bg-gray-50 rounded-lg p-4 mt-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-700">Upload Progress</span>
                              <span className="text-sm text-gray-600">
                                {Object.keys(uploadedDocuments).length} of {currentRequiredDocuments.length} documents
                              </span>
                            </div>

                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${(Object.keys(uploadedDocuments).length / currentRequiredDocuments.length) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Destination Step (Form1 only) */}
                {currentStep === 3 && isForm1 && (
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                      <h3 className="text-lg font-semibold text-[#1a73c0] mb-6 flex items-center">
                        <MapPin className="h-5 w-5 mr-2" />
                        Destination Information
                      </h3>



                      {/* Destination Type Selection */}
                      <div className="space-y-4 mb-6">
                        <Label className="text-sm font-medium text-gray-700">
                          Destination Type <span className="text-red-500">*</span>
                        </Label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <label className="flex items-center space-x-3 p-4 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-[#1a73c0] transition-all duration-200">
                            <input
                              type="radio"
                              name="destination_type"
                              value="internal"
                              checked={formData.is_uog_destination === true}
                              onChange={() => {
                                updateField('is_uog_destination', true);
                                // Clear external fields
                                updateField('order_type', '');
                                updateField('institution_name', '');
                                updateField('country', '');
                                updateField('institution_address', '');
                                updateField('mailing_agent', '');
                              }}
                              className="text-[#1a73c0] focus:ring-[#1a73c0]"
                            />
                            <div>
                              <div className="font-medium text-gray-800">Internal (UoG)</div>
                              <div className="text-sm text-gray-600">Send to University of Gondar</div>
                            </div>
                          </label>
                          <label className="flex items-center space-x-3 p-4 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-[#1a73c0] transition-all duration-200">
                            <input
                              type="radio"
                              name="destination_type"
                              value="external"
                              checked={formData.is_uog_destination === false}
                              onChange={() => {
                                updateField('is_uog_destination', false);
                                // Clear internal fields
                                updateField('uog_college', '');
                                updateField('uog_department', '');
                              }}
                              className="text-[#1a73c0] focus:ring-[#1a73c0]"
                            />
                            <div>
                              <div className="font-medium text-gray-800">External Institution</div>
                              <div className="text-sm text-gray-600">Send to external institution</div>
                            </div>
                          </label>
                        </div>
                        {validationErrors.is_uog_destination && (
                          <p className="text-red-500 text-sm">{validationErrors.is_uog_destination}</p>
                        )}
                      </div>

                      {/* Internal Destination Fields */}
                      {formData.is_uog_destination && (
                        <div className="space-y-4">
                          <h4 className="font-medium text-gray-800 mb-4">University of Gondar Destination</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="uog_college" className="text-sm font-medium text-gray-700">
                                UoG College <span className="text-red-500">*</span>
                              </Label>
                              <Select
                                value={formData.uog_college || ''}
                                onValueChange={(value) => {
                                  updateField('uog_college', value);
                                  loadDepartments(value);
                                }}
                              >
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select UoG college" />
                                </SelectTrigger>
                                <SelectContent>
                                  {colleges.map((college) => (
                                    <SelectItem key={college.id} value={college.id.toString()}>
                                      {college.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              {validationErrors.uog_college && (
                                <p className="text-red-500 text-sm">{validationErrors.uog_college}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="uog_department" className="text-sm font-medium text-gray-700">
                                UoG Department <span className="text-red-500">*</span>
                              </Label>
                              <Select value={formData.uog_department || ''} onValueChange={(value) => updateField('uog_department', value)}>
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select UoG department" />
                                </SelectTrigger>
                                <SelectContent>
                                  {uogDepartments.map((department) => (
                                    <SelectItem key={department.id} value={department.id.toString()}>
                                      {department.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              {validationErrors.uog_department && (
                                <p className="text-red-500 text-sm">{validationErrors.uog_department}</p>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* External Destination Fields */}
                      {formData.is_uog_destination === false && (
                        <div className="space-y-6">
                          <h4 className="font-medium text-gray-800 mb-4">External Institution Details</h4>

                          {/* Order Type and Mailing Agent */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="order_type" className="text-sm font-medium text-gray-700">
                                Order Type <span className="text-red-500">*</span>
                              </Label>
                              <Select value={formData.order_type || ''} onValueChange={(value) => updateField('order_type', value)}>
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select order type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Local">Local</SelectItem>
                                  <SelectItem value="International">International</SelectItem>
                                  <SelectItem value="Legal Delegate">Legal Delegate</SelectItem>
                                </SelectContent>
                              </Select>
                              {validationErrors.order_type && (
                                <p className="text-red-500 text-sm">{validationErrors.order_type}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="mailing_agent" className="text-sm font-medium text-gray-700">
                                Mailing Agent <span className="text-red-500">*</span>
                              </Label>
                              <Select value={formData.mailing_agent || ''} onValueChange={(value) => updateField('mailing_agent', value)}>
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select mailing agent" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Normal Postal">Normal Postal</SelectItem>
                                  <SelectItem value="DHL">DHL</SelectItem>
                                  <SelectItem value="SMS">SMS</SelectItem>
                                </SelectContent>
                              </Select>
                              {validationErrors.mailing_agent && (
                                <p className="text-red-500 text-sm">{validationErrors.mailing_agent}</p>
                              )}
                            </div>
                          </div>

                          {/* Institution Details */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="institution_name" className="text-sm font-medium text-gray-700">
                                Institution Name <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="institution_name"
                                value={formData.institution_name || ''}
                                onChange={(e) => updateField('institution_name', e.target.value)}
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                placeholder="Enter institution name"
                                maxLength={100}
                              />
                              {validationErrors.institution_name && (
                                <p className="text-red-500 text-sm">{validationErrors.institution_name}</p>
                              )}
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="country" className="text-sm font-medium text-gray-700">
                                Country <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="country"
                                value={formData.country || ''}
                                onChange={(e) => updateField('country', e.target.value)}
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                                placeholder="Enter country"
                                maxLength={100}
                              />
                              {validationErrors.country && (
                                <p className="text-red-500 text-sm">{validationErrors.country}</p>
                              )}
                            </div>
                          </div>

                          {/* Institution Address */}
                          <div className="space-y-2">
                            <Label htmlFor="institution_address" className="text-sm font-medium text-gray-700">
                              Institution Address <span className="text-red-500">*</span>
                            </Label>
                            <textarea
                              id="institution_address"
                              value={formData.institution_address || ''}
                              onChange={(e) => updateField('institution_address', e.target.value)}
                              className="w-full h-24 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200 p-3 resize-none"
                              placeholder="Enter complete institution address"
                              maxLength={500}
                            />
                            {validationErrors.institution_address && (
                              <p className="text-red-500 text-sm">{validationErrors.institution_address}</p>
                            )}
                            <p className="text-xs text-gray-500">
                              {(formData.institution_address || '').length}/500 characters
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Summary Step - Review & Submit */}
                {currentStep === totalSteps - 1 && (
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-2xl border border-green-100">
                      <h3 className="text-lg font-semibold text-[#1a73c0] mb-6 flex items-center">
                        <CheckCircle className="h-5 w-5 mr-2" />
                        Review & Submit Application
                      </h3>



                      {/* Application Summary */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Personal Information */}
                        <div className="bg-white p-4 rounded-xl border border-gray-200">
                          <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                            <User className="h-4 w-4 mr-2 text-blue-500" />
                            Personal Information
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Full Name:</span>
                              <span className="font-medium">{formData.first_name} {formData.father_name} {formData.last_name}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Email:</span>
                              <span className="font-medium">{formData.email}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Phone:</span>
                              <span className="font-medium">{formData.phone_number}</span>
                            </div>
                            {formData.student_id && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">Student ID:</span>
                                <span className="font-medium">{formData.student_id}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Academic Information */}
                        <div className="bg-white p-4 rounded-xl border border-gray-200">
                          <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                            <School className="h-4 w-4 mr-2 text-blue-500" />
                            Academic Information
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Degree Type:</span>
                              <span className="font-medium">{formData.degree_type}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Admission Type:</span>
                              <span className="font-medium">{formData.admission_type}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Status:</span>
                              <span className="font-medium">{formData.student_status}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">College:</span>
                              <span className="font-medium">
                                {formData.is_other_college
                                  ? formData.other_college_name
                                  : colleges.find(c => c.id.toString() === formData.college)?.name || 'Not selected'
                                }
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Service Information */}
                        <div className="bg-white p-4 rounded-xl border border-gray-200">
                          <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                            <FileText className="h-4 w-4 mr-2 text-blue-500" />
                            Service Information
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Service Type:</span>
                              <span className="font-medium">
                                {serviceTypes.find(st => st.id.toString() === formData.service_type)?.name || 'Not selected'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Service Fee:</span>
                              <span className="font-medium text-green-600">
                                ${serviceTypes.find(st => st.id.toString() === formData.service_type)?.fee || '0.00'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Documents:</span>
                              <span className="font-medium">
                                {Object.keys(uploadedDocuments).length} uploaded
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Destination Information (Form1 only) */}
                        {isForm1 && (
                          <div className="bg-white p-4 rounded-xl border border-gray-200">
                            <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                              <MapPin className="h-4 w-4 mr-2 text-blue-500" />
                              Destination Information
                            </h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Destination Type:</span>
                                <span className="font-medium">
                                  {formData.is_uog_destination ? 'University of Gondar' : 'External Institution'}
                                </span>
                              </div>
                              {formData.is_uog_destination ? (
                                <>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">UoG College:</span>
                                    <span className="font-medium">
                                      {colleges.find(c => c.id.toString() === formData.uog_college)?.name || 'Not selected'}
                                    </span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">UoG Department:</span>
                                    <span className="font-medium">
                                      {departments[formData.uog_college as string]?.find(d => d.id.toString() === formData.uog_department)?.name || 'Not selected'}
                                    </span>
                                  </div>
                                </>
                              ) : (
                                <>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">Institution:</span>
                                    <span className="font-medium">{formData.institution_name}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">Country:</span>
                                    <span className="font-medium">{formData.country}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">Mailing Agent:</span>
                                    <span className="font-medium">{formData.mailing_agent}</span>
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                        )}
                      </div>


                    </div>
                  </div>
                )}
              </div>

              {/* Navigation Buttons - Inside Form */}
              <div className="flex items-center justify-between pt-8 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={goToPreviousStep}
                  disabled={currentStep === 0}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Previous</span>
                </Button>

                <div className="flex items-center space-x-4">
                  {currentStep === totalSteps - 1 ? (
                    <div className="flex flex-col items-end space-y-2">
                      {!isSubmitReady && (
                        <div className="text-sm text-red-500 text-right space-y-1">
                          {!isFormValid && (
                            <p>Please complete all required fields</p>
                          )}
                          {!isDocumentValidationPassed && (
                            <p>Please upload all required documents</p>
                          )}
                          {!isDestinationValidationPassed && isForm1 && (
                            <p>Please complete destination information</p>
                          )}
                        </div>
                      )}
                      <Button
                        type="submit"
                        disabled={submitting || !isSubmitReady}
                        onClick={handleSubmit}
                        className={`px-8 py-3 text-lg font-semibold transition-all duration-200 ${
                          isSubmitReady
                            ? 'bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#1a73c0] text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                      >
                        {submitting ? (
                          <>
                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                            Submitting Application...
                          </>
                        ) : (
                          <>
                            <Check className="h-5 w-5 mr-2" />
                            Submit Application
                          </>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <Button
                      type="button"
                      onClick={goToNextStep}
                      className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#1a73c0] text-white flex items-center space-x-2"
                    >
                      <span>Next</span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Success Modal */}
        {showSuccessModal && submissionResult && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4">
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[95vh] overflow-y-auto mx-2 sm:mx-4">
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-green-600 to-emerald-600 p-4 sm:p-6 lg:p-8 text-white">
                <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-3 sm:space-y-0 sm:space-x-4">
                  <div className="flex-shrink-0">
                    <div className="p-3 bg-white/20 rounded-full">
                      <CheckCircle className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
                    </div>
                  </div>
                  <div className="text-center sm:text-left flex-1">
                    <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-1">
                      Application Submitted Successfully!
                    </h3>
                    <p className="text-green-100 text-sm sm:text-base lg:text-lg">
                      Your application has been received and is being processed
                    </p>
                  </div>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-4 sm:p-6 lg:p-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">

                  {/* Application Details Card */}
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 sm:p-6 border border-blue-200 shadow-lg">
                    <div className="flex items-center space-x-3 mb-4 sm:mb-6">
                      <div className="p-3 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-md">
                        <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h4 className="font-bold text-gray-800 text-xl">Application Details</h4>
                    </div>

                    <div className="space-y-4">
                      {/* Applicant Name */}
                      <div className="bg-white rounded-xl p-4 border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
                          <span className="text-gray-600 font-semibold text-sm">Applicant Name</span>
                          <span className="font-bold text-gray-800 text-lg">{submissionResult.applicant_name}</span>
                        </div>
                      </div>

                      {/* Service Type */}
                      <div className="bg-white rounded-xl p-4 border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
                          <span className="text-gray-600 font-semibold text-sm">Service Type</span>
                          <span className="font-bold text-gray-800 text-lg">{submissionResult.service_type}</span>
                        </div>
                      </div>

                      {/* Service Fee */}
                      <div className="bg-white rounded-xl p-4 border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
                          <span className="text-gray-600 font-semibold text-sm">Service Fee</span>
                          <span className="font-bold text-green-600 text-xl">{submissionResult.service_fee} ETB</span>
                        </div>
                      </div>

                      {/* Transaction ID */}
                      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-4 text-white shadow-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
                          <span className="font-semibold text-blue-100 text-sm">Transaction ID</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-bold text-white text-xl tracking-wider">{submissionResult.transaction_id}</span>
                            <button
                              onClick={() => {
                                navigator.clipboard.writeText(submissionResult.transaction_id);
                                toast.success('Transaction ID copied!');
                              }}
                              className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                            >
                              <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Important Notes & Next Steps Card */}
                  <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-xl p-4 sm:p-6 border border-amber-200 shadow-lg">
                    <div className="flex items-center space-x-3 mb-4 sm:mb-6">
                      <div className="p-3 bg-gradient-to-r from-amber-600 to-orange-600 rounded-xl shadow-md">
                        <svg className="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h4 className="font-bold text-amber-800 text-xl">Important Notes & Next Steps</h4>
                    </div>

                    <div className="space-y-4">
                      <div className="bg-white rounded-xl p-4 border border-amber-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-start space-x-3">
                          <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div>
                            <h5 className="font-bold text-amber-800 text-sm mb-1">Keep Your Reference</h5>
                            <p className="text-amber-700 text-sm">Save your transaction ID for all future communications and tracking</p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white rounded-xl p-4 border border-amber-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-start space-x-3">
                          <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div>
                            <h5 className="font-bold text-amber-800 text-sm mb-1">Processing Timeline</h5>
                            <p className="text-amber-700 text-sm">Your application will be processed within 3-5 business days</p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white rounded-xl p-4 border border-amber-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-start space-x-3">
                          <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div>
                            <h5 className="font-bold text-amber-800 text-sm mb-1">Email Notifications</h5>
                            <p className="text-amber-700 text-sm">You'll receive status updates and completion notifications via email</p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white rounded-xl p-4 border border-amber-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-start space-x-3">
                          <div className="w-3 h-3 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mt-1.5 flex-shrink-0"></div>
                          <div>
                            <h5 className="font-bold text-amber-800 text-sm mb-1">Need Assistance?</h5>
                            <p className="text-amber-700 text-sm">Contact our support team if you have any questions or concerns</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="bg-gray-50 px-4 sm:px-6 lg:px-8 py-4 sm:py-6 border-t border-gray-200">
                <div className="flex flex-col space-y-3 sm:space-y-4">
                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                    <button
                      onClick={() => {
                        setShowSuccessModal(false);
                        navigate('/services');
                      }}
                      className="flex-1 bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#1a73c0] text-white py-3 sm:py-4 px-4 sm:px-6 rounded-xl font-bold text-sm sm:text-base shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center space-x-2"
                    >
                      <svg className="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                      <span>Continue to Services</span>
                    </button>
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(submissionResult.transaction_id);
                        toast.success('Transaction ID copied!');
                      }}
                      className="sm:w-auto px-4 sm:px-6 py-3 sm:py-4 border-2 border-gray-300 rounded-xl text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 font-semibold flex items-center justify-center space-x-2 text-sm sm:text-base"
                    >
                      <svg className="h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      <span className="hidden sm:inline">Copy Transaction ID</span>
                      <span className="sm:hidden">Copy ID</span>
                    </button>
                  </div>

                  {/* Support Information */}
                  <div className="text-center">
                    <p className="text-gray-500 text-xs sm:text-sm">
                      Need help? Contact our support team at
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800 font-medium ml-1">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  );
});

AdvancedAlumniApplicationForm.displayName = 'AdvancedAlumniApplicationForm';

export default AdvancedAlumniApplicationForm;
