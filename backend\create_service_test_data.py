#!/usr/bin/env python
"""
Create test data for Service Type testing.
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.certificate_type.models import CertificateType
from setups.service_type.models import ServiceType
from decimal import Decimal

def create_test_data():
    print("Creating test data...")
    
    # Create certificate types if they don't exist
    cert_types_data = [
        'Academic Transcript',
        'Diploma Certificate', 
        'Verification Letter',
        'Grade Report',
        'Enrollment Certificate'
    ]
    
    cert_types = []
    for name in cert_types_data:
        cert_type, created = CertificateType.objects.get_or_create(
            name=name,
            defaults={'is_active': True}
        )
        cert_types.append(cert_type)
        if created:
            print(f"✅ Created certificate type: {name}")
        else:
            print(f"📋 Certificate type exists: {name}")
    
    # Create service types
    service_types_data = [
        {
            'name': 'Basic Academic Verification',
            'fee': Decimal('15.00'),
            'is_active': True,
            'cert_types': cert_types[:2]
        },
        {
            'name': 'Premium Document Service',
            'fee': Decimal('35.50'),
            'is_active': True,
            'cert_types': cert_types[1:4]
        },
        {
            'name': 'Express Processing',
            'fee': Decimal('50.00'),
            'is_active': False,
            'cert_types': cert_types[2:]
        }
    ]
    
    for service_data in service_types_data:
        service_type, created = ServiceType.objects.get_or_create(
            name=service_data['name'],
            defaults={
                'fee': service_data['fee'],
                'is_active': service_data['is_active']
            }
        )
        
        if created:
            print(f"✅ Created service type: {service_data['name']}")
            # Add certificate types
            service_type.document_types.add(*service_data['cert_types'])
            print(f"   Added {len(service_data['cert_types'])} document types")
        else:
            print(f"📋 Service type exists: {service_data['name']}")
    
    # Print summary
    print(f"\n📊 Summary:")
    print(f"Certificate types: {CertificateType.objects.count()}")
    print(f"Service types: {ServiceType.objects.count()}")
    print(f"Active service types: {ServiceType.objects.filter(is_active=True).count()}")

if __name__ == '__main__':
    create_test_data()
