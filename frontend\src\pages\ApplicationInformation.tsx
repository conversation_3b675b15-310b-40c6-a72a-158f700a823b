import React, { useEffect } from 'react';
import ApplicationInformationView from '../components/ApplicationInformationView';
import AdminLayout from '../components/AdminLayout';

const ApplicationInformationPage: React.FC = () => {
  // Set document title when component mounts
  useEffect(() => {
    document.title = 'Application Information | Admin Portal';
  }, []);

  return (
    <AdminLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Application Information</h1>
        <ApplicationInformationView />
      </div>
    </AdminLayout>
  );
};

export default ApplicationInformationPage;
