import django_filters
from django.db.models import Q
from .models import ServiceRequest


class ServiceRequestFilter(django_filters.FilterSet):
    """
    Advanced filtering for ServiceRequest model.
    """
    
    # Text search filters
    name = django_filters.CharFilter(method='filter_by_name', label='Full Name')
    email = django_filters.CharFilter(lookup_expr='icontains', label='Email')
    mobile = django_filters.CharFilter(lookup_expr='icontains', label='Mobile')
    
    # Foreign key filters
    service_type = django_filters.UUIDFilter(field_name='service_type__id', label='Service Type')
    service_type_name = django_filters.CharFilter(
        field_name='service_type__name',
        lookup_expr='icontains',
        label='Service Type Name'
    )
    admission_type = django_filters.NumberFilter(field_name='admission_type__id', label='Admission Type')
    degree = django_filters.NumberFilter(field_name='degree__id', label='Degree')
    college = django_filters.NumberFilter(field_name='college__id', label='College')
    department = django_filters.NumberFilter(field_name='department__id', label='Department')
    
    # Status filters
    status = django_filters.ChoiceFilter(
        choices=ServiceRequest._meta.get_field('status').choices,
        label='Status'
    )
    status_in = django_filters.BaseInFilter(
        field_name='status',
        lookup_expr='in',
        label='Status (multiple)'
    )
    
    # Student status filters
    student_status = django_filters.ChoiceFilter(
        choices=ServiceRequest.STUDENT_STATUS_CHOICES,
        label='Student Status'
    )
    year_type = django_filters.ChoiceFilter(
        choices=ServiceRequest.YEAR_TYPE_CHOICES,
        label='Year Type'
    )
    
    # Date filters
    created_after = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte',
        label='Created After'
    )
    created_before = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte',
        label='Created Before'
    )
    created_date = django_filters.DateFilter(
        field_name='created_at__date',
        label='Created Date'
    )
    
    # Year filters
    year_ec = django_filters.NumberFilter(label='Ethiopian Calendar Year')
    year_gc = django_filters.NumberFilter(label='Gregorian Calendar Year')
    graduation_year_ec = django_filters.NumberFilter(label='Graduation Year (EC)')
    graduation_year_gc = django_filters.NumberFilter(label='Graduation Year (GC)')
    
    # Mailing filters
    mailing_destination = django_filters.ChoiceFilter(
        choices=ServiceRequest.MAILING_DESTINATION_CHOICES,
        label='Mailing Destination'
    )
    institute_country = django_filters.CharFilter(
        lookup_expr='icontains',
        label='Institute Country'
    )
    
    # Boolean filters
    has_documents = django_filters.BooleanFilter(
        method='filter_has_documents',
        label='Has Document Uploads'
    )
    is_college_other = django_filters.BooleanFilter(label='College is Other')
    is_department_other = django_filters.BooleanFilter(label='Department is Other')
    
    # User filters
    created_by = django_filters.NumberFilter(field_name='created_by__id', label='Created By')
    created_by_username = django_filters.CharFilter(
        field_name='created_by__username',
        lookup_expr='icontains',
        label='Created By Username'
    )
    
    class Meta:
        model = ServiceRequest
        fields = [
            'name', 'email', 'mobile', 'service_type', 'service_type_name',
            'admission_type', 'degree', 'college', 'department', 'status',
            'status_in', 'student_status', 'year_type', 'created_after',
            'created_before', 'created_date', 'year_ec', 'year_gc',
            'graduation_year_ec', 'graduation_year_gc', 'mailing_destination',
            'institute_country', 'has_documents', 'is_college_other',
            'is_department_other', 'created_by', 'created_by_username'
        ]
    
    def filter_by_name(self, queryset, name, value):
        """Filter by full name (first, middle, last name)."""
        if not value:
            return queryset
        
        # Split the search term into words
        words = value.split()
        
        # Create Q objects for each word to search in first, middle, and last names
        q_objects = Q()
        for word in words:
            q_objects |= (
                Q(first_name__icontains=word) |
                Q(middle_name__icontains=word) |
                Q(last_name__icontains=word)
            )
        
        return queryset.filter(q_objects)
    
    def filter_has_documents(self, queryset, name, value):
        """Filter by whether the request has document uploads."""
        if value is True:
            return queryset.filter(document_uploads__isnull=False).distinct()
        elif value is False:
            return queryset.filter(document_uploads__isnull=True)
        return queryset


class ServiceRequestAdvancedFilter(ServiceRequestFilter):
    """
    Extended filter with more advanced options for admin users.
    """
    
    # Additional admin filters
    is_deleted = django_filters.BooleanFilter(label='Is Deleted')
    deleted_after = django_filters.DateTimeFilter(
        field_name='deleted_at',
        lookup_expr='gte',
        label='Deleted After'
    )
    deleted_by = django_filters.NumberFilter(field_name='deleted_by__id', label='Deleted By')
    
    # Document verification filters
    has_verified_documents = django_filters.BooleanFilter(
        method='filter_has_verified_documents',
        label='Has Verified Documents'
    )
    has_unverified_documents = django_filters.BooleanFilter(
        method='filter_has_unverified_documents',
        label='Has Unverified Documents'
    )
    
    # Service type specific filters
    requires_mailing = django_filters.BooleanFilter(
        method='filter_requires_mailing',
        label='Requires Mailing Address'
    )
    requires_graduation_year = django_filters.BooleanFilter(
        method='filter_requires_graduation_year',
        label='Requires Graduation Year'
    )
    
    class Meta(ServiceRequestFilter.Meta):
        fields = ServiceRequestFilter.Meta.fields + [
            'is_deleted', 'deleted_after', 'deleted_by',
            'has_verified_documents', 'has_unverified_documents',
            'requires_mailing', 'requires_graduation_year'
        ]
    
    def filter_has_verified_documents(self, queryset, name, value):
        """Filter by whether the request has verified documents."""
        if value is True:
            return queryset.filter(
                document_uploads__is_verified=True
            ).distinct()
        elif value is False:
            return queryset.exclude(
                document_uploads__is_verified=True
            ).distinct()
        return queryset
    
    def filter_has_unverified_documents(self, queryset, name, value):
        """Filter by whether the request has unverified documents."""
        if value is True:
            return queryset.filter(
                document_uploads__is_verified=False
            ).distinct()
        elif value is False:
            return queryset.exclude(
                document_uploads__is_verified=False
            ).distinct()
        return queryset
    
    def filter_requires_mailing(self, queryset, name, value):
        """Filter by service types that require mailing address."""
        if value is True:
            return queryset.filter(
                service_type__name__icontains='official transcript'
            )
        elif value is False:
            return queryset.exclude(
                service_type__name__icontains='official transcript'
            )
        return queryset
    
    def filter_requires_graduation_year(self, queryset, name, value):
        """Filter by service types that require graduation year."""
        if value is True:
            return queryset.filter(
                service_type__name__icontains='original degree'
            )
        elif value is False:
            return queryset.exclude(
                service_type__name__icontains='original degree'
            )
        return queryset
