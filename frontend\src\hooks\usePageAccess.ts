import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface PageAccessConfig {
  // Authentication requirements
  requireAuth?: boolean;
  requireActive?: boolean;
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  
  // Permission requirements
  permissions?: string[];
  groups?: string[];
  requireAll?: boolean;
  
  // Redirect options
  redirectTo?: string;
  redirectWhenDenied?: boolean;
  
  // Custom validation
  customCheck?: (user: any) => boolean;
  
  // Loading behavior
  showLoadingWhileChecking?: boolean;
}

interface PageAccessResult {
  hasAccess: boolean;
  isLoading: boolean;
  accessDeniedReason?: string;
  redirecting: boolean;
}

/**
 * Hook for dynamic page access control
 * Automatically handles redirects and access validation
 */
export const usePageAccess = (config: PageAccessConfig): PageAccessResult => {
  const {
    requireAuth = true,
    requireActive = true,
    requireStaff = false,
    requireSuperuser = false,
    permissions = [],
    groups = [],
    requireAll = false,
    redirectTo,
    redirectWhenDenied = true,
    customCheck,
    showLoadingWhileChecking = true
  } = config;

  const { user, isAuthenticated, isLoading, hasPermission, hasRole, hasAnyRole } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  const [accessResult, setAccessResult] = useState<PageAccessResult>({
    hasAccess: false,
    isLoading: true,
    redirecting: false
  });

  useEffect(() => {
    // Wait for auth to load
    if (isLoading && showLoadingWhileChecking) {
      setAccessResult(prev => ({ ...prev, isLoading: true }));
      return;
    }

    const checkAccess = () => {
      // Check authentication
      if (requireAuth && !isAuthenticated) {
        return {
          hasAccess: false,
          reason: 'Authentication required',
          redirect: '/login'
        };
      }

      // Check if user exists
      if (requireAuth && !user) {
        return {
          hasAccess: false,
          reason: 'User not found',
          redirect: '/login'
        };
      }

      // Check active status
      if (requireActive && user && !user.is_active) {
        return {
          hasAccess: false,
          reason: 'Account inactive',
          redirect: '/login'
        };
      }

      // Check staff requirement
      if (requireStaff && user && !user.is_staff) {
        return {
          hasAccess: false,
          reason: 'Staff access required',
          redirect: '/dashboard'
        };
      }

      // Check superuser requirement
      if (requireSuperuser && user && !user.is_superuser) {
        return {
          hasAccess: false,
          reason: 'Superuser access required',
          redirect: '/graduate-admin'
        };
      }

      // Superuser bypass (unless custom check is provided)
      if (user?.is_superuser && !customCheck) {
        return { hasAccess: true };
      }

      // Custom validation check
      if (customCheck && !customCheck(user)) {
        return {
          hasAccess: false,
          reason: 'Custom validation failed',
          redirect: redirectTo || '/dashboard'
        };
      }

      // Check permissions
      if (permissions.length > 0) {
        const permissionCheck = requireAll 
          ? permissions.every(permission => hasPermission(permission))
          : permissions.some(permission => hasPermission(permission));
        
        if (!permissionCheck) {
          return {
            hasAccess: false,
            reason: `Missing permissions: ${permissions.join(', ')}`,
            redirect: redirectTo || '/graduate-admin'
          };
        }
      }

      // Check groups/roles
      if (groups.length > 0) {
        const groupCheck = requireAll
          ? groups.every(group => hasRole(group))
          : hasAnyRole(groups);
        
        if (!groupCheck) {
          return {
            hasAccess: false,
            reason: `Missing groups: ${groups.join(', ')}`,
            redirect: redirectTo || '/graduate-admin'
          };
        }
      }

      // All checks passed
      return { hasAccess: true };
    };

    const result = checkAccess();
    
    if (!result.hasAccess && redirectWhenDenied && result.redirect) {
      console.log(`🔒 Page Access Denied: ${result.reason} - Redirecting to ${result.redirect}`);
      setAccessResult({
        hasAccess: false,
        isLoading: false,
        accessDeniedReason: result.reason,
        redirecting: true
      });
      
      // Add current location to redirect back after login
      const redirectPath = result.redirect === '/login' 
        ? `${result.redirect}?redirect=${encodeURIComponent(location.pathname + location.search)}`
        : result.redirect;
      
      navigate(redirectPath, { replace: true });
    } else {
      setAccessResult({
        hasAccess: result.hasAccess,
        isLoading: false,
        accessDeniedReason: result.reason,
        redirecting: false
      });
      
      if (result.hasAccess) {
        console.log(`✅ Page Access Granted: ${location.pathname}`);
      } else {
        console.log(`🔒 Page Access Denied: ${result.reason}`);
      }
    }
  }, [
    isAuthenticated,
    user,
    isLoading,
    requireAuth,
    requireActive,
    requireStaff,
    requireSuperuser,
    permissions,
    groups,
    requireAll,
    redirectTo,
    redirectWhenDenied,
    customCheck,
    hasPermission,
    hasRole,
    hasAnyRole,
    navigate,
    location
  ]);

  return accessResult;
};

// Convenience hooks for common scenarios
export const useAuthRequired = (redirectTo?: string) => {
  return usePageAccess({
    requireAuth: true,
    redirectTo: redirectTo || '/login'
  });
};

export const useStaffRequired = (redirectTo?: string) => {
  return usePageAccess({
    requireAuth: true,
    requireStaff: true,
    redirectTo: redirectTo || '/dashboard'
  });
};

export const useSuperuserRequired = (redirectTo?: string) => {
  return usePageAccess({
    requireAuth: true,
    requireSuperuser: true,
    redirectTo: redirectTo || '/graduate-admin'
  });
};

export const usePermissionRequired = (permissions: string[], requireAll = false, redirectTo?: string) => {
  return usePageAccess({
    requireAuth: true,
    permissions,
    requireAll,
    redirectTo: redirectTo || '/graduate-admin'
  });
};

export const useRoleRequired = (roles: string[], requireAll = false, redirectTo?: string) => {
  return usePageAccess({
    requireAuth: true,
    groups: roles,
    requireAll,
    redirectTo: redirectTo || '/graduate-admin'
  });
};

export default usePageAccess;
