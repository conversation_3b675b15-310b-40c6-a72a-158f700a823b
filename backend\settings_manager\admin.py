from django.contrib import admin
from django.utils.html import format_html
from .models import OrganizationSetting, QuickLink, SocialMediaLink


@admin.register(OrganizationSetting)
class OrganizationSettingAdmin(admin.ModelAdmin):
    fieldsets = (
        ('System Information', {
            'fields': ('system_name', 'organization', 'copyright')
        }),
        ('Contact Information', {
            'fields': ('contact_info', 'contact_number', 'support_email', 'address', 'po_box')
        }),
        ('Security', {
            'fields': ('account_inactivity_period',)
        }),
        ('Branding', {
            'fields': ('header_logo', 'footer_logo', 'favicon', 'primary_color', 'secondary_color')
        }),
    )
    readonly_fields = ('updated_at',)

    def has_add_permission(self, request):
        # Only allow adding if no instance exists
        return not OrganizationSetting.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the settings object
        return False


@admin.register(QuickLink)
class QuickLinkAdmin(admin.ModelAdmin):
    list_display = ('name', 'url_display', 'is_external', 'order', 'is_active')
    list_filter = ('is_active', 'is_external')
    search_fields = ('name', 'url', 'description')
    list_editable = ('order', 'is_active')

    def url_display(self, obj):
        return format_html('<a href="{}" target="_blank">{}</a>', obj.url, obj.url)
    url_display.short_description = 'URL'


@admin.register(SocialMediaLink)
class SocialMediaLinkAdmin(admin.ModelAdmin):
    list_display = ('platform', 'url_display', 'order', 'is_active')
    list_filter = ('platform', 'is_active')
    search_fields = ('platform', 'url', 'display_name')
    list_editable = ('order', 'is_active')

    def url_display(self, obj):
        return format_html('<a href="{}" target="_blank">{}</a>', obj.url, obj.url)
    url_display.short_description = 'URL'


# SMTP admin is registered separately to avoid import issues
