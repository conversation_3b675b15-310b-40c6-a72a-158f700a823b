# Generated by Django 5.2.1 on 2025-05-31 12:43

import django.core.validators
import django.db.models.deletion
import registration.models
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('application_information', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ApplicantDocumentation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('degree', models.FileField(blank=True, null=True, upload_to=registration.models.degree_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']), registration.models.validate_file_size])),
                ('sponsorship', models.FileField(blank=True, null=True, upload_to=registration.models.sponsorship_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf']), registration.models.validate_file_size])),
                ('student_copy', models.FileField(blank=True, null=True, upload_to=registration.models.student_copy_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']), registration.models.validate_file_size])),
                ('recommendation', models.FileField(blank=True, null=True, upload_to=registration.models.recommendation_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf']), registration.models.validate_file_size])),
                ('publication', models.FileField(blank=True, null=True, upload_to=registration.models.publication_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf']), registration.models.validate_file_size])),
                ('conceptnote', models.FileField(blank=True, null=True, upload_to=registration.models.conceptnote_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf']), registration.models.validate_file_size])),
                ('grade_12_certificate', models.FileField(blank=True, null=True, upload_to=registration.models.grade_12_certificate_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']), registration.models.validate_file_size])),
                ('grade_9_12_transcript', models.FileField(blank=True, null=True, upload_to=registration.models.grade_9_12_transcript_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']), registration.models.validate_file_size])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ApplicantGAT',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('GAT_No', models.CharField(db_column='gat_no', max_length=20, unique=True, verbose_name='GAT Number')),
                ('GAT_Result', models.PositiveIntegerField(db_column='gat_result', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='GAT Result')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gat_records', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ApplicantInformation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('grandfather_name', models.CharField(max_length=100)),
                ('gender', models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')], max_length=10)),
                ('dob', models.DateField(verbose_name='Date of Birth')),
                ('mobile', models.CharField(max_length=15)),
                ('program_level', models.CharField(choices=[('BSC/BA', 'Bachelor of Science/Arts'), ('MSC/MBA', 'Master of Science/Business Administration'), ('PHD', 'Doctor of Philosophy')], default='MSC/MBA', max_length=20, verbose_name='Program Level')),
                ('ug_university', models.CharField(blank=True, help_text='Required for MSC/MBA and PHD applications', max_length=255, null=True, verbose_name='Undergraduate University')),
                ('ug_field_of_study', models.CharField(blank=True, help_text='Required for MSC/MBA and PHD applications', max_length=255, null=True, verbose_name='Undergraduate Field of Study')),
                ('ug_CGPA', models.DecimalField(blank=True, db_column='ug_cgpa', decimal_places=2, help_text='Required for MSC/MBA and PHD applications', max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(2.0), django.core.validators.MaxValueValidator(4.0)], verbose_name='Undergraduate CGPA')),
                ('pg_university', models.CharField(blank=True, help_text='Required for PHD applications', max_length=255, null=True, verbose_name='Postgraduate University')),
                ('pg_field_of_study', models.CharField(blank=True, help_text='Required for PHD applications', max_length=255, null=True, verbose_name='Postgraduate Field of Study')),
                ('pg_CGPA', models.DecimalField(blank=True, db_column='pg_cgpa', decimal_places=2, help_text='Required for PHD applications', max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(2.0), django.core.validators.MaxValueValidator(4.0)], verbose_name='Postgraduate CGPA')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='applicant_info', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ApplicantProgramSelection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('application_num', models.CharField(default=registration.models.generate_application_num, editable=False, max_length=20, unique=True)),
                ('transaction_id', models.CharField(default=registration.models.generate_transaction_id, editable=False, max_length=20, unique=True)),
                ('sponsorship', models.CharField(choices=[('Ministry of Education', 'Ministry of Education'), ('Other Government Office', 'Other Government Office'), ('University of Gondar', 'University of Gondar'), ('Private Office ', 'Private Office'), ('Self', 'Self')], max_length=50)),
                ('registrar_off_status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Pending', max_length=50)),
                ('department_status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Pending', max_length=50)),
                ('remark', models.TextField(blank=True, null=True)),
                ('payment_status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Pending', max_length=50)),
                ('year', models.PositiveIntegerField(default=registration.models.current_year)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application_info', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='application_information.applicationinformation')),
                ('dep_approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='department_approvals', to=settings.AUTH_USER_MODEL)),
                ('gat', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='program_selection', to='registration.applicantgat')),
                ('reg_approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='registrar_approvals', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='program_selections', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ApplicantPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_status', models.CharField(choices=[('Pending', 'Pending'), ('Initiated', 'Initiated'), ('Processing', 'Processing'), ('Completed', 'Completed'), ('Failed', 'Failed'), ('Verified', 'Verified'), ('Expired', 'Expired'), ('Refunded', 'Refunded'), ('Cancelled', 'Cancelled')], default='Pending', max_length=50)),
                ('payment_method', models.CharField(choices=[('TeleBirr', 'TeleBirr'), ('CBE Birr', 'Bank Transfer')], max_length=50)),
                ('amount_paid', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0, message='Payment amount cannot be negative')])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicant_gat', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payment', to='registration.applicantgat')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL)),
                ('applicant_program_selection', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payment', to='registration.applicantprogramselection')),
            ],
        ),
    ]
