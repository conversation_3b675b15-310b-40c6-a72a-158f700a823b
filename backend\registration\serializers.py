from rest_framework import serializers
from .models import (
    ApplicantInformation,
    ApplicantGAT,
    ApplicantProgramSelection,
    ApplicantDocumentation,
    ApplicantPayment
)

class ApplicantInformationSerializer(serializers.ModelSerializer):
    # Add first_name and last_name from the User model
    first_name = serializers.CharField(source='author.first_name', read_only=True)
    last_name = serializers.CharField(source='author.last_name', read_only=True)

    # Use DateField with input formats
    dob = serializers.DateField(input_formats=['%Y-%m-%d', 'iso-8601'])

    class Meta:
        model = ApplicantInformation
        fields = ['id', 'author', 'first_name', 'last_name', 'grandfather_name', 'gender', 'dob', 'mobile',
                 'program_level',
                 'ug_university', 'ug_field_of_study', 'ug_CGPA',
                 'pg_university', 'pg_field_of_study', 'pg_CGPA',
                 'created_at', 'updated_at']
        read_only_fields = ['author', 'created_at', 'updated_at']

    def create(self, validated_data):
        print(f"Creating ApplicantInformation with data: {validated_data}")
        try:
            # Validate educational fields based on program level
            self.validate_educational_fields(validated_data)

            instance = super().create(validated_data)
            print(f"Successfully created instance: {instance}")
            return instance
        except Exception as e:
            print(f"Error in serializer create method: {str(e)}")
            raise

    def update(self, instance, validated_data):
        print(f"Updating ApplicantInformation with data: {validated_data}")
        try:
            # Validate educational fields based on program level
            program_level = validated_data.get('program_level', instance.program_level)
            self.validate_educational_fields(validated_data, program_level)

            # Update the instance
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            print(f"Successfully updated instance: {instance}")
            return instance
        except Exception as e:
            print(f"Error in serializer update method: {str(e)}")
            raise

    def validate_educational_fields(self, data, program_level=None):
        """
        Validate that the required educational fields are provided based on program level.
        """
        # Get the program level from data or use the provided one
        program_level = program_level or data.get('program_level')
        if not program_level:
            return  # Can't validate without program level

        errors = {}

        # For MSC/MBA and PHD, undergraduate details are required
        if program_level in ['MSC/MBA', 'PHD']:
            if not data.get('ug_university'):
                errors['ug_university'] = ['Undergraduate university is required for MSC/MBA and PHD applications']
            if not data.get('ug_field_of_study'):
                errors['ug_field_of_study'] = ['Undergraduate field of study is required for MSC/MBA and PHD applications']
            if 'ug_CGPA' in data and data['ug_CGPA'] is None:
                errors['ug_CGPA'] = ['Undergraduate CGPA is required for MSC/MBA and PHD applications']

        # For PHD, postgraduate details are required
        if program_level == 'PHD':
            if not data.get('pg_university'):
                errors['pg_university'] = ['Postgraduate university is required for PHD applications']
            if not data.get('pg_field_of_study'):
                errors['pg_field_of_study'] = ['Postgraduate field of study is required for PHD applications']
            if 'pg_CGPA' in data and data['pg_CGPA'] is None:
                errors['pg_CGPA'] = ['Postgraduate CGPA is required for PHD applications']

        if errors:
            raise serializers.ValidationError(errors)

    def validate_dob(self, value):
        """Validate that the date of birth is in the past"""
        import datetime
        if value > datetime.date.today():
            raise serializers.ValidationError("Date of birth must be in the past")
        return value

    def validate_ug_CGPA(self, value):
        """Validate that the CGPA is between 0 and 4.0"""
        if value is not None and (value < 0 or value > 4.0):
            raise serializers.ValidationError("CGPA must be between 0 and 4.0")
        return value

    def validate_pg_CGPA(self, value):
        """Validate that the CGPA is between 0 and 4.0 if provided"""
        if value is not None and (value < 0 or value > 4.0):
            raise serializers.ValidationError("CGPA must be between 0 and 4.0")
        return value

class ApplicantGATSerializer(serializers.ModelSerializer):
    # We'll handle the program_selection in to_representation instead of as a field

    class Meta:
        model = ApplicantGAT
        fields = ['id', 'user', 'GAT_No', 'GAT_Result']
        read_only_fields = ['user']

    def update(self, instance, validated_data):
        try:
            # Update the instance with the validated data
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()
            return instance
        except Exception as e:
            print(f"Error in GAT serializer update method: {str(e)}")

            # Check if this is a unique constraint violation (duplicate GAT number)
            error_message = str(e)
            if "unique constraint" in error_message.lower() or "duplicate key" in error_message.lower():
                # This is a duplicate GAT number error
                raise serializers.ValidationError({"GAT_No": ["This GAT ID is already taken by someone else."]})

            # If it's not a duplicate GAT number error, re-raise the original exception
            raise

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Add program_selection ID if it exists
        try:
            if hasattr(instance, 'program_selection') and instance.program_selection:
                representation['program_selection'] = instance.program_selection.id
            else:
                representation['program_selection'] = None
        except Exception as e:
            print(f"Error getting program_selection for GAT {instance.id}: {str(e)}")
            representation['program_selection'] = None
        return representation

    def create(self, validated_data):
        print(f"Creating ApplicantGAT with data: {validated_data}")
        try:
            # Get the current user from the context
            user = self.context['request'].user
            validated_data['user'] = user

            # Always create a new GAT record for a new application
            print(f"Creating new GAT record for user: {user}")
            instance = super().create(validated_data)
            print(f"Successfully created new GAT record with ID: {instance.id}")
            return instance
        except Exception as e:
            print(f"Error in GAT serializer create method: {str(e)}")

            # Check if this is a unique constraint violation (duplicate GAT number)
            error_message = str(e)
            if "unique constraint" in error_message.lower() or "duplicate key" in error_message.lower():
                # This is a duplicate GAT number error
                from rest_framework import serializers
                raise serializers.ValidationError({"GAT_No": ["This GAT ID is already taken by someone else."]})

            # If it's not a duplicate GAT number error, re-raise the original exception
            raise

class ApplicantProgramSelectionSerializer(serializers.ModelSerializer):
    year_name = serializers.SerializerMethodField()
    term_name = serializers.SerializerMethodField()

    def get_year_name(self, obj):
        return obj.year.year if obj.year else None

    def get_term_name(self, obj):
        return obj.term.name if obj.term else None

    class Meta:
        model = ApplicantProgramSelection
        fields = ['id', 'user', 'gat', 'application_info', 'sponsorship', 'year', 'year_name', 'term', 'term_name', 'created_at', 'updated_at', 'application_num', 'transaction_id']
        read_only_fields = ['created_at', 'updated_at', 'application_num', 'transaction_id']
        extra_kwargs = {
            'user': {'required': False},
            'gat': {'required': False},  # Can be set after creation
            'application_info': {'required': True},
            'sponsorship': {'required': True},
            'year': {'required': True},
            'term': {'required': True}
        }

    def create(self, validated_data):
        print(f"Creating program selection with data: {validated_data}")
        try:
            # Get the current user from the request
            request = self.context.get('request')
            print(f"Request: {request}")
            print(f"Request data: {request.data if request else 'No request'}")

            user = request.user if request else validated_data.get('user')
            print(f"User: {user}")

            if not user:
                print("User is required but not provided")
                raise serializers.ValidationError("User is required")

            # Set the user in the validated data
            validated_data['user'] = user

            # Print all keys in validated_data and request.data for debugging
            print(f"validated_data keys: {validated_data.keys()}")
            print(f"request.data keys: {request.data.keys() if request else 'No request'}")

            # Check if application_info is provided
            if 'application_info' not in validated_data:
                print("Application info is required but not provided")
                raise serializers.ValidationError({"application_info": ["This field is required."]})

            # Check if sponsorship is provided
            if 'sponsorship' not in validated_data:
                print("Sponsorship is required but not provided")
                raise serializers.ValidationError({"sponsorship": ["This field is required."]})

            # Log the sponsorship value for debugging
            print(f"Sponsorship value: {validated_data.get('sponsorship')}")

            # Check if gat is provided in the request data
            gat_id = request.data.get('gat')
            print(f"GAT ID from request data: {gat_id}, type: {type(gat_id)}")

            # Remove any existing 'gat' key to avoid confusion
            if 'gat' in validated_data:
                print(f"Removing existing 'gat' from validated_data: {validated_data['gat']}")
                del validated_data['gat']

            if gat_id:
                try:
                    # Convert to int if it's a string
                    if isinstance(gat_id, str) and gat_id.isdigit():
                        gat_id = int(gat_id)

                    # Get the GAT record - explicitly use the id field
                    gat = ApplicantGAT.objects.get(id=gat_id, user=user)

                    # Verify we got the correct GAT record
                    print(f"Found GAT record with ID {gat.id} for user {user.id}")

                    # Explicitly set the gat field to the ApplicantGAT instance
                    validated_data['gat'] = gat
                    print(f"GAT record found and set: {gat}, ID: {gat.id}")

                    # Double check that we're using the correct GAT ID
                    print(f"Ensuring gat_id will be set to {gat.id} and not user_id {user.id}")
                except (ApplicantGAT.DoesNotExist, ValueError, TypeError) as e:
                    print(f"Error finding GAT record with ID {gat_id} for user {user}: {str(e)}")
                    # Don't raise an error, just continue without setting gat
            else:
                print("No GAT ID provided in request data")

            print(f"Updated validated_data: {validated_data}")

            # Always create a new program selection record for a new application
            print(f"Creating new program selection record for user: {user}")

            # Print the final validated_data before creating the instance
            print(f"Final validated_data before create: {validated_data}")
            print(f"GAT in validated_data: {validated_data.get('gat')}")

            # If gat is in validated_data, ensure it's the correct ApplicantGAT instance
            if 'gat' in validated_data and validated_data['gat']:
                gat_instance = validated_data['gat']
                print(f"Confirmed GAT instance with ID {gat_instance.id} will be used")

            # Create the instance
            instance = super().create(validated_data)
            print(f"Successfully created new program selection record with ID: {instance.id}")
            print(f"Created instance - user: {instance.user}, gat: {instance.gat}")

            # Verify the gat_id was set correctly
            if instance.gat:
                print(f"Verified gat_id was set to {instance.gat.id}")
                # Double check with a direct database query
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute(
                        "SELECT gat_id FROM registration_applicantprogramselection WHERE id = %s",
                        [instance.id]
                    )
                    row = cursor.fetchone()
                    if row and row[0]:
                        print(f"Database check confirms gat_id = {row[0]}")
                    else:
                        print(f"WARNING: Database check shows gat_id is not set correctly")

            return instance
        except Exception as e:
            print(f"Error in program selection serializer create method: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def update(self, instance, validated_data):
        print(f"Updating program selection with data: {validated_data}")
        try:
            # Check if sponsorship is provided
            if 'sponsorship' not in validated_data:
                print("Sponsorship is required but not provided for update")
                raise serializers.ValidationError({"sponsorship": ["This field is required."]})

            # Log the sponsorship value for debugging
            print(f"Sponsorship value for update: {validated_data.get('sponsorship')}")

            # Check if gat is provided in the request data
            request = self.context.get('request')
            if request and request.data:
                gat_id = request.data.get('gat')
                print(f"Update - GAT ID from request data: {gat_id}, type: {type(gat_id)}")

                # Remove any existing 'gat' key to avoid confusion
                if 'gat' in validated_data:
                    print(f"Update - Removing existing 'gat' from validated_data: {validated_data['gat']}")
                    del validated_data['gat']

                if gat_id:
                    try:
                        # Convert to int if it's a string
                        if isinstance(gat_id, str) and gat_id.isdigit():
                            gat_id = int(gat_id)

                        # Get the GAT record - explicitly use the id field
                        gat = ApplicantGAT.objects.get(id=gat_id, user=instance.user)

                        # Verify we got the correct GAT record
                        print(f"Update - Found GAT record with ID {gat.id} for user {instance.user.id}")

                        # Explicitly set the gat field to the ApplicantGAT instance
                        validated_data['gat'] = gat
                        print(f"Update - GAT record found and set: {gat}, ID: {gat.id}")

                        # Double check that we're using the correct GAT ID
                        print(f"Update - Ensuring gat_id will be set to {gat.id} and not user_id {instance.user.id}")
                    except (ApplicantGAT.DoesNotExist, ValueError, TypeError) as e:
                        print(f"Update - Error finding GAT record with ID {gat_id} for user {instance.user}: {str(e)}")
                        # Don't raise an error, just continue without setting gat
                else:
                    print("No GAT ID provided in request data for update")

            # If gat is in validated_data, ensure it's the correct ApplicantGAT instance
            if 'gat' in validated_data and validated_data['gat']:
                gat_instance = validated_data['gat']
                print(f"Update - Confirmed GAT instance with ID {gat_instance.id} will be used")

            # Update the instance with the validated data
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            # Verify the gat_id was set correctly
            if instance.gat:
                print(f"Update - Verified gat_id was set to {instance.gat.id}")
                # Double check with a direct database query
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute(
                        "SELECT gat_id FROM registration_applicantprogramselection WHERE id = %s",
                        [instance.id]
                    )
                    row = cursor.fetchone()
                    if row and row[0]:
                        print(f"Update - Database check confirms gat_id = {row[0]}")
                    else:
                        print(f"WARNING: Update - Database check shows gat_id is not set correctly")

            return instance
        except Exception as e:
            print(f"Error in program selection serializer update method: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

class ApplicantDocumentationSerializer(serializers.ModelSerializer):
    class Meta:
        model = ApplicantDocumentation
        fields = ['id', 'user', 'degree', 'sponsorship', 'student_copy', 'recommendation', 'publication', 'conceptnote', 'grade_12_certificate', 'grade_9_12_transcript', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']
        extra_kwargs = {
            'user': {'required': False},
            'degree': {'required': False},
            'sponsorship': {'required': False},
            'student_copy': {'required': False},
            'recommendation': {'required': False},
            'publication': {'required': False},
            'conceptnote': {'required': False},
            'grade_12_certificate': {'required': False},
            'grade_9_12_transcript': {'required': False}
        }

    def create(self, validated_data):
        print(f"Creating documentation with data: {validated_data}")
        try:
            # Get the current user from the request
            request = self.context.get('request')
            print(f"Request: {request}")

            user = request.user if request else validated_data.get('user')
            print(f"User: {user}")

            if not user:
                print("User is required but not provided")
                raise serializers.ValidationError("User is required")

            # Set the user in the validated data
            validated_data['user'] = user

            # Create a new record
            instance = super().create(validated_data)
            print(f"Successfully created new documentation record with ID: {instance.id}")

            return instance
        except Exception as e:
            print(f"Error in documentation serializer create method: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

class ApplicantPaymentSerializer(serializers.ModelSerializer):
    # Add fields that match the old structure for backward compatibility
    payment_amount = serializers.DecimalField(
        max_digits=10, decimal_places=2, required=False,
        source='amount_paid'
    )
    payment_status = serializers.CharField(required=False)
    payment_method = serializers.CharField(required=False)
    telebirr_id = serializers.CharField(required=False, allow_blank=True)
    payment_date = serializers.DateField(required=False)
    payment_time = serializers.TimeField(required=False)

    # Add GAT information
    gat_no = serializers.CharField(source='applicant_gat.GAT_No', read_only=True)
    gat_result = serializers.IntegerField(source='applicant_gat.GAT_Result', read_only=True)

    class Meta:
        model = ApplicantPayment
        fields = [
            'id', 'user', 'payment_amount', 'payment_status', 'payment_method',
            'telebirr_id', 'payment_date', 'payment_time',
            'created_at', 'updated_at', 'applicant_program_selection', 'applicant_gat',
            'amount_paid', 'gat_no', 'gat_result'
        ]
        read_only_fields = ['created_at', 'updated_at']
        extra_kwargs = {
            'user': {'required': False},
            'applicant_program_selection': {'required': False},
            'applicant_gat': {'required': False},
            'amount_paid': {'required': True},
            'payment_status': {'required': True},
            'payment_method': {'required': True}
        }

    def create(self, validated_data):
        print(f"Creating payment with data: {validated_data}")
        try:
            # Get the current user from the request
            request = self.context.get('request')
            print(f"Request: {request}")

            user = request.user if request else validated_data.get('user')
            print(f"User: {user}")

            if not user:
                print("User is required but not provided")
                raise serializers.ValidationError("User is required")

            # Set the user in the validated data
            validated_data['user'] = user

            # Check if applicant_gat is provided in the request data
            gat_id = None
            if request and request.data:
                gat_id = request.data.get('applicant_gat')
                print(f"GAT ID from request data: {gat_id}, type: {type(gat_id)}")

            # Remove any existing 'applicant_gat' key to avoid confusion
            if 'applicant_gat' in validated_data:
                print(f"Removing existing 'applicant_gat' from validated_data: {validated_data['applicant_gat']}")
                del validated_data['applicant_gat']

            if gat_id:
                try:
                    # Convert to int if it's a string
                    if isinstance(gat_id, str) and gat_id.isdigit():
                        gat_id = int(gat_id)

                    # Get the GAT record - explicitly use the id field
                    gat = ApplicantGAT.objects.get(id=gat_id, user=user)

                    # Verify we got the correct GAT record
                    print(f"Found GAT record with ID {gat.id} for user {user.id}")

                    # Explicitly set the applicant_gat field to the ApplicantGAT instance
                    validated_data['applicant_gat'] = gat
                    print(f"GAT record found and set: {gat}, ID: {gat.id}")
                except (ApplicantGAT.DoesNotExist, ValueError, TypeError) as e:
                    print(f"Error finding GAT record with ID {gat_id} for user {user}: {str(e)}")
                    raise serializers.ValidationError({"applicant_gat": [f"GAT record with ID {gat_id} not found for this user."]})

            # Create a new record
            instance = super().create(validated_data)
            print(f"Successfully created new payment record with ID: {instance.id}")
            return instance
        except Exception as e:
            print(f"Error in payment serializer create method: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def to_representation(self, instance):
        """Add GAT information to the representation"""
        representation = super().to_representation(instance)

        # Add GAT information if available
        if instance.applicant_gat:
            representation['gat_no'] = instance.applicant_gat.GAT_No
            representation['gat_result'] = instance.applicant_gat.GAT_Result

            # Add program information if available through GAT
            if hasattr(instance.applicant_gat, 'program_selection') and instance.applicant_gat.program_selection:
                program_selection = instance.applicant_gat.program_selection
                if program_selection.application_info:
                    app_info = program_selection.application_info
                    representation['program_info'] = {
                        'college': app_info.college.name if app_info.college else None,
                        'department': app_info.department.name if app_info.department else None,
                        'program': app_info.program.program_name if app_info.program else None,
                        'admission_type': app_info.admission_type.name if app_info.admission_type else None,
                        'field_of_study': app_info.field_of_study.name if app_info.field_of_study else None,
                        'study_program': app_info.study_program.name if app_info.study_program else None,
                        'duration': app_info.duration,
                        'special_case': app_info.spacial_case
                    }

        return representation