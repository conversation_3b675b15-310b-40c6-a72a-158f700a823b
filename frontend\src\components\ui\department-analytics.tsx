import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from './card';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LabelList, Cell } from 'recharts';
import { ChartExport } from './chart-export';
import { Input } from './input';
import { Button } from './button';
import { Search, BookOpen, TrendingUp, Filter } from 'lucide-react';

interface DepartmentAnalyticsProps {
  departmentData: any[];
  isLoading?: boolean;
}

export const DepartmentAnalytics: React.FC<DepartmentAnalyticsProps> = ({
  departmentData,
  isLoading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'count' | 'gpa' | 'name'>('count');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  if (isLoading) {
    return (
      <div className="space-y-6 mb-8">
        <Card className="border-0 shadow-xl">
          <CardHeader>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-100 rounded animate-pulse"></div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-xl">
          <CardContent>
            <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Filter and sort department data
  const filteredAndSortedData = departmentData
    .filter(dept => 
      dept.departmentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      dept.collegeName.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      let aValue, bValue;
      switch (sortBy) {
        case 'count':
          aValue = a.count;
          bValue = b.count;
          break;
        case 'gpa':
          aValue = parseFloat(a.averageGPA);
          bValue = parseFloat(b.averageGPA);
          break;
        case 'name':
          aValue = a.departmentName.toLowerCase();
          bValue = b.departmentName.toLowerCase();
          break;
        default:
          aValue = a.count;
          bValue = b.count;
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  // Prepare data for bar chart (top 10)
  const chartData = filteredAndSortedData.slice(0, 10).map(dept => ({
    name: dept.departmentName.length > 15 ? dept.departmentName.substring(0, 15) + '...' : dept.departmentName,
    fullName: dept.departmentName,
    college: dept.collegeName,
    graduates: dept.count,
    avgGPA: parseFloat(dept.averageGPA),
    recentYear: dept.recentYear
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{data.fullName}</p>
          <p className="text-sm text-gray-600">{data.college}</p>
          <p className="text-blue-600">
            <span className="font-medium">Graduates:</span> {data.graduates.toLocaleString()}
          </p>
          <p className="text-green-600">
            <span className="font-medium">Avg GPA:</span> {data.avgGPA}
          </p>
          <p className="text-purple-600">
            <span className="font-medium">Recent Year:</span> {data.recentYear}
          </p>
        </div>
      );
    }
    return null;
  };

  const EnhancedCustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-xl shadow-xl backdrop-blur-sm">
          <div className="flex items-center mb-3">
            <div
              className="w-4 h-4 rounded-full mr-2"
              style={{ backgroundColor: data.color }}
            ></div>
            <p className="font-bold text-gray-800 text-lg">{data.fullName}</p>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Rank:</span>
              <span className="font-bold text-emerald-600">#{data.rank}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Graduates:</span>
              <span className="font-bold text-blue-600">{data.graduates.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Percentage:</span>
              <span className="font-bold text-purple-600">{data.percentage}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">College:</span>
              <span className="font-medium text-teal-600 text-sm">{data.college}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Avg GPA:</span>
              <span className="font-bold text-orange-600">{data.avgGPA.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 font-medium">Recent Year:</span>
              <span className="font-medium text-gray-700">{data.recentYear}</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6 mb-8">
      {/* Enhanced Top 10 Departments Bar Chart */}
      <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-emerald-50 via-teal-50 to-cyan-50 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                <div className="h-3 w-3 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-full mr-3 shadow-sm"></div>
                Top 10 Departments by Graduate Count
              </CardTitle>
              <p className="text-gray-600 mt-1">Leading departments with highest graduate output and performance metrics</p>
            </div>
            <div className="flex items-center space-x-2">
              <ChartExport
                chartId="department-bar-chart"
                filename="top-departments-by-graduates"
                data={chartData}
              />
              <div className="bg-gradient-to-br from-emerald-600 to-teal-600 p-2 rounded-lg shadow-lg">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Chart Container */}
          <div className="h-[500px] mb-6">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={(() => {
                  const totalGraduates = chartData.reduce((sum, dept) => sum + dept.graduates, 0);
                  return chartData.map((dept, index) => ({
                    ...dept,
                    percentage: totalGraduates > 0 ? ((dept.graduates / totalGraduates) * 100).toFixed(1) : '0.0',
                    color: [
                      '#059669', '#0891b2', '#7c3aed', '#dc2626', '#ea580c',
                      '#16a34a', '#2563eb', '#9333ea', '#c2410c', '#15803d'
                    ][index % 10],
                    rank: index + 1
                  }));
                })()}
                layout="vertical"
                margin={{ top: 20, right: 80, left: 120, bottom: 5 }}
              >
                <defs>
                  <linearGradient id="departmentGradient" x1="0" y1="0" x2="1" y2="0">
                    <stop offset="0%" stopColor="#059669" stopOpacity={0.8}/>
                    <stop offset="50%" stopColor="#0891b2" stopOpacity={0.9}/>
                    <stop offset="100%" stopColor="#7c3aed" stopOpacity={1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" strokeOpacity={0.6} />
                <XAxis
                  type="number"
                  tick={{ fontSize: 12, fill: '#64748b', fontWeight: 500 }}
                  axisLine={{ stroke: '#64748b', strokeWidth: 1 }}
                  tickLine={{ stroke: '#64748b' }}
                  label={{
                    value: 'Number of Graduates',
                    position: 'insideBottom',
                    offset: -5,
                    style: { textAnchor: 'middle', fontSize: '12px', fill: '#64748b', fontWeight: 'bold' }
                  }}
                />
                <YAxis
                  type="category"
                  dataKey="name"
                  tick={{ fontSize: 11, fill: '#374151', fontWeight: 500 }}
                  axisLine={{ stroke: '#64748b', strokeWidth: 1 }}
                  tickLine={{ stroke: '#64748b' }}
                  width={115}
                  interval={0}
                />
                <Tooltip content={<EnhancedCustomTooltip />} />
                <Bar
                  dataKey="graduates"
                  radius={[0, 8, 8, 0]}
                  stroke="#ffffff"
                  strokeWidth={1}
                >
                  <LabelList
                    dataKey="percentage"
                    position="right"
                    formatter={(value: any) => `${value}%`}
                    style={{
                      fontSize: '11px',
                      fontWeight: 'bold',
                      fill: '#374151'
                    }}
                  />
                  {chartData.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={[
                        '#059669', '#0891b2', '#7c3aed', '#dc2626', '#ea580c',
                        '#16a34a', '#2563eb', '#9333ea', '#c2410c', '#15803d'
                      ][index % 10]}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Department Performance Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg border border-emerald-200">
              <div className="text-2xl font-bold text-emerald-700">
                {chartData.length > 0 ? chartData[0].graduates.toLocaleString() : '0'}
              </div>
              <div className="text-sm text-emerald-600 font-medium">Top Department</div>
              <div className="text-xs text-emerald-500 mt-1">
                {chartData.length > 0 ? chartData[0].fullName : 'No data'}
              </div>
            </div>
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
              <div className="text-2xl font-bold text-blue-700">
                {chartData.reduce((sum, dept) => sum + dept.graduates, 0).toLocaleString()}
              </div>
              <div className="text-sm text-blue-600 font-medium">Total Graduates</div>
              <div className="text-xs text-blue-500 mt-1">Top 10 departments</div>
            </div>
            <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
              <div className="text-2xl font-bold text-purple-700">
                {chartData.length > 0 ? (chartData.reduce((sum, dept) => sum + dept.graduates, 0) / chartData.length).toFixed(0) : '0'}
              </div>
              <div className="text-sm text-purple-600 font-medium">Average per Dept</div>
              <div className="text-xs text-purple-500 mt-1">Graduate count</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Data Table */}
      <Card className="border-0 shadow-xl bg-white">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                <div className="h-2 w-2 bg-gray-600 rounded-full mr-3"></div>
                Department Analytics Table
              </CardTitle>
              <p className="text-gray-600 mt-1">Searchable and sortable department data</p>
            </div>
            <div className="bg-gray-600 p-2 rounded-lg">
              <BookOpen className="h-5 w-5 text-white" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Search and Sort Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search departments or colleges..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={sortBy === 'count' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  if (sortBy === 'count') {
                    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
                  } else {
                    setSortBy('count');
                    setSortOrder('desc');
                  }
                }}
              >
                Count {sortBy === 'count' && (sortOrder === 'desc' ? '↓' : '↑')}
              </Button>
              <Button
                variant={sortBy === 'gpa' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  if (sortBy === 'gpa') {
                    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
                  } else {
                    setSortBy('gpa');
                    setSortOrder('desc');
                  }
                }}
              >
                GPA {sortBy === 'gpa' && (sortOrder === 'desc' ? '↓' : '↑')}
              </Button>
              <Button
                variant={sortBy === 'name' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  if (sortBy === 'name') {
                    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
                  } else {
                    setSortBy('name');
                    setSortOrder('asc');
                  }
                }}
              >
                Name {sortBy === 'name' && (sortOrder === 'desc' ? '↓' : '↑')}
              </Button>
            </div>
          </div>

          {/* Data Table */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Department</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">College</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Graduates</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Avg GPA</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Recent Year</th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedData.slice(0, 20).map((dept, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <td className="py-3 px-4 font-medium text-gray-900">{dept.departmentName}</td>
                    <td className="py-3 px-4 text-gray-600">{dept.collegeName}</td>
                    <td className="py-3 px-4 text-right font-semibold text-blue-600">
                      {dept.count.toLocaleString()}
                    </td>
                    <td className="py-3 px-4 text-right font-semibold text-green-600">
                      {dept.averageGPA}
                    </td>
                    <td className="py-3 px-4 text-right text-gray-600">{dept.recentYear}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredAndSortedData.length > 20 && (
            <div className="mt-4 text-center text-gray-500 text-sm">
              Showing top 20 of {filteredAndSortedData.length} departments
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DepartmentAnalytics;
