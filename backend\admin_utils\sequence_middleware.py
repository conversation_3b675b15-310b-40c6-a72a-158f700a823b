from django.db import connection
import logging

# Set up logging
logger = logging.getLogger(__name__)

class SequenceResetMiddleware:
    """
    Middleware to reset sequences after a request is complete.
    This avoids transaction issues by performing the reset outside of any transaction.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Process the request first
        response = self.get_response(request)

        # After the request is complete, check if we need to reset any sequences
        self._reset_sequences_if_needed(request)

        return response

    def _reset_sequences_if_needed(self, request):
        """
        Check if any sequences need to be reset and do so if needed.
        """
        # Check for department sequence reset flag
        if request.session.get('reset_department_sequence'):
            try:
                # Reset the department sequence
                self._reset_department_sequence()
                # Clear the flag
                del request.session['reset_department_sequence']
                request.session.modified = True
                logger.info("Department sequence reset successfully after request")
            except Exception as e:
                logger.error(f"Error resetting department sequence: {str(e)}")

    def _reset_department_sequence(self):
        """
        Reset the department sequence to the next available ID.
        """
        from setups.department.models import Department

        # Get the table name
        table_name = Department._meta.db_table

        # Get the primary key field name
        pk_field = Department._meta.pk.name

        # Get the maximum ID
        max_id = Department.objects.all().order_by('-id').first()
        next_id = (max_id.id + 1) if max_id else 1

        # Execute the SQL to reset the sequence
        with connection.cursor() as cursor:
            cursor.execute(f"SELECT pg_get_serial_sequence('{table_name}', '{pk_field}');")
            sequence_name = cursor.fetchone()[0]

            if sequence_name:
                # Set the sequence to a higher value
                new_value = next_id + 100  # Add a buffer to avoid future conflicts
                cursor.execute(f"SELECT setval('{sequence_name}', {new_value}, false);")

                # Log the fix only to the debug log, not to the console
                logger.debug(f"Reset sequence for {table_name}. Set to {new_value}")
            else:
                logger.warning(f"No sequence found for {table_name}.{pk_field}")
