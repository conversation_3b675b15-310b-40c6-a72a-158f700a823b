import os
import json
import django
import sys

# Add the project root directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models
from GraduateVerification.models import (
    VerificationCollege,
    VerificationDepartment,
    VerificationFieldOfStudy,
    AdmissionClassification,
    VerificationProgram,
    GraduateStudent
)

def load_colleges():
    print("Loading colleges...")
    with open('seed_data/verification_colleges.json', 'r', encoding='utf-8') as f:
        colleges_data = json.load(f)

    for college_data in colleges_data:
        VerificationCollege.objects.get_or_create(
            name=college_data['name'],
            code=college_data['code']
        )
    print(f"Loaded {len(colleges_data)} colleges")

def load_departments():
    print("Loading departments...")
    with open('seed_data/verification_departments.json', 'r', encoding='utf-8') as f:
        departments_data = json.load(f)

    for dept_data in departments_data:
        college = VerificationCollege.objects.get(code=dept_data['college_code'])
        VerificationDepartment.objects.get_or_create(
            name=dept_data['name'],
            code=dept_data['code'],
            college=college
        )
    print(f"Loaded {len(departments_data)} departments")

def load_fields_of_study():
    print("Loading fields of study...")
    with open('seed_data/verification_fields_of_study.json', 'r', encoding='utf-8') as f:
        fields_data = json.load(f)

    for field_data in fields_data:
        department = VerificationDepartment.objects.get(code=field_data['department_code'])
        VerificationFieldOfStudy.objects.get_or_create(
            name=field_data['name'],
            code=field_data['code'],
            department=department
        )
    print(f"Loaded {len(fields_data)} fields of study")

def load_admission_classifications():
    print("Loading admission classifications...")
    with open('seed_data/verification_admission_classifications.json', 'r', encoding='utf-8') as f:
        classifications_data = json.load(f)

    for classification_data in classifications_data:
        AdmissionClassification.objects.get_or_create(
            name=classification_data['name']
        )
    print(f"Loaded {len(classifications_data)} admission classifications")

def load_programs():
    print("Loading programs...")
    with open('seed_data/verification_programs.json', 'r', encoding='utf-8') as f:
        programs_data = json.load(f)

    for program_data in programs_data:
        VerificationProgram.objects.get_or_create(
            name=program_data['name'],
            code=program_data['code']
            # registration_fee field doesn't exist in the model
            # registration_fee=program_data['registration_fee']
        )
    print(f"Loaded {len(programs_data)} programs")

def load_graduates():
    print("Loading graduates...")
    with open('seed_data/verification_graduates.json', 'r', encoding='utf-8') as f:
        graduates_data = json.load(f)

    for graduate_data in graduates_data:
        # Get related objects
        college = VerificationCollege.objects.get(code=graduate_data['college_code'])
        department = VerificationDepartment.objects.get(code=graduate_data['department_code'])
        field_of_study = VerificationFieldOfStudy.objects.get(name=graduate_data['field_of_study'])
        program = VerificationProgram.objects.get(code=graduate_data['program_code'])
        admission_classification = AdmissionClassification.objects.get(name=graduate_data['admission_classification'])

        # Create graduate
        GraduateStudent.objects.get_or_create(
            student_id=graduate_data['student_id'],
            defaults={
                'first_name': graduate_data['first_name'],
                'middle_name': graduate_data['middle_name'],
                'last_name': graduate_data['last_name'],
                'year_of_graduation': graduate_data['year_of_graduation'],
                'gpa': graduate_data['gpa'],
                'gender': graduate_data['gender'],
                'college': college,
                'department': department,
                'field_of_study': field_of_study,
                'program': program,
                'admission_classification': admission_classification
            }
        )
    print(f"Loaded {len(graduates_data)} graduates")

if __name__ == '__main__':
    # Load data in the correct order to maintain relationships
    load_colleges()
    load_departments()
    # Fields of study are already loaded by clear_and_load_fields.py
    # load_fields_of_study()
    load_admission_classifications()
    # Programs are already loaded by clear_and_load_programs.py
    # load_programs()
    load_graduates()

    print("Seed data loading complete!")
