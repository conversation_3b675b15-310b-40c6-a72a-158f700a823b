# Generated by Django 5.2.1 on 2025-05-31 12:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('admission_type', '0001_initial'),
        ('college', '0001_initial'),
        ('department', '0001_initial'),
        ('program', '0001_initial'),
        ('study_field', '0001_initial'),
        ('study_program', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ApplicationInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('spacial_case', models.CharField(blank=True, max_length=255, verbose_name='Special Case')),
                ('duration', models.CharField(blank=True, max_length=10, verbose_name='Duration')),
                ('status', models.BooleanField(default=True, verbose_name='Active Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('admission_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admission_type.admissiontype', verbose_name='Admission Type')),
                ('college', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='college.college', verbose_name='College')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='department.department', verbose_name='Department')),
                ('field_of_study', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='study_field.studyfield', verbose_name='Field of Study')),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='program.program', verbose_name='Program')),
                ('study_program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='study_program.studyprogram', verbose_name='Study Program')),
            ],
            options={
                'verbose_name': 'Application Information',
                'verbose_name_plural': 'Application Information',
                'ordering': ['-created_at'],
            },
        ),
    ]
