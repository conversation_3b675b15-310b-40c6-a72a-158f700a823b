from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from user_management.models import Role


class Command(BaseCommand):
    help = 'Add a new role with specified permissions'

    def add_arguments(self, parser):
        parser.add_argument('role_name', type=str, help='Name of the role to create')
        parser.add_argument(
            '--description',
            type=str,
            default='',
            help='Description of the role'
        )
        parser.add_argument(
            '--permissions',
            nargs='*',
            help='List of permission codenames (e.g., auth.add_user auth.change_user)'
        )
        parser.add_argument(
            '--copy-from',
            type=str,
            help='Copy permissions from an existing role'
        )
        parser.add_argument(
            '--inactive',
            action='store_true',
            help='Create role as inactive'
        )

    def handle(self, *args, **options):
        role_name = options['role_name']
        description = options['description']
        permissions = options.get('permissions', [])
        copy_from = options.get('copy_from')
        is_active = not options['inactive']

        # Check if role already exists
        if Group.objects.filter(name=role_name).exists():
            self.stdout.write(
                self.style.ERROR(f'Role "{role_name}" already exists!')
            )
            return

        try:
            # Create the group
            group = Group.objects.create(name=role_name)
            self.stdout.write(f'Created group: {role_name}')

            # Create the role
            role = Role.objects.create(
                group=group,
                description=description,
                is_active=is_active
            )
            self.stdout.write(f'Created role: {role_name}')

            # Add permissions
            permission_objects = []

            # Copy permissions from existing role
            if copy_from:
                try:
                    source_group = Group.objects.get(name=copy_from)
                    permission_objects.extend(source_group.permissions.all())
                    self.stdout.write(f'Copied permissions from: {copy_from}')
                except Group.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Source role "{copy_from}" not found')
                    )

            # Add specific permissions
            for perm_codename in permissions:
                try:
                    if '.' in perm_codename:
                        app_label, codename = perm_codename.split('.')
                        permission = Permission.objects.get(
                            codename=codename,
                            content_type__app_label=app_label
                        )
                        permission_objects.append(permission)
                        self.stdout.write(f'Added permission: {perm_codename}')
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f'Invalid permission format: {perm_codename}. '
                                'Use format: app_label.codename'
                            )
                        )
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {perm_codename}')
                    )

            # Set permissions
            if permission_objects:
                group.permissions.set(permission_objects)
                self.stdout.write(f'Assigned {len(permission_objects)} permissions')

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created role "{role_name}" with {len(permission_objects)} permissions'
                )
            )

            # Display role summary
            self.stdout.write('\n--- Role Summary ---')
            self.stdout.write(f'Name: {role.name}')
            self.stdout.write(f'Description: {role.description}')
            self.stdout.write(f'Active: {role.is_active}')
            self.stdout.write(f'Permissions: {group.permissions.count()}')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating role: {str(e)}')
            )
            # Cleanup on error
            if 'group' in locals():
                group.delete()
            if 'role' in locals():
                role.delete()


# Example usage:
# python manage.py add_role "Department Head" --description "Manages department operations" --permissions auth.view_user auth.change_user
# python manage.py add_role "Coordinator" --copy-from "Staff" --permissions auth.add_user
# python manage.py add_role "Intern" --description "Limited access role" --inactive
