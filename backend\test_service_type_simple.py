#!/usr/bin/env python
"""
Simple test script for Service Type model and basic functionality.
This script tests the model creation, validation, and basic operations.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.exceptions import ValidationError
from setups.certificate_type.models import CertificateType
from setups.service_type.models import ServiceType
from decimal import Decimal

def test_service_type_model():
    """Test Service Type model functionality."""
    print("=" * 60)
    print("TESTING SERVICE TYPE MODEL")
    print("=" * 60)
    
    # Test 1: Create certificate types for testing
    print("\n1. Creating test certificate types...")
    cert_types = []
    for name in ['Academic Transcript', 'Diploma Certificate', 'Verification Letter']:
        cert_type, created = CertificateType.objects.get_or_create(
            name=name,
            defaults={'is_active': True}
        )
        cert_types.append(cert_type)
        if created:
            print(f"✅ Created certificate type: {name}")
        else:
            print(f"📋 Certificate type already exists: {name}")
    
    # Test 2: Create a service type
    print("\n2. Testing ServiceType creation...")
    try:
        service_type = ServiceType.objects.create(
            name='Academic Verification Service',
            fee=Decimal('25.50'),
            is_active=True
        )
        print(f"✅ Created service type: {service_type.name}")
        print(f"   ID: {service_type.id}")
        print(f"   Fee: ${service_type.fee}")
        print(f"   Active: {service_type.is_active}")
        print(f"   String representation: {str(service_type)}")
    except Exception as e:
        print(f"❌ Error creating service type: {e}")
        return
    
    # Test 3: Add document types to service type
    print("\n3. Testing document type associations...")
    try:
        service_type.document_types.add(cert_types[0], cert_types[1])
        print(f"✅ Added {len(cert_types[:2])} document types to service")
        print(f"   Document types count: {service_type.document_types_count}")
        print(f"   Active document types count: {service_type.active_document_types_count}")
        
        # List associated document types
        associated_types = service_type.document_types.all()
        print(f"   Associated types: {[dt.name for dt in associated_types]}")
    except Exception as e:
        print(f"❌ Error adding document types: {e}")
    
    # Test 4: Test validation
    print("\n4. Testing validation...")
    
    # Test duplicate name validation
    try:
        duplicate_service = ServiceType(
            name='Academic Verification Service',  # Same name
            fee=Decimal('30.00'),
            is_active=True
        )
        duplicate_service.full_clean()
        print("❌ Duplicate name validation failed - should have raised error")
    except ValidationError as e:
        print("✅ Duplicate name validation works correctly")
        print(f"   Error: {e.message_dict.get('name', ['Unknown error'])[0]}")
    except Exception as e:
        print(f"❌ Unexpected error in duplicate validation: {e}")
    
    # Test negative fee validation
    try:
        negative_fee_service = ServiceType(
            name='Negative Fee Service',
            fee=Decimal('-10.00'),
            is_active=True
        )
        negative_fee_service.full_clean()
        print("❌ Negative fee validation failed - should have raised error")
    except ValidationError as e:
        print("✅ Negative fee validation works correctly")
        print(f"   Error: {e.message_dict.get('fee', ['Unknown error'])[0]}")
    except Exception as e:
        print(f"❌ Unexpected error in negative fee validation: {e}")
    
    # Test empty name validation
    try:
        empty_name_service = ServiceType(
            name='',
            fee=Decimal('15.00'),
            is_active=True
        )
        empty_name_service.full_clean()
        print("❌ Empty name validation failed - should have raised error")
    except ValidationError as e:
        print("✅ Empty name validation works correctly")
        print(f"   Error: {e.message_dict.get('name', ['Unknown error'])[0]}")
    except Exception as e:
        print(f"❌ Unexpected error in empty name validation: {e}")
    
    # Test 5: Update service type
    print("\n5. Testing service type updates...")
    try:
        service_type.name = 'Premium Academic Verification Service'
        service_type.fee = Decimal('35.00')
        service_type.save()
        print(f"✅ Updated service type: {service_type.name}")
        print(f"   New fee: ${service_type.fee}")
    except Exception as e:
        print(f"❌ Error updating service type: {e}")
    
    # Test 6: Test properties
    print("\n6. Testing model properties...")
    try:
        print(f"✅ Document types count: {service_type.document_types_count}")
        print(f"✅ Active document types count: {service_type.active_document_types_count}")
        
        # Deactivate one certificate type to test active count
        cert_types[0].is_active = False
        cert_types[0].save()
        print(f"✅ After deactivating one cert type:")
        print(f"   Total document types: {service_type.document_types_count}")
        print(f"   Active document types: {service_type.active_document_types_count}")
        
        # Reactivate for cleanup
        cert_types[0].is_active = True
        cert_types[0].save()
    except Exception as e:
        print(f"❌ Error testing properties: {e}")
    
    # Test 7: List all service types
    print("\n7. Testing service type queries...")
    try:
        all_services = ServiceType.objects.all()
        print(f"✅ Total service types in database: {all_services.count()}")
        
        active_services = ServiceType.objects.filter(is_active=True)
        print(f"✅ Active service types: {active_services.count()}")
        
        for service in all_services:
            print(f"   - {service.name}: ${service.fee} ({'Active' if service.is_active else 'Inactive'})")
    except Exception as e:
        print(f"❌ Error querying service types: {e}")
    
    # Test 8: Test ordering
    print("\n8. Testing model ordering...")
    try:
        # Create another service type to test ordering
        service_type2 = ServiceType.objects.create(
            name='Basic Verification Service',
            fee=Decimal('15.00'),
            is_active=True
        )
        
        ordered_services = ServiceType.objects.all()
        print("✅ Service types ordered by name:")
        for service in ordered_services:
            print(f"   - {service.name}")
        
        # Clean up
        service_type2.delete()
    except Exception as e:
        print(f"❌ Error testing ordering: {e}")
    
    print("\n" + "=" * 60)
    print("SERVICE TYPE MODEL TESTING COMPLETED")
    print("=" * 60)
    
    # Clean up
    try:
        service_type.delete()
        print("\n✅ Cleanup completed - test service type deleted")
    except Exception as e:
        print(f"\n❌ Error during cleanup: {e}")

if __name__ == '__main__':
    test_service_type_model()
