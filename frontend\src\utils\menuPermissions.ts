/**
 * Menu Permission System
 * Defines permission requirements for each menu item and provides utilities
 * for checking user access to specific menu items based on their permissions and roles.
 */

import { User } from '@/contexts/AuthContext';

// Define menu permission requirements
export interface MenuPermission {
  // Permission-based access (any of these permissions grants access)
  permissions?: string[];
  // Role-based access (user must have staff/superuser status)
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  // Group-based access (user must be in one of these groups)
  groups?: string[];
  // Custom access function for complex logic
  customCheck?: (user: User) => boolean;
  // Public access (no authentication required)
  public?: boolean;
}

// Menu permission definitions
export const MENU_PERMISSIONS: Record<string, MenuPermission> = {
  // ===== APPLICANT DASHBOARD MENUS =====
  'dashboard': {
    public: false, // Requires authentication but no specific permissions
  },
  'personal-information': {
    public: false,
  },
  'application': {
    public: false,
  },
  'application-status': {
    public: false,
  },
  'notifications': {
    public: false,
  },

  // ===== DASHBOARD MENUS =====
  'graduation-dashboard': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'application-dashboard': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'service-fee-dashboard': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== GRADUATE VERIFICATION =====
  'manage-graduates': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-colleges': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-departments': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'graduate-fields-of-study': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-admission-classifications': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-programs': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== APPLICATION PORTAL =====
  'manage-colleges-app': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-departments-app': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-programs-app': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-study-programs': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-admission-types': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-registration-periods': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-fields-of-study-app': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-years': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-terms': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'application-information': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'manage-applicants': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'downloadable-content': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== SERVICES =====
  'service-types': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'document-types': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'alumni-applications-service': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== OFFICIALS =====
  'certificate-types': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'official-certificates': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== COMMUNICATION =====
  'announcements': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'official-management': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== ADMINISTRATIVE MENUS =====
  'user-management': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== SYSTEM ADMINISTRATION =====
  'system-settings': {
    requireSuperuser: true,
    // Superusers have automatic access
  },
  'authentication-management': {
    requireSuperuser: true,
    // Superusers have automatic access
  },
  'database-management': {
    requireSuperuser: true,
    // Superusers have automatic access
  },
  'permission-management': {
    requireSuperuser: true,
    // Superusers have automatic access
  },

  // ===== REPORTS & ANALYTICS =====
  'reports': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'analytics': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== SETTINGS =====
  'general-settings': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== AUTHENTICATION & AUTHORIZATION =====
  'authentication-authorization': {
    requireSuperuser: true,
    // Superusers have automatic access
  }
};

/**
 * Check if user has access to a specific menu item
 */
export const hasMenuAccess = (menuKey: string, user: User | null): boolean => {
  // If user is not authenticated, only allow public menus
  if (!user) {
    const menuPermission = MENU_PERMISSIONS[menuKey];
    return menuPermission?.public === true;
  }

  // SUPERUSERS HAVE ACCESS TO EVERYTHING
  if (user.is_superuser) {
    return true;
  }

  // Get menu permission requirements
  const menuPermission = MENU_PERMISSIONS[menuKey];

  // If no permission defined, allow access for authenticated users
  if (!menuPermission) {
    return true;
  }

  // Check public access
  if (menuPermission.public === true) {
    return true;
  }

  // Check superuser requirement (already handled above)
  if (menuPermission.requireSuperuser) {
    return false; // Non-superusers can't access superuser-only items
  }

  // Check staff requirement
  if (menuPermission.requireStaff && !user.is_staff) {
    return false;
  }

  // Check custom access function
  if (menuPermission.customCheck) {
    return menuPermission.customCheck(user);
  }

  // Check permissions (user needs at least one of the required permissions)
  if (menuPermission.permissions && menuPermission.permissions.length > 0) {
    const userPermissions = user.permissions || [];
    const hasRequiredPermission = menuPermission.permissions.some(
      permission => userPermissions.includes(permission)
    );
    
    if (!hasRequiredPermission) {
      return false;
    }
  }

  // Check groups (user needs to be in at least one of the required groups)
  if (menuPermission.groups && menuPermission.groups.length > 0) {
    const userGroups = user.role_names || [];
    const hasRequiredGroup = menuPermission.groups.some(
      group => userGroups.includes(group)
    );
    
    if (!hasRequiredGroup) {
      return false;
    }
  }

  return true;
};

/**
 * Get all accessible menu keys for a user
 */
export const getAccessibleMenus = (user: User | null): string[] => {
  return Object.keys(MENU_PERMISSIONS).filter(menuKey => 
    hasMenuAccess(menuKey, user)
  );
};

/**
 * Check if user has any admin access
 */
export const hasAnyAdminAccess = (user: User | null): boolean => {
  if (!user) return false;
  
  // Superusers always have admin access
  if (user.is_superuser) return true;
  
  // Staff users have admin access
  if (user.is_staff) return true;
  
  return false;
};

/**
 * Get user's access level for display purposes
 */
export const getUserAccessLevel = (user: User | null): string => {
  if (!user) return 'Guest';
  if (user.is_superuser) return 'Super Administrator';
  if (user.is_staff) return 'Staff';
  return 'User';
};

/**
 * Debug function to list all permissions for a user
 */
export const debugUserPermissions = (user: User | null): void => {
  if (!user) {
    console.log('No user provided for permission debug');
    return;
  }

  console.group('🔐 User Permission Debug');
  console.log('User:', user.username);
  console.log('Is Staff:', user.is_staff);
  console.log('Is Superuser:', user.is_superuser);
  console.log('Permissions:', user.permissions || []);
  console.log('Role Names:', user.role_names || []);
  
  console.group('📋 Menu Access');
  Object.keys(MENU_PERMISSIONS).forEach(menuKey => {
    const hasAccess = hasMenuAccess(menuKey, user);
    console.log(`${hasAccess ? '✅' : '❌'} ${menuKey}`);
  });
  console.groupEnd();
  
  console.groupEnd();
};
