/**
 * Menu Permission System
 * Defines permission requirements for each menu item and provides utilities
 * for checking user access to specific menu items based on their permissions and roles.
 */

import { User } from '@/contexts/AuthContext';

// Define menu permission requirements
export interface MenuPermission {
  // Permission-based access (any of these permissions grants access)
  permissions?: string[];
  // Role-based access (user must have staff/superuser status)
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  // Group-based access (user must be in one of these groups)
  groups?: string[];
  // Allow staff users even if they have no groups/permissions
  allowStaffWithoutGroups?: boolean;
  // Custom access function for complex logic
  customCheck?: (user: User) => boolean;
  // Public access (no authentication required)
  public?: boolean;
}

// Menu permission definitions
export const MENU_PERMISSIONS: Record<string, MenuPermission> = {
  // ===== APPLICANT DASHBOARD MENUS =====
  'dashboard': {
    public: false, // Requires authentication but no specific permissions
  },
  'personal-information': {
    public: false,
  },
  'application': {
    public: false,
  },
  'application-status': {
    public: false,
  },
  'notifications': {
    public: false,
  },

  // ===== DASHBOARD MENUS =====
  'graduation-dashboard': {
    requireStaff: true,
    permissions: ['unknown.view_graduatestudent', 'graduate.view_graduate', 'graduate.view_dashboard'],
    groups: ['Verification Clerk', 'Registrar', 'Department Head', 'Main Registrar', 'Administrator'],
    allowStaffWithoutGroups: false
  },
  'application-dashboard': {
    requireStaff: true,
    permissions: ['application.view_application', 'application.view_dashboard'],
    groups: ['Verification Clerk', 'Registrar', 'Department Head', 'Main Registrar', 'Administrator'],
    allowStaffWithoutGroups: false
  },
  'service-fee-dashboard': {
    requireStaff: true,
    permissions: ['service.view_service', 'service.view_dashboard'],
    groups: ['Verification Clerk', 'Registrar', 'Department Head', 'Main Registrar', 'Administrator'],
    allowStaffWithoutGroups: false
  },

  // ===== GRADUATE VERIFICATION =====
  'manage-graduates': {
    requireStaff: true,
    permissions: ['unknown.view_graduatestudent', 'unknown.change_graduatestudent', 'unknown.add_graduatestudent'],
    groups: ['Verification Clerk', 'Registrar', 'Department Head', 'Main Registrar', 'Administrator']
  },
  'manage-colleges': {
    requireStaff: true,
    permissions: ['unknown.view_verificationcollege', 'unknown.change_verificationcollege', 'unknown.add_verificationcollege'],
    groups: ['Verification Clerk', 'Main Registrar', 'Administrator']
  },
  'manage-departments': {
    requireStaff: true,
    permissions: ['unknown.view_verificationdepartment', 'unknown.change_verificationdepartment', 'unknown.add_verificationdepartment'],
    groups: ['Verification Clerk', 'Department Head', 'Main Registrar', 'Administrator']
  },
  'graduate-fields-of-study': {
    requireStaff: true,
    permissions: ['unknown.view_verificationfieldofstudy', 'unknown.change_verificationfieldofstudy', 'unknown.add_verificationfieldofstudy'],
    groups: ['Verification Clerk', 'Registrar', 'Department Head', 'Main Registrar', 'Administrator']
  },
  'manage-admission-classifications': {
    requireStaff: true,
    permissions: ['admission.view_classification', 'admission.change_classification'],
    groups: ['Verification Clerk', 'Registrar', 'Main Registrar', 'Administrator']
  },
  'manage-programs': {
    requireStaff: true,
    permissions: ['unknown.view_verificationprogram', 'unknown.change_verificationprogram', 'unknown.add_verificationprogram'],
    groups: ['Verification Clerk', 'Department Head', 'Main Registrar', 'Administrator']
  },

  // ===== APPLICATION PORTAL =====
  'manage-colleges-app': {
    requireStaff: true,
    permissions: ['college.view_college', 'college.change_college'],
    groups: ['Main Registrar', 'Administrator']
  },
  'manage-departments-app': {
    requireStaff: true,
    permissions: ['department.view_department', 'department.change_department'],
    groups: ['Department Head', 'Main Registrar', 'Administrator']
  },
  'manage-programs-app': {
    requireStaff: true,
    permissions: ['program.view_program', 'program.change_program'],
    groups: ['Department Head', 'Main Registrar', 'Administrator']
  },
  'manage-study-programs': {
    requireStaff: true,
    permissions: ['program.view_studyprogram', 'program.change_studyprogram'],
    groups: ['Department Head', 'Main Registrar', 'Administrator']
  },
  'manage-admission-types': {
    requireStaff: true,
    permissions: ['admission.view_type', 'admission.change_type'],
    groups: ['Registrar', 'Main Registrar', 'Administrator']
  },
  'manage-registration-periods': {
    requireStaff: true,
    permissions: ['registration.view_period', 'registration.change_period'],
    groups: ['Main Registrar', 'Administrator']
  },
  'manage-fields-of-study-app': {
    requireStaff: true,
    permissions: ['field.view_field', 'field.change_field'],
    groups: ['Registrar', 'Department Head', 'Main Registrar', 'Administrator']
  },
  'manage-years': {
    requireStaff: true,
    permissions: ['academic.view_year', 'academic.change_year'],
    groups: ['Main Registrar', 'Administrator']
  },
  'manage-terms': {
    requireStaff: true,
    permissions: ['academic.view_term', 'academic.change_term'],
    groups: ['Main Registrar', 'Administrator']
  },
  'application-information': {
    requireStaff: true,
    permissions: ['application.view_application', 'application.view_statistics'],
    groups: ['Registrar', 'Department Head', 'Main Registrar', 'Administrator']
  },
  'manage-applicants': {
    requireStaff: true,
    permissions: ['applicant.view_applicant', 'applicant.change_applicant'],
    groups: ['Registrar', 'Department Head', 'Main Registrar', 'Administrator']
  },
  'downloadable-content': {
    requireStaff: true,
    permissions: ['content.view_document', 'content.change_document'],
    groups: ['Registrar', 'Main Registrar', 'Administrator']
  },

  // ===== SERVICES =====
  'service-types': {
    requireStaff: true,
    permissions: ['service.view_servicetype', 'service.change_servicetype'],
    groups: ['Registrar', 'Main Registrar', 'Administrator']
  },
  'document-types': {
    requireStaff: true,
    permissions: ['document.view_documenttype', 'document.change_documenttype'],
    groups: ['Registrar', 'Main Registrar', 'Administrator']
  },
  'alumni-applications-service': {
    requireStaff: true,
    permissions: ['alumni.view_application', 'alumni.change_application'],
    groups: ['Registrar', 'Main Registrar', 'Administrator']
  },

  // ===== OFFICIALS =====
  'certificate-types': {
    requireStaff: true,
    permissions: ['certificate.view_certificatetype', 'certificate.change_certificatetype'],
    groups: ['Main Registrar', 'Administrator']
  },
  'official-certificates': {
    requireStaff: true,
    permissions: ['certificate.view_certificate', 'certificate.change_certificate'],
    groups: ['Main Registrar', 'Administrator']
  },

  // ===== COMMUNICATION =====
  'announcements': {
    requireStaff: true,
    permissions: ['communication.view_announcement', 'communication.add_announcement'],
    groups: ['Main Registrar', 'Administrator']
  },
  'official-management': {
    requireStaff: true,
    permissions: ['communication.view_official', 'communication.change_official'],
    groups: ['Main Registrar', 'Administrator']
  },

  // ===== ADMINISTRATIVE MENUS =====
  'user-management': {
    requireStaff: true,
    permissions: ['auth.view_user', 'auth.change_user'],
    groups: ['Administrator']
  },

  // ===== SYSTEM ADMINISTRATION =====
  'system-settings': {
    requireSuperuser: true,
    // Superusers have automatic access
  },
  'authentication-management': {
    requireSuperuser: true,
    // Superusers have automatic access
  },
  'database-management': {
    requireSuperuser: true,
    // Superusers have automatic access
  },
  'permission-management': {
    requireSuperuser: true,
    // Superusers have automatic access
  },

  // ===== REPORTS & ANALYTICS =====
  'reports': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },
  'analytics': {
    requireStaff: true,
    // Basic staff access - specific permissions checked within components
  },

  // ===== SETTINGS =====
  'general-settings': {
    requireStaff: true,
    permissions: ['settings.view_setting', 'settings.change_setting'],
    groups: ['Administrator']
  },

  // ===== AUTHENTICATION & AUTHORIZATION =====
  'authentication-authorization': {
    requireSuperuser: true,
    permissions: ['auth.view_user', 'auth.change_user', 'auth.view_group', 'auth.change_group'],
    groups: ['Administrator']
  }
};

/**
 * Check if user has access to a specific menu item
 */
export const hasMenuAccess = (menuKey: string, user: User | null): boolean => {
  // If user is not authenticated, only allow public menus
  if (!user) {
    const menuPermission = MENU_PERMISSIONS[menuKey];
    return menuPermission?.public === true;
  }

  // SUPERUSERS HAVE ACCESS TO EVERYTHING
  if (user.is_superuser) {
    return true;
  }

  // Get menu permission requirements
  const menuPermission = MENU_PERMISSIONS[menuKey];

  // If no permission defined, allow access for authenticated users
  if (!menuPermission) {
    return true;
  }

  // Check public access
  if (menuPermission.public === true) {
    return true;
  }

  // Check superuser requirement (already handled above)
  if (menuPermission.requireSuperuser) {
    return false; // Non-superusers can't access superuser-only items
  }

  // Check staff requirement
  if (menuPermission.requireStaff && !user.is_staff) {
    return false;
  }

  // Check custom access function
  if (menuPermission.customCheck) {
    return menuPermission.customCheck(user);
  }

  // SPECIAL CASE: Verification Clerk group gets access to verification-related menus
  const userGroups = user.role_names || [];
  if (userGroups.includes('Verification Clerk')) {
    const verificationMenus = [
      'graduation-dashboard',
      'manage-graduates',
      'manage-colleges',
      'manage-departments',
      'graduate-fields-of-study',
      'manage-programs'
    ];

    if (verificationMenus.includes(menuKey)) {
      console.log(`✅ Menu Access Granted: Verification Clerk access for menu ${menuKey}`);
      return true;
    }
  }

  // STRICT PERMISSION CHECKING: Non-superusers must have explicit permissions or groups
  if (!user.is_superuser) {
    // Check if menu requires specific permissions
    if (menuPermission.permissions && menuPermission.permissions.length > 0) {
      const userPermissions = user.permissions || [];
      const hasRequiredPermission = menuPermission.permissions.some(
        permission => userPermissions.includes(permission)
      );

      if (!hasRequiredPermission) {
        console.log(`🔒 Menu Access Denied: Missing required permissions for menu ${menuKey}`, {
          required: menuPermission.permissions,
          userHas: userPermissions
        });
        return false;
      }
    }

    // Check if menu requires specific groups
    if (menuPermission.groups && menuPermission.groups.length > 0) {
      const userGroups = user.role_names || [];
      const hasRequiredGroup = menuPermission.groups.some(
        group => userGroups.includes(group)
      );

      if (!hasRequiredGroup) {
        console.log(`🔒 Menu Access Denied: Missing required groups for menu ${menuKey}`, {
          required: menuPermission.groups,
          userHas: userGroups
        });
        return false;
      }
    }

    // Check if user has required groups (more permissive for group-based access)
    if (menuPermission.groups && menuPermission.groups.length > 0) {
      const userGroups = user.role_names || [];
      const hasRequiredGroup = menuPermission.groups.some(
        group => userGroups.includes(group)
      );

      // If user has required group, grant access regardless of specific permissions
      if (hasRequiredGroup) {
        console.log(`✅ Menu Access Granted: User has required group for menu ${menuKey}`, {
          userGroups,
          requiredGroups: menuPermission.groups
        });
        return true;
      }
    }

    // DENY BY DEFAULT: If no specific permissions or groups are defined for this menu,
    // and user is not superuser, deny access unless explicitly allowed
    if ((!menuPermission.permissions || menuPermission.permissions.length === 0) &&
        (!menuPermission.groups || menuPermission.groups.length === 0) &&
        !menuPermission.allowStaffWithoutGroups &&
        menuPermission.requireStaff) {
      console.log(`🔒 Menu Access Denied: No explicit permissions defined for non-superuser menu ${menuKey}`);
      return false;
    }

    // For staff users, check if they have any groups or permissions at all
    if (user.is_staff && menuPermission.requireStaff) {
      const userHasGroups = user.role_names && user.role_names.length > 0;
      const userHasPermissions = user.permissions && user.permissions.length > 0;

      // If staff user has no groups or permissions, deny access (unless menu allows it)
      if (!userHasGroups && !userHasPermissions && !menuPermission.allowStaffWithoutGroups) {
        console.log(`🔒 Menu Access Denied: Staff user without groups/permissions for menu ${menuKey}`);
        return false;
      }
    }
  }

  console.log(`✅ Menu Access Granted: All checks passed for menu ${menuKey}`);
  return true;
};

/**
 * Get all accessible menu keys for a user
 */
export const getAccessibleMenus = (user: User | null): string[] => {
  return Object.keys(MENU_PERMISSIONS).filter(menuKey => 
    hasMenuAccess(menuKey, user)
  );
};

/**
 * Check if user has any admin access - STRICT MODE
 */
export const hasAnyAdminAccess = (user: User | null): boolean => {
  if (!user) return false;

  // Superusers always have admin access
  if (user.is_superuser) return true;

  // STRICT MODE: Staff users must have groups or permissions to access admin
  if (user.is_staff) {
    const userHasGroups = user.role_names && user.role_names.length > 0;
    const userHasPermissions = user.permissions && user.permissions.length > 0;

    // Staff must have at least one group or permission
    return userHasGroups || userHasPermissions;
  }

  return false;
};

/**
 * Get user's access level for display purposes
 */
export const getUserAccessLevel = (user: User | null): string => {
  if (!user) return 'Guest';
  if (user.is_superuser) return 'Super Administrator';
  if (user.is_staff) return 'Staff';
  return 'User';
};

/**
 * Debug function to list all permissions for a user
 */
export const debugUserPermissions = (user: User | null): void => {
  if (!user) {
    console.log('No user provided for permission debug');
    return;
  }

  console.group('🔐 User Permission Debug');
  console.log('User:', user.username);
  console.log('Is Staff:', user.is_staff);
  console.log('Is Superuser:', user.is_superuser);
  console.log('Permissions:', user.permissions || []);
  console.log('Role Names:', user.role_names || []);
  
  console.group('📋 Menu Access');
  Object.keys(MENU_PERMISSIONS).forEach(menuKey => {
    const hasAccess = hasMenuAccess(menuKey, user);
    console.log(`${hasAccess ? '✅' : '❌'} ${menuKey}`);
  });
  console.groupEnd();
  
  console.groupEnd();
};
