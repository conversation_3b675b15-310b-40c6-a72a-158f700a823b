/**
 * Menu Permission System
 * Defines permission requirements for each menu item and provides utilities
 * for checking user access to specific menu items based on their permissions and roles.
 */

import { User } from '@/contexts/AuthContext';

// Define menu permission requirements
export interface MenuPermission {
  // Permission-based access (any of these permissions grants access)
  permissions?: string[];
  // Role-based access (user must have staff/superuser status)
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  // Group-based access (user must be in one of these groups)
  groups?: string[];
  // Custom access function for complex logic
  customCheck?: (user: User) => boolean;
  // Public access (no authentication required)
  public?: boolean;
}

// Menu permission definitions
export const MENU_PERMISSIONS: Record<string, MenuPermission> = {
  // ===== APPLICANT DASHBOARD MENUS =====
  'dashboard': {
    public: false, // Requires authentication but no specific permissions
  },
  'personal-information': {
    public: false,
  },
  'application': {
    public: false,
  },
  'application-status': {
    public: false,
  },
  'notifications': {
    public: false,
  },

  // ===== STAFF DASHBOARD MENUS =====
  'graduate-admin': {
    requireStaff: true,
    permissions: [
      'GraduateVerification.view_graduatestudent',
      'GraduateVerification.view_verificationcollege',
      'study_program.view_studyprogram'
    ]
  },
  'graduate-dashboard': {
    requireStaff: true,
    permissions: [
      'GraduateVerification.view_graduatestudent',
      'GraduateVerification.view_verificationcollege'
    ]
  },
  'graduate-management': {
    requireStaff: true,
    permissions: [
      'GraduateVerification.add_graduatestudent',
      'GraduateVerification.change_graduatestudent',
      'GraduateVerification.delete_graduatestudent',
      'GraduateVerification.view_graduatestudent'
    ]
  },
  'application-dashboard': {
    requireStaff: true,
    permissions: [
      'alumni_applications.view_alumniapplication',
      'registration.view_applicantinformation'
    ]
  },
  'alumni-applications': {
    requireStaff: true,
    permissions: [
      'alumni_applications.view_alumniapplication',
      'alumni_applications.add_alumniapplication',
      'alumni_applications.change_alumniapplication'
    ]
  },

  // ===== ADMINISTRATIVE MENUS =====
  'user-management': {
    requireStaff: true,
    permissions: [
      'user_management.view_userprofile',
      'user_management.change_userprofile',
      'auth.view_user'
    ]
  },
  'program-management': {
    requireStaff: true,
    permissions: [
      'program.view_program',
      'program.add_program',
      'program.change_program',
      'study_program.view_studyprogram',
      'study_program.change_studyprogram'
    ]
  },
  'department-management': {
    requireStaff: true,
    permissions: [
      'department.view_department',
      'department.add_department',
      'department.change_department'
    ]
  },
  'college-management': {
    requireStaff: true,
    permissions: [
      'GraduateVerification.view_verificationcollege',
      'GraduateVerification.add_verificationcollege',
      'GraduateVerification.change_verificationcollege'
    ]
  },

  // ===== SERVICE MANAGEMENT =====
  'service-management': {
    requireStaff: true,
    permissions: [
      'services.view_service',
      'services.add_service',
      'services.change_service',
      'service_type.view_servicetype',
      'service_type.change_servicetype'
    ]
  },
  'certificate-management': {
    requireStaff: true,
    permissions: [
      'certificate_type.view_certificatetype',
      'certificate_type.add_certificatetype',
      'certificate_type.change_certificatetype'
    ]
  },
  'document-management': {
    requireStaff: true,
    permissions: [
      'downloadable.view_downloadable',
      'downloadable.add_downloadable',
      'downloadable.change_downloadable'
    ]
  },

  // ===== COMMUNICATION =====
  'announcements': {
    requireStaff: true,
    permissions: [
      'communication.view_announcement',
      'communication.add_announcement',
      'communication.change_announcement'
    ]
  },
  'official-management': {
    requireStaff: true,
    permissions: [
      'official.view_officialsent',
      'official.view_officialreceived',
      'official.change_officialsent',
      'official.change_officialreceived'
    ]
  },

  // ===== SYSTEM ADMINISTRATION =====
  'system-settings': {
    requireSuperuser: true,
    permissions: [
      'settings_manager.view_organizationsetting',
      'settings_manager.change_organizationsetting',
      'settings_manager.add_socialmedialink'
    ]
  },
  'authentication-management': {
    requireSuperuser: true,
    permissions: [
      'auth.view_user',
      'auth.add_user',
      'auth.change_user',
      'auth.delete_user',
      'sessions.view_session',
      'token_blacklist.view_outstandingtoken'
    ]
  },
  'database-management': {
    requireSuperuser: true,
    permissions: [
      'admin.view_logentry',
      'contenttypes.view_contenttype'
    ]
  },

  // ===== REPORTS & ANALYTICS =====
  'reports': {
    requireStaff: true,
    permissions: [
      'GraduateVerification.view_graduatestudent',
      'alumni_applications.view_alumniapplication',
      'registration.view_applicantinformation'
    ]
  },
  'analytics': {
    requireStaff: true,
    permissions: [
      'GraduateVerification.view_graduatestudent',
      'study_program.view_studyprogram'
    ]
  }
};

/**
 * Check if user has access to a specific menu item
 */
export const hasMenuAccess = (menuKey: string, user: User | null): boolean => {
  // If user is not authenticated, only allow public menus
  if (!user) {
    const menuPermission = MENU_PERMISSIONS[menuKey];
    return menuPermission?.public === true;
  }

  // Get menu permission requirements
  const menuPermission = MENU_PERMISSIONS[menuKey];
  
  // If no permission defined, allow access for authenticated users
  if (!menuPermission) {
    return true;
  }

  // Check public access
  if (menuPermission.public === true) {
    return true;
  }

  // Check superuser requirement
  if (menuPermission.requireSuperuser && !user.is_superuser) {
    return false;
  }

  // Check staff requirement
  if (menuPermission.requireStaff && !user.is_staff && !user.is_superuser) {
    return false;
  }

  // Check custom access function
  if (menuPermission.customCheck) {
    return menuPermission.customCheck(user);
  }

  // Check permissions (user needs at least one of the required permissions)
  if (menuPermission.permissions && menuPermission.permissions.length > 0) {
    const userPermissions = user.permissions || [];
    const hasRequiredPermission = menuPermission.permissions.some(
      permission => userPermissions.includes(permission)
    );
    
    if (!hasRequiredPermission) {
      return false;
    }
  }

  // Check groups (user needs to be in at least one of the required groups)
  if (menuPermission.groups && menuPermission.groups.length > 0) {
    const userGroups = user.role_names || [];
    const hasRequiredGroup = menuPermission.groups.some(
      group => userGroups.includes(group)
    );
    
    if (!hasRequiredGroup) {
      return false;
    }
  }

  return true;
};

/**
 * Get all accessible menu keys for a user
 */
export const getAccessibleMenus = (user: User | null): string[] => {
  return Object.keys(MENU_PERMISSIONS).filter(menuKey => 
    hasMenuAccess(menuKey, user)
  );
};

/**
 * Check if user has any admin access
 */
export const hasAnyAdminAccess = (user: User | null): boolean => {
  if (!user) return false;
  
  // Superusers always have admin access
  if (user.is_superuser) return true;
  
  // Staff users have admin access
  if (user.is_staff) return true;
  
  return false;
};

/**
 * Get user's access level for display purposes
 */
export const getUserAccessLevel = (user: User | null): string => {
  if (!user) return 'Guest';
  if (user.is_superuser) return 'Super Administrator';
  if (user.is_staff) return 'Staff';
  return 'User';
};

/**
 * Debug function to list all permissions for a user
 */
export const debugUserPermissions = (user: User | null): void => {
  if (!user) {
    console.log('No user provided for permission debug');
    return;
  }

  console.group('🔐 User Permission Debug');
  console.log('User:', user.username);
  console.log('Is Staff:', user.is_staff);
  console.log('Is Superuser:', user.is_superuser);
  console.log('Permissions:', user.permissions || []);
  console.log('Role Names:', user.role_names || []);
  
  console.group('📋 Menu Access');
  Object.keys(MENU_PERMISSIONS).forEach(menuKey => {
    const hasAccess = hasMenuAccess(menuKey, user);
    console.log(`${hasAccess ? '✅' : '❌'} ${menuKey}`);
  });
  console.groupEnd();
  
  console.groupEnd();
};
