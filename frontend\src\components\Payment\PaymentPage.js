import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Typography,
  Alert,
  Paper,
} from '@mui/material';
import { PaymentOutlined, CheckCircleOutline, ErrorOutline } from '@mui/icons-material';

const PaymentPage = () => {
  const { applicationId } = useParams();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [applicationData, setApplicationData] = useState(null);
  const [paymentProvider, setPaymentProvider] = useState('TB'); // Default to Telebirr
  const [paymentMethod, setPaymentMethod] = useState('H5'); // Default to H5 Web
  const [paymentUrl, setPaymentUrl] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState(null);
  const [paymentId, setPaymentId] = useState(null);
  
  // Fetch application details
  useEffect(() => {
    const fetchApplicationData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/registration/program-selection/${applicationId}/`);
        setApplicationData(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load application details. Please try again later.');
        setLoading(false);
      }
    };
    
    fetchApplicationData();
  }, [applicationId]);
  
  // Poll for payment status if we have a payment ID
  useEffect(() => {
    let intervalId;
    
    if (paymentId) {
      intervalId = setInterval(async () => {
        try {
          const response = await axios.get(`/api/registration/payment/status/${paymentId}/`);
          setPaymentStatus(response.data);
          
          // If payment is completed, stop polling
          if (response.data.is_completed) {
            clearInterval(intervalId);
            // Redirect to application details after a short delay
            setTimeout(() => {
              navigate(`/applications/${applicationId}/details`);
            }, 3000);
          }
        } catch (err) {
          console.error('Error checking payment status:', err);
        }
      }, 5000); // Check every 5 seconds
    }
    
    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [paymentId, applicationId, navigate]);
  
  const handleProviderChange = (event) => {
    setPaymentProvider(event.target.value);
  };
  
  const handleMethodChange = (event) => {
    setPaymentMethod(event.target.value);
  };
  
  const initiatePayment = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`/api/registration/payment/initiate/${applicationId}/`, {
        params: {
          provider: paymentProvider,
          method: paymentMethod
        }
      });
      
      if (response.data.success) {
        setPaymentUrl(response.data.payment_url);
        setPaymentId(response.data.payment_id);
        
        // Open the payment URL in a new window
        window.open(response.data.payment_url, '_blank');
      } else {
        setError(response.data.message || 'Payment initiation failed. Please try again.');
      }
      
      setLoading(false);
    } catch (err) {
      setError('Failed to initiate payment. Please try again later.');
      setLoading(false);
    }
  };
  
  if (loading && !applicationData) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }
  
  if (error && !applicationData) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          <PaymentOutlined sx={{ mr: 1, verticalAlign: 'middle' }} />
          Application Payment
        </Typography>
        
        {applicationData && (
          <Box mb={4}>
            <Typography variant="h6" gutterBottom>
              Application Details
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1">
                  <strong>Application Number:</strong> {applicationData.application_num}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body1">
                  <strong>Program:</strong> {applicationData.application_info.program_name}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body1">
                  <strong>Fee Amount:</strong> {applicationData.application_info.application_fee} ETB
                </Typography>
              </Grid>
            </Grid>
          </Box>
        )}
        
        <Divider sx={{ mb: 4 }} />
        
        {paymentStatus && paymentStatus.is_completed ? (
          <Alert 
            icon={<CheckCircleOutline fontSize="inherit" />} 
            severity="success"
            sx={{ mb: 3 }}
          >
            Payment completed successfully! Redirecting to your application...
          </Alert>
        ) : (
          <>
            {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
            
            <Box mb={4}>
              <Typography variant="h6" gutterBottom>
                Select Payment Method
              </Typography>
              
              <FormControl component="fieldset" sx={{ mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Payment Provider
                </Typography>
                <RadioGroup
                  row
                  name="payment-provider"
                  value={paymentProvider}
                  onChange={handleProviderChange}
                >
                  <FormControlLabel value="TB" control={<Radio />} label="Telebirr" />
                  <FormControlLabel value="CB" control={<Radio />} label="CBE Birr" />
                </RadioGroup>
              </FormControl>
              
              <FormControl component="fieldset">
                <Typography variant="subtitle1" gutterBottom>
                  Payment Method
                </Typography>
                <RadioGroup
                  row
                  name="payment-method"
                  value={paymentMethod}
                  onChange={handleMethodChange}
                >
                  <FormControlLabel value="H5" control={<Radio />} label="Web Payment" />
                  <FormControlLabel value="US" control={<Radio />} label="USSD" />
                  <FormControlLabel value="AP" control={<Radio />} label="Mobile App" />
                </RadioGroup>
              </FormControl>
            </Box>
            
            <Box display="flex" justifyContent="space-between">
              <Button
                variant="outlined"
                onClick={() => navigate(`/applications/${applicationId}/details`)}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={initiatePayment}
                disabled={loading || !applicationData}
                startIcon={loading ? <CircularProgress size={20} /> : null}
              >
                {loading ? 'Processing...' : 'Pay Now'}
              </Button>
            </Box>
            
            {paymentUrl && (
              <Box mt={3}>
                <Alert severity="info">
                  Payment initiated! If a new window didn't open, please{' '}
                  <a href={paymentUrl} target="_blank" rel="noopener noreferrer">
                    click here
                  </a>{' '}
                  to complete your payment.
                </Alert>
              </Box>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
};

export default PaymentPage;
