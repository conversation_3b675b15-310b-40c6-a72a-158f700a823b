# Generated by Django 5.2.1 on 2025-06-01 20:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('certificate_type', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OfficialReceived',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('second_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female')], max_length=1)),
                ('sender_institute', models.Char<PERSON>ield(max_length=255)),
                ('arival_date', models.DateField()),
                ('courier', models.Char<PERSON><PERSON>(max_length=100)),
                ('tracking_number', models.CharField(max_length=100, unique=True)),
                ('certificate_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='received_certificates', to='certificate_type.certificatetype')),
            ],
        ),
        migrations.CreateModel(
            name='OfficialSent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('second_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female')], max_length=1)),
                ('receiver_institute', models.CharField(max_length=255)),
                ('send_date', models.DateField()),
                ('courier', models.CharField(max_length=100)),
                ('tracking_number', models.CharField(max_length=100, unique=True)),
                ('certificate_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='sent_certificates', to='certificate_type.certificatetype')),
            ],
        ),
    ]
