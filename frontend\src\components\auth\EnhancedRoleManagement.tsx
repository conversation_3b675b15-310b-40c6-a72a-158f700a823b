import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Shield, 
  Plus, 
  Edit, 
  Trash2,
  Search, 
  Filter, 
  RefreshCw,
  Users,
  Crown,
  Layers,
  Clock,
  MapPin,
  Building,
  Briefcase,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ArrowUp,
  ArrowDown,
  Copy,
  Download,
  Upload,
  Settings,
  Eye,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';
import api from '@/services/api';

// Enhanced Role Types for Large-Scale Systems
interface EnhancedRole {
  id: number;
  name: string;
  description: string;
  level: number;
  parent_role?: number;
  is_system_role: boolean;
  is_active: boolean;
  permissions: Permission[];
  user_count: number;
  created_at: string;
  updated_at: string;
  context_restrictions?: {
    departments?: string[];
    projects?: string[];
    time_restrictions?: {
      start_time: string;
      end_time: string;
      days_of_week: number[];
    };
    ip_restrictions?: string[];
    location_restrictions?: string[];
  };
  auto_assignment_rules?: {
    department_based: boolean;
    seniority_based: boolean;
    skill_based: boolean;
    custom_rules: Record<string, any>;
  };
}

interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: string;
  app_label: string;
  model: string;
  description?: string;
}

interface RoleTemplate {
  id: number;
  name: string;
  description: string;
  permissions: number[];
  level: number;
  is_public: boolean;
}

const EnhancedRoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<EnhancedRole[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roleTemplates, setRoleTemplates] = useState<RoleTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [showBulkDialog, setShowBulkDialog] = useState(false);
  const [editingRole, setEditingRole] = useState<EnhancedRole | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState('');
  const [filterActive, setFilterActive] = useState<boolean | undefined>(undefined);
  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);

  // Form states
  const [roleForm, setRoleForm] = useState<Partial<EnhancedRole>>({
    name: '',
    description: '',
    level: 1,
    is_active: true,
    permissions: [],
    context_restrictions: {},
    auto_assignment_rules: {
      department_based: false,
      seniority_based: false,
      skill_based: false,
      custom_rules: {}
    }
  });

  const [bulkOperation, setBulkOperation] = useState({
    action: 'activate' as 'activate' | 'deactivate' | 'delete' | 'assign_permissions' | 'change_level',
    permissions: [] as number[],
    level: 1
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [rolesResponse, permissionsResponse, templatesResponse] = await Promise.all([
        api.get('/auth/enhanced-roles/'),
        api.get('/auth/permissions/'),
        api.get('/auth/role-templates/')
      ]);
      
      setRoles(rolesResponse.data.results || rolesResponse.data);
      setPermissions(permissionsResponse.data.results || permissionsResponse.data);
      setRoleTemplates(templatesResponse.data.results || templatesResponse.data);
    } catch (error) {
      toast.error('Failed to load role data');
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRole = async () => {
    try {
      await api.post('/auth/enhanced-roles/', roleForm);
      toast.success('Role created successfully');
      setShowCreateDialog(false);
      resetForm();
      loadData();
    } catch (error) {
      toast.error('Failed to create role');
      console.error('Error creating role:', error);
    }
  };

  const handleUpdateRole = async () => {
    if (!editingRole) return;
    
    try {
      await api.patch(`/auth/enhanced-roles/${editingRole.id}/`, roleForm);
      toast.success('Role updated successfully');
      setShowEditDialog(false);
      setEditingRole(null);
      resetForm();
      loadData();
    } catch (error) {
      toast.error('Failed to update role');
      console.error('Error updating role:', error);
    }
  };

  const handleDeleteRole = async (roleId: number) => {
    if (!confirm('Are you sure you want to delete this role? This action cannot be undone.')) return;
    
    try {
      await api.delete(`/auth/enhanced-roles/${roleId}/`);
      toast.success('Role deleted successfully');
      loadData();
    } catch (error) {
      toast.error('Failed to delete role');
      console.error('Error deleting role:', error);
    }
  };

  const handleBulkOperation = async () => {
    if (selectedRoles.length === 0) {
      toast.error('Please select roles first');
      return;
    }

    try {
      const payload = {
        role_ids: selectedRoles,
        action: bulkOperation.action,
        ...(bulkOperation.action === 'assign_permissions' && { permissions: bulkOperation.permissions }),
        ...(bulkOperation.action === 'change_level' && { level: bulkOperation.level })
      };

      await api.post('/auth/enhanced-roles/bulk-operation/', payload);
      toast.success(`Bulk ${bulkOperation.action} completed successfully`);
      setShowBulkDialog(false);
      setSelectedRoles([]);
      loadData();
    } catch (error) {
      toast.error(`Failed to perform bulk ${bulkOperation.action}`);
      console.error('Error in bulk operation:', error);
    }
  };

  const handleCloneRole = async (role: EnhancedRole) => {
    const newName = prompt('Enter name for cloned role:', `${role.name} (Copy)`);
    if (!newName) return;
    
    try {
      const cloneData = {
        ...role,
        name: newName,
        id: undefined,
        created_at: undefined,
        updated_at: undefined
      };
      
      await api.post('/auth/enhanced-roles/', cloneData);
      toast.success('Role cloned successfully');
      loadData();
    } catch (error) {
      toast.error('Failed to clone role');
      console.error('Error cloning role:', error);
    }
  };

  const handleExportRoles = async () => {
    try {
      const response = await api.get('/auth/enhanced-roles/export/', { responseType: 'blob' });
      const blob = new Blob([response.data], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'roles_export.json';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Roles exported successfully');
    } catch (error) {
      toast.error('Failed to export roles');
      console.error('Error exporting roles:', error);
    }
  };

  const openEditDialog = (role: EnhancedRole) => {
    setEditingRole(role);
    setRoleForm({
      name: role.name,
      description: role.description,
      level: role.level,
      parent_role: role.parent_role,
      is_active: role.is_active,
      permissions: role.permissions,
      context_restrictions: role.context_restrictions || {},
      auto_assignment_rules: role.auto_assignment_rules || {
        department_based: false,
        seniority_based: false,
        skill_based: false,
        custom_rules: {}
      }
    });
    setShowEditDialog(true);
  };

  const resetForm = () => {
    setRoleForm({
      name: '',
      description: '',
      level: 1,
      is_active: true,
      permissions: [],
      context_restrictions: {},
      auto_assignment_rules: {
        department_based: false,
        seniority_based: false,
        skill_based: false,
        custom_rules: {}
      }
    });
  };

  const getRoleLevelBadge = (level: number) => {
    const colors = {
      1: 'bg-gray-100 text-gray-800',
      2: 'bg-blue-100 text-blue-800',
      3: 'bg-green-100 text-green-800',
      4: 'bg-yellow-100 text-yellow-800',
      5: 'bg-red-100 text-red-800'
    };
    
    return (
      <Badge variant="outline" className={colors[level as keyof typeof colors] || 'bg-purple-100 text-purple-800'}>
        Level {level}
      </Badge>
    );
  };

  const getPermissionsByApp = () => {
    const permissionsByApp: Record<string, Permission[]> = {};
    permissions.forEach(permission => {
      const app = permission.app_label || 'Unknown';
      if (!permissionsByApp[app]) {
        permissionsByApp[app] = [];
      }
      permissionsByApp[app].push(permission);
    });
    return permissionsByApp;
  };

  const filteredRoles = roles.filter(role => {
    const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesLevel = !filterLevel || role.level.toString() === filterLevel;
    const matchesActive = filterActive === undefined || role.is_active === filterActive;
    
    return matchesSearch && matchesLevel && matchesActive;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Enhanced Role Management</h1>
          <p className="text-muted-foreground">
            Scalable role system for large user bases with flexible permissions
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportRoles}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={loadData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {selectedRoles.length > 0 && (
            <Button variant="outline" onClick={() => setShowBulkDialog(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Bulk Actions ({selectedRoles.length})
            </Button>
          )}
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Role
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{roles.length}</div>
            <p className="text-xs text-muted-foreground">
              {roles.filter(r => r.is_active).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Roles</CardTitle>
            <Crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(r => r.is_system_role).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Built-in roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.reduce((sum, role) => sum + role.user_count, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Role Levels</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.max(...roles.map(r => r.level), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Maximum level
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search Roles</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or description..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="level-filter">Role Level</Label>
              <Select value={filterLevel} onValueChange={setFilterLevel}>
                <SelectTrigger>
                  <SelectValue placeholder="All levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All levels</SelectItem>
                  {[1, 2, 3, 4, 5].map(level => (
                    <SelectItem key={level} value={level.toString()}>
                      Level {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={filterActive === undefined ? '' : filterActive.toString()}
                onValueChange={(value) => setFilterActive(value === '' ? undefined : value === 'true')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setFilterLevel('');
                  setFilterActive(undefined);
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Roles Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Enhanced Roles ({filteredRoles.length})</span>
          </CardTitle>
          <CardDescription>
            Manage roles with hierarchical permissions and context-based access control
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedRoles.length === filteredRoles.length && filteredRoles.length > 0}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedRoles(filteredRoles.map(r => r.id));
                        } else {
                          setSelectedRoles([]);
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>Role Name</TableHead>
                  <TableHead>Level</TableHead>
                  <TableHead>Users</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading roles...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredRoles.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="text-muted-foreground">
                        <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No roles found</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRoles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedRoles.includes(role.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedRoles([...selectedRoles, role.id]);
                            } else {
                              setSelectedRoles(selectedRoles.filter(id => id !== role.id));
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{role.name}</div>
                          <div className="text-sm text-muted-foreground max-w-xs truncate">
                            {role.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getRoleLevelBadge(role.level)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{role.user_count} users</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{role.permissions.length} permissions</Badge>
                      </TableCell>
                      <TableCell>
                        {role.is_active ? (
                          <Badge variant="outline" className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-red-100 text-red-800">
                            <XCircle className="h-3 w-3 mr-1" />
                            Inactive
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {role.is_system_role ? (
                          <Badge variant="default">
                            <Crown className="h-3 w-3 mr-1" />
                            System
                          </Badge>
                        ) : (
                          <Badge variant="outline">Custom</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(role)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCloneRole(role)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          {!role.is_system_role && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteRole(role.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedRoleManagement;
