from rest_framework import serializers
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from .models import UserProfile, Role, PermissionCategory

class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for UserProfile model
    """
    full_name = serializers.ReadOnlyField()
    roles = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = UserProfile
        fields = ['department', 'phone', 'employee_id', 'full_name', 'roles', 'permissions', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def get_roles(self, obj):
        return [group.name for group in obj.user.groups.all()]

    def get_permissions(self, obj):
        return list(obj.user.get_all_permissions())


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for the User model with full CRUD operations and RBAC information.
    """
    password = serializers.CharField(write_only=True, required=False)
    profile = UserProfileSerializer(read_only=True)
    roles = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    role_names = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'password', 'is_active', 'is_staff', 'is_superuser',
            'date_joined', 'last_login', 'groups', 'profile',
            'roles', 'permissions', 'role_names'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login', 'roles', 'permissions', 'role_names']

    def get_roles(self, obj):
        """Get detailed role information"""
        return [{'id': group.id, 'name': group.name} for group in obj.groups.all()]

    def get_permissions(self, obj):
        """Get all permissions for the user including detailed permission info"""
        # Get all permissions (both user-specific and group permissions)
        all_permissions = obj.get_all_permissions()

        # Also get detailed permission objects for more information
        user_permissions = obj.user_permissions.all()
        group_permissions = []

        for group in obj.groups.all():
            group_permissions.extend(group.permissions.all())

        # Create detailed permission list
        detailed_permissions = []
        for perm in set(list(user_permissions) + group_permissions):
            detailed_permissions.append({
                'id': perm.id,
                'name': perm.name,
                'codename': perm.codename,
                'content_type': {
                    'id': perm.content_type.id,
                    'app_label': perm.content_type.app_label,
                    'model': perm.content_type.model,
                }
            })

        return {
            'permission_strings': list(all_permissions),
            'detailed_permissions': detailed_permissions,
            'permission_count': len(all_permissions)
        }

    def get_role_names(self, obj):
        """Get simple list of role names"""
        return [group.name for group in obj.groups.all()]

    def create(self, validated_data):
        """
        Create a new user with encrypted password and return it
        """
        password = validated_data.pop('password', None)
        groups = validated_data.pop('groups', [])

        user = User.objects.create(**validated_data)

        if password:
            user.set_password(password)

        if groups:
            user.groups.set(groups)

        user.save()
        return user

    def update(self, instance, validated_data):
        """
        Update and return an existing user instance
        If password is provided and not empty, update it
        If password is not provided or empty, keep the old password
        """
        # Check if password is in the validated data
        password = validated_data.pop('password', None)
        groups = validated_data.pop('groups', None)

        # Update all other fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Only update password if it's provided and not empty
        if password is not None and password.strip():
            instance.set_password(password)

        # Update groups if provided
        if groups is not None:
            instance.groups.set(groups)

        instance.save()
        return instance


class UserDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for returning enhanced user details with RBAC information
    """
    is_staff = serializers.BooleanField(read_only=True)
    is_superuser = serializers.BooleanField(read_only=True)
    last_login = serializers.DateTimeField(read_only=True)
    role_names = serializers.SerializerMethodField()
    roles = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    profile = serializers.SerializerMethodField()
    is_admin = serializers.SerializerMethodField()
    auth_info = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_staff',
                 'is_superuser', 'is_active', 'date_joined', 'last_login', 'role_names',
                 'roles', 'permissions', 'profile', 'is_admin', 'auth_info']
        read_only_fields = fields

    def get_role_names(self, obj):
        """Get list of role names for the user"""
        return list(obj.groups.values_list('name', flat=True))

    def get_roles(self, obj):
        """Get detailed role information"""
        return [{'id': group.id, 'name': group.name} for group in obj.groups.all()]

    def get_permissions(self, obj):
        """Get comprehensive permission information for the user"""
        # Get all permissions (both user-specific and group permissions)
        all_permissions = obj.get_all_permissions()

        # Also get detailed permission objects for more information
        user_permissions = obj.user_permissions.all()
        group_permissions = []

        for group in obj.groups.all():
            group_permissions.extend(group.permissions.all())

        # Create detailed permission list
        detailed_permissions = []
        for perm in set(list(user_permissions) + group_permissions):
            detailed_permissions.append({
                'id': perm.id,
                'name': perm.name,
                'codename': perm.codename,
                'content_type': {
                    'id': perm.content_type.id,
                    'app_label': perm.content_type.app_label,
                    'model': perm.content_type.model,
                }
            })

        return {
            'permission_strings': list(all_permissions),
            'detailed_permissions': detailed_permissions,
            'permission_count': len(all_permissions),
            'has_permissions': len(all_permissions) > 0
        }

    def get_profile(self, obj):
        """Get user profile information"""
        try:
            profile = obj.profile
            return {
                'department': profile.department,
                'phone': profile.phone,
                'employee_id': profile.employee_id,
                'full_name': f"{obj.first_name} {obj.last_name}".strip() or obj.username
            }
        except:
            return {
                'department': None,
                'phone': None,
                'employee_id': None,
                'full_name': f"{obj.first_name} {obj.last_name}".strip() or obj.username
            }

    def get_is_admin(self, obj):
        """Check if user has admin privileges"""
        return obj.is_superuser or obj.groups.filter(name__in=['Admin', 'Super Admin']).exists()


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration with password confirmation
    """
    password1 = serializers.CharField(write_only=True)
    password2 = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'password1', 'password2']
        extra_kwargs = {
            'password1': {'write_only': True},
            'password2': {'write_only': True},
            'email': {'required': True},
            'first_name': {'required': False},
            'last_name': {'required': False}
        }

    def validate(self, data):
        """
        Check that the two passwords match.
        """
        if data['password1'] != data['password2']:
            raise serializers.ValidationError("Passwords do not match.")

        # Ensure email is valid and not empty
        email = data.get('email')
        if not email:
            raise serializers.ValidationError("Email is required.")

        return data

    def create(self, validated_data):
        """
        Create a new user and return it.
        """
        # Remove password2 from validated data as it's not needed for user creation
        validated_data.pop('password2', None)

        # Extract password1 for user creation (create_user expects 'password')
        password = validated_data.pop('password1')

        # Extract first_name and last_name if present
        first_name = validated_data.pop('first_name', '')
        last_name = validated_data.pop('last_name', '')

        # Create the user using create_user method (this handles password hashing)
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=password
        )

        # Set first_name and last_name separately
        user.first_name = first_name
        user.last_name = last_name
        user.save()

        return user


class StaffSerializer(UserSerializer):
    """
    Serializer for staff members (users with is_staff=True)
    """
    department = serializers.CharField(required=False, allow_blank=True)
    role = serializers.CharField(required=False, allow_blank=True)

    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields + ['department', 'role']

    def create(self, validated_data):
        """
        Create a new staff member with is_staff=True
        """
        validated_data['is_staff'] = True
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """
        Update a staff member
        Ensure is_staff remains True
        """
        # Make sure is_staff remains True
        validated_data['is_staff'] = True

        # Use the parent class's update method to handle password and other fields
        return super().update(instance, validated_data)


class RoleSerializer(serializers.ModelSerializer):
    """
    Serializer for the custom Role model
    """
    name = serializers.CharField(source='group.name')
    permissions = serializers.SerializerMethodField()
    permission_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=Permission.objects.all(),
        source='group.permissions',
        write_only=True,
        required=False
    )
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = ['id', 'name', 'description', 'is_active', 'permissions', 'permission_ids',
                 'user_count', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at', 'permissions', 'user_count']

    def get_permissions(self, obj):
        return [{'id': perm.id, 'name': perm.name, 'codename': perm.codename}
                for perm in obj.group.permissions.all()]

    def get_user_count(self, obj):
        return obj.group.user_set.count()

    def create(self, validated_data):
        group_data = validated_data.pop('group', {})
        permissions = group_data.pop('permissions', [])

        # Create the group first
        group = Group.objects.create(name=group_data.get('name'))

        # Create the role
        role = Role.objects.create(group=group, **validated_data)

        # Set permissions
        if permissions:
            group.permissions.set(permissions)

        return role

    def update(self, instance, validated_data):
        group_data = validated_data.pop('group', {})
        permissions = group_data.pop('permissions', None)

        # Update group name if provided
        if 'name' in group_data:
            instance.group.name = group_data['name']
            instance.group.save()

        # Update role fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Update permissions if provided
        if permissions is not None:
            instance.group.permissions.set(permissions)

        instance.save()
        return instance


class GroupSerializer(serializers.ModelSerializer):
    """
    Serializer for the Group model (for backward compatibility)
    """
    permissions = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=Permission.objects.all(),
        required=False
    )
    permission_details = serializers.SerializerMethodField()
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Group
        fields = ['id', 'name', 'permissions', 'permission_details', 'user_count']

    def get_permission_details(self, obj):
        return [{'id': perm.id, 'name': perm.name, 'codename': perm.codename}
                for perm in obj.permissions.all()]

    def get_user_count(self, obj):
        return obj.user_set.count()

    def create(self, validated_data):
        permissions = validated_data.pop('permissions', [])
        group = Group.objects.create(**validated_data)

        if permissions:
            group.permissions.set(permissions)

        return group

    def update(self, instance, validated_data):
        permissions = validated_data.pop('permissions', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if permissions is not None:
            instance.permissions.set(permissions)

        instance.save()
        return instance


class PermissionCategorySerializer(serializers.ModelSerializer):
    """
    Serializer for PermissionCategory model
    """
    class Meta:
        model = PermissionCategory
        fields = ['id', 'name', 'description', 'created_at']
        read_only_fields = ['created_at']


class ContentTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the ContentType model
    """
    class Meta:
        model = ContentType
        fields = ['id', 'app_label', 'model']


class PermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for the Permission model
    """
    content_type = ContentTypeSerializer(read_only=True)
    content_type_id = serializers.PrimaryKeyRelatedField(
        queryset=ContentType.objects.all(),
        source='content_type',
        write_only=True
    )

    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type', 'content_type_id']
