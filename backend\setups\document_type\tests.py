from django.test import TestCase
from django.core.exceptions import ValidationError
from .models import DocumentType

class DocumentTypeModelTest(TestCase):
    def test_create_document_type(self):
        """Test creating a document type."""
        doc_type = DocumentType.objects.create(
            name="Birth Certificate",
            description="Official birth certificate document"
        )
        self.assertEqual(doc_type.name, "Birth Certificate")
        self.assertTrue(doc_type.is_active)
        self.assertIsNotNone(doc_type.id)

    def test_document_type_str_representation(self):
        """Test string representation of document type."""
        doc_type = DocumentType(name="Passport")
        self.assertEqual(str(doc_type), "Passport")

    def test_duplicate_name_validation(self):
        """Test that duplicate names are not allowed."""
        DocumentType.objects.create(name="ID Card")

        with self.assertRaises(ValidationError):
            duplicate = DocumentType(name="ID Card")
            duplicate.full_clean()

    def test_empty_name_validation(self):
        """Test that empty names are not allowed."""
        with self.assertRaises(ValidationError):
            doc_type = DocumentType(name="")
            doc_type.full_clean()

    def test_short_name_validation(self):
        """Test that names must be at least 2 characters."""
        with self.assertRaises(ValidationError):
            doc_type = DocumentType(name="A")
            doc_type.full_clean()
