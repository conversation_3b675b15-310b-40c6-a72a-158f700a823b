import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { PermissionGate } from './PermissionGate';
import { DynamicButton, ViewButton, EditButton, DeleteButton } from './DynamicButton';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
  permissions?: string[];
  groups?: string[];
  hideWhenDenied?: boolean;
}

interface TableAction {
  label: string;
  action: 'view' | 'edit' | 'delete' | 'custom';
  icon?: React.ReactNode;
  onClick: (row: any) => void;
  permissions?: string[];
  groups?: string[];
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost';
  customCheck?: (row: any, user: any) => boolean;
}

interface DynamicTableProps {
  data: any[];
  columns: TableColumn[];
  actions?: TableAction[];
  feature?: string;
  permissions?: string[];
  groups?: string[];
  
  // Table behavior
  showActions?: boolean;
  actionsLabel?: string;
  emptyMessage?: string;
  loading?: boolean;
  
  // Row-level permissions
  rowPermissionCheck?: (row: any, user: any) => boolean;
}

/**
 * Dynamic Table Component
 * Automatically controls column visibility and row actions based on permissions
 */
export const DynamicTable: React.FC<DynamicTableProps> = ({
  data,
  columns,
  actions = [],
  feature = 'table',
  permissions,
  groups,
  showActions = true,
  actionsLabel = 'Actions',
  emptyMessage = 'No data available',
  loading = false,
  rowPermissionCheck
}) => {
  const featureAccess = useFeatureAccess(feature, { permissions, groups });

  // Filter columns based on permissions
  const visibleColumns = columns.filter(column => {
    if (!column.permissions && !column.groups) return true;
    
    return (
      <PermissionGate
        permissions={column.permissions}
        groups={column.groups}
        hideWhenDenied
      >
        <span />
      </PermissionGate>
    );
  });

  // Filter actions based on permissions
  const getVisibleActions = (row: any) => {
    return actions.filter(action => {
      // Check row-level permissions
      if (rowPermissionCheck && !rowPermissionCheck(row, null)) {
        return false;
      }
      
      // Check action-specific permissions
      if (action.customCheck && !action.customCheck(row, null)) {
        return false;
      }
      
      // Check feature-level permissions
      switch (action.action) {
        case 'view':
          return featureAccess.canView;
        case 'edit':
          return featureAccess.canEdit;
        case 'delete':
          return featureAccess.canDelete;
        default:
          return true;
      }
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!featureAccess.canView) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-muted-foreground">You don't have permission to view this data.</p>
          {featureAccess.deniedReason && (
            <p className="text-sm text-red-600 mt-1">{featureAccess.deniedReason}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            {visibleColumns.map((column) => (
              <TableHead key={column.key}>
                {column.label}
              </TableHead>
            ))}
            {showActions && actions.length > 0 && (
              <TableHead className="text-right">{actionsLabel}</TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell 
                colSpan={visibleColumns.length + (showActions ? 1 : 0)} 
                className="text-center py-8"
              >
                {emptyMessage}
              </TableCell>
            </TableRow>
          ) : (
            data.map((row, index) => {
              const visibleActionsForRow = getVisibleActions(row);
              
              return (
                <TableRow key={index}>
                  {visibleColumns.map((column) => (
                    <TableCell key={column.key}>
                      <PermissionGate
                        permissions={column.permissions}
                        groups={column.groups}
                        hideWhenDenied
                        fallback={
                          <Badge variant="outline" className="text-xs">
                            Restricted
                          </Badge>
                        }
                      >
                        {column.render 
                          ? column.render(row[column.key], row)
                          : row[column.key]
                        }
                      </PermissionGate>
                    </TableCell>
                  ))}
                  {showActions && actions.length > 0 && (
                    <TableCell className="text-right">
                      {visibleActionsForRow.length > 0 && (
                        <div className="flex items-center justify-end gap-2">
                          {visibleActionsForRow.length <= 3 ? (
                            // Show buttons directly if 3 or fewer actions
                            visibleActionsForRow.map((action, actionIndex) => (
                              <DynamicButton
                                key={actionIndex}
                                size="sm"
                                variant={action.variant || 'outline'}
                                onClick={() => action.onClick(row)}
                                feature={feature}
                                action={action.action === 'custom' ? undefined : action.action}
                                permissions={action.permissions}
                                groups={action.groups}
                                customCheck={action.customCheck ? (user) => action.customCheck!(row, user) : undefined}
                              >
                                {action.icon}
                                <span className="sr-only">{action.label}</span>
                              </DynamicButton>
                            ))
                          ) : (
                            // Use dropdown for more than 3 actions
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <DynamicButton
                                  variant="ghost"
                                  size="sm"
                                  feature={feature}
                                  permissions={permissions}
                                  groups={groups}
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </DynamicButton>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {visibleActionsForRow.map((action, actionIndex) => (
                                  <DropdownMenuItem
                                    key={actionIndex}
                                    onClick={() => action.onClick(row)}
                                  >
                                    {action.icon}
                                    <span className="ml-2">{action.label}</span>
                                  </DropdownMenuItem>
                                ))}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </div>
  );
};

// Example usage component
export const GraduateTable: React.FC<{ graduates: any[] }> = ({ graduates }) => {
  const columns: TableColumn[] = [
    {
      key: 'id',
      label: 'ID',
      permissions: ['unknown.view_graduatestudent']
    },
    {
      key: 'name',
      label: 'Name',
      permissions: ['unknown.view_graduatestudent']
    },
    {
      key: 'email',
      label: 'Email',
      permissions: ['unknown.view_graduatestudent'],
      groups: ['Verification Clerk', 'Registrar']
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <Badge variant={value === 'verified' ? 'default' : 'secondary'}>
          {value}
        </Badge>
      )
    }
  ];

  const actions: TableAction[] = [
    {
      label: 'View',
      action: 'view',
      icon: <Eye className="h-4 w-4" />,
      onClick: (row) => console.log('View', row),
      permissions: ['unknown.view_graduatestudent']
    },
    {
      label: 'Edit',
      action: 'edit',
      icon: <Edit className="h-4 w-4" />,
      onClick: (row) => console.log('Edit', row),
      permissions: ['unknown.change_graduatestudent']
    },
    {
      label: 'Delete',
      action: 'delete',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: (row) => console.log('Delete', row),
      permissions: ['unknown.delete_graduatestudent'],
      variant: 'destructive'
    }
  ];

  return (
    <DynamicTable
      data={graduates}
      columns={columns}
      actions={actions}
      feature="graduate"
      permissions={['unknown.view_graduatestudent']}
      groups={['Verification Clerk', 'Registrar', 'Administrator']}
    />
  );
};

export default DynamicTable;
