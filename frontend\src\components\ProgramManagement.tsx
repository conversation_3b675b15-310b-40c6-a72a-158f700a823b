import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Pencil, Trash2, Search, RefreshCw, GraduationCap, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface Program {
  id: number;
  name: string;
  code: string;
}

const ProgramManagement = () => {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentProgram, setCurrentProgram] = useState<Program | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: ''
  });
  const [formErrors, setFormErrors] = useState({
    name: '',
    code: ''
  });

  // Fetch programs on component mount
  useEffect(() => {
    fetchPrograms();
  }, []);

  // Debug log to check programs data structure
  useEffect(() => {
    console.log('Programs state:', programs);
    console.log('Is Array?', Array.isArray(programs));
  }, [programs]);

  const fetchPrograms = async () => {
    setLoading(true);
    try {
      console.log('Fetching programs data');

      // Use the known working endpoint
      const endpoint = 'http://localhost:8000/api/verification-programs/';
      const headers: Record<string, string> = {
        'Accept': 'application/json'
      };

      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(endpoint, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        // If the first endpoint fails, try the alternative endpoint
        console.log('First endpoint failed, trying alternative endpoint');
        const alternativeEndpoint = 'http://localhost:8000/api/programs/';
        const alternativeResponse = await fetch(alternativeEndpoint, {
          method: 'GET',
          headers
        });

        if (!alternativeResponse.ok) {
          throw new Error(`HTTP error! Status: ${alternativeResponse.status}`);
        }

        const data = await alternativeResponse.json();
        console.log('Successfully fetched programs data from alternative endpoint:', data);
        setPrograms(data);
        return;
      }

      const data = await response.json();
      console.log('Successfully fetched programs data:', data);
      setPrograms(data);
    } catch (error: any) {
      console.error('Error fetching programs:', error);
      toast.error(`Failed to fetch programs: ${error.message}`);
      setPrograms([]); // Ensure programs is always an array
    } finally {
      setLoading(false);
    }
  };





  // Helper functions for form handling
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    // Auto-format code to uppercase
    let newValue = value;
    if (name === 'code') {
      newValue = value.toUpperCase();
    }

    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : newValue,
    });

    // Perform real-time validation
    const newErrors = { ...formErrors };

    if (name === 'name') {
      // Clear previous error
      newErrors.name = '';

      // Validate name in real-time
      if (!newValue.trim()) {
        newErrors.name = 'Name is required';
      } else if (newValue.length > 200) {
        newErrors.name = 'Name must be less than 200 characters';
      } else if (newValue.length < 3) {
        newErrors.name = 'Name must be at least 3 characters';
      } else if (Array.isArray(programs)) {
        // Check for duplicate name (only when adding or when editing and name has changed)
        if (!currentProgram) {
          const existingProgram = programs.find(
            (program) => program.name.toLowerCase() === newValue.toLowerCase()
          );
          if (existingProgram) {
            newErrors.name = 'This name is already in use. Program names must be unique.';
          }
        } else if (currentProgram.name.toLowerCase() !== newValue.toLowerCase()) {
          const existingProgram = programs.find(
            (program) =>
              program.id !== currentProgram.id &&
              program.name.toLowerCase() === newValue.toLowerCase()
          );
          if (existingProgram) {
            newErrors.name = 'This name is already in use. Program names must be unique.';
          }
        }
      }
    } else if (name === 'code') {
      // Clear previous error
      newErrors.code = '';

      // Validate code in real-time
      if (!newValue.trim()) {
        newErrors.code = 'Code is required';
      } else if (newValue.length > 20) {
        newErrors.code = 'Code must be less than 20 characters';
      } else if (!/^[A-Za-z0-9]+$/.test(newValue.trim())) {
        newErrors.code = 'Code must contain only letters and numbers';
      } else if (newValue.length < 2) {
        newErrors.code = 'Code must be at least 2 characters';
      } else if (Array.isArray(programs)) {
        // Check for duplicate code (only when adding or when editing and code has changed)
        if (!currentProgram) {
          const existingProgram = programs.find(
            (program) => program.code.toUpperCase() === newValue.toUpperCase()
          );
          if (existingProgram) {
            newErrors.code = 'This code is already in use. Codes must be unique.';
          }
        } else if (currentProgram.code.toUpperCase() !== newValue.toUpperCase()) {
          const existingProgram = programs.find(
            (program) =>
              program.id !== currentProgram.id &&
              program.code.toUpperCase() === newValue.toUpperCase()
          );
          if (existingProgram) {
            newErrors.code = 'This code is already in use. Codes must be unique.';
          }
        }
      }
    }

    setFormErrors(newErrors);
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user selects a value
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    let valid = true;
    const newErrors = {
      name: '',
      code: ''
    };

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      valid = false;
    } else if (formData.name.length > 200) {
      newErrors.name = 'Name must be less than 200 characters';
      valid = false;
    } else if (formData.name.length < 3) {
      newErrors.name = 'Name must be at least 3 characters';
      valid = false;
    } else {
      // Check for duplicate name
      if (!currentProgram && Array.isArray(programs)) {
        const existingProgram = programs.find(
          (program) => program.name.toLowerCase() === formData.name.toLowerCase()
        );
        if (existingProgram) {
          newErrors.name = 'This name is already in use. Program names must be unique.';
          valid = false;
        }
      }
      // When editing, check if the name is changed and if it's already in use
      else if (currentProgram && Array.isArray(programs)) {
        if (currentProgram.name.toLowerCase() !== formData.name.toLowerCase()) {
          const existingProgram = programs.find(
            (program) =>
              program.id !== currentProgram.id &&
              program.name.toLowerCase() === formData.name.toLowerCase()
          );
          if (existingProgram) {
            newErrors.name = 'This name is already in use. Program names must be unique.';
            valid = false;
          }
        }
      }
    }

    // Validate code
    if (!formData.code.trim()) {
      newErrors.code = 'Code is required';
      valid = false;
    } else if (formData.code.length > 20) {
      newErrors.code = 'Code must be less than 20 characters';
      valid = false;
    } else if (!/^[A-Za-z0-9]+$/.test(formData.code.trim())) {
      newErrors.code = 'Code must contain only letters and numbers';
      valid = false;
    } else if (formData.code.length < 2) {
      newErrors.code = 'Code must be at least 2 characters';
      valid = false;
    } else {
      // Check for duplicate code
      if (!currentProgram && Array.isArray(programs)) {
        const existingProgram = programs.find(
          (program) => program.code.toUpperCase() === formData.code.toUpperCase()
        );
        if (existingProgram) {
          newErrors.code = 'This code is already in use. Codes must be unique.';
          valid = false;
        }
      }
      // When editing, check if the code is changed and if it's already in use
      else if (currentProgram && Array.isArray(programs)) {
        if (currentProgram.code.toUpperCase() !== formData.code.toUpperCase()) {
          const existingProgram = programs.find(
            (program) =>
              program.id !== currentProgram.id &&
              program.code.toUpperCase() === formData.code.toUpperCase()
          );
          if (existingProgram) {
            newErrors.code = 'This code is already in use. Codes must be unique.';
            valid = false;
          }
        }
      }
    }

    setFormErrors(newErrors);
    return valid;
  };

  // Helper function to determine the correct API endpoint
  const getApiEndpoint = async () => {
    const token = localStorage.getItem('token');
    if (!token) return null;

    // Try all possible endpoints to find which one works
    const endpoints = [
      'http://localhost:8000/api/verification-programs/',
      'http://localhost:8000/api/programs/'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          console.log(`Found working endpoint: ${endpoint}`);
          return endpoint;
        }
      } catch (error) {
        console.error(`Error testing endpoint ${endpoint}:`, error);
      }
    }

    return null;
  };

  const handleAddProgram = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add a program');
        return;
      }

      // Determine the correct endpoint
      const endpoint = await getApiEndpoint();

      if (!endpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Prepare the data for submission
      const programData = {
        name: formData.name,
        code: formData.code
      };

      // Make the request with the current token
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(programData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          console.log('Server error response:', errorData);

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                const errorMessage = Array.isArray(errors) ? errors[0] : errors.toString();
                newErrors[field as keyof typeof newErrors] = errorMessage;
                hasFieldErrors = true;

                // Log specific uniqueness errors
                if (errorMessage.includes('unique') || errorMessage.includes('already exists')) {
                  console.log(`Uniqueness error for ${field}:`, errorMessage);
                }
              }
            }

            // Check for non-field errors that might indicate uniqueness constraints
            if (errorData.non_field_errors) {
              const nonFieldErrors = Array.isArray(errorData.non_field_errors)
                ? errorData.non_field_errors
                : [errorData.non_field_errors.toString()];

              for (const error of nonFieldErrors) {
                if (error.toLowerCase().includes('name') && error.toLowerCase().includes('unique')) {
                  newErrors.name = 'This program name already exists. Names must be unique.';
                  hasFieldErrors = true;
                }
                if (error.toLowerCase().includes('code') && error.toLowerCase().includes('unique')) {
                  newErrors.code = 'This program code already exists. Codes must be unique.';
                  hasFieldErrors = true;
                }
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Program added successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchPrograms(); // Refresh the list after adding
    } catch (error) {
      console.error('Error adding program:', error);
      toast.error(`Failed to add program: ${error.message}`);
    }
  };

  const handleEditProgram = async () => {
    if (!currentProgram) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit a program');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Prepare the data for submission
      const programData = {
        name: formData.name,
        code: formData.code
      };

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentProgram.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(programData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          console.log('Server error response:', errorData);

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                const errorMessage = Array.isArray(errors) ? errors[0] : errors.toString();
                newErrors[field as keyof typeof newErrors] = errorMessage;
                hasFieldErrors = true;

                // Log specific uniqueness errors
                if (errorMessage.includes('unique') || errorMessage.includes('already exists')) {
                  console.log(`Uniqueness error for ${field}:`, errorMessage);
                }
              }
            }

            // Check for non-field errors that might indicate uniqueness constraints
            if (errorData.non_field_errors) {
              const nonFieldErrors = Array.isArray(errorData.non_field_errors)
                ? errorData.non_field_errors
                : [errorData.non_field_errors.toString()];

              for (const error of nonFieldErrors) {
                if (error.toLowerCase().includes('name') && error.toLowerCase().includes('unique')) {
                  newErrors.name = 'This program name already exists. Names must be unique.';
                  hasFieldErrors = true;
                }
                if (error.toLowerCase().includes('code') && error.toLowerCase().includes('unique')) {
                  newErrors.code = 'This program code already exists. Codes must be unique.';
                  hasFieldErrors = true;
                }
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Program updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchPrograms(); // Refresh the list after updating
    } catch (error) {
      console.error('Error updating program:', error);
      toast.error(`Failed to update program: ${error.message}`);
    }
  };

  const handleDeleteProgram = async () => {
    if (!currentProgram) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete a program');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentProgram.id}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `HTTP error! Status: ${response.status}`);
      }

      toast.success('Program deleted successfully');
      setIsDeleteDialogOpen(false);
      fetchPrograms(); // Refresh the list after deleting
    } catch (error) {
      console.error('Error deleting program:', error);
      toast.error(`Failed to delete program: ${error.message}`);
    }
  };

  const openEditDialog = (program: Program) => {
    console.log('Opening edit dialog with program:', program);
    setCurrentProgram(program);

    // Set form data with safe toString() calls
    const formDataValues = {
      name: program.name || '',
      code: program.code || ''
    };
    console.log('Form data values:', formDataValues);
    setFormData(formDataValues);

    // Clear form errors
    setFormErrors({
      name: '',
      code: ''
    });

    // Open the dialog
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (program: Program) => {
    setCurrentProgram(program);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: ''
    });
    setFormErrors({
      name: '',
      code: ''
    });
    setCurrentProgram(null);
  };

  // Safely filter programs, ensuring programs is an array
  const filteredPrograms = useMemo(() => {
    if (!Array.isArray(programs)) return [];

    return programs.filter(
      (program) =>
        program && (
          (program.name && program.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (program.code && program.code.toLowerCase().includes(searchTerm.toLowerCase()))
        )
    );
  }, [programs, searchTerm]);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredPrograms.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredPrograms.length / itemsPerPage);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Programs Management</CardTitle>
                <CardDescription className="mt-1">
                  Manage programs for graduate verification
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
                setIsAddDialogOpen(open);
                if (!open) {
                  resetForm();
                }
              }}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Program
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-[95vw] sm:max-w-lg lg:max-w-xl mx-4 sm:mx-auto max-h-[90vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
                        <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">Add New Program</DialogTitle>
                        <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                          Enter the details for the new program
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>

                  <div className="p-4 sm:p-5 lg:p-6 space-y-4 lg:space-y-5">
                    {/* Program Name */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <GraduationCap className="h-4 w-4 text-[#1a73c0]" />
                        <Label htmlFor="name" className="text-sm font-semibold text-gray-800">Program Name *</Label>
                      </div>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="e.g. Master of Science, Bachelor of Arts, PhD"
                        className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.name
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.name && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.name}</p>
                        </div>
                      )}
                    </div>

                    {/* Program Code */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                        </svg>
                        <Label htmlFor="code" className="text-sm font-semibold text-gray-800">Program Code *</Label>
                      </div>
                      <Input
                        id="code"
                        name="code"
                        value={formData.code}
                        onChange={handleInputChange}
                        placeholder="e.g. MSC, BA, PHD"
                        className={cn(
                          "h-10 text-sm transition-all duration-200 font-mono",
                          formErrors.code
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.code && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.code}</p>
                        </div>
                      )}
                      <p className="text-xs text-gray-500 flex items-center space-x-1">
                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>Use a short, unique code (2-10 characters)</span>
                      </p>
                    </div>
                  </div>

                  <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t">
                    <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
                      <DialogClose asChild>
                        <Button
                          variant="outline"
                          className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                        >
                          Cancel
                        </Button>
                      </DialogClose>
                      <Button
                        onClick={handleAddProgram}
                        className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create Program
                      </Button>
                    </div>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search Programs
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search by name or code..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="rounded-lg border border-blue-200 overflow-hidden shadow-sm">
            <Table>
              <TableHeader>
                <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
                  <TableHead className="font-semibold text-[#1a73c0]">Name</TableHead>
                  <TableHead className="font-semibold text-[#1a73c0]">Code</TableHead>
                  <TableHead className="text-right font-semibold text-[#1a73c0] w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-5 w-5 animate-spin text-[#1a73c0]" />
                        <span className="text-gray-600">Loading programs...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : currentItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-12">
                      <div className="flex flex-col items-center justify-center space-y-3">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                          <GraduationCap className="w-8 h-8 text-blue-400" />
                        </div>
                        <div className="text-gray-700 font-medium">No programs found</div>
                        <div className="text-sm text-gray-500 max-w-sm text-center">
                          {searchTerm ?
                            'Try adjusting your search criteria to find what you\'re looking for.' :
                            'There are no programs available. Click the "Add Program" button to create one.'}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentItems.map((program) => (
                    <TableRow key={program.id} className="hover:bg-blue-50 transition-colors">
                      <TableCell className="font-medium text-[#1a73c0]">{program.name || 'N/A'}</TableCell>
                      <TableCell className="text-gray-600">{program.code || 'N/A'}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(program)}
                            title="Edit"
                            className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDeleteDialog(program)}
                            title="Delete"
                            className="h-8 w-8 p-0 border-red-200 hover:bg-red-100 hover:text-red-700 transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter>
          {filteredPrograms.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredPrograms.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredPrograms.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        if (!open) {
          resetForm();
        }
      }}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Pencil className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-[#1a73c0]">Edit Program</DialogTitle>
                <DialogDescription className="mt-1">
                  Update the program information
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name" className="text-sm font-medium text-gray-700">Name</Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="e.g. Computer Science"
                className={formErrors.name ? 'border-red-500 focus-visible:ring-red-400' : 'border-blue-200 focus-visible:ring-blue-400'}
              />
              {formErrors.name && (
                <p className="text-sm text-red-500">{formErrors.name}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-code" className="text-sm font-medium text-gray-700">Code</Label>
              <Input
                id="edit-code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                placeholder="e.g. CS, ENG, BIO"
                className={formErrors.code ? 'border-red-500 focus-visible:ring-red-400' : 'border-blue-200 focus-visible:ring-blue-400'}
              />
              {formErrors.code && (
                <p className="text-sm text-red-500">{formErrors.code}</p>
              )}
            </div>
          </div>
          <DialogFooter className="bg-gray-50 px-4 py-3 rounded-b-lg border-t">
            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button variant="outline" className="border-gray-300 hover:bg-gray-100">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                onClick={handleEditProgram}
                className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200"
              >
                Update Program
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-pink-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-red-500 rounded-lg shadow-sm">
                <Trash2 className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-red-600">Confirm Deletion</DialogTitle>
                <DialogDescription className="mt-1">
                  Are you sure you want to delete the program "{currentProgram?.name}"? This action cannot be undone.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">
                <strong>Warning:</strong> Deleting this program will permanently remove it from the system.
                This action cannot be undone.
              </p>
            </div>
          </div>
          <DialogFooter className="bg-gray-50 px-4 py-3 rounded-b-lg border-t">
            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button variant="outline" className="border-gray-300 hover:bg-gray-100">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                variant="destructive"
                onClick={handleDeleteProgram}
                className="bg-red-600 hover:bg-red-700 transition-all duration-200"
              >
                Delete Program
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProgramManagement;
