import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Plus, Pencil, Trash2, Search, RefreshCw, Building, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface AdmissionClassification {
  id: number;
  name: string;
  description: string;
}

const AdmissionClassificationManagement = () => {
  const [admissionClassifications, setAdmissionClassifications] = useState<AdmissionClassification[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentClassification, setCurrentClassification] = useState<AdmissionClassification | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState({
    name: '',
    description: '',
  });

  // Fetch admission classifications on component mount
  useEffect(() => {
    fetchAdmissionClassifications();
  }, []);

  // Debug log to check admission classifications data structure
  useEffect(() => {
    console.log('Admission Classifications state:', admissionClassifications);
    console.log('Is Array?', Array.isArray(admissionClassifications));
  }, [admissionClassifications]);

  const fetchAdmissionClassifications = async () => {
    setLoading(true);
    try {
      console.log('Fetching admission classifications data');

      // Only use the endpoint we know works
      const endpoint = 'http://localhost:8000/api/admission-classifications/';
      const headers: Record<string, string> = {
        'Accept': 'application/json'
      };

      const token = localStorage.getItem('token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(endpoint, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Successfully fetched admission classifications data:', data);
      setAdmissionClassifications(data);
    } catch (error: any) {
      console.error('Error fetching admission classifications:', error);
      toast.error(`Failed to fetch admission classifications: ${error.message}`);
      setAdmissionClassifications([]); // Ensure admission classifications is always an array
    } finally {
      setLoading(false);
    }
  };

  // Helper functions for form handling
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    let valid = true;
    const newErrors = { name: '', description: '' };

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      valid = false;
    } else if (formData.name.length > 100) {
      newErrors.name = 'Name must be less than 100 characters';
      valid = false;
    }

    // Validate description
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
      valid = false;
    } else if (formData.description.length > 255) {
      newErrors.description = 'Description must be less than 255 characters';
      valid = false;
    }

    // Check for duplicate name
    if (!currentClassification && Array.isArray(admissionClassifications)) {
      const existingClassification = admissionClassifications.find(
        (classification) => classification.name.toLowerCase() === formData.name.toLowerCase()
      );
      if (existingClassification) {
        newErrors.name = 'This name is already in use. Names must be unique.';
        valid = false;
      }
    } else if (currentClassification && Array.isArray(admissionClassifications)) {
      if (currentClassification.name.toLowerCase() !== formData.name.toLowerCase()) {
        const existingClassification = admissionClassifications.find(
          (classification) =>
            classification.id !== currentClassification.id &&
            classification.name.toLowerCase() === formData.name.toLowerCase()
        );
        if (existingClassification) {
          newErrors.name = 'This name is already in use. Names must be unique.';
          valid = false;
        }
      }
    }

    setFormErrors(newErrors);
    return valid;
  };

  // Helper function to get the API endpoint
  const getApiEndpoint = async () => {
    const token = localStorage.getItem('token');
    if (!token) return null;

    // Use the known working endpoint
    const endpoint = 'http://localhost:8000/api/admission-classifications/';

    try {
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        return endpoint;
      }
    } catch (error) {
      console.error(`Error testing endpoint ${endpoint}:`, error);
    }

    return null;
  };

  const handleAddClassification = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add an admission classification');
        return;
      }

      // Determine the correct endpoint
      const endpoint = await getApiEndpoint();

      if (!endpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                newErrors[field as keyof typeof newErrors] = Array.isArray(errors) ? errors[0] : errors.toString();
                hasFieldErrors = true;
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Admission classification added successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchAdmissionClassifications(); // Refresh the list after adding
    } catch (error) {
      console.error('Error adding admission classification:', error);
      toast.error(`Failed to add admission classification: ${error.message}`);
    }
  };

  const handleEditClassification = async () => {
    if (!currentClassification) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit an admission classification');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentClassification.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                newErrors[field as keyof typeof newErrors] = Array.isArray(errors) ? errors[0] : errors.toString();
                hasFieldErrors = true;
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Admission classification updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchAdmissionClassifications(); // Refresh the list after updating
    } catch (error) {
      console.error('Error updating admission classification:', error);
      toast.error(`Failed to update admission classification: ${error.message}`);
    }
  };

  const handleDeleteClassification = async () => {
    if (!currentClassification) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete an admission classification');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentClassification.id}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `HTTP error! Status: ${response.status}`);
      }

      toast.success('Admission classification deleted successfully');
      setIsDeleteDialogOpen(false);
      fetchAdmissionClassifications(); // Refresh the list after deleting
    } catch (error) {
      console.error('Error deleting admission classification:', error);
      toast.error(`Failed to delete admission classification: ${error.message}`);
    }
  };

  const openEditDialog = (classification: AdmissionClassification) => {
    setCurrentClassification(classification);
    setFormData({
      name: classification.name,
      description: classification.description,
    });
    setFormErrors({
      name: '',
      description: '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (classification: AdmissionClassification) => {
    setCurrentClassification(classification);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
    });
    setFormErrors({
      name: '',
      description: '',
    });
    setCurrentClassification(null);
  };

  // Safely filter admission classifications, ensuring admissionClassifications is an array
  const filteredAdmissionClassifications = useMemo(() => {
    if (!Array.isArray(admissionClassifications)) return [];

    return admissionClassifications.filter(
      (classification) =>
        classification && (
          (classification.name && classification.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (classification.description && classification.description.toLowerCase().includes(searchTerm.toLowerCase()))
        )
    );
  }, [admissionClassifications, searchTerm]);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredAdmissionClassifications.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredAdmissionClassifications.length / itemsPerPage);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Admission Classifications</CardTitle>
                <CardDescription className="mt-1">
                  Manage admission classifications for graduate verification
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Classification
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-[95vw] sm:max-w-lg lg:max-w-xl mx-4 sm:mx-auto max-h-[90vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
                        <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">Add New Admission Classification</DialogTitle>
                        <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                          Enter the details for the new admission classification
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>

                  <div className="p-4 sm:p-5 lg:p-6 space-y-4 lg:space-y-5">
                    {/* Name */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        <Label htmlFor="name" className="text-sm font-semibold text-gray-800">Classification Name *</Label>
                      </div>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="e.g. Regular, Special, Transfer, International"
                        className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.name
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.name && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.name}</p>
                        </div>
                      )}
                    </div>

                    {/* Description */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <Label htmlFor="description" className="text-sm font-semibold text-gray-800">Description *</Label>
                      </div>
                      <Input
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder="e.g. Students admitted through regular admission process"
                        className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.description
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.description && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.description}</p>
                        </div>
                      )}
                      <p className="text-xs text-gray-500 flex items-center space-x-1">
                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>Provide a clear description of this admission classification</span>
                      </p>
                    </div>
                  </div>

                  <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t">
                    <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
                      <DialogClose asChild>
                        <Button
                          variant="outline"
                          className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                        >
                          Cancel
                        </Button>
                      </DialogClose>
                      <Button
                        onClick={handleAddClassification}
                        className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create Classification
                      </Button>
                    </div>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search Admission Classifications
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search by name or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="rounded-lg border border-blue-200 overflow-hidden shadow-sm">
            <Table>
              <TableHeader>
                <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
                  <TableHead className="font-semibold text-[#1a73c0]">Name</TableHead>
                  <TableHead className="font-semibold text-[#1a73c0]">Description</TableHead>
                  <TableHead className="text-right font-semibold text-[#1a73c0] w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-5 w-5 animate-spin text-[#1a73c0]" />
                        <span className="text-gray-600">Loading admission classifications...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : currentItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-12">
                      <div className="flex flex-col items-center justify-center space-y-3">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                          <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="text-gray-700 font-medium">No admission classifications found</div>
                        <div className="text-sm text-gray-500 max-w-sm text-center">
                          {searchTerm ?
                            'Try adjusting your search criteria to find what you\'re looking for.' :
                            'There are no admission classifications available. Click the "Add Classification" button to create one.'}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentItems.map((classification) => (
                    <TableRow key={classification.id} className="hover:bg-blue-50 transition-colors">
                      <TableCell className="font-medium text-[#1a73c0]">{classification.name}</TableCell>
                      <TableCell className="text-gray-600">{classification.description}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(classification)}
                            title="Edit"
                            className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDeleteDialog(classification)}
                            title="Delete"
                            className="h-8 w-8 p-0 border-red-200 hover:bg-red-100 hover:text-red-700 transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter>
          {filteredAdmissionClassifications.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredAdmissionClassifications.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredAdmissionClassifications.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-lg lg:max-w-xl mx-4 sm:mx-auto max-h-[90vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
                <Pencil className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">Edit Admission Classification</DialogTitle>
                <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                  Update the admission classification information
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="p-4 sm:p-5 lg:p-6 space-y-4 lg:space-y-5">
            {/* Name */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <Label htmlFor="edit-name" className="text-sm font-semibold text-gray-800">Classification Name *</Label>
              </div>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="e.g. Regular, Special, Transfer, International"
                className={cn(
                  "h-10 text-sm transition-all duration-200",
                  formErrors.name
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}
              />
              {formErrors.name && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.name}</p>
                </div>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <Label htmlFor="edit-description" className="text-sm font-semibold text-gray-800">Description *</Label>
              </div>
              <Input
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="e.g. Students admitted through regular admission process"
                className={cn(
                  "h-10 text-sm transition-all duration-200",
                  formErrors.description
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}
              />
              {formErrors.description && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.description}</p>
                </div>
              )}
              <p className="text-xs text-gray-500 flex items-center space-x-1">
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span>Provide a clear description of this admission classification</span>
              </p>
            </div>
          </div>

          <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t">
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
              <DialogClose asChild>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                onClick={handleEditClassification}
                className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Pencil className="h-4 w-4 mr-2" />
                Update Classification
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-pink-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-red-500 rounded-lg shadow-sm">
                <Trash2 className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-red-600">Confirm Deletion</DialogTitle>
                <DialogDescription className="mt-1">
                  Are you sure you want to delete the admission classification "{currentClassification?.name}"? This action cannot be undone.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">
                <strong>Warning:</strong> Deleting this admission classification will permanently remove it from the system.
                This action cannot be undone.
              </p>
            </div>
          </div>
          <DialogFooter className="bg-gray-50 px-4 py-3 rounded-b-lg border-t">
            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button variant="outline" className="border-gray-300 hover:bg-gray-100">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                variant="destructive"
                onClick={handleDeleteClassification}
                className="bg-red-600 hover:bg-red-700 transition-all duration-200"
              >
                Delete Classification
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdmissionClassificationManagement;
