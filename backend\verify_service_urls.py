#!/usr/bin/env python3
"""
Script to verify ServiceType URLs and show current routing configuration.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.service_type.models import ServiceType

def verify_service_urls():
    """Verify and display current ServiceType URL configuration."""
    
    print("🔍 Service Type URL Verification")
    print("=" * 60)
    
    # Get all active service types
    services = ServiceType.objects.filter(is_active=True).order_by('name')
    
    if not services.exists():
        print("❌ No active service types found!")
        return
    
    print(f"📊 Found {services.count()} active service types:\n")
    
    form1_services = []
    form2_services = []
    external_services = []
    no_url_services = []
    
    for service in services:
        print(f"🔸 {service.name}")
        print(f"   ID: {service.id}")
        print(f"   Fee: ${service.fee}")
        print(f"   URL: {service.url or 'None'}")
        
        # Categorize based on URL
        if not service.url or not service.url.strip():
            no_url_services.append(service)
            print(f"   ➡️  Routing: Default to Form2 (Simplified)")
        elif 'form=form1' in service.url or 'form=complete' in service.url:
            form1_services.append(service)
            print(f"   ➡️  Routing: Form1 (Complete Application)")
        elif 'form=form2' in service.url or 'form=simplified' in service.url:
            form2_services.append(service)
            print(f"   ➡️  Routing: Form2 (Simplified Application)")
        elif service.url.startswith('http') and 'localhost' not in service.url:
            external_services.append(service)
            print(f"   ➡️  Routing: External URL (New Tab)")
        else:
            print(f"   ➡️  Routing: Custom Internal URL")
        
        print()
    
    # Summary
    print("=" * 60)
    print("📋 ROUTING SUMMARY")
    print("=" * 60)
    
    if form1_services:
        print(f"\n✅ Form1 (Complete Application) - {len(form1_services)} services:")
        for service in form1_services:
            print(f"   • {service.name}")
    
    if form2_services:
        print(f"\n✅ Form2 (Simplified Application) - {len(form2_services)} services:")
        for service in form2_services:
            print(f"   • {service.name}")
    
    if external_services:
        print(f"\n🌐 External URLs - {len(external_services)} services:")
        for service in external_services:
            print(f"   • {service.name} → {service.url}")
    
    if no_url_services:
        print(f"\n⚠️  No URL (Default to Form2) - {len(no_url_services)} services:")
        for service in no_url_services:
            print(f"   • {service.name}")
    
    # Test URLs
    print("\n" + "=" * 60)
    print("🧪 TEST INSTRUCTIONS")
    print("=" * 60)
    
    print("\n1. Open the services page:")
    print("   http://localhost:8080/services")
    
    if form1_services:
        print(f"\n2. Test Form1 services (should redirect to ?form=form1):")
        for service in form1_services[:3]:  # Show first 3
            print(f"   • Click 'Apply for Service' on '{service.name}'")
    
    if form2_services:
        print(f"\n3. Test Form2 services (should redirect to ?form=form2):")
        for service in form2_services[:3]:  # Show first 3
            print(f"   • Click 'Apply for Service' on '{service.name}'")
    
    print("\n4. Check browser console for routing logs")
    print("5. Verify correct form opens (Form1 has 4 tabs, Form2 has 3 tabs)")

def main():
    """Main function."""
    try:
        verify_service_urls()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
