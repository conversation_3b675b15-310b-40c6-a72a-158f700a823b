import AdminLayout from '@/components/NewAdminLayout';
import { StaffLevelRoute } from '@/components/RoleBasedRoute';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import GraduateReportChart from '@/components/GraduateReportChart';
import OptimizedGraduateDashboard from '@/components/OptimizedGraduateDashboard';
import { BarChart3, PieC<PERSON>, LineChart, LayoutDashboard } from 'lucide-react';

const GraduateReports = () => {
  return (
    <StaffLevelRoute>
      <AdminLayout>
        <div className="w-full max-w-7xl mx-auto">

        <Tabs defaultValue="dashboard">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <LayoutDashboard className="h-4 w-4" />
              <span>Dashboard</span>
            </TabsTrigger>
            <TabsTrigger value="department" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>By Department</span>
            </TabsTrigger>
            <TabsTrigger value="college" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>By College</span>
            </TabsTrigger>
            <TabsTrigger value="gender" className="flex items-center gap-2">
              <PieChart className="h-4 w-4" />
              <span>By Gender</span>
            </TabsTrigger>
            <TabsTrigger value="year" className="flex items-center gap-2">
              <LineChart className="h-4 w-4" />
              <span>By Year</span>
            </TabsTrigger>
            <TabsTrigger value="program" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>By Program</span>
            </TabsTrigger>
            <TabsTrigger value="admission" className="flex items-center gap-2">
              <PieChart className="h-4 w-4" />
              <span>By Admission</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard">
            <div className="mt-6">
              <OptimizedGraduateDashboard />
            </div>
          </TabsContent>

          <TabsContent value="department">
            <GraduateReportChart
              title="Graduates by Department"
              description="Distribution of graduates across different departments"
              reportType="department"
            />
          </TabsContent>

          <TabsContent value="college">
            <GraduateReportChart
              title="Graduates by College"
              description="Distribution of graduates across different colleges"
              reportType="college"
            />
          </TabsContent>

          <TabsContent value="gender">
            <GraduateReportChart
              title="Graduates by Gender"
              description="Gender distribution of graduates"
              reportType="gender"
            />
          </TabsContent>

          <TabsContent value="year">
            <GraduateReportChart
              title="Graduates by Year"
              description="Number of graduates per year"
              reportType="year"
            />
          </TabsContent>

          <TabsContent value="program">
            <GraduateReportChart
              title="Graduates by Program"
              description="Distribution of graduates across different programs"
              reportType="program"
            />
          </TabsContent>

          <TabsContent value="admission">
            <GraduateReportChart
              title="Graduates by Admission Classification"
              description="Distribution of graduates by admission classification"
              reportType="admission"
            />
          </TabsContent>
        </Tabs>

        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>About Graduate Reports</CardTitle>
              <CardDescription>
                Understanding the data and how to use these reports
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                These reports provide valuable insights into the distribution of graduates across various dimensions.
                Use these reports to:
              </p>

              <ul className="list-disc pl-6 space-y-2">
                <li>
                  <strong>Department Analysis:</strong> Identify which departments have the highest number of graduates,
                  helping to understand program popularity and resource allocation needs.
                </li>
                <li>
                  <strong>College Analysis:</strong> See the distribution of graduates across different colleges,
                  providing insights into college performance and graduation rates.
                </li>
                <li>
                  <strong>Gender Distribution:</strong> Analyze gender balance across graduates, which can inform
                  diversity and inclusion initiatives.
                </li>
                <li>
                  <strong>Yearly Trends:</strong> Track graduation rates over time to identify trends and patterns
                  that may require attention or indicate successful program changes.
                </li>
                <li>
                  <strong>Program Distribution:</strong> See which academic programs have the most graduates,
                  helping to identify popular programs and potential areas for expansion.
                </li>
                <li>
                  <strong>Admission Classification:</strong> Understand the distribution of graduates by their
                  admission classification, providing insights into admission pathways.
                </li>
              </ul>

              <p className="text-sm text-muted-foreground mt-4">
                Note: These reports are generated based on the current data in the system. For the most accurate
                analysis, ensure that graduate data is regularly updated and maintained.
              </p>
            </CardContent>
          </Card>
        </div>
        </div>
      </AdminLayout>
    </StaffLevelRoute>
  );
};

export default GraduateReports;
