/**
 * Enhanced Activity Monitor Service
 * Provides comprehensive user activity tracking and session management
 */

interface ActivityEvent {
  type: string;
  timestamp: number;
  path?: string;
  target?: string;
}

interface SessionInfo {
  startTime: number;
  lastActivity: number;
  totalEvents: number;
  sessionId: string;
  isActive: boolean;
}

class ActivityMonitorService {
  private events: ActivityEvent[] = [];
  private sessionInfo: SessionInfo | null = null;
  private activityCallbacks: Array<(event: ActivityEvent) => void> = [];
  private inactivityCallbacks: Array<(duration: number) => void> = [];
  private isMonitoring = false;
  private inactivityTimer: NodeJS.Timeout | null = null;
  private readonly INACTIVITY_THRESHOLD = 15 * 60 * 1000; // 15 minutes
  private readonly MAX_EVENTS = 1000; // Limit stored events

  /**
   * Start monitoring user activity
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.initializeSession();
    this.attachEventListeners();
    this.startInactivityTimer();

    console.log('Activity monitoring started');
  }

  /**
   * Stop monitoring user activity
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    this.detachEventListeners();
    this.clearInactivityTimer();

    if (this.sessionInfo) {
      this.sessionInfo.isActive = false;
    }

    console.log('Activity monitoring stopped');
  }

  /**
   * Initialize a new session
   */
  private initializeSession(): void {
    const now = Date.now();
    this.sessionInfo = {
      startTime: now,
      lastActivity: now,
      totalEvents: 0,
      sessionId: this.generateSessionId(),
      isActive: true
    };

    // Store session info in localStorage
    localStorage.setItem('sessionInfo', JSON.stringify(this.sessionInfo));
    localStorage.setItem('lastActivity', now.toString());
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Attach event listeners for activity tracking
   */
  private attachEventListeners(): void {
    const events = [
      'mousedown',
      'mousemove',
      'keydown',
      'scroll',
      'touchstart',
      'touchmove',
      'click',
      'focus',
      'blur'
    ];

    events.forEach(eventType => {
      document.addEventListener(eventType, this.handleActivity, { passive: true });
    });

    // Track page visibility changes
    document.addEventListener('visibilitychange', this.handleVisibilityChange);

    // Track route changes (for SPAs)
    window.addEventListener('popstate', this.handleRouteChange);
  }

  /**
   * Detach event listeners
   */
  private detachEventListeners(): void {
    const events = [
      'mousedown',
      'mousemove',
      'keydown',
      'scroll',
      'touchstart',
      'touchmove',
      'click',
      'focus',
      'blur'
    ];

    events.forEach(eventType => {
      document.removeEventListener(eventType, this.handleActivity);
    });

    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('popstate', this.handleRouteChange);
  }

  /**
   * Handle user activity events
   */
  private handleActivity = (event: Event): void => {
    if (!this.isMonitoring || !this.sessionInfo) return;

    const now = Date.now();
    const activityEvent: ActivityEvent = {
      type: event.type,
      timestamp: now,
      path: window.location.pathname,
      target: (event.target as Element)?.tagName?.toLowerCase()
    };

    // Update session info
    this.sessionInfo.lastActivity = now;
    this.sessionInfo.totalEvents++;

    // Store activity event
    this.addEvent(activityEvent);

    // Update localStorage
    localStorage.setItem('lastActivity', now.toString());
    localStorage.setItem('sessionInfo', JSON.stringify(this.sessionInfo));

    // Notify callbacks
    this.activityCallbacks.forEach(callback => callback(activityEvent));

    // Reset inactivity timer
    this.resetInactivityTimer();
  };

  /**
   * Handle page visibility changes
   */
  private handleVisibilityChange = (): void => {
    const event: ActivityEvent = {
      type: document.hidden ? 'page_hidden' : 'page_visible',
      timestamp: Date.now(),
      path: window.location.pathname
    };

    this.addEvent(event);

    if (!document.hidden) {
      // Page became visible, reset inactivity timer
      this.resetInactivityTimer();
    }
  };

  /**
   * Handle route changes
   */
  private handleRouteChange = (): void => {
    const event: ActivityEvent = {
      type: 'route_change',
      timestamp: Date.now(),
      path: window.location.pathname
    };

    this.addEvent(event);
    this.resetInactivityTimer();
  };

  /**
   * Add an event to the events array
   */
  private addEvent(event: ActivityEvent): void {
    this.events.push(event);

    // Limit the number of stored events
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(-this.MAX_EVENTS);
    }
  }

  /**
   * Start the inactivity timer
   */
  private startInactivityTimer(): void {
    this.inactivityTimer = setTimeout(() => {
      this.handleInactivity();
    }, this.INACTIVITY_THRESHOLD);
  }

  /**
   * Reset the inactivity timer
   */
  private resetInactivityTimer(): void {
    this.clearInactivityTimer();
    this.startInactivityTimer();
  }

  /**
   * Clear the inactivity timer
   */
  private clearInactivityTimer(): void {
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = null;
    }
  }

  /**
   * Handle inactivity timeout
   */
  private handleInactivity(): void {
    const duration = Date.now() - (this.sessionInfo?.lastActivity || 0);
    
    console.log(`User inactive for ${Math.round(duration / 1000 / 60)} minutes`);
    
    // Notify inactivity callbacks
    this.inactivityCallbacks.forEach(callback => callback(duration));
  }

  /**
   * Register a callback for activity events
   */
  onActivity(callback: (event: ActivityEvent) => void): () => void {
    this.activityCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.activityCallbacks.indexOf(callback);
      if (index > -1) {
        this.activityCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Register a callback for inactivity events
   */
  onInactivity(callback: (duration: number) => void): () => void {
    this.inactivityCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.inactivityCallbacks.indexOf(callback);
      if (index > -1) {
        this.inactivityCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Get current session information
   */
  getSessionInfo(): SessionInfo | null {
    return this.sessionInfo;
  }

  /**
   * Get recent activity events
   */
  getRecentEvents(limit = 50): ActivityEvent[] {
    return this.events.slice(-limit);
  }

  /**
   * Get time since last activity
   */
  getTimeSinceLastActivity(): number {
    if (!this.sessionInfo) return 0;
    return Date.now() - this.sessionInfo.lastActivity;
  }

  /**
   * Check if user is currently active
   */
  isUserActive(): boolean {
    const timeSinceLastActivity = this.getTimeSinceLastActivity();
    return timeSinceLastActivity < 60000; // Active if activity within last minute
  }

  /**
   * Get session duration
   */
  getSessionDuration(): number {
    if (!this.sessionInfo) return 0;
    return Date.now() - this.sessionInfo.startTime;
  }
}

// Create and export a singleton instance
export const activityMonitor = new ActivityMonitorService();
export default activityMonitor;
