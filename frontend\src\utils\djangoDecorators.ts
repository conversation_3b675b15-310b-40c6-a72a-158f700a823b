/**
 * Django-Compatible Decorators and Middleware
 * 
 * Implements Django's authentication decorators and middleware:
 * - @login_required
 * - @staff_member_required  
 * - @permission_required
 * - @user_passes_test
 * - LoginRequiredMixin
 * - PermissionRequiredMixin
 */

import React from 'react';
import { Navigate } from 'react-router-dom';
import { User } from '@/types/auth';
import { createDjangoAuth } from './djangoAuth';

/**
 * Django's @login_required decorator equivalent
 */
export const loginRequired = <P extends object>(
  Component: React.ComponentType<P>,
  redirectTo: string = '/login'
) => {
  return (props: P & { user?: User | null }) => {
    const auth = createDjangoAuth(props.user || null);
    
    if (!auth.isAuthenticated()) {
      return <Navigate to={redirectTo} replace />;
    }
    
    return <Component {...props} />;
  };
};

/**
 * Django's @staff_member_required decorator equivalent
 */
export const staffMemberRequired = <P extends object>(
  Component: React.ComponentType<P>,
  redirectTo: string = '/login'
) => {
  return (props: P & { user?: User | null }) => {
    const auth = createDjangoAuth(props.user || null);
    
    if (!auth.isAuthenticated()) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (!auth.isStaff()) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600">You must be a staff member to access this page.</p>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};

/**
 * Django's @permission_required decorator equivalent
 */
export const permissionRequired = <P extends object>(
  permissions: string | string[],
  Component: React.ComponentType<P>,
  options: {
    redirectTo?: string;
    requireAll?: boolean;
    loginUrl?: string;
    raiseException?: boolean;
  } = {}
) => {
  const {
    redirectTo = '/login',
    requireAll = true,
    loginUrl = '/login',
    raiseException = false
  } = options;

  return (props: P & { user?: User | null }) => {
    const auth = createDjangoAuth(props.user || null);
    
    // Check authentication first
    if (!auth.isAuthenticated()) {
      return <Navigate to={loginUrl} replace />;
    }
    
    // Check permissions
    const permArray = Array.isArray(permissions) ? permissions : [permissions];
    const hasPermission = auth.hasPerms(permArray, requireAll);
    
    if (!hasPermission) {
      if (raiseException) {
        throw new Error(`Permission denied. Required permissions: ${permArray.join(', ')}`);
      }
      
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Permission Denied</h1>
            <p className="text-gray-600 mb-2">You don't have permission to access this page.</p>
            <p className="text-sm text-gray-500">
              Required permissions: {permArray.join(', ')}
            </p>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};

/**
 * Django's @user_passes_test decorator equivalent
 */
export const userPassesTest = <P extends object>(
  testFunc: (user: User | null) => boolean,
  Component: React.ComponentType<P>,
  options: {
    loginUrl?: string;
    redirectTo?: string;
  } = {}
) => {
  const { loginUrl = '/login', redirectTo = '/login' } = options;

  return (props: P & { user?: User | null }) => {
    const user = props.user || null;
    
    if (!user) {
      return <Navigate to={loginUrl} replace />;
    }
    
    if (!testFunc(user)) {
      return <Navigate to={redirectTo} replace />;
    }
    
    return <Component {...props} />;
  };
};

/**
 * Django's @superuser_required decorator equivalent
 */
export const superuserRequired = <P extends object>(
  Component: React.ComponentType<P>,
  redirectTo: string = '/login'
) => {
  return (props: P & { user?: User | null }) => {
    const auth = createDjangoAuth(props.user || null);
    
    if (!auth.isAuthenticated()) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (!auth.isSuperuser()) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600">You must be a superuser to access this page.</p>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};

/**
 * Django's LoginRequiredMixin equivalent
 */
export interface LoginRequiredMixinProps {
  user?: User | null;
  loginUrl?: string;
}

export const withLoginRequired = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P & LoginRequiredMixinProps) => {
    const { user, loginUrl = '/login', ...componentProps } = props;
    const auth = createDjangoAuth(user || null);
    
    if (!auth.isAuthenticated()) {
      return <Navigate to={loginUrl} replace />;
    }
    
    return <Component {...(componentProps as P)} />;
  };
};

/**
 * Django's PermissionRequiredMixin equivalent
 */
export interface PermissionRequiredMixinProps {
  user?: User | null;
  permissions?: string | string[];
  requireAll?: boolean;
  loginUrl?: string;
  permissionDeniedMessage?: string;
}

export const withPermissionRequired = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P & PermissionRequiredMixinProps) => {
    const {
      user,
      permissions = [],
      requireAll = true,
      loginUrl = '/login',
      permissionDeniedMessage = 'You don\'t have permission to access this page.',
      ...componentProps
    } = props;
    
    const auth = createDjangoAuth(user || null);
    
    // Check authentication first
    if (!auth.isAuthenticated()) {
      return <Navigate to={loginUrl} replace />;
    }
    
    // Check permissions if specified
    if (permissions && (Array.isArray(permissions) ? permissions.length > 0 : permissions)) {
      const permArray = Array.isArray(permissions) ? permissions : [permissions];
      const hasPermission = auth.hasPerms(permArray, requireAll);
      
      if (!hasPermission) {
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-red-600 mb-4">Permission Denied</h1>
              <p className="text-gray-600 mb-2">{permissionDeniedMessage}</p>
              <p className="text-sm text-gray-500">
                Required permissions: {permArray.join(', ')}
              </p>
            </div>
          </div>
        );
      }
    }
    
    return <Component {...(componentProps as P)} />;
  };
};

/**
 * Django's @method_decorator equivalent for class-based views
 */
export const methodDecorator = <P extends object>(
  decorator: (Component: React.ComponentType<P>) => React.ComponentType<P>
) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    descriptor.value = decorator(originalMethod);
    return descriptor;
  };
};

/**
 * Utility function to check multiple decorators (like Django's @method_decorator)
 */
export const applyDecorators = <P extends object>(
  Component: React.ComponentType<P>,
  ...decorators: Array<(Component: React.ComponentType<P>) => React.ComponentType<P>>
) => {
  return decorators.reduce((DecoratedComponent, decorator) => {
    return decorator(DecoratedComponent);
  }, Component);
};

/**
 * Django-style group required decorator
 */
export const groupRequired = <P extends object>(
  groups: string | string[],
  Component: React.ComponentType<P>,
  options: {
    requireAll?: boolean;
    loginUrl?: string;
  } = {}
) => {
  const { requireAll = false, loginUrl = '/login' } = options;

  return (props: P & { user?: User | null }) => {
    const auth = createDjangoAuth(props.user || null);
    
    if (!auth.isAuthenticated()) {
      return <Navigate to={loginUrl} replace />;
    }
    
    const groupArray = Array.isArray(groups) ? groups : [groups];
    const userGroups = auth.getGroups().map(g => g.name);
    
    const hasGroup = requireAll
      ? groupArray.every(group => userGroups.includes(group))
      : groupArray.some(group => userGroups.includes(group));
    
    if (!hasGroup) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-2">You must be a member of the required group(s).</p>
            <p className="text-sm text-gray-500">
              Required groups: {groupArray.join(', ')}
            </p>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
};
