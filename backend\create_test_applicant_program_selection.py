#!/usr/bin/env python
"""
Create test ApplicantProgramSelection with year foreign key
"""

import os
import sys
import django
from django.utils import timezone
from datetime import timedelta

# Add the backend directory to the Python path
backend_path = os.path.dirname(__file__)
sys.path.insert(0, backend_path)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from registration.models import ApplicantProgramSelection
from registration.serializers import ApplicantProgramSelectionSerializer
from setups.year.models import Year
from setups.application_information.models import ApplicationInformation
from django.contrib.auth.models import User

def create_test_applicant_program_selection():
    """Create test ApplicantProgramSelection with year foreign key"""
    try:
        print("=== Creating Test ApplicantProgramSelection ===")
        
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        print(f"User: {user.username} ({'created' if created else 'exists'})")
        
        # Get the year
        year = Year.objects.first()
        if not year:
            print("❌ No year found in database")
            return
        
        print(f"Year: {year.year} (UUID: {year.uuid})")
        
        # Get an application info (if any exists)
        app_info = ApplicationInformation.objects.first()
        if not app_info:
            print("❌ No application information found in database")
            return
        
        print(f"Application Info: {app_info.id}")
        
        # Create ApplicantProgramSelection
        program_selection, created = ApplicantProgramSelection.objects.get_or_create(
            user=user,
            application_info=app_info,
            year=year,
            defaults={
                'sponsorship': 'Self'
            }
        )
        print(f"Program Selection: {program_selection} ({'created' if created else 'exists'})")
        
        # Test the serializer
        serializer = ApplicantProgramSelectionSerializer(program_selection)
        print(f"\nSerialized data: {serializer.data}")
        
        print("\n✅ Test ApplicantProgramSelection created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_test_applicant_program_selection()
