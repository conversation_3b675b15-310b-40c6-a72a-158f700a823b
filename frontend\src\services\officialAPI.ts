import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// Types for Official certificates
export interface OfficialSent {
  id: number;
  first_name: string;
  second_name: string;
  last_name: string;
  full_name: string;
  gender: 'M' | 'F';
  gender_display: string;
  receiver_institute: string;
  send_date: string;
  courier: string;
  certificate_type: string;
  certificate_type_details: {
    uuid: string;
    name: string;
    description: string;
    is_active: boolean;
  };
  certificate_type_name: string;
  tracking_number: string;
}

export interface OfficialReceived {
  id: number;
  first_name: string;
  second_name: string;
  last_name: string;
  full_name: string;
  gender: 'M' | 'F';
  gender_display: string;
  sender_institute: string;
  arival_date: string;
  courier: string;
  certificate_type: string;
  certificate_type_details: {
    uuid: string;
    name: string;
    description: string;
    is_active: boolean;
  };
  certificate_type_name: string;
  tracking_number: string;
}

export interface OfficialSentCreateUpdate {
  first_name: string;
  second_name: string;
  last_name: string;
  gender: 'M' | 'F';
  receiver_institute: string;
  send_date: string;
  courier: string;
  certificate_type: string;
  tracking_number: string;
}

export interface OfficialReceivedCreateUpdate {
  first_name: string;
  second_name: string;
  last_name: string;
  gender: 'M' | 'F';
  sender_institute: string;
  arival_date: string;
  courier: string;
  certificate_type: string;
  tracking_number: string;
}

export interface OfficialStatistics {
  sent: {
    total: number;
    by_certificate_type: Array<{ certificate_type__name: string; count: number }>;
    by_gender: Array<{ gender: string; count: number }>;
  };
  received: {
    total: number;
    by_certificate_type: Array<{ certificate_type__name: string; count: number }>;
    by_gender: Array<{ gender: string; count: number }>;
  };
}

export interface TrackingSearchResult {
  tracking_number: string;
  sent: OfficialSent | null;
  received: OfficialReceived | null;
}

// Get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };
};

// Official Sent API functions
export const officialSentAPI = {
  // Get all sent certificates
  getAll: (params?: any) => {
    return axios.get(`${API_BASE_URL}/official/sent/`, {
      ...getAuthHeaders(),
      params,
    });
  },

  // Get sent certificate by ID
  getById: (id: number) => {
    return axios.get(`${API_BASE_URL}/official/sent/${id}/`, getAuthHeaders());
  },

  // Create new sent certificate
  create: (data: OfficialSentCreateUpdate) => {
    return axios.post(`${API_BASE_URL}/official/sent/`, data, getAuthHeaders());
  },

  // Update sent certificate
  update: (id: number, data: OfficialSentCreateUpdate) => {
    return axios.put(`${API_BASE_URL}/official/sent/${id}/`, data, getAuthHeaders());
  },

  // Delete sent certificate
  delete: (id: number) => {
    return axios.delete(`${API_BASE_URL}/official/sent/${id}/`, getAuthHeaders());
  },
};

// Official Received API functions
export const officialReceivedAPI = {
  // Get all received certificates
  getAll: (params?: any) => {
    return axios.get(`${API_BASE_URL}/official/received/`, {
      ...getAuthHeaders(),
      params,
    });
  },

  // Get received certificate by ID
  getById: (id: number) => {
    return axios.get(`${API_BASE_URL}/official/received/${id}/`, getAuthHeaders());
  },

  // Create new received certificate
  create: (data: OfficialReceivedCreateUpdate) => {
    return axios.post(`${API_BASE_URL}/official/received/`, data, getAuthHeaders());
  },

  // Update received certificate
  update: (id: number, data: OfficialReceivedCreateUpdate) => {
    return axios.put(`${API_BASE_URL}/official/received/${id}/`, data, getAuthHeaders());
  },

  // Delete received certificate
  delete: (id: number) => {
    return axios.delete(`${API_BASE_URL}/official/received/${id}/`, getAuthHeaders());
  },
};

// General Official API functions
export const officialAPI = {
  // Get statistics
  getStatistics: () => {
    return axios.get(`${API_BASE_URL}/official/statistics/`, getAuthHeaders());
  },

  // Search by tracking number
  searchByTrackingNumber: (trackingNumber: string) => {
    return axios.get(`${API_BASE_URL}/official/search/`, {
      ...getAuthHeaders(),
      params: { tracking_number: trackingNumber },
    });
  },
};

export default {
  sent: officialSentAPI,
  received: officialReceivedAPI,
  general: officialAPI,
};
