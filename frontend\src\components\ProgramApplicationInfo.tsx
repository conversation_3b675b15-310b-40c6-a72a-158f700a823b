import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import programAPI, { ApplicationInfo } from '@/services/programService';

interface ProgramApplicationInfoProps {
  programId: number;
}

interface ProgramApplicationResponse {
  application_info: ApplicationInfo[];
  registration_active: boolean;
  registration_message: string;
  registration_period: {
    id: number;
    program: number;
    program_name: string;
    open_date: string;
    close_date: string;
    is_active: boolean;
  } | null;
}

const ProgramApplicationInfo: React.FC<ProgramApplicationInfoProps> = ({ programId }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<ProgramApplicationResponse | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!programId) {
        setError('No program selected');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await programAPI.getProgramApplication(programId);
        setData(response);
      } catch (err) {
        console.error('Error fetching program application info:', err);
        setError('Failed to load program information');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [programId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-[#1a73c0]" />
        <span className="ml-2">Loading program information...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!data || !data.application_info || data.application_info.length === 0) {
    return (
      <Alert className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No Information Available</AlertTitle>
        <AlertDescription>
          No application information found for this program.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Registration Status */}
      <Card>
        <CardHeader>
          <CardTitle>Registration Status</CardTitle>
          <CardDescription>Current status of registration for this program</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert
            variant={data.registration_active ? "default" : "destructive"}
            className="mb-4"
          >
            {data.registration_active ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <AlertTitle>
              {data.registration_active ? "Registration Open" : "Registration Closed"}
            </AlertTitle>
            <AlertDescription>{data.registration_message}</AlertDescription>
          </Alert>

          {data.registration_period && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">Registration Period Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Program</p>
                  <p>{data.registration_period.program_name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <p>{data.registration_period.is_active ? 'Active' : 'Inactive'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Opens</p>
                  <p>{new Date(data.registration_period.open_date).toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Closes</p>
                  <p>{new Date(data.registration_period.close_date).toLocaleString()}</p>
                </div>
              </div>
            </div>
          )}

          {data.registration_active && (
            <div className="mt-6">
              <Link to="/application">
                <Button className="bg-[#1a73c0] hover:bg-[#0e4a7d]">
                  Apply Now
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Application Information - Only show if registration is active */}
      {data.registration_active && data.application_info.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Program Details</CardTitle>
            <CardDescription>Information about this program</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>College</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Field of Study</TableHead>
                  <TableHead>Study Program</TableHead>
                  <TableHead>Admission Type</TableHead>
                  <TableHead>Fee</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.application_info.map((info) => (
                  <TableRow key={info.id}>
                    <TableCell>{info.college.name}</TableCell>
                    <TableCell>{info.department.name}</TableCell>
                    <TableCell>{info.field_of_study.name}</TableCell>
                    <TableCell>{info.study_program.name}</TableCell>
                    <TableCell>{info.admission_type.name}</TableCell>
                    <TableCell>
                      {info.program.registration_fee
                        ? `$${Number(info.program.registration_fee).toFixed(2)}`
                        : 'N/A'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProgramApplicationInfo;
