import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Lock, AlertTriangle } from 'lucide-react';
import { useSimpleRBAC } from '@/contexts/SimpleRBACContext';
import DjangoAuthManagement from '@/components/auth/DjangoAuthManagement';

const SimpleDjangoAuthManagement: React.FC = () => {
  const { isSuperuser, isStaff } = useSimpleRBAC();

  // Check if user has access to authentication management
  if (!isSuperuser && !isStaff) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lock className="h-5 w-5" />
              <span>Access Denied</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              You don't have permission to access the authentication management system.
              Please contact your administrator.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Simple Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Django Authentication & Authorization</h1>
          <p className="text-muted-foreground">
            Simple and practical user and permission management using Django's built-in auth system
          </p>
          {!isSuperuser && isStaff && (
            <div className="mt-2">
              <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Staff Access - Some features may be limited
              </Badge>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isSuperuser ? "default" : "secondary"} className={isSuperuser ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}>
            <Shield className="h-3 w-3 mr-1" />
            {isSuperuser ? "Superuser Access" : "Staff Access"}
          </Badge>
        </div>
      </div>

      {/* Main Django Auth Management Component */}
      <DjangoAuthManagement />
    </div>
  );
};

export default SimpleDjangoAuthManagement;
