#!/usr/bin/env python
"""
Simple script to set organization logo
"""

import os
import sys
import django
from django.core.files.base import ContentFile
import requests

# Add the backend directory to the Python path
backend_path = os.path.dirname(__file__)
sys.path.insert(0, backend_path)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from settings_manager.models import OrganizationSetting

def set_default_logo():
    """Set a default logo for the organization"""
    try:
        # Get organization settings
        settings = OrganizationSetting.get_settings()
        print(f"Current organization: {settings.system_name}")
        print(f"Current header logo: {settings.header_logo}")
        
        if settings.header_logo:
            print(f"Header logo already exists: {settings.header_logo.url}")
            return
        
        # Download the University of Gondar logo
        logo_url = "https://res.cloudinary.com/dtlkgxx8l/image/upload/v1744978060/gondar-logo_uhm6rl.png"
        print(f"Downloading logo from: {logo_url}")
        
        try:
            response = requests.get(logo_url, timeout=30)
            response.raise_for_status()
            logo_content = response.content
            filename = 'gondar-logo.png'
            print("Logo downloaded successfully")
        except Exception as e:
            print(f"Failed to download logo: {e}")
            # Use a simple fallback - create a placeholder
            print("Creating a simple placeholder logo...")
            # For now, just skip the logo setting
            print("Skipping logo setting - you can upload one manually via Django admin")
            return
        
        # Save the logo
        settings.header_logo.save(
            filename,
            ContentFile(logo_content),
            save=True
        )
        
        print(f"✅ Header logo set successfully: {settings.header_logo.url}")
        
        # Also set footer logo
        if not settings.footer_logo:
            settings.footer_logo.save(
                f"footer_{filename}",
                ContentFile(logo_content),
                save=True
            )
            print(f"✅ Footer logo also set: {settings.footer_logo.url}")
        
        print("✅ Logo setup completed!")
        
    except Exception as e:
        print(f"❌ Error setting logo: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    set_default_logo()
