import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { Plus, Pencil, Trash2, Search, Calendar, RefreshCw, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import { termAPI } from '../services/api';

// Interfaces
interface Term {
  id: string; // UUID
  name: string;
  description?: string;
  updated_at: string;
}

interface TermFormData {
  name: string;
  description: string;
}

const TermManagement: React.FC = () => {
  // State management
  const [terms, setTerms] = useState<Term[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingTerm, setEditingTerm] = useState<Term | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Form state
  const [formData, setFormData] = useState<TermFormData>({
    name: '',
    description: ''
  });

  // Fetch terms
  const fetchTerms = async () => {
    try {
      setLoading(true);
      const response = await termAPI.getTerms();

      // Handle different response structures
      let termsData = [];
      let totalCount = 0;

      if (response.data) {
        if (Array.isArray(response.data)) {
          // Direct array response
          termsData = response.data;
          totalCount = response.data.length;
        } else if (response.data.results && Array.isArray(response.data.results)) {
          // Paginated response
          termsData = response.data.results;
          totalCount = response.data.count || response.data.results.length;
        } else if (response.data.count !== undefined) {
          // Response with count but no results array
          termsData = [];
          totalCount = response.data.count;
        }
      }

      setTerms(termsData);
      setTotalCount(totalCount);
    } catch (error) {
      console.error('Error fetching terms:', error);
      toast.error('Failed to fetch terms');
    } finally {
      setLoading(false);
    }
  };

  // Filter terms based on search term
  const filteredTerms = terms.filter(term =>
    term.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (term.description && term.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredTerms.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredTerms.length / itemsPerPage);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Load terms on component mount
  useEffect(() => {
    fetchTerms();
  }, []);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };



  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: ''
    });
  };

  // Handle add term
  const handleAddTerm = async () => {
    try {
      const submitData = {
        ...formData,
        description: formData.description.trim() || null // Convert empty string to null
      };

      await termAPI.createTerm(submitData);
      toast.success('Term created successfully');
      setIsAddModalOpen(false);
      resetForm();
      fetchTerms();
    } catch (error: any) {
      console.error('Error creating term:', error);
      const errorMessage = error.response?.data?.name?.[0] || 
                          error.response?.data?.error || 
                          'Failed to create term';
      toast.error(errorMessage);
    }
  };

  // Handle edit term
  const handleEditTerm = async () => {
    if (!editingTerm) return;

    try {
      const submitData = {
        ...formData,
        description: formData.description.trim() || null // Convert empty string to null
      };

      await termAPI.updateTerm(editingTerm.id, submitData);
      toast.success('Term updated successfully');
      setIsEditModalOpen(false);
      setEditingTerm(null);
      resetForm();
      fetchTerms();
    } catch (error: any) {
      console.error('Error updating term:', error);
      const errorMessage = error.response?.data?.name?.[0] || 
                          error.response?.data?.error || 
                          'Failed to update term';
      toast.error(errorMessage);
    }
  };

  // Handle delete term
  const handleDeleteTerm = async (term: Term) => {
    if (!confirm(`Are you sure you want to delete "${term.name}"?`)) return;

    try {
      await termAPI.deleteTerm(term.id);
      toast.success('Term deleted successfully');
      fetchTerms();
    } catch (error: any) {
      console.error('Error deleting term:', error);
      const errorMessage = error.response?.data?.error || 'Failed to delete term';
      toast.error(errorMessage);
    }
  };



  // Open edit modal
  const openEditModal = (term: Term) => {
    setEditingTerm(term);
    setFormData({
      name: term.name,
      description: term.description || ''
    });
    setIsEditModalOpen(true);
  };





  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Term Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete academic terms and sessions
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={fetchTerms}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 transition-all duration-200"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Term
                  </Button>
                </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Add New Term</DialogTitle>
                  <DialogDescription className="text-gray-600 leading-relaxed">
                    Create a new academic term or session. Fill in the required information below.
                  </DialogDescription>
                </div>
              </div>
            </DialogHeader>
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="add-name" className="text-sm font-medium text-gray-700">
                    Term Name *
                  </Label>
                  <Input
                    id="add-name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter term name (e.g., Fall Semester 2024)"
                    className="border-blue-200 focus:ring-blue-400 focus:border-blue-400 transition-all"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="add-description" className="text-sm font-medium text-gray-700">
                    Description
                  </Label>
                  <Textarea
                    id="add-description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter term description (optional)"
                    className="border-blue-200 focus:ring-blue-400 focus:border-blue-400 transition-all"
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsAddModalOpen(false);
                    resetForm();
                  }}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleAddTerm}
                  className="bg-[#1a73c0] hover:bg-blue-700 text-white transition-all duration-200"
                  disabled={!formData.name.trim()}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Term
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Search & Filter Section */}
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
            <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Search & Filter
            </h3>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                <Input
                  placeholder="Search terms by name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Terms Table */}
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[35%] text-[#1a73c0] font-medium">Term Name</TableHead>
                    <TableHead className="w-[45%] text-[#1a73c0] font-medium">Description</TableHead>
                    <TableHead className="w-[20%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0]"></div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">Loading terms...</p>
                            <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : !Array.isArray(currentItems) || currentItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="p-4 bg-blue-50 rounded-full">
                            <Calendar className="h-8 w-8 text-blue-400" />
                          </div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">No terms found</p>
                            <p className="text-sm text-gray-500">
                              {searchTerm ? 'Try adjusting your search criteria' : 'Get started by adding your first term'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    Array.isArray(currentItems) && currentItems.map((term) => (
                      <TableRow key={term.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{term.name}</TableCell>
                        <TableCell>
                          {term.description ? (
                            <span className="text-gray-700">{term.description}</span>
                          ) : (
                            <span className="text-gray-400 italic">No description provided</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditModal(term)}
                              title="Edit"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteTerm(term)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredTerms.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredTerms.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredTerms.length}</span> records
                </div>
              </div>

              {/* Pagination Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="First page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="Previous page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Dynamic page number buttons */}
                <div className="flex gap-1">
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant={currentPage === number ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={`w-8 h-8 ${currentPage === number ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show smart pagination
                    <>
                      {/* Always show first page */}
                      <Button
                        variant={currentPage === 1 ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={`w-8 h-8 ${currentPage === 1 ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {currentPage > 4 && (
                        <Button
                          variant="outline"
                          size="sm"
                          disabled
                          className="w-8 h-8 border-blue-200"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number => number >= Math.max(2, currentPage - 1) && number <= Math.min(totalPages - 1, currentPage + 1))
                        .map(number => (
                          <Button
                            key={number}
                            variant={currentPage === number ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={`w-8 h-8 ${currentPage === number ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {currentPage < totalPages - 3 && (
                        <Button
                          variant="outline"
                          size="sm"
                          disabled
                          className="w-8 h-8 border-blue-200"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant={currentPage === totalPages ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={`w-8 h-8 ${currentPage === totalPages ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="Next page"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="Last page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Pencil className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Edit Term</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Update the term information below. Make sure to save your changes when finished.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <div className="space-y-2">
                <Label htmlFor="edit-name" className="text-sm font-medium text-gray-700">
                  Term Name *
                </Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter term name (e.g., Fall Semester 2024)"
                  className="border-blue-200 focus:ring-blue-400 focus:border-blue-400 transition-all"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description" className="text-sm font-medium text-gray-700">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter term description (optional)"
                  className="border-blue-200 focus:ring-blue-400 focus:border-blue-400 transition-all"
                  rows={3}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditModalOpen(false);
                  setEditingTerm(null);
                  resetForm();
                }}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
              <Button
                onClick={handleEditTerm}
                className="bg-[#1a73c0] hover:bg-blue-700 text-white transition-all duration-200"
                disabled={!formData.name.trim()}
              >
                <Pencil className="h-4 w-4 mr-2" />
                Update Term
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TermManagement;
