# Migration to convert certificate_type foreign keys from bigint to UUID

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('certificate_type', '0001_initial'),
        ('official', '0003_alter_officialreceived_options_and_more'),
    ]

    operations = [
        # Step 1: Add temporary UUID columns
        migrations.RunSQL(
            "ALTER TABLE official_officialsent ADD COLUMN certificate_type_uuid_temp UUID;",
            reverse_sql="ALTER TABLE official_officialsent DROP COLUMN certificate_type_uuid_temp;"
        ),
        migrations.RunSQL(
            "ALTER TABLE official_officialreceived ADD COLUMN certificate_type_uuid_temp UUID;",
            reverse_sql="ALTER TABLE official_officialreceived DROP COLUMN certificate_type_uuid_temp;"
        ),

        # Step 2: Copy UUID values from certificate_type table to temporary columns
        # Map integer IDs to corresponding UUIDs
        migrations.RunSQL(
            """
            UPDATE official_officialsent
            SET certificate_type_uuid_temp = ct.uuid
            FROM certificate_type_certificatetype ct
            WHERE ct.uuid IS NOT NULL
            AND official_officialsent.certificate_type_id IS NOT NULL;
            """,
            reverse_sql="UPDATE official_officialsent SET certificate_type_uuid_temp = NULL;"
        ),
        migrations.RunSQL(
            """
            UPDATE official_officialreceived
            SET certificate_type_uuid_temp = ct.uuid
            FROM certificate_type_certificatetype ct
            WHERE ct.uuid IS NOT NULL
            AND official_officialreceived.certificate_type_id IS NOT NULL;
            """,
            reverse_sql="UPDATE official_officialreceived SET certificate_type_uuid_temp = NULL;"
        ),

        # Step 3: Drop old foreign key constraints and columns
        migrations.RunSQL(
            "ALTER TABLE official_officialsent DROP CONSTRAINT IF EXISTS official_officialsent_certificate_type_id_fkey;",
            reverse_sql="-- Cannot reverse constraint drop"
        ),
        migrations.RunSQL(
            "ALTER TABLE official_officialreceived DROP CONSTRAINT IF EXISTS official_officialreceived_certificate_type_id_fkey;",
            reverse_sql="-- Cannot reverse constraint drop"
        ),
        migrations.RunSQL(
            "ALTER TABLE official_officialsent DROP COLUMN certificate_type_id;",
            reverse_sql="ALTER TABLE official_officialsent ADD COLUMN certificate_type_id BIGINT;"
        ),
        migrations.RunSQL(
            "ALTER TABLE official_officialreceived DROP COLUMN certificate_type_id;",
            reverse_sql="ALTER TABLE official_officialreceived ADD COLUMN certificate_type_id BIGINT;"
        ),

        # Step 4: Rename temporary columns to final names
        migrations.RunSQL(
            "ALTER TABLE official_officialsent RENAME COLUMN certificate_type_uuid_temp TO certificate_type_id;",
            reverse_sql="ALTER TABLE official_officialsent RENAME COLUMN certificate_type_id TO certificate_type_uuid_temp;"
        ),
        migrations.RunSQL(
            "ALTER TABLE official_officialreceived RENAME COLUMN certificate_type_uuid_temp TO certificate_type_id;",
            reverse_sql="ALTER TABLE official_officialreceived RENAME COLUMN certificate_type_id TO certificate_type_uuid_temp;"
        ),

        # Step 5: Add foreign key constraints pointing to UUID field
        migrations.RunSQL(
            """
            ALTER TABLE official_officialsent
            ADD CONSTRAINT official_officialsent_certificate_type_id_fkey
            FOREIGN KEY (certificate_type_id)
            REFERENCES certificate_type_certificatetype(uuid)
            ON DELETE RESTRICT;
            """,
            reverse_sql="ALTER TABLE official_officialsent DROP CONSTRAINT official_officialsent_certificate_type_id_fkey;"
        ),
        migrations.RunSQL(
            """
            ALTER TABLE official_officialreceived
            ADD CONSTRAINT official_officialreceived_certificate_type_id_fkey
            FOREIGN KEY (certificate_type_id)
            REFERENCES certificate_type_certificatetype(uuid)
            ON DELETE RESTRICT;
            """,
            reverse_sql="ALTER TABLE official_officialreceived DROP CONSTRAINT official_officialreceived_certificate_type_id_fkey;"
        ),

        # Step 6: Make the columns NOT NULL
        migrations.RunSQL(
            "ALTER TABLE official_officialsent ALTER COLUMN certificate_type_id SET NOT NULL;",
            reverse_sql="ALTER TABLE official_officialsent ALTER COLUMN certificate_type_id DROP NOT NULL;"
        ),
        migrations.RunSQL(
            "ALTER TABLE official_officialreceived ALTER COLUMN certificate_type_id SET NOT NULL;",
            reverse_sql="ALTER TABLE official_officialreceived ALTER COLUMN certificate_type_id DROP NOT NULL;"
        ),
    ]
