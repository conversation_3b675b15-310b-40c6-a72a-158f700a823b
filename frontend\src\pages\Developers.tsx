import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Github, 
  Linkedin, 
  Mail, 
  Code, 
  Database, 
  Globe, 
  Users, 
  Award,
  BookOpen,
  Coffee,
  Heart
} from 'lucide-react';
import HeaderStyleFooter from '@/components/HeaderStyleFooter';

const Developers: React.FC = () => {
  const developers = [
    {
      id: 1,
      name: 'Meseret Teshale',
      role: 'Full Stack Developer',
      specialization: 'Frontend Architecture & UI/UX',
      description: 'Specialized in React development, user interface design, and creating seamless user experiences. Passionate about clean code and modern web technologies.',
      skills: ['React', 'TypeScript', 'Tailwind CSS', 'UI/UX Design', 'Frontend Architecture'],
      avatar: '/api/placeholder/150/150',
      email: '<EMAIL>',
      github: 'https://github.com/meseretteshale',
      linkedin: 'https://linkedin.com/in/meseretteshale'
    },
    {
      id: 2,
      name: 'Tewodros Abebaw',
      role: 'Backend Developer',
      specialization: 'System Architecture & Database Design',
      description: 'Expert in Django development, database optimization, and building scalable backend systems. Focused on performance and security best practices.',
      skills: ['Django', 'Python', 'PostgreSQL', 'REST APIs', 'System Architecture'],
      avatar: '/api/placeholder/150/150',
      email: '<EMAIL>',
      github: 'https://github.com/tewodrosabebaw',
      linkedin: 'https://linkedin.com/in/tewodrosabebaw'
    },
    {
      id: 3,
      name: 'Aragaw Mebratu',
      role: 'DevOps Engineer',
      specialization: 'Infrastructure & Deployment',
      description: 'Specialized in cloud infrastructure, CI/CD pipelines, and ensuring reliable deployment processes. Passionate about automation and system reliability.',
      skills: ['DevOps', 'Docker', 'CI/CD', 'Cloud Infrastructure', 'System Administration'],
      avatar: '/api/placeholder/150/150',
      email: '<EMAIL>',
      github: 'https://github.com/aragawmebratu',
      linkedin: 'https://linkedin.com/in/aragawmebratu'
    }
  ];

  const technologies = [
    {
      category: 'Frontend',
      icon: <Globe className="h-6 w-6" />,
      items: ['React 18', 'TypeScript', 'Tailwind CSS', 'Vite', 'React Router', 'React Query']
    },
    {
      category: 'Backend',
      icon: <Database className="h-6 w-6" />,
      items: ['Django 5.2', 'Python 3.11', 'PostgreSQL', 'Redis', 'Django REST Framework']
    },
    {
      category: 'Development',
      icon: <Code className="h-6 w-6" />,
      items: ['Git', 'Docker', 'VS Code', 'Postman', 'ESLint', 'Prettier']
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-white/10 p-4 rounded-full">
                <Users className="h-12 w-12" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Meet Our Development Team
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              The talented developers behind the University of Gondar Graduate Management System
            </p>
            <div className="flex items-center justify-center gap-2 text-blue-100">
              <Heart className="h-5 w-5 text-red-400" />
              <span>Built with passion and dedication</span>
            </div>
          </div>
        </div>
      </div>

      {/* Team Overview */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Development Team</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            A dedicated team of three skilled developers working together to create innovative solutions 
            for graduate program management at the University of Gondar.
          </p>
        </div>

        {/* Developer Profiles */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {developers.map((developer) => (
            <Card key={developer.id} className="hover:shadow-lg transition-shadow duration-300">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4">
                  <div className="w-32 h-32 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                    {developer.name.split(' ').map(n => n[0]).join('')}
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">
                  {developer.name}
                </CardTitle>
                <CardDescription className="text-blue-600 font-medium">
                  {developer.role}
                </CardDescription>
                <Badge variant="outline" className="mx-auto">
                  {developer.specialization}
                </Badge>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 text-center">
                  {developer.description}
                </p>
                
                {/* Skills */}
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Skills</h4>
                  <div className="flex flex-wrap gap-1">
                    {developer.skills.map((skill, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Contact Links */}
                <div className="flex justify-center gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href={`mailto:${developer.email}`} className="flex items-center gap-1">
                      <Mail className="h-4 w-4" />
                      Email
                    </a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href={developer.github} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1">
                      <Github className="h-4 w-4" />
                      GitHub
                    </a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href={developer.linkedin} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1">
                      <Linkedin className="h-4 w-4" />
                      LinkedIn
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Technologies Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Technologies & Tools</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Modern technologies and frameworks powering the graduate management system
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {technologies.map((tech, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    <div className="bg-blue-100 p-3 rounded-full text-blue-600">
                      {tech.icon}
                    </div>
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-900">
                    {tech.category}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {tech.items.map((item, itemIndex) => (
                      <Badge key={itemIndex} variant="outline" className="mr-2 mb-2">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Project Information */}
        <div className="mb-16">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold text-gray-900 mb-4">
                About This Project
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-blue-600" />
                    Project Overview
                  </h3>
                  <p className="text-gray-600 mb-4">
                    The University of Gondar Graduate Management System is a comprehensive web application
                    designed to streamline graduate program administration, application processing, and
                    student management processes.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-gray-700">Modern, responsive design</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-gray-700">Role-based access control</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-gray-700">Comprehensive application management</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-gray-700">Alumni services integration</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Coffee className="h-5 w-5 text-blue-600" />
                    Development Approach
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Built with modern web technologies and best practices, focusing on performance,
                    security, and user experience. The system follows agile development methodologies
                    and maintains high code quality standards.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-gray-700">Clean, maintainable code</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-gray-700">Comprehensive testing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-gray-700">Security-first design</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-gray-700">Scalable architecture</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact Section */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-gray-900">
                Get In Touch
              </CardTitle>
              <CardDescription>
                Have questions about the system or want to collaborate? We'd love to hear from you!
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild className="bg-blue-600 hover:bg-blue-700">
                  <a href="mailto:<EMAIL>" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Contact Team
                  </a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/graduate-admin" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Admin Portal
                  </a>
                </Button>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                University of Gondar - Graduate School Management System
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <HeaderStyleFooter />
    </div>
  );
};

export default Developers;
