import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Plus, 
  Search, 
  RefreshCw, 
  Eye,
  FileText,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

import {
  ServiceRequest,
  ServiceRequestListResponse,
  ServiceRequestFilters,
  STATUS_OPTIONS
} from '@/types/serviceRequest';

import {
  serviceRequestAPI,
  serviceRequestUtils
} from '@/services/serviceRequestAPI';

import ServiceRequestForm from './ServiceRequestForm';

const ServiceRequestList: React.FC = () => {
  // State management
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);

  // Load service requests
  const fetchServiceRequests = async () => {
    setLoading(true);
    try {
      const filters: ServiceRequestFilters = {
        name: searchTerm || undefined,
        status: (statusFilter && statusFilter !== 'all') ? statusFilter : undefined,
      };

      const response = await serviceRequestAPI.getAll(filters);
      setServiceRequests(response.results);
    } catch (error) {
      console.error('Error fetching service requests:', error);
      toast.error('Failed to load service requests');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchServiceRequests();
  }, [searchTerm, statusFilter]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = STATUS_OPTIONS.find(s => s.value === status);
    if (!statusConfig) return null;

    return (
      <Badge
        variant="outline"
        className={cn(
          "flex items-center space-x-1",
          status === 'pending' && "border-orange-200 text-orange-700 bg-orange-50",
          status === 'processing' && "border-blue-200 text-blue-700 bg-blue-50",
          status === 'completed' && "border-green-200 text-green-700 bg-green-50",
          status === 'rejected' && "border-red-200 text-red-700 bg-red-50"
        )}
      >
        {getStatusIcon(status)}
        <span>{statusConfig.label}</span>
      </Badge>
    );
  };

  if (loading && serviceRequests.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0] mx-auto mb-4"></div>
            <div className="text-center">
              <p className="text-gray-600 font-medium">Loading service requests...</p>
              <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Content Card */}
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">My Service Requests</CardTitle>
                <CardDescription className="mt-1">
                  View and track your service requests
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={fetchServiceRequests}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 transition-all duration-200"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                onClick={() => setIsFormOpen(true)}
                className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Request
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          {/* Search & Filter Section */}
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
            <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Search & Filter
            </h3>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                <Input
                  placeholder="Search by service type or status..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="border-blue-200 focus:ring-blue-400 shadow-sm">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {STATUS_OPTIONS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Service Requests Table */}
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="text-[#1a73c0] font-medium">Service Type</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Status</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Degree</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Created</TableHead>
                    <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0]"></div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">Loading service requests...</p>
                            <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : !Array.isArray(serviceRequests) || serviceRequests.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="p-4 bg-blue-50 rounded-full">
                            <FileText className="h-8 w-8 text-blue-400" />
                          </div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">No service requests found</p>
                            <p className="text-sm text-gray-500">
                              {searchTerm || statusFilter ? 'Try adjusting your search criteria' : 'Get started by creating your first service request'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    Array.isArray(serviceRequests) && serviceRequests.map((request) => (
                      <TableRow key={request.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell>
                          <div>
                            <p className="font-medium text-[#1a73c0]">{request.service_type_name}</p>
                            <p className="text-sm text-gray-500">{request.college_display}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">{request.degree_name}</p>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1 text-sm text-gray-500">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(request.created_at).toLocaleDateString()}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            title="View Details"
                            className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Service Request Form Dialog */}
      <ServiceRequestForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSuccess={fetchServiceRequests}
        mode="create"
      />
    </div>
  );
};

export default ServiceRequestList;
