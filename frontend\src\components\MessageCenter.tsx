import React, { useState } from 'react';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Mail, MessageSquare, Megaphone, Send } from 'lucide-react';
import communicationAPI from '@/services/communicationAPI';

const MessageCenter = () => {
  const [activeTab, setActiveTab] = useState('email');
  const [isLoading, setIsLoading] = useState(false);
  
  // Email form state
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [emailRecipients, setEmailRecipients] = useState('');
  
  // SMS form state
  const [smsMessage, setSmsMessage] = useState('');
  const [smsRecipients, setSmsRecipients] = useState('');
  
  // Announcement form state
  const [announcementTitle, setAnnouncementTitle] = useState('');
  const [announcementContent, setAnnouncementContent] = useState('');
  const [announcementPriority, setAnnouncementPriority] = useState('medium');
  const [announcementAudience, setAnnouncementAudience] = useState('all');

  // Handle email form submission
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!emailSubject || !emailContent || !emailRecipients) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    setIsLoading(true);
    try {
      await communicationAPI.createEmailNotification({
        subject: emailSubject,
        content: emailContent,
        recipients: emailRecipients,
        status: 'draft'
      });
      
      toast.success('Email notification created successfully');
      
      // Reset form
      setEmailSubject('');
      setEmailContent('');
      setEmailRecipients('');
    } catch (error) {
      console.error('Error creating email notification:', error);
      toast.error('Failed to create email notification');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle SMS form submission
  const handleSMSSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!smsMessage || !smsRecipients) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    if (smsMessage.length > 160) {
      toast.error('SMS message cannot exceed 160 characters');
      return;
    }
    
    setIsLoading(true);
    try {
      await communicationAPI.createSMSNotification({
        message: smsMessage,
        recipients: smsRecipients,
        status: 'draft'
      });
      
      toast.success('SMS notification created successfully');
      
      // Reset form
      setSmsMessage('');
      setSmsRecipients('');
    } catch (error) {
      console.error('Error creating SMS notification:', error);
      toast.error('Failed to create SMS notification');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle announcement form submission
  const handleAnnouncementSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!announcementTitle || !announcementContent) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    setIsLoading(true);
    try {
      await communicationAPI.createAnnouncement({
        title: announcementTitle,
        content: announcementContent,
        priority: announcementPriority as 'low' | 'medium' | 'high',
        target_audience: announcementAudience as 'all' | 'students' | 'staff' | 'applicants',
        is_active: true,
        start_date: new Date().toISOString()
      });
      
      toast.success('Announcement created successfully');
      
      // Reset form
      setAnnouncementTitle('');
      setAnnouncementContent('');
      setAnnouncementPriority('medium');
      setAnnouncementAudience('all');
    } catch (error) {
      console.error('Error creating announcement:', error);
      toast.error('Failed to create announcement');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Message Center</h2>
        <p className="text-muted-foreground">
          Send communications to users through different channels
        </p>
      </div>

      <Tabs defaultValue="email" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="email" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email
          </TabsTrigger>
          <TabsTrigger value="sms" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            SMS
          </TabsTrigger>
          <TabsTrigger value="announcement" className="flex items-center gap-2">
            <Megaphone className="h-4 w-4" />
            Announcement
          </TabsTrigger>
        </TabsList>
        
        {/* Email Tab */}
        <TabsContent value="email">
          <Card>
            <CardHeader>
              <CardTitle>Send Email</CardTitle>
              <CardDescription>
                Create and send email notifications to users
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleEmailSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email-subject">Subject</Label>
                  <Input
                    id="email-subject"
                    value={emailSubject}
                    onChange={(e) => setEmailSubject(e.target.value)}
                    placeholder="Enter email subject"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email-recipients">Recipients</Label>
                  <Input
                    id="email-recipients"
                    value={emailRecipients}
                    onChange={(e) => setEmailRecipients(e.target.value)}
                    placeholder="<EMAIL>, <EMAIL>"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Comma-separated list of email addresses or recipient groups
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email-content">Content</Label>
                  <Textarea
                    id="email-content"
                    value={emailContent}
                    onChange={(e) => setEmailContent(e.target.value)}
                    placeholder="Enter email content"
                    rows={8}
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    HTML formatting is supported
                  </p>
                </div>
                <div className="flex justify-end">
                  <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                    <Send className="h-4 w-4" />
                    {isLoading ? 'Creating...' : 'Create Email'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* SMS Tab */}
        <TabsContent value="sms">
          <Card>
            <CardHeader>
              <CardTitle>Send SMS</CardTitle>
              <CardDescription>
                Create and send SMS notifications to users
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSMSSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="sms-message">Message</Label>
                  <Textarea
                    id="sms-message"
                    value={smsMessage}
                    onChange={(e) => setSmsMessage(e.target.value)}
                    placeholder="Enter SMS message"
                    rows={4}
                    maxLength={160}
                    required
                  />
                  <div className={`text-xs flex justify-end ${
                    smsMessage.length > 160 ? 'text-red-500' : 
                    smsMessage.length > 140 ? 'text-yellow-500' : 
                    'text-muted-foreground'
                  }`}>
                    {smsMessage.length}/160 characters
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sms-recipients">Recipients</Label>
                  <Input
                    id="sms-recipients"
                    value={smsRecipients}
                    onChange={(e) => setSmsRecipients(e.target.value)}
                    placeholder="+251912345678, +251987654321"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Comma-separated list of phone numbers or recipient groups
                  </p>
                </div>
                <div className="flex justify-end">
                  <Button 
                    type="submit" 
                    disabled={isLoading || smsMessage.length > 160} 
                    className="flex items-center gap-2"
                  >
                    <Send className="h-4 w-4" />
                    {isLoading ? 'Creating...' : 'Create SMS'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Announcement Tab */}
        <TabsContent value="announcement">
          <Card>
            <CardHeader>
              <CardTitle>Create Announcement</CardTitle>
              <CardDescription>
                Create and publish system-wide announcements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAnnouncementSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="announcement-title">Title</Label>
                  <Input
                    id="announcement-title"
                    value={announcementTitle}
                    onChange={(e) => setAnnouncementTitle(e.target.value)}
                    placeholder="Enter announcement title"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="announcement-content">Content</Label>
                  <Textarea
                    id="announcement-content"
                    value={announcementContent}
                    onChange={(e) => setAnnouncementContent(e.target.value)}
                    placeholder="Enter announcement content"
                    rows={6}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="announcement-priority">Priority</Label>
                    <Select
                      value={announcementPriority}
                      onValueChange={setAnnouncementPriority}
                    >
                      <SelectTrigger id="announcement-priority">
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="announcement-audience">Target Audience</Label>
                    <Select
                      value={announcementAudience}
                      onValueChange={setAnnouncementAudience}
                    >
                      <SelectTrigger id="announcement-audience">
                        <SelectValue placeholder="Select audience" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Users</SelectItem>
                        <SelectItem value="students">Students</SelectItem>
                        <SelectItem value="staff">Staff</SelectItem>
                        <SelectItem value="applicants">Applicants</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex justify-end">
                  <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                    <Megaphone className="h-4 w-4" />
                    {isLoading ? 'Creating...' : 'Create Announcement'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MessageCenter;
