import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { graduateVerificationAPI } from '@/services/api';

interface ReportData {
  labels: string[];
  data: number[];
}

interface GraduateReportChartProps {
  title: string;
  description: string;
  reportType: 'department' | 'college' | 'gender' | 'year' | 'program' | 'admission';
}

const GraduateReportChart = ({ title, description, reportType }: GraduateReportChartProps) => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReportData = async () => {
      setLoading(true);
      setError(null);

      try {
        let data: ReportData;

        switch (reportType) {
          case 'department':
            const deptResponse = await graduateVerificationAPI.getReportByDepartment();
            data = deptResponse.data;
            break;
          case 'college':
            const collegeResponse = await graduateVerificationAPI.getReportByCollege();
            data = collegeResponse.data;
            break;
          case 'gender':
            const genderResponse = await graduateVerificationAPI.getReportByGender();
            data = genderResponse.data;
            break;
          case 'year':
            const yearResponse = await graduateVerificationAPI.getReportByYear();
            data = yearResponse.data;
            break;
          case 'program':
            const programResponse = await graduateVerificationAPI.getReportByProgram();
            data = programResponse.data;
            break;
          case 'admission':
            const admissionResponse = await graduateVerificationAPI.getReportByAdmissionClassification();
            data = admissionResponse.data;
            break;
          default:
            throw new Error('Invalid report type');
        }

        setReportData(data);
      } catch (err) {
        console.error(`Error fetching ${reportType} report:`, err);
        setError(`Failed to load ${reportType} report data`);
      } finally {
        setLoading(false);
      }
    };

    fetchReportData();
  }, [reportType]);

  // Calculate total for percentages
  const total = reportData?.data.reduce((sum, value) => sum + value, 0) || 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-64 text-destructive">
            <p>{error}</p>
          </div>
        ) : reportData && reportData.labels.length > 0 ? (
          <div className="space-y-8">
            {/* Bar Chart */}
            <div className="h-64 flex items-end gap-2">
              {reportData.labels.map((label, index) => {
                const value = reportData.data[index];
                const percentage = total > 0 ? (value / total) * 100 : 0;
                const height = `${Math.max(5, percentage)}%`;

                return (
                  <div key={label} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-primary rounded-t-md transition-all duration-500 ease-in-out"
                      style={{
                        height,
                        minHeight: '20px',
                        backgroundColor: `hsl(215, ${Math.min(100, percentage + 30)}%, 50%)`
                      }}
                    />
                    <div className="mt-2 text-xs font-medium text-center">{label}</div>
                  </div>
                );
              })}
            </div>

            {/* Data Table */}
            <div className="border rounded-md overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="bg-muted">
                    <th className="px-4 py-2 text-left text-sm font-medium">
                      {reportType === 'department' ? 'Department' :
                       reportType === 'gender' ? 'Gender' :
                       reportType === 'year' ? 'Year' :
                       reportType === 'program' ? 'Program' :
                       'Admission Classification'}
                    </th>
                    <th className="px-4 py-2 text-right text-sm font-medium">Count</th>
                    <th className="px-4 py-2 text-right text-sm font-medium">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData.labels.map((label, index) => {
                    const value = reportData.data[index];
                    const percentage = total > 0 ? (value / total) * 100 : 0;

                    return (
                      <tr key={label} className="border-t">
                        <td className="px-4 py-2 text-sm">{label}</td>
                        <td className="px-4 py-2 text-sm text-right">{value}</td>
                        <td className="px-4 py-2 text-sm text-right">{percentage.toFixed(1)}%</td>
                      </tr>
                    );
                  })}
                  <tr className="border-t bg-muted/50">
                    <td className="px-4 py-2 text-sm font-medium">Total</td>
                    <td className="px-4 py-2 text-sm font-medium text-right">{total}</td>
                    <td className="px-4 py-2 text-sm font-medium text-right">100%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="flex justify-center items-center h-64 text-muted-foreground">
            <p>No data available</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GraduateReportChart;
