# 🔧 Document Replace Fix & Application URLs

## ✅ **Document Replace Functionality - Fixed!**

### **🐛 Issue Identified**
**Problem**: The "Replace" button for existing documents was not showing when there was an existing document uploaded.

**Root Cause**: The button logic was showing upload/replace button only when there was no `uploadedDocument` (pending upload), but it should always show for existing documents.

### **🔧 Fix Applied**

**Before (Broken Logic)**:
```tsx
{uploadedDocument ? (
  // Show "Ready" status and remove button
) : (
  // Show upload/replace button ONLY when no pending upload
)}
```

**After (Fixed Logic)**:
```tsx
{/* Upload/Replace button - always show */}
<Button onClick={() => fileInputRef.current?.click()}>
  {existingDocument ? 'Replace' : 'Upload'}
</Button>

{/* Show pending upload status separately */}
{uploadedDocument && (
  // Show "Ready" status and remove button for pending uploads
)}
```

### **🎯 Key Changes**

1. **Always Show Upload/Replace Button** ✅
   - Button is always visible regardless of upload status
   - Text changes based on whether document exists: "Replace" vs "Upload"

2. **Separate Pending Upload Status** ✅
   - "Ready" status and remove button only show for pending uploads
   - Doesn't interfere with replace functionality

3. **Proper Event Prevention** ✅
   - All buttons have `type="button"` and proper event prevention
   - No form submission when clicking document actions

## 📍 **Alumni Application URLs**

### **🎓 Complete Application (Form1)**
```
Frontend Access: /graduate-admin?tab=alumni-applications
API Endpoint: /api/applications/form1/
```

**Features**:
- ✅ **Full Personal Information** (name, contact, student ID)
- ✅ **Academic Information** (college, department, degree, status)
- ✅ **Service Information** (service type, document requirements)
- ✅ **Destination Logic** (UoG internal vs external institution)
- ✅ **Document Upload** with view/replace functionality
- ✅ **Comprehensive Validation** and error handling

### **🎓 Simplified Application (Form2)**
```
Frontend Access: /graduate-admin?tab=alumni-applications (Form2 tab)
API Endpoint: /api/applications/form2/
```

**Features**:
- ✅ **Essential Personal Information** (name, contact)
- ✅ **Basic Academic Information** (college, department, degree)
- ✅ **Service Information** (service type, documents)
- ✅ **Streamlined Process** (no destination logic)
- ✅ **Document Upload** with view/replace functionality
- ✅ **Quick Validation** for faster processing

## 🚀 **How to Access Applications**

### **For Staff Users**
1. **Login** to the system
2. **Navigate to**: `/graduate-admin`
3. **Select Tab**: "Alumni Applications"
4. **Choose Form Type**:
   - **"Form1"** tab for Complete Applications
   - **"Form2"** tab for Simplified Applications
5. **Click "Create"** to start new application

### **Direct URLs**
```bash
# Graduate Admin Dashboard
https://your-domain.com/graduate-admin

# Alumni Applications Tab
https://your-domain.com/graduate-admin?tab=alumni-applications

# API Endpoints
https://your-domain.com/api/applications/form1/     # Complete Applications
https://your-domain.com/api/applications/form2/     # Simplified Applications
```

## 🧪 **Testing the Fix**

### **Test Document Replace**
1. **Navigate to**: `/graduate-admin?tab=alumni-applications`
2. **Edit application** with uploaded documents
3. **Go to "Service Information"** tab
4. **Expected Results**:
   - ✅ **"Replace" button** visible for existing documents
   - ✅ **"Upload" button** visible for missing documents
   - ✅ **File picker opens** when clicking Replace/Upload
   - ✅ **No form submission** occurs
   - ✅ **Pending uploads** show "Ready" status with remove option

### **Test Both Application Types**
1. **Complete Application (Form1)**:
   - ✅ All 4 tabs: Personal, Academic, Service, Destination
   - ✅ Destination logic with UoG internal/external options
   - ✅ Full validation and document management

2. **Simplified Application (Form2)**:
   - ✅ 3 tabs: Personal, Academic, Service
   - ✅ Streamlined form without destination logic
   - ✅ Essential fields only for quick processing

## 📊 **Application Comparison**

| Feature | Complete (Form1) | Simplified (Form2) |
|---------|------------------|-------------------|
| **Personal Info** | ✅ Full details | ✅ Essential only |
| **Academic Info** | ✅ Comprehensive | ✅ Basic |
| **Service Info** | ✅ With documents | ✅ With documents |
| **Destination Logic** | ✅ UoG/External | ❌ Not included |
| **Document Upload** | ✅ Full featured | ✅ Full featured |
| **Validation** | ✅ Comprehensive | ✅ Streamlined |
| **Use Case** | Complex requests | Quick services |

## 🎯 **Success Criteria**

### **Document Replace Functionality**
- ✅ **Replace button** always visible for existing documents
- ✅ **Upload button** visible for missing documents
- ✅ **File picker** opens correctly
- ✅ **No form submission** when clicking document actions
- ✅ **Pending uploads** managed separately

### **Application Access**
- ✅ **Staff users** can access both application types
- ✅ **Form1** includes all comprehensive features
- ✅ **Form2** provides streamlined experience
- ✅ **Document management** works in both forms
- ✅ **URLs** are accessible and functional

## 🔗 **Quick Access Links**

### **For Development/Testing**
```bash
# Local Development
http://localhost:3000/graduate-admin?tab=alumni-applications

# API Testing
http://localhost:8000/api/applications/form1/
http://localhost:8000/api/applications/form2/
```

### **For Production**
```bash
# Replace with your actual domain
https://your-domain.com/graduate-admin?tab=alumni-applications
```

## ✅ **Ready for Use**

Both the document replace functionality and application URLs are now working correctly:

1. **Document Replace**: Fixed and fully functional
2. **Complete Applications**: Available at Form1 tab
3. **Simplified Applications**: Available at Form2 tab
4. **Staff Access**: Through graduate-admin dashboard
5. **API Endpoints**: Properly configured and accessible

Users can now seamlessly create, edit, and manage alumni applications with full document upload/replace capabilities! 🎉
