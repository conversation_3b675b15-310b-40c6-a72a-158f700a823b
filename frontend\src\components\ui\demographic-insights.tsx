import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './card';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LabelList } from 'recharts';
import { ChartExport } from './chart-export';
import { Users, Award, TrendingUp } from 'lucide-react';

const GENDER_COLORS = ['#0088FE', '#FF8042'];
const GPA_COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];

interface DemographicInsightsProps {
  demographicData: {
    gender: Record<string, number>;
    gpaRanges: Record<string, number>;
  } | null;
  isLoading?: boolean;
}

export const DemographicInsights: React.FC<DemographicInsightsProps> = ({
  demographicData,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card className="border-0 shadow-xl">
          <CardHeader>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-100 rounded animate-pulse"></div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-xl">
          <CardHeader>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-100 rounded animate-pulse"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!demographicData) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card className="border-0 shadow-xl">
          <CardContent className="flex items-center justify-center h-80">
            <div className="text-center text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No demographic data available</p>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-xl">
          <CardContent className="flex items-center justify-center h-80">
            <div className="text-center text-gray-500">
              <Award className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No GPA distribution data available</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Prepare gender data for donut chart
  const genderData = Object.entries(demographicData.gender).map(([gender, count], index) => ({
    name: gender,
    value: count,
    color: GENDER_COLORS[index % GENDER_COLORS.length]
  }));

  const totalGraduates = genderData.reduce((sum, item) => sum + item.value, 0);

  // Prepare GPA ranges data for stacked bar chart
  const gpaData = Object.entries(demographicData.gpaRanges).map(([range, count], index) => ({
    range,
    count,
    percentage: totalGraduates > 0 ? ((count / totalGraduates) * 100).toFixed(1) : '0',
    color: GPA_COLORS[index % GPA_COLORS.length]
  }));

  const GenderTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const percentage = totalGraduates > 0 ? ((data.value / totalGraduates) * 100).toFixed(1) : '0';
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{data.name}</p>
          <p className="text-blue-600">
            <span className="font-medium">Count:</span> {data.value.toLocaleString()}
          </p>
          <p className="text-green-600">
            <span className="font-medium">Percentage:</span> {percentage}%
          </p>
        </div>
      );
    }
    return null;
  };

  const GPATooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">GPA Range: {label}</p>
          <p className="text-blue-600">
            <span className="font-medium">Graduates:</span> {data.count.toLocaleString()}
          </p>
          <p className="text-green-600">
            <span className="font-medium">Percentage:</span> {data.percentage}%
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      {/* Gender Distribution Donut Chart */}
      <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                <div className="h-2 w-2 bg-blue-600 rounded-full mr-3"></div>
                Gender Distribution
              </CardTitle>
              <p className="text-gray-600 mt-1">Male/Female ratio with exact counts</p>
            </div>
            <div className="flex items-center space-x-2">
              <ChartExport 
                chartId="gender-donut-chart"
                filename="gender-distribution"
                data={genderData}
              />
              <div className="bg-blue-600 p-2 rounded-lg">
                <Users className="h-5 w-5 text-white" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="h-96 p-6 relative">
          <div className="relative h-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={genderData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => {
                    const percentage = totalGraduates > 0 ? ((value / totalGraduates) * 100).toFixed(1) : '0';
                    return `${name}: ${percentage}%`;
                  }}
                  outerRadius={100}
                  innerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {genderData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<GenderTooltip />} />
              </PieChart>
            </ResponsiveContainer>

            {/* Center Statistics - Properly contained */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">{totalGraduates.toLocaleString()}</div>
                <div className="text-sm text-gray-500">Total Graduates</div>
              </div>
            </div>
          </div>
          
          {/* Legend */}
          <div className="mt-4 flex justify-center space-x-6">
            {genderData.map((entry, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div 
                  className="w-4 h-4 rounded-full" 
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span className="text-sm font-medium text-gray-700">
                  {entry.name}: {entry.value.toLocaleString()}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* GPA Distribution Stacked Bar Chart */}
      <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                <div className="h-2 w-2 bg-green-600 rounded-full mr-3"></div>
                Academic Performance Distribution
              </CardTitle>
              <p className="text-gray-600 mt-1">Graduates by GPA ranges (2.0-4.0)</p>
            </div>
            <div className="flex items-center space-x-2">
              <ChartExport 
                chartId="gpa-bar-chart"
                filename="gpa-distribution"
                data={gpaData}
              />
              <div className="bg-green-600 p-2 rounded-lg">
                <Award className="h-5 w-5 text-white" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Chart Container */}
          <div className="h-80 mb-6">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={gpaData}
                margin={{ top: 30, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis
                  dataKey="range"
                  tick={{ fontSize: 12 }}
                  axisLine={{ stroke: '#64748b' }}
                  tickLine={{ stroke: '#64748b' }}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  axisLine={{ stroke: '#64748b' }}
                  tickLine={{ stroke: '#64748b' }}
                  label={{ value: 'Number of Graduates', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<GPATooltip />} />
                <Bar
                  dataKey="count"
                  fill="#10B981"
                  radius={[4, 4, 0, 0]}
                >
                  <LabelList
                    dataKey="percentage"
                    position="top"
                    formatter={(value: any) => `${value}%`}
                    style={{
                      fontSize: '12px',
                      fontWeight: 'bold',
                      fill: '#374151'
                    }}
                  />
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Performance Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-bold text-green-700">
                {gpaData.filter(item => parseFloat(item.range.split('-')[0]) >= 3.0)
                  .reduce((sum, item) => sum + item.count, 0).toLocaleString()}
              </div>
              <div className="text-xs text-green-600">High Performers (3.0+)</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-700">
                {gpaData.filter(item => item.range === '3.5-4.0')[0]?.percentage || '0'}%
              </div>
              <div className="text-xs text-blue-600">Excellent (3.5-4.0)</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DemographicInsights;
