import { useEffect, useState, useCallback } from 'react';
import { toast } from 'sonner';
import { authAPI } from '@/services/api';
import { activityMonitor } from '@/services/activityMonitor';

interface SessionManagerConfig {
  idleTimeoutMinutes?: number;
  warningTimeMinutes?: number;
  enableActivityMonitoring?: boolean;
  enableServerSideLogout?: boolean;
}

interface SessionState {
  isActive: boolean;
  isWarning: boolean;
  timeRemaining: number | null;
  lastActivity: number | null;
  sessionDuration: number;
  isAuthenticated: boolean;
}

/**
 * Enhanced session management hook with comprehensive activity monitoring
 */
export const useSessionManager = (config: SessionManagerConfig = {}) => {
  const {
    idleTimeoutMinutes = 15,
    warningTimeMinutes = 1,
    enableActivityMonitoring = true,
    enableServerSideLogout = true
  } = config;

  const [sessionState, setSessionState] = useState<SessionState>({
    isActive: true,
    isWarning: false,
    timeRemaining: null,
    lastActivity: null,
    sessionDuration: 0,
    isAuthenticated: false
  });

  const [warningTimer, setWarningTimer] = useState<NodeJS.Timeout | null>(null);
  const [logoutTimer, setLogoutTimer] = useState<NodeJS.Timeout | null>(null);
  const [countdownInterval, setCountdownInterval] = useState<NodeJS.Timeout | null>(null);

  /**
   * Check if user is authenticated
   */
  const checkAuthentication = useCallback(() => {
    const token = localStorage.getItem('token');
    const isAuth = localStorage.getItem('isAuthenticated') === 'true';
    return !!(token && isAuth);
  }, []);

  /**
   * Perform logout with optional server-side session termination
   */
  const performLogout = useCallback(async (reason: 'inactivity' | 'manual' = 'manual') => {
    try {
      if (enableServerSideLogout) {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          await authAPI.logout(refreshToken);
        }
      }
    } catch (error) {
      console.error('Error during server-side logout:', error);
    }

    // Clear all authentication data
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    localStorage.removeItem('lastActivity');
    localStorage.removeItem('sessionInfo');
    localStorage.removeItem('public_certificate_stats');

    // Stop activity monitoring
    if (enableActivityMonitoring) {
      activityMonitor.stopMonitoring();
    }

    // Show appropriate message
    if (reason === 'inactivity') {
      toast.error('Session Expired', {
        description: 'You have been logged out due to inactivity for security reasons.',
        duration: 5000,
      });
    }

    // Redirect to login with reason
    window.location.href = `/login?reason=session_expired`;
  }, [enableServerSideLogout, enableActivityMonitoring]);

  /**
   * Reset session timers
   */
  const resetTimers = useCallback(() => {
    // Clear existing timers
    if (warningTimer) clearTimeout(warningTimer);
    if (logoutTimer) clearTimeout(logoutTimer);
    if (countdownInterval) clearInterval(countdownInterval);

    // Update last activity
    const now = Date.now();
    localStorage.setItem('lastActivity', now.toString());

    // Reset warning state
    setSessionState(prev => ({
      ...prev,
      isWarning: false,
      timeRemaining: null,
      lastActivity: now
    }));

    // Dismiss any existing warning toasts
    toast.dismiss('session-warning');

    // Set new warning timer
    const warningTimeout = setTimeout(() => {
      setSessionState(prev => ({
        ...prev,
        isWarning: true,
        timeRemaining: warningTimeMinutes * 60
      }));

      // Show warning toast
      toast.warning('Session Warning', {
        id: 'session-warning',
        description: `You will be logged out in ${warningTimeMinutes} minute${warningTimeMinutes !== 1 ? 's' : ''} due to inactivity.`,
        duration: Infinity,
        action: {
          label: 'Stay Logged In',
          onClick: resetTimers
        }
      });

      // Start countdown
      const countdown = setInterval(() => {
        setSessionState(prev => {
          if (prev.timeRemaining === null || prev.timeRemaining <= 1) {
            clearInterval(countdown);
            return { ...prev, timeRemaining: 0 };
          }
          return { ...prev, timeRemaining: prev.timeRemaining - 1 };
        });
      }, 1000);

      setCountdownInterval(countdown);
    }, (idleTimeoutMinutes - warningTimeMinutes) * 60 * 1000);

    // Set new logout timer
    const logoutTimeout = setTimeout(() => {
      performLogout('inactivity');
    }, idleTimeoutMinutes * 60 * 1000);

    setWarningTimer(warningTimeout);
    setLogoutTimer(logoutTimeout);
  }, [idleTimeoutMinutes, warningTimeMinutes, warningTimer, logoutTimer, countdownInterval, performLogout]);

  /**
   * Initialize session management
   */
  const initializeSession = useCallback(() => {
    if (!checkAuthentication()) {
      setSessionState(prev => ({ ...prev, isAuthenticated: false }));
      return;
    }

    setSessionState(prev => ({ ...prev, isAuthenticated: true }));

    // Start activity monitoring if enabled
    if (enableActivityMonitoring) {
      activityMonitor.startMonitoring();

      // Register activity callback
      const unsubscribeActivity = activityMonitor.onActivity(() => {
        resetTimers();
      });

      // Register inactivity callback
      const unsubscribeInactivity = activityMonitor.onInactivity((duration) => {
        console.log(`Inactivity detected: ${Math.round(duration / 1000 / 60)} minutes`);
      });

      // Return cleanup function
      return () => {
        unsubscribeActivity();
        unsubscribeInactivity();
        activityMonitor.stopMonitoring();
      };
    } else {
      // Fallback to basic event listeners
      const events = ['mousedown', 'keydown', 'scroll', 'touchstart', 'mousemove'];
      const handleActivity = () => resetTimers();

      events.forEach(event => {
        window.addEventListener(event, handleActivity);
      });

      // Return cleanup function
      return () => {
        events.forEach(event => {
          window.removeEventListener(event, handleActivity);
        });
      };
    }
  }, [checkAuthentication, enableActivityMonitoring, resetTimers]);

  /**
   * Update session duration periodically
   */
  useEffect(() => {
    if (!sessionState.isAuthenticated) return;

    const updateDuration = () => {
      if (enableActivityMonitoring) {
        const duration = activityMonitor.getSessionDuration();
        setSessionState(prev => ({ ...prev, sessionDuration: duration }));
      }
    };

    const interval = setInterval(updateDuration, 1000);
    return () => clearInterval(interval);
  }, [sessionState.isAuthenticated, enableActivityMonitoring]);

  /**
   * Initialize session on mount
   */
  useEffect(() => {
    const cleanup = initializeSession();
    
    return () => {
      if (cleanup) cleanup();
      if (warningTimer) clearTimeout(warningTimer);
      if (logoutTimer) clearTimeout(logoutTimer);
      if (countdownInterval) clearInterval(countdownInterval);
    };
  }, [initializeSession]);

  /**
   * Manual logout function
   */
  const logout = useCallback(() => {
    performLogout('manual');
  }, [performLogout]);

  /**
   * Extend session (reset timers)
   */
  const extendSession = useCallback(() => {
    resetTimers();
    toast.success('Session Extended', {
      description: 'Your session has been extended.',
      duration: 3000,
    });
  }, [resetTimers]);

  /**
   * Get session statistics
   */
  const getSessionStats = useCallback(() => {
    if (!enableActivityMonitoring) return null;

    return {
      sessionInfo: activityMonitor.getSessionInfo(),
      recentEvents: activityMonitor.getRecentEvents(10),
      timeSinceLastActivity: activityMonitor.getTimeSinceLastActivity(),
      isUserActive: activityMonitor.isUserActive(),
      sessionDuration: activityMonitor.getSessionDuration()
    };
  }, [enableActivityMonitoring]);

  return {
    sessionState,
    logout,
    extendSession,
    resetTimers,
    getSessionStats,
    isAuthenticated: sessionState.isAuthenticated,
    isWarning: sessionState.isWarning,
    timeRemaining: sessionState.timeRemaining,
    sessionDuration: sessionState.sessionDuration
  };
};

export default useSessionManager;
