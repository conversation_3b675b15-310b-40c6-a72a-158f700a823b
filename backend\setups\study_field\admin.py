from django.contrib import admin
from setups.study_field.models import StudyField
# Register your models here.
@admin.register(StudyField)
class StudyFieldAdmin(admin.ModelAdmin):
    list_display = ['field_of_study', 'get_college', 'get_department', 'description']
    search_fields = ['field_of_study', 'college__name', 'department__name', 'description']
    list_filter = ['college', 'department', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    ordering = ['field_of_study']
    list_per_page = 20
    list_max_show_all = 100
    autocomplete_fields = ['college', 'department']

    def get_college(self, obj):
        return obj.college.name
    get_college.short_description = 'College'
    get_college.admin_order_field = 'college__name'

    def get_department(self, obj):
        return obj.department.name
    get_department.short_description = 'Department'
    get_department.admin_order_field = 'department__name'
