import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { 
  Users, 
  AlertTriangle, 
  Search, 
  RefreshCw,
  Download,
  UserCheck,
  UserX,
  Shield,
  Clock
} from 'lucide-react';
import { useRBAC } from '@/contexts/RBACContext';
import { authAPI } from '@/services/api';
import { toast } from 'sonner';

interface UserAuditData {
  user_id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
  is_active: boolean;
  roles: string[];
  permission_count: number;
  issues: string[];
  last_login?: string;
  department?: string;
  date_joined: string;
}

const UserAudit: React.FC = () => {
  const { isSuperuser, isAdmin } = useRBAC();
  const [auditData, setAuditData] = useState<UserAuditData[]>([]);
  const [filteredData, setFilteredData] = useState<UserAuditData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'issues' | 'staff' | 'inactive'>('all');

  const hasAuditAccess = isSuperuser || isAdmin;

  useEffect(() => {
    if (hasAuditAccess) {
      loadAuditData();
    }
  }, [hasAuditAccess]);

  useEffect(() => {
    filterAuditData();
  }, [auditData, searchTerm, filterType]);

  const loadAuditData = async () => {
    setIsLoading(true);
    try {
      const response = await authAPI.validateRoles();
      if (response.data && response.data.validation_results) {
        setAuditData(response.data.validation_results);
        toast.success('User audit data loaded successfully');
      }
    } catch (error) {
      console.error('Failed to load audit data:', error);
      toast.error('Failed to load user audit data');
    } finally {
      setIsLoading(false);
    }
  };

  const filterAuditData = () => {
    let filtered = auditData;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user => 
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.last_name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    switch (filterType) {
      case 'issues':
        filtered = filtered.filter(user => user.issues.length > 0);
        break;
      case 'staff':
        filtered = filtered.filter(user => user.is_staff);
        break;
      case 'inactive':
        filtered = filtered.filter(user => !user.is_active);
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    setFilteredData(filtered);
  };

  const exportAuditReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      total_users: auditData.length,
      users_with_issues: auditData.filter(u => u.issues.length > 0).length,
      staff_users: auditData.filter(u => u.is_staff).length,
      inactive_users: auditData.filter(u => !u.is_active).length,
      audit_results: filteredData,
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `user-audit-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('User audit report exported successfully');
  };

  const getIssueColor = (issueCount: number) => {
    if (issueCount === 0) return 'text-green-600';
    if (issueCount <= 2) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatLastLogin = (lastLogin?: string) => {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  };

  if (!hasAuditAccess) {
    return (
      <Card>
        <CardContent className="pt-6">
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to access user audit features.
              This section requires administrator or superuser privileges.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{auditData.length}</p>
                <p className="text-xs text-muted-foreground">Total Users</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold">
                  {auditData.filter(u => u.issues.length > 0).length}
                </p>
                <p className="text-xs text-muted-foreground">Users with Issues</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-2xl font-bold">
                  {auditData.filter(u => u.is_staff).length}
                </p>
                <p className="text-xs text-muted-foreground">Staff Users</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <UserX className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-2xl font-bold">
                  {auditData.filter(u => !u.is_active).length}
                </p>
                <p className="text-xs text-muted-foreground">Inactive Users</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Audit Interface */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <UserCheck className="h-5 w-5" />
              <span>User Security Audit</span>
            </CardTitle>
            <CardDescription>
              Review user accounts for security compliance and role assignments
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadAuditData}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportAuditReport}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              {['all', 'issues', 'staff', 'inactive'].map((type) => (
                <Button
                  key={type}
                  variant={filterType === type ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterType(type as any)}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Button>
              ))}
            </div>
          </div>

          {/* User List */}
          <div className="space-y-4">
            {filteredData.map((user) => (
              <Card key={user.user_id} className={user.issues.length > 0 ? 'border-red-200 bg-red-50' : ''}>
                <CardContent className="pt-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="flex items-center space-x-2">
                        {user.issues.length > 0 ? (
                          <UserX className="h-5 w-5 text-red-600" />
                        ) : (
                          <UserCheck className="h-5 w-5 text-green-600" />
                        )}
                        <div>
                          <div className="flex items-center space-x-2">
                            <p className="font-medium">{user.username}</p>
                            <div className="flex space-x-1">
                              {user.is_superuser && <Badge variant="destructive">Superuser</Badge>}
                              {user.is_staff && <Badge variant="secondary">Staff</Badge>}
                              {!user.is_active && <Badge variant="outline">Inactive</Badge>}
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                          <div className="flex items-center space-x-4 mt-1">
                            <p className="text-xs text-muted-foreground">
                              Roles: {user.roles.length > 0 ? user.roles.join(', ') : 'None'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Permissions: {user.permission_count}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <p className="text-xs text-muted-foreground">
                          Last login: {formatLastLogin(user.last_login)}
                        </p>
                      </div>
                      <p className={`text-sm font-medium ${getIssueColor(user.issues.length)}`}>
                        {user.issues.length} issue{user.issues.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  
                  {user.issues.length > 0 && (
                    <div className="mt-3 space-y-2">
                      {user.issues.map((issue, index) => (
                        <Alert key={index} variant="destructive" className="py-2">
                          <AlertTriangle className="h-3 w-3" />
                          <AlertDescription className="text-xs ml-2">
                            {issue}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredData.length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No users found matching your criteria</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UserAudit;
