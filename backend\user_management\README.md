# User Management

This app handles user management functionality for the application.

## Account Inactivity Feature

The system can automatically deactivate user accounts that have been inactive for a specified period. This feature is configurable in the Organization Settings.

### Management Command

To manually check for inactive users and deactivate them, run:

```bash
python manage.py check_inactive_users
```

### Setting Up Scheduled Task

To automate this process, set up a scheduled task (cron job) to run the command daily:

#### Linux/Unix (Cron)

Add the following to your crontab:

```bash
# Run daily at 2:00 AM
0 2 * * * cd /path/to/your/project && /path/to/your/python /path/to/your/project/manage.py check_inactive_users >> /path/to/your/logs/inactive_users.log 2>&1
```

#### Windows (Task Scheduler)

1. Open Task Scheduler
2. Create a new Basic Task
3. Set the trigger to Daily at 2:00 AM
4. Set the action to Start a Program
5. Program/script: `C:\path\to\python.exe`
6. Add arguments: `C:\path\to\your\project\manage.py check_inactive_users`
7. Start in: `C:\path\to\your\project`

### Configuration

The inactivity period can be configured in the Organization Settings page in the admin interface. Available options:

- Disabled (default)
- 30 days
- 60 days
- 90 days
- 6 months
- 1 year

When a user is deactivated due to inactivity, they will need to contact an administrator to reactivate their account.
