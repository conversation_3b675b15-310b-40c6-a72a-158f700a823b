from django.db import models
from django.contrib.auth.models import User, Group, Permission
from django.db.models.signals import post_save
from django.dispatch import receiver
import uuid

# Extended User Profile for RBAC
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    department = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    employee_id = models.CharField(max_length=50, blank=True, null=True, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s profile"

    @property
    def full_name(self):
        return f"{self.user.first_name} {self.user.last_name}".strip()

    @property
    def roles(self):
        """Get all roles (groups) assigned to this user"""
        return self.user.groups.all()

    @property
    def permissions(self):
        """Get all permissions for this user (direct + group permissions)"""
        return self.user.get_all_permissions()

    def has_role(self, role_name):
        """Check if user has a specific role"""
        return self.user.groups.filter(name=role_name).exists()

    def has_any_role(self, role_names):
        """Check if user has any of the specified roles"""
        return self.user.groups.filter(name__in=role_names).exists()

    def add_role(self, role_name):
        """Add a role to the user"""
        try:
            role = Group.objects.get(name=role_name)
            self.user.groups.add(role)
            return True
        except Group.DoesNotExist:
            return False

    def remove_role(self, role_name):
        """Remove a role from the user"""
        try:
            role = Group.objects.get(name=role_name)
            self.user.groups.remove(role)
            return True
        except Group.DoesNotExist:
            return False

# Signal to create UserProfile when User is created
@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    if hasattr(instance, 'profile'):
        instance.profile.save()
    else:
        UserProfile.objects.create(user=instance)

# Custom Role model for enhanced role management
class Role(models.Model):
    """
    Custom Role model that extends Django's Group model functionality
    """
    group = models.OneToOneField(Group, on_delete=models.CASCADE, related_name='role_detail')
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return self.group.name

    @property
    def name(self):
        return self.group.name

    @property
    def permissions(self):
        return self.group.permissions.all()

    def add_permission(self, permission_codename):
        """Add a permission to this role"""
        try:
            permission = Permission.objects.get(codename=permission_codename)
            self.group.permissions.add(permission)
            return True
        except Permission.DoesNotExist:
            return False

    def remove_permission(self, permission_codename):
        """Remove a permission from this role"""
        try:
            permission = Permission.objects.get(codename=permission_codename)
            self.group.permissions.remove(permission)
            return True
        except Permission.DoesNotExist:
            return False

# Permission Categories for better organization
class PermissionCategory(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Permission Categories"

    def __str__(self):
        return self.name
