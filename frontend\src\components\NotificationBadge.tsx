import { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import { Link } from 'react-router-dom';
import notificationService from '@/services/notificationService';

interface NotificationBadgeProps {
  className?: string;
}

const NotificationBadge = ({ className = '' }: NotificationBadgeProps) => {
  const [unreadCount, setUnreadCount] = useState(0);

  // Update unread count when component mounts and when notifications change
  useEffect(() => {
    const updateUnreadCount = () => {
      const notifications = notificationService.getNotifications();
      const unread = notifications.filter(notification => !notification.read).length;
      setUnreadCount(unread);
    };

    // Initial count
    updateUnreadCount();

    // Set up an interval to check for new notifications
    const intervalId = setInterval(updateUnreadCount, 30000); // Check every 30 seconds

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  return (
    <Link to="/notifications" className={`relative ${className}`}>
      <Bell className="h-5 w-5" />
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
          {unreadCount > 9 ? '9+' : unreadCount}
        </span>
      )}
    </Link>
  );
};

export default NotificationBadge;
