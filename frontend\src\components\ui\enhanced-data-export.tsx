import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Button } from './button';
import { Input } from './input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { Download, FileSpreadsheet, FileText, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

interface EnhancedDataExportProps {
  exportData: any[];
  isLoading?: boolean;
}

export const EnhancedDataExport: React.FC<EnhancedDataExportProps> = ({
  exportData,
  isLoading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  const [columnFilters, setColumnFilters] = useState<Record<string, string>>({});

  // Define columns
  const columns = [
    { key: 'student_id', label: 'Student ID', sortable: true },
    { key: 'full_name', label: 'Full Name', sortable: true },
    { key: 'college_name', label: 'College', sortable: true },
    { key: 'department_name', label: 'Department', sortable: true },
    { key: 'program_name', label: 'Program', sortable: true },
    { key: 'year_of_graduation', label: 'Graduation Year', sortable: true },
    { key: 'gpa', label: 'GPA', sortable: true },
    { key: 'gender', label: 'Gender', sortable: true }
  ];

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let filtered = exportData.filter(item => {
      // Global search
      const globalMatch = Object.values(item).some(value => 
        value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      // Column-specific filters
      const columnMatch = Object.entries(columnFilters).every(([column, filter]) => {
        if (!filter) return true;
        const value = item[column]?.toString().toLowerCase() || '';
        return value.includes(filter.toLowerCase());
      });
      
      return globalMatch && columnMatch;
    });

    // Sort data
    if (sortColumn) {
      filtered.sort((a, b) => {
        let aValue = a[sortColumn];
        let bValue = b[sortColumn];
        
        // Handle different data types
        if (sortColumn === 'gpa' || sortColumn === 'year_of_graduation') {
          aValue = parseFloat(aValue) || 0;
          bValue = parseFloat(bValue) || 0;
        } else {
          aValue = aValue?.toString().toLowerCase() || '';
          bValue = bValue?.toString().toLowerCase() || '';
        }
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [exportData, searchTerm, sortColumn, sortDirection, columnFilters]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedData.length / pageSize);
  const paginatedData = filteredAndSortedData.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Export functions
  const exportToCSV = () => {
    const headers = columns.map(col => col.label);
    const csvData = [
      headers,
      ...filteredAndSortedData.map(item => 
        columns.map(col => item[col.key] || '')
      )
    ];

    const csvContent = csvData.map(row => 
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `graduate-data-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(
      filteredAndSortedData.map(item => {
        const row: any = {};
        columns.forEach(col => {
          row[col.label] = item[col.key] || '';
        });
        return row;
      })
    );

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Graduate Data');
    XLSX.writeFile(workbook, `graduate-data-${new Date().toISOString().slice(0, 10)}.xlsx`);
  };

  const exportToPDF = () => {
    const doc = new jsPDF('landscape');
    
    doc.setFontSize(16);
    doc.text('Graduate Data Export', 14, 15);
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 25);
    doc.text(`Total Records: ${filteredAndSortedData.length}`, 14, 30);

    const tableData = filteredAndSortedData.map(item => 
      columns.map(col => item[col.key] || '')
    );

    (doc as any).autoTable({
      head: [columns.map(col => col.label)],
      body: tableData,
      startY: 35,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [26, 115, 192] },
      margin: { top: 35 }
    });

    doc.save(`graduate-data-${new Date().toISOString().slice(0, 10)}.pdf`);
  };

  if (isLoading) {
    return (
      <Card className="border-0 shadow-xl">
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-100 rounded animate-pulse"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-xl bg-white">
      <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <div className="h-2 w-2 bg-gray-600 rounded-full mr-3"></div>
              Comprehensive Data Export
            </CardTitle>
            <p className="text-gray-600 mt-1">Interactive table with advanced filtering and export options</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={exportToCSV} variant="outline" size="sm">
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              CSV
            </Button>
            <Button onClick={exportToExcel} variant="outline" size="sm">
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Excel
            </Button>
            <Button onClick={exportToPDF} variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              PDF
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {/* Search and Controls */}
        <div className="flex flex-col lg:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search across all fields..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(parseInt(value))}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="25">25 rows</SelectItem>
                <SelectItem value="50">50 rows</SelectItem>
                <SelectItem value="100">100 rows</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Data Table */}
        <div className="overflow-x-auto border rounded-lg">
          <table className="w-full border-collapse">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th key={column.key} className="text-left py-3 px-4 font-semibold text-gray-700 border-b">
                    <div className="flex items-center space-x-2">
                      <span
                        className={column.sortable ? 'cursor-pointer hover:text-blue-600' : ''}
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        {column.label}
                        {sortColumn === column.key && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </span>
                    </div>
                    <Input
                      placeholder={`Filter ${column.label.toLowerCase()}...`}
                      value={columnFilters[column.key] || ''}
                      onChange={(e) => setColumnFilters(prev => ({
                        ...prev,
                        [column.key]: e.target.value
                      }))}
                      className="mt-2 h-8 text-xs"
                    />
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {paginatedData.map((item, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                  {columns.map((column) => (
                    <td key={column.key} className="py-3 px-4 text-sm text-gray-900">
                      {item[column.key] || '-'}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-500">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredAndSortedData.length)} of {filteredAndSortedData.length} results
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedDataExport;
