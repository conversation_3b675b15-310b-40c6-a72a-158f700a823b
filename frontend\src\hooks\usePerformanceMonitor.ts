import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  totalTime: number;
}

/**
 * A hook to monitor component performance
 * @param componentName Name of the component being monitored
 * @param isLoading Loading state of the component
 * @returns void
 */
const usePerformanceMonitor = (componentName: string, isLoading: boolean) => {
  const startTimeRef = useRef<number>(performance.now());
  const loadStartTimeRef = useRef<number>(performance.now());
  const renderStartTimeRef = useRef<number | null>(null);
  const metricsLoggedRef = useRef<boolean>(false);

  useEffect(() => {
    // Reset on mount
    startTimeRef.current = performance.now();
    loadStartTimeRef.current = performance.now();
    metricsLoggedRef.current = false;
    
    return () => {
      // Log final metrics on unmount if not already logged
      if (!metricsLoggedRef.current) {
        const totalTime = performance.now() - startTimeRef.current;
        console.log(`[${componentName}] Total lifecycle time: ${totalTime.toFixed(2)}ms`);
      }
    };
  }, [componentName]);

  useEffect(() => {
    if (isLoading) {
      // Start of loading phase
      loadStartTimeRef.current = performance.now();
      renderStartTimeRef.current = null;
    } else if (renderStartTimeRef.current === null) {
      // End of loading phase, start of render phase
      const loadTime = performance.now() - loadStartTimeRef.current;
      renderStartTimeRef.current = performance.now();
      
      // Schedule a check for after render is complete
      setTimeout(() => {
        const renderTime = performance.now() - (renderStartTimeRef.current || 0);
        const totalTime = performance.now() - startTimeRef.current;
        
        const metrics: PerformanceMetrics = {
          loadTime,
          renderTime,
          totalTime
        };
        
        console.log(`[${componentName}] Performance metrics:`, {
          'Data loading time': `${loadTime.toFixed(2)}ms`,
          'Rendering time': `${renderTime.toFixed(2)}ms`,
          'Total time': `${totalTime.toFixed(2)}ms`
        });
        
        metricsLoggedRef.current = true;
        
        // Send metrics to analytics if needed
        // sendMetricsToAnalytics(componentName, metrics);
      }, 0);
    }
  }, [isLoading, componentName]);
};

export default usePerformanceMonitor;
