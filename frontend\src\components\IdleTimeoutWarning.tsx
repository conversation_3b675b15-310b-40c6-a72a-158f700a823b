import React, { useEffect, useState } from 'react';
import { AlertCircle, Clock } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Progress } from '@/components/ui/progress';

interface IdleTimeoutWarningProps {
  isWarned: boolean;
  timeRemaining: number | null;
  warningTime: number; // in minutes
  onStayLoggedIn: () => void;
  onLogout: () => void;
}

const IdleTimeoutWarning: React.FC<IdleTimeoutWarningProps> = ({
  isWarned,
  timeRemaining,
  warningTime,
  onStayLoggedIn,
  onLogout,
}) => {
  const [progress, setProgress] = useState(100);

  // Calculate progress percentage
  useEffect(() => {
    if (timeRemaining !== null && warningTime > 0) {
      const totalSeconds = warningTime * 60;
      const percentage = (timeRemaining / totalSeconds) * 100;
      setProgress(Math.max(0, Math.min(100, percentage)));
    }
  }, [timeRemaining, warningTime]);

  // Format time remaining
  const formatTimeRemaining = () => {
    if (timeRemaining === null) return '';
    
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <AlertDialog open={isWarned}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            <AlertDialogTitle>Session Timeout Warning</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="pt-2">
            <p className="mb-4">
              Your session is about to expire due to inactivity. You will be automatically logged out in:
            </p>
            <div className="flex flex-col items-center justify-center py-2">
              <div className="flex items-center gap-2 text-2xl font-bold text-amber-600 mb-2">
                <Clock className="h-6 w-6" />
                <span>{formatTimeRemaining()}</span>
              </div>
              <Progress value={progress} className="h-2 w-full" />
            </div>
            <p className="mt-4 text-sm text-muted-foreground">
              Click "Stay Logged In" to continue your session, or "Logout" to end your session now.
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onLogout}>Logout</AlertDialogCancel>
          <AlertDialogAction onClick={onStayLoggedIn}>Stay Logged In</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default IdleTimeoutWarning;
