from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from django.contrib.auth.models import User
from .models import AlumniApplication, AlumniApplicationMini, ApplicationDocument
from setups.service_type.models import ServiceType
from setups.college.models import College
from setups.department.models import Department
from setups.document_type.models import DocumentType
from .security import FileSecurityValidator, sanitize_filename, generate_secure_filename
import logging

logger = logging.getLogger(__name__)


class ApplicationDocumentSerializer(serializers.ModelSerializer):
    """Serializer for application document uploads."""
    
    formatted_file_size = serializers.ReadOnlyField()
    
    class Meta:
        model = ApplicationDocument
        fields = [
            'id', 'document_type_name', 'file', 'original_filename',
            'file_size', 'formatted_file_size', 'mime_type',
            'upload_timestamp', 'uploaded_by'
        ]
        read_only_fields = ['id', 'original_filename', 'file_size', 'mime_type', 'upload_timestamp', 'uploaded_by']
    
    def validate_file(self, value):
        """Enhanced security validation for uploaded files."""
        # Initialize security validator
        validator = FileSecurityValidator()

        # Perform comprehensive security validation
        validation_result = validator.validate_file(value)

        # Log validation attempt
        logger.info(f"File validation attempt: {value.name}, Size: {value.size}, MIME: {value.content_type}")

        if not validation_result['is_valid']:
            # Log security violations
            logger.warning(f"File validation failed for {value.name}: {validation_result['errors']}")

            # Raise validation error with specific message
            error_messages = validation_result['errors']
            raise serializers.ValidationError(
                f"File validation failed: {'; '.join(error_messages)}"
            )

        # Log warnings if any
        if validation_result['warnings']:
            logger.warning(f"File validation warnings for {value.name}: {validation_result['warnings']}")

        # Store validation metadata for later use
        value._validation_result = validation_result

        return value

    def create(self, validated_data):
        """Create document with enhanced security metadata."""
        file = validated_data['file']

        # Extract validation result if available
        validation_result = getattr(file, '_validation_result', {})
        file_info = validation_result.get('file_info', {})

        # Generate secure filename
        secure_filename = generate_secure_filename(file.name)

        # Set metadata fields
        validated_data['original_filename'] = file.name
        validated_data['file_size'] = file.size
        validated_data['mime_type'] = file.content_type

        # Store security metadata in a custom field if needed
        if 'hash' in file_info:
            # You could store the hash in a custom field or log it
            logger.info(f"File hash for {file.name}: {file_info['hash']}")

        # Create the document instance
        instance = super().create(validated_data)

        # Log successful upload
        logger.info(f"Document uploaded successfully: {instance.id}, Original: {file.name}, Secure: {secure_filename}")

        return instance


class AlumniApplicationSerializer(serializers.ModelSerializer):
    """Serializer for complete alumni application form (Form1)."""

    documents = ApplicationDocumentSerializer(many=True, read_only=True)
    full_name = serializers.ReadOnlyField()
    college_name = serializers.ReadOnlyField()
    department_name = serializers.ReadOnlyField()

    # Document type information
    required_document_types = serializers.SerializerMethodField()
    required_document_types_list = serializers.ReadOnlyField()
    missing_document_types = serializers.SerializerMethodField()
    document_completion_status = serializers.SerializerMethodField()

    class Meta:
        model = AlumniApplication
        fields = [
            'id', 'first_name', 'father_name', 'last_name', 'student_id',
            'phone_number', 'email', 'admission_type', 'degree_type',
            'is_other_college', 'college', 'other_college_name',
            'department', 'other_department_name', 'student_status',
            'current_year', 'year_of_leaving_ethiopian', 'year_of_leaving_gregorian',
            'year_of_graduation_ethiopian', 'year_of_graduation_gregorian',
            'payment_status', 'transaction_id', 'application_status', 'service_type',
            'is_uog_destination', 'uog_college', 'uog_department',
            'order_type', 'institution_name', 'country', 'institution_address',
            'mailing_agent', 'created_at', 'updated_at', 'created_by', 'updated_by',
            'documents', 'full_name', 'college_name', 'department_name',
            'required_document_types', 'required_document_types_list',
            'missing_document_types', 'document_completion_status'
        ]
        read_only_fields = [
            'id', 'transaction_id', 'created_at', 'updated_at',
            'created_by', 'updated_by', 'documents', 'full_name',
            'college_name', 'department_name', 'required_document_types',
            'required_document_types_list', 'missing_document_types',
            'document_completion_status'
        ]
    
    def validate(self, data):
        """Custom validation for the entire object."""
        # Create a temporary instance for validation
        instance = AlumniApplication(**data)

        # If updating, preserve the existing instance's pk
        if self.instance:
            instance.pk = self.instance.pk

        try:
            instance.clean()
        except DjangoValidationError as e:
            # Handle both dict-style and list-style validation errors
            if hasattr(e, 'message_dict'):
                raise serializers.ValidationError(e.message_dict)
            elif hasattr(e, 'messages'):
                # For non-field errors, create a general error
                raise serializers.ValidationError({'non_field_errors': e.messages})
            else:
                # Fallback for other error formats
                raise serializers.ValidationError({'non_field_errors': [str(e)]})

        return data
    
    def validate_service_type(self, value):
        """Validate service type is active."""
        if value and not value.is_active:
            raise serializers.ValidationError("Selected service type is not active.")
        return value
    
    def validate_email(self, value):
        """Validate email uniqueness per application type."""
        if self.instance:
            # For updates, exclude current instance
            existing = AlumniApplication.objects.filter(email=value).exclude(pk=self.instance.pk)
        else:
            existing = AlumniApplication.objects.filter(email=value)

        if existing.exists():
            raise serializers.ValidationError("An application with this email already exists.")
        return value

    def get_required_document_types(self, obj):
        """Get required document types for the service type."""
        if obj.service_type:
            return DocumentTypeLookupSerializer(obj.required_document_types, many=True).data
        return []

    def get_missing_document_types(self, obj):
        """Get missing document types."""
        missing_types = obj.get_missing_document_types()
        return DocumentTypeLookupSerializer(missing_types, many=True).data

    def get_document_completion_status(self, obj):
        """Get document completion status."""
        return obj.get_document_completion_status()


class AlumniApplicationMiniSerializer(serializers.ModelSerializer):
    """Serializer for simplified alumni application form (Form2)."""

    documents = ApplicationDocumentSerializer(many=True, read_only=True)
    full_name = serializers.ReadOnlyField()
    college_name = serializers.ReadOnlyField()
    department_name = serializers.ReadOnlyField()

    # Document type information
    required_document_types = serializers.SerializerMethodField()
    required_document_types_list = serializers.ReadOnlyField()
    missing_document_types = serializers.SerializerMethodField()
    document_completion_status = serializers.SerializerMethodField()

    class Meta:
        model = AlumniApplicationMini
        fields = [
            'id', 'first_name', 'father_name', 'last_name', 'student_id',
            'phone_number', 'email', 'admission_type', 'degree_type',
            'is_other_college', 'college', 'other_college_name',
            'department', 'other_department_name', 'student_status',
            'current_year', 'year_of_leaving_ethiopian', 'year_of_leaving_gregorian',
            'year_of_graduation_ethiopian', 'year_of_graduation_gregorian',
            'payment_status', 'transaction_id', 'application_status', 'service_type',
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'documents', 'full_name', 'college_name', 'department_name',
            'required_document_types', 'required_document_types_list',
            'missing_document_types', 'document_completion_status'
        ]
        read_only_fields = [
            'id', 'transaction_id', 'created_at', 'updated_at',
            'created_by', 'updated_by', 'documents', 'full_name',
            'college_name', 'department_name', 'required_document_types',
            'required_document_types_list', 'missing_document_types',
            'document_completion_status'
        ]
    
    def validate(self, data):
        """Custom validation for the entire object."""
        # Create a temporary instance for validation
        instance = AlumniApplicationMini(**data)

        # If updating, preserve the existing instance's pk
        if self.instance:
            instance.pk = self.instance.pk

        try:
            instance.clean()
        except DjangoValidationError as e:
            # Handle both dict-style and list-style validation errors
            if hasattr(e, 'message_dict'):
                raise serializers.ValidationError(e.message_dict)
            elif hasattr(e, 'messages'):
                # For non-field errors, create a general error
                raise serializers.ValidationError({'non_field_errors': e.messages})
            else:
                # Fallback for other error formats
                raise serializers.ValidationError({'non_field_errors': [str(e)]})

        return data
    
    def validate_service_type(self, value):
        """Validate service type is active."""
        if value and not value.is_active:
            raise serializers.ValidationError("Selected service type is not active.")
        return value
    
    def validate_email(self, value):
        """Validate email uniqueness per application type."""
        if self.instance:
            # For updates, exclude current instance
            existing = AlumniApplicationMini.objects.filter(email=value).exclude(pk=self.instance.pk)
        else:
            existing = AlumniApplicationMini.objects.filter(email=value)

        if existing.exists():
            raise serializers.ValidationError("An application with this email already exists.")
        return value

    def get_required_document_types(self, obj):
        """Get required document types for the service type."""
        if obj.service_type:
            return DocumentTypeLookupSerializer(obj.required_document_types, many=True).data
        return []

    def get_missing_document_types(self, obj):
        """Get missing document types."""
        missing_types = obj.get_missing_document_types()
        return DocumentTypeLookupSerializer(missing_types, many=True).data

    def get_document_completion_status(self, obj):
        """Get document completion status."""
        return obj.get_document_completion_status()


# Lookup serializers for dropdown population
class DocumentTypeLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for document type lookups."""

    class Meta:
        model = DocumentType
        fields = ['id', 'name', 'description', 'is_active']


class ServiceTypeLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for service type lookups."""

    required_document_types = DocumentTypeLookupSerializer(
        source='document_types', many=True, read_only=True
    )
    required_document_types_count = serializers.SerializerMethodField()

    class Meta:
        model = ServiceType
        fields = ['id', 'name', 'fee', 'description', 'required_document_types', 'required_document_types_count']

    def get_required_document_types_count(self, obj):
        """Get count of required document types."""
        return obj.document_types.filter(is_active=True).count()


class CollegeLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for college lookups."""

    class Meta:
        model = College
        fields = ['id', 'name', 'description']


class DepartmentLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for department lookups."""

    college_name = serializers.CharField(source='college.name', read_only=True)

    class Meta:
        model = Department
        fields = ['id', 'name', 'college', 'college_name', 'description']


# List serializers for performance optimization
class AlumniApplicationListSerializer(serializers.ModelSerializer):
    """Optimized serializer for listing Form1 applications."""

    full_name = serializers.ReadOnlyField()
    service_type_name = serializers.CharField(source='service_type.name', read_only=True)
    college_name = serializers.ReadOnlyField()
    document_completion_status = serializers.SerializerMethodField()

    class Meta:
        model = AlumniApplication
        fields = [
            'id', 'full_name', 'email', 'phone_number', 'student_id',
            'service_type_name', 'college_name', 'application_status',
            'payment_status', 'transaction_id', 'created_at',
            'document_completion_status'
        ]

    def get_document_completion_status(self, obj):
        """Get document completion status."""
        return obj.get_document_completion_status()


class AlumniApplicationMiniListSerializer(serializers.ModelSerializer):
    """Optimized serializer for listing Form2 applications."""

    full_name = serializers.ReadOnlyField()
    service_type_name = serializers.CharField(source='service_type.name', read_only=True)
    college_name = serializers.ReadOnlyField()
    document_completion_status = serializers.SerializerMethodField()

    class Meta:
        model = AlumniApplicationMini
        fields = [
            'id', 'full_name', 'email', 'phone_number', 'student_id',
            'service_type_name', 'college_name', 'application_status',
            'payment_status', 'transaction_id', 'created_at',
            'document_completion_status'
        ]

    def get_document_completion_status(self, obj):
        """Get document completion status."""
        return obj.get_document_completion_status()


# Status update serializers
class ApplicationStatusSerializer(serializers.ModelSerializer):
    """Serializer for updating application and payment status."""

    class Meta:
        fields = ['application_status', 'payment_status']

    def validate_application_status(self, value):
        """Validate application status values."""
        valid_statuses = ['Pending', 'On Review', 'Processing', 'Complete']
        if value not in valid_statuses:
            raise serializers.ValidationError(
                f"Invalid application status. Must be one of: {', '.join(valid_statuses)}"
            )
        return value

    def validate_payment_status(self, value):
        """Validate payment status values."""
        valid_statuses = ['Pending', 'Initiated', 'Processing', 'Completed', 'Failed', 'Expired', 'Refunded', 'Cancelled']
        if value not in valid_statuses:
            raise serializers.ValidationError(
                f"Invalid payment status. Must be one of: {', '.join(valid_statuses)}"
            )
        return value


class AlumniApplicationStatusSerializer(ApplicationStatusSerializer):
    """Status serializer for AlumniApplication."""

    class Meta(ApplicationStatusSerializer.Meta):
        model = AlumniApplication


class AlumniApplicationMiniStatusSerializer(ApplicationStatusSerializer):
    """Status serializer for AlumniApplicationMini."""

    class Meta(ApplicationStatusSerializer.Meta):
        model = AlumniApplicationMini
