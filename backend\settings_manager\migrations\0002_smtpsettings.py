# Generated by Django 5.2.1 on 2025-06-07 13:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_manager', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SMTPSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('smtp_host', models.CharField(help_text='SMTP server hostname (e.g., smtp.gmail.com)', max_length=255, verbose_name='SMTP Host')),
                ('smtp_port', models.IntegerField(default=587, help_text='SMTP server port (587 for TLS, 465 for SSL, 25 for plain)', verbose_name='SMTP Port')),
                ('smtp_username', models.Char<PERSON>ield(help_text='SMTP authentication username', max_length=255, verbose_name='SMTP Username')),
                ('smtp_password', models.<PERSON>r<PERSON><PERSON>(help_text='SMTP authentication password', max_length=255, verbose_name='SMTP Password')),
                ('smtp_use_tls', models.BooleanField(default=True, help_text='Enable TLS encryption (recommended for port 587)', verbose_name='Use TLS')),
                ('smtp_use_ssl', models.BooleanField(default=False, help_text='Enable SSL encryption (recommended for port 465)', verbose_name='Use SSL')),
                ('smtp_from_email', models.EmailField(help_text='Email address used as sender', max_length=254, verbose_name='From Email')),
                ('smtp_from_name', models.CharField(blank=True, help_text='Display name for sent emails', max_length=255, verbose_name='From Name')),
                ('smtp_timeout', models.IntegerField(default=30, help_text='SMTP connection timeout in seconds', verbose_name='Timeout (seconds)')),
                ('smtp_enabled', models.BooleanField(default=True, help_text='Enable or disable email functionality', verbose_name='Enable SMTP')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'SMTP Settings',
                'verbose_name_plural': 'SMTP Settings',
            },
        ),
    ]
