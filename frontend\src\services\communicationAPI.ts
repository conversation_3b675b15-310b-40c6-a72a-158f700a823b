import axios from 'axios';
import { API_BASE_URL } from '@/config';
import { getAuthHeader } from '@/utils/auth';

// Types
export interface Announcement {
  id: number;
  title: string;
  content: string;
  author: number;
  author_details?: {
    id: number;
    username: string;
    email: string;
    full_name: string;
  };
  priority: 'low' | 'medium' | 'high';
  target_audience: 'all' | 'students' | 'staff' | 'applicants';
  is_active: boolean;
  start_date: string;
  end_date: string | null;
  created_at: string;
  updated_at: string;
  is_expired: boolean;
}

export interface EmailNotification {
  id: number;
  subject: string;
  content: string;
  sender: number;
  sender_details?: {
    id: number;
    username: string;
    email: string;
    full_name: string;
  };
  recipients: string;
  status: 'draft' | 'scheduled' | 'sent' | 'failed';
  scheduled_time: string | null;
  sent_time: string | null;
  created_at: string;
  updated_at: string;
}

export interface SMSNotification {
  id: number;
  message: string;
  sender: number;
  sender_details?: {
    id: number;
    username: string;
    email: string;
    full_name: string;
  };
  recipients: string;
  status: 'draft' | 'scheduled' | 'sent' | 'failed';
  scheduled_time: string | null;
  sent_time: string | null;
  created_at: string;
  updated_at: string;
  message_length: number;
}

// API functions
const communicationAPI = {
  // Announcements
  getAnnouncements: async () => {
    try {
      console.log('Fetching announcements from:', `${API_BASE_URL}/communication/announcements/`);
      const response = await axios.get(`${API_BASE_URL}/communication/announcements/`, {
        headers: getAuthHeader()
      });
      console.log('Announcements response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching announcements:', error);
      // Return empty array on error
      return [];
    }
  },

  getActiveAnnouncements: async (audience?: string) => {
    const url = audience
      ? `${API_BASE_URL}/communication/announcements/active/?audience=${audience}`
      : `${API_BASE_URL}/communication/announcements/active/`;

    const response = await axios.get(url, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  getAnnouncement: async (id: number) => {
    const response = await axios.get(`${API_BASE_URL}/communication/announcements/${id}/`, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  createAnnouncement: async (data: Partial<Announcement>) => {
    const response = await axios.post(`${API_BASE_URL}/communication/announcements/`, data, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  updateAnnouncement: async (id: number, data: Partial<Announcement>) => {
    const response = await axios.patch(`${API_BASE_URL}/communication/announcements/${id}/`, data, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  deleteAnnouncement: async (id: number) => {
    const response = await axios.delete(`${API_BASE_URL}/communication/announcements/${id}/`, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  // Email Notifications
  getEmailNotifications: async () => {
    try {
      console.log('Fetching email notifications from:', `${API_BASE_URL}/communication/emails/`);
      const response = await axios.get(`${API_BASE_URL}/communication/emails/`, {
        headers: getAuthHeader()
      });
      console.log('Email notifications response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching email notifications:', error);
      // Return empty array on error
      return [];
    }
  },

  getEmailNotification: async (id: number) => {
    const response = await axios.get(`${API_BASE_URL}/communication/emails/${id}/`, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  createEmailNotification: async (data: Partial<EmailNotification>) => {
    try {
      console.log('Create email notification request:', {
        url: `${API_BASE_URL}/communication/emails/`,
        data,
        headers: getAuthHeader()
      });

      const response = await axios.post(`${API_BASE_URL}/communication/emails/`, data, {
        headers: getAuthHeader()
      });

      console.log('Create email notification response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Create email notification error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        fullError: error
      });
      throw error;
    }
  },

  updateEmailNotification: async (id: number, data: Partial<EmailNotification>) => {
    try {
      console.log('Update email notification request:', {
        id,
        url: `${API_BASE_URL}/communication/emails/${id}/`,
        data,
        headers: getAuthHeader()
      });

      const response = await axios.patch(`${API_BASE_URL}/communication/emails/${id}/`, data, {
        headers: getAuthHeader()
      });

      console.log('Update email notification response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update email notification error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        fullError: error
      });
      throw error;
    }
  },

  deleteEmailNotification: async (id: number) => {
    const response = await axios.delete(`${API_BASE_URL}/communication/emails/${id}/`, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  sendEmailNotification: async (id: number) => {
    const response = await axios.post(`${API_BASE_URL}/communication/emails/${id}/send/`, {}, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  // Bulk operations for email notifications
  bulkSendEmailNotifications: async (notificationIds: number[]) => {
    try {
      console.log('Bulk send request:', {
        url: `${API_BASE_URL}/communication/emails/bulk_send/`,
        data: { notification_ids: notificationIds },
        headers: getAuthHeader()
      });

      const response = await axios.post(`${API_BASE_URL}/communication/emails/bulk_send/`, {
        notification_ids: notificationIds
      }, {
        headers: getAuthHeader()
      });

      console.log('Bulk send response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Bulk send error details:', error.response?.data || error.message);
      throw error;
    }
  },

  bulkDeleteEmailNotifications: async (notificationIds: number[]) => {
    try {
      console.log('Bulk delete request:', {
        url: `${API_BASE_URL}/communication/emails/bulk_delete/`,
        data: { notification_ids: notificationIds },
        headers: getAuthHeader()
      });

      const response = await axios.delete(`${API_BASE_URL}/communication/emails/bulk_delete/`, {
        data: { notification_ids: notificationIds },
        headers: getAuthHeader()
      });

      console.log('Bulk delete response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Bulk delete error details:', error.response?.data || error.message);
      throw error;
    }
  },

  getEmailNotificationStatistics: async () => {
    const response = await axios.get(`${API_BASE_URL}/communication/emails/statistics/`, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  // SMS Notifications
  getSMSNotifications: async () => {
    try {
      console.log('Fetching SMS notifications from:', `${API_BASE_URL}/communication/sms/`);
      const response = await axios.get(`${API_BASE_URL}/communication/sms/`, {
        headers: getAuthHeader()
      });
      console.log('SMS notifications response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching SMS notifications:', error);
      // Return empty array on error
      return [];
    }
  },

  getSMSNotification: async (id: number) => {
    const response = await axios.get(`${API_BASE_URL}/communication/sms/${id}/`, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  createSMSNotification: async (data: Partial<SMSNotification>) => {
    const response = await axios.post(`${API_BASE_URL}/communication/sms/`, data, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  updateSMSNotification: async (id: number, data: Partial<SMSNotification>) => {
    const response = await axios.patch(`${API_BASE_URL}/communication/sms/${id}/`, data, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  deleteSMSNotification: async (id: number) => {
    const response = await axios.delete(`${API_BASE_URL}/communication/sms/${id}/`, {
      headers: getAuthHeader()
    });
    return response.data;
  },

  sendSMSNotification: async (id: number) => {
    const response = await axios.post(`${API_BASE_URL}/communication/sms/${id}/send/`, {}, {
      headers: getAuthHeader()
    });
    return response.data;
  }
};

export default communicationAPI;
