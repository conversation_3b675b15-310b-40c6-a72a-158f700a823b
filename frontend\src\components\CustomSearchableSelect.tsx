import React, { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Search, X } from 'lucide-react';

interface Option {
  id: number | string;
  name: string;
  [key: string]: any;
}

interface CustomSearchableSelectProps {
  options: Option[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder: string;
  className?: string;
  error?: string;
  id: string;
  searchPlaceholder?: string;
}

const CustomSearchableSelect: React.FC<CustomSearchableSelectProps> = ({
  options,
  value,
  onValueChange,
  placeholder,
  className = '',
  error,
  id,
  searchPlaceholder = 'Search...'
}) => {
  // State
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Ensure options is an array before using array methods
  const optionsArray = Array.isArray(options) ? options : [];

  // Log options for debugging
  console.log(`CustomSearchableSelect ${id} options:`, optionsArray);
  console.log(`CustomSearchableSelect ${id} options length:`, optionsArray.length);

  // Get selected option name
  const selectedOption = optionsArray.find(option => {
    if (!option || typeof option !== 'object') return false;
    return option.id?.toString() === value;
  });
  const displayText = selectedOption?.name || placeholder;

  // Filter options based on search term
  const filteredOptions = searchTerm.trim() === ''
    ? optionsArray
    : optionsArray.filter(option => {
        if (!option || typeof option !== 'object' || !option.name) return false;
        return option.name.toLowerCase().includes(searchTerm.toLowerCase());
      });

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest(`#${id}-container`)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, id]);

  // Handle option selection
  const handleSelectOption = (optionId: string) => {
    onValueChange(optionId);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div id={`${id}-container`} className="relative w-full">
      {/* Trigger button */}
      <button
        type="button"
        id={id}
        className={`flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 text-sm ${className}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={`${!selectedOption ? 'text-muted-foreground' : ''}`}>
          {displayText}
        </span>
        <ChevronDown className="h-4 w-4 opacity-50" />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-1 w-full rounded-md border bg-white shadow-lg">
          {/* Search input */}
          <div className="p-2 sticky top-0 bg-white border-b border-gray-100">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />

              {searchTerm && (
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSearchTerm('');
                  }}
                >
                  <X className="h-4 w-4" />
                </button>
              )}

              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={searchPlaceholder}
                className="pl-8 pr-8 py-1 h-8 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                onClick={(e) => e.stopPropagation()}
                autoComplete="off"
                spellCheck="false"
              />
            </div>
          </div>

          {/* Options list */}
          <div className="max-h-[200px] overflow-y-auto p-1">
            {filteredOptions.length > 0 ? (
              <div className="py-1">
                {filteredOptions.map((option) => (
                  <div
                    key={option.id || Math.random()}
                    className="px-2 py-1.5 text-sm rounded-sm hover:bg-blue-50 cursor-pointer"
                    onClick={() => handleSelectOption(option.id?.toString() || '')}
                  >
                    {option.name || 'Unnamed Option'}
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-3 px-2 text-center text-gray-500 italic text-sm">
                No results matching your search
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <p className="text-red-500 text-xs mt-1 flex items-center">
          <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
};

export default CustomSearchableSelect;
