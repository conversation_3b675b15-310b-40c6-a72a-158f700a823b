from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import ServiceType
from setups.document_type.models import DocumentType


class DocumentTypeBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for DocumentType to avoid circular imports."""

    class Meta:
        model = DocumentType
        fields = ['id', 'name', 'is_active']


class ServiceTypeSerializer(serializers.ModelSerializer):
    """Serializer for ServiceType with nested document type information."""

    document_types = DocumentTypeBasicSerializer(many=True, read_only=True)
    document_type_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        required=False,
        allow_empty=True,
        help_text="List of document type IDs to associate with this service"
    )
    document_types_count = serializers.ReadOnlyField()
    active_document_types_count = serializers.ReadOnlyField()
    
    class Meta:
        model = ServiceType
        fields = [
            'id', 'name', 'description', 'fee', 'url', 'is_active', 'created_at', 'updated_at',
            'document_types', 'document_type_ids', 'document_types_count',
            'active_document_types_count'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate service type name."""
        if not value or not value.strip():
            raise serializers.ValidationError("Service type name is required.")
        
        value = value.strip()
        if len(value) < 2:
            raise serializers.ValidationError("Service type name must be at least 2 characters long.")
        
        # Check for duplicate names (case-insensitive)
        instance_id = self.instance.id if self.instance else None
        existing = ServiceType.objects.filter(
            name__iexact=value
        ).exclude(pk=instance_id)
        
        if existing.exists():
            raise serializers.ValidationError("A service type with this name already exists.")
        
        return value

    def validate_fee(self, value):
        """Validate service fee."""
        if value is not None:
            if value < 0:
                raise serializers.ValidationError("Service fee cannot be negative.")
            if value > 99999999.99:
                raise serializers.ValidationError("Service fee is too large.")
        return value

    def validate_document_type_ids(self, value):
        """Validate document type IDs."""
        if value:
            # Check if all provided IDs exist (allow both active and inactive)
            existing_ids = set(
                DocumentType.objects.filter(
                    id__in=value
                ).values_list('id', flat=True)
            )

            invalid_ids = set(value) - existing_ids
            if invalid_ids:
                raise serializers.ValidationError(
                    f"Invalid document type IDs: {list(invalid_ids)}"
                )

        return value

    def create(self, validated_data):
        """Create a new service type with associated document types."""
        document_type_ids = validated_data.pop('document_type_ids', [])

        try:
            service_type = ServiceType.objects.create(**validated_data)

            if document_type_ids:
                document_types = DocumentType.objects.filter(
                    id__in=document_type_ids
                )
                service_type.document_types.set(document_types)

            return service_type
        except DjangoValidationError as e:
            raise serializers.ValidationError(e.message_dict)

    def update(self, instance, validated_data):
        """Update service type with associated document types."""
        document_type_ids = validated_data.pop('document_type_ids', None)
        
        try:
            # Update basic fields
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            
            instance.full_clean()
            instance.save()
            
            # Update document types if provided
            if document_type_ids is not None:
                if document_type_ids:
                    document_types = DocumentType.objects.filter(
                        id__in=document_type_ids
                    )
                    instance.document_types.set(document_types)
                else:
                    instance.document_types.clear()
            
            return instance
        except DjangoValidationError as e:
            raise serializers.ValidationError(e.message_dict)


class ServiceTypeListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing service types."""

    document_types = DocumentTypeBasicSerializer(many=True, read_only=True)
    document_types_count = serializers.ReadOnlyField()
    active_document_types_count = serializers.ReadOnlyField()

    class Meta:
        model = ServiceType
        fields = [
            'id', 'name', 'description', 'fee', 'url', 'is_active', 'created_at', 'updated_at',
            'document_types', 'document_types_count', 'active_document_types_count'
        ]
