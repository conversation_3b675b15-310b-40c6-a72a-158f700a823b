from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ServiceRequestViewSet,
    DocumentUploadViewSet,
    ServiceTypeLookupViewSet,
    AdmissionTypeLookupViewSet,
    StudyProgramLookupViewSet,
    CollegeLookupViewSet,
    DepartmentLookupViewSet,
    CertificateTypeLookupViewSet
)

# Create router and register viewsets
router = DefaultRouter()

# Authenticated service request endpoints
router.register(r'service-requests', ServiceRequestViewSet, basename='servicerequest')
router.register(r'document-uploads', DocumentUploadViewSet, basename='documentupload')

# Lookup endpoints for dropdown population (public access)
router.register(r'lookups/service-types', ServiceTypeLookupViewSet, basename='servicetype-lookup')
router.register(r'lookups/admission-types', AdmissionTypeLookupViewSet, basename='admissiontype-lookup')
router.register(r'lookups/study-programs', StudyProgramLookupViewSet, basename='studyprogram-lookup')
router.register(r'lookups/colleges', CollegeLookupViewSet, basename='college-lookup')
router.register(r'lookups/departments', DepartmentLookupViewSet, basename='department-lookup')
router.register(r'lookups/certificate-types', CertificateTypeLookupViewSet, basename='certificatetype-lookup')

urlpatterns = [
    path('', include(router.urls)),

    # Additional public endpoints - REMOVED
]
