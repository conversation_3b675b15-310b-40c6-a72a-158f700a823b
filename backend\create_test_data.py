#!/usr/bin/env python
"""
Create test data for registration periods
"""

import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# Add the backend directory to the Python path
backend_path = os.path.dirname(__file__)
sys.path.insert(0, backend_path)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.registration_period.models import RegistrationPeriod
from setups.year.models import Year
from setups.program.models import Program
from setups.college.models import College
from setups.department.models import Department
from setups.study_program.models import StudyProgram
from setups.study_field.models import StudyField
from setups.admission_type.models import AdmissionType

def create_test_data():
    """Create test data for registration periods"""
    try:
        print("=== Creating Test Data ===")

        # Create simple test program
        program, created = Program.objects.get_or_create(
            program_code="TEST-CS",
            defaults={
                'program_name': 'Test Computer Science Program',
                'registration_fee': 1000.00
            }
        )
        print(f"Program: {program.program_name} ({'created' if created else 'exists'})")
        
        # Get the year
        year = Year.objects.first()
        if not year:
            print("❌ No year found in database")
            return
        
        print(f"Year: {year.year}")
        
        # Create registration period
        registration_period, created = RegistrationPeriod.objects.get_or_create(
            program=program,
            year=year,
            defaults={
                'open_date': timezone.now() + timedelta(days=1),
                'close_date': timezone.now() + timedelta(days=30),
                'is_active': True
            }
        )
        print(f"Registration Period: {registration_period} ({'created' if created else 'exists'})")
        
        print("\n✅ Test data created successfully!")
        
        # Test the serializer
        from setups.registration_period.serializers import RegistrationPeriodSerializer
        serializer = RegistrationPeriodSerializer(registration_period)
        print(f"\nSerialized data: {serializer.data}")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_test_data()
