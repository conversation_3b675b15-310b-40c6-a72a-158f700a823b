import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      position="top-left"
      expand={true}
      richColors={true}
      duration={3000}
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-green-500 group-[.toaster]:text-white group-[.toaster]:border-green-600 group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-white",
          actionButton:
            "group-[.toast]:bg-green-700 group-[.toast]:text-white",
          cancelButton:
            "group-[.toast]:bg-green-700 group-[.toast]:text-white",
          success: "group-[.toast]:bg-green-500 group-[.toast]:text-white group-[.toast]:border-green-600",
          error: "group-[.toast]:bg-green-500 group-[.toast]:text-white group-[.toast]:border-green-600",
          warning: "group-[.toast]:bg-green-500 group-[.toast]:text-white group-[.toast]:border-green-600",
          info: "group-[.toast]:bg-green-500 group-[.toast]:text-white group-[.toast]:border-green-600",
        },
      }}
      {...props}
    />
  )
}

export { Toaster }
