import React, { ReactNode, useEffect, useState, useMemo } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useSimpleRBAC as useRBAC } from '@/contexts/SimpleRBACContext';
import { toast } from 'sonner';
import { AlertTriangle, Shield, UserX, Lock } from 'lucide-react';

interface EnhancedAccessControlProps {
  children: ReactNode;
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  requirePermissions?: string[];
  requireGroups?: string[];
  allowStaffWithoutGroups?: boolean;
  redirectTo?: string;
  showMessage?: boolean;
}

/**
 * Enhanced Access Control Component
 * Implements comprehensive user access control based on:
 * - Authentication status
 * - User active status
 * - Staff/Superuser privileges
 * - Group membership
 * - Specific permissions
 */
export const EnhancedAccessControl: React.FC<EnhancedAccessControlProps> = ({
  children,
  requireStaff = false,
  requireSuperuser = false,
  requirePermissions = [],
  requireGroups = [],
  allowStaffWithoutGroups = false,
  redirectTo,
  showMessage = true
}) => {
  const { user, isAuthenticated, isLoading, hasPermission, hasRole, hasAnyRole } = useAuth();
  const {
    isSuperuser,
    isStaff,
    isRegularUser,
    canAccessAdmin,
    isLoading: rbacLoading
  } = useRBAC();
  const location = useLocation();

  // Memoize access control decision to prevent infinite re-renders
  const accessControlResult = useMemo(() => {
    // Show loading state
    if (isLoading || rbacLoading) {
      return {
        type: 'loading',
        component: (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-sm text-muted-foreground">Verifying access...</p>
            </div>
          </div>
        )
      };
    }

    // Check 1: Not logged in
    if (!isAuthenticated || !user) {
      console.log('🔒 Access Control: User not authenticated');
      return {
        type: 'redirect',
        component: <Navigate to="/login" replace />
      };
    }

    // Check 2: User not active
    if (!user.is_active) {
      console.log('🔒 Access Control: User account is inactive');
      if (showMessage) {
        toast.error('Your account is inactive. Please contact an administrator.');
      }
      return {
        type: 'redirect',
        component: <Navigate to="/login" replace />
      };
    }

    // Check 3: Superuser access (bypass all other checks)
    if (user.is_superuser) {
      console.log('✅ Access Control: Superuser access granted');
      return {
        type: 'allow',
        component: children
      };
    }

    // Check 4: Regular user trying to access admin areas
    if ((requireStaff || requireSuperuser) && !user.is_staff && !user.is_superuser) {
      console.log('🔒 Access Control: Regular user trying to access admin area');
      return {
        type: 'redirect',
        component: <Navigate to="/dashboard" replace />
      };
    }

    // Check 5: Superuser-only areas
    if (requireSuperuser && !user.is_superuser) {
      console.log('🔒 Access Control: Non-superuser trying to access superuser-only area');

      if (showMessage) {
        return {
          type: 'deny',
          component: (
            <div className="container mx-auto px-4 py-8">
              <div className="max-w-md mx-auto text-center">
                <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h2 className="text-xl font-semibold mb-2">Superuser Access Required</h2>
                <p className="text-muted-foreground mb-4">
                  This area requires superuser privileges. Contact your system administrator if you need access.
                </p>
                <button
                  onClick={() => window.history.back()}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Go Back
                </button>
              </div>
            </div>
          )
        };
      }
      return {
        type: 'redirect',
        component: <Navigate to={redirectTo || "/graduate-admin"} replace />
      };
    }

    // Check 6: STRICT ACCESS CONTROL - Non-superusers must have explicit permissions
    if (!user.is_superuser && requireStaff && user.is_staff) {
      const userHasGroups = user.role_names && user.role_names.length > 0;
      const userHasPermissions = user.permissions && user.permissions.length > 0;

      // DENY BY DEFAULT: Staff users must have groups or permissions unless explicitly allowed
      if (!userHasGroups && !userHasPermissions && !allowStaffWithoutGroups) {
        console.log('🔒 Access Control: Staff user without groups or permissions - STRICT MODE');

        if (showMessage) {
          return {
            type: 'deny',
            component: (
              <div className="container mx-auto px-4 py-8">
                <div className="max-w-md mx-auto text-center">
                  <UserX className="h-12 w-12 text-orange-500 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold mb-2">Access Restricted - Strict Mode</h2>
                  <p className="text-muted-foreground mb-4">
                    Your staff account does not have the required groups or permissions to access this area.
                    In strict mode, all access must be explicitly granted through groups or permissions.
                  </p>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                    <p className="text-sm text-orange-800">
                      <strong>Security Policy:</strong> Non-superusers require explicit permissions
                    </p>
                    <p className="text-sm text-orange-800 mt-1">
                      <strong>Account Status:</strong> Staff member without assigned roles
                    </p>
                  </div>
                  <button
                    onClick={() => window.history.back()}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Go Back
                  </button>
                </div>
              </div>
            )
          };
        }
        return {
          type: 'redirect',
          component: <Navigate to={redirectTo || "/dashboard"} replace />
        };
      }
    }

    // Check 7: Specific permission requirements
    if (requirePermissions.length > 0) {
      const hasRequiredPermissions = requirePermissions.some(permission =>
        hasPermission(permission)
      );

      if (!hasRequiredPermissions) {
        console.log('🔒 Access Control: Missing required permissions', requirePermissions);

        if (showMessage) {
          return {
            type: 'deny',
            component: (
              <div className="container mx-auto px-4 py-8">
                <div className="max-w-md mx-auto text-center">
                  <Lock className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold mb-2">Insufficient Permissions</h2>
                  <p className="text-muted-foreground mb-4">
                    You don't have the required permissions to access this area.
                  </p>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                    <p className="text-sm text-red-800">
                      <strong>Required:</strong> {requirePermissions.join(', ')}
                    </p>
                  </div>
                  <button
                    onClick={() => window.history.back()}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Go Back
                  </button>
                </div>
              </div>
            )
          };
        }
        return {
          type: 'redirect',
          component: <Navigate to={redirectTo || "/graduate-admin"} replace />
        };
      }
    }

    // Check 8: Specific group requirements
    if (requireGroups.length > 0) {
      const hasRequiredGroups = hasAnyRole(requireGroups);

      if (!hasRequiredGroups) {
        console.log('🔒 Access Control: Missing required groups', requireGroups);

        if (showMessage) {
          return {
            type: 'deny',
            component: (
              <div className="container mx-auto px-4 py-8">
                <div className="max-w-md mx-auto text-center">
                  <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold mb-2">Group Access Required</h2>
                  <p className="text-muted-foreground mb-4">
                    You need to be a member of specific groups to access this area.
                  </p>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                    <p className="text-sm text-yellow-800">
                      <strong>Required Groups:</strong> {requireGroups.join(', ')}
                    </p>
                  </div>
                  <button
                    onClick={() => window.history.back()}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Go Back
                  </button>
                </div>
              </div>
            )
          };
        }
        return {
          type: 'redirect',
          component: <Navigate to={redirectTo || "/graduate-admin"} replace />
        };
      }
    }

    // All checks passed - grant access
    console.log('✅ Access Control: All checks passed, granting access');
    return {
      type: 'allow',
      component: children
    };
  }, [
    isLoading,
    rbacLoading,
    isAuthenticated,
    user,
    requireStaff,
    requireSuperuser,
    requirePermissions,
    requireGroups,
    allowStaffWithoutGroups,
    showMessage,
    redirectTo,
    children,
    hasPermission,
    hasAnyRole
  ]);

  // Return the memoized result
  return <>{accessControlResult.component}</>;
};

// Convenience components for common access patterns
export const AdminAccess: React.FC<{ children: ReactNode }> = ({ children }) => (
  <EnhancedAccessControl requireStaff>
    {children}
  </EnhancedAccessControl>
);

export const SuperuserAccess: React.FC<{ children: ReactNode }> = ({ children }) => (
  <EnhancedAccessControl requireSuperuser>
    {children}
  </EnhancedAccessControl>
);

export const StaffWithGroupsAccess: React.FC<{ children: ReactNode }> = ({ children }) => (
  <EnhancedAccessControl requireStaff allowStaffWithoutGroups={false}>
    {children}
  </EnhancedAccessControl>
);

export default EnhancedAccessControl;
