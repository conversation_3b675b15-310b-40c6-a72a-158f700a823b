from django.contrib import admin
from .models import Downloadable


@admin.register(Downloadable)
class DownloadableAdmin(admin.ModelAdmin):
    list_display = ['title', 'file', 'is_active', 'created_at', 'updated_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title']
    readonly_fields = ['id', 'created_at', 'updated_at']
    fieldsets = (
        ('File Information', {
            'fields': ('title', 'file', 'is_active')
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
