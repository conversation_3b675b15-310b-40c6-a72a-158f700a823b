import { useState, useEffect } from 'react';
import AdminLayout from '@/components/NewAdminLayout';
// StaffLevelRoute removed - using simplified system
import { useLocation, useNavigate } from 'react-router-dom';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, BarChart3, Building, Settings, School } from 'lucide-react';
import { useSimpleRBAC as useRBAC } from '@/contexts/SimpleRBACContext';
import DocumentTitle from '@/components/DocumentTitle';
import GraduateManagement from '@/components/GraduateManagement';
import GraduateDashboard from '@/components/GraduateDashboard';
import CertificateTypeManagement from '@/components/CertificateTypeManagement';

import ServiceTypeManagement from '@/components/ServiceTypeManagement';
import DocumentTypeManagement from '@/components/DocumentTypeManagement';

import AlumniApplicationsManagement from '@/components/AlumniApplicationsManagement';
import OfficialManagement from '@/components/OfficialManagement';
import ApplicationDashboard from '@/components/ApplicationDashboard';
import { AccessControlDemo } from '@/components/AccessControlDemo';
import { UserSimulator } from '@/components/UserSimulator';
import { PermissionMatrix } from '@/components/PermissionMatrix';
import { UserDebug } from '@/components/UserDebug';
import { DynamicAccessDemo } from '@/components/DynamicAccessDemo';
import { PermissionSystemDemo } from '@/components/PermissionSystemDemo';
import { PermissionSystemDemo } from '@/components/PermissionSystemDemo';
import ServiceFeeDashboard from '@/components/ServiceFeeDashboard';
import AdminSettings from '@/components/AdminSettings';
import CollegeManagement from '@/components/CollegeManagement';
import DepartmentManagement from '@/components/DepartmentManagement';
import ApplicationInformationView from '@/components/ApplicationInformationView';

// Import components for routes that need to be integrated
import GraduateVerificationView from '@/pages/GraduateVerification';

import GraduateFieldOfStudyManagement from '@/components/GraduateFieldOfStudyManagement';
import AdmissionClassificationManagement from '@/components/AdmissionClassificationManagement';
import ProgramManagement from '@/components/ProgramManagement';
import ApplicationCollegeManagement from '@/components/ApplicationCollegeManagement';
import ApplicationDepartmentManagement from '@/components/ApplicationDepartmentManagement';
import ApplicationProgramManagement from '@/components/ApplicationProgramManagement';
import ApplicationStudyProgramManagement from '@/components/ApplicationStudyProgramManagement';
import ApplicationAdmissionTypeManagement from '@/components/ApplicationAdmissionTypeManagement';

import ApplicationRegistrationPeriodManagement from '@/components/ApplicationRegistrationPeriodManagement';
import ApplicationFieldOfStudyManagement from '@/components/ApplicationFieldOfStudyManagement';
import YearManagement from '@/components/YearManagement';
import TermManagement from '@/components/TermManagement';

// User Management Components
import ApplicantManagement from '@/components/ApplicantManagement';
import SimpleDjangoAuthManagement from '@/pages/SimpleDjangoAuthManagement';

// Communication Components
import Announcements from '@/components/Announcements';
import EmailNotifications from '@/components/EmailNotifications';
import SMSNotifications from '@/components/SMSNotifications';
import MessageCenter from '@/components/MessageCenter';

// Settings Components
import SettingsPage from '@/pages/SettingsPage';

// Downloadable Components
import DownloadableManagement from '@/components/DownloadableManagement';

// Security Components
import SecurityMonitor from '@/components/security/SecurityMonitor';
import UserAudit from '@/components/security/UserAudit';
import RBACTest from '@/components/debug/RBACTest';




const GraduateAdmin = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('dashboard');

  // Use simple RBAC context
  const { isSuperuser, isAdmin, canAccessAdmin } = useRBAC();

  // Set active tab based on URL query parameter
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    if (tab && [
      'dashboard', 'application-dashboard', 'service-fee-dashboard',
      'manage', 'colleges', 'departments', 'graduate-fields-of-study',
      'admission-classifications', 'programs', 'settings', 'application-colleges',
      'application-departments', 'application-programs', 'application-study-programs',
      'application-admission-types', 'application-sponsorships', 'application-registration-periods',
      'application-fields-of-study', 'application-information', 'graduate-verification', 'applicants',
      'years', 'terms', 'downloadable-content',
      // User Management tabs (simplified - show message only)
      'users', 'roles', 'permissions', 'role-management', 'authentication-management',
      // Officials tabs
      'certificate-types', 'official-certificates',
      // Services tabs
      'service-types', 'document-types', 'alumni-applications',
      // Communication tabs
      'announcements', 'email-notifications', 'sms-notifications', 'message-center',
      // Security tabs (simplified - show message only)
      'security-monitor', 'user-audit', 'security-debug', 'rbac-test',
      // Access control demo
      'access-control-demo', 'user-simulator', 'permission-matrix', 'user-debug', 'dynamic-access-demo', 'permission-system-demo', 'permission-system-demo'
    ].includes(tab)) {
      setActiveTab(tab);
    } else if (!tab || tab === '') {
      // Default to dashboard if no tab is specified
      setActiveTab('dashboard');
      // Update URL to include the default tab
      navigate('/graduate-admin?tab=dashboard', { replace: true });
    }
  }, [location.search, navigate]);

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`/graduate-admin?tab=${value}`, { replace: true });
  };

  return (
    <AdminLayout>
        <DocumentTitle pageTitle={`Admin - ${activeTab.charAt(0).toUpperCase() + activeTab.slice(1).replace(/-/g, ' ')}`} />
      <div className="w-full max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">
            {activeTab === 'dashboard' && 'Graduation Dashboard'}
            {activeTab === 'application-dashboard' && 'Application Dashboard'}
            {activeTab === 'service-fee-dashboard' && 'Service Fee Dashboard'}
            {activeTab === 'manage' && 'Manage Graduates'}
            {activeTab === 'colleges' && 'Manage Colleges'}
            {activeTab === 'departments' && 'Manage Departments'}

            {activeTab === 'graduate-fields-of-study' && 'Graduate Fields of Study'}
            {activeTab === 'admission-classifications' && 'Manage Admission Classifications'}
            {activeTab === 'programs' && 'Manage Programs'}
            {activeTab === 'settings' && 'System Settings'}
            {activeTab === 'application-colleges' && 'Application Colleges'}
            {activeTab === 'application-departments' && 'Application Departments'}
            {activeTab === 'application-programs' && 'Application Programs'}
            {activeTab === 'application-study-programs' && 'Application Study Programs'}
            {activeTab === 'application-admission-types' && 'Application Admission Types'}

            {activeTab === 'application-registration-periods' && 'Application Registration Periods'}
            {activeTab === 'application-fields-of-study' && 'Application Fields of Study'}
            {activeTab === 'application-information' && 'Application Information'}
            {activeTab === 'graduate-verification' && 'Graduate Verification'}
            {activeTab === 'years' && 'Year Management'}
            {activeTab === 'terms' && 'Term Management'}

            {activeTab === 'applicants' && 'Applicant Management'}
            {activeTab === 'downloadable-content' && 'Downloadable Content Management'}

            {/* User Management Titles */}
            {activeTab === 'users' && 'User Management'}
            {activeTab === 'roles' && 'User Roles'}
            {activeTab === 'permissions' && 'User Permissions'}
            {activeTab === 'role-management' && 'Role Management'}
            {activeTab === 'authentication-management' && 'Authentication & Authorization'}

            {/* Officials Titles */}
            {activeTab === 'certificate-types' && 'Certificate Type Management'}
            {activeTab === 'official-certificates' && 'Official Certificate Management'}

            {/* Services Titles */}
            {activeTab === 'service-types' && 'Service Type Management'}
            {activeTab === 'document-types' && 'Document Type Management'}

            {activeTab === 'alumni-applications' && 'Alumni Applications Management'}

            {/* Communication Titles */}
            {activeTab === 'announcements' && 'Announcements'}
            {activeTab === 'email-notifications' && 'Email Notifications'}
            {activeTab === 'sms-notifications' && 'SMS Notifications'}
            {activeTab === 'message-center' && 'Message Center'}

            {/* Security Titles */}
            {activeTab === 'security-monitor' && 'Security Monitor'}
            {activeTab === 'user-audit' && 'User Security Audit'}
            {activeTab === 'security-debug' && 'Security Debug Console'}
            {activeTab === 'rbac-test' && 'RBAC Backend Test'}
          </h1>
          <p className="mt-1 text-muted-foreground">
            {activeTab === 'dashboard' && 'Overview of graduate verification statistics'}
            {activeTab === 'application-dashboard' && 'Overview of application statistics and trends'}
            {activeTab === 'service-fee-dashboard' && 'Overview of service fee revenue and transactions'}
            {activeTab === 'manage' && 'View and manage graduate records'}
            {activeTab === 'colleges' && 'Manage college information'}
            {activeTab === 'departments' && 'Manage department information'}

            {activeTab === 'graduate-fields-of-study' && 'View and search fields of study for graduate verification'}
            {activeTab === 'admission-classifications' && 'Manage admission classifications'}
            {activeTab === 'programs' && 'Manage program information'}
            {activeTab === 'settings' && 'Configure system-wide settings and preferences'}
            {activeTab === 'application-colleges' && 'Manage colleges for the application portal'}
            {activeTab === 'application-departments' && 'Manage departments for the application portal'}
            {activeTab === 'application-programs' && 'Manage programs for the application portal'}
            {activeTab === 'application-study-programs' && 'Manage study programs for the application portal'}
            {activeTab === 'application-admission-types' && 'Manage admission types for the application portal'}

            {activeTab === 'application-registration-periods' && 'Manage registration periods for the application portal'}
            {activeTab === 'application-fields-of-study' && 'Manage fields of study for the application portal'}
            {activeTab === 'application-information' && 'View and manage application information for different programs, colleges, and departments'}
            {activeTab === 'graduate-verification' && 'Verify graduate information and credentials'}
            {activeTab === 'years' && 'Manage academic years for the system'}
            {activeTab === 'terms' && 'Manage academic terms and sessions for the system'}

            {activeTab === 'applicants' && 'View and manage applicant information and application status'}
            {activeTab === 'downloadable-content' && 'Manage downloadable files and resources for users'}

            {/* User Management Descriptions */}
            {activeTab === 'users' && 'View, create, update, and delete users in the system'}
            {activeTab === 'roles' && 'Configure user roles and their permissions'}
            {activeTab === 'permissions' && 'Manage system permissions for user roles'}
            {activeTab === 'role-management' && 'Comprehensive role and permission management with hierarchical access control'}
            {activeTab === 'authentication-management' && 'Comprehensive Django authentication and authorization management with users, groups, and permissions'}

            {/* Officials Descriptions */}
            {activeTab === 'certificate-types' && 'Manage certificate types for the application system'}
            {activeTab === 'official-certificates' && 'Track and manage certificates sent to and received from external institutions'}

            {/* Services Descriptions */}
            {activeTab === 'service-types' && 'Manage service types with pricing and document requirements'}
            {activeTab === 'document-types' && 'Manage document types required for various services and applications'}

            {activeTab === 'alumni-applications' && 'Manage alumni application requests for transcripts, certificates, and other services'}

            {/* Communication Descriptions */}
            {activeTab === 'announcements' && 'Create and manage system-wide announcements for users'}
            {activeTab === 'email-notifications' && 'Send and manage email notifications to users'}
            {activeTab === 'sms-notifications' && 'Send and manage SMS notifications to users'}
            {activeTab === 'message-center' && 'Central hub for sending communications through different channels'}

            {/* Security Descriptions */}
            {activeTab === 'security-monitor' && 'Monitor security events, failed access attempts, and suspicious activities in real-time'}
            {activeTab === 'user-audit' && 'Audit user accounts for security compliance and proper role assignments'}
            {activeTab === 'security-debug' && 'Debug and verify RBAC security fixes with real-time access control testing'}
            {activeTab === 'rbac-test' && 'Test what the backend API is actually returning for RBAC data and permissions'}
          </p>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="admin-tabs"
        >
          {/* Tabs navigation removed - now using only sidebar navigation */}

          <TabsContent value="dashboard">
            <GraduateDashboard />
          </TabsContent>

          <TabsContent value="application-dashboard">
            <ApplicationDashboard />
          </TabsContent>

          <TabsContent value="service-fee-dashboard">
            <ServiceFeeDashboard />
          </TabsContent>

          <TabsContent value="manage">
            <GraduateManagement />
          </TabsContent>

          <TabsContent value="colleges">
            <CollegeManagement />
          </TabsContent>

          <TabsContent value="departments">
            <DepartmentManagement />
          </TabsContent>



          <TabsContent value="graduate-fields-of-study">
            <GraduateFieldOfStudyManagement />
          </TabsContent>

          <TabsContent value="admission-classifications">
            <AdmissionClassificationManagement />
          </TabsContent>

          <TabsContent value="programs">
            <ProgramManagement />
          </TabsContent>

          <TabsContent value="settings">
            <SettingsPage />
          </TabsContent>

          <TabsContent value="application-colleges">
            <ApplicationCollegeManagement />
          </TabsContent>

          <TabsContent value="application-departments">
            <ApplicationDepartmentManagement />
          </TabsContent>

          <TabsContent value="application-programs">
            <ApplicationProgramManagement />
          </TabsContent>

          <TabsContent value="application-study-programs">
            <ApplicationStudyProgramManagement />
          </TabsContent>

          <TabsContent value="application-admission-types">
            <ApplicationAdmissionTypeManagement />
          </TabsContent>



          <TabsContent value="application-registration-periods">
            <ApplicationRegistrationPeriodManagement />
          </TabsContent>

          <TabsContent value="application-fields-of-study">
            <ApplicationFieldOfStudyManagement />
          </TabsContent>

          <TabsContent value="application-information">
            <ApplicationInformationView />
          </TabsContent>

          <TabsContent value="graduate-verification">
            <GraduateVerificationView />
          </TabsContent>

          <TabsContent value="years">
            <YearManagement />
          </TabsContent>

          <TabsContent value="terms">
            <TermManagement />
          </TabsContent>

          <TabsContent value="applicants" className="overflow-hidden">
            <ApplicantManagement />
          </TabsContent>

          <TabsContent value="downloadable-content">
            <DownloadableManagement />
          </TabsContent>

          {/* Officials Tabs */}
          <TabsContent value="certificate-types">
            <CertificateTypeManagement />
          </TabsContent>



          <TabsContent value="service-types">
            <ServiceTypeManagement />
          </TabsContent>

          <TabsContent value="document-types">
            <DocumentTypeManagement />
          </TabsContent>



          <TabsContent value="alumni-applications">
            <AlumniApplicationsManagement />
          </TabsContent>

          <TabsContent value="official-certificates">
            <OfficialManagement />
          </TabsContent>

          {/* User Management Tabs - REMOVED (simplified system) */}
          <TabsContent value="users">
            <Card>
              <CardContent className="text-center py-8">
                <div className="text-gray-500">
                  <p>User management features have been simplified. Only superusers have admin access.</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="roles">
            <Card>
              <CardContent className="text-center py-8">
                <div className="text-gray-500">
                  <p>Role management features have been simplified. Only superusers have admin access.</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="permissions">
            <Card>
              <CardContent className="text-center py-8">
                <div className="text-gray-500">
                  <p>Permission management features have been simplified. Only superusers have admin access.</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="role-management">
            <Card>
              <CardContent className="text-center py-8">
                <div className="text-gray-500">
                  <p>Role management features have been simplified. Only superusers have admin access.</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>



          {/* Communication Tabs */}
          <TabsContent value="announcements">
            <Announcements />
          </TabsContent>

          <TabsContent value="email-notifications">
            <EmailNotifications />
          </TabsContent>

          <TabsContent value="sms-notifications">
            <SMSNotifications />
          </TabsContent>

          <TabsContent value="message-center">
            <MessageCenter />
          </TabsContent>

          {/* Security Tabs - Enhanced RBAC Implementation */}
          <TabsContent value="security-monitor">
            <SecurityMonitor />
          </TabsContent>

          <TabsContent value="user-audit">
            <UserAudit />
          </TabsContent>

          <TabsContent value="security-debug">
            <RBACTest />
          </TabsContent>

          <TabsContent value="rbac-test">
            <RBACTest />
          </TabsContent>

          {/* Authentication & Authorization Management */}
          <TabsContent value="authentication-management">
            <SimpleDjangoAuthManagement />
          </TabsContent>

          {/* Access Control Demo */}
          <TabsContent value="access-control-demo">
            <AccessControlDemo />
          </TabsContent>

          {/* User Simulator */}
          <TabsContent value="user-simulator">
            <UserSimulator />
          </TabsContent>

          {/* Permission Matrix */}
          <TabsContent value="permission-matrix">
            <PermissionMatrix />
          </TabsContent>

          {/* User Debug */}
          <TabsContent value="user-debug">
            <UserDebug />
          </TabsContent>

          {/* Dynamic Access Demo */}
          <TabsContent value="dynamic-access-demo">
            <DynamicAccessDemo />
          </TabsContent>

          {/* Permission System Demo */}
          <TabsContent value="permission-system-demo">
            <PermissionSystemDemo />
          </TabsContent>

        </Tabs>


      </div>
      </AdminLayout>
  );
};

export default GraduateAdmin;
