# Django imports
from rest_framework import generics, filters
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from .models import StudyField
from .serializers import StudyFieldSerializer

# Create your views here.
# List and Create view for StudyField (GET, POST)
class StudyFieldList(generics.ListCreateAPIView):
    serializer_class = StudyFieldSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['college', 'department', 'status']
    search_fields = ['field_of_study', 'description', 'college__name', 'department__name']
    ordering_fields = ['field_of_study', 'created_at', 'updated_at']
    ordering = ['field_of_study']

    def get_queryset(self):
        return StudyField.objects.all()

    def perform_create(self, serializer):
        serializer.save()

# Public List view for StudyField (GET only)
class PublicStudyFieldList(generics.ListAPIView):
    serializer_class = StudyFieldSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['college', 'department', 'status']
    search_fields = ['field_of_study', 'description', 'college__name', 'department__name']
    ordering_fields = ['field_of_study', 'created_at', 'updated_at']
    ordering = ['field_of_study']

    def get_queryset(self):
        # Only return active fields of study for public access
        return StudyField.objects.filter(status=True)

# Delete view for StudyField (DELETE)
class StudyFieldDelete(generics.DestroyAPIView):
    serializer_class = StudyFieldSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return StudyField.objects.all()

# Retrieve and Update view for StudyField (GET, PUT)
class StudyFieldDetail(generics.RetrieveUpdateAPIView):
    serializer_class = StudyFieldSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return StudyField.objects.all()