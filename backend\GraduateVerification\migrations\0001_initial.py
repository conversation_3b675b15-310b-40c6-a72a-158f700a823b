# Generated by Django 5.2.1 on 2025-05-31 12:42

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AdmissionClassification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the admission classification', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the classification')),
            ],
            options={
                'verbose_name_plural': 'Admission Classifications',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VerificationCollege',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(help_text='Name of the college', max_length=200, unique=True)),
                ('code', models.CharField(help_text='Unique code identifier for the college', max_length=20, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Colleges',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VerificationProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the program', max_length=200, unique=True)),
                ('code', models.CharField(help_text='Unique program code', max_length=20, unique=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VerificationDepartment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the department', max_length=200)),
                ('code', models.CharField(help_text='Department code', max_length=20)),
                ('college', models.ForeignKey(help_text='College this department belongs to', on_delete=django.db.models.deletion.PROTECT, related_name='departments', to='GraduateVerification.verificationcollege')),
            ],
            options={
                'verbose_name_plural': 'Departments',
                'ordering': ['college', 'name'],
                'unique_together': {('college', 'code')},
            },
        ),
        migrations.CreateModel(
            name='VerificationFieldOfStudy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the field of study', max_length=200)),
                ('code', models.CharField(help_text='Field of study code', max_length=20)),
                ('duration', models.PositiveIntegerField(blank=True, help_text='Duration of the field of study in years', null=True)),
                ('department', models.ForeignKey(help_text='Department this field belongs to', on_delete=django.db.models.deletion.PROTECT, related_name='fields_of_study', to='GraduateVerification.verificationdepartment')),
            ],
            options={
                'verbose_name_plural': 'Fields of Study',
                'ordering': ['department', 'name'],
                'unique_together': {('department', 'code')},
            },
        ),
        migrations.CreateModel(
            name='GraduateStudent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.CharField(db_index=True, help_text='Unique identifier for the student', max_length=50, unique=True)),
                ('first_name', models.CharField(db_index=True, help_text="Student's first name", max_length=100)),
                ('middle_name', models.CharField(blank=True, help_text="Student's middle name (optional)", max_length=100, null=True)),
                ('last_name', models.CharField(db_index=True, help_text="Student's last name", max_length=100)),
                ('year_of_entry', models.PositiveIntegerField(db_index=True, help_text='Year when the student entered the program', validators=[django.core.validators.MinValueValidator(1900, message='Year must be 1900 or later'), django.core.validators.MaxValueValidator(2025, message='Year cannot be later than 2025')])),
                ('year_of_graduation', models.PositiveIntegerField(db_index=True, help_text='Year when the student graduated', validators=[django.core.validators.MinValueValidator(1900, message='Year must be 1900 or later'), django.core.validators.MaxValueValidator(2025, message='Year cannot be later than 2025')])),
                ('gpa', models.DecimalField(decimal_places=2, max_digits=3, validators=[django.core.validators.MinValueValidator(Decimal('2.00')), django.core.validators.MaxValueValidator(Decimal('4.00'))])),
                ('gender', models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')], help_text="Student's gender identity", max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Record creation timestamp')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Record last update timestamp')),
                ('admission_classification', models.ForeignKey(help_text='Classification of admission', on_delete=django.db.models.deletion.PROTECT, related_name='graduate_students', to='GraduateVerification.admissionclassification')),
                ('college', models.ForeignKey(help_text='College the student graduated from', on_delete=django.db.models.deletion.PROTECT, related_name='graduate_students', to='GraduateVerification.verificationcollege')),
                ('department', models.ForeignKey(help_text='Department the student graduated from', on_delete=django.db.models.deletion.PROTECT, related_name='graduate_students', to='GraduateVerification.verificationdepartment')),
                ('field_of_study', models.ForeignKey(help_text="Student's field of study", on_delete=django.db.models.deletion.PROTECT, related_name='graduate_students', to='GraduateVerification.verificationfieldofstudy')),
                ('program', models.ForeignKey(help_text='Academic program completed', on_delete=django.db.models.deletion.PROTECT, related_name='graduate_students', to='GraduateVerification.verificationprogram')),
            ],
            options={
                'verbose_name': 'Graduate Verification',
                'verbose_name_plural': 'Graduate Verifications',
                'ordering': ['-year_of_graduation', 'last_name', 'first_name'],
                'indexes': [models.Index(fields=['first_name', 'last_name'], name='GraduateVer_first_n_d6129c_idx'), models.Index(fields=['last_name', 'first_name'], name='GraduateVer_last_na_07fa09_idx'), models.Index(fields=['year_of_entry'], name='GraduateVer_year_of_7ef246_idx'), models.Index(fields=['year_of_graduation', 'field_of_study'], name='GraduateVer_year_of_5a1389_idx'), models.Index(fields=['year_of_graduation', 'college'], name='GraduateVer_year_of_c55498_idx'), models.Index(fields=['year_of_entry', 'year_of_graduation'], name='GraduateVer_year_of_b182b5_idx')],
            },
        ),
    ]
