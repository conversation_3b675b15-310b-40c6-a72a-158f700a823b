#!/usr/bin/env python3
"""
Comprehensive RBAC Testing Script for Alumni Applications System
Tests all role-based access control functionality including new features.
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User, Group
from django.test import Client
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

class RBACTester:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.client = APIClient()
        self.test_results = []
        
        # Test users for different roles
        self.test_users = {
            'super_admin': None,
            'admin': None,
            'staff': None,
            'registrar': None,
            'department_head': None,
            'anonymous': None
        }
        
        # Test endpoints
        self.endpoints = {
            'alumni_applications': {
                'list_form1': '/api/applications/form1/',
                'create_form1': '/api/applications/form1/',
                'list_form2': '/api/applications/form2/',
                'create_form2': '/api/applications/form2/',
                'statistics': '/api/applications/statistics/',
                'update_status': '/api/applications/form1/{id}/update_status/',
            },
            'user_management': {
                'users_list': '/api/user/users/',
                'roles_list': '/api/user/roles/',
                'permissions_list': '/api/user/permissions/',
            },
            'registration': {
                'statistics': '/api/registration/statistics/',
                'applications': '/api/registration/applications/',
            },
            'official': {
                'statistics': '/api/official/statistics/',
                'sent_list': '/api/official/sent/',
                'received_list': '/api/official/received/',
            },
            'service_requests': {
                'list': '/api/service-requests/',
                'statistics': '/api/service-requests/statistics/',
            }
        }

    def setup_test_users(self):
        """Create test users with different roles"""
        print("🔧 Setting up test users...")
        
        # Create groups if they don't exist
        groups = {
            'Super Admin': 'Highest level access with full system control',
            'Administrator': 'Administrative access to most system functions',
            'Main Registrar': 'Main registrar with full registration access',
            'Registrar Officer': 'Registrar officer with limited registration access',
            'Department Head': 'Department head with department-specific access',
            'Staff': 'General staff access'
        }
        
        for group_name, description in groups.items():
            group, created = Group.objects.get_or_create(name=group_name)
            if created:
                print(f"  ✅ Created group: {group_name}")
        
        # Create test users
        users_config = {
            'super_admin': {
                'username': 'test_super_admin',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'groups': ['Super Admin'],
                'is_superuser': True,
                'is_staff': True
            },
            'admin': {
                'username': 'test_admin',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'groups': ['Administrator'],
                'is_staff': True
            },
            'staff': {
                'username': 'test_staff',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'groups': ['Staff'],
                'is_staff': True
            },
            'registrar': {
                'username': 'test_registrar',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'groups': ['Main Registrar'],
                'is_staff': True
            },
            'department_head': {
                'username': 'test_dept_head',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'groups': ['Department Head'],
                'is_staff': True
            }
        }
        
        for role, config in users_config.items():
            # Delete existing user if exists
            User.objects.filter(username=config['username']).delete()
            
            # Create new user
            user = User.objects.create_user(
                username=config['username'],
                email=config['email'],
                password=config['password'],
                is_superuser=config.get('is_superuser', False),
                is_staff=config.get('is_staff', False)
            )
            
            # Add to groups
            for group_name in config['groups']:
                group = Group.objects.get(name=group_name)
                user.groups.add(group)
            
            self.test_users[role] = user
            print(f"  ✅ Created user: {config['username']} ({role})")

    def get_auth_token(self, user):
        """Get JWT token for user"""
        if user is None:
            return None
        
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def test_endpoint(self, endpoint_url, method='GET', user_role=None, data=None, expected_status=None):
        """Test an endpoint with specific user role"""
        # Set authentication
        if user_role and user_role != 'anonymous':
            user = self.test_users.get(user_role)
            if user:
                token = self.get_auth_token(user)
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            else:
                self.client.credentials()
        else:
            self.client.credentials()  # No authentication
        
        # Make request
        try:
            if method == 'GET':
                response = self.client.get(endpoint_url)
            elif method == 'POST':
                response = self.client.post(endpoint_url, data=data, format='json')
            elif method == 'PUT':
                response = self.client.put(endpoint_url, data=data, format='json')
            elif method == 'PATCH':
                response = self.client.patch(endpoint_url, data=data, format='json')
            elif method == 'DELETE':
                response = self.client.delete(endpoint_url)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            # Record result
            result = {
                'endpoint': endpoint_url,
                'method': method,
                'user_role': user_role or 'anonymous',
                'status_code': response.status_code,
                'expected_status': expected_status,
                'success': expected_status is None or response.status_code == expected_status,
                'response_data': response.data if hasattr(response, 'data') else None,
                'timestamp': datetime.now().isoformat()
            }
            
            self.test_results.append(result)
            return result
            
        except Exception as e:
            result = {
                'endpoint': endpoint_url,
                'method': method,
                'user_role': user_role or 'anonymous',
                'status_code': 'ERROR',
                'expected_status': expected_status,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            self.test_results.append(result)
            return result

    def test_alumni_applications_rbac(self):
        """Test alumni applications RBAC"""
        print("\n📋 Testing Alumni Applications RBAC...")
        
        # Test public endpoints (should allow anonymous access)
        public_endpoints = [
            ('/api/applications/form1/', 'GET'),
            ('/api/applications/form2/', 'GET'),
            ('/api/applications/form1/', 'POST'),
            ('/api/applications/form2/', 'POST'),
        ]
        
        for endpoint, method in public_endpoints:
            result = self.test_endpoint(endpoint, method, 'anonymous', expected_status=200)
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {method} {endpoint} (anonymous): {result['status_code']}")
        
        # Test statistics endpoint (should require authentication)
        for role in ['anonymous', 'staff', 'admin', 'super_admin']:
            expected = 401 if role == 'anonymous' else 200
            result = self.test_endpoint('/api/applications/statistics/', 'GET', role, expected_status=expected)
            status = "✅" if result['success'] else "❌"
            print(f"  {status} GET /api/applications/statistics/ ({role}): {result['status_code']}")
        
        # Test update status endpoint (should require authentication)
        test_data = {'application_status': 'Processing'}
        for role in ['anonymous', 'staff', 'admin', 'super_admin']:
            expected = 401 if role == 'anonymous' else 404  # 404 because we're using fake ID
            result = self.test_endpoint('/api/applications/form1/fake-id/update_status/', 'PATCH', role, test_data, expected_status=expected)
            status = "✅" if result['success'] else "❌"
            print(f"  {status} PATCH update_status ({role}): {result['status_code']}")

    def test_user_management_rbac(self):
        """Test user management RBAC"""
        print("\n👥 Testing User Management RBAC...")
        
        # These endpoints should require admin roles
        admin_endpoints = [
            '/api/user/users/',
            '/api/user/roles/',
            '/api/user/permissions/',
        ]
        
        for endpoint in admin_endpoints:
            for role in ['anonymous', 'staff', 'admin', 'super_admin']:
                if role == 'anonymous':
                    expected = 401
                elif role in ['admin', 'super_admin']:
                    expected = 200
                else:
                    expected = 403
                
                result = self.test_endpoint(endpoint, 'GET', role, expected_status=expected)
                status = "✅" if result['success'] else "❌"
                print(f"  {status} GET {endpoint} ({role}): {result['status_code']}")

    def test_statistics_endpoints_rbac(self):
        """Test all statistics endpoints RBAC"""
        print("\n📊 Testing Statistics Endpoints RBAC...")
        
        statistics_endpoints = [
            '/api/applications/statistics/',  # Alumni applications statistics
            '/api/registration/statistics/',   # Registration statistics
            '/api/official/statistics/',      # Official certificates statistics
            '/api/service-requests/statistics/', # Service requests statistics
        ]
        
        for endpoint in statistics_endpoints:
            for role in ['anonymous', 'staff', 'registrar', 'admin', 'super_admin']:
                if role == 'anonymous':
                    expected = 401  # Unauthorized
                else:
                    expected = 200  # Should be accessible to authenticated users
                
                result = self.test_endpoint(endpoint, 'GET', role, expected_status=expected)
                status = "✅" if result['success'] else "❌"
                print(f"  {status} GET {endpoint} ({role}): {result['status_code']}")

    def test_revenue_calculations(self):
        """Test revenue calculations in statistics"""
        print("\n💰 Testing Revenue Calculations...")

        # Test with authenticated user
        result = self.test_endpoint('/api/applications/statistics/', 'GET', 'admin')

        if result['status_code'] == 200 and result['response_data']:
            data = result['response_data']

            # Check if revenue data is present
            if 'revenue' in data:
                revenue = data['revenue']
                print(f"  ✅ Revenue data present:")
                print(f"    - Paid Revenue: {revenue.get('paid_revenue', 'N/A')} ETB")
                print(f"    - Pending Revenue: {revenue.get('pending_revenue', 'N/A')} ETB")
                print(f"    - Unpaid Revenue: {revenue.get('unpaid_revenue', 'N/A')} ETB")
                print(f"    - Total Potential: {revenue.get('total_potential_revenue', 'N/A')} ETB")
            else:
                print(f"  ❌ Revenue data missing from response")

            # Check time-based statistics
            if 'time_based' in data:
                time_based = data['time_based']
                print(f"  ✅ Time-based statistics present:")
                print(f"    - Today: {time_based.get('today', 'N/A')}")
                print(f"    - 3 Days: {time_based.get('three_days', 'N/A')}")
                print(f"    - 1 Week: {time_based.get('one_week', 'N/A')}")
                print(f"    - 1 Month: {time_based.get('one_month', 'N/A')}")
            else:
                print(f"  ❌ Time-based statistics missing from response")
        else:
            print(f"  ❌ Failed to get statistics: {result['status_code']}")

    def test_file_access_rbac(self):
        """Test file access permissions"""
        print("\n📁 Testing File Access RBAC...")

        # Test document file access
        fake_document_id = "fake-doc-id"
        file_endpoint = f'/api/applications/documents/{fake_document_id}/file/'

        for role in ['anonymous', 'staff', 'admin', 'super_admin']:
            expected = 404  # Will be 404 for fake ID, but tests authentication
            result = self.test_endpoint(file_endpoint, 'GET', role, expected_status=expected)
            status = "✅" if result['success'] else "❌"
            print(f"  {status} GET {file_endpoint} ({role}): {result['status_code']}")

    def test_method_based_permissions(self):
        """Test method-based permissions"""
        print("\n🔧 Testing Method-Based Permissions...")

        # Test different HTTP methods on alumni applications
        test_data = {
            'first_name': 'Test',
            'father_name': 'Test',
            'last_name': 'User',
            'email': '<EMAIL>',
            'phone_number': '+251912345678'
        }

        methods_tests = [
            ('GET', '/api/applications/form1/', None),
            ('POST', '/api/applications/form1/', test_data),
            ('PUT', '/api/applications/form1/fake-id/', test_data),
            ('DELETE', '/api/applications/form1/fake-id/', None),
        ]

        for method, endpoint, data in methods_tests:
            for role in ['anonymous', 'staff', 'admin']:
                if method in ['GET', 'POST'] and role == 'anonymous':
                    expected = 200  # Public access allowed
                elif method in ['PUT', 'DELETE'] and role == 'anonymous':
                    expected = 401  # Authentication required
                elif method in ['PUT', 'DELETE']:
                    expected = 404  # Authenticated but fake ID
                else:
                    expected = 200

                result = self.test_endpoint(endpoint, method, role, data, expected_status=expected)
                status = "✅" if result['success'] else "❌"
                print(f"  {status} {method} {endpoint} ({role}): {result['status_code']}")

    def test_middleware_protection(self):
        """Test middleware-level protection"""
        print("\n🛡️ Testing Middleware Protection...")

        # Test protected URL patterns from middleware
        protected_urls = [
            '/api/admin/',
            '/api/user/users/',
            '/api/user/roles/',
            '/api/user/permissions/',
        ]

        for url in protected_urls:
            for role in ['anonymous', 'staff', 'admin', 'super_admin']:
                if role == 'anonymous':
                    expected = 403  # Middleware should block
                elif role in ['admin', 'super_admin']:
                    expected = 404  # Allowed by middleware, but endpoint might not exist
                else:
                    expected = 403  # Insufficient permissions

                result = self.test_endpoint(url, 'GET', role, expected_status=expected)
                status = "✅" if result['success'] else "❌"
                print(f"  {status} GET {url} ({role}): {result['status_code']}")

    def test_cross_role_access(self):
        """Test cross-role access scenarios"""
        print("\n🔄 Testing Cross-Role Access...")

        # Test scenarios where different roles should have different access levels
        scenarios = [
            {
                'endpoint': '/api/applications/statistics/',
                'method': 'GET',
                'description': 'Alumni statistics access',
                'expected_access': {
                    'anonymous': False,
                    'staff': True,
                    'registrar': True,
                    'admin': True,
                    'super_admin': True
                }
            },
            {
                'endpoint': '/api/user/users/',
                'method': 'GET',
                'description': 'User management access',
                'expected_access': {
                    'anonymous': False,
                    'staff': False,
                    'registrar': False,
                    'admin': True,
                    'super_admin': True
                }
            }
        ]

        for scenario in scenarios:
            print(f"\n  Testing: {scenario['description']}")
            for role, should_have_access in scenario['expected_access'].items():
                expected = 200 if should_have_access else (401 if role == 'anonymous' else 403)
                result = self.test_endpoint(scenario['endpoint'], scenario['method'], role, expected_status=expected)

                access_granted = result['status_code'] == 200
                correct_access = access_granted == should_have_access

                status = "✅" if correct_access else "❌"
                access_text = "GRANTED" if access_granted else "DENIED"
                expected_text = "SHOULD HAVE" if should_have_access else "SHOULD NOT HAVE"

                print(f"    {status} {role}: {access_text} access ({expected_text} access)")

    def test_token_validation(self):
        """Test JWT token validation"""
        print("\n🔑 Testing Token Validation...")

        # Test with valid token
        result = self.test_endpoint('/api/applications/statistics/', 'GET', 'admin')
        status = "✅" if result['status_code'] == 200 else "❌"
        print(f"  {status} Valid token: {result['status_code']}")

        # Test with invalid token
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid-token')
        result = self.test_endpoint('/api/applications/statistics/', 'GET', None, expected_status=401)
        status = "✅" if result['success'] else "❌"
        print(f"  {status} Invalid token: {result['status_code']}")

        # Test with malformed token
        self.client.credentials(HTTP_AUTHORIZATION='InvalidFormat')
        result = self.test_endpoint('/api/applications/statistics/', 'GET', None, expected_status=401)
        status = "✅" if result['success'] else "❌"
        print(f"  {status} Malformed token: {result['status_code']}")

        # Test without token
        self.client.credentials()
        result = self.test_endpoint('/api/applications/statistics/', 'GET', None, expected_status=401)
        status = "✅" if result['success'] else "❌"
        print(f"  {status} No token: {result['status_code']}")

    def run_all_tests(self):
        """Run all RBAC tests"""
        print("🚀 Starting Comprehensive RBAC Testing...")
        print("=" * 60)

        # Setup
        self.setup_test_users()

        # Run core RBAC tests
        self.test_alumni_applications_rbac()
        self.test_user_management_rbac()
        self.test_statistics_endpoints_rbac()

        # Run new functionality tests
        self.test_revenue_calculations()
        self.test_file_access_rbac()
        self.test_method_based_permissions()
        self.test_middleware_protection()
        self.test_cross_role_access()
        self.test_token_validation()

        # Generate report
        self.generate_report()

    def generate_report(self):
        """Generate test report"""
        print("\n" + "=" * 60)
        print("📊 RBAC TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['method']} {result['endpoint']} ({result['user_role']}): {result['status_code']}")
                    if 'error' in result:
                        print(f"    Error: {result['error']}")
        
        # Save detailed report
        with open('rbac_test_report.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: rbac_test_report.json")

if __name__ == "__main__":
    tester = RBACTester()
    tester.run_all_tests()
