#!/usr/bin/env python3
"""
Real Email Test for Alumni Applications
Tests email sending with real email addresses
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from alumni_applications.email_service import AlumniApplicationEmailService
from alumni_applications.models import AlumniApplication, AlumniApplicationMini
from setups.service_type.models import ServiceType
from setups.college.models import College
from decimal import Decimal
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealEmailTest:
    def __init__(self):
        self.test_email = None
        
    def get_test_email(self):
        """Get test email from user input"""
        if len(sys.argv) > 1:
            self.test_email = sys.argv[1]
        else:
            self.test_email = input("Enter a real email address to test with: ").strip()
        
        if not self.test_email:
            print("❌ No email address provided")
            return False
        
        # Validate email format
        if not AlumniApplicationEmailService._is_valid_email(self.test_email):
            print(f"❌ Invalid email address: {self.test_email}")
            return False
        
        print(f"✅ Using test email: {self.test_email}")
        return True

    def create_test_application(self):
        """Create a test application for email testing"""
        print("\n🔧 Creating test application...")
        
        try:
            # Get or create test service type
            service_type, created = ServiceType.objects.get_or_create(
                name='Email Test Service',
                defaults={
                    'fee': Decimal('150.00'),
                    'description': 'Test service for email verification'
                }
            )
            
            # Get or create test college
            college, created = College.objects.get_or_create(
                name='Email Test College',
                defaults={'description': 'Test college for email verification'}
            )
            
            # Create test application
            application = AlumniApplication.objects.create(
                first_name='Email',
                father_name='Test',
                last_name='User',
                email=self.test_email,
                phone_number='+251912345678',
                admission_type='Regular',
                degree_type='Degree',
                college=college,
                other_department_name='Computer Science',
                is_other_college=False,
                student_status='Graduated',
                year_of_graduation_ethiopian='2015',
                year_of_graduation_gregorian=2023,
                service_type=service_type,
                payment_status='Pending',
                application_status='Pending',
                is_uog_destination=True,
                uog_college=college
            )
            
            print(f"✅ Created test application: {application.id}")
            print(f"   Transaction ID: {application.transaction_id}")
            print(f"   Email: {application.email}")
            
            return application
            
        except Exception as e:
            print(f"❌ Failed to create test application: {e}")
            return None

    def test_email_sending(self, application):
        """Test sending email to real address"""
        print("\n📧 Testing email sending...")
        
        try:
            # Test Form1 email
            print("  Testing Form1 email...")
            result1 = AlumniApplicationEmailService.send_application_confirmation(
                application=application,
                is_form1=True
            )
            
            if result1:
                print("  ✅ Form1 email sent successfully")
            else:
                print("  ❌ Form1 email failed")
            
            # Test Form2 email (create mini application)
            print("  Testing Form2 email...")
            
            mini_app = AlumniApplicationMini.objects.create(
                first_name='Email',
                father_name='Test',
                last_name='Mini',
                email=self.test_email,
                phone_number='+251912345679',
                admission_type='Regular',
                degree_type='Degree',
                college=application.college,
                other_department_name='Computer Science',
                is_other_college=False,
                student_status='Graduated',
                year_of_graduation_ethiopian='2015',
                year_of_graduation_gregorian=2023,
                service_type=application.service_type,
                payment_status='Pending',
                application_status='Pending'
            )
            
            result2 = AlumniApplicationEmailService.send_application_confirmation(
                application=mini_app,
                is_form1=False
            )
            
            if result2:
                print("  ✅ Form2 email sent successfully")
            else:
                print("  ❌ Form2 email failed")
            
            # Clean up mini application
            mini_app.delete()
            
            return result1 and result2
            
        except Exception as e:
            print(f"  ❌ Email sending failed: {e}")
            return False

    def check_email_logs(self):
        """Check email notification logs"""
        print("\n📝 Checking email logs...")
        
        try:
            from communication.models import EmailNotification
            from django.utils import timezone
            from datetime import timedelta
            
            # Check recent notifications for our test email
            recent_notifications = EmailNotification.objects.filter(
                recipients__icontains=self.test_email,
                sent_time__gte=timezone.now() - timedelta(minutes=10)
            ).order_by('-sent_time')
            
            print(f"  Found {recent_notifications.count()} recent notifications for {self.test_email}")
            
            for notification in recent_notifications[:3]:  # Show last 3
                print(f"    - {notification.subject} ({notification.status}) at {notification.sent_time}")
            
            # Check for failures
            failed_notifications = EmailNotification.objects.filter(
                recipients__icontains=self.test_email,
                status='failed',
                sent_time__gte=timezone.now() - timedelta(hours=1)
            )
            
            if failed_notifications.exists():
                print(f"  ⚠️  Found {failed_notifications.count()} failed notifications:")
                for notification in failed_notifications:
                    print(f"    - {notification.subject}: {notification.content[:100]}...")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Failed to check email logs: {e}")
            return False

    def cleanup_test_data(self, application):
        """Clean up test data"""
        print("\n🧹 Cleaning up test data...")
        
        try:
            if application:
                application.delete()
                print("  ✅ Test application deleted")
            
            # Clean up test service type and college if they were created for testing
            ServiceType.objects.filter(name='Email Test Service').delete()
            College.objects.filter(name='Email Test College').delete()
            print("  ✅ Test data cleaned up")
            
        except Exception as e:
            print(f"  ⚠️  Cleanup warning: {e}")

    def run_test(self):
        """Run the complete email test"""
        print("🚀 Starting Real Email Test...")
        print("=" * 50)
        
        # Get test email
        if not self.get_test_email():
            return False
        
        # Create test application
        application = self.create_test_application()
        if not application:
            return False
        
        try:
            # Test email sending
            email_success = self.test_email_sending(application)
            
            # Check logs
            self.check_email_logs()
            
            # Results
            print("\n" + "=" * 50)
            print("📊 EMAIL TEST RESULTS")
            print("=" * 50)
            
            if email_success:
                print("✅ EMAIL TEST PASSED")
                print(f"   Emails sent successfully to: {self.test_email}")
                print("   Check your inbox and spam folder")
                print("   Check Django admin for email notification logs")
            else:
                print("❌ EMAIL TEST FAILED")
                print("   Check the error messages above")
                print("   Verify SMTP configuration")
                print("   Check email notification logs in Django admin")
            
            print("\n📧 NEXT STEPS:")
            print("1. Check your email inbox and spam folder")
            print("2. Verify email content and formatting")
            print("3. Test with different email providers")
            print("4. Check Django admin: /admin/communication/emailnotification/")
            
            return email_success
            
        finally:
            # Always clean up
            self.cleanup_test_data(application)

if __name__ == "__main__":
    test = RealEmailTest()
    success = test.run_test()
    sys.exit(0 if success else 1)
