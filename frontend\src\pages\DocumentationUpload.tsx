import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileUp, Loader2, Info, ExternalLink, FileText, Award, Upload } from 'lucide-react';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { applicationAPI } from '@/services/api';

// Define the schema for the form
const documentationSchema = z.object({
  degree: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Degree file must be less than 2MB' }
  ),
  sponsorship: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Sponsorship file must be less than 2MB' }
  ),
  student_copy: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Student copy file must be less than 2MB' }
  ),
  recommendation: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Recommendation file must be less than 2MB' }
  ),
  publication: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Publication file must be less than 2MB' }
  ),
  conceptnote: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Concept note file must be less than 2MB' }
  ),
  grade_12_certificate: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Grade 12 certificate file must be less than 2MB' }
  ),
  grade_9_12_transcript: z.instanceof(FileList).optional().refine(
    (files) => !files || files.length === 0 || (files.length === 1 && files[0].size <= 2 * 1024 * 1024),
    { message: 'Grade 9-12 transcript file must be less than 2MB' }
  ),
});

type DocumentationFormValues = z.infer<typeof documentationSchema>;

const DocumentationUpload = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingExistingData, setIsLoadingExistingData] = useState(false);
  const [existingDocumentation, setExistingDocumentation] = useState<any>(null);
  const [programLevel, setProgramLevel] = useState<string | null>(null);
  const [admissionType, setAdmissionType] = useState<string | null>(null);
  const [isLoadingProgramInfo, setIsLoadingProgramInfo] = useState(false);
  const navigate = useNavigate();

  // Extract GAT ID from URL query parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const gatIdFromUrl = queryParams.get('gat');

  // Log the GAT ID from URL for debugging
  useEffect(() => {
    if (gatIdFromUrl) {
      console.log('GAT ID from URL in DocumentationUpload:', gatIdFromUrl);
    }
  }, [gatIdFromUrl]);

  // Initialize form
  const form = useForm<DocumentationFormValues>({
    resolver: zodResolver(documentationSchema),
    defaultValues: {
      degree: undefined,
      sponsorship: undefined,
      student_copy: undefined,
      recommendation: undefined,
      publication: undefined,
      conceptnote: undefined,
      grade_12_certificate: undefined,
      grade_9_12_transcript: undefined,
    },
  });

  // Fetch program level and admission type
  useEffect(() => {
    const fetchProgramInfo = async () => {
      setIsLoadingProgramInfo(true);
      try {
        // Step 1: Get the applicant's personal information to get program level
        const personalInfoResponse = await applicationAPI.getCurrentApplicantInfo();
        if (personalInfoResponse.data && personalInfoResponse.data.length > 0) {
          const personalInfo = personalInfoResponse.data[0];
          setProgramLevel(personalInfo.program_level);
          console.log('Program level:', personalInfo.program_level);
        }

        // Step 2: If we have a GAT ID, get the program selection to get admission type
        if (gatIdFromUrl) {
          const programSelectionResponse = await applicationAPI.getProgramSelectionByGAT(Number(gatIdFromUrl));
          if (programSelectionResponse.data && programSelectionResponse.data.length > 0) {
            const programSelection = programSelectionResponse.data[0];

            // Step 3: Get the application info to get admission type
            if (programSelection.application_info) {
              const applicationInfoResponse = await applicationAPI.getApplicationInfo(programSelection.application_info);
              if (applicationInfoResponse.data) {
                const admissionTypeName = applicationInfoResponse.data.admission_type?.name;
                setAdmissionType(admissionTypeName);
                console.log('Admission type:', admissionTypeName);
              }
            }
          }
        } else {
          // If no GAT ID, try to get the most recent program selection
          const programSelectionsResponse = await applicationAPI.getCurrentProgramSelection();
          if (programSelectionsResponse.data && programSelectionsResponse.data.length > 0) {
            const programSelection = programSelectionsResponse.data[0];

            // Get the application info to get admission type
            if (programSelection.application_info) {
              const applicationInfoResponse = await applicationAPI.getApplicationInfo(programSelection.application_info);
              if (applicationInfoResponse.data) {
                const admissionTypeName = applicationInfoResponse.data.admission_type?.name;
                setAdmissionType(admissionTypeName);
                console.log('Admission type from most recent program selection:', admissionTypeName);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error fetching program information:', error);
      } finally {
        setIsLoadingProgramInfo(false);
      }
    };

    fetchProgramInfo();
  }, [gatIdFromUrl]);

  // Fetch existing documentation if available
  useEffect(() => {
    const fetchDocumentation = async () => {
      setIsLoadingExistingData(true);
      try {
        const response = await applicationAPI.getCurrentDocumentation();
        console.log('Fetched documentation:', response.data);

        if (response.data && response.data.length > 0) {
          // Use the first documentation record for the current user
          const documentation = response.data[0];
          setExistingDocumentation(documentation);
          console.log('Using documentation:', documentation);
        } else {
          console.log('No existing documentation found for current user');
          setExistingDocumentation(null);
        }
      } catch (error) {
        console.error('Error fetching documentation:', error);
      } finally {
        setIsLoadingExistingData(false);
      }
    };

    fetchDocumentation();
  }, []);

  // Submit form
  const onSubmit = async (data: DocumentationFormValues) => {
    setIsLoading(true);

    try {
      // Create FormData object to handle file uploads
      const formData = new FormData();

      // Add files to FormData if they exist
      if (data.degree && data.degree.length > 0) formData.append('degree', data.degree[0]);
      if (data.sponsorship && data.sponsorship.length > 0) formData.append('sponsorship', data.sponsorship[0]);
      if (data.student_copy && data.student_copy.length > 0) formData.append('student_copy', data.student_copy[0]);
      if (data.recommendation && data.recommendation.length > 0) formData.append('recommendation', data.recommendation[0]);
      if (data.publication && data.publication.length > 0) formData.append('publication', data.publication[0]);
      if (data.conceptnote && data.conceptnote.length > 0) formData.append('conceptnote', data.conceptnote[0]);
      if (data.grade_12_certificate && data.grade_12_certificate.length > 0) formData.append('grade_12_certificate', data.grade_12_certificate[0]);
      if (data.grade_9_12_transcript && data.grade_9_12_transcript.length > 0) formData.append('grade_9_12_transcript', data.grade_9_12_transcript[0]);

      console.log('Submitting documentation with FormData');

      let response;
      if (existingDocumentation) {
        // Update existing documentation
        response = await applicationAPI.updateDocumentation(existingDocumentation.id, formData);
        console.log('Updated documentation:', response);
      } else {
        // Create new documentation
        response = await applicationAPI.submitDocumentation(formData);
        console.log('Created documentation:', response);
      }

      if (response.status === 200 || response.status === 201) {
        toast.success('Documentation uploaded successfully!');

        // Check if we have all required information before navigating to payment
        try {
          // Get personal information
          const personalInfoResponse = await applicationAPI.getCurrentApplicantInfo();
          const hasPersonalInfo = personalInfoResponse.data && personalInfoResponse.data.length > 0;

          // Get GAT information
          const gatResponse = await applicationAPI.getCurrentGAT();
          const hasGatInfo = gatResponse.data && gatResponse.data.length > 0;

          // Get program selection
          const programSelectionResponse = await applicationAPI.getCurrentProgramSelection();
          const hasProgramSelection = programSelectionResponse.data && programSelectionResponse.data.length > 0;

          // Documentation is already confirmed by this submission
          const hasDocumentation = true;

          // Check if all required information is available
          if (hasPersonalInfo && hasGatInfo && hasProgramSelection && hasDocumentation) {
            // Navigate to the next step with GAT ID if available
            if (gatIdFromUrl) {
              console.log(`Navigating to payment page with GAT ID: ${gatIdFromUrl}`);
              navigate(`/application/payment?gat=${gatIdFromUrl}`);
            } else {
              console.log('Navigating to payment page without GAT ID');
              navigate('/application/payment');
            }
          } else {
            // Determine which step is missing and navigate there
            if (!hasPersonalInfo) {
              toast.error('Personal information is missing. Please complete it first.');
              navigate('/application/personal-info');
            } else if (!hasGatInfo) {
              toast.error('GAT information is missing. Please complete it first.');
              navigate('/application/gat');
            } else if (!hasProgramSelection) {
              toast.error('Program selection is missing. Please complete it first.');
              navigate('/application/program-selection');
            }
          }
        } catch (error) {
          console.error('Error checking application completeness:', error);
          toast.error('Error checking application completeness. Please try again.');
        }
      } else {
        toast.error('Failed to upload documentation. Please try again.');
      }
    } catch (error: any) {
      console.error('Error uploading documentation:', error);

      if (error.response?.data) {
        // Handle specific error messages from the API
        const errorData = error.response.data;

        if (typeof errorData === 'string') {
          toast.error(errorData);
        } else if (typeof errorData === 'object') {
          // Display each field error
          Object.entries(errorData).forEach(([field, errors]) => {
            if (Array.isArray(errors) && errors.length > 0) {
              toast.error(`${field}: ${errors[0]}`);
            } else if (typeof errors === 'string') {
              toast.error(`${field}: ${errors}`);
            }
          });
        }
      } else {
        toast.error('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get file name from URL
  const getFileNameFromUrl = (url: string) => {
    if (!url) return '';
    const parts = url.split('/');
    return parts[parts.length - 1];
  };

  // Helper functions to determine document requirements based on program level and admission type
  const isDocumentRequired = (documentType: string): boolean => {
    if (!programLevel) return false;

    // BSC/BA requirements (identical for both regular and extension programs)
    if (programLevel === 'BSC/BA') {
      if (documentType === 'grade_12_certificate' || documentType === 'grade_9_12_transcript') {
        return true;
      }
      return false;
    }

    // MSC/MBA requirements for REGULAR admission
    if (programLevel === 'MSC/MBA' && admissionType === 'REGULAR') {
      if (documentType === 'degree' || documentType === 'sponsorship' || documentType === 'student_copy') {
        return true;
      }
      return false;
    }

    // PHD requirements
    if (programLevel === 'PHD') {
      if (documentType === 'degree' || documentType === 'sponsorship' ||
          documentType === 'recommendation' || documentType === 'conceptnote') {
        return true;
      }
      return false;
    }

    // Default case (MSC/MBA with other admission types)
    if (documentType === 'degree' || documentType === 'student_copy') {
      return true;
    }

    return false;
  };

  const isDocumentOptional = (documentType: string): boolean => {
    if (!programLevel) return false;

    // MSC/MBA REGULAR optional documents
    if (programLevel === 'MSC/MBA' && admissionType === 'REGULAR') {
      if (documentType === 'recommendation') {
        return true;
      }
      return false;
    }

    // PHD optional documents
    if (programLevel === 'PHD') {
      if (documentType === 'student_copy' || documentType === 'publication') {
        return true;
      }
      return false;
    }

    // Default case (MSC/MBA with other admission types)
    if (documentType === 'sponsorship') {
      return true;
    }

    return false;
  };

  const isDocumentNeeded = (documentType: string): boolean => {
    return isDocumentRequired(documentType) || isDocumentOptional(documentType);
  };

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        {/* Hero Section */}
        <Card className="shadow-2xl border-0 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
            <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                 style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
            <div className="relative z-10 flex items-center">
              <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                <Upload className="h-8 w-8 text-[#1a73c0]" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-white">Documentation Upload</CardTitle>
                <CardDescription className="text-blue-100 mt-1">
                  {existingDocumentation ? 'Update your application documents' : 'Please upload the required documents for your application'}
                </CardDescription>
                {isLoadingExistingData && (
                  <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading your documents...
                  </div>
                )}
                {existingDocumentation && !isLoadingExistingData && (
                  <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    You are editing your previously uploaded documents
                  </div>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-8">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                  <div className="space-y-6">
                    <div className="flex items-center">
                      <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                        <FileText className="h-5 w-5 text-[#1a73c0]" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-800">Required Documents</h3>
                      <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                    </div>

                    {/* Document Requirements Guide */}
                    {programLevel && (
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6">
                        <div className="flex items-start">
                          <Info className="h-5 w-5 text-[#1a73c0] mt-0.5 mr-3 flex-shrink-0" />
                          <div>
                            <h3 className="font-medium text-[#1a73c0] mb-2">Document Requirements for {programLevel} Program</h3>

                            {programLevel === 'BSC/BA' && (
                              <div className="space-y-2">
                                <p className="text-sm text-gray-700">For Bachelor's degree programs:</p>
                                <ul className="list-disc pl-5 text-sm text-gray-700 space-y-1">
                                  <li><span className="font-semibold text-green-600">Mandatory:</span> Grade 12 Certificate and Grade 9-12 Transcript</li>
                                  <li><span className="font-semibold text-red-500">Not Required:</span> All other documents</li>
                                  <li>These requirements apply to both regular and extension programs</li>
                                </ul>
                              </div>
                            )}

                            {programLevel === 'MSC/MBA' && admissionType === 'REGULAR' && (
                              <div className="space-y-2">
                                <p className="text-sm text-gray-700">For Master's degree programs (Regular):</p>
                                <ul className="list-disc pl-5 text-sm text-gray-700 space-y-1">
                                  <li><span className="font-semibold text-green-600">Mandatory:</span> Degree Certificate, Sponsorship Letter, Student Copy</li>
                                  <li><span className="font-semibold text-blue-500">Optional:</span> Letter of Recommendation</li>
                                  <li><span className="font-semibold text-red-500">Not Required:</span> Grade 12 Certificate, Grade 9-12 Transcript, Publication, Concept Note</li>
                                </ul>
                              </div>
                            )}

                            {programLevel === 'MSC/MBA' && admissionType !== 'REGULAR' && (
                              <div className="space-y-2">
                                <p className="text-sm text-gray-700">For Master's degree programs (Extension/Distance):</p>
                                <ul className="list-disc pl-5 text-sm text-gray-700 space-y-1">
                                  <li><span className="font-semibold text-green-600">Mandatory:</span> Degree Certificate, Student Copy</li>
                                  <li><span className="font-semibold text-blue-500">Optional:</span> Sponsorship Letter</li>
                                  <li><span className="font-semibold text-red-500">Not Required:</span> Recommendation Letter, Grade 12 Certificate, Grade 9-12 Transcript, Publication, Concept Note</li>
                                </ul>
                              </div>
                            )}

                            {programLevel === 'PHD' && (
                              <div className="space-y-2">
                                <p className="text-sm text-gray-700">For Doctoral programs:</p>
                                <ul className="list-disc pl-5 text-sm text-gray-700 space-y-1">
                                  <li><span className="font-semibold text-green-600">Mandatory:</span> Degree Certificate, Sponsorship Letter, Recommendation Letter, Concept Note</li>
                                  <li><span className="font-semibold text-blue-500">Optional:</span> Student Copy, Publication</li>
                                  <li><span className="font-semibold text-red-500">Not Required:</span> Grade 12 Certificate, Grade 9-12 Transcript</li>
                                </ul>
                              </div>
                            )}

                            <div className="mt-3 text-sm">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                                Mandatory
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                                Optional
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Not Required
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {isLoadingProgramInfo && (
                      <div className="flex justify-center items-center py-4 mb-4">
                        <Loader2 className="h-6 w-6 animate-spin text-[#1a73c0] mr-2" />
                        <span className="text-gray-600">Loading document requirements...</span>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Degree Certificate */}
                    {(isDocumentNeeded('degree') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="degree"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('degree') ? 'border-green-200' :
                            isDocumentOptional('degree') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Degree Certificate</FormLabel>
                              {isDocumentRequired('degree') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('degree') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf,.jpg,.jpeg,.png"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.degree && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.degree)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.degree, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your degree certificate (PDF, JPG, PNG)</p>
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Sponsorship Letter */}
                    {(isDocumentNeeded('sponsorship') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="sponsorship"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('sponsorship') ? 'border-green-200' :
                            isDocumentOptional('sponsorship') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Sponsorship Letter</FormLabel>
                              {isDocumentRequired('sponsorship') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('sponsorship') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.sponsorship && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.sponsorship)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.sponsorship, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your sponsorship letter (PDF only)</p>
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Student Copy */}
                    {(isDocumentNeeded('student_copy') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="student_copy"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('student_copy') ? 'border-green-200' :
                            isDocumentOptional('student_copy') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Student Copy</FormLabel>
                              {isDocumentRequired('student_copy') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('student_copy') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf,.jpg,.jpeg,.png"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.student_copy && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.student_copy)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.student_copy, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your student copy (PDF, JPG, PNG)</p>
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Recommendation Letter */}
                    {(isDocumentNeeded('recommendation') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="recommendation"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('recommendation') ? 'border-green-200' :
                            isDocumentOptional('recommendation') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Recommendation Letter</FormLabel>
                              {isDocumentRequired('recommendation') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('recommendation') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.recommendation && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.recommendation)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.recommendation, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your recommendation letter (PDF only)</p>
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Publication */}
                    {(isDocumentNeeded('publication') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="publication"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('publication') ? 'border-green-200' :
                            isDocumentOptional('publication') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Publication</FormLabel>
                              {isDocumentRequired('publication') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('publication') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.publication && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.publication)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.publication, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your publication (PDF only)</p>
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Concept Note */}
                    {(isDocumentNeeded('conceptnote') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="conceptnote"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('conceptnote') ? 'border-green-200' :
                            isDocumentOptional('conceptnote') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Concept Note</FormLabel>
                              {isDocumentRequired('conceptnote') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('conceptnote') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.conceptnote && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.conceptnote)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.conceptnote, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your concept note (PDF only)</p>
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Grade 12 Certificate */}
                    {(isDocumentNeeded('grade_12_certificate') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="grade_12_certificate"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('grade_12_certificate') ? 'border-green-200' :
                            isDocumentOptional('grade_12_certificate') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Grade 12 Certificate</FormLabel>
                              {isDocumentRequired('grade_12_certificate') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('grade_12_certificate') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf,.jpg,.jpeg,.png"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.grade_12_certificate && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.grade_12_certificate)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.grade_12_certificate, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your Grade 12 certificate (PDF, JPG, JPEG, PNG)</p>
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Grade 9-12 Transcript */}
                    {(isDocumentNeeded('grade_9_12_transcript') || !programLevel) && (
                      <FormField
                        control={form.control}
                        name="grade_9_12_transcript"
                        render={({ field: { onChange, value, ...rest } }) => (
                          <FormItem className={`bg-white p-4 rounded-lg shadow border ${
                            isDocumentRequired('grade_9_12_transcript') ? 'border-green-200' :
                            isDocumentOptional('grade_9_12_transcript') ? 'border-blue-200' : 'border-gray-100'
                          } hover:border-blue-200 transition-all`}>
                            <div className="flex items-center">
                              <FormLabel className="text-gray-700 font-medium">Grade 9-12 Transcript</FormLabel>
                              {isDocumentRequired('grade_9_12_transcript') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Required
                                </span>
                              )}
                              {isDocumentOptional('grade_9_12_transcript') && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Optional
                                </span>
                              )}
                            </div>
                            <div className="relative">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md z-10">
                                <FileUp className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <FormControl>
                                <Input
                                  type="file"
                                  accept=".pdf,.jpg,.jpeg,.png"
                                  onChange={(e) => onChange(e.target.files)}
                                  {...rest}
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20 cursor-pointer"
                                />
                              </FormControl>
                            </div>
                            {existingDocumentation?.grade_9_12_transcript && (
                              <div className="flex items-center text-sm text-green-600 mt-2">
                                <span>Current file: {getFileNameFromUrl(existingDocumentation.grade_9_12_transcript)}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="ml-2 h-6 px-2 text-blue-600"
                                  onClick={() => window.open(existingDocumentation.grade_9_12_transcript, '_blank')}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" /> View
                                </Button>
                              </div>
                            )}
                            <FormMessage className="text-red-500 text-sm mt-1" />
                            <p className="text-xs text-gray-500 mt-2">Upload your Grade 9-12 transcript (PDF, JPG, JPEG, PNG)</p>
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                  </div>

                  <div className="flex justify-end space-x-6 pt-10 border-t mt-10 bg-gray-50 -mx-8 -mb-8 px-8 py-6 rounded-b-lg">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        // Navigate back to program selection with GAT ID if available
                        if (gatIdFromUrl) {
                          console.log(`Navigating back to program selection with GAT ID: ${gatIdFromUrl}`);
                          navigate(`/application/program-selection?gat=${gatIdFromUrl}`);
                        } else {
                          console.log('Navigating back to program selection without GAT ID');
                          navigate('/application/program-selection');
                        }
                      }}
                      className="px-8 py-3 border-gray-300 hover:bg-gray-50 transition-colors text-base"
                    >
                      Back
                    </Button>
                    <Button
                      type="submit"
                      className="px-8 py-3 bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-base"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <span className="flex items-center">
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          Saving...
                        </span>
                      ) : existingDocumentation ? (
                        <span className="flex items-center">
                          Update and Continue
                          <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </span>
                      ) : (
                        <span className="flex items-center">
                          Save and Continue
                          <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </span>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
    </DashboardLayout>
  );
};

export default DocumentationUpload;
