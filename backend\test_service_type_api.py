#!/usr/bin/env python
"""
Test script for Service Type API endpoints.
This script tests the basic CRUD operations and custom endpoints.
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from setups.certificate_type.models import CertificateType
from setups.service_type.models import ServiceType

User = get_user_model()

def get_auth_headers():
    """Create a test user and get JWT token for authentication."""
    try:
        # Try to get existing test user
        user = User.objects.get(username='testuser')
    except User.DoesNotExist:
        # Create test user
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    # Generate JWT token
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)

    return {
        'HTTP_AUTHORIZATION': f'Bearer {access_token}',
        'content_type': 'application/json'
    }

def test_service_type_api():
    """Test Service Type API endpoints."""
    print("=" * 60)
    print("TESTING SERVICE TYPE API")
    print("=" * 60)

    client = Client()
    auth_headers = get_auth_headers()

    # Test 1: List service types (should be empty initially)
    print("\n1. Testing GET /api/service-types/")
    try:
        response = client.get('/api/service-types/', **auth_headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data)} service types")
        else:
            print(f"❌ Error: {response.content}")
            return
    except Exception as e:
        print(f"❌ Exception: {e}")
        return
    
    # Create some test certificate types first
    print("\n2. Creating test certificate types...")
    cert_types = []
    for name in ['Academic Transcript', 'Diploma Certificate', 'Verification Letter']:
        cert_type, created = CertificateType.objects.get_or_create(
            name=name,
            defaults={'is_active': True}
        )
        cert_types.append(cert_type)
        if created:
            print(f"✅ Created certificate type: {name}")
        else:
            print(f"📋 Certificate type already exists: {name}")

    # Test 2: Create a new service type
    print("\n3. Testing POST /api/service-types/")
    service_data = {
        'name': 'Academic Verification Service',
        'fee': '25.50',
        'is_active': True,
        'document_type_ids': [str(cert_types[0].id), str(cert_types[1].id)]
    }

    try:
        response = client.post(
            '/api/service-types/',
            data=json.dumps(service_data),
            **auth_headers
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            service_type_data = response.json()
            service_type_id = service_type_data['id']
            print(f"✅ Created service type: {service_type_data['name']}")
            print(f"   ID: {service_type_id}")
            print(f"   Fee: ${service_type_data['fee']}")
            print(f"   Document Types: {len(service_type_data['document_types'])}")
        else:
            print(f"❌ Error: {response.content}")
            return
    except Exception as e:
        print(f"❌ Exception: {e}")
        return
    
        # Test 3: Get specific service type
        print(f"\n4. Testing GET /api/service-types/{service_type_id}/")
        try:
            response = client.get(f'/api/service-types/{service_type_id}/', **auth_headers)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Retrieved service type: {data['name']}")
                print(f"   Associated document types: {[dt['name'] for dt in data['document_types']]}")
            else:
                print(f"❌ Error: {response.content}")
        except Exception as e:
            print(f"❌ Exception: {e}")

        # Test 4: Update service type
        print(f"\n5. Testing PUT /api/service-types/{service_type_id}/")
        update_data = {
            'name': 'Premium Academic Verification Service',
            'fee': '35.00',
            'is_active': True,
            'document_type_ids': [str(cert_types[0].id), str(cert_types[1].id), str(cert_types[2].id)]
        }

        try:
            response = client.put(
                f'/api/service-types/{service_type_id}/',
                data=json.dumps(update_data),
                **auth_headers
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Updated service type: {data['name']}")
                print(f"   New fee: ${data['fee']}")
                print(f"   Document types count: {data['document_types_count']}")
            else:
                print(f"❌ Error: {response.content}")
        except Exception as e:
            print(f"❌ Exception: {e}")

        # Test 5: Toggle status
        print(f"\n6. Testing POST /api/service-types/{service_type_id}/toggle_status/")
        try:
            response = client.post(f'/api/service-types/{service_type_id}/toggle_status/', **auth_headers)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {data['message']}")
                print(f"   New status: {'Active' if data['service_type']['is_active'] else 'Inactive'}")
            else:
                print(f"❌ Error: {response.content}")
        except Exception as e:
            print(f"❌ Exception: {e}")

    # Test 6: Get active service types
    print("\n7. Testing GET /api/service-types/active/")
    try:
        response = client.get('/api/service-types/active/', **auth_headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data)} active service types")
        else:
            print(f"❌ Error: {response.content}")
    except Exception as e:
        print(f"❌ Exception: {e}")

    # Test 7: Search service types
    print("\n8. Testing GET /api/service-types/search/?q=verification")
    try:
        response = client.get('/api/service-types/search/?q=verification', **auth_headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', data)
            print(f"✅ Search found {len(results)} service types")
        else:
            print(f"❌ Error: {response.content}")
    except Exception as e:
        print(f"❌ Exception: {e}")

    # Test 8: Filter by fee range
    print("\n9. Testing GET /api/service-types/?fee_min=20&fee_max=40")
    try:
        response = client.get('/api/service-types/?fee_min=20&fee_max=40', **auth_headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Filter found {len(data)} service types in fee range $20-$40")
        else:
            print(f"❌ Error: {response.content}")
    except Exception as e:
        print(f"❌ Exception: {e}")

    print("\n" + "=" * 60)
    print("SERVICE TYPE API TESTING COMPLETED")
    print("=" * 60)

if __name__ == '__main__':
    test_service_type_api()
