import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import Layout from '@/components/Layout';
import DocumentTitle from '@/components/DocumentTitle';
import AdvancedAlumniApplicationForm from '@/components/AdvancedAlumniApplicationForm';

const PublicAlumniApplication: React.FC = () => {
  const [searchParams] = useSearchParams();
  const formType = searchParams.get('form') as 'form1' | 'form2' || 'form2';

  // Custom pattern background for hero section
  const heroPatternImage = '/images/university-pattern.svg';

  // State for organization name
  const [orgName, setOrgName] = useState<string>('University of Gondar');

  return (
    <Layout>
      <DocumentTitle pageTitle={`Alumni Application - ${formType === 'form1' ? 'Complete' : 'Simplified'}`} />

      {/* Hero Section with Animated Patterned Background - Half Height */}
      <section className="relative py-4 md:py-6 h-[175px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          <h1 className="text-xl md:text-2xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
            {orgName} Alumni Application
          </h1>
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            {formType === 'form1'
              ? 'Complete application form for official transcripts and comprehensive academic services.'
              : 'Simplified application form for basic services and certificate requests.'
            }
          </p>
        </div>
      </section>

      <AdvancedAlumniApplicationForm formType={formType} />
    </Layout>
  );
};

export default PublicAlumniApplication;
