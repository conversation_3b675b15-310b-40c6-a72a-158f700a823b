<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="600" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background pattern with opacity gradient -->
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1a73c0;stop-opacity:0.9"/>
      <stop offset="50%" style="stop-color:#1a73c0;stop-opacity:0.7"/>
      <stop offset="100%" style="stop-color:#1a73c0;stop-opacity:0.8"/>
    </linearGradient>
    
    <!-- Pattern definitions -->
    <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.1"/>
    </pattern>
    
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="url(#smallGrid)"/>
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
    </pattern>
    
    <pattern id="diagonalPattern" width="60" height="60" patternUnits="userSpaceOnUse" patternTransform="rotate(45)">
      <rect width="30" height="30" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.1" x="0" y="0"/>
    </pattern>
    
    <!-- Binary pattern -->
    <pattern id="binaryPattern" width="120" height="120" patternUnits="userSpaceOnUse">
      <text x="10" y="20" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">01001</text>
      <text x="60" y="20" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">10110</text>
      <text x="10" y="40" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">10011</text>
      <text x="60" y="40" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">01010</text>
      <text x="10" y="60" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">11001</text>
      <text x="60" y="60" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">00101</text>
      <text x="10" y="80" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">01100</text>
      <text x="60" y="80" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">11010</text>
      <text x="10" y="100" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">10010</text>
      <text x="60" y="100" font-family="monospace" font-size="12" fill="white" fill-opacity="0.15">01101</text>
    </pattern>
    
    <!-- Mask for right side -->
    <mask id="rightMask">
      <rect x="600" y="0" width="600" height="600" fill="white"/>
    </mask>
    
    <!-- Mask for left side -->
    <mask id="leftMask">
      <rect x="0" y="0" width="600" height="600" fill="white"/>
    </mask>
  </defs>
  
  <!-- Main background -->
  <rect width="1200" height="600" fill="url(#blueGradient)"/>
  
  <!-- Left side patterns - geometric -->
  <g mask="url(#leftMask)">
    <rect width="1200" height="600" fill="url(#grid)"/>
    <rect width="1200" height="600" fill="url(#diagonalPattern)"/>
    
    <!-- Circles and dots -->
    <circle cx="150" cy="150" r="80" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
    <circle cx="150" cy="150" r="40" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.3"/>
    <circle cx="150" cy="150" r="5" fill="white" fill-opacity="0.4"/>
    
    <circle cx="450" cy="300" r="100" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
    <circle cx="450" cy="300" r="50" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.3"/>
    <circle cx="450" cy="300" r="5" fill="white" fill-opacity="0.4"/>
    
    <circle cx="250" cy="450" r="70" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
    <circle cx="250" cy="450" r="35" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.3"/>
    <circle cx="250" cy="450" r="5" fill="white" fill-opacity="0.4"/>
  </g>
  
  <!-- Right side patterns - binary and education -->
  <g mask="url(#rightMask)">
    <rect width="1200" height="600" fill="url(#binaryPattern)"/>
    
    <!-- Education icons -->
    <!-- Graduation Cap -->
    <g transform="translate(750, 150) scale(1.5)" fill="none" stroke="white" stroke-width="1.5" stroke-opacity="0.4">
      <path d="M0 10l20-10 20 10-20 10z"/>
      <path d="M40 10v13"/>
      <path d="M10 15v8c0 0 5 5 10 5s10-5 10-5v-8"/>
      <path d="M20 20v8"/>
    </g>
    
    <!-- Book -->
    <g transform="translate(900, 300) scale(1.5)" fill="none" stroke="white" stroke-width="1.5" stroke-opacity="0.4">
      <path d="M5 5h10c5 0 5 2.5 5 5v20c0-2.5-2.5-5-5-5H5z"/>
      <path d="M35 5H25c-5 0-5 2.5-5 5v20c0-2.5 2.5-5 5-5h10z"/>
    </g>
    
    <!-- Computer -->
    <g transform="translate(700, 400) scale(1.5)" fill="none" stroke="white" stroke-width="1.5" stroke-opacity="0.4">
      <rect x="5" y="5" width="30" height="20" rx="2"/>
      <path d="M5 25h30"/>
      <path d="M15 30h10"/>
      <path d="M20 25v5"/>
    </g>
  </g>
  
  <!-- Center divider with nodes -->
  <line x1="600" y1="0" x2="600" y2="600" stroke="white" stroke-width="2" stroke-opacity="0.3"/>
  
  <!-- Nodes on divider -->
  <g>
    <!-- Top node - Graduation -->
    <circle cx="600" cy="150" r="15" fill="white" fill-opacity="0.2" stroke="white" stroke-width="1" stroke-opacity="0.5"/>
    <g transform="translate(592, 142) scale(0.7)" fill="none" stroke="white" stroke-width="2">
      <path d="M0 10l20-10 20 10-20 10z"/>
      <path d="M10 15v8c0 0 5 5 10 5s10-5 10-5v-8"/>
    </g>
    
    <!-- Middle node - Book -->
    <circle cx="600" cy="300" r="15" fill="white" fill-opacity="0.2" stroke="white" stroke-width="1" stroke-opacity="0.5"/>
    <g transform="translate(590, 290) scale(0.7)" fill="none" stroke="white" stroke-width="2">
      <path d="M5 5h10c5 0 5 2.5 5 5v20c0-2.5-2.5-5-5-5H5z"/>
      <path d="M35 5H25c-5 0-5 2.5-5 5v20c0-2.5 2.5-5 5-5h10z"/>
    </g>
    
    <!-- Bottom node - Computer -->
    <circle cx="600" cy="450" r="15" fill="white" fill-opacity="0.2" stroke="white" stroke-width="1" stroke-opacity="0.5"/>
    <g transform="translate(590, 440) scale(0.7)" fill="none" stroke="white" stroke-width="2">
      <rect x="5" y="5" width="30" height="20" rx="2"/>
      <path d="M5 25h30"/>
      <path d="M15 30h10"/>
    </g>
  </g>
  
  <!-- Subtle wave pattern at bottom -->
  <path d="M0 550 Q 300 500, 600 550 T 1200 550 V 600 H 0 Z" fill="white" fill-opacity="0.05"/>
</svg>
