from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q

class RegistrationPeriod(models.Model):
    program = models.ForeignKey(
        'program.Program',
        on_delete=models.CASCADE,
        related_name='registration_periods'
    )
    year = models.ForeignKey(
        'year.Year',
        on_delete=models.CASCADE,
        related_name='registration_periods'
    )
    term = models.ForeignKey(
        'term.Term',
        on_delete=models.CASCADE,
        related_name='registration_periods'
    )
    open_date = models.DateTimeField(
        help_text="Must be in the future"
    )
    close_date = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(
        default=False,
        help_text="Only one active period per program allowed"
    )

    class Meta:
        unique_together = [['program', 'year', 'term']]
        ordering = ['-open_date']
        verbose_name = "Registration Period"
        verbose_name_plural = "Registration Periods"

    def is_open(self):
        """Check if registration is currently active."""
        now = timezone.now()
        return self.open_date <= now < self.close_date and self.is_active

    def clean(self):
        """Full model validation."""
        now = timezone.now()

        # Ensure dates are timezone-aware
        open_date = (
            timezone.make_aware(self.open_date)
            if timezone.is_naive(self.open_date)
            else self.open_date
        )
        close_date = (
            timezone.make_aware(self.close_date)
            if timezone.is_naive(self.close_date)
            else self.close_date
        )

        # 1. Validate open_date is in the future
        if open_date <= now:
            raise ValidationError(
                {'open_date': 'Opening date must be in the future.'}
            )

        # 2. Validate close_date is after open_date
        if open_date >= close_date:
            raise ValidationError(
                {'close_date': 'Closing date must be after opening date.'}
            )

        # 3. Validate max duration (e.g., 1 year)
        max_duration = timezone.timedelta(days=365)
        if (close_date - open_date) > max_duration:
            raise ValidationError(
                'Registration period cannot exceed 1 year.'
            )

        # 4. Only one active period per program
        if self.is_active:
            active_periods = RegistrationPeriod.objects.filter(
                program=self.program,
                is_active=True
            ).exclude(pk=self.pk if self.pk else None)
            
            if active_periods.exists():
                raise ValidationError(
                    'Another active registration period exists for this program.'
                )

        # 5. Prevent overlapping periods for same program/year/term
        overlapping_periods = RegistrationPeriod.objects.filter(
            program=self.program,
            year=self.year,
            term=self.term
        ).exclude(pk=self.pk if self.pk else None).filter(
            Q(open_date__lte=close_date, close_date__gte=open_date)
        )

        if overlapping_periods.exists():
            raise ValidationError(
                'This period overlaps with an existing one for the same program, year, and term.'
            )

    def save(self, *args, **kwargs):
        """Run full validation before saving."""
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return (
            f"{self.program.program_name if self.program else 'No Program'} - "
            f"{self.year.year if self.year else 'No Year'} - "
            f"{self.term.name if self.term else 'No Term'} "
            f"({self.open_date.strftime('%Y-%m-%d %H:%M')} to "
            f"{self.close_date.strftime('%Y-%m-%d %H:%M')})"
        )