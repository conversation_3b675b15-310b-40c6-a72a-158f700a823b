from rest_framework import serializers
from .models import RegistrationPeriod
from django.utils import timezone
from datetime import datetime

class RegistrationPeriodSerializer(serializers.ModelSerializer):
    program_name = serializers.SerializerMethodField()
    year_name = serializers.SerializerMethodField()
    term_name = serializers.SerializerMethodField()

    def get_program_name(self, obj):
        return obj.program.program_name if obj.program else None

    def get_year_name(self, obj):
        return obj.year.year if obj.year else None

    def get_term_name(self, obj):
        return obj.term.name if obj.term else None

    class Meta:
        model = RegistrationPeriod
        fields = ['id', 'program', 'program_name', 'year', 'year_name', 'term', 'term_name', 'open_date', 'close_date', 'created_at', 'is_active']

    def validate_open_date(self, value):
        """
        Ensure that the open_date is after the current date and time for new registration periods
        or when is_active is true.
        """
        print(f"Validating open_date: {value}, instance: {self.instance}")

        # Convert value to datetime if it is a string
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value)
                print(f"Converted string to datetime: {value}")
            except ValueError as e:
                print(f"Error converting date string: {e}")
                raise serializers.ValidationError(f"Invalid date format: {e}")

        # Make sure the open_date is timezone-aware
        if value.tzinfo is None:
            value = timezone.make_aware(value)
            print(f"Made timezone-aware: {value}")

        # Check if is_active is set to True in the incoming data
        is_active = self.initial_data.get('is_active')
        if isinstance(is_active, str):
            is_active = is_active.lower() == 'true'

        # If is_active is True, validate that open_date is in the future
        if is_active:
            now = timezone.now()

            if value <= now:
                print(f"Validation failed: open_date {value} is not in the future and is_active is True")
                raise serializers.ValidationError("When active, the opening date must be in the future.")

        print(f"Open date validation passed: {value}")
        return value

    def validate_close_date(self, value):
        """
        Ensure that the close_date is after the open_date.
        """
        print(f"Validating close_date: {value}, instance: {self.instance}")

        # Convert value to datetime if it is a string
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value)
                print(f"Converted close_date string to datetime: {value}")
            except ValueError as e:
                print(f"Error converting close_date string: {e}")
                raise serializers.ValidationError(f"Invalid close date format: {e}")

        # Make sure the close_date is timezone-aware
        if value.tzinfo is None:
            value = timezone.make_aware(value)
            print(f"Made close_date timezone-aware: {value}")

        open_date = self.initial_data.get('open_date')
        print(f"Open date from initial data: {open_date}")

        # Convert open_date to datetime if it is a string
        if isinstance(open_date, str):
            try:
                open_date = datetime.fromisoformat(open_date)
                print(f"Converted open_date string to datetime: {open_date}")
            except ValueError as e:
                print(f"Error converting open_date string: {e}")
                # Don't raise here, as open_date will be validated separately

        # Make sure open_date is timezone-aware
        if open_date and open_date.tzinfo is None:
            open_date = timezone.make_aware(open_date)
            print(f"Made open_date timezone-aware: {open_date}")

        if open_date and value <= open_date:
            print(f"Validation failed: close_date {value} is not after open_date {open_date}")
            raise serializers.ValidationError("The closing date must be after the opening date.")

        print(f"Close date validation passed: {value}")
        return value

    def validate(self, data):
        """
        Ensure that open_date is not equal to close_date and validate open_date when is_active is True.
        """
        print(f"Running validate method, instance: {self.instance}")
        print(f"Validated data: {data}")

        open_date = data.get('open_date')
        close_date = data.get('close_date')
        is_active = data.get('is_active')

        print(f"Final open_date: {open_date}, close_date: {close_date}, is_active: {is_active}")

        if open_date == close_date:
            print("Validation failed: open_date equals close_date")
            raise serializers.ValidationError("The opening date cannot be the same as the closing date.")

        # Double-check the open_date validation when is_active is True
        # This ensures the validation is applied during both creation and updates
        if is_active and open_date:
            now = timezone.now()

            if open_date <= now:
                print(f"Validation failed: open_date {open_date} is not in the future and is_active is True")
                raise serializers.ValidationError({"open_date": "When active, the opening date must be in the future."})

        print("All validation passed")
        return data
