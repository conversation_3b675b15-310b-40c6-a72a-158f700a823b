import django_filters
from django.db.models import Q
from .models import ServiceType
from setups.certificate_type.models import CertificateType


class ServiceTypeFilter(django_filters.FilterSet):
    """Filter class for ServiceType model."""
    
    name = django_filters.CharFilter(
        field_name='name',
        lookup_expr='icontains',
        help_text='Filter by service type name (case-insensitive partial match)'
    )

    description = django_filters.CharFilter(
        field_name='description',
        lookup_expr='icontains',
        help_text='Filter by service type description (case-insensitive partial match)'
    )
    
    is_active = django_filters.BooleanFilter(
        field_name='is_active',
        help_text='Filter by active status (true/false)'
    )
    
    fee_min = django_filters.NumberFilter(
        field_name='fee',
        lookup_expr='gte',
        help_text='Filter by minimum fee amount'
    )
    
    fee_max = django_filters.NumberFilter(
        field_name='fee',
        lookup_expr='lte',
        help_text='Filter by maximum fee amount'
    )
    
    fee_range = django_filters.RangeFilter(
        field_name='fee',
        help_text='Filter by fee range (e.g., fee_range_min=10&fee_range_max=100)'
    )
    
    document_types = django_filters.ModelMultipleChoiceFilter(
        field_name='document_types',
        queryset=CertificateType.objects.filter(is_active=True),
        help_text='Filter by associated certificate types (multiple values allowed)'
    )
    
    has_document_types = django_filters.BooleanFilter(
        method='filter_has_document_types',
        help_text='Filter by whether service type has any associated document types'
    )
    
    created_after = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte',
        help_text='Filter by creation date (after specified date)'
    )
    
    created_before = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte',
        help_text='Filter by creation date (before specified date)'
    )
    
    search = django_filters.CharFilter(
        method='filter_search',
        help_text='Search across name, description, and related fields'
    )

    class Meta:
        model = ServiceType
        fields = {
            'name': ['exact', 'icontains'],
            'description': ['exact', 'icontains'],
            'is_active': ['exact'],
            'fee': ['exact', 'gte', 'lte'],
            'created_at': ['exact', 'gte', 'lte'],
            'updated_at': ['exact', 'gte', 'lte'],
        }

    def filter_has_document_types(self, queryset, name, value):
        """Filter by whether service type has associated document types."""
        if value is True:
            return queryset.filter(document_types__isnull=False).distinct()
        elif value is False:
            return queryset.filter(document_types__isnull=True)
        return queryset

    def filter_search(self, queryset, name, value):
        """Search across multiple fields."""
        if not value:
            return queryset

        return queryset.filter(
            Q(name__icontains=value) |
            Q(description__icontains=value) |
            Q(document_types__name__icontains=value)
        ).distinct()
