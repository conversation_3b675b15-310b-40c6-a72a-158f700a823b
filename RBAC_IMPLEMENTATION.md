# Django Authentication and Authorization Implementation

## Overview

This document describes the comprehensive Role-Based Access Control (RBAC) implementation that integrates Django's backend authentication and authorization system with the React frontend.

## 🔧 Backend Implementation

### 1. Enhanced User Management Views

**File**: `backend/user_management/views.py`

New RBAC endpoints added:
- `RBACInfoView` - Comprehensive user RBAC information
- `PermissionCheckView` - Real-time permission validation
- `RoleValidationView` - Security audit and role validation

### 2. RBAC Middleware

**File**: `backend/user_management/middleware.py`

- `RBACMiddleware` - Adds RBAC helper methods to requests
- `RoleBasedAccessMiddleware` - URL-level access control

### 3. Permission Classes

**File**: `backend/user_management/permissions.py`

- `RoleBasedPermission` - Base permission class
- `AdminOnlyPermission` - Administrator access
- `StaffOnlyPermission` - Staff-level access
- `UserManagementPermission` - User management operations
- `RoleManagementPermission` - Role management operations

### 4. API Endpoints

New endpoints available:
```
GET /api/user/rbac-info/ - Get comprehensive RBAC info
POST /api/user/permission-check/ - Check specific permissions
POST /api/user/role-validation/ - Validate user roles
GET /api/user/role-validation/ - Security audit (Admin only)
```

## 🎨 Frontend Implementation

### 1. Enhanced RBAC Context

**File**: `frontend/src/contexts/RBACContext.tsx`

Comprehensive authentication context with:
- Real-time backend validation
- Role and permission checking
- Security validation
- Access level determination

### 2. Permission and Role Hooks

**File**: `frontend/src/hooks/usePermissions.ts`

Utility hooks for:
- `usePermissions()` - Permission checking
- `useRoles()` - Role checking  
- `useAuth()` - Combined authentication

### 3. Route Protection Components

**File**: `frontend/src/components/RoleBasedRoute.tsx`

Enhanced route protection:
- `RoleBasedRoute` - Flexible role/permission requirements
- `StaffRoute` - Staff-only access
- `StaffLevelRoute` - Enhanced staff access with role validation
- `AdminRoute` - Administrator access
- `SuperuserRoute` - Superuser-only access

### 4. Security Monitoring

**Files**: 
- `frontend/src/components/security/SecurityMonitor.tsx`
- `frontend/src/components/security/UserAudit.tsx`

Security features:
- Real-time security monitoring
- User audit and compliance checking
- Access violation tracking
- Security report generation

### 5. RBAC Testing Interface

**File**: `frontend/src/components/debug/RBACTest.tsx`

Testing and debugging:
- Manual permission/role testing
- Automated comprehensive tests
- Local vs backend validation comparison
- Access pattern verification

## 🔒 Security Features

### 1. Backend Validation

- All frontend permissions are validated against Django backend
- Staff users must have assigned roles/groups
- Comprehensive permission checking
- Security audit logging

### 2. Access Control Matrix

| User Type | is_staff | Groups | Frontend Access | Backend Access |
|-----------|----------|--------|----------------|----------------|
| Superuser | Any | Any | ✅ Full Access | ✅ Full Access |
| Staff | True | Has Groups | ✅ Role-based | ✅ Permission-based |
| Staff | True | No Groups | ❌ Denied | ❌ Denied |
| Regular | False | Any | ✅ User areas only | ✅ User permissions |

### 3. Security Validation Process

1. **User Login** → JWT token issued with user info
2. **Route Access** → Frontend checks local permissions
3. **Backend Validation** → Real-time validation via API
4. **Permission Check** → Django permission system validation
5. **Access Decision** → Grant/deny based on comprehensive checks

## 📋 Role Definitions

### Standard Roles

- **Super Admin** - Full system access
- **Administrator** - Administrative functions
- **Main Registrar** - Registrar operations
- **Registrar Officer** - Registration tasks
- **Department Head** - Department management
- **Verification Clerk** - Verification tasks
- **Official Clerk** - Official document management
- **Service Manager** - Service management

### Permission Categories

- **User Management** - Create, view, edit, delete users
- **Role Management** - Manage roles and permissions
- **Application Management** - Handle applications
- **Graduate Management** - Manage graduate records
- **Official Management** - Handle official documents
- **Service Management** - Manage services
- **System Settings** - System configuration

## 🚀 Usage Examples

### 1. Protecting Routes

```tsx
// Staff-only route
<Route path="/admin" element={
  <StaffLevelRoute>
    <AdminPanel />
  </StaffLevelRoute>
} />

// Specific role requirement
<Route path="/users" element={
  <RoleBasedRoute requiredRoles={['Administrator', 'Super Admin']}>
    <UserManagement />
  </RoleBasedRoute>
} />

// Permission-based access
<Route path="/reports" element={
  <RoleBasedRoute requiredPermissions={['reports.view']}>
    <Reports />
  </RoleBasedRoute>
} />
```

### 2. Using Hooks

```tsx
function MyComponent() {
  const { hasRole, hasPermission } = useAuth();
  const { canManageUsers } = usePermissions();
  const { isAdministrator } = useRoles();

  if (!canManageUsers) {
    return <AccessDenied />;
  }

  return (
    <div>
      {isAdministrator && <AdminTools />}
      {hasPermission('users.create') && <CreateUserButton />}
    </div>
  );
}
```

### 3. Backend Permission Checking

```python
# In Django views
from user_management.permissions import AdminOnlyPermission

class UserManagementView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]
    
    def get(self, request):
        # Only admins can access this
        return Response(data)
```

## 🔧 Configuration

### 1. Django Settings

Ensure these are in your `settings.py`:

```python
INSTALLED_APPS = [
    'rest_framework',
    'rest_framework_simplejwt',
    'user_management',
    # ... other apps
]

MIDDLEWARE = [
    'user_management.middleware.RBACMiddleware',
    'user_management.middleware.RoleBasedAccessMiddleware',
    # ... other middleware
]
```

### 2. Frontend Setup

Wrap your app with the RBAC provider:

```tsx
function App() {
  return (
    <AuthProvider>
      <RBACProvider>
        <Routes>
          {/* Your routes */}
        </Routes>
      </RBACProvider>
    </AuthProvider>
  );
}
```

## 🧪 Testing

### 1. Backend Tests

Run Django tests for permission classes:
```bash
python manage.py test user_management.tests
```

### 2. Frontend Testing

Use the built-in RBAC test interface:
- Navigate to `/graduate-admin?tab=rbac-test`
- Run manual or automated tests
- Verify local vs backend validation

### 3. Security Audit

Regular security audits:
- Check `/graduate-admin?tab=user-audit`
- Review staff users without roles
- Monitor access violations
- Export security reports

## 📊 Monitoring

### 1. Security Dashboard

Access security monitoring at:
- `/graduate-admin?tab=security-monitor`
- Real-time security events
- User compliance audit
- Access violation tracking

### 2. User Audit

Regular user audits:
- Staff users without roles
- Inactive accounts
- Permission mismatches
- Role assignment issues

## 🔄 Migration Guide

### From Simple RBAC

1. Update imports:
```tsx
// Old
import { useSimpleRBAC } from './contexts/SimpleRBACContext';

// New  
import { useRBAC } from './contexts/RBACContext';
import { useAuth } from './hooks/usePermissions';
```

2. Update route protection:
```tsx
// Old
<SuperuserOnly><Component /></SuperuserOnly>

// New
<SuperuserRoute><Component /></SuperuserRoute>
```

3. Update permission checks:
```tsx
// Old
const canAccess = user?.is_staff;

// New
const { hasStaffAccess } = useRoles();
```

## 🛡️ Security Best Practices

1. **Always validate on backend** - Never trust frontend-only checks
2. **Use principle of least privilege** - Grant minimum required permissions
3. **Regular audits** - Monitor user roles and permissions
4. **Staff role requirements** - All staff users must have assigned roles
5. **Session management** - Implement proper session timeout
6. **Logging** - Log all security events and access attempts

## 📝 Troubleshooting

### Common Issues

1. **Staff user without roles** - Assign appropriate groups in Django admin
2. **Permission denied errors** - Check user permissions in backend
3. **Route access issues** - Verify route protection configuration
4. **Backend validation failures** - Check API endpoints and authentication

### Debug Tools

- Use RBAC test interface for debugging
- Check browser console for security warnings
- Review Django logs for permission errors
- Use security monitor for access tracking

## 🔮 Future Enhancements

1. **Dynamic permissions** - Runtime permission assignment
2. **Advanced audit logging** - Detailed security event tracking
3. **Role hierarchies** - Nested role inheritance
4. **Time-based access** - Temporary permission grants
5. **Multi-factor authentication** - Enhanced security for sensitive operations
