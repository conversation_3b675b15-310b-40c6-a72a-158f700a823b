from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator, EmailValidator
from django.utils import timezone
import uuid


class ServiceRequest(models.Model):
    """
    Comprehensive model for handling multiple service types with conditional fields
    and dynamic behavior based on service type selection.
    """
    
    # Student Status Choices
    STUDENT_STATUS_CHOICES = [
        ('active', 'Active Student'),
        ('inactive', 'Inactive Student'),
        ('graduate', 'Graduate'),
    ]
    
    # Year Type Choices
    YEAR_TYPE_CHOICES = [
        ('current', 'Current Year'),
        ('leaving', 'Leaving Year'),
        ('graduation', 'Graduation Year'),
    ]
    
    # Mailing Destination Choices
    MAILING_DESTINATION_CHOICES = [
        ('uog', 'University of Gondar'),
        ('external', 'External Institution'),
    ]
    
    # Mailing Agent Choices
    MAILING_AGENT_CHOICES = [
        ('dhl', 'DHL'),
        ('ems', 'EMS'),
        ('other', 'Other'),
    ]
    
    # Core Fields (Required for all service types)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    email = models.EmailField(validators=[EmailValidator()])
    
    # Phone number validation
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    mobile = models.CharField(validators=[phone_regex], max_length=17)

    # Reference number for public tracking (alumni requests)
    reference_number = models.CharField(max_length=20, unique=True, blank=True, null=True)

    # Foreign Key Relationships
    service_type = models.ForeignKey(
        'service_type.ServiceType',
        on_delete=models.PROTECT,
        related_name='service_requests'
    )
    admission_type = models.ForeignKey(
        'admission_type.AdmissionType',
        on_delete=models.PROTECT,
        related_name='service_requests'
    )
    degree = models.ForeignKey(
        'study_program.StudyProgram',
        on_delete=models.PROTECT,
        related_name='service_requests'
    )

    # College fields with "Other" option
    college = models.ForeignKey(
        'college.College',
        on_delete=models.PROTECT,
        related_name='service_requests',
        blank=True,
        null=True
    )
    college_other = models.CharField(max_length=200, blank=True, null=True)
    is_college_other = models.BooleanField(default=False)

    # Department fields with "Other" option
    department = models.ForeignKey(
        'department.Department',
        on_delete=models.PROTECT,
        related_name='service_requests',
        blank=True,
        null=True
    )
    department_other = models.CharField(max_length=200, blank=True, null=True)
    is_department_other = models.BooleanField(default=False)
    
    # Student Status Fields (conditional based on service type)
    student_status = models.CharField(
        max_length=20,
        choices=STUDENT_STATUS_CHOICES,
        blank=True,
        null=True
    )
    year_ec = models.IntegerField(blank=True, null=True, help_text="Ethiopian Calendar year")
    year_gc = models.IntegerField(blank=True, null=True, help_text="Gregorian Calendar year")
    year_type = models.CharField(
        max_length=20,
        choices=YEAR_TYPE_CHOICES,
        blank=True,
        null=True
    )
    
    # Graduation Fields (for Original Degree service)
    graduation_year_ec = models.IntegerField(blank=True, null=True)
    graduation_year_gc = models.IntegerField(blank=True, null=True)
    
    # Mailing Address Fields (for Official Transcript only)
    mailing_destination = models.CharField(
        max_length=20,
        choices=MAILING_DESTINATION_CHOICES,
        blank=True,
        null=True
    )
    mailing_college = models.ForeignKey(
        'college.College',
        on_delete=models.PROTECT,
        related_name='mailing_service_requests',
        blank=True,
        null=True
    )
    mailing_department = models.ForeignKey(
        'department.Department',
        on_delete=models.PROTECT,
        related_name='mailing_service_requests',
        blank=True,
        null=True
    )
    institute_name = models.CharField(max_length=300, blank=True, null=True)
    institute_country = models.CharField(max_length=100, blank=True, null=True)
    institute_address = models.TextField(blank=True, null=True)
    mailing_agent = models.CharField(
        max_length=20,
        choices=MAILING_AGENT_CHOICES,
        blank=True,
        null=True
    )
    mailing_agent_other = models.CharField(max_length=100, blank=True, null=True)
    
    # Status and Processing Fields
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('completed', 'Completed'),
            ('rejected', 'Rejected'),
        ],
        default='pending'
    )
    
    # Audit Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_service_requests',
        blank=True,
        null=True
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='updated_service_requests',
        blank=True,
        null=True
    )
    
    # Soft Delete Fields
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    deleted_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='deleted_service_requests',
        blank=True,
        null=True
    )
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Service Request'
        verbose_name_plural = 'Service Requests'
        indexes = [
            models.Index(fields=['service_type', 'status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['email']),
            models.Index(fields=['is_deleted']),
        ]
    
    def clean(self):
        """Custom validation based on service type requirements."""
        super().clean()
        
        if not self.service_type:
            return  # Skip validation if service_type is not set
        
        service_name = self.service_type.name.lower()
        
        # Validate college/department "Other" fields
        if self.is_college_other and not self.college_other:
            raise ValidationError({'college_other': 'College name is required when "Other" is selected.'})
        
        if not self.is_college_other and not self.college:
            raise ValidationError({'college': 'College selection is required.'})
        
        if self.is_department_other and not self.department_other:
            raise ValidationError({'department_other': 'Department name is required when "Other" is selected.'})
        
        if not self.is_department_other and not self.department:
            raise ValidationError({'department': 'Department selection is required.'})
        
        # Service-specific validation
        if 'official transcript' in service_name:
            self._validate_official_transcript()
        elif 'original degree' in service_name:
            self._validate_original_degree()
        else:
            self._validate_other_services()
    
    def _validate_official_transcript(self):
        """Validation for Official Transcript service."""
        required_fields = ['student_status', 'year_type', 'mailing_destination']
        
        for field in required_fields:
            if not getattr(self, field):
                raise ValidationError({field: f'{field.replace("_", " ").title()} is required for Official Transcript service.'})
        
        # Validate year fields based on year_type
        if self.year_type in ['current', 'leaving', 'graduation']:
            if not self.year_ec and not self.year_gc:
                raise ValidationError('Either Ethiopian Calendar year or Gregorian Calendar year is required.')
        
        # Validate mailing address fields
        if self.mailing_destination == 'uog':
            if not self.mailing_college:
                raise ValidationError({'mailing_college': 'Mailing college is required for UOG delivery.'})
        elif self.mailing_destination == 'external':
            required_external_fields = ['institute_name', 'institute_country', 'institute_address']
            for field in required_external_fields:
                if not getattr(self, field):
                    raise ValidationError({field: f'{field.replace("_", " ").title()} is required for external delivery.'})
        
        # Validate mailing agent
        if self.mailing_agent == 'other' and not self.mailing_agent_other:
            raise ValidationError({'mailing_agent_other': 'Please specify the mailing agent.'})
    
    def _validate_original_degree(self):
        """Validation for Original Degree service."""
        if not self.graduation_year_ec and not self.graduation_year_gc:
            raise ValidationError('Either graduation year (Ethiopian Calendar) or graduation year (Gregorian Calendar) is required.')
    
    def _validate_other_services(self):
        """Validation for other services (Student Copy, Temporary Certificate, etc.)."""
        required_fields = ['student_status', 'year_type']
        
        for field in required_fields:
            if not getattr(self, field):
                raise ValidationError({field: f'{field.replace("_", " ").title()} is required for this service.'})
        
        # Validate year fields based on year_type
        if self.year_type in ['current', 'leaving', 'graduation']:
            if not self.year_ec and not self.year_gc:
                raise ValidationError('Either Ethiopian Calendar year or Gregorian Calendar year is required.')
    
    def save(self, *args, **kwargs):
        """Override save to run validation and handle audit fields."""
        # Clean names
        self.first_name = self.first_name.strip().title()
        self.last_name = self.last_name.strip().title()
        if self.middle_name:
            self.middle_name = self.middle_name.strip().title()
        
        # Run validation
        self.full_clean()
        
        super().save(*args, **kwargs)
    
    def soft_delete(self, user=None):
        """Soft delete the service request."""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        if user:
            self.deleted_by = user
        self.save()
    
    def restore(self, user=None):
        """Restore a soft-deleted service request."""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        if user:
            self.updated_by = user
        self.save()
    
    @property
    def full_name(self):
        """Return the full name of the requester."""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"
    
    @property
    def college_name(self):
        """Return the college name (either selected or other)."""
        if self.is_college_other:
            return self.college_other
        return self.college.name if self.college else None
    
    @property
    def department_name(self):
        """Return the department name (either selected or other)."""
        if self.is_department_other:
            return self.department_other
        return self.department.name if self.department else None
    
    @property
    def requires_mailing_address(self):
        """Check if this service type requires mailing address."""
        if self.service_type:
            return 'official transcript' in self.service_type.name.lower()
        return False
    
    @property
    def requires_graduation_year(self):
        """Check if this service type requires graduation year."""
        if self.service_type:
            return 'original degree' in self.service_type.name.lower()
        return False
    
    @property
    def requires_student_status(self):
        """Check if this service type requires student status."""
        if self.service_type:
            service_name = self.service_type.name.lower()
            return not ('original degree' in service_name)
        return True
    
    def __str__(self):
        return f"{self.full_name} - {self.service_type.name if self.service_type else 'No Service'} ({self.status})"


class DocumentUpload(models.Model):
    """
    Model for handling document uploads related to service requests.
    Links to the document types required by the selected ServiceType.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    service_request = models.ForeignKey(
        ServiceRequest,
        on_delete=models.CASCADE,
        related_name='document_uploads'
    )
    document_type = models.ForeignKey(
        'certificate_type.CertificateType',
        on_delete=models.PROTECT,
        related_name='document_uploads'
    )
    
    # File upload field
    file = models.FileField(
        upload_to='service_requests/documents/%Y/%m/%d/',
        help_text="Upload the required document"
    )
    
    # Additional metadata
    original_filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField(help_text="File size in bytes")
    content_type = models.CharField(max_length=100)
    
    # Status fields
    is_verified = models.BooleanField(default=False)
    verification_notes = models.TextField(blank=True, null=True)
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='uploaded_documents',
        blank=True,
        null=True
    )
    verified_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='verified_documents',
        blank=True,
        null=True
    )
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Document Upload'
        verbose_name_plural = 'Document Uploads'
        unique_together = ['service_request', 'document_type']
        indexes = [
            models.Index(fields=['service_request', 'document_type']),
            models.Index(fields=['is_verified']),
        ]
    
    def clean(self):
        """Validate that the document type is required for the service type."""
        super().clean()
        
        if self.service_request and self.document_type:
            required_document_types = self.service_request.service_type.document_types.all()
            if self.document_type not in required_document_types:
                raise ValidationError({
                    'document_type': f'Document type "{self.document_type.name}" is not required for service "{self.service_request.service_type.name}".'
                })
    
    def save(self, *args, **kwargs):
        """Override save to capture file metadata."""
        if self.file:
            self.original_filename = self.file.name
            self.file_size = self.file.size
            # Note: content_type would need to be set from the uploaded file
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.service_request.full_name} - {self.document_type.name}"
