import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogClose } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import {
  User,
  School,
  CreditCard,
  MapPin,
  Save,
  Loader2,
  FileText,
  Upload,
  X,
  CheckCircle,
  AlertCircle,
  Eye
} from 'lucide-react';
import {
  alumniApplicationsAPI,
  AlumniApplication,
  AlumniApplicationMini,
  ServiceType,
  College,
  Department
} from '@/services/alumniApplicationsAPI';
// Remove the InlineDocumentUpload import since we're not using it anymore

interface AlumniApplicationFormProps {
  application?: AlumniApplication | AlumniApplicationMini | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  formType: 'form1' | 'form2';
}

// Document Type Upload Card Component
interface DocumentTypeUploadCardProps {
  docType: {
    id: string;
    name: string;
    description?: string;
  };
  uploadedDocument?: {
    id: string;
    file: File;
    documentType: string;
    status: string;
  };
  existingDocument?: {
    id: string;
    document_type_name: string;
    file: string;
    original_filename: string;
    file_size: number;
    formatted_file_size: string;
    mime_type: string;
    upload_timestamp: string;
  };
  onFileUpload: (file: File) => void;
  onRemoveDocument: () => void;
  onViewDocument?: (documentUrl: string, filename: string, mimeType?: string) => void;
}

const DocumentTypeUploadCard: React.FC<DocumentTypeUploadCardProps> = ({
  docType,
  uploadedDocument,
  existingDocument,
  onFileUpload,
  onRemoveDocument,
  onViewDocument
}) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = React.useState(false);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      onFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('File input changed:', e.target.files);
    if (e.target.files && e.target.files[0]) {
      console.log('Selected file:', e.target.files[0]);
      onFileUpload(e.target.files[0]);
      // Reset the input so the same file can be selected again
      e.target.value = '';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div
      className={`border rounded-lg p-3 transition-colors cursor-pointer ${
        uploadedDocument
          ? 'bg-green-50 border-green-200'
          : existingDocument
          ? 'hover:bg-blue-50 hover:border-blue-300'
          : 'hover:bg-orange-50 hover:border-orange-300'
      }`}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        // Only trigger file input if clicking on the card itself, not on buttons
        if (e.target === e.currentTarget || (e.target as HTMLElement).closest('.card-content-area')) {
          console.log('Card clicked, opening file picker');
          fileInputRef.current?.click();
        }
      }}
    >
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
        onChange={handleFileSelect}
      />

      <div className="flex items-center justify-between card-content-area">
        <div className="flex items-center gap-2 flex-1 card-content-area">
          <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div className="flex-1">
            <div className="font-medium text-sm">{docType.name}</div>
            {docType.description && (
              <div className="text-xs text-muted-foreground">{docType.description}</div>
            )}

            {/* Show existing uploaded document */}
            {existingDocument && (
              <div className="text-xs text-blue-600 mt-1 flex items-center gap-2">
                <span>📄 {existingDocument.original_filename} ({existingDocument.formatted_file_size})</span>
                <span className="text-green-600">✓ Uploaded</span>
              </div>
            )}

            {/* Show pending upload document */}
            {uploadedDocument && (
              <div className="text-xs text-orange-600 mt-1">
                📄 {uploadedDocument.file.name} ({formatFileSize(uploadedDocument.file.size)}) - Pending upload
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* View existing document button */}
          {existingDocument && onViewDocument && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onViewDocument(existingDocument.file, existingDocument.original_filename, existingDocument.mime_type);
              }}
              className="h-6 px-2 border-green-200 hover:bg-green-50 hover:text-green-700"
              title="View uploaded document"
            >
              <Eye className="h-3 w-3 mr-1" />
              <span className="text-xs">View</span>
            </Button>
          )}

          {/* Show pending upload status and remove button */}
          {uploadedDocument && (
            <>
              <div className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-xs text-green-600 font-medium">Ready</span>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onRemoveDocument();
                }}
                className="text-red-600 hover:text-red-700 h-6 w-6 p-0"
                title="Remove pending upload"
              >
                <X className="h-3 w-3" />
              </Button>
            </>
          )}


        </div>
      </div>

      {dragActive && !uploadedDocument && (
        <div className="mt-2 p-2 border-2 border-dashed border-primary bg-primary/5 rounded text-center">
          <div className="text-xs text-primary font-medium">Drop file here</div>
        </div>
      )}
    </div>
  );
};

const AlumniApplicationForm: React.FC<AlumniApplicationFormProps> = ({
  application,
  isOpen,
  onClose,
  onSuccess,
  formType
}) => {
  const [formData, setFormData] = useState<any>({
    first_name: '',
    father_name: '',
    last_name: '',
    student_id: '',
    phone_number: '',
    email: '',
    admission_type: '',
    degree_type: '',
    college: '',
    department: '',
    is_other_college: false,
    other_college_name: '',
    other_department_name: '',
    student_status: '',
    current_year: '',
    year_of_leaving_ethiopian: '',
    year_of_leaving_gregorian: '',
    year_of_graduation_ethiopian: '',
    year_of_graduation_gregorian: '',
    service_type: '',
    // Form1 specific fields
    is_uog_destination: true,
    uog_college: '',
    uog_department: '',
    order_type: '',
    institution_name: '',
    institution_address: '',
    country: '',
    mailing_agent: ''
  });

  const [activeTab, setActiveTab] = useState('personal');
  const [documentsToUpload, setDocumentsToUpload] = useState<any[]>([]);

  // Debug documentsToUpload changes
  useEffect(() => {
    console.log('documentsToUpload state changed:', documentsToUpload);
  }, [documentsToUpload]);
  const [documentViewModal, setDocumentViewModal] = useState<{
    isOpen: boolean;
    documentUrl: string;
    filename: string;
    mimeType: string;
  }>({
    isOpen: false,
    documentUrl: '',
    filename: '',
    mimeType: ''
  });

  // Fetch lookup data
  const { data: serviceTypes, isLoading: serviceTypesLoading, error: serviceTypesError } = useQuery({
    queryKey: ['service-types'],
    queryFn: () => alumniApplicationsAPI.getServiceTypes()
  });

  // Debug logging
  React.useEffect(() => {
    console.log('Service Types Data:', serviceTypes);
    console.log('Service Types Loading:', serviceTypesLoading);
    console.log('Service Types Error:', serviceTypesError);
  }, [serviceTypes, serviceTypesLoading, serviceTypesError]);

  const { data: colleges, isLoading: collegesLoading, error: collegesError } = useQuery({
    queryKey: ['colleges'],
    queryFn: () => alumniApplicationsAPI.getColleges()
  });

  const { data: departments, isLoading: departmentsLoading, error: departmentsError } = useQuery({
    queryKey: ['departments', formData.college],
    queryFn: () => alumniApplicationsAPI.getDepartments(formData.college),
    enabled: !!formData.college
  });

  const { data: uogDepartments, isLoading: uogDepartmentsLoading, error: uogDepartmentsError } = useQuery({
    queryKey: ['uog-departments', formData.uog_college],
    queryFn: () => alumniApplicationsAPI.getDepartments(formData.uog_college),
    enabled: !!formData.uog_college && formType === 'form1'
  });

  // Fetch required documents for selected service type
  const { data: requiredDocuments, isLoading: requiredDocumentsLoading, error: requiredDocumentsError } = useQuery({
    queryKey: ['required-documents', formData.service_type],
    queryFn: () => alumniApplicationsAPI.getServiceTypeRequiredDocuments(formData.service_type),
    enabled: !!formData.service_type,
    retry: 1,
    onError: (error) => {
      console.error('Required Documents Error Details:', {
        error,
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status,
        serviceType: formData.service_type
      });
    }
  });

  // Debug logging for all lookup data
  React.useEffect(() => {
    console.log('Colleges Data:', colleges);
    console.log('Colleges Loading:', collegesLoading);
    console.log('Colleges Error:', collegesError);
    console.log('Departments Data:', departments);
    console.log('Departments Loading:', departmentsLoading);
    console.log('Departments Error:', departmentsError);
    console.log('Required Documents Data:', requiredDocuments);
    console.log('Required Documents Loading:', requiredDocumentsLoading);
    console.log('Required Documents Error:', requiredDocumentsError);
  }, [colleges, collegesLoading, collegesError, departments, departmentsLoading, departmentsError, requiredDocuments, requiredDocumentsLoading, requiredDocumentsError]);

  // Initialize form data
  useEffect(() => {
    if (application) {
      console.log('Initializing form with application data:', application);
      console.log('Application keys:', Object.keys(application));
      console.log('College value:', application.college);
      console.log('Department value:', application.department);
      console.log('Service type value:', application.service_type);
      console.log('UoG College value:', (application as AlumniApplication).uog_college);
      console.log('UoG Department value:', (application as AlumniApplication).uog_department);

      const initData = {
        ...application,
        // Ensure required fields have proper values (convert to strings for Select components)
        college: application.college ? String(application.college) : '',
        department: application.department ? String(application.department) : '',
        service_type: application.service_type ? String(application.service_type) : '',
        uog_college: formType === 'form1' ? ((application as AlumniApplication).uog_college ? String((application as AlumniApplication).uog_college) : '') : '',
        uog_department: formType === 'form1' ? ((application as AlumniApplication).uog_department ? String((application as AlumniApplication).uog_department) : '') : '',
        // Ensure all form fields have default values
        first_name: application.first_name || '',
        father_name: application.father_name || '',
        last_name: application.last_name || '',
        student_id: application.student_id || '',
        phone_number: application.phone_number || '',
        email: application.email || '',
        admission_type: application.admission_type || '',
        degree_type: application.degree_type || '',
        student_status: application.student_status || '',
        is_other_college: application.is_other_college || false,
        other_college_name: application.other_college_name || '',
        other_department_name: application.other_department_name || '',
        current_year: application.current_year || '',
        year_of_leaving_ethiopian: application.year_of_leaving_ethiopian || '',
        year_of_graduation_ethiopian: application.year_of_graduation_ethiopian || ''
      };

      // Convert integer years to string for display in year picker inputs
      if (application.year_of_leaving_gregorian) {
        initData.year_of_leaving_gregorian = String(application.year_of_leaving_gregorian);
      }
      if (application.year_of_graduation_gregorian) {
        initData.year_of_graduation_gregorian = String(application.year_of_graduation_gregorian);
      }

      // Add Form1-specific fields if applicable
      if (formType === 'form1') {
        const form1App = application as AlumniApplication;
        initData.is_uog_destination = form1App.is_uog_destination || false;
        initData.order_type = form1App.order_type || '';
        initData.institution_name = form1App.institution_name || '';
        initData.institution_address = form1App.institution_address || '';
        initData.country = form1App.country || '';
        initData.mailing_agent = form1App.mailing_agent || '';
      }

      console.log('Initialized form data:', initData);
      console.log('Form data college:', initData.college);
      console.log('Form data department:', initData.department);
      console.log('Form data service_type:', initData.service_type);
      console.log('Form data uog_college:', initData.uog_college);
      console.log('Form data uog_department:', initData.uog_department);
      setFormData(initData);
    }
  }, [application, formType]);

  // Debug form data changes
  useEffect(() => {
    console.log('Form data updated:', {
      college: formData.college,
      department: formData.department,
      service_type: formData.service_type,
      uog_college: formData.uog_college,
      uog_department: formData.uog_department
    });
  }, [formData.college, formData.department, formData.service_type, formData.uog_college, formData.uog_department]);

  // Create/Update mutation
  const mutation = useMutation({
    mutationFn: async (data: any) => {
      console.log('Mutation function called with data:', data);
      console.log('Application exists:', !!application);
      console.log('Form type:', formType);

      try {
        let response;
        if (application) {
          // Update
          if (formType === 'form1') {
            console.log('Updating Form1 application...');
            response = await alumniApplicationsAPI.updateApplication(application.id, data);
          } else {
            console.log('Updating Form2 application...');
            response = await alumniApplicationsAPI.updateMiniApplication(application.id, data);
          }
        } else {
          // Create
          if (formType === 'form1') {
            console.log('Creating Form1 application...');
            response = await alumniApplicationsAPI.createApplication(data);
          } else {
            console.log('Creating Form2 application...');
            response = await alumniApplicationsAPI.createMiniApplication(data);
          }
        }

        console.log('Mutation response received:', response);
        return response;
      } catch (error) {
        console.error('Mutation error:', error);
        throw error;
      }
    },
    onSuccess: async (response: any) => {
      console.log('Full response object:', response);
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);

      // Check if this is actually an error response (400, 401, etc.)
      if (response.status >= 400) {
        console.error('Response is actually an error:', response.status, response.data);
        // This should be handled by onError, but the validateStatus function is causing it to be treated as success
        throw new Error(`Server returned ${response.status}: ${JSON.stringify(response.data)}`);
      }

      console.log('Response keys:', Object.keys(response));
      if (response.data) {
        console.log('Response.data keys:', Object.keys(response.data));
      }

      // Try different ways to extract the ID
      let applicationId = response.data?.id || response.id || application?.id;

      // If still undefined, try to find ID in any nested structure
      if (!applicationId && response.data) {
        // Check if response.data is the actual application object
        if (typeof response.data === 'object' && response.data.id) {
          applicationId = response.data.id;
        }
        // Check if it's wrapped in another property
        else if (response.data.data && response.data.data.id) {
          applicationId = response.data.data.id;
        }
        // Check if it's in a results array
        else if (response.data.results && response.data.results[0] && response.data.results[0].id) {
          applicationId = response.data.results[0].id;
        }
      }

      console.log('Application created successfully with ID:', applicationId);
      console.log('Documents to upload:', documentsToUpload);

      // Upload documents if any are ready
      if (documentsToUpload.length > 0 && applicationId) {
        try {
          for (const doc of documentsToUpload) {
            if (doc.documentType && doc.file) {
              const uploadFormData = new FormData();
              uploadFormData.append('file', doc.file);
              uploadFormData.append('document_type_name', doc.documentType);

              console.log('Uploading document:', doc.documentType, 'for application:', applicationId);

              // Use the correct upload endpoint for each form type
              if (formType === 'form1') {
                await alumniApplicationsAPI.uploadDocumentToApplication(String(applicationId), uploadFormData);
              } else {
                await alumniApplicationsAPI.uploadDocumentToMiniApplication(String(applicationId), uploadFormData);
              }
            }
          }
          toast.success(
            application
              ? 'Application updated and documents uploaded successfully'
              : 'Application created and documents uploaded successfully'
          );
        } catch (error) {
          console.error('Document upload error:', error);
          console.error('Document upload error details:', error.response?.data);
          toast.warning('Application saved but some documents failed to upload. You can upload them later.');
        }
      } else {
        toast.success(application ? 'Application updated successfully' : 'Application created successfully');
      }

      onSuccess();
    },
    onError: (error: any) => {
      console.error('Application submission error:', error);
      console.error('Error response status:', error.response?.status);
      console.error('Error response data:', error.response?.data);
      console.error('Error message:', error.message);

      // Show detailed validation errors
      if (error.response?.data) {
        const errorData = error.response.data;
        console.error('Detailed error data:', JSON.stringify(errorData, null, 2));

        if (typeof errorData === 'object') {
          const errorMessages = [];
          for (const [field, messages] of Object.entries(errorData)) {
            if (Array.isArray(messages)) {
              errorMessages.push(`${field}: ${messages.join(', ')}`);
            } else {
              errorMessages.push(`${field}: ${messages}`);
            }
          }
          toast.error(`Validation errors: ${errorMessages.join('; ')}`);
        } else {
          toast.error(errorData.message || 'Failed to save application');
        }
      } else if (error.message) {
        toast.error(error.message);
      } else {
        toast.error('Failed to save application');
      }
    }
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  // Document upload handlers
  const handleDocumentUpload = (documentType: string, file: File) => {
    console.log('handleDocumentUpload called:', { documentType, file });
    console.log('File details:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    // Validate file
    if (!isValidFileType(file)) {
      console.log('File type validation failed');
      toast.error('Invalid file type. Allowed: PDF, JPG, PNG, DOC, DOCX');
      return;
    }

    if (!isValidFileSize(file)) {
      console.log('File size validation failed');
      toast.error('File size exceeds 10MB limit');
      return;
    }

    console.log('File validation passed, adding to upload queue');

    // Remove existing document of same type and add new one
    const updatedDocuments = documentsToUpload.filter(doc => doc.documentType !== documentType);
    console.log('Current documents to upload:', documentsToUpload);
    console.log('Updated documents after filter:', updatedDocuments);

    const newDocument = {
      id: Date.now().toString(),
      file,
      documentType,
      status: 'pending' as const
    };
    console.log('New document created:', newDocument);

    const newDocumentsArray = [...updatedDocuments, newDocument];
    console.log('Final documents array:', newDocumentsArray);

    setDocumentsToUpload(newDocumentsArray);
    toast.success(`${documentType} ready for upload`);
  };

  const handleRemoveDocument = (documentType: string) => {
    const updatedDocuments = documentsToUpload.filter(doc => doc.documentType !== documentType);
    setDocumentsToUpload(updatedDocuments);
    toast.info(`${documentType} removed`);
  };

  // Document view handler
  const handleViewDocument = (documentUrl: string, filename: string, mimeType?: string) => {
    console.log('handleViewDocument called:', { documentUrl, filename, mimeType });

    // Determine mime type from filename if not provided
    const fileExtension = filename.toLowerCase().split('.').pop();
    let detectedMimeType = mimeType || '';

    if (!detectedMimeType) {
      switch (fileExtension) {
        case 'pdf':
          detectedMimeType = 'application/pdf';
          break;
        case 'jpg':
        case 'jpeg':
          detectedMimeType = 'image/jpeg';
          break;
        case 'png':
          detectedMimeType = 'image/png';
          break;
        case 'doc':
          detectedMimeType = 'application/msword';
          break;
        case 'docx':
          detectedMimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        default:
          detectedMimeType = 'application/octet-stream';
      }
    }

    // Check if document can be viewed in modal
    const viewableTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (viewableTypes.includes(detectedMimeType)) {
      // Open in modal
      setDocumentViewModal({
        isOpen: true,
        documentUrl,
        filename,
        mimeType: detectedMimeType
      });
    } else {
      // Fallback to new tab for unsupported types
      window.open(documentUrl, '_blank');
      toast.info(`Opening ${filename} in new tab`);
    }
  };

  const isValidFileType = (file: File) => {
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    // Also check file extension as fallback
    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'];
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    console.log('File type validation:', {
      fileName: file.name,
      fileType: file.type,
      allowedTypes,
      hasValidType: allowedTypes.includes(file.type),
      hasValidExtension,
      isValid: allowedTypes.includes(file.type) || hasValidExtension
    });

    return allowedTypes.includes(file.type) || hasValidExtension;
  };

  const isValidFileSize = (file: File) => {
    return file.size <= 10 * 1024 * 1024; // 10MB
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Form validation
  const validateForm = () => {
    const errors: string[] = [];

    // Required field validation
    if (!formData.first_name?.trim()) errors.push('First name is required');
    if (!formData.father_name?.trim()) errors.push('Father name is required');
    if (!formData.last_name?.trim()) errors.push('Last name is required');
    if (!formData.phone_number?.trim()) errors.push('Phone number is required');
    if (!formData.email?.trim()) errors.push('Email is required');
    if (!formData.admission_type) errors.push('Admission type is required');
    if (!formData.degree_type) errors.push('Degree type is required');
    if (!formData.student_status) errors.push('Student status is required');
    if (!formData.service_type) errors.push('Service type is required');

    // College/Department validation
    if (!formData.is_other_college) {
      if (!formData.college) errors.push('College is required');
      if (!formData.department) errors.push('Department is required');
    } else {
      if (!formData.other_college_name?.trim()) errors.push('College name is required');
      if (!formData.other_department_name?.trim()) errors.push('Department name is required');
    }

    // Student status specific validation
    if (formData.student_status === 'Active' && !formData.current_year) {
      errors.push('Current year is required for active students');
    }
    if (formData.student_status === 'Inactive') {
      if (!formData.year_of_leaving_gregorian && !formData.year_of_leaving_ethiopian) {
        errors.push('Year of leaving is required for inactive students');
      }
    }
    if (formData.student_status === 'Graduated') {
      if (!formData.year_of_graduation_gregorian && !formData.year_of_graduation_ethiopian) {
        errors.push('Year of graduation is required for graduated students');
      }
    }

    // Form1 specific validation
    if (formType === 'form1') {
      if (!formData.is_uog_destination) {
        if (!formData.order_type) errors.push('Order type is required');
        if (!formData.institution_name?.trim()) errors.push('Institution name is required');
        if (!formData.country?.trim()) errors.push('Country is required');
        if (!formData.institution_address?.trim()) errors.push('Institution address is required');
        if (!formData.mailing_agent) errors.push('Mailing agent is required');
      } else {
        if (!formData.uog_college) errors.push('UoG college is required');
        if (!formData.uog_department) errors.push('UoG department is required');
      }
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      errors.push('Please enter a valid email address');
    }

    // Phone validation
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (formData.phone_number && !phoneRegex.test(formData.phone_number)) {
      errors.push('Please enter a valid phone number with country code');
    }

    return errors;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      toast.error(`Please fix the following errors:\n${validationErrors.join('\n')}`);
      return;
    }

    // Check if required documents are ready (optional validation)
    const requiredDocTypes = requiredDocuments?.data?.required_document_types || [];
    const readyDocuments = documentsToUpload.filter(doc => doc.documentType && doc.file);

    if (requiredDocTypes.length > 0 && readyDocuments.length === 0 && !application) {
      const proceed = window.confirm(
        'No documents are ready for upload. You can upload them later. Do you want to continue?'
      );
      if (!proceed) return;
    }

    // Prepare data based on form type
    const submitData = { ...formData };

    // Convert year strings to integers for Gregorian calendar fields
    if (submitData.year_of_leaving_gregorian && submitData.year_of_leaving_gregorian !== '') {
      submitData.year_of_leaving_gregorian = parseInt(submitData.year_of_leaving_gregorian, 10);
    }
    if (submitData.year_of_graduation_gregorian && submitData.year_of_graduation_gregorian !== '') {
      submitData.year_of_graduation_gregorian = parseInt(submitData.year_of_graduation_gregorian, 10);
    }

    // Clean up empty strings for optional fields and remove fields that shouldn't be sent
    Object.keys(submitData).forEach(key => {
      if (submitData[key] === '' || submitData[key] === undefined) {
        if (['student_id', 'other_college_name', 'other_department_name', 'current_year',
             'year_of_leaving_ethiopian', 'year_of_leaving_gregorian',
             'year_of_graduation_ethiopian', 'year_of_graduation_gregorian'].includes(key)) {
          delete submitData[key]; // Remove the field entirely instead of setting to null
        }
      }
    });

    // Remove conditional fields based on student status
    if (submitData.student_status !== 'Active') {
      delete submitData.current_year;
    }
    if (submitData.student_status !== 'Inactive') {
      delete submitData.year_of_leaving_ethiopian;
      delete submitData.year_of_leaving_gregorian;
    }
    if (submitData.student_status !== 'Graduated') {
      delete submitData.year_of_graduation_ethiopian;
      delete submitData.year_of_graduation_gregorian;
    }

    if (formType === 'form2') {
      // Remove Form1-specific fields for Form2
      delete submitData.is_uog_destination;
      delete submitData.uog_college;
      delete submitData.uog_department;
      delete submitData.order_type;
      delete submitData.institution_name;
      delete submitData.institution_address;
      delete submitData.country;
      delete submitData.mailing_agent;
    }

    // Debug logging
    console.log('Submitting data:', submitData);
    console.log('Form type:', formType);

    mutation.mutate(submitData);
  };

  const isForm1 = formType === 'form1';

  // Current year choices (matching backend CURRENT_YEAR_CHOICES)
  const currentYearChoices = [
    { value: '1st year', label: '1st' },
    { value: '2nd year', label: '2nd' },
    { value: '3rd year', label: '3rd' },
    { value: '4th year', label: '4th' },
    { value: '5th year', label: '5th' },
    { value: '6th year', label: '6th' },
    { value: '7th year', label: '7th' },
    { value: '8th year', label: '8th' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {application ? 'Edit' : 'Create'} Alumni Application ({isForm1 ? 'Complete' : 'Simplified'})
          </DialogTitle>
          <DialogDescription>
            {application ? 'Update the alumni application information below.' : 'Fill out the form below to create a new alumni application.'}
          </DialogDescription>
          <DialogClose />
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="personal">Personal</TabsTrigger>
              <TabsTrigger value="academic">Academic</TabsTrigger>
              <TabsTrigger value="service">Service</TabsTrigger>
              {isForm1 && <TabsTrigger value="destination">Destination</TabsTrigger>}
            </TabsList>

            {/* Personal Information Tab */}
            <TabsContent value="personal" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Personal Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="first_name">First Name *</Label>
                      <Input
                        id="first_name"
                        name="first_name"
                        autoComplete="given-name"
                        value={formData.first_name}
                        onChange={(e) => handleInputChange('first_name', e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="father_name">Father's Name *</Label>
                      <Input
                        id="father_name"
                        name="father_name"
                        autoComplete="additional-name"
                        value={formData.father_name}
                        onChange={(e) => handleInputChange('father_name', e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last_name">Last Name *</Label>
                      <Input
                        id="last_name"
                        name="last_name"
                        autoComplete="family-name"
                        value={formData.last_name}
                        onChange={(e) => handleInputChange('last_name', e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="student_id">Student ID</Label>
                      <Input
                        id="student_id"
                        name="student_id"
                        value={formData.student_id}
                        onChange={(e) => handleInputChange('student_id', e.target.value)}
                        placeholder="e.g., uog/1254/21 (optional)"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone_number">Phone Number *</Label>
                      <Input
                        id="phone_number"
                        name="phone_number"
                        type="tel"
                        autoComplete="tel"
                        value={formData.phone_number}
                        onChange={(e) => handleInputChange('phone_number', e.target.value)}
                        placeholder="+251912345678"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Academic Information Tab */}
            <TabsContent value="academic" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <School className="h-5 w-5" />
                    Academic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="admission_type">Admission Type *</Label>
                      <Select value={formData.admission_type} onValueChange={(value) => handleInputChange('admission_type', value)} name="admission_type">
                        <SelectTrigger id="admission_type">
                          <SelectValue placeholder="Select admission type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Regular">Regular</SelectItem>
                          <SelectItem value="Evening">Evening</SelectItem>
                          <SelectItem value="Summer">Summer</SelectItem>
                          <SelectItem value="Distance">Distance</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="degree_type">Degree Type *</Label>
                      <Select value={formData.degree_type} onValueChange={(value) => handleInputChange('degree_type', value)} name="degree_type">
                        <SelectTrigger id="degree_type">
                          <SelectValue placeholder="Select degree type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Diploma">Diploma</SelectItem>
                          <SelectItem value="Degree">Degree</SelectItem>
                          <SelectItem value="Master's">Master's</SelectItem>
                          <SelectItem value="PHD">PHD</SelectItem>
                          <SelectItem value="Sp.Certificate">Sp.Certificate</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="is_other_college"
                      name="is_other_college"
                      checked={formData.is_other_college}
                      onCheckedChange={(checked) => handleInputChange('is_other_college', checked)}
                    />
                    <Label htmlFor="is_other_college">College is not in the list</Label>
                  </div>

                  {formData.is_other_college ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="other_college_name">College *</Label>
                        <Input
                          id="other_college_name"
                          name="other_college_name"
                          value={formData.other_college_name}
                          onChange={(e) => handleInputChange('other_college_name', e.target.value)}
                          required={formData.is_other_college}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="other_department_name">Department Name *</Label>
                        <Input
                          id="other_department_name"
                          name="other_department_name"
                          value={formData.other_department_name}
                          onChange={(e) => handleInputChange('other_department_name', e.target.value)}
                          required={formData.is_other_college}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="college">College *</Label>
                        <Select value={formData.college} onValueChange={(value) => handleInputChange('college', value)} name="college">
                          <SelectTrigger id="college">
                            <SelectValue placeholder="Select college" />
                          </SelectTrigger>
                          <SelectContent>
                            {colleges?.data?.filter(college => college.id && String(college.id).trim() !== '').map((college: College) => (
                              <SelectItem key={college.id} value={String(college.id)}>
                                {college.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="department">Department *</Label>
                        <Select
                          value={formData.department}
                          onValueChange={(value) => handleInputChange('department', value)}
                          disabled={!formData.college}
                          name="department"
                        >
                          <SelectTrigger id="department">
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                          <SelectContent>
                            {departments?.data?.filter(dept => dept.id && String(dept.id).trim() !== '').map((dept: Department) => (
                              <SelectItem key={dept.id} value={String(dept.id)}>
                                {dept.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="student_status">Student Status *</Label>
                      <Select value={formData.student_status} onValueChange={(value) => handleInputChange('student_status', value)} name="student_status">
                        <SelectTrigger id="student_status">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Active">Active</SelectItem>
                          <SelectItem value="Inactive">Inactive</SelectItem>
                          <SelectItem value="Graduated">Graduated</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {formData.student_status === 'Active' && (
                      <div className="space-y-3">
                        <Label>Current Year *</Label>
                        <RadioGroup
                          value={formData.current_year}
                          onValueChange={(value) => handleInputChange('current_year', value)}
                          className="grid grid-cols-2 md:grid-cols-4 gap-4"
                        >
                          {currentYearChoices.map((yearOption) => (
                            <div key={yearOption.value} className="flex items-center space-x-2">
                              <RadioGroupItem value={yearOption.value} id={`year-${yearOption.value}`} />
                              <Label htmlFor={`year-${yearOption.value}`} className="text-sm font-normal cursor-pointer">
                                {yearOption.label}
                              </Label>
                            </div>
                          ))}
                        </RadioGroup>
                      </div>
                    )}
                  </div>

                  {/* Conditional fields based on student status */}
                  {formData.student_status === 'Inactive' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="year_of_leaving_ethiopian">Year of Leaving (Ethiopian Calendar)</Label>
                        <Input
                          id="year_of_leaving_ethiopian"
                          name="year_of_leaving_ethiopian"
                          type="number"
                          min="1900"
                          max={new Date().getFullYear() - 7}
                          value={formData.year_of_leaving_ethiopian}
                          onChange={(e) => handleInputChange('year_of_leaving_ethiopian', e.target.value)}
                          placeholder={`e.g., ${new Date().getFullYear() - 7}`}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="year_of_leaving_gregorian">Year of Leaving (Gregorian Calendar)</Label>
                        <Input
                          id="year_of_leaving_gregorian"
                          name="year_of_leaving_gregorian"
                          type="number"
                          min="1900"
                          max={new Date().getFullYear()}
                          value={formData.year_of_leaving_gregorian}
                          onChange={(e) => handleInputChange('year_of_leaving_gregorian', e.target.value)}
                          placeholder={`e.g., ${new Date().getFullYear()}`}
                        />
                      </div>
                    </div>
                  )}

                  {formData.student_status === 'Graduated' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="year_of_graduation_ethiopian">Year of Graduation (Ethiopian Calendar)</Label>
                        <Input
                          id="year_of_graduation_ethiopian"
                          name="year_of_graduation_ethiopian"
                          type="number"
                          min="1900"
                          max={new Date().getFullYear() - 7}
                          value={formData.year_of_graduation_ethiopian}
                          onChange={(e) => handleInputChange('year_of_graduation_ethiopian', e.target.value)}
                          placeholder={`e.g., ${new Date().getFullYear() - 7}`}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="year_of_graduation_gregorian">Year of Graduation (Gregorian Calendar)</Label>
                        <Input
                          id="year_of_graduation_gregorian"
                          name="year_of_graduation_gregorian"
                          type="number"
                          min="1900"
                          max={new Date().getFullYear()}
                          value={formData.year_of_graduation_gregorian}
                          onChange={(e) => handleInputChange('year_of_graduation_gregorian', e.target.value)}
                          placeholder={`e.g., ${new Date().getFullYear()}`}
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Service Information Tab */}
            <TabsContent value="service" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Service Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="service_type">Service Type *</Label>
                    <Select value={formData.service_type} onValueChange={(value) => handleInputChange('service_type', value)} name="service_type">
                      <SelectTrigger id="service_type">
                        <SelectValue placeholder="Select service type" />
                      </SelectTrigger>
                      <SelectContent>
                        {serviceTypes?.data?.filter(service => service.id && String(service.id).trim() !== '').map((service: ServiceType) => (
                          <SelectItem key={service.id} value={String(service.id)}>
                            {service.name} - {service.fee} ETB
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Required Document Types with Upload */}
                  {formData.service_type && (
                    <div className="space-y-2">
                      <Label>Required Document Types</Label>
                      {requiredDocumentsLoading ? (
                        <div className="text-sm text-muted-foreground">Loading required documents...</div>
                      ) : requiredDocumentsError ? (
                        <div className="text-sm text-destructive">
                          Error loading required documents: {requiredDocumentsError?.message || 'Unknown error'}
                          <br />
                          <span className="text-xs">Service Type: {formData.service_type}</span>
                        </div>
                      ) : requiredDocuments?.data?.required_document_types?.length > 0 ? (
                        <div className="grid grid-cols-1 gap-3">
                          {requiredDocuments.data.required_document_types.map((docType: any, index: number) => {
                            const uploadedDoc = documentsToUpload.find(doc => doc.documentType === docType.name);
                            const existingDoc = application?.documents?.find(doc => doc.document_type_name === docType.name);
                            return (
                              <DocumentTypeUploadCard
                                key={index}
                                docType={docType}
                                uploadedDocument={uploadedDoc}
                                existingDocument={existingDoc}
                                onFileUpload={(file) => handleDocumentUpload(docType.name, file)}
                                onRemoveDocument={() => handleRemoveDocument(docType.name)}
                                onViewDocument={handleViewDocument}
                              />
                            );
                          })}
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">No document requirements for this service</div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Destination Information Tab (Form1 only) */}
            {isForm1 && (
              <TabsContent value="destination" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Destination Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="uog_destination"
                          name="destination"
                          checked={formData.is_uog_destination}
                          onChange={() => handleInputChange('is_uog_destination', true)}
                        />
                        <Label htmlFor="uog_destination">UoG Internal</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="external_destination"
                          name="destination"
                          checked={!formData.is_uog_destination}
                          onChange={() => handleInputChange('is_uog_destination', false)}
                        />
                        <Label htmlFor="external_destination">External Institution</Label>
                      </div>
                    </div>

                    {formData.is_uog_destination ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="uog_college">UoG College *</Label>
                          <Select value={formData.uog_college} onValueChange={(value) => handleInputChange('uog_college', value)} name="uog_college">
                            <SelectTrigger id="uog_college">
                              <SelectValue placeholder="Select UoG college" />
                            </SelectTrigger>
                            <SelectContent>
                              {colleges?.data?.filter(college => college.id && String(college.id).trim() !== '').map((college: College) => (
                                <SelectItem key={college.id} value={String(college.id)}>
                                  {college.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="uog_department">UoG Department *</Label>
                          <Select
                            value={formData.uog_department}
                            onValueChange={(value) => handleInputChange('uog_department', value)}
                            disabled={!formData.uog_college}
                            name="uog_department"
                          >
                            <SelectTrigger id="uog_department">
                              <SelectValue placeholder="Select UoG department" />
                            </SelectTrigger>
                            <SelectContent>
                              {uogDepartments?.data?.filter(dept => dept.id && String(dept.id).trim() !== '').map((dept: Department) => (
                                <SelectItem key={dept.id} value={String(dept.id)}>
                                  {dept.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="order_type">Order Type *</Label>
                            <Select value={formData.order_type} onValueChange={(value) => handleInputChange('order_type', value)} name="order_type">
                              <SelectTrigger id="order_type">
                                <SelectValue placeholder="Select order type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Local">Local</SelectItem>
                                <SelectItem value="International">International</SelectItem>
                                <SelectItem value="Legal Delegate">Legal Delegate</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="institution_name">Institution Name *</Label>
                            <Input
                              id="institution_name"
                              name="institution_name"
                              value={formData.institution_name}
                              onChange={(e) => handleInputChange('institution_name', e.target.value)}
                              required={!formData.is_uog_destination}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="country">Country *</Label>
                            <Input
                              id="country"
                              name="country"
                              value={formData.country}
                              onChange={(e) => handleInputChange('country', e.target.value)}
                              placeholder="e.g., United States"
                              required={!formData.is_uog_destination}
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="institution_address">Institution Address *</Label>
                            <Textarea
                              id="institution_address"
                              name="institution_address"
                              value={formData.institution_address}
                              onChange={(e) => handleInputChange('institution_address', e.target.value)}
                              rows={3}
                              required={!formData.is_uog_destination}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="mailing_agent">Mailing Agent *</Label>
                            <Select value={formData.mailing_agent} onValueChange={(value) => handleInputChange('mailing_agent', value)} name="mailing_agent">
                              <SelectTrigger id="mailing_agent">
                                <SelectValue placeholder="Select mailing agent" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Normal Postal">Normal Postal</SelectItem>
                                <SelectItem value="DHL">DHL</SelectItem>
                                <SelectItem value="SMS">SMS</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            {/* Document Status */}
            {formData.service_type && requiredDocuments?.data?.required_document_types?.length > 0 && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <FileText className="h-4 w-4" />
                <span>
                  Documents: {documentsToUpload.filter(doc => doc.documentType && doc.file).length} of {requiredDocuments.data.required_document_types.length} ready
                </span>
              </div>
            )}

            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {documentsToUpload.length > 0 ? 'Saving & Uploading...' : 'Saving...'}
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {application ? 'Update' : 'Create'} Application
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>

      {/* Document Viewer Modal */}
      <Dialog open={documentViewModal.isOpen} onOpenChange={(open) => setDocumentViewModal(prev => ({ ...prev, isOpen: open }))}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {documentViewModal.filename}
            </DialogTitle>
            <DialogDescription>
              Document viewer - {documentViewModal.mimeType}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            {documentViewModal.mimeType === 'application/pdf' ? (
              <iframe
                src={documentViewModal.documentUrl}
                className="w-full h-[70vh] border rounded"
                title={documentViewModal.filename}
              />
            ) : documentViewModal.mimeType.startsWith('image/') ? (
              <div className="flex justify-center items-center h-[70vh] bg-gray-50 rounded">
                <img
                  src={documentViewModal.documentUrl}
                  alt={documentViewModal.filename}
                  className="max-w-full max-h-full object-contain"
                />
              </div>
            ) : (documentViewModal.mimeType === 'application/msword' ||
                   documentViewModal.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') ? (
              <div className="flex flex-col items-center justify-center h-[70vh] bg-gray-50 rounded">
                <FileText className="h-16 w-16 text-blue-500 mb-4" />
                <p className="text-lg font-medium mb-2">{documentViewModal.filename}</p>
                <p className="text-sm text-muted-foreground mb-4">Word documents cannot be previewed directly</p>
                <Button
                  onClick={() => window.open(documentViewModal.documentUrl, '_blank')}
                  className="flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Open in New Tab
                </Button>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-[70vh] bg-gray-50 rounded">
                <FileText className="h-16 w-16 text-gray-400 mb-4" />
                <p className="text-lg font-medium mb-2">{documentViewModal.filename}</p>
                <p className="text-sm text-muted-foreground mb-4">Preview not available for this file type</p>
                <Button
                  onClick={() => window.open(documentViewModal.documentUrl, '_blank')}
                  className="flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Open in New Tab
                </Button>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => window.open(documentViewModal.documentUrl, '_blank')}
            >
              Open in New Tab
            </Button>
            <Button
              variant="outline"
              onClick={() => setDocumentViewModal(prev => ({ ...prev, isOpen: false }))}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
};

export default AlumniApplicationForm;
