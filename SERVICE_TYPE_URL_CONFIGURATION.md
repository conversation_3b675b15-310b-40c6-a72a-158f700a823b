# Service Type URL Configuration Guide

## 📋 **Overview**

Each Service Type can have its own URL that determines which alumni application form to use. This allows for flexible routing and form selection based on the specific service requirements.

## 🎯 **URL Format Options**

### **1. Complete Application (Form1)**
Use this for services requiring comprehensive information (e.g., official transcripts):
```
http://localhost:8080/alumni-application?form=form1
```

### **2. Simplified Application (Form2)**
Use this for basic services (e.g., certificates, student copies):
```
http://localhost:8080/alumni-application?form=form2
```

### **3. External URLs**
You can also use external URLs that will open in a new tab:
```
https://external-service.example.com/application
```

### **4. Custom Internal URLs**
For custom routing within the application:
```
http://localhost:8080/custom-page?service=transcript
```

## ⚙️ **Configuration Methods**

### **Method 1: Admin Interface**
1. Navigate to `/graduate-admin?tab=service-types`
2. Click "Edit" on any service type
3. Set the "Service URL" field with the appropriate URL
4. Save the changes

### **Method 2: Django Admin**
1. Access Django admin at `/admin/`
2. Go to "Service Types" under "Service Type"
3. Edit any service type
4. Set the "URL" field
5. Save

### **Method 3: Database Script**
Use the provided script to bulk update URLs:
```bash
cd backend
python update_service_type_urls.py
```

## 📊 **Current Configuration**

Based on the latest update, here's the current URL configuration:

### **Form1 (Complete Application) - 3 Services**
- ✅ **Official Transcript** → `?form=form1`
- ✅ **offical transcrpit** → `?form=form1`
- ✅ **offical transcrpit 2** → `?form=form1`

### **Form2 (Simplified Application) - 4 Services**
- ✅ **Original Degree Certificate** → `?form=form2`
- ✅ **Student Copy Transcript** → `?form=form2`
- ✅ **Temporary Certificate** → `?form=form2`
- ✅ **hermon** → `?form=form2`

## 🔄 **How URL Routing Works**

When a user clicks "Apply for Service" on the services page, the system:

1. **Checks the service's URL field**
2. **Analyzes the URL content**:
   - If contains `form=form1` or `form=complete` → Navigate to Form1
   - If contains `form=form2` or `form=simplified` → Navigate to Form2
   - If internal URL → Navigate directly
   - If external URL → Open in new tab
   - If no URL → Default to Form2

## 🛠️ **Adding New Service Types**

When creating new service types, set the URL based on the service complexity:

### **Use Form1 (Complete) for:**
- Official transcripts
- Degree verification
- International document requests
- Services requiring destination information

### **Use Form2 (Simplified) for:**
- Student copy transcripts
- Temporary certificates
- Basic verification letters
- Internal document requests

## 🧪 **Testing URL Configuration**

### **Test Form1 Services:**
1. Go to `/services`
2. Click "Apply for Service" on "Official Transcript"
3. Should redirect to `/alumni-application?form=form1`
4. Should show 4 tabs: Personal, Academic, Service, Destination

### **Test Form2 Services:**
1. Go to `/services`
2. Click "Apply for Service" on "Original Degree Certificate"
3. Should redirect to `/alumni-application?form=form2`
4. Should show 3 tabs: Personal, Academic, Service

## 🔧 **Troubleshooting**

### **Service redirects to wrong form:**
- Check the service's URL field in admin
- Ensure URL contains correct form parameter
- Update URL if necessary

### **Service opens external page instead of form:**
- Verify URL is internal (starts with current domain)
- Check for typos in URL
- Ensure URL format is correct

### **Service defaults to Form2 unexpectedly:**
- Check if URL field is empty
- Verify URL contains recognizable form parameter
- Update URL to specify form type

## 📝 **Best Practices**

1. **Consistent URLs**: Use the standard format for internal forms
2. **Clear Naming**: Use descriptive service names
3. **Regular Testing**: Test URL routing after changes
4. **Documentation**: Keep this guide updated with new services
5. **Backup**: Always backup before bulk URL updates

## 🚀 **Future Enhancements**

Potential improvements for URL configuration:

1. **Form Type Field**: Add explicit form_type field to ServiceType model
2. **URL Validation**: Enhanced validation for URL formats
3. **Bulk Edit**: Interface for bulk URL updates
4. **URL Templates**: Predefined URL templates for common scenarios
5. **Analytics**: Track which URLs are most used

## 📞 **Support**

For questions about service type URL configuration:
- Check this documentation first
- Test changes in development environment
- Contact system administrator for database-level changes
