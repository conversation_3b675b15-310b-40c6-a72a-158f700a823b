from rest_framework import serializers
from .models import Year

class YearSerializer(serializers.ModelSerializer):
    class Meta:
        model = Year
        fields = ['uuid', 'year', 'created_at', 'updated_at']
        ref_name = 'Year'
        read_only_fields = ['uuid', 'created_at', 'updated_at']

    def validate_year(self, value):
        """
        Validate that the year is a valid format and not empty
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Year cannot be empty.")
        
        # Check if year already exists (for create) or exists for other instances (for update)
        instance = self.instance
        if instance:
            # Update case - check if year exists for other instances
            if Year.objects.filter(year=value).exclude(uuid=instance.uuid).exists():
                raise serializers.ValidationError("A year with this value already exists.")
        else:
            # Create case - check if year exists
            if Year.objects.filter(year=value).exists():
                raise serializers.ValidationError("A year with this value already exists.")
        
        return value.strip()
