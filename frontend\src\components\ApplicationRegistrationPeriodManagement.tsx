import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Pencil, Trash2, Search, RefreshCw, Calendar, Clock, CheckCircle, XCircle, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, FileText } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import axios from 'axios';

interface Program {
  id: number;
  program_code: string;
  program_name: string;
  registration_fee: number | null;
}

interface Year {
  uuid: string;
  year: string;
  created_at: string;
  updated_at: string;
}

interface Term {
  id: string;
  name: string;
  description?: string;
  updated_at: string;
}

interface RegistrationPeriod {
  id: number;
  program: number;
  program_name?: string;
  year: string;
  year_name?: string;
  term: string;
  term_name?: string;
  open_date: string;
  close_date: string;
  is_active: boolean;
  created_at: string;
}

const ApplicationRegistrationPeriodManagement = () => {
  const [registrationPeriods, setRegistrationPeriods] = useState<RegistrationPeriod[]>([]);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [years, setYears] = useState<Year[]>([]);
  const [terms, setTerms] = useState<Term[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentRegistrationPeriod, setCurrentRegistrationPeriod] = useState<RegistrationPeriod | null>(null);
  const [formData, setFormData] = useState({
    program: '',
    year: '',
    term: '',
    open_date: '',
    close_date: '',
    is_active: false,
  });
  const [formErrors, setFormErrors] = useState({
    program: '',
    year: '',
    term: '',
    open_date: '',
    close_date: '',
    general: '',
  });

  // Fetch registration periods, programs, years, and terms on component mount
  useEffect(() => {
    fetchPrograms();
    fetchYears();
    fetchTerms();
    fetchRegistrationPeriods();
  }, []);

  const fetchPrograms = async () => {
    try {
      // Try to fetch from the application program endpoint
      const response = await axios.get('http://localhost:8000/api/programs/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.data) {
        console.log('Programs data:', response.data);
        setPrograms(response.data);
      } else {
        console.warn('No program data returned from API');
        setPrograms([]);
      }
    } catch (error) {
      console.error('Error fetching programs:', error);

      // Try the public endpoint as fallback
      try {
        const publicResponse = await axios.get('http://localhost:8000/api/programs/public/');
        if (publicResponse.data) {
          console.log('Programs data from public endpoint:', publicResponse.data);
          setPrograms(publicResponse.data);
        } else {
          console.warn('No program data returned from public API');
          setPrograms([]);
        }
      } catch (publicError) {
        console.error('Error fetching programs from public endpoint:', publicError);
        toast.error('Failed to load programs');
        setPrograms([]);
      }
    }
  };

  const fetchYears = async () => {
    try {
      // Try to fetch from the years endpoint
      const response = await axios.get('http://localhost:8000/api/years/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.data) {
        console.log('Years data:', response.data);
        setYears(response.data);
      } else {
        console.warn('No year data returned from API');
        setYears([]);
      }
    } catch (error) {
      console.error('Error fetching years:', error);

      // Try the public endpoint as fallback
      try {
        const publicResponse = await axios.get('http://localhost:8000/api/years/public/');
        if (publicResponse.data) {
          console.log('Years data from public endpoint:', publicResponse.data);
          setYears(publicResponse.data);
        } else {
          console.warn('No year data returned from public API');
          setYears([]);
        }
      } catch (publicError) {
        console.error('Error fetching years from public endpoint:', publicError);
        toast.error('Failed to load years');
        setYears([]);
      }
    }
  };

  const fetchTerms = async () => {
    try {
      // Try to fetch from the terms endpoint
      const response = await axios.get('http://localhost:8000/api/terms/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.data) {
        console.log('Terms data:', response.data);
        setTerms(response.data);
      } else {
        console.warn('No term data returned from API');
        setTerms([]);
      }
    } catch (error) {
      console.error('Error fetching terms:', error);
      toast.error('Failed to load terms');
      setTerms([]);
    }
  };

  const fetchRegistrationPeriods = async () => {
    setLoading(true);
    try {
      // Try to fetch from the registration period endpoint
      const response = await axios.get('http://localhost:8000/api/registration-periods/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.data) {
        console.log('Registration Periods data:', response.data);
        setRegistrationPeriods(response.data);
      } else {
        console.warn('No registration period data returned from API');
        setRegistrationPeriods([]);
      }
    } catch (error) {
      console.error('Error fetching registration periods:', error);

      // Try the public endpoint as fallback
      try {
        const publicResponse = await axios.get('http://localhost:8000/api/registration-periods/public/');
        if (publicResponse.data) {
          console.log('Registration Periods data from public endpoint:', publicResponse.data);
          setRegistrationPeriods(publicResponse.data);
        } else {
          console.warn('No registration period data returned from public API');
          setRegistrationPeriods([]);
        }
      } catch (publicError) {
        console.error('Error fetching registration periods from public endpoint:', publicError);
        toast.error('Failed to load registration periods');
        setRegistrationPeriods([]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear errors when user types
    setFormErrors({
      ...formErrors,
      [name]: '',
      general: '', // Clear general error when any field changes
    });

    // Real-time validation for date fields - Based on model validation
    if (name === 'open_date' && value) {
      const openDate = new Date(value);

      if (isNaN(openDate.getTime())) {
        setFormErrors(prev => ({
          ...prev,
          open_date: 'Invalid date format'
        }));
      } else {
        // Model validation: Opening date must be in the future (greater than current date and time)
        const now = new Date();

        if (openDate <= now) {
          setFormErrors(prev => ({
            ...prev,
            open_date: 'Opening date must be in the future.'
          }));
        }
      }

      // Check relationship with close_date
      if (formData.close_date) {
        const closeDate = new Date(formData.close_date);
        if (openDate >= closeDate) {
          setFormErrors(prev => ({
            ...prev,
            close_date: 'The closing date must be after the opening date.'
          }));
        } else if (openDate.getTime() === closeDate.getTime()) {
          setFormErrors(prev => ({
            ...prev,
            close_date: 'The opening date cannot be the same as the closing date.'
          }));
        } else {
          setFormErrors(prev => ({
            ...prev,
            close_date: ''
          }));
        }
      }
    }

    if (name === 'close_date' && value) {
      const closeDate = new Date(value);

      if (isNaN(closeDate.getTime())) {
        setFormErrors(prev => ({
          ...prev,
          close_date: 'Invalid date format'
        }));
      } else {
        // Model validation: Maximum 1 year in the future
        const now = new Date();
        const maxCloseDate = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000));
        if (closeDate > maxCloseDate) {
          setFormErrors(prev => ({
            ...prev,
            close_date: 'The closing date cannot be more than one year in the future.'
          }));
        } else if (formData.open_date) {
          const openDate = new Date(formData.open_date);

          // Model validation: The closing date must be after the opening date
          if (closeDate <= openDate) {
            setFormErrors(prev => ({
              ...prev,
              close_date: 'The closing date must be after the opening date.'
            }));
          } else if (openDate.getTime() === closeDate.getTime()) {
            setFormErrors(prev => ({
              ...prev,
              close_date: 'The opening date cannot be the same as the closing date.'
            }));
          } else {
            setFormErrors(prev => ({
              ...prev,
              close_date: ''
            }));
          }
        }
      }
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear errors when user selects
    setFormErrors({
      ...formErrors,
      [name]: '',
      general: '', // Clear general error when any field changes
    });

    // Real-time validation for program/year/term combination - Model unique_together constraint
    if ((name === 'program' || name === 'year' || name === 'term') && value) {
      const checkProgram = name === 'program' ? value : formData.program;
      const checkYear = name === 'year' ? value : formData.year;
      const checkTerm = name === 'term' ? value : formData.term;

      if (checkProgram && checkYear && checkTerm) {
        const duplicate = registrationPeriods.find(period =>
          period.program.toString() === checkProgram &&
          period.year === checkYear &&
          period.term === checkTerm &&
          (!currentRegistrationPeriod || period.id !== currentRegistrationPeriod.id)
        );

        if (duplicate) {
          setFormErrors(prev => ({
            ...prev,
            general: 'Registration period with this Program, Year, and Term already exists.'
          }));
        }
      }
    }

    // Real-time validation for active program constraint
    if (name === 'program' && value && formData.is_active) {
      const activeForProgram = registrationPeriods.find(period => {
        if (period.program.toString() !== value || !period.is_active) return false;
        if (currentRegistrationPeriod && period.id === currentRegistrationPeriod.id) return false;
        return true;
      });

      if (activeForProgram) {
        setFormErrors(prev => ({
          ...prev,
          general: 'There is already an active registration period for this program.'
        }));
      }
    }
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData({
      ...formData,
      is_active: checked,
    });

    // Clear general error when status changes
    setFormErrors(prev => ({
      ...prev,
      general: ''
    }));

    // Model validation: If switching to active, validate constraints
    if (checked && formData.open_date) {
      const openDate = new Date(formData.open_date);
      const now = new Date();

      // Model validation: When active, opening date must be in the future
      if (openDate <= now) {
        setFormErrors(prev => ({
          ...prev,
          open_date: 'When active, the opening date must be in the future.'
        }));
        toast.warning('When active, the opening date must be in the future.');
      } else {
        // Clear the error if it was previously set
        setFormErrors(prev => ({
          ...prev,
          open_date: ''
        }));
      }

      // Model validation: Only one active period per program
      if (formData.program) {
        const activeForProgram = registrationPeriods.find(period => {
          if (period.program.toString() !== formData.program || !period.is_active) return false;
          if (currentRegistrationPeriod && period.id === currentRegistrationPeriod.id) return false;
          return true;
        });

        if (activeForProgram) {
          setFormErrors(prev => ({
            ...prev,
            general: 'There is already an active registration period for this program.'
          }));
          toast.warning('There is already an active registration period for this program.');
        }
      }
    } else if (!checked) {
      // Clear validation errors when switching to inactive
      setFormErrors(prev => ({
        ...prev,
        open_date: prev.open_date.includes('When active') ? '' : prev.open_date,
        general: prev.general.includes('already an active') ? '' : prev.general
      }));
    }
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = {
      program: '',
      year: '',
      term: '',
      open_date: '',
      close_date: '',
      general: '',
    };

    // Clear general error first
    newErrors.general = '';

    // Program validation - Required field (ForeignKey constraint)
    if (!formData.program) {
      newErrors.program = 'Program selection is required';
      valid = false;
    } else if (!programs.find(p => p.id.toString() === formData.program)) {
      newErrors.program = 'Please select a valid program';
      valid = false;
    }

    // Year validation - Required field (ForeignKey constraint)
    if (!formData.year) {
      newErrors.year = 'Academic year selection is required';
      valid = false;
    } else if (!years.find(y => y.uuid === formData.year)) {
      newErrors.year = 'Please select a valid academic year';
      valid = false;
    }

    // Term validation - Required field (ForeignKey constraint)
    if (!formData.term) {
      newErrors.term = 'Term selection is required';
      valid = false;
    } else if (!terms.find(t => t.id === formData.term)) {
      newErrors.term = 'Please select a valid term';
      valid = false;
    }

    // Opening date validation - Based on model clean() method
    if (!formData.open_date) {
      newErrors.open_date = 'Both open_date and close_date must be set.';
      valid = false;
    } else {
      const openDate = new Date(formData.open_date);

      // Check if date is valid
      if (isNaN(openDate.getTime())) {
        newErrors.open_date = 'Invalid date format';
        valid = false;
      } else {
        // Model validation: Opening date must be in the future (greater than current date and time)
        const now = new Date();

        if (openDate <= now) {
          newErrors.open_date = 'Opening date must be in the future.';
          valid = false;
        }
      }
    }

    // Closing date validation - Based on model clean() method
    if (!formData.close_date) {
      newErrors.close_date = 'Both open_date and close_date must be set.';
      valid = false;
    } else {
      const closeDate = new Date(formData.close_date);

      // Check if date is valid
      if (isNaN(closeDate.getTime())) {
        newErrors.close_date = 'Invalid date format';
        valid = false;
      } else if (formData.open_date) {
        const openDate = new Date(formData.open_date);

        // Model validation: The closing date must be after the opening date
        if (openDate >= closeDate) {
          newErrors.close_date = 'The closing date must be after the opening date.';
          valid = false;
        }

        // Serializer validation: Opening date cannot be the same as closing date
        if (openDate.getTime() === closeDate.getTime()) {
          newErrors.close_date = 'The opening date cannot be the same as the closing date.';
          valid = false;
        }

        // Model validation: Maximum 1 year in the future
        const now = new Date();
        const maxCloseDate = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000)); // 1 year from now
        if (closeDate > maxCloseDate) {
          newErrors.close_date = 'The closing date cannot be more than one year in the future.';
          valid = false;
        }
      }
    }

    // Model validation: unique_together constraint [program, year, term]
    if (formData.program && formData.year && formData.term && valid) {
      const duplicate = registrationPeriods.find(period =>
        period.program.toString() === formData.program &&
        period.year === formData.year &&
        period.term === formData.term &&
        (!currentRegistrationPeriod || period.id !== currentRegistrationPeriod.id)
      );

      if (duplicate) {
        newErrors.general = 'Registration period with this Program, Year, and Term already exists.';
        valid = false;
      }
    }

    // Model validation: Only one active period per program
    if (formData.program && formData.is_active && valid) {
      const activeForProgram = registrationPeriods.find(period => {
        if (period.program.toString() !== formData.program || !period.is_active) return false;
        if (currentRegistrationPeriod && period.id === currentRegistrationPeriod.id) return false;
        return true;
      });

      if (activeForProgram) {
        newErrors.general = 'There is already an active registration period for this program.';
        valid = false;
      }
    }

    // Model validation: No overlapping periods for same program, year, and term
    if (formData.program && formData.year && formData.term && formData.open_date && formData.close_date && valid) {
      const openDate = new Date(formData.open_date);
      const closeDate = new Date(formData.close_date);

      const overlapping = registrationPeriods.find(period => {
        if (period.program.toString() !== formData.program || period.year !== formData.year || period.term !== formData.term) return false;
        if (currentRegistrationPeriod && period.id === currentRegistrationPeriod.id) return false;

        const existingOpen = new Date(period.open_date);
        const existingClose = new Date(period.close_date);

        // Check for overlap: open_date <= existing_close_date AND close_date >= existing_open_date
        return (openDate <= existingClose && closeDate >= existingOpen);
      });

      if (overlapping) {
        newErrors.general = 'This registration period overlaps with an existing one for the same program, year, and term.';
        valid = false;
      }
    }

    setFormErrors(newErrors);
    return valid;
  };

  const handleAddRegistrationPeriod = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add a registration period');
        return;
      }

      // Prepare the data for submission
      const registrationPeriodData = {
        program: parseInt(formData.program),
        year: formData.year,
        term: formData.term,
        open_date: formData.open_date,
        close_date: formData.close_date,
        is_active: formData.is_active,
      };

      // Show loading toast
      const loadingToast = toast.loading('Adding registration period...');

      // Make the request with the current token
      const response = await axios.post('http://localhost:8000/api/registration-periods/', registrationPeriodData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      toast.success('Registration Period added successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchRegistrationPeriods(); // Refresh the list after adding
    } catch (error) {
      console.error('Error adding registration period:', error);

      // Handle validation errors from the server
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        const newErrors = { ...formErrors };
        let hasFieldErrors = false;

        // Check for field-specific errors
        if (errorData.program) {
          newErrors.program = Array.isArray(errorData.program)
            ? errorData.program[0]
            : errorData.program;
          hasFieldErrors = true;
        }

        if (errorData.open_date) {
          newErrors.open_date = Array.isArray(errorData.open_date)
            ? errorData.open_date[0]
            : errorData.open_date;
          hasFieldErrors = true;
        }

        if (errorData.close_date) {
          newErrors.close_date = Array.isArray(errorData.close_date)
            ? errorData.close_date[0]
            : errorData.close_date;
          hasFieldErrors = true;
        }

        if (hasFieldErrors) {
          setFormErrors(newErrors);
          toast.error('Please fix the errors in the form');
        } else {
          toast.error(`Failed to add registration period: ${error.response.data.detail || 'Unknown error'}`);
        }
      } else {
        toast.error(`Failed to add registration period: ${error.message}`);
      }
    }
  };

  const handleEditRegistrationPeriod = async () => {
    if (!currentRegistrationPeriod) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit a registration period');
        return;
      }

      // Prepare the data for submission
      const registrationPeriodData = {
        program: parseInt(formData.program),
        year: formData.year,
        term: formData.term,
        open_date: formData.open_date,
        close_date: formData.close_date,
        is_active: formData.is_active,
      };

      // Show loading toast
      const loadingToast = toast.loading('Updating registration period...');

      // Make the request with the current token
      const response = await axios.put(`http://localhost:8000/api/registration-periods/${currentRegistrationPeriod.id}/`, registrationPeriodData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      toast.success('Registration Period updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchRegistrationPeriods(); // Refresh the list after editing
    } catch (error) {
      console.error('Error updating registration period:', error);

      // Handle validation errors from the server
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        const newErrors = { ...formErrors };
        let hasFieldErrors = false;

        // Check for field-specific errors
        if (errorData.program) {
          newErrors.program = Array.isArray(errorData.program)
            ? errorData.program[0]
            : errorData.program;
          hasFieldErrors = true;
        }

        if (errorData.open_date) {
          newErrors.open_date = Array.isArray(errorData.open_date)
            ? errorData.open_date[0]
            : errorData.open_date;
          hasFieldErrors = true;
        }

        if (errorData.close_date) {
          newErrors.close_date = Array.isArray(errorData.close_date)
            ? errorData.close_date[0]
            : errorData.close_date;
          hasFieldErrors = true;
        }

        if (hasFieldErrors) {
          setFormErrors(newErrors);
          toast.error('Please fix the errors in the form');
        } else {
          toast.error(`Failed to update registration period: ${error.response.data.detail || 'Unknown error'}`);
        }
      } else {
        toast.error(`Failed to update registration period: ${error.message}`);
      }
    }
  };

  const handleDeleteRegistrationPeriod = async () => {
    if (!currentRegistrationPeriod) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete a registration period');
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading('Deleting registration period...');

      // Make the request with the current token
      const response = await axios.delete(`http://localhost:8000/api/registration-periods/delete/${currentRegistrationPeriod.id}/`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      toast.success('Registration Period deleted successfully');
      setIsDeleteDialogOpen(false);
      fetchRegistrationPeriods(); // Refresh the list after deleting
    } catch (error) {
      console.error('Error deleting registration period:', error);

      // Handle validation errors from the server
      if (error.response && error.response.data) {
        if (error.response.data.detail) {
          toast.error(`Failed to delete registration period: ${error.response.data.detail}`);
        } else {
          toast.error(`Failed to delete registration period: ${JSON.stringify(error.response.data)}`);
        }
      } else {
        toast.error(`Failed to delete registration period: ${error.message}`);
      }
    }
  };

  const openEditDialog = (registrationPeriod: RegistrationPeriod) => {
    setCurrentRegistrationPeriod(registrationPeriod);

    // Format dates for datetime-local input
    const openDate = new Date(registrationPeriod.open_date);
    const closeDate = new Date(registrationPeriod.close_date);

    // Format to YYYY-MM-DDThh:mm
    const formatDateForInput = (date: Date) => {
      return date.toISOString().slice(0, 16);
    };

    setFormData({
      program: registrationPeriod.program.toString(),
      year: registrationPeriod.year,
      term: registrationPeriod.term,
      open_date: formatDateForInput(openDate),
      close_date: formatDateForInput(closeDate),
      is_active: registrationPeriod.is_active,
    });

    setFormErrors({
      program: '',
      year: '',
      term: '',
      open_date: '',
      close_date: '',
      general: '',
    });

    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (registrationPeriod: RegistrationPeriod) => {
    setCurrentRegistrationPeriod(registrationPeriod);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      program: '',
      year: '',
      term: '',
      open_date: '',
      close_date: '',
      is_active: false,
    });
    setFormErrors({
      program: '',
      year: '',
      term: '',
      open_date: '',
      close_date: '',
      general: '',
    });
    setCurrentRegistrationPeriod(null);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get program name by ID
  const getProgramName = (programId: number): string => {
    const program = programs.find(p => p.id === programId);
    return program ? `${program.program_code} - ${program.program_name}` : 'Unknown Program';
  };

  // Get year name by UUID
  const getYearName = (yearUuid: string): string => {
    const year = years.find(y => y.uuid === yearUuid);
    return year ? year.year : 'Unknown Year';
  };

  // Get term name by ID
  const getTermName = (termId: string): string => {
    const term = terms.find(t => t.id === termId);
    return term ? term.name : 'Unknown Term';
  };

  // Check if registration period is currently active (between open and close dates)
  const isCurrentlyActive = (openDate: string, closeDate: string): boolean => {
    const now = new Date();
    const open = new Date(openDate);
    const close = new Date(closeDate);
    return now >= open && now <= close;
  };

  // Filter registration periods based on search term and status
  const filteredRegistrationPeriods = registrationPeriods.filter(period => {
    // Filter by search term
    const programName = getProgramName(period.program);
    const matchesSearch =
      programName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      formatDate(period.open_date).toLowerCase().includes(searchTerm.toLowerCase()) ||
      formatDate(period.close_date).toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by status
    let matchesStatus = true;
    const now = new Date();
    const openDate = new Date(period.open_date);
    const closeDate = new Date(period.close_date);

    if (statusFilter === 'active') {
      matchesStatus = period.is_active === true;
    } else if (statusFilter === 'inactive') {
      matchesStatus = period.is_active === false;
    } else if (statusFilter === 'current') {
      matchesStatus = period.is_active === true && now >= openDate && now <= closeDate;
    } else if (statusFilter === 'upcoming') {
      matchesStatus = period.is_active === true && now < openDate;
    } else if (statusFilter === 'expired') {
      matchesStatus = period.is_active === true && now > closeDate;
    }

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredRegistrationPeriods.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredRegistrationPeriods.length / itemsPerPage);

  // Reset to first page when search term or status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Registration Period Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete registration periods for the application portal
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Registration Period
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                        <Calendar className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Add New Registration Period</DialogTitle>
                        <DialogDescription className="text-gray-600 leading-relaxed">
                          Create a new registration period for students to apply to programs. Set the dates, select the program and academic year, and configure the availability settings.
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>
                  <div className="p-6 space-y-8">
                    {/* General Error Display */}
                    {formErrors.general && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <svg className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <div>
                            <h3 className="text-sm font-medium text-red-800">Validation Error</h3>
                            <p className="text-sm text-red-700 mt-1">{formErrors.general}</p>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Program and Year Selection Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <FileText className="h-4 w-4 text-[#1a73c0]" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Program, Academic Year & Term</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="space-y-3">
                          <Label htmlFor="program" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Program
                          </Label>
                          <Select
                            value={formData.program}
                            onValueChange={(value) => handleSelectChange('program', value)}
                          >
                            <SelectTrigger
                              id="program"
                              className={cn(
                                "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                                formErrors.program ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              )}
                            >
                              <SelectValue placeholder="Choose a program..." />
                            </SelectTrigger>
                            <SelectContent className="max-h-60">
                              {programs.map((program) => (
                                <SelectItem key={program.id} value={program.id.toString()}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{program.program_code}</span>
                                    <span className="text-sm text-gray-500">{program.program_name}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {formErrors.program ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.program}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Select the program for this registration period</p>
                          )}
                        </div>
                        <div className="space-y-3">
                          <Label htmlFor="year" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Academic Year
                          </Label>
                          <Select
                            value={formData.year}
                            onValueChange={(value) => handleSelectChange('year', value)}
                          >
                            <SelectTrigger
                              id="year"
                              className={cn(
                                "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                                formErrors.year ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              )}
                            >
                              <SelectValue placeholder="Choose academic year..." />
                            </SelectTrigger>
                            <SelectContent>
                              {years.map((year) => (
                                <SelectItem key={year.uuid} value={year.uuid}>
                                  <div className="flex items-center">
                                    <Calendar className="h-4 w-4 mr-2 text-[#1a73c0]" />
                                    {year.year}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {formErrors.year ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.year}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Select the academic year for this registration period</p>
                          )}
                        </div>
                        <div className="space-y-3">
                          <Label htmlFor="term" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Term
                          </Label>
                          <Select
                            value={formData.term}
                            onValueChange={(value) => handleSelectChange('term', value)}
                          >
                            <SelectTrigger
                              id="term"
                              className={cn(
                                "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                                formErrors.term ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              )}
                            >
                              <SelectValue placeholder="Choose term..." />
                            </SelectTrigger>
                            <SelectContent>
                              {terms.map((term) => (
                                <SelectItem key={term.id} value={term.id}>
                                  <div className="flex items-center">
                                    <Calendar className="h-4 w-4 mr-2 text-[#1a73c0]" />
                                    {term.name}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {formErrors.term ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.term}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Select the term for this registration period</p>
                          )}
                        </div>
                      </div>
                    </div>
                    {/* Date and Time Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <Clock className="h-4 w-4 text-green-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Registration Schedule</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="open_date" className="text-sm font-semibold text-gray-700 flex items-center">
                              <span className="text-red-500 mr-1">*</span>
                              Opening Date & Time
                            </Label>
                            {formData.is_active && (
                              <span className="text-xs text-amber-600 font-medium bg-amber-50 px-2 py-1 rounded-full border border-amber-200">
                                Must be today or future
                              </span>
                            )}
                          </div>
                          <div className="relative">
                            <Input
                              id="open_date"
                              name="open_date"
                              type="datetime-local"
                              value={formData.open_date}
                              onChange={handleInputChange}
                              className={cn(
                                "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200 pl-10",
                                formErrors.open_date ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              )}
                            />
                            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          </div>
                          {formErrors.open_date ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.open_date}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">
                              The opening date must be in the future (greater than current date and time).
                            </p>
                          )}
                        </div>
                        <div className="space-y-3">
                          <Label htmlFor="close_date" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Closing Date & Time
                          </Label>
                          <div className="relative">
                            <Input
                              id="close_date"
                              name="close_date"
                              type="datetime-local"
                              value={formData.close_date}
                              onChange={handleInputChange}
                              className={cn(
                                "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200 pl-10",
                                formErrors.close_date ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              )}
                            />
                            <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          </div>
                          {formErrors.close_date ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.close_date}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">The date and time when registration will close. Must be after the opening date.</p>
                          )}
                        </div>
                      </div>
                    </div>
                    {/* Status Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <CheckCircle className="h-4 w-4 text-purple-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Availability Settings</h3>
                      </div>

                      <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <Switch
                              id="is_active"
                              checked={formData.is_active}
                              onCheckedChange={handleSwitchChange}
                              className={cn(
                                "data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300",
                                "transition-all duration-200"
                              )}
                            />
                            <Label htmlFor="is_active" className="text-base font-semibold text-gray-700">
                              Registration Period Status
                            </Label>
                          </div>
                          <span className={cn(
                            "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                            formData.is_active
                              ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                              : "bg-gray-100 text-gray-800 border border-gray-200"
                          )}>
                            {formData.is_active ? "✓ Active" : "○ Inactive"}
                          </span>
                        </div>

                        <div className={cn(
                          "p-4 rounded-lg border transition-all duration-200",
                          formData.is_active
                            ? "bg-green-50 border-green-200"
                            : "bg-gray-50 border-gray-200"
                        )}>
                          <div className="flex items-start space-x-3">
                            {formData.is_active ? (
                              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                            ) : (
                              <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                            )}
                            <div>
                              <p className={cn(
                                "text-sm font-medium mb-1",
                                formData.is_active ? "text-green-800" : "text-gray-700"
                              )}>
                                {formData.is_active ? "Registration Period is Active" : "Registration Period is Inactive"}
                              </p>
                              <p className="text-xs text-gray-600 leading-relaxed">
                                {formData.is_active
                                  ? "This registration period will be visible to applicants and they can submit applications. The opening date must be today or in the future when active."
                                  : "This registration period will be hidden from applicants and no new applications can be submitted."}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
                    <div className="flex items-center justify-between w-full">
                      <div className="text-xs text-gray-500">
                        <span className="text-red-500">*</span> Required fields
                      </div>
                      <div className="flex space-x-3">
                        <DialogClose asChild>
                          <Button
                            variant="outline"
                            className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                          >
                            Cancel
                          </Button>
                        </DialogClose>
                        <Button
                          onClick={handleAddRegistrationPeriod}
                          className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create Registration Period
                        </Button>
                      </div>
                    </div>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search & Filter
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search registration periods..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
                <div className="flex gap-3">
                  <Select
                    value={statusFilter}
                    onValueChange={(value) => setStatusFilter(value)}
                  >
                    <SelectTrigger className="w-[150px] border-blue-200 focus:ring-blue-400 shadow-sm">
                      <SelectValue placeholder="Filter by Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="current">Current</SelectItem>
                      <SelectItem value="upcoming">Upcoming</SelectItem>
                      <SelectItem value="expired">Expired</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            {(searchTerm || statusFilter !== 'all') && (
              <div className="p-3 border border-blue-100 rounded-md bg-white shadow-sm">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-sm font-medium text-[#1a73c0] flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                    Active Filters:
                  </span>

                  {searchTerm && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                      Search: {searchTerm}
                      <button
                        onClick={() => setSearchTerm('')}
                        className="ml-2 hover:text-blue-900 transition-colors"
                        aria-label="Remove search filter"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </span>
                  )}

                  {statusFilter !== 'all' && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                      Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                      <button
                        onClick={() => setStatusFilter('all')}
                        className="ml-2 hover:text-blue-900 transition-colors"
                        aria-label="Remove status filter"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </span>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('all');
                    }}
                    className="text-xs h-7 px-3 ml-auto border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Clear All
                  </Button>
                </div>
              </div>
            )}
          </div>
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[18%] text-[#1a73c0] font-medium">Program</TableHead>
                    <TableHead className="w-[12%] text-[#1a73c0] font-medium">Year</TableHead>
                    <TableHead className="w-[12%] text-[#1a73c0] font-medium">Term</TableHead>
                    <TableHead className="w-[18%] text-[#1a73c0] font-medium">Opening Date</TableHead>
                    <TableHead className="w-[18%] text-[#1a73c0] font-medium">Closing Date</TableHead>
                    <TableHead className="w-[12%] text-[#1a73c0] font-medium">Status</TableHead>
                    <TableHead className="w-[10%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col justify-center items-center space-y-3">
                        <div className="bg-blue-100 p-3 rounded-full">
                          <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                        </div>
                        <div className="text-[#1a73c0] font-medium">Loading registration periods...</div>
                        <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredRegistrationPeriods.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col justify-center items-center space-y-3">
                        <div className="bg-gray-100 p-3 rounded-full">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="text-gray-700 font-medium">No registration periods found</div>
                        <div className="text-sm text-gray-500 max-w-sm text-center">
                          {searchTerm ?
                            'Try adjusting your search criteria to find what you\'re looking for.' :
                            'There are no registration periods available. Click the "Add Registration Period" button to create one.'}
                        </div>
                        {(searchTerm || statusFilter !== 'all') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSearchTerm('');
                              setStatusFilter('all');
                            }}
                            className="mt-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                          >
                            Clear Filters
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentItems.map((period) => (
                    <TableRow key={period.id} className="hover:bg-blue-50 transition-colors">
                      <TableCell className="font-medium text-[#1a73c0]">{getProgramName(period.program)}</TableCell>
                      <TableCell className="font-medium text-gray-700">{getYearName(period.year)}</TableCell>
                      <TableCell className="font-medium text-gray-700">{getTermName(period.term)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-blue-500" />
                          {formatDate(period.open_date)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-blue-500" />
                          {formatDate(period.close_date)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {period.is_active ? (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200 shadow-sm">
                              <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-green-600"></span>
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200 shadow-sm">
                              <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-red-600"></span>
                              Inactive
                            </span>
                          )}
                          {period.is_active && isCurrentlyActive(period.open_date, period.close_date) && (
                            <span className="ml-2 px-2.5 py-1 bg-green-100 text-green-800 text-xs rounded-full border border-green-200 shadow-sm">
                              Current
                            </span>
                          )}
                          {period.is_active && !isCurrentlyActive(period.open_date, period.close_date) && (
                            <span className="ml-2 px-2.5 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full border border-yellow-200 shadow-sm">
                              {new Date(period.open_date) > new Date() ? 'Upcoming' : 'Expired'}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(period)}
                            title="Edit"
                            className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDeleteDialog(period)}
                            className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredRegistrationPeriods.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredRegistrationPeriods.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredRegistrationPeriods.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Dynamic page number buttons with ellipsis for large page counts */}
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === number
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show a condensed version with ellipsis
                    <>
                      {/* Always show first page */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === 1
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {currentPage > 3 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number =>
                          number > 1 &&
                          number < totalPages &&
                          (
                            number === currentPage - 1 ||
                            number === currentPage ||
                            number === currentPage + 1 ||
                            (currentPage <= 3 && number <= 4) ||
                            (currentPage >= totalPages - 2 && number >= totalPages - 3)
                          )
                        )
                        .map(number => (
                          <Button
                            key={number}
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={cn(
                              "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                              currentPage === number
                                ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                                : "bg-white text-gray-700 hover:bg-blue-50"
                            )}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {currentPage < totalPages - 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === totalPages
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Pencil className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Edit Registration Period</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Update the registration period settings. Make sure to review all fields before saving changes.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6 space-y-8">
            {/* General Error Display */}
            {formErrors.general && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <svg className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-red-800">Validation Error</h3>
                    <p className="text-sm text-red-700 mt-1">{formErrors.general}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Program and Year Selection Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-4 w-4 text-[#1a73c0]" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Program, Academic Year & Term</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="edit-program" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Program
                  </Label>
                  <Select
                    value={formData.program}
                    onValueChange={(value) => handleSelectChange('program', value)}
                  >
                    <SelectTrigger
                      id="edit-program"
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.program ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    >
                      <SelectValue placeholder="Choose a program..." />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      {programs.map((program) => (
                        <SelectItem key={program.id} value={program.id.toString()}>
                          <div className="flex flex-col">
                            <span className="font-medium">{program.program_code}</span>
                            <span className="text-sm text-gray-500">{program.program_name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.program ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.program}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Select the program for this registration period</p>
                  )}
                </div>
                <div className="space-y-3">
                  <Label htmlFor="edit-year" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Academic Year
                  </Label>
                  <Select
                    value={formData.year}
                    onValueChange={(value) => handleSelectChange('year', value)}
                  >
                    <SelectTrigger
                      id="edit-year"
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.year ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    >
                      <SelectValue placeholder="Choose academic year..." />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year.uuid} value={year.uuid}>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-[#1a73c0]" />
                            {year.year}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.year ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.year}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Select the academic year for this registration period</p>
                  )}
                </div>
                <div className="space-y-3">
                  <Label htmlFor="edit-term" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Term
                  </Label>
                  <Select
                    value={formData.term}
                    onValueChange={(value) => handleSelectChange('term', value)}
                  >
                    <SelectTrigger
                      id="edit-term"
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.term ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    >
                      <SelectValue placeholder="Choose term..." />
                    </SelectTrigger>
                    <SelectContent>
                      {terms.map((term) => (
                        <SelectItem key={term.id} value={term.id}>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-[#1a73c0]" />
                            {term.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.term ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.term}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Select the term for this registration period</p>
                  )}
                </div>
              </div>
            </div>
            {/* Date and Time Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Clock className="h-4 w-4 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Registration Schedule</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="edit-open_date" className="text-sm font-semibold text-gray-700 flex items-center">
                      <span className="text-red-500 mr-1">*</span>
                      Opening Date & Time
                    </Label>
                    {formData.is_active && (
                      <span className="text-xs text-amber-600 font-medium bg-amber-50 px-2 py-1 rounded-full border border-amber-200">
                        Must be today or future
                      </span>
                    )}
                  </div>
                  <div className="relative">
                    <Input
                      id="edit-open_date"
                      name="open_date"
                      type="datetime-local"
                      value={formData.open_date}
                      onChange={handleInputChange}
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200 pl-10",
                        formErrors.open_date ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    />
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  </div>
                  {formErrors.open_date ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.open_date}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">
                      The opening date must be in the future (greater than current date and time).
                    </p>
                  )}
                </div>
                <div className="space-y-3">
                  <Label htmlFor="edit-close_date" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Closing Date & Time
                  </Label>
                  <div className="relative">
                    <Input
                      id="edit-close_date"
                      name="close_date"
                      type="datetime-local"
                      value={formData.close_date}
                      onChange={handleInputChange}
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200 pl-10",
                        formErrors.close_date ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    />
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  </div>
                  {formErrors.close_date ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.close_date}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">The date and time when registration will close. Must be after the opening date.</p>
                  )}
                </div>
              </div>
            </div>

            {/* Status Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Availability Settings</h3>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Switch
                      id="edit-is_active"
                      checked={formData.is_active}
                      onCheckedChange={handleSwitchChange}
                      className={cn(
                        "data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300",
                        "transition-all duration-200"
                      )}
                    />
                    <Label htmlFor="edit-is_active" className="text-base font-semibold text-gray-700">
                      Registration Period Status
                    </Label>
                  </div>
                  <span className={cn(
                    "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                    formData.is_active
                      ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                      : "bg-gray-100 text-gray-800 border border-gray-200"
                  )}>
                    {formData.is_active ? "✓ Active" : "○ Inactive"}
                  </span>
                </div>

                <div className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  formData.is_active
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                )}>
                  <div className="flex items-start space-x-3">
                    {formData.is_active ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={cn(
                        "text-sm font-medium mb-1",
                        formData.is_active ? "text-green-800" : "text-gray-700"
                      )}>
                        {formData.is_active ? "Registration Period is Active" : "Registration Period is Inactive"}
                      </p>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {formData.is_active
                          ? "This registration period will be visible to applicants and they can submit applications. The opening date must be today or in the future when active."
                          : "This registration period will be hidden from applicants and no new applications can be submitted."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="text-xs text-gray-500">
                <span className="text-red-500">*</span> Required fields
              </div>
              <div className="flex space-x-3">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={handleEditRegistrationPeriod}
                  className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Update Registration Period
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-pink-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-red-500 rounded-lg shadow-sm">
                <Trash2 className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-red-600">Confirm Deletion</DialogTitle>
                <DialogDescription className="mt-1">
                  This action cannot be undone
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-6">
            <p className="text-gray-700">
              Are you sure you want to delete the registration period for:
            </p>
            {currentRegistrationPeriod && (
              <div className="mt-3 p-3 bg-gray-50 rounded-md border border-gray-200">
                <p className="font-medium text-[#1a73c0]">
                  {getProgramName(currentRegistrationPeriod.program)}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  Academic Year: <span className="font-medium">{getYearName(currentRegistrationPeriod.year)}</span>
                </p>
                <p className="text-sm text-gray-600">
                  Term: <span className="font-medium">{getTermName(currentRegistrationPeriod.term)}</span>
                </p>
                <div className="mt-2 flex flex-col gap-1 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <span>Opens: {formatDate(currentRegistrationPeriod.open_date)}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <span>Closes: {formatDate(currentRegistrationPeriod.close_date)}</span>
                  </div>
                  <div className="flex items-center mt-1">
                    {currentRegistrationPeriod.is_active ? (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                        <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-green-600"></span>
                        Active
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                        <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-red-600"></span>
                        Inactive
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}
            <p className="mt-4 text-sm text-red-500">
              Warning: This will permanently delete this registration period from the system.
            </p>
          </div>
          <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
            <DialogClose asChild>
              <Button variant="outline" className="border-gray-300">Cancel</Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteRegistrationPeriod}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ApplicationRegistrationPeriodManagement;
