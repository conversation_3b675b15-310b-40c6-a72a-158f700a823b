from rest_framework import generics
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from .models import RegistrationPeriod
from .serializers import RegistrationPeriodSerializer

# Create your views here.

# List and Create view for RegistrationPeriod (GET, POST)
class RegistrationPeriodList(generics.ListCreateAPIView):
    serializer_class = RegistrationPeriodSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return RegistrationPeriod.objects.all()

    def perform_create(self, serializer):
        if serializer.is_valid():
            serializer.save()
        else:
            print(serializer.errors)

# Delete view for RegistrationPeriod (DELETE)
class RegistrationPeriodDelete(generics.DestroyAPIView):
    serializer_class = RegistrationPeriodSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return RegistrationPeriod.objects.all()

# Retrieve and Update view for RegistrationPeriod (GET, PUT)
class RegistrationPeriodDetail(generics.RetrieveUpdateAPIView):
    serializer_class = RegistrationPeriodSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return RegistrationPeriod.objects.all()

    def update(self, request, *args, **kwargs):
        print(f"Update request received: {request.data}")
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        print(f"Updating instance: {instance}")

        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        try:
            serializer.is_valid(raise_exception=True)
            print(f"Serializer validated data: {serializer.validated_data}")
            self.perform_update(serializer)
            print("Update successful")
            return Response(serializer.data)
        except Exception as e:
            print(f"Update failed with error: {e}")
            if hasattr(serializer, 'errors'):
                print(f"Serializer errors: {serializer.errors}")
            raise

# Public list view for RegistrationPeriod (GET only)
class PublicRegistrationPeriodList(generics.ListAPIView):
    serializer_class = RegistrationPeriodSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        # Return all registration periods for public access
        return RegistrationPeriod.objects.all()
