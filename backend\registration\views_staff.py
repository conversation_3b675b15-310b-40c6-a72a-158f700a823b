from rest_framework import generics, filters, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth.models import User, Group
from django.http import JsonR<PERSON>ponse
from .models import ApplicantProgramSelection, ApplicantInformation, ApplicantDocumentation
from .serializers_staff import ApplicantListSerializer, ApplicantDetailSerializer
from django.db.models import Q

class StaffPermission(IsAuthenticated):
    """
    Custom permission to only allow staff members with specific permissions to access the view.
    """
    def has_permission(self, request, view):
        # First check if the user is authenticated
        if not super().has_permission(request, view):
            return False

        # Check if the user is staff
        if not request.user.is_staff:
            return False

        # Check if the user has the 'view_applicants' permission
        # This can come from direct user permissions or group permissions
        return (
            request.user.is_superuser or
            request.user.has_perm('registration.view_applicantprogramselection')
        )

class ApplicantListView(generics.ListAPIView):
    """
    API endpoint for staff to view all applicants
    """
    serializer_class = ApplicantListSerializer
    permission_classes = [StaffPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = {
        'registrar_off_status': ['exact'],
        'department_status': ['exact'],
        'payment_status': ['exact'],
        'sponsorship': ['exact'],
        'created_at': ['gte', 'lte'],
        'year': ['exact'],
        'term': ['exact'],
    }
    search_fields = ['user__username', 'user__email', 'user__first_name', 'user__last_name',
                     'application_num', 'transaction_id']
    ordering_fields = ['created_at', 'user__last_name', 'user__first_name']
    ordering = ['-created_at']  # Default ordering

    def get_queryset(self):
        """
        This view returns a list of all applicants.
        Staff can filter by department if they have department-specific permissions.
        """
        queryset = ApplicantProgramSelection.objects.all().select_related(
            'user', 'gat', 'application_info',
            'application_info__college',
            'application_info__department',
            'application_info__program'
        )

        # Filter by college/department if specified in query params
        college_id = self.request.query_params.get('college_id')
        department_id = self.request.query_params.get('department_id')
        program_id = self.request.query_params.get('program_id')
        year_id = self.request.query_params.get('year')
        term_id = self.request.query_params.get('term')

        if college_id:
            queryset = queryset.filter(application_info__college_id=college_id)

        if department_id:
            queryset = queryset.filter(application_info__department_id=department_id)

        if program_id:
            queryset = queryset.filter(application_info__program_id=program_id)

        if year_id:
            queryset = queryset.filter(year_id=year_id)

        if term_id:
            queryset = queryset.filter(term_id=term_id)

        return queryset

class ApplicantDetailView(generics.RetrieveAPIView):
    """
    API endpoint for staff to view details of a specific applicant
    """
    serializer_class = ApplicantDetailSerializer
    permission_classes = [StaffPermission]
    queryset = ApplicantProgramSelection.objects.all().select_related(
        'user', 'gat', 'application_info',
        'application_info__college',
        'application_info__department',
        'application_info__program'
    )

class ApplicantStatusUpdateView(generics.UpdateAPIView):
    """
    API endpoint for staff to update the status of an applicant
    """
    serializer_class = ApplicantDetailSerializer
    permission_classes = [StaffPermission]
    queryset = ApplicantProgramSelection.objects.all()

    def update(self, request, *args, **kwargs):
        instance = self.get_object()

        # Only allow updating status fields and remark
        allowed_fields = ['registrar_off_status', 'department_status', 'payment_status', 'remark']
        data = {k: v for k, v in request.data.items() if k in allowed_fields}

        # Update the approved_by fields based on which status is being updated
        if 'registrar_off_status' in data:
            instance.reg_approved_by = request.user

        if 'department_status' in data:
            instance.dep_approved_by = request.user

        # Update the instance with the filtered data
        for key, value in data.items():
            setattr(instance, key, value)

        instance.save()

        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class ApplicantDocumentsView(APIView):
    """
    API endpoint for staff to get documents for a specific user
    """
    permission_classes = [StaffPermission]

    def get(self, request, user_id):
        try:
            # Get the documents for the user
            docs = ApplicantDocumentation.objects.filter(user_id=user_id).first()

            if not docs:
                return Response({"detail": "No documents found for this user"}, status=404)

            # Create a dictionary of document URLs
            document_data = {}

            # Only include fields that actually have files
            if docs.degree:
                document_data['degree'] = request.build_absolute_uri(docs.degree.url)
            if docs.sponsorship:
                document_data['sponsorship'] = request.build_absolute_uri(docs.sponsorship.url)
            if docs.student_copy:
                document_data['student_copy'] = request.build_absolute_uri(docs.student_copy.url)
            if docs.recommendation:
                document_data['recommendation'] = request.build_absolute_uri(docs.recommendation.url)
            if docs.publication:
                document_data['publication'] = request.build_absolute_uri(docs.publication.url)
            if docs.conceptnote:
                document_data['conceptnote'] = request.build_absolute_uri(docs.conceptnote.url)
            if docs.grade_12_certificate:
                document_data['grade_12_certificate'] = request.build_absolute_uri(docs.grade_12_certificate.url)
            if docs.grade_9_12_transcript:
                document_data['grade_9_12_transcript'] = request.build_absolute_uri(docs.grade_9_12_transcript.url)

            return Response(document_data)
        except Exception as e:
            return Response({"detail": str(e)}, status=500)
