from django.contrib import admin
from .smtp_models import SMTPSettings


@admin.register(SMTPSettings)
class SMTPSettingsAdmin(admin.ModelAdmin):
    fieldsets = (
        ('Server Configuration', {
            'fields': ('smtp_enabled', 'smtp_host', 'smtp_port')
        }),
        ('Authentication', {
            'fields': ('smtp_username', 'smtp_password')
        }),
        ('Security', {
            'fields': ('smtp_use_tls', 'smtp_use_ssl', 'smtp_timeout')
        }),
        ('Email Settings', {
            'fields': ('smtp_from_email', 'smtp_from_name')
        }),
    )
    readonly_fields = ('created_at', 'updated_at')
    
    def has_add_permission(self, request):
        # Only allow adding if no instance exists
        return not SMTPSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the settings object
        return False
