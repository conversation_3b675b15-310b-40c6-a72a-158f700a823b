from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator, EmailValidator, RegexValidator
from decimal import Decimal
from django.utils import timezone
import uuid


class VerificationCollege(models.Model):
    name = models.Char<PERSON>ield(
        max_length=200,
        unique=True,
        help_text="Name of the college"
    )
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique code identifier for the college"
    )

    class Meta:
        ordering = ['name']
        verbose_name_plural = "Colleges"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        self.code = self.code.upper()
        super().save(*args, **kwargs)

class VerificationDepartment(models.Model):
    college = models.ForeignKey(
        VerificationCollege,
        on_delete=models.PROTECT,
        related_name='departments',
        help_text="College this department belongs to"
    )
    name = models.CharField(
        max_length=200,
        help_text="Name of the department"
    )
    code = models.Char<PERSON><PERSON>(
        max_length=20,
        help_text="Department code"
    )

    class Meta:
        unique_together = ('college', 'code')
        ordering = ['college', 'name']
        verbose_name_plural = "Departments"

    def __str__(self):
        return f"{self.name} ({self.college.code})"

    def save(self, *args, **kwargs):
        self.code = self.code.upper()
        super().save(*args, **kwargs)

class VerificationFieldOfStudy(models.Model):
    department = models.ForeignKey(
        VerificationDepartment,
        on_delete=models.PROTECT,
        related_name='fields_of_study',
        help_text="Department this field belongs to"
    )
    name = models.CharField(
        max_length=200,
        help_text="Name of the field of study"
    )
    code = models.CharField(
        max_length=20,
        help_text="Field of study code"
    )
    duration = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Duration of the field of study in years"
    )

    class Meta:
        unique_together = ('department', 'code')
        verbose_name_plural = "Fields of Study"
        ordering = ['department', 'name']

    def __str__(self):
        duration_str = f" ({self.duration} years)" if self.duration else ""
        return f"{self.name} ({self.code}){duration_str}"

    def save(self, *args, **kwargs):
        self.code = self.code.upper()
        super().save(*args, **kwargs)

class VerificationProgram(models.Model):
    name = models.CharField(
        max_length=200,
        unique=True,
        help_text="Name of the program"
    )
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique program code"
    )

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        self.code = self.code.upper()
        super().save(*args, **kwargs)

class AdmissionClassification(models.Model):
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Name of the admission classification"
    )
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Detailed description of the classification (optional)"
    )

    class Meta:
        ordering = ['name']
        verbose_name_plural = "Admission Classifications"

    def __str__(self):
        return self.name


class GraduateStudentManager(models.Manager):
    """Custom manager for GraduateStudent with soft delete support"""

    def get_queryset(self):
        """Return only non-deleted records by default"""
        return super().get_queryset().filter(is_deleted=False)

    def all_with_deleted(self):
        """Return all records including soft-deleted ones"""
        return super().get_queryset()

    def deleted_only(self):
        """Return only soft-deleted records"""
        return super().get_queryset().filter(is_deleted=True)


class GraduateStudent(models.Model):
    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
    ]

    student_id = models.CharField(
        max_length=50,
        unique=True,
        db_index=True,
        help_text="Unique identifier for the student"
    )
    first_name = models.CharField(
        max_length=100,
        db_index=True,
        help_text="Student's first name"
    )
    middle_name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Student's middle name (optional)"
    )
    last_name = models.CharField(
        max_length=100,
        db_index=True,
        help_text="Student's last name"
    )
    year_of_entry = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1900, message="Year must be 1900 or later"),
            MaxValueValidator(timezone.now().year, message=f"Year cannot be later than {timezone.now().year}")
        ],
        db_index=True,
        help_text="Year when the student entered the program"
    )
    year_of_graduation = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1900, message="Year must be 1900 or later"),
            MaxValueValidator(timezone.now().year, message=f"Year cannot be later than {timezone.now().year}")
        ],
        db_index=True,
        help_text="Year when the student graduated"
    )
    gpa = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal('2.00')),
            MaxValueValidator(Decimal('4.00'))
        ]
    )
    gender = models.CharField(
        max_length=20,
        choices=GENDER_CHOICES,
        help_text="Student's gender identity"
    )
    college = models.ForeignKey(
        'VerificationCollege',
        on_delete=models.PROTECT,
        related_name='graduate_students',
        help_text="College the student graduated from"
    )
    department = models.ForeignKey(
        'VerificationDepartment',
        on_delete=models.PROTECT,
        related_name='graduate_students',
        help_text="Department the student graduated from"
    )
    field_of_study = models.ForeignKey(
        'VerificationFieldOfStudy',
        on_delete=models.PROTECT,
        related_name='graduate_students',
        help_text="Student's field of study"
    )
    program = models.ForeignKey(
        'VerificationProgram',
        on_delete=models.PROTECT,
        related_name='graduate_students',
        help_text="Academic program completed"
    )
    admission_classification = models.ForeignKey(
        'AdmissionClassification',
        on_delete=models.PROTECT,
        related_name='graduate_students',
        help_text="Classification of admission"
    )

    # Audit trail fields
    created_by = models.ForeignKey(
        'auth.User',
        on_delete=models.PROTECT,
        related_name='created_graduate_students',
        null=True,
        blank=True,
        help_text="User who created this record"
    )
    updated_by = models.ForeignKey(
        'auth.User',
        on_delete=models.PROTECT,
        related_name='updated_graduate_students',
        null=True,
        blank=True,
        help_text="User who last updated this record"
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Record creation timestamp"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="Record last update timestamp"
    )

    # Soft delete fields
    is_deleted = models.BooleanField(
        default=False,
        db_index=True,
        help_text="Indicates if the record is soft deleted"
    )
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when the record was soft deleted"
    )
    deleted_by = models.ForeignKey(
        'auth.User',
        on_delete=models.PROTECT,
        related_name='deleted_graduate_students',
        null=True,
        blank=True,
        help_text="User who soft deleted this record"
    )

    # Custom manager
    objects = GraduateStudentManager()

    class Meta:
        indexes = [
            models.Index(fields=['first_name', 'last_name']),
            models.Index(fields=['last_name', 'first_name']),
            models.Index(fields=['year_of_entry']),
            models.Index(fields=['year_of_graduation', 'field_of_study']),
            models.Index(fields=['year_of_graduation', 'college']),
            models.Index(fields=['year_of_entry', 'year_of_graduation']),
        ]
        verbose_name = "Graduate Verification"
        verbose_name_plural = "Graduate Verifications"
        ordering = ['-year_of_graduation', 'last_name', 'first_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.student_id})"

    def get_full_name(self):
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    def save(self, *args, **kwargs):
        self.first_name = self.first_name.strip().title()
        self.last_name = self.last_name.strip().title()
        if self.middle_name:
            self.middle_name = self.middle_name.strip().title()
        super().save(*args, **kwargs)

    def soft_delete(self, user=None):
        """Soft delete the record"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        if user:
            self.deleted_by = user
        self.save()

    def restore(self, user=None):
        """Restore a soft-deleted record"""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        if user:
            self.updated_by = user
        self.save()

