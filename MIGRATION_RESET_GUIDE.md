# Migration Reset Guide for application_information App

This guide helps you fix the Django migration error: `django.db.utils.ProgrammingError: column "duration" of relation "application_information_applicationinformation" already exists`

## Problem
The migration files were deleted but the database still contains the table structure, causing conflicts when trying to create new migrations.

## Solution Options

### Option 1: Automated Script (Recommended)
Run the PowerShell script:
```powershell
.\reset_application_information_migrations.ps1
```

### Option 2: Manual Steps

#### Prerequisites
1. Activate your virtual environment:
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

2. Ensure Django is installed:
   ```bash
   pip install -r backend/requirements.txt
   ```

#### Step-by-Step Manual Reset

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Remove migration cache files:**
   ```bash
   # Windows PowerShell
   Remove-Item -Path "setups/application_information/migrations/__pycache__" -Recurse -Force -ErrorAction SilentlyContinue
   
   # Linux/Mac
   rm -rf setups/application_information/migrations/__pycache__/
   ```

3. **Unapply all migrations for application_information:**
   ```bash
   python manage.py migrate application_information zero --fake
   ```

4. **Remove migration entries from Django's migration table:**
   ```bash
   python manage.py shell
   ```
   
   In the Django shell, run:
   ```python
   from django.db import connection
   cursor = connection.cursor()
   cursor.execute("DELETE FROM django_migrations WHERE app = 'application_information';")
   exit()
   ```

5. **Drop the existing table:**
   ```bash
   python manage.py shell
   ```
   
   In the Django shell, run:
   ```python
   from django.db import connection
   cursor = connection.cursor()
   cursor.execute('DROP TABLE IF EXISTS application_information_applicationinformation;')
   exit()
   ```

6. **Create fresh migrations:**
   ```bash
   python manage.py makemigrations application_information
   ```

7. **Apply the fresh migrations:**
   ```bash
   python manage.py migrate application_information
   ```

8. **Verify the migration status:**
   ```bash
   python manage.py showmigrations application_information
   ```

### Option 3: Database-First Approach (If above fails)

If the above steps don't work, you can reset the entire database:

1. **Backup important data** (if any)

2. **Delete the database file:**
   ```bash
   # If using SQLite
   rm backend/db.sqlite3
   ```

3. **Run all migrations fresh:**
   ```bash
   cd backend
   python manage.py migrate
   ```

4. **Create a superuser:**
   ```bash
   python manage.py createsuperuser
   ```

## Expected Results

After successful reset, you should see:
- Fresh migration files in `backend/setups/application_information/migrations/`
- No migration conflicts
- Ability to run `python manage.py makemigrations` without errors

## Troubleshooting

### Error: "No module named 'django'"
- Activate your virtual environment
- Install dependencies: `pip install -r requirements.txt`

### Error: "Table doesn't exist"
- This is normal during the reset process
- Continue with the next steps

### Error: "Migration already exists"
- Delete any remaining migration files manually
- Clear the `__pycache__` directories
- Restart from step 3

### Error: "Foreign key constraint"
- You may need to reset related apps as well
- Consider using Option 3 (full database reset)

## Prevention

To avoid this issue in the future:
1. Never manually delete migration files without proper Django commands
2. Use `python manage.py migrate app_name zero` before deleting migrations
3. Always backup your database before migration changes
4. Use version control to track migration file changes

## Files Modified/Created

- `reset_application_information_migrations.ps1` - Automated reset script
- `MIGRATION_RESET_GUIDE.md` - This guide
- Updated `.gitignore` to temporarily ignore migration files during reset

## Next Steps

After fixing the migrations:
1. Test the application_information functionality
2. Create any necessary seed data
3. Run tests to ensure everything works correctly
4. Remove the temporary migration ignore patterns from `.gitignore`
