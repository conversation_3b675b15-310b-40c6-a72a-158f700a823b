import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { Switch } from './ui/switch';
import { Loader2, Plus, Pencil, Trash2, X, Check, RefreshCw, Filter, Building, GraduationCap, CheckCircle, FileText, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Download, FileSpreadsheet, ToggleLeft, ToggleRight } from 'lucide-react';
import { useToast } from './ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from './ui/alert-dialog';
import { useAuth } from '@/hooks/useAuth';

interface College {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
}

interface Program {
  id: number;
  program_code: string;
  program_name: string;
  registration_fee: number;
}

interface AdmissionType {
  id: number;
  name: string;
}

interface StudyField {
  id: number;
  field_of_study: string;
  code?: string;
  college?: number;
  department?: number;
  description?: string;
  status?: boolean;
}

interface StudyProgram {
  id: number;
  program_code: string;
  program_name: string;
}

interface ApplicationInfo {
  id: number;
  admission_type: AdmissionType;
  program: Program;
  college: College;
  department: Department;
  field_of_study: {
    id: number;
    name: string;
  };
  study_program: {
    id: number;
    name: string;
  };
  spacial_case: string;
  duration: string;
  status: boolean;
  created_at: string;
  updated_at: string;
}

const ApplicationInformationView: React.FC = () => {
  const [applicationInfo, setApplicationInfo] = useState<ApplicationInfo[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [filteredDepartments, setFilteredDepartments] = useState<Department[]>([]);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [admissionTypes, setAdmissionTypes] = useState<AdmissionType[]>([]);
  const [studyFields, setStudyFields] = useState<StudyField[]>([]);
  const [filteredStudyFields, setFilteredStudyFields] = useState<StudyField[]>([]);
  const [studyPrograms, setStudyPrograms] = useState<StudyProgram[]>([]);

  const [selectedCollege, setSelectedCollege] = useState<string>('0');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('0');
  const [selectedProgram, setSelectedProgram] = useState<string>('0');
  const [selectedAdmissionType, setSelectedAdmissionType] = useState<string>('0');
  const [filteredFilterDepartments, setFilteredFilterDepartments] = useState<Department[]>([]);



  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  const [loading, setLoading] = useState<boolean>(false);
  const [filterLoading, setFilterLoading] = useState<boolean>(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState<boolean>(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [selectedInfo, setSelectedInfo] = useState<ApplicationInfo | null>(null);
  const [formData, setFormData] = useState({
    college: '',
    department: '',
    program: '',
    admission_type: '',
    field_of_study: '',
    study_program: '',
    duration: '',
    spacial_case: '',
    status: true
  });
  const [formLoading, setFormLoading] = useState<boolean>(false);

  const { toast } = useToast();
  const { user } = useAuth();

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      setLoading(true);
      try {
        // Import the API instance
        import('@/services/api').then(async ({ default: api }) => {
          try {
            // Fetch colleges
            const collegesResponse = await api.get('/colleges/');
            setColleges([{ id: 0, name: 'All Colleges' }, ...collegesResponse.data]);

            // Fetch departments
            const departmentsResponse = await api.get('/departments/');
            setDepartments([{ id: 0, name: 'All Departments' }, ...departmentsResponse.data]);

            // Initialize filtered departments for filter section
            setFilteredFilterDepartments([{ id: 0, name: 'All Departments' }]);

            // Fetch programs
            const programsResponse = await api.get('/programs/');
            setPrograms([{ id: 0, program_code: 'ALL', program_name: 'All Programs', registration_fee: 0 }, ...programsResponse.data]);

            // Fetch admission types
            const admissionTypesResponse = await api.get('/admission-types/');
            setAdmissionTypes([{ id: 0, name: 'All Admission Types' }, ...admissionTypesResponse.data]);

            // Fetch study fields
            const studyFieldsResponse = await api.get('/study-fields/');
            setStudyFields(studyFieldsResponse.data);

            // Fetch study programs
            const studyProgramsResponse = await api.get('/study-programs/');
            setStudyPrograms(studyProgramsResponse.data);

            // Fetch application information with no filters initially
            await fetchApplicationInfo();
          } catch (error) {
            console.error('Error fetching initial data with API instance:', error);
            throw error;
          }
        }).catch(error => {
          console.error('Error importing API instance:', error);
          throw error;
        });
      } catch (error) {
        console.error('Error fetching initial data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load initial data. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  // Fetch application information based on filters
  const fetchApplicationInfo = async () => {
    setFilterLoading(true);
    try {
      const params = new URLSearchParams();

      if (selectedCollege !== '0') params.append('college', selectedCollege);
      if (selectedDepartment !== '0') params.append('department', selectedDepartment);
      if (selectedProgram !== '0') params.append('program', selectedProgram);
      if (selectedAdmissionType !== '0') params.append('admission_type', selectedAdmissionType);

      // Import the API instance
      const { default: api } = await import('@/services/api');
      const response = await api.get(`/application-information/filter/?${params.toString()}`);
      setApplicationInfo(response.data);

      // Reset to first page when filters change
      setCurrentPage(1);

      toast({
        title: 'Success',
        description: `Found ${response.data.length} application information records.`,
      });
    } catch (error) {
      console.error('Error fetching application information:', error);
      toast({
        title: 'Error',
        description: 'Failed to load application information. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setFilterLoading(false);
    }
  };

  // Auto-filter when filter criteria change
  useEffect(() => {
    setCurrentPage(1);
    fetchApplicationInfo();
  }, [selectedCollege, selectedDepartment, selectedProgram, selectedAdmissionType]);

  // Debug form data
  useEffect(() => {
    console.log("Form Data:", formData);
    console.log("Study Fields:", studyFields);
    console.log("Study Programs:", studyPrograms);
  }, [formData, studyFields, studyPrograms]);

  // Reset filters
  const handleReset = () => {
    setSelectedCollege('0');
    setSelectedDepartment('0');
    setSelectedProgram('0');
    setSelectedAdmissionType('0');
    setFilteredFilterDepartments([]);
    setCurrentPage(1); // Reset to first page when filters change
    fetchApplicationInfo();
  };

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = applicationInfo.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(applicationInfo.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle status toggle
  const handleStatusToggle = async (info: ApplicationInfo) => {
    try {
      const { default: api } = await import('@/services/api');

      // Update the status
      const updatedData = {
        ...info,
        status: !info.status,
        college_id: info.college.id,
        department_id: info.department.id,
        program_id: info.program.id,
        admission_type_id: info.admission_type.id,
        field_of_study_id: info.field_of_study.id,
        study_program_id: info.study_program.id
      };

      await api.put(`/application-information/${info.id}/`, updatedData);

      // Update the local state
      setApplicationInfo(prev =>
        prev.map(item =>
          item.id === info.id
            ? { ...item, status: !item.status }
            : item
        )
      );

      toast({
        title: 'Success',
        description: `Application information ${!info.status ? 'activated' : 'deactivated'} successfully.`,
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Export to Excel
  const exportToExcel = () => {
    try {
      // Prepare data for export
      const exportData = applicationInfo.map((info, index) => ({
        'S.No': index + 1,
        'College': info.college.name,
        'Department': info.department.name,
        'Program': info.program.program_name,
        'Admission Type': info.admission_type.name,
        'Field of Study': info.field_of_study.name,
        'Study Program': info.study_program.name,
        'Duration': info.duration || 'N/A',
        'Special Case': info.spacial_case || 'N/A',
        'Status': info.status ? 'Active' : 'Inactive',
        'Created Date': new Date(info.created_at).toLocaleDateString(),
        'Updated Date': new Date(info.updated_at).toLocaleDateString()
      }));

      // Convert to CSV format
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => {
            const value = row[header as keyof typeof row];
            // Escape commas and quotes in values
            return typeof value === 'string' && (value.includes(',') || value.includes('"'))
              ? `"${value.replace(/"/g, '""')}"`
              : value;
          }).join(',')
        )
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `application-information-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Export Successful',
        description: 'Application information exported to Excel successfully.',
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export application information. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Export to PDF
  const exportToPDF = () => {
    try {
      // Create HTML content for PDF
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Application Information Report</title>
          <style>
            @page {
              size: A4 landscape;
              margin: 0.5in;
            }
            @media print {
              body {
                margin: 0;
                padding: 0;
                width: 100%;
                max-width: none;
              }
              table {
                page-break-inside: auto;
                width: 100%;
              }
              tr {
                page-break-inside: avoid;
                page-break-after: auto;
              }
              thead {
                display: table-header-group;
              }
            }
            body {
              font-family: Arial, sans-serif;
              margin: 10px;
              padding: 0;
              width: 100%;
              max-width: none;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
            }
            .header h1 {
              color: #1a73c0;
              margin-bottom: 5px;
              font-size: 24px;
            }
            .header p {
              color: #666;
              margin: 0;
              font-size: 12px;
            }
            .meta-info {
              margin-bottom: 15px;
              padding: 8px;
              background-color: #f8f9fa !important;
              border-radius: 5px;
              font-size: 11px;
              border: 1px solid #e9ecef;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
              table-layout: fixed;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 4px;
              text-align: left;
              font-size: 9px;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
            th {
              background-color: #1a73c0 !important;
              color: white !important;
              font-weight: bold;
              font-size: 10px;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9 !important;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .status-active {
              color: #16a34a !important;
              font-weight: bold;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .status-inactive {
              color: #dc2626 !important;
              font-weight: bold;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .footer {
              margin-top: 20px;
              text-align: center;
              font-size: 8px;
              color: #666;
            }
            /* Column widths for better layout */
            .col-sno { width: 4%; }
            .col-college { width: 12%; }
            .col-department { width: 12%; }
            .col-program { width: 12%; }
            .col-admission { width: 10%; }
            .col-field { width: 12%; }
            .col-study { width: 12%; }
            .col-duration { width: 8%; }
            .col-special { width: 10%; }
            .col-status { width: 6%; }
            .col-date { width: 8%; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Application Information Report</h1>
            <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
          </div>

          <div class="meta-info">
            <strong>Total Records:</strong> ${applicationInfo.length} |
            <strong>Active Records:</strong> ${applicationInfo.filter(info => info.status).length} |
            <strong>Inactive Records:</strong> ${applicationInfo.filter(info => !info.status).length}
          </div>

          <table>
            <thead>
              <tr>
                <th class="col-sno">S.No</th>
                <th class="col-college">College</th>
                <th class="col-department">Department</th>
                <th class="col-program">Program</th>
                <th class="col-admission">Admission Type</th>
                <th class="col-field">Field of Study</th>
                <th class="col-study">Study Program</th>
                <th class="col-duration">Duration</th>
                <th class="col-special">Special Case</th>
                <th class="col-status">Status</th>
                <th class="col-date">Created Date</th>
              </tr>
            </thead>
            <tbody>
              ${applicationInfo.map((info, index) => `
                <tr>
                  <td class="col-sno">${index + 1}</td>
                  <td class="col-college">${info.college.name}</td>
                  <td class="col-department">${info.department.name}</td>
                  <td class="col-program">${info.program.program_name}</td>
                  <td class="col-admission">${info.admission_type.name}</td>
                  <td class="col-field">${info.field_of_study.name}</td>
                  <td class="col-study">${info.study_program.name}</td>
                  <td class="col-duration">${info.duration || 'N/A'}</td>
                  <td class="col-special">${info.spacial_case || 'N/A'}</td>
                  <td class="col-status ${info.status ? 'status-active' : 'status-inactive'}">${info.status ? 'Active' : 'Inactive'}</td>
                  <td class="col-date">${new Date(info.created_at).toLocaleDateString()}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="footer">
            <p>This report was generated from the Application Information Management System</p>
          </div>
        </body>
        </html>
      `;

      // Open print dialog
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        printWindow.focus();

        // Wait for content to load then print
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 250);

        toast({
          title: 'PDF Export Initiated',
          description: 'PDF export dialog opened. Please save or print the document.',
        });
      } else {
        throw new Error('Unable to open print window');
      }
    } catch (error) {
      console.error('PDF export error:', error);
      toast({
        title: 'PDF Export Failed',
        description: 'Failed to export PDF. Please check your browser settings and try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle form input changes
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Fetch departments by college ID for form
  const fetchDepartmentsByCollege = async (collegeId: string) => {
    if (collegeId === '0' || !collegeId) {
      setFilteredDepartments([{ id: 0, name: 'Select Department' }]);
      return;
    }

    try {
      const { default: api } = await import('@/services/api');
      const response = await api.get(`/departments/public/?college=${collegeId}`);
      setFilteredDepartments([{ id: 0, name: 'Select Department' }, ...response.data]);
    } catch (error) {
      console.error('Error fetching departments by college:', error);
      toast({
        title: 'Error',
        description: 'Failed to load departments for the selected college.',
        variant: 'destructive',
      });
      setFilteredDepartments([{ id: 0, name: 'Select Department' }]);
    }
  };

  // Fetch departments by college ID for filter
  const fetchFilterDepartmentsByCollege = async (collegeId: string) => {
    if (collegeId === '0' || !collegeId) {
      setFilteredFilterDepartments([{ id: 0, name: 'All Departments' }]);
      setSelectedDepartment('0'); // Reset department selection when college is reset
      return;
    }

    try {
      const { default: api } = await import('@/services/api');
      const response = await api.get(`/departments/public/?college=${collegeId}`);
      setFilteredFilterDepartments([{ id: 0, name: 'All Departments' }, ...response.data]);
      setSelectedDepartment('0'); // Reset department selection when college changes
    } catch (error) {
      console.error('Error fetching filter departments by college:', error);
      toast({
        title: 'Error',
        description: 'Failed to load departments for the selected college.',
        variant: 'destructive',
      });
      setFilteredFilterDepartments([{ id: 0, name: 'All Departments' }]);
    }
  };

  // Filter study fields by department for form
  const filterStudyFieldsByDepartment = (departmentId: string) => {
    if (!departmentId || departmentId === '0') {
      setFilteredStudyFields([]);
      return;
    }

    const filtered = studyFields.filter(field =>
      field.department && field.department.toString() === departmentId
    );
    setFilteredStudyFields(filtered);
  };

  // Reset form data
  const resetFormData = () => {
    setFormData({
      college: '',
      department: '',
      program: '',
      admission_type: '',
      field_of_study: '',
      study_program: '',
      duration: '',
      spacial_case: '',
      status: true
    });
    setFilteredDepartments([]);
    setFilteredStudyFields([]);
  };

  // Open edit dialog and populate form data
  const handleEditClick = async (info: ApplicationInfo) => {
    setSelectedInfo(info);

    // Set form data
    setFormData({
      college: info.college.id.toString(),
      department: info.department.id.toString(),
      program: info.program.id.toString(),
      admission_type: info.admission_type.id.toString(),
      field_of_study: info.field_of_study.id.toString(),
      study_program: info.study_program.id.toString(),
      duration: info.duration || '',
      spacial_case: info.spacial_case || '',
      status: info.status
    });

    // Fetch departments for the selected college
    await fetchDepartmentsByCollege(info.college.id.toString());

    // Filter study fields for the selected department
    filterStudyFieldsByDepartment(info.department.id.toString());

    setIsEditDialogOpen(true);
  };

  // Open delete dialog
  const handleDeleteClick = (info: ApplicationInfo) => {
    setSelectedInfo(info);
    setIsDeleteDialogOpen(true);
  };

  // Validate form data
  const validateForm = () => {
    console.log('=== FORM VALIDATION ===');
    const requiredFields = ['college', 'department', 'program', 'admission_type', 'field_of_study', 'study_program'];

    console.log('Checking required fields:', requiredFields);
    requiredFields.forEach(field => {
      const value = formData[field as keyof typeof formData];
      console.log(`${field}:`, value, typeof value, !value || value === '' || value === '0');
    });

    const missingFields = requiredFields.filter(field => {
      const value = formData[field as keyof typeof formData];
      return !value || value === '' || value === '0';
    });

    console.log('Missing fields:', missingFields);

    if (missingFields.length > 0) {
      console.log('Validation failed - missing fields:', missingFields);
      toast({
        title: 'Validation Error',
        description: `Please fill in all required fields: ${missingFields.join(', ')}`,
        variant: 'destructive',
      });
      return false;
    }
    console.log('Validation passed - all required fields present');
    return true;
  };

  // Create new application information
  const handleCreate = async () => {
    console.log('=== CREATE BUTTON CLICKED ===');
    console.log('User is staff:', user?.is_staff);
    console.log('Current form data:', formData);

    if (!user?.is_staff) {
      console.log('Access denied - user is not staff');
      toast({
        title: 'Access Denied',
        description: 'Only staff users can create application information.',
        variant: 'destructive',
      });
      return;
    }

    // Validate form data
    console.log('Starting form validation...');
    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }
    console.log('Form validation passed');

    // Check for duplicate application information
    console.log('Checking for duplicate records...');
    const isDuplicate = applicationInfo.some(info =>
      info.college.id.toString() === formData.college &&
      info.department.id.toString() === formData.department &&
      info.program.id.toString() === formData.program &&
      info.admission_type.id.toString() === formData.admission_type &&
      info.field_of_study.id.toString() === formData.field_of_study &&
      info.study_program.id.toString() === formData.study_program
    );

    if (isDuplicate) {
      const duplicateInfo = applicationInfo.find(info =>
        info.college.id.toString() === formData.college &&
        info.department.id.toString() === formData.department &&
        info.program.id.toString() === formData.program &&
        info.admission_type.id.toString() === formData.admission_type &&
        info.field_of_study.id.toString() === formData.field_of_study &&
        info.study_program.id.toString() === formData.study_program
      );

      console.log('Duplicate record found:', duplicateInfo);
      toast({
        title: 'Duplicate Record Found',
        description: `This combination already exists: ${duplicateInfo?.college.name} - ${duplicateInfo?.department.name} - ${duplicateInfo?.program.program_name} - ${duplicateInfo?.admission_type.name} - ${duplicateInfo?.field_of_study.name} - ${duplicateInfo?.study_program.name}`,
        variant: 'destructive',
      });
      return;
    }
    console.log('No duplicate records found');

    setFormLoading(true);

    // Convert string IDs to integers and validate
    const parseAndValidate = (value: string, fieldName: string): number => {
      const parsed = parseInt(value);
      if (isNaN(parsed) || parsed <= 0) {
        throw new Error(`Invalid ${fieldName}: ${value}`);
      }
      return parsed;
    };

    let dataToSend: any = null;

    try {
      try {
        // Prepare the data to send - ensure required fields are valid integers
        // Backend serializer expects foreign key fields with _id suffix
        dataToSend = {
          college_id: parseAndValidate(formData.college, 'college'),
          department_id: parseAndValidate(formData.department, 'department'),
          program_id: parseAndValidate(formData.program, 'program'),
          admission_type_id: parseAndValidate(formData.admission_type, 'admission_type'),
          field_of_study_id: parseAndValidate(formData.field_of_study, 'field_of_study'),
          study_program_id: parseAndValidate(formData.study_program, 'study_program'),
          duration: formData.duration || '',
          spacial_case: formData.spacial_case || '',
          status: formData.status
        };

        console.log('Form data being sent:', dataToSend);
        console.log('Current form state:', formData);

        // Import the API instance
        const { default: api } = await import('@/services/api');
        console.log('Making API call to create application information...');

        const response = await api.post('/application-information/', dataToSend);
        console.log('API response:', response);
        console.log('API response status:', response.status);
        console.log('API response data:', response.data);

        // Check if the response indicates an error
        if (response.status >= 400) {
          throw new Error(`API Error: ${response.status} - ${JSON.stringify(response.data)}`);
        }

        toast({
          title: 'Success',
          description: 'Application information created successfully.',
        });

        console.log('Refreshing data...');
        // Refresh the data
        await fetchApplicationInfo();
        console.log('Data refreshed successfully');
        console.log('Closing dialog and resetting form...');
        setIsAddDialogOpen(false);
        resetFormData();
        console.log('Dialog closed and form reset');

      } catch (parseError: any) {
        console.error('Data validation error:', parseError);
        toast({
          title: 'Validation Error',
          description: `Please check your form data: ${parseError.message}`,
          variant: 'destructive',
        });
        return;
      }
    } catch (error: any) {
      console.error('=== ERROR CREATING APPLICATION INFORMATION ===');
      console.error('Full error object:', error);
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      console.error('Error response data:', error.response?.data);
      console.error('Error response status:', error.response?.status);

      // Log the detailed error information for 400 errors
      if (error.response?.status === 400) {
        console.error('=== 400 BAD REQUEST DETAILS ===');
        console.error('Request data that was sent:', dataToSend);
        console.error('Backend validation errors:', error.response.data);
      }

      // Handle different types of errors
      if (error.response && error.response.data) {
        // API error with response data
        const errorMessage = typeof error.response.data === 'string'
          ? error.response.data
          : Object.entries(error.response.data)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');

        console.error('Parsed error message:', errorMessage);

        toast({
          title: 'Error',
          description: `Failed to create application information: ${errorMessage}`,
          variant: 'destructive',
        });
      } else {
        // Generic error
        console.error('Generic error - no response data');
        toast({
          title: 'Error',
          description: 'Failed to create application information. Please try again.',
          variant: 'destructive',
        });
      }
    } finally {
      console.log('Setting form loading to false');
      setFormLoading(false);
    }
  };

  // Update application information
  const handleUpdate = async () => {
    if (!user?.is_staff || !selectedInfo) {
      toast({
        title: 'Access Denied',
        description: 'Only staff users can update application information.',
        variant: 'destructive',
      });
      return;
    }

    // Validate form data
    if (!validateForm()) {
      return;
    }

    // Check for duplicate application information (excluding current record)
    console.log('Checking for duplicate records during update...');
    const isDuplicate = applicationInfo.some(info =>
      info.id !== selectedInfo.id && // Exclude current record being edited
      info.college.id.toString() === formData.college &&
      info.department.id.toString() === formData.department &&
      info.program.id.toString() === formData.program &&
      info.admission_type.id.toString() === formData.admission_type &&
      info.field_of_study.id.toString() === formData.field_of_study &&
      info.study_program.id.toString() === formData.study_program
    );

    if (isDuplicate) {
      const duplicateInfo = applicationInfo.find(info =>
        info.id !== selectedInfo.id &&
        info.college.id.toString() === formData.college &&
        info.department.id.toString() === formData.department &&
        info.program.id.toString() === formData.program &&
        info.admission_type.id.toString() === formData.admission_type &&
        info.field_of_study.id.toString() === formData.field_of_study &&
        info.study_program.id.toString() === formData.study_program
      );

      console.log('Duplicate record found during update:', duplicateInfo);
      toast({
        title: 'Duplicate Record Found',
        description: `This combination already exists: ${duplicateInfo?.college.name} - ${duplicateInfo?.department.name} - ${duplicateInfo?.program.program_name} - ${duplicateInfo?.admission_type.name} - ${duplicateInfo?.field_of_study.name} - ${duplicateInfo?.study_program.name}`,
        variant: 'destructive',
      });
      return;
    }
    console.log('No duplicate records found during update');

    setFormLoading(true);
    try {
      // Import the API instance
      const { default: api } = await import('@/services/api');
      // Convert string IDs to integers and validate
      const parseAndValidate = (value: string, fieldName: string): number => {
        const parsed = parseInt(value);
        if (isNaN(parsed) || parsed <= 0) {
          throw new Error(`Invalid ${fieldName}: ${value}`);
        }
        return parsed;
      };

      try {
        // Prepare the data to send - ensure required fields are valid integers
        // Backend serializer expects foreign key fields with _id suffix
        const dataToSend = {
          college_id: parseAndValidate(formData.college, 'college'),
          department_id: parseAndValidate(formData.department, 'department'),
          program_id: parseAndValidate(formData.program, 'program'),
          admission_type_id: parseAndValidate(formData.admission_type, 'admission_type'),
          field_of_study_id: parseAndValidate(formData.field_of_study, 'field_of_study'),
          study_program_id: parseAndValidate(formData.study_program, 'study_program'),
          duration: formData.duration || '',
          spacial_case: formData.spacial_case || '',
          status: formData.status
        };

        console.log('Update data being sent:', dataToSend);
        console.log('Current form state:', formData);

        await api.put(`/application-information/${selectedInfo.id}/`, dataToSend);
      } catch (parseError: any) {
        console.error('Data validation error:', parseError);
        toast({
          title: 'Validation Error',
          description: `Please check your form data: ${parseError.message}`,
          variant: 'destructive',
        });
        return;
      }

      toast({
        title: 'Success',
        description: 'Application information updated successfully.',
      });

      // Refresh the data
      await fetchApplicationInfo();
      setIsEditDialogOpen(false);
      setSelectedInfo(null);
      resetFormData();
    } catch (error: any) {
      console.error('Error updating application information:', error);

      // Handle different types of errors
      if (error.response && error.response.data) {
        // API error with response data
        const errorMessage = typeof error.response.data === 'string'
          ? error.response.data
          : Object.entries(error.response.data)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');

        toast({
          title: 'Error',
          description: `Failed to update application information: ${errorMessage}`,
          variant: 'destructive',
        });
      } else {
        // Generic error
        toast({
          title: 'Error',
          description: 'Failed to update application information. Please try again.',
          variant: 'destructive',
        });
      }
    } finally {
      setFormLoading(false);
    }
  };

  // Delete application information
  const handleDelete = async () => {
    if (!user?.is_staff || !selectedInfo) {
      toast({
        title: 'Access Denied',
        description: 'Only staff users can delete application information.',
        variant: 'destructive',
      });
      return;
    }

    setFormLoading(true);
    try {
      // Import the API instance
      const { default: api } = await import('@/services/api');
      await api.delete(`/application-information/${selectedInfo.id}/`);

      toast({
        title: 'Success',
        description: 'Application information deleted successfully.',
      });

      // Refresh the data
      await fetchApplicationInfo();
      setIsDeleteDialogOpen(false);
      setSelectedInfo(null);
    } catch (error) {
      console.error('Error deleting application information:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete application information. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setFormLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Pencil className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Application Information Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete application information for the application portal
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              {/* Add button - only visible to staff users */}
              {user?.is_staff && (
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
                      onClick={() => {
                        resetFormData();
                        setFilteredDepartments([{ id: 0, name: 'Select Department' }]);
                        setFilteredStudyFields([]);
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Application Information
                    </Button>
                  </DialogTrigger>
                </Dialog>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Loading state */}
          {loading && (
            <div className="flex justify-center items-center h-64">
              <div className="flex flex-col justify-center items-center space-y-3">
                <div className="bg-blue-100 p-3 rounded-full">
                  <Loader2 className="h-8 w-8 text-[#1a73c0] animate-spin" />
                </div>
                <div className="text-[#1a73c0] font-medium">Loading application information...</div>
                <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
              </div>
            </div>
          )}

          {!loading && (
            <>
              {/* Filter section */}
              <div className="mb-6">
                {/* Filter Options Section */}
                <div className="p-5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                    <div className="flex items-center">
                      <div className="bg-[#1a73c0] p-1.5 rounded-md shadow-sm mr-3">
                        <Filter className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-base font-medium text-[#1a73c0]">Filter Options</h3>
                        <p className="text-xs text-gray-500 mt-0.5">Refine your search with these filters</p>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2 md:mt-0">
                      {/* Export buttons */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={exportToExcel}
                        className="border-green-200 text-green-700 hover:bg-green-50 hover:text-green-800 transition-all duration-200"
                        disabled={applicationInfo.length === 0}
                      >
                        <FileSpreadsheet className="h-3.5 w-3.5 mr-1.5" />
                        Export Excel
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={exportToPDF}
                        className="border-red-200 text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200"
                        disabled={applicationInfo.length === 0}
                      >
                        <Download className="h-3.5 w-3.5 mr-1.5" />
                        Export PDF
                      </Button>

                      {/* Reset Filters button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleReset}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50 transition-all"
                      >
                        <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                        Reset Filters
                      </Button>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-blue-200 shadow-sm">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="space-y-2 group">
                        <Label htmlFor="college-filter" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                          <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                            <Building className="h-4 w-4 text-blue-500" />
                          </div>
                          College
                        </Label>
                        <Select
                          value={selectedCollege}
                          onValueChange={async (value) => {
                            setSelectedCollege(value);
                            await fetchFilterDepartmentsByCollege(value);
                          }}
                        >
                          <SelectTrigger
                            id="college-filter"
                            className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                          >
                            <SelectValue placeholder="All Colleges" />
                          </SelectTrigger>
                          <SelectContent>
                            {colleges.map((college) => (
                              <SelectItem key={college.id} value={college.id.toString()}>
                                {college.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2 group">
                        <Label htmlFor="department-filter" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                          <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                            <Building className="h-4 w-4 text-blue-500" />
                          </div>
                          Department
                        </Label>
                        <Select
                          value={selectedDepartment}
                          onValueChange={(value) => setSelectedDepartment(value)}
                          disabled={selectedCollege === '0'}
                        >
                          <SelectTrigger
                            id="department-filter"
                            className={`border-blue-200 focus:ring-blue-400 shadow-sm transition-all rounded-md h-9 text-sm ${
                              selectedCollege === '0' ? "opacity-70 cursor-not-allowed" : "hover:border-blue-300"
                            }`}
                          >
                            <SelectValue placeholder="All Departments" />
                          </SelectTrigger>
                          <SelectContent>
                            {(filteredFilterDepartments.length > 0 ? filteredFilterDepartments : departments).map((department) => (
                              <SelectItem key={department.id} value={department.id.toString()}>
                                {department.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2 group">
                        <Label htmlFor="program-filter" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                          <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                            <GraduationCap className="h-4 w-4 text-blue-500" />
                          </div>
                          Program
                        </Label>
                        <Select
                          value={selectedProgram}
                          onValueChange={(value) => setSelectedProgram(value)}
                        >
                          <SelectTrigger
                            id="program-filter"
                            className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                          >
                            <SelectValue placeholder="All Programs" />
                          </SelectTrigger>
                          <SelectContent>
                            {programs.map((program) => (
                              <SelectItem key={program.id} value={program.id.toString()}>
                                {program.program_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2 group">
                        <Label htmlFor="admission-type-filter" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                          <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                            <CheckCircle className="h-4 w-4 text-blue-500" />
                          </div>
                          Admission Type
                        </Label>
                        <Select
                          value={selectedAdmissionType}
                          onValueChange={(value) => setSelectedAdmissionType(value)}
                        >
                          <SelectTrigger
                            id="admission-type-filter"
                            className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                          >
                            <SelectValue placeholder="All Admission Types" />
                          </SelectTrigger>
                          <SelectContent>
                            {admissionTypes.map((type) => (
                              <SelectItem key={type.id} value={type.id.toString()}>
                                {type.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {filterLoading && (
                      <div className="mt-4 flex justify-end">
                        <div className="flex items-center text-sm text-blue-600">
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Updating results...
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Application Information Table */}
              <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                      <TableRow>
                        <TableHead className="text-[#1a73c0] font-medium">College</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Department</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Program</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Admission Type</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Field of Study</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Study Program</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Duration</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Special Case</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Status</TableHead>
                        {user?.is_staff && <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {currentItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={user?.is_staff ? 10 : 9} className="text-center py-12">
                            <div className="flex flex-col justify-center items-center space-y-3">
                              <div className="bg-gray-100 p-3 rounded-full">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <div className="text-gray-700 font-medium">No application information found</div>
                              <div className="text-sm text-gray-500 max-w-sm text-center">
                                Try adjusting your filter criteria or add new application information.
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={handleReset}
                                className="mt-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                              >
                                Reset Filters
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        currentItems.map((info) => (
                          <TableRow key={info.id} className="hover:bg-blue-50 transition-colors">
                            <TableCell className="font-medium text-[#1a73c0]">{info.college.name}</TableCell>
                            <TableCell>{info.department.name}</TableCell>
                            <TableCell>{info.program.program_name}</TableCell>
                            <TableCell>{info.admission_type.name}</TableCell>
                            <TableCell>{info.field_of_study.name}</TableCell>
                            <TableCell>{info.study_program.name}</TableCell>
                            <TableCell>{info.duration || '-'}</TableCell>
                            <TableCell>{info.spacial_case || '-'}</TableCell>
                            <TableCell>
                              {info.status ? (
                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200 shadow-sm">
                                  <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-green-600"></span>
                                  Active
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200 shadow-sm">
                                  <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-red-600"></span>
                                  Inactive
                                </span>
                              )}
                            </TableCell>
                            {user?.is_staff && (
                              <TableCell className="text-right">
                                <div className="flex justify-end space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleStatusToggle(info)}
                                    className={cn(
                                      "h-8 w-8 p-0",
                                      info.status
                                        ? "hover:bg-orange-50 hover:text-orange-600"
                                        : "hover:bg-green-50 hover:text-green-600"
                                    )}
                                    title={info.status ? "Deactivate application information" : "Activate application information"}
                                  >
                                    {info.status ? (
                                      <ToggleRight className="h-4 w-4" />
                                    ) : (
                                      <ToggleLeft className="h-4 w-4" />
                                    )}
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleEditClick(info)}
                                    title="Edit"
                                    className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                  >
                                    <Pencil className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteClick(info)}
                                    className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                                    title="Delete"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            )}
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>


            </>
          )}
        </CardContent>
        <CardFooter>
          {applicationInfo.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
            <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
              <span className="text-sm font-medium text-[#1a73c0]">Show</span>
              <select
                value={itemsPerPage}
                onChange={(e) => handleItemsPerPageChange(e.target.value)}
                className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                id="page-size"
                name="page-size"
              >
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
              <span className="text-sm font-medium text-[#1a73c0]">per page</span>
              <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                <span className="font-medium text-[#1a73c0]">
                  {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, applicationInfo.length)}
                </span> of <span className="font-medium text-[#1a73c0]">{applicationInfo.length}</span> records
              </div>
            </div>

            <div className="flex items-center">
              <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                    currentPage === 1
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="First Page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                    currentPage === 1
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="Previous Page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Dynamic page number buttons with ellipsis for large page counts */}
                {totalPages <= 7 ? (
                  // If we have 7 or fewer pages, show all page numbers
                  Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                    <Button
                      key={number}
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePageChange(number)}
                      className={cn(
                        "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                        currentPage === number
                          ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                          : "bg-white text-gray-700 hover:bg-blue-50"
                      )}
                    >
                      {number}
                    </Button>
                  ))
                ) : (
                  // For more than 7 pages, show a condensed version with ellipsis
                  <>
                    {/* Always show first page */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePageChange(1)}
                      className={cn(
                        "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                        currentPage === 1
                          ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                          : "bg-white text-gray-700 hover:bg-blue-50"
                      )}
                    >
                      1
                    </Button>

                    {/* Show ellipsis if not showing first few pages */}
                    {currentPage > 3 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled
                        className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                      >
                        ...
                      </Button>
                    )}

                    {/* Show current page and adjacent pages */}
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(number =>
                        number > 1 &&
                        number < totalPages &&
                        (
                          number === currentPage - 1 ||
                          number === currentPage ||
                          number === currentPage + 1 ||
                          (currentPage <= 3 && number <= 4) ||
                          (currentPage >= totalPages - 2 && number >= totalPages - 3)
                        )
                      )
                      .map(number => (
                        <Button
                          key={number}
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(number)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === number
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {number}
                        </Button>
                      ))
                    }

                    {/* Show ellipsis if not showing last few pages */}
                    {currentPage < totalPages - 2 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled
                        className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                      >
                        ...
                      </Button>
                    )}

                    {/* Always show last page */}
                    {totalPages > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(totalPages)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === totalPages
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {totalPages}
                      </Button>
                    )}
                  </>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                    currentPage >= totalPages
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="Next Page"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage >= totalPages}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none",
                    currentPage >= totalPages
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="Last Page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          )}
        </CardFooter>
      </Card>

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-gondar">Add New Application Information</DialogTitle>
            <DialogDescription>
              Create a new application information record. Fields marked with * are required.
            </DialogDescription>
          </DialogHeader>

          <div className="bg-gray-50 p-4 rounded-md mb-4 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 mb-2">PROGRAM DETAILS</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="college" className="flex items-center">
                  College <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="college"
                  value={formData.college}
                  onValueChange={(value) => {
                    setFormData(prev => ({ ...prev, college: value, department: '' }));
                    fetchDepartmentsByCollege(value);
                  }}
                >
                  <SelectTrigger id="college" className={!formData.college ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select College" />
                  </SelectTrigger>
                  <SelectContent>
                    {colleges.filter(c => c.id !== 0).map((college) => (
                      <SelectItem key={college.id} value={college.id.toString()}>
                        {college.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="department" className="flex items-center">
                  Department <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="department"
                  value={formData.department}
                  onValueChange={(value) => {
                    setFormData(prev => ({ ...prev, department: value, field_of_study: '' }));
                    filterStudyFieldsByDepartment(value);
                  }}
                  disabled={!formData.college || filteredDepartments.length <= 1}
                >
                  <SelectTrigger id="department" className={!formData.department ? "border-red-300" : ""}>
                    <SelectValue placeholder={!formData.college ? "Select College First" : "Select Department"} />
                  </SelectTrigger>
                  <SelectContent>
                    {(filteredDepartments.length > 0 ? filteredDepartments : departments)
                      .filter(d => d.id !== 0)
                      .map((department) => (
                        <SelectItem key={department.id} value={department.id.toString()}>
                          {department.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="program" className="flex items-center">
                  Program <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="program"
                  value={formData.program}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, program: value }))}
                >
                  <SelectTrigger id="program" className={!formData.program ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select Program" />
                  </SelectTrigger>
                  <SelectContent>
                    {programs.filter(p => p.id !== 0).map((program) => (
                      <SelectItem key={program.id} value={program.id.toString()}>
                        {program.program_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="admission_type" className="flex items-center">
                  Admission Type <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="admission_type"
                  value={formData.admission_type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, admission_type: value }))}
                >
                  <SelectTrigger id="admission_type" className={!formData.admission_type ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select Admission Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {admissionTypes.filter(t => t.id !== 0).map((type) => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-md mb-4 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 mb-2">STUDY DETAILS</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="field_of_study" className="flex items-center">
                  Field of Study <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="field_of_study"
                  value={formData.field_of_study}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, field_of_study: value }))}
                  disabled={!formData.department || filteredStudyFields.length === 0}
                >
                  <SelectTrigger id="field_of_study" className={!formData.field_of_study ? "border-red-300" : ""}>
                    <SelectValue placeholder={!formData.department ? "Select Department First" : "Select Field of Study"}>
                      {formData.field_of_study && filteredStudyFields.find(f => f.id.toString() === formData.field_of_study)?.field_of_study}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {filteredStudyFields.map((field) => (
                      <SelectItem key={field.id} value={field.id.toString()}>
                        {field.field_of_study}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500">
                  {!formData.department
                    ? "Select a department to see available fields of study"
                    : filteredStudyFields.length === 0
                      ? "No fields of study available for this department"
                      : `${filteredStudyFields.length} field(s) available for this department`}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="study_program" className="flex items-center">
                  Study Program <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="study_program"
                  value={formData.study_program}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, study_program: value }))}
                >
                  <SelectTrigger id="study_program" className={!formData.study_program ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select Study Program">
                      {formData.study_program && studyPrograms.find(p => p.id.toString() === formData.study_program)?.program_name}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {studyPrograms.map((program) => (
                      <SelectItem key={program.id} value={program.id.toString()}>
                        {program.program_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration">
                  Duration
                </Label>
                <Input
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleFormChange}
                  placeholder="e.g., 4 years"
                />
                <p className="text-xs text-gray-500">Specify the program duration</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="spacial_case">
                  Special Case
                </Label>
                <Input
                  id="spacial_case"
                  name="spacial_case"
                  value={formData.spacial_case}
                  onChange={handleFormChange}
                  placeholder="Any special case information"
                />
                <p className="text-xs text-gray-500">Optional special case details</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-md mb-4 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 mb-2">STATUS SETTINGS</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="status"
                  checked={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300 text-gondar focus:ring-gondar"
                />
                <Label htmlFor="status" className="text-sm font-medium text-gray-700 cursor-pointer">
                  Active Status
                </Label>
              </div>
              <p className="text-xs text-gray-500 ml-7">
                {formData.status
                  ? "This application information will be available for selection in applications."
                  : "This application information will be hidden and not available for selection."}
              </p>
            </div>
          </div>

          <DialogFooter className="flex justify-between items-center pt-2">
            <p className="text-xs text-gray-500">
              <span className="text-red-500">*</span> Required fields
            </p>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleCreate}
                disabled={formLoading}
                className="bg-gondar hover:bg-gondar-dark"
              >
                {formLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Create
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-gondar">Edit Application Information</DialogTitle>
            <DialogDescription>
              Update the application information details. Fields marked with * are required.
            </DialogDescription>
          </DialogHeader>

          <div className="bg-gray-50 p-4 rounded-md mb-4 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 mb-2">PROGRAM DETAILS</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-college" className="flex items-center">
                  College <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="college"
                  value={formData.college}
                  onValueChange={(value) => {
                    setFormData(prev => ({ ...prev, college: value, department: '' }));
                    fetchDepartmentsByCollege(value);
                  }}
                >
                  <SelectTrigger id="edit-college" className={!formData.college ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select College" />
                  </SelectTrigger>
                  <SelectContent>
                    {colleges.filter(c => c.id !== 0).map((college) => (
                      <SelectItem key={college.id} value={college.id.toString()}>
                        {college.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-department" className="flex items-center">
                  Department <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="department"
                  value={formData.department}
                  onValueChange={(value) => {
                    setFormData(prev => ({ ...prev, department: value, field_of_study: '' }));
                    filterStudyFieldsByDepartment(value);
                  }}
                  disabled={!formData.college || filteredDepartments.length <= 1}
                >
                  <SelectTrigger id="edit-department" className={!formData.department ? "border-red-300" : ""}>
                    <SelectValue placeholder={!formData.college ? "Select College First" : "Select Department"} />
                  </SelectTrigger>
                  <SelectContent>
                    {(filteredDepartments.length > 0 ? filteredDepartments : departments)
                      .filter(d => d.id !== 0)
                      .map((department) => (
                        <SelectItem key={department.id} value={department.id.toString()}>
                          {department.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-program" className="flex items-center">
                  Program <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="program"
                  value={formData.program}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, program: value }))}
                >
                  <SelectTrigger id="edit-program" className={!formData.program ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select Program" />
                  </SelectTrigger>
                  <SelectContent>
                    {programs.filter(p => p.id !== 0).map((program) => (
                      <SelectItem key={program.id} value={program.id.toString()}>
                        {program.program_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-admission_type" className="flex items-center">
                  Admission Type <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="admission_type"
                  value={formData.admission_type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, admission_type: value }))}
                >
                  <SelectTrigger id="edit-admission_type" className={!formData.admission_type ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select Admission Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {admissionTypes.filter(t => t.id !== 0).map((type) => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-md mb-4 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 mb-2">STUDY DETAILS</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-field_of_study" className="flex items-center">
                  Field of Study <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="field_of_study"
                  value={formData.field_of_study}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, field_of_study: value }))}
                  disabled={!formData.department || filteredStudyFields.length === 0}
                >
                  <SelectTrigger id="edit-field_of_study" className={!formData.field_of_study ? "border-red-300" : ""}>
                    <SelectValue placeholder={!formData.department ? "Select Department First" : "Select Field of Study"}>
                      {formData.field_of_study && filteredStudyFields.find(f => f.id.toString() === formData.field_of_study)?.field_of_study}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {filteredStudyFields.map((field) => (
                      <SelectItem key={field.id} value={field.id.toString()}>
                        {field.field_of_study}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500">
                  {!formData.department
                    ? "Select a department to see available fields of study"
                    : filteredStudyFields.length === 0
                      ? "No fields of study available for this department"
                      : `${filteredStudyFields.length} field(s) available for this department`}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-study_program" className="flex items-center">
                  Study Program <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  name="study_program"
                  value={formData.study_program}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, study_program: value }))}
                >
                  <SelectTrigger id="edit-study_program" className={!formData.study_program ? "border-red-300" : ""}>
                    <SelectValue placeholder="Select Study Program">
                      {formData.study_program && studyPrograms.find(p => p.id.toString() === formData.study_program)?.program_name}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {studyPrograms.map((program) => (
                      <SelectItem key={program.id} value={program.id.toString()}>
                        {program.program_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-duration">
                  Duration
                </Label>
                <Input
                  id="edit-duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleFormChange}
                  placeholder="e.g., 4 years"
                />
                <p className="text-xs text-gray-500">Specify the program duration</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-spacial_case">
                  Special Case
                </Label>
                <Input
                  id="edit-spacial_case"
                  name="spacial_case"
                  value={formData.spacial_case}
                  onChange={handleFormChange}
                  placeholder="Any special case information"
                />
                <p className="text-xs text-gray-500">Optional special case details</p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-md mb-4 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 mb-2">STATUS SETTINGS</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="edit-status"
                  checked={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.checked }))}
                  className="h-4 w-4 rounded border-gray-300 text-gondar focus:ring-gondar"
                />
                <Label htmlFor="edit-status" className="text-sm font-medium text-gray-700 cursor-pointer">
                  Active Status
                </Label>
              </div>
              <p className="text-xs text-gray-500 ml-7">
                {formData.status
                  ? "This application information will be available for selection in applications."
                  : "This application information will be hidden and not available for selection."}
              </p>
            </div>
          </div>

          <DialogFooter className="flex justify-between items-center pt-2">
            <p className="text-xs text-gray-500">
              <span className="text-red-500">*</span> Required fields
            </p>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleUpdate}
                disabled={formLoading}
                className="bg-gondar hover:bg-gondar-dark"
              >
                {formLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Update
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl font-bold text-red-600">
              Delete Application Information
            </AlertDialogTitle>
            <AlertDialogDescription className="py-4">
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                <p className="text-red-800">
                  This action <span className="font-bold">cannot be undone</span>. This will permanently delete the application information
                  and remove it from our servers.
                </p>
              </div>

              {selectedInfo && (
                <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Information to be deleted:</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li><span className="font-medium">College:</span> {selectedInfo.college.name}</li>
                    <li><span className="font-medium">Department:</span> {selectedInfo.department.name}</li>
                    <li><span className="font-medium">Program:</span> {selectedInfo.program.program_name}</li>
                    <li><span className="font-medium">Admission Type:</span> {selectedInfo.admission_type.name}</li>
                  </ul>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-gray-300">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={formLoading}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
            >
              {formLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ApplicationInformationView;
