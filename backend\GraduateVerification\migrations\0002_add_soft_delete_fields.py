# Generated migration for soft delete functionality

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('GraduateVerification', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='graduatestudent',
            name='is_deleted',
            field=models.BooleanField(
                default=False,
                db_index=True,
                help_text='Indicates if the record is soft deleted'
            ),
        ),
        migrations.AddField(
            model_name='graduatestudent',
            name='deleted_at',
            field=models.DateTimeField(
                blank=True,
                null=True,
                help_text='Timestamp when the record was soft deleted'
            ),
        ),
        migrations.AddField(
            model_name='graduatestudent',
            name='deleted_by',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='deleted_graduate_students',
                to=settings.AUTH_USER_MODEL,
                help_text='User who soft deleted this record'
            ),
        ),
    ]
