import os
import json
import django
import sys

# Add the project root directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models
from GraduateVerification.models import (
    VerificationCollege,
    VerificationDepartment,
    VerificationFieldOfStudy,
    AdmissionClassification,
    VerificationProgram,
    GraduateStudent
)

def load_graduates_one_by_one():
    print("Loading graduates one by one...")
    with open('seed_data/verification_graduates.json', 'r', encoding='utf-8') as f:
        graduates_data = json.load(f)

    for i, graduate_data in enumerate(graduates_data):
        try:
            # Get related objects
            college = VerificationCollege.objects.get(code=graduate_data['college_code'])
            department = VerificationDepartment.objects.get(code=graduate_data['department_code'])
            field_of_study = VerificationFieldOfStudy.objects.get(name=graduate_data['field_of_study'])
            # Convert program code to uppercase to match database
            program_code = graduate_data['program_code'].upper()
            program = VerificationProgram.objects.get(code=program_code)
            admission_classification = AdmissionClassification.objects.get(name=graduate_data['admission_classification'])

            # Create graduate
            GraduateStudent.objects.get_or_create(
                student_id=graduate_data['student_id'],
                defaults={
                    'first_name': graduate_data['first_name'],
                    'middle_name': graduate_data['middle_name'],
                    'last_name': graduate_data['last_name'],
                    'year_of_graduation': graduate_data['year_of_graduation'],
                    'gpa': graduate_data['gpa'],
                    'gender': graduate_data['gender'],
                    'college': college,
                    'department': department,
                    'field_of_study': field_of_study,
                    'program': program,
                    'admission_classification': admission_classification
                }
            )
            print(f"Loaded graduate {i+1}/{len(graduates_data)}: {graduate_data['first_name']} {graduate_data['last_name']}")
        except Exception as e:
            print(f"Error loading graduate {i+1}/{len(graduates_data)}: {graduate_data['first_name']} {graduate_data['last_name']}")
            print(f"Error details: {e}")
            print(f"Graduate data: {graduate_data}")
            # Continue with the next graduate
            continue

    print(f"Finished loading graduates")

if __name__ == '__main__':
    load_graduates_one_by_one()
