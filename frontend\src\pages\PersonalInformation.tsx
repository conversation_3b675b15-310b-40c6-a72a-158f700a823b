import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm, FieldErrors } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Calendar, Phone, School, Award, User, Info, BookOpen, Mail, Loader2, AlertCircle, AlertTriangle, CheckCircle2 } from 'lucide-react';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { applicationAPI } from '@/services/api';
import notificationService from '@/services/notificationService';

// Define the schema for the form
const personalInfoSchema = z.object({
  grandfather_name: z.string().min(2, 'Grandfather name must be at least 2 characters').max(100, 'Grandfather name must be at most 100 characters'),
  gender: z.string().min(1, 'Please select your gender'),
  dob: z.string().refine(date => {
    const today = new Date();
    const dob = new Date(date);
    return dob < today;
  }, { message: 'Date of birth must be in the past' }),
  mobile: z.string().min(10, 'Mobile number must be at least 10 digits').max(15, 'Mobile number must be at most 15 digits'),
  program_level: z.string().min(1, 'Please select your program level'),

  // Undergraduate fields - conditionally required based on program level
  ug_university: z.string().max(255, 'University name must be at most 255 characters')
    .optional()
    .superRefine((val, ctx) => {
      // Get the form values from the context
      const formData = ctx.path[0] as any;
      const programLevel = formData?.program_level;

      // Required for MSC/MBA and PHD
      if ((programLevel === 'MSC/MBA' || programLevel === 'PHD') && (!val || val.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Undergraduate university is required for MSC/MBA and PHD applications',
        });
      }
    }),

  ug_field_of_study: z.string().max(255, 'Field of study must be at most 255 characters')
    .optional()
    .superRefine((val, ctx) => {
      // Get the form values from the context
      const formData = ctx.path[0] as any;
      const programLevel = formData?.program_level;

      // Required for MSC/MBA and PHD
      if ((programLevel === 'MSC/MBA' || programLevel === 'PHD') && (!val || val.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Undergraduate field of study is required for MSC/MBA and PHD applications',
        });
      }
    }),

  ug_CGPA: z.string()
    .optional()
    .superRefine((val, ctx) => {
      // Get the form values from the context
      const formData = ctx.path[0] as any;
      const programLevel = formData?.program_level;

      // Required for MSC/MBA and PHD
      if ((programLevel === 'MSC/MBA' || programLevel === 'PHD') && (!val || val.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Undergraduate CGPA is required for MSC/MBA and PHD applications',
        });
        return;
      }

      // Validate CGPA format if provided
      if (val && val.trim() !== '') {
        const numCgpa = parseFloat(val);
        if (isNaN(numCgpa) || numCgpa < 2.0 || numCgpa > 4.0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'CGPA must be between 2.0 and 4.0',
          });
        }
      }
    }),

  // Postgraduate fields - conditionally required for PHD
  pg_university: z.string().max(255, 'University name must be at most 255 characters')
    .optional()
    .superRefine((val, ctx) => {
      // Get the form values from the context
      const formData = ctx.path[0] as any;
      const programLevel = formData?.program_level;

      // Required for PHD
      if (programLevel === 'PHD' && (!val || val.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Postgraduate university is required for PHD applications',
        });
      }
    }),

  pg_field_of_study: z.string().max(255, 'Field of study must be at most 255 characters')
    .optional()
    .superRefine((val, ctx) => {
      // Get the form values from the context
      const formData = ctx.path[0] as any;
      const programLevel = formData?.program_level;

      // Required for PHD
      if (programLevel === 'PHD' && (!val || val.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Postgraduate field of study is required for PHD applications',
        });
      }
    }),

  pg_CGPA: z.string()
    .optional()
    .superRefine((val, ctx) => {
      // Get the form values from the context
      const formData = ctx.path[0] as any;
      const programLevel = formData?.program_level;

      // Required for PHD
      if (programLevel === 'PHD' && (!val || val.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Postgraduate CGPA is required for PHD applications',
        });
        return;
      }

      // Validate CGPA format if provided
      if (val && val.trim() !== '') {
        const numCgpa = parseFloat(val);
        if (isNaN(numCgpa) || numCgpa < 2.0 || numCgpa > 4.0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'CGPA must be between 2.0 and 4.0',
          });
        }
      }
    }),
});

type PersonalInfoFormValues = z.infer<typeof personalInfoSchema>;

// Component to display validation errors
const ValidationErrorSummary = ({ errors }: { errors: FieldErrors<PersonalInfoFormValues> }) => {
  // Check if there are any errors
  const hasErrors = Object.keys(errors).length > 0;

  if (!hasErrors) return null;

  // Get all error messages
  const errorMessages: string[] = [];
  Object.entries(errors).forEach(([field, error]) => {
    if (error?.message) {
      errorMessages.push(`${field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}: ${error.message}`);
    }
  });

  return (
    <Alert variant="destructive" className="mb-6 bg-red-50 border-red-200">
      <AlertCircle className="h-5 w-5" />
      <AlertTitle className="font-semibold text-red-800">Validation Errors</AlertTitle>
      <AlertDescription className="mt-2">
        <ul className="list-disc pl-5 space-y-1 text-red-700">
          {errorMessages.map((message, index) => (
            <li key={index}>{message}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
};

const PersonalInformation = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [existingPersonalInfo, setExistingPersonalInfo] = useState<any>(null);
  const [showValidationSummary, setShowValidationSummary] = useState(false);
  const navigate = useNavigate();

  // Initialize form with default values
  const form = useForm<PersonalInfoFormValues>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      grandfather_name: '',
      gender: '',
      dob: '',
      mobile: '',
      program_level: '',
      ug_university: '',
      ug_field_of_study: '',
      ug_CGPA: '',
      pg_university: '',
      pg_field_of_study: '',
      pg_CGPA: '',
    },
  });

  // Fetch existing personal information if available
  useEffect(() => {
    const fetchPersonalInfo = async () => {
      try {
        const response = await applicationAPI.getCurrentApplicantInfo();
        console.log('Fetched applicant info:', response.data);
        if (response.data && response.data.length > 0) {
          const personalInfo = response.data[0];
          setExistingPersonalInfo(personalInfo);
          console.log('Existing personal info:', personalInfo);

          // Format the date to YYYY-MM-DD format for the form
          const formatDate = (dateString: string) => {
            if (!dateString) return '';
            try {
              const date = new Date(dateString);
              return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
            } catch (error) {
              console.error('Error formatting date:', error);
              return '';
            }
          };

          // Update form values with existing data
          form.reset({
            grandfather_name: personalInfo.grandfather_name || '',
            gender: personalInfo.gender || '',
            dob: formatDate(personalInfo.dob) || '',
            mobile: personalInfo.mobile || '',
            program_level: personalInfo.program_level || 'MSC/MBA', // Default to MSC/MBA if not set
            ug_university: personalInfo.ug_university || '',
            ug_field_of_study: personalInfo.ug_field_of_study || '',
            ug_CGPA: personalInfo.ug_CGPA ? personalInfo.ug_CGPA.toString() : '',
            pg_university: personalInfo.pg_university || '',
            pg_field_of_study: personalInfo.pg_field_of_study || '',
            pg_CGPA: personalInfo.pg_CGPA ? personalInfo.pg_CGPA.toString() : ''
          });
        }
      } catch (error) {
        console.error('Error fetching personal information:', error);
      }
    };

    fetchPersonalInfo();
  }, [form]);



  const onSubmit = async (data: PersonalInfoFormValues) => {
    setIsLoading(true);
    // Hide validation summary when submitting
    setShowValidationSummary(false);

    try {

      // Format the date to YYYY-MM-DD format
      const formatDate = (dateString: string) => {
        try {
          const date = new Date(dateString);
          return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
        } catch (error) {
          console.error('Error formatting date:', error);
          return dateString; // Return original if parsing fails
        }
      };

      // Create a clean data object without any string booleans
      const formattedData = {
        grandfather_name: data.grandfather_name,
        gender: data.gender,
        dob: formatDate(data.dob),
        mobile: data.mobile,
        program_level: data.program_level,
        ug_university: data.ug_university || null,
        ug_field_of_study: data.ug_field_of_study || null,
        ug_CGPA: data.ug_CGPA ? parseFloat(data.ug_CGPA) : null,
        pg_university: data.pg_university || null,
        pg_field_of_study: data.pg_field_of_study || null,
        pg_CGPA: data.pg_CGPA ? parseFloat(data.pg_CGPA) : null
        // Removed problematic fields
      };

      console.log('Submitting data:', formattedData);

      try {
        // Check if user is authenticated
        const token = localStorage.getItem('token');
        if (!token) {
          toast.error('You are not authenticated. Please log in again.');
          navigate('/login');
          return;
        }

        let response: any;
        // Always use the create endpoint, which will update if a record exists
        console.log('Submitting personal information');
        response = await applicationAPI.submitApplicantInfo(formattedData);
        console.log('Personal information response:', response);

        if (response.status === 200 || response.status === 201) {
          // Show toast notification
          toast.success('Personal information submitted successfully!');

          // Add a notification to the notification center
          notificationService.addPersonalInfoSubmissionNotification(!!existingPersonalInfo);

          // Only navigate to the next step if it's a new submission, not an update
          if (!existingPersonalInfo) {
            navigate('/application/gat');
          }
        } else {
          toast.error('Failed to submit personal information. Please try again.');
        }
      } catch (apiError: any) {
        console.error('API submission error:', apiError);

        // Handle specific error messages
        if (apiError.response?.data) {
          const errorData = apiError.response.data;
          console.error('Error data:', errorData);

          // If the error is a string
          if (typeof errorData === 'string') {
            toast.error(errorData);
          }
          // If the error is an array of strings
          else if (Array.isArray(errorData)) {
            errorData.forEach((err: string) => {
              // Log all errors for debugging
              console.error(`Error message: ${err}`);

              // Special handling for common validation errors
              if (err.includes('"Pending" value must be either True or False') ||
                  err.includes('payment_status') ||
                  err.includes('boolean')) {
                console.error('Validation error detected. Trying to fix...');
                // If updating, try again with a different approach
                if (existingPersonalInfo) {
                  // Create a minimal data object with only essential fields
                  const minimalData = {
                    grandfather_name: data.grandfather_name,
                    gender: data.gender,
                    dob: formatDate(data.dob),
                    mobile: data.mobile,
                    program_level: data.program_level,
                    ug_university: data.ug_university || null,
                    ug_field_of_study: data.ug_field_of_study || null,
                    ug_CGPA: data.ug_CGPA ? parseFloat(data.ug_CGPA) : null
                  };

                  console.log('Retrying with minimal data:', minimalData);
                  applicationAPI.submitApplicantInfo(minimalData)
                    .then(response => {
                      console.log('Fixed submission successful:', response);
                      toast.success('Personal information submitted successfully!');
                    })
                    .catch(retryError => {
                      console.error('Retry also failed:', retryError);
                      toast.error('Could not update your information. Please try again later.');
                    });
                  return; // Skip showing the error toast for this specific error
                }
              }
              toast.error(err);
            });
          }
          // If the error is an object with field-specific errors
          else if (typeof errorData === 'object') {
            // Display each field error
            Object.entries(errorData).forEach(([field, errors]) => {
              if (Array.isArray(errors) && errors.length > 0) {
                toast.error(`${field}: ${errors[0]}`);
              } else if (typeof errors === 'string') {
                toast.error(`${field}: ${errors}`);
              }
            });
          }
        } else {
          toast.error('Failed to save personal information. Please try again.');
        }

      }
    } catch (error: any) {
      // This catch block will handle any errors not caught by the inner try-catch
      console.error('Unhandled error submitting personal information:', error);

      // Only show a generic error if we haven't already shown a specific one
      if (!error.response) {
        toast.error('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <Card className="shadow-2xl border-0 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
              <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                   style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
              <div className="relative z-10 flex items-center">
                <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                  <User className="h-8 w-8 text-[#1a73c0]" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-white">Applicant Details</CardTitle>
                  <CardDescription className="text-blue-100 mt-1">
                    Fill in all required fields marked with an asterisk (*)
                  </CardDescription>
                  {existingPersonalInfo && (
                    <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                      <Info className="h-4 w-4 mr-2" />
                      You are editing your previously submitted information
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-8">

              {/* Show validation error summary if there are errors */}
              {showValidationSummary && <ValidationErrorSummary errors={form.formState.errors} />}

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit, (errors) => {
                  console.error('Form validation errors:', errors);
                  setShowValidationSummary(true);
                  // Scroll to the top of the form to show the error summary
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                  // Show toast notification
                  toast.error('Please fix the validation errors before submitting the form.');
                })} className="space-y-10">
                  {/* Personal Information Section */}
                  <div className="space-y-6">
                    <div className="flex items-center">
                      <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                        <User className="h-5 w-5 text-[#1a73c0]" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-800">Personal Information</h3>
                      <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                      <div className="md:col-span-3">
                        <div className="p-0 rounded-xl mb-8 overflow-hidden shadow-lg">


                          {/* Content area with user details */}
                          <div className="bg-white p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                              <div className="bg-gradient-to-br from-white to-blue-50 p-6 rounded-xl shadow-sm border border-blue-100 hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 group">
                                <div className="flex items-center mb-3">
                                  <div className="bg-[#1a73c0] p-2 rounded-full mr-3 group-hover:bg-[#0d4a8b] transition-colors duration-300">
                                    <User className="h-5 w-5 text-white" />
                                  </div>
                                  <p className="text-sm font-medium text-[#1a73c0] uppercase tracking-wider">First Name</p>
                                </div>
                                <p className="text-2xl font-semibold text-gray-800 pl-2 mt-2">{localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') || '{}').first_name || JSON.parse(localStorage.getItem('user') || '{}').firstName || 'N/A' : 'N/A'}</p>
                                <div className="h-1 w-16 bg-[#1a73c0]/30 rounded-full mt-3 group-hover:w-24 transition-all duration-300"></div>
                              </div>

                              <div className="bg-gradient-to-br from-white to-blue-50 p-6 rounded-xl shadow-sm border border-blue-100 hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 group">
                                <div className="flex items-center mb-3">
                                  <div className="bg-[#1a73c0] p-2 rounded-full mr-3 group-hover:bg-[#0d4a8b] transition-colors duration-300">
                                    <User className="h-5 w-5 text-white" />
                                  </div>
                                  <p className="text-sm font-medium text-[#1a73c0] uppercase tracking-wider">Last Name</p>
                                </div>
                                <p className="text-2xl font-semibold text-gray-800 pl-2 mt-2">{localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') || '{}').last_name || JSON.parse(localStorage.getItem('user') || '{}').lastName || 'N/A' : 'N/A'}</p>
                                <div className="h-1 w-16 bg-[#1a73c0]/30 rounded-full mt-3 group-hover:w-24 transition-all duration-300"></div>
                              </div>

                              <div className="bg-gradient-to-br from-white to-blue-50 p-6 rounded-xl shadow-sm border border-blue-100 hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 flex flex-col justify-center group">
                                <div className="flex items-center mb-3">
                                  <div className="bg-[#1a73c0] p-2 rounded-full mr-3 group-hover:bg-[#0d4a8b] transition-colors duration-300">
                                    <Mail className="h-5 w-5 text-white" />
                                  </div>
                                  <p className="text-sm font-medium text-[#1a73c0] uppercase tracking-wider">Email</p>
                                </div>
                                <p className="text-lg font-medium text-gray-800 pl-2 mt-2 break-all">{localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') || '{}').email || 'N/A' : 'N/A'}</p>
                                <div className="h-1 w-16 bg-[#1a73c0]/30 rounded-full mt-3 group-hover:w-24 transition-all duration-300"></div>
                              </div>
                            </div>

                            <div className="mt-6 bg-blue-50 p-3 rounded-lg border border-blue-100 flex items-center">
                              <Info className="h-4 w-4 mr-2 text-[#1a73c0]" />
                              <span className="text-sm text-gray-600">This information is automatically retrieved from your account details and cannot be modified here.</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <FormField
                        control={form.control}
                        name="grandfather_name"
                        render={({ field }) => (
                          <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                            <FormLabel className="text-gray-700 font-medium">Grandfather's Name *</FormLabel>
                            <FormControl>
                              <div className="relative mt-1.5">
                                <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                  <User className="h-5 w-5 text-[#1a73c0]" />
                                </div>
                                <Input
                                  id={`grandfather_name`}
                                  placeholder="Enter your grandfather's name"
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                  maxLength={100}
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                              {form.formState.errors.grandfather_name && <AlertTriangle className="h-4 w-4" />}
                            </FormMessage>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="gender"
                        render={({ field }) => (
                          <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                            <FormLabel className="text-gray-700 font-medium">Gender *</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger id="gender" className="mt-1.5 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20">
                                  <SelectValue placeholder="Select gender" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="Male">Male</SelectItem>
                                <SelectItem value="Female">Female</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                              {form.formState.errors.gender && <AlertTriangle className="h-4 w-4" />}
                            </FormMessage>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="mobile"
                        render={({ field }) => (
                          <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                            <FormLabel className="text-gray-700 font-medium">Mobile Number *</FormLabel>
                            <FormControl>
                              <div className="relative mt-1.5">
                                <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                  <Phone className="h-5 w-5 text-[#1a73c0]" />
                                </div>
                                <Input
                                  id="mobile"
                                  placeholder="Enter your mobile number"
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                  maxLength={15}
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                              {form.formState.errors.mobile && <AlertTriangle className="h-4 w-4" />}
                            </FormMessage>
                          </FormItem>
                        )}
                      />

                    </div>

                    {/* Date of Birth and Program Level in separate rows */}
                    <div className="grid grid-cols-1 md:grid-cols-1 gap-6 mt-6">
                      <FormField
                        control={form.control}
                        name="dob"
                        render={({ field }) => (
                          <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                            <FormLabel className="text-gray-700 font-medium">Date of Birth *</FormLabel>
                            <FormControl>
                              <div className="relative mt-1.5">
                                <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                  <Calendar className="h-5 w-5 text-[#1a73c0]" />
                                </div>
                                <Input
                                  id="dob"
                                  type="date"
                                  className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                              {form.formState.errors.dob && <AlertTriangle className="h-4 w-4" />}
                            </FormMessage>
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Program Level - Radio Buttons */}
                    <div className="grid grid-cols-1 md:grid-cols-1 gap-6 mt-6">
                      <FormField
                        control={form.control}
                        name="program_level"
                        render={({ field }) => (
                          <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                            <FormLabel className="text-gray-700 font-medium mb-3">Program Level *</FormLabel>
                            <div className="space-y-4">
                              <FormControl>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  {/* BSC/BA Option */}
                                  <div
                                    className={`relative flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                                      field.value === 'BSC/BA'
                                        ? 'border-[#1a73c0] bg-blue-50'
                                        : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                    onClick={() => field.onChange('BSC/BA')}
                                  >
                                    <div className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                                      field.value === 'BSC/BA' ? 'border-[#1a73c0]' : 'border-gray-400'
                                    }`}>
                                      {field.value === 'BSC/BA' && (
                                        <div className="w-3 h-3 rounded-full bg-[#1a73c0]" />
                                      )}
                                    </div>
                                    <div>
                                      <p className="font-medium">Bachelor's Degree</p>
                                      <p className="text-sm text-gray-500">BSC/BA</p>
                                    </div>
                                    {field.value === 'BSC/BA' && (
                                      <div className="absolute top-2 right-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#1a73c0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                          <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                      </div>
                                    )}
                                  </div>

                                  {/* MSC/MBA Option */}
                                  <div
                                    className={`relative flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                                      field.value === 'MSC/MBA'
                                        ? 'border-[#1a73c0] bg-blue-50'
                                        : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                    onClick={() => field.onChange('MSC/MBA')}
                                  >
                                    <div className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                                      field.value === 'MSC/MBA' ? 'border-[#1a73c0]' : 'border-gray-400'
                                    }`}>
                                      {field.value === 'MSC/MBA' && (
                                        <div className="w-3 h-3 rounded-full bg-[#1a73c0]" />
                                      )}
                                    </div>
                                    <div>
                                      <p className="font-medium">Master's Degree</p>
                                      <p className="text-sm text-gray-500">MSC/MBA</p>
                                    </div>
                                    {field.value === 'MSC/MBA' && (
                                      <div className="absolute top-2 right-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#1a73c0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                          <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                      </div>
                                    )}
                                  </div>

                                  {/* PHD Option */}
                                  <div
                                    className={`relative flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all ${
                                      field.value === 'PHD'
                                        ? 'border-[#1a73c0] bg-blue-50'
                                        : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                    onClick={() => field.onChange('PHD')}
                                  >
                                    <div className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                                      field.value === 'PHD' ? 'border-[#1a73c0]' : 'border-gray-400'
                                    }`}>
                                      {field.value === 'PHD' && (
                                        <div className="w-3 h-3 rounded-full bg-[#1a73c0]" />
                                      )}
                                    </div>
                                    <div>
                                      <p className="font-medium">Doctorate</p>
                                      <p className="text-sm text-gray-500">PHD</p>
                                    </div>
                                    {field.value === 'PHD' && (
                                      <div className="absolute top-2 right-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#1a73c0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                          <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </FormControl>
                            </div>
                            <FormMessage className="text-red-500 text-sm mt-3 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                              {form.formState.errors.program_level && <AlertTriangle className="h-4 w-4" />}
                            </FormMessage>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Educational Information Sections - Conditional based on program level */}
                  {(() => {
                    // Get the current program level
                    const programLevel = form.watch('program_level');

                    return (
                      <>
                        {/* Show a message for BSC/BA applicants */}
                        {programLevel === 'BSC/BA' && (
                          <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 mt-6">
                            <div className="flex items-center mb-4">
                              <div className="bg-blue-100 p-2 rounded-full mr-3">
                                <Info className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <h3 className="text-lg font-medium text-gray-800">No Educational Details Required</h3>
                            </div>
                            <p className="text-gray-600 ml-10">
                              For Bachelor of Science/Arts (BSC/BA) applications, no prior educational details are required.
                            </p>
                          </div>
                        )}

                        {/* Undergraduate Information Section - Show for MSC/MBA and PHD */}
                        {(programLevel === 'MSC/MBA' || programLevel === 'PHD') && (
                          <div className="space-y-6">
                            <div className="flex items-center">
                              <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                                <School className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <h3 className="text-lg font-medium text-gray-800">Undergraduate Information</h3>
                              <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                              <FormField
                                control={form.control}
                                name="ug_university"
                                render={({ field }) => (
                                  <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                    <FormLabel className="text-gray-700 font-medium">
                                  University/College *
                                  <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                                    Required for MSC/MBA & PHD
                                  </span>
                                </FormLabel>
                                    <FormControl>
                                      <div className="relative mt-1.5">
                                        <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                          <School className="h-5 w-5 text-[#1a73c0]" />
                                        </div>
                                        <Input
                                          id="ug_university"
                                          placeholder="Enter your university/college name"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          maxLength={255}
                                          {...field}
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                                      {form.formState.errors.ug_university && <AlertTriangle className="h-4 w-4" />}
                                    </FormMessage>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="ug_field_of_study"
                                render={({ field }) => (
                                  <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                    <FormLabel className="text-gray-700 font-medium">
                                  Field of Study *
                                  <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                                    Required for MSC/MBA & PHD
                                  </span>
                                </FormLabel>
                                    <FormControl>
                                      <div className="relative mt-1.5">
                                        <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                          <Award className="h-5 w-5 text-[#1a73c0]" />
                                        </div>
                                        <Input
                                          id="ug_field_of_study"
                                          placeholder="Enter your field of study"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          maxLength={255}
                                          {...field}
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                                      {form.formState.errors.ug_field_of_study && <AlertTriangle className="h-4 w-4" />}
                                    </FormMessage>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="ug_CGPA"
                                render={({ field }) => (
                                  <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                    <FormLabel className="text-gray-700 font-medium">
                                  CGPA *
                                  <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                                    Required for MSC/MBA & PHD
                                  </span>
                                </FormLabel>
                                    <FormControl>
                                      <div className="relative mt-1.5">
                                        <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                          <BookOpen className="h-5 w-5 text-[#1a73c0]" />
                                        </div>
                                        <Input
                                          id="ug_CGPA"
                                          placeholder="Enter your CGPA (e.g., 3.5)"
                                          type="number"
                                          step="0.01"
                                          min="2.0"
                                          max="4.0"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          {...field}
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                                      {form.formState.errors.ug_CGPA && <AlertTriangle className="h-4 w-4" />}
                                    </FormMessage>
                                    <p className="text-xs text-gray-500 mt-2">Enter your Cumulative Grade Point Average on a 4.0 scale</p>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        )}

                        {/* Postgraduate Information Section - Show for PHD only */}
                        {programLevel === 'PHD' && (
                          <div className="space-y-6">
                            <div className="flex items-center">
                              <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                                <Award className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <h3 className="text-lg font-medium text-gray-800">Postgraduate Information</h3>
                              <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                              <FormField
                                control={form.control}
                                name="pg_university"
                                render={({ field }) => (
                                  <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                    <FormLabel className="text-gray-700 font-medium">
                                  University/College *
                                  <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                                    Required for PHD
                                  </span>
                                </FormLabel>
                                    <FormControl>
                                      <div className="relative mt-1.5">
                                        <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                          <School className="h-5 w-5 text-[#1a73c0]" />
                                        </div>
                                        <Input
                                          id="pg_university"
                                          placeholder="Enter your university/college name"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          maxLength={255}
                                          {...field}
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                                      {form.formState.errors.pg_university && <AlertTriangle className="h-4 w-4" />}
                                    </FormMessage>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="pg_field_of_study"
                                render={({ field }) => (
                                  <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                    <FormLabel className="text-gray-700 font-medium">
                                  Field of Study *
                                  <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                                    Required for PHD
                                  </span>
                                </FormLabel>
                                    <FormControl>
                                      <div className="relative mt-1.5">
                                        <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                          <Award className="h-5 w-5 text-[#1a73c0]" />
                                        </div>
                                        <Input
                                          id="pg_field_of_study"
                                          placeholder="Enter your field of study"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          maxLength={255}
                                          {...field}
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                                      {form.formState.errors.pg_field_of_study && <AlertTriangle className="h-4 w-4" />}
                                    </FormMessage>
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="pg_CGPA"
                                render={({ field }) => (
                                  <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                    <FormLabel className="text-gray-700 font-medium">
                                  CGPA *
                                  <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                                    Required for PHD
                                  </span>
                                </FormLabel>
                                    <FormControl>
                                      <div className="relative mt-1.5">
                                        <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                          <BookOpen className="h-5 w-5 text-[#1a73c0]" />
                                        </div>
                                        <Input
                                          id="pg_CGPA"
                                          placeholder="Enter your CGPA (e.g., 3.5)"
                                          type="number"
                                          step="0.01"
                                          min="2.0"
                                          max="4.0"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          {...field}
                                        />
                                      </div>
                                    </FormControl>
                                    <FormMessage className="text-red-500 text-sm mt-1 font-medium bg-red-50 p-1.5 rounded-md flex items-center gap-1.5 border border-red-200" >
                                      {form.formState.errors.pg_CGPA && <AlertTriangle className="h-4 w-4" />}
                                    </FormMessage>
                                    <p className="text-xs text-gray-500 mt-2">Enter your Cumulative Grade Point Average on a 4.0 scale</p>
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        )}
                      </>
                    );
                  })()}

                  <div className="flex justify-end space-x-6 pt-10 border-t mt-10 bg-gray-50 -mx-8 -mb-8 px-8 py-6 rounded-b-lg">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate('/dashboard')}
                      className="px-8 py-3 border-gray-300 hover:bg-gray-50 transition-colors text-base"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="px-8 py-3 bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-base"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <span className="flex items-center">
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          Saving...
                        </span>
                      ) : existingPersonalInfo ? (
                        <span className="flex items-center">
                          Update Information
                          <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </span>
                      ) : (
                        <span className="flex items-center">
                          Save and Continue
                          <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </span>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
    </DashboardLayout>
  );
};

export default PersonalInformation;
