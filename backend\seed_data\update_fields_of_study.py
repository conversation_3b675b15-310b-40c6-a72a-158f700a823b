import json
import os

# Path to the fields of study JSON file
file_path = os.path.join(os.path.dirname(__file__), 'verification_fields_of_study.json')

# Read the existing file
with open(file_path, 'r') as f:
    fields_data = json.load(f)

# Update each field to add a code if it doesn't have one
for field in fields_data:
    if 'code' not in field:
        # Create a code based on the name
        name_parts = field['name'].split()
        if len(name_parts) == 1:
            # If name is a single word, use the first 4 letters
            code = name_parts[0][:4].upper()
        else:
            # If name has multiple words, use the first letter of each word
            code = ''.join(word[0] for word in name_parts).upper()
            
            # If code is too short, add more letters from the first word
            if len(code) < 2:
                code = name_parts[0][:4].upper()
        
        # Add the code to the field
        field['code'] = code

# Write the updated data back to the file
with open(file_path, 'w') as f:
    json.dump(fields_data, f, indent=2)

print(f"Updated {len(fields_data)} fields of study with codes.")
