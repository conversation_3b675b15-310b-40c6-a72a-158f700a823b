import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Clock, Save, Shield, Bell } from 'lucide-react';
import { toast } from 'sonner';
import DashboardLayout from '@/components/DashboardLayout';

const Settings = () => {
  // State for idle timeout settings
  const [idleTimeoutEnabled, setIdleTimeoutEnabled] = useState(true);
  const [idleTimeoutDuration, setIdleTimeoutDuration] = useState('5');
  const [showWarning, setShowWarning] = useState(true);
  const [warningDuration, setWarningDuration] = useState('1');

  // Load settings from localStorage on component mount
  useEffect(() => {
    const storedIdleTimeoutEnabled = localStorage.getItem('idleTimeoutEnabled');
    const storedIdleTimeoutDuration = localStorage.getItem('idleTimeoutDuration');
    const storedShowWarning = localStorage.getItem('idleTimeoutWarningEnabled');
    const storedWarningDuration = localStorage.getItem('idleTimeoutWarningDuration');

    if (storedIdleTimeoutEnabled !== null) {
      setIdleTimeoutEnabled(storedIdleTimeoutEnabled === 'true');
    }
    
    if (storedIdleTimeoutDuration !== null) {
      setIdleTimeoutDuration(storedIdleTimeoutDuration);
    }
    
    if (storedShowWarning !== null) {
      setShowWarning(storedShowWarning === 'true');
    }
    
    if (storedWarningDuration !== null) {
      setWarningDuration(storedWarningDuration);
    }
  }, []);

  // Save idle timeout settings
  const saveIdleTimeoutSettings = () => {
    localStorage.setItem('idleTimeoutEnabled', idleTimeoutEnabled.toString());
    localStorage.setItem('idleTimeoutDuration', idleTimeoutDuration);
    localStorage.setItem('idleTimeoutWarningEnabled', showWarning.toString());
    localStorage.setItem('idleTimeoutWarningDuration', warningDuration);
    
    toast.success('Session timeout settings saved successfully');
    
    // Reload the page to apply the new settings
    window.location.reload();
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Settings</h1>
          <p className="text-muted-foreground">Manage your account settings and preferences</p>
        </div>

        <Tabs defaultValue="session" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="session" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Session</span>
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>Security</span>
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <span>Notifications</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="session">
            <Card>
              <CardHeader>
                <CardTitle>Session Timeout Settings</CardTitle>
                <CardDescription>
                  Configure how long your session stays active during periods of inactivity
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="idle-timeout-toggle" className="font-medium">
                      Auto Logout on Inactivity
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically log out when you're inactive for a specified period
                    </p>
                  </div>
                  <Switch
                    id="idle-timeout-toggle"
                    checked={idleTimeoutEnabled}
                    onCheckedChange={setIdleTimeoutEnabled}
                  />
                </div>

                {idleTimeoutEnabled && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="idle-timeout-duration">Inactivity Timeout</Label>
                      <Select
                        value={idleTimeoutDuration}
                        onValueChange={setIdleTimeoutDuration}
                      >
                        <SelectTrigger id="idle-timeout-duration" className="w-full">
                          <SelectValue placeholder="Select timeout duration" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 minute</SelectItem>
                          <SelectItem value="5">5 minutes</SelectItem>
                          <SelectItem value="10">10 minutes</SelectItem>
                          <SelectItem value="15">15 minutes</SelectItem>
                          <SelectItem value="30">30 minutes</SelectItem>
                          <SelectItem value="60">1 hour</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground">
                        You will be automatically logged out after this period of inactivity
                      </p>
                    </div>

                    <div className="flex items-center justify-between pt-2">
                      <div>
                        <Label htmlFor="warning-toggle" className="font-medium">
                          Show Warning Before Logout
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Display a warning message before automatic logout
                        </p>
                      </div>
                      <Switch
                        id="warning-toggle"
                        checked={showWarning}
                        onCheckedChange={setShowWarning}
                      />
                    </div>

                    {showWarning && (
                      <div className="space-y-2">
                        <Label htmlFor="warning-duration">Warning Time</Label>
                        <Select
                          value={warningDuration}
                          onValueChange={setWarningDuration}
                        >
                          <SelectTrigger id="warning-duration" className="w-full">
                            <SelectValue placeholder="Select warning time" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0.5">30 seconds</SelectItem>
                            <SelectItem value="1">1 minute</SelectItem>
                            <SelectItem value="2">2 minutes</SelectItem>
                            <SelectItem value="3">3 minutes</SelectItem>
                            <SelectItem value="5">5 minutes</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground">
                          Warning will be shown this amount of time before automatic logout
                        </p>
                      </div>
                    )}
                  </>
                )}

                <Button 
                  onClick={saveIdleTimeoutSettings}
                  className="w-full sm:w-auto"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Session Settings
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Manage your account security settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Security settings will be available in a future update.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure how you receive notifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Notification settings will be available in a future update.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
