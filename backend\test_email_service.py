#!/usr/bin/env python
"""
Test script for the alumni application email service.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from alumni_applications.email_service import AlumniApplicationEmailService
from alumni_applications.models import AlumniApplication, AlumniApplicationMini
from setups.service_type.models import ServiceType
from settings_manager.smtp_models import SMTPSettings


def test_smtp_configuration():
    """Test SMTP configuration."""
    print("Testing SMTP Configuration...")
    
    try:
        smtp_settings = SMTPSettings.load()
        print(f"SMTP Host: {smtp_settings.host}")
        print(f"SMTP Port: {smtp_settings.port}")
        print(f"SMTP Username: {smtp_settings.username}")
        print(f"From Email: {smtp_settings.from_email}")
        print(f"Use TLS: {smtp_settings.use_tls}")
        print(f"Use SSL: {smtp_settings.use_ssl}")
        
        if smtp_settings.host:
            print("✅ SMTP is configured")
            return True
        else:
            print("❌ SMTP is not configured")
            return False
            
    except Exception as e:
        print(f"❌ Error loading SMTP settings: {e}")
        return False


def test_email_service():
    """Test the email service with a mock application."""
    print("\nTesting Email Service...")
    
    try:
        # Create a mock application for testing
        service_type = ServiceType.objects.first()
        if not service_type:
            print("❌ No service types found. Please create a service type first.")
            return False
            
        # Create a test application with all required fields
        from django.utils import timezone
        import uuid

        test_application = AlumniApplication(
            id=uuid.uuid4(),
            first_name="Test",
            father_name="User",
            last_name="Application",
            email="<EMAIL>",  # Use a real email for testing
            phone_number="+************",
            service_type=service_type,  # Use the ServiceType instance
            transaction_id="TEST123456",
            application_status="Pending",
            payment_status="Pending",
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        
        # Don't save to database, just use for email testing
        print(f"Mock application created:")
        print(f"  Name: {test_application.first_name} {test_application.father_name} {test_application.last_name}")
        print(f"  Email: {test_application.email}")
        print(f"  Service Type: {service_type.name}")
        print(f"  Transaction ID: {test_application.transaction_id}")
        
        # Test email sending
        success = AlumniApplicationEmailService.send_application_confirmation(
            application=test_application,
            is_form1=True
        )
        
        if success:
            print("✅ Email sent successfully!")
            return True
        else:
            print("❌ Email sending failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing email service: {e}")
        return False


def test_email_templates():
    """Test email template rendering."""
    print("\nTesting Email Templates...")
    
    try:
        from django.template.loader import render_to_string
        
        # Test context
        context = {
            'applicant_name': 'John Doe Smith',
            'transaction_id': 'TEST123456',
            'service_type': 'Official Transcript',
            'service_fee': '25.00',
            'application_type': 'Complete Application',
            'form_type': 'form1',
            'application_id': 'test-uuid',
            'submission_date': 'December 19, 2024 at 02:30 PM',
            'support_email': '<EMAIL>',
            'portal_url': 'http://localhost:8080',
        }
        
        # Test HTML template
        html_content = render_to_string('alumni_applications/confirmation_email.html', context)
        print(f"✅ HTML template rendered successfully ({len(html_content)} characters)")
        
        # Test text template
        text_content = render_to_string('alumni_applications/confirmation_email.txt', context)
        print(f"✅ Text template rendered successfully ({len(text_content)} characters)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing email templates: {e}")
        return False


def main():
    """Main test function."""
    print("🧪 Alumni Application Email Service Test")
    print("=" * 50)
    
    # Test SMTP configuration
    smtp_ok = test_smtp_configuration()
    
    # Test email templates
    templates_ok = test_email_templates()
    
    # Test email service (only if SMTP is configured)
    if smtp_ok:
        email_ok = test_email_service()
    else:
        print("\n⚠️  Skipping email service test (SMTP not configured)")
        email_ok = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  SMTP Configuration: {'✅ PASS' if smtp_ok else '❌ FAIL'}")
    print(f"  Email Templates: {'✅ PASS' if templates_ok else '❌ FAIL'}")
    print(f"  Email Service: {'✅ PASS' if email_ok else '❌ FAIL/SKIP'}")
    
    if smtp_ok and templates_ok and email_ok:
        print("\n🎉 All tests passed! Email service is ready.")
    elif templates_ok:
        print("\n⚠️  Email templates work, but SMTP needs configuration.")
    else:
        print("\n❌ Some tests failed. Please check the configuration.")


if __name__ == "__main__":
    main()
