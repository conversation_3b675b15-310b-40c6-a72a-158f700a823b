# Generated by Django 5.2.1 on 2025-05-31 12:42

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Title of the announcement', max_length=255, verbose_name='Title')),
                ('content', models.TextField(help_text='Content of the announcement', verbose_name='Content')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='medium', max_length=10, verbose_name='Priority')),
                ('target_audience', models.CharField(choices=[('all', 'All Users'), ('students', 'Students'), ('staff', 'Staff'), ('applicants', 'Applicants')], default='all', max_length=20, verbose_name='Target Audience')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this announcement is currently active', verbose_name='Is Active')),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now, help_text='When this announcement should start being displayed', verbose_name='Start Date')),
                ('end_date', models.DateTimeField(blank=True, help_text='When this announcement should stop being displayed (optional)', null=True, verbose_name='End Date')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='announcements', to=settings.AUTH_USER_MODEL, verbose_name='Author')),
            ],
            options={
                'verbose_name': 'Announcement',
                'verbose_name_plural': 'Announcements',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(help_text='Email subject line', max_length=255, verbose_name='Subject')),
                ('content', models.TextField(help_text='Email content (HTML supported)', verbose_name='Content')),
                ('recipients', models.TextField(help_text='Comma-separated list of email addresses or recipient groups', verbose_name='Recipients')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('sent', 'Sent'), ('failed', 'Failed')], default='draft', max_length=20, verbose_name='Status')),
                ('scheduled_time', models.DateTimeField(blank=True, help_text='When this email should be sent (if scheduled)', null=True, verbose_name='Scheduled Time')),
                ('sent_time', models.DateTimeField(blank=True, help_text='When this email was actually sent', null=True, verbose_name='Sent Time')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_emails', to=settings.AUTH_USER_MODEL, verbose_name='Sender')),
            ],
            options={
                'verbose_name': 'Email Notification',
                'verbose_name_plural': 'Email Notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SMSNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(help_text='SMS message content (160 characters max for standard SMS)', verbose_name='Message')),
                ('recipients', models.TextField(help_text='Comma-separated list of phone numbers or recipient groups', verbose_name='Recipients')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('sent', 'Sent'), ('failed', 'Failed')], default='draft', max_length=20, verbose_name='Status')),
                ('scheduled_time', models.DateTimeField(blank=True, help_text='When this SMS should be sent (if scheduled)', null=True, verbose_name='Scheduled Time')),
                ('sent_time', models.DateTimeField(blank=True, help_text='When this SMS was actually sent', null=True, verbose_name='Sent Time')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_sms', to=settings.AUTH_USER_MODEL, verbose_name='Sender')),
            ],
            options={
                'verbose_name': 'SMS Notification',
                'verbose_name_plural': 'SMS Notifications',
                'ordering': ['-created_at'],
            },
        ),
    ]
