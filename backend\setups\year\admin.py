from django.contrib import admin
from setups.year.models import Year

# Register your models here.
@admin.register(Year)
class YearAdmin(admin.ModelAdmin):
    list_display = ['year', 'created_at', 'updated_at']
    search_fields = ['year', 'uuid']
    list_filter = ['created_at', 'updated_at']
    date_hierarchy = 'created_at'
    ordering = ['-year']
    list_per_page = 20
    list_max_show_all = 100
    list_display_links = ['year']
    readonly_fields = ['uuid', 'created_at', 'updated_at']
    fieldsets = [
        ('Year Information', {
            'fields': ['year']
        }),
        ('Metadata', {
            'fields': ['uuid', 'created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
