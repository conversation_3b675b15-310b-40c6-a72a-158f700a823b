import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import DownloadableList from '@/components/DownloadableList';
import DocumentTitle from '@/components/DocumentTitle';
import { settingsAPI } from '@/services/api';

const Downloads = () => {
  // Custom pattern background for hero section
  const heroPatternImage = '/images/university-pattern.svg';

  // State for organization name and loading state
  const [orgName, setOrgName] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Fetch organization name directly from the API
  useEffect(() => {
    // Try to get from localStorage first for immediate display
    const cachedOrgName = localStorage.getItem('organizationName');
    if (cachedOrgName) {
      setOrgName(cachedOrgName);
      setIsLoading(false);
    }

    const fetchOrgName = async () => {
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data?.organization) {
          setOrgName(response.data.organization);
          // Cache for future use
          localStorage.setItem('organizationName', response.data.organization);
        }
      } catch (error) {
        console.error('Error fetching organization name:', error);
        // Fallback to default
        setOrgName('University of Gondar');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrgName();
  }, []);

  return (
    <Layout>
      <DocumentTitle title="Downloads" />

      {/* Hero Section with Animated Patterned Background - Half Height */}
      <section className="relative py-4 md:py-6 h-[175px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          <h1 className="text-xl md:text-2xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
            {isLoading ? '' : orgName || 'University of Gondar'} Downloads
          </h1>
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Access and download important files, documents, and resources {isLoading ? '' : `from ${orgName || 'University of Gondar'}`}
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        <DownloadableList />
      </div>
    </Layout>
  );
};

export default Downloads;
