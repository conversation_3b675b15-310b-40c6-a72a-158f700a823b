import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  User, 
  Shield, 
  Users, 
  Settings,
  RefreshCw,
  TestTube
} from 'lucide-react';

/**
 * User Simulator Component
 * Helps test different user scenarios for access control
 */
export const UserSimulator: React.FC = () => {
  const [simulatedUser, setSimulatedUser] = useState({
    is_active: true,
    is_staff: false,
    is_superuser: false,
    role_names: [] as string[],
    permissions: [] as string[]
  });

  const testScenarios = [
    {
      name: 'Regular User (Active)',
      description: 'Active user, not staff, not superuser',
      expectedBehavior: 'Should redirect to applicant dashboard',
      user: {
        is_active: true,
        is_staff: false,
        is_superuser: false,
        role_names: [],
        permissions: []
      }
    },
    {
      name: 'Inactive User',
      description: 'Inactive user account',
      expectedBehavior: 'Should show message and redirect to login',
      user: {
        is_active: false,
        is_staff: false,
        is_superuser: false,
        role_names: [],
        permissions: []
      }
    },
    {
      name: 'Staff Without Groups',
      description: 'Staff user with no groups or permissions',
      expectedBehavior: 'Should show access denied message',
      user: {
        is_active: true,
        is_staff: true,
        is_superuser: false,
        role_names: [],
        permissions: []
      }
    },
    {
      name: 'Staff With Groups',
      description: 'Staff user with assigned groups',
      expectedBehavior: 'Should grant access based on permissions',
      user: {
        is_active: true,
        is_staff: true,
        is_superuser: false,
        role_names: ['Registrar', 'Department Head'],
        permissions: ['user.view_user', 'graduate.view_graduate']
      }
    },
    {
      name: 'Superuser',
      description: 'Superuser with full access',
      expectedBehavior: 'Should grant full access to everything',
      user: {
        is_active: true,
        is_staff: true,
        is_superuser: true,
        role_names: ['Administrator'],
        permissions: ['*']
      }
    }
  ];

  const handleScenarioTest = (scenario: typeof testScenarios[0]) => {
    setSimulatedUser(scenario.user);
    console.log('🧪 Testing Scenario:', scenario.name);
    console.log('📋 Expected Behavior:', scenario.expectedBehavior);
    console.log('👤 Simulated User:', scenario.user);
  };

  const resetToDefault = () => {
    setSimulatedUser({
      is_active: true,
      is_staff: false,
      is_superuser: false,
      role_names: [],
      permissions: []
    });
  };

  const getStatusColor = (value: boolean) => {
    return value ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (value: boolean) => {
    return value ? '✓' : '✗';
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Current Simulation Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            User Simulation Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${getStatusColor(simulatedUser.is_active)}`}>
                {getStatusIcon(simulatedUser.is_active)}
              </div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getStatusColor(simulatedUser.is_staff)}`}>
                {getStatusIcon(simulatedUser.is_staff)}
              </div>
              <div className="text-xs text-muted-foreground">Staff</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getStatusColor(simulatedUser.is_superuser)}`}>
                {getStatusIcon(simulatedUser.is_superuser)}
              </div>
              <div className="text-xs text-muted-foreground">Superuser</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getStatusColor(simulatedUser.role_names.length > 0)}`}>
                {simulatedUser.role_names.length}
              </div>
              <div className="text-xs text-muted-foreground">Groups</div>
            </div>
          </div>

          <div className="space-y-2">
            <div>
              <Label className="text-sm font-medium">Groups/Roles</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {simulatedUser.role_names.length > 0 ? (
                  simulatedUser.role_names.map((role, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {role}
                    </Badge>
                  ))
                ) : (
                  <span className="text-sm text-muted-foreground">No roles assigned</span>
                )}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Permissions</Label>
              <div className="text-sm text-muted-foreground mt-1">
                {simulatedUser.permissions.length > 0 ? (
                  `${simulatedUser.permissions.length} permissions assigned`
                ) : (
                  'No specific permissions assigned'
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={resetToDefault} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-1" />
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Scenarios */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Test Scenarios
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            {testScenarios.map((scenario, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm">{scenario.name}</h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      {scenario.description}
                    </p>
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs">
                        {scenario.expectedBehavior}
                      </Badge>
                    </div>
                  </div>
                  <Button 
                    onClick={() => handleScenarioTest(scenario)}
                    size="sm"
                    variant="outline"
                  >
                    Test
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Manual Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Manual User Controls
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={simulatedUser.is_active}
                onCheckedChange={(checked) => 
                  setSimulatedUser(prev => ({ ...prev, is_active: checked }))
                }
              />
              <Label htmlFor="active">Active Account</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="staff"
                checked={simulatedUser.is_staff}
                onCheckedChange={(checked) => 
                  setSimulatedUser(prev => ({ ...prev, is_staff: checked }))
                }
              />
              <Label htmlFor="staff">Staff Status</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="superuser"
                checked={simulatedUser.is_superuser}
                onCheckedChange={(checked) => 
                  setSimulatedUser(prev => ({ ...prev, is_superuser: checked }))
                }
              />
              <Label htmlFor="superuser">Superuser Status</Label>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <p><strong>Note:</strong> This is a simulation tool for testing access control scenarios. 
            Changes here don't affect the actual user authentication system.</p>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Testing Instructions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div>
              <strong>1. Test Each Scenario:</strong> Click the "Test" button for each scenario to see how the access control system responds.
            </div>
            <div>
              <strong>2. Check Console:</strong> Open browser console to see detailed access control logs.
            </div>
            <div>
              <strong>3. Navigate:</strong> Try accessing different admin areas to see permission-based filtering.
            </div>
            <div>
              <strong>4. Menu Access:</strong> Check how menu items are filtered based on user permissions.
            </div>
            <div>
              <strong>5. Error Messages:</strong> Verify that appropriate error messages are shown for denied access.
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSimulator;
