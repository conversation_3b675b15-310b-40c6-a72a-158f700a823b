#!/usr/bin/env python
"""
Test database connection and apply migration
"""
import os
import sys
import django
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

def test_connection():
    """Test database connection"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"✓ Database connection successful: {result}")
            return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def check_table_exists():
    """Check if the graduate student table exists"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'GraduateVerification_graduatestudent';
            """)
            result = cursor.fetchone()
            if result:
                print(f"✓ Table exists: {result[0]}")
                return True
            else:
                print("✗ Table does not exist")
                return False
    except Exception as e:
        print(f"✗ Error checking table: {e}")
        return False

def check_audit_columns():
    """Check if audit trail columns exist"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'GraduateVerification_graduatestudent' 
                AND column_name IN ('created_by_id', 'updated_by_id');
            """)
            columns = [row[0] for row in cursor.fetchall()]
            
            if 'created_by_id' in columns and 'updated_by_id' in columns:
                print("✓ Audit trail columns already exist!")
                return True
            else:
                print(f"✗ Missing audit columns. Found: {columns}")
                return False
    except Exception as e:
        print(f"✗ Error checking columns: {e}")
        return False

def add_audit_columns():
    """Add audit trail columns"""
    try:
        with connection.cursor() as cursor:
            print("Adding created_by_id column...")
            cursor.execute("""
                ALTER TABLE "GraduateVerification_graduatestudent" 
                ADD COLUMN IF NOT EXISTS "created_by_id" integer NULL;
            """)
            
            print("Adding updated_by_id column...")
            cursor.execute("""
                ALTER TABLE "GraduateVerification_graduatestudent" 
                ADD COLUMN IF NOT EXISTS "updated_by_id" integer NULL;
            """)
            
            print("Adding foreign key constraints...")
            try:
                cursor.execute("""
                    ALTER TABLE "GraduateVerification_graduatestudent" 
                    ADD CONSTRAINT "GraduateVerification_graduatestudent_created_by_id_fkey" 
                    FOREIGN KEY ("created_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
                """)
            except Exception as e:
                print(f"  Created_by constraint may already exist: {e}")
            
            try:
                cursor.execute("""
                    ALTER TABLE "GraduateVerification_graduatestudent" 
                    ADD CONSTRAINT "GraduateVerification_graduatestudent_updated_by_id_fkey" 
                    FOREIGN KEY ("updated_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
                """)
            except Exception as e:
                print(f"  Updated_by constraint may already exist: {e}")
            
            print("✓ Audit columns added successfully!")
            return True
            
    except Exception as e:
        print(f"✗ Error adding columns: {e}")
        return False

def test_model_access():
    """Test if we can access the model with audit fields"""
    try:
        from GraduateVerification.models import GraduateStudent
        
        # Test basic query
        count = GraduateStudent.objects.count()
        print(f"✓ Model access successful: {count} graduate students")
        
        # Test query with audit fields
        try:
            audit_count = GraduateStudent.objects.select_related('created_by', 'updated_by').count()
            print(f"✓ Audit field query successful: {audit_count} records")
            return True
        except Exception as e:
            print(f"✗ Audit field query failed: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Model access failed: {e}")
        return False

if __name__ == '__main__':
    print("=== Database Migration Test ===")
    
    # Test connection
    if not test_connection():
        print("❌ Cannot connect to database")
        sys.exit(1)
    
    # Check table
    if not check_table_exists():
        print("❌ Graduate student table not found")
        sys.exit(1)
    
    # Check audit columns
    if not check_audit_columns():
        print("Adding audit trail columns...")
        if not add_audit_columns():
            print("❌ Failed to add audit columns")
            sys.exit(1)
    
    # Test model access
    if test_model_access():
        print("\n🎉 Migration completed successfully!")
        print("✓ Database connection working")
        print("✓ Audit trail columns exist")
        print("✓ Model queries working")
        print("\n💡 You can now restart your Django server and test the dashboard")
    else:
        print("\n⚠️ Migration partially successful")
        print("Database columns added but model access failed")
        print("Try restarting Django server")
