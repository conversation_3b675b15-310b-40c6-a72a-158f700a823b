import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Plus, Pencil, Trash2, Search, RefreshCw, Filter, Building, CheckCircle, RotateCcw, XCircle, FileText, ChevronLeft, <PERSON>evronRight, <PERSON><PERSON>rons<PERSON>eft, ChevronsRight } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface College {
  id: number;
  name: string;
  description?: string;
  code?: string;
}

interface Department {
  id: number;
  name: string;
  description?: string;
  college: number;
  college_name?: string;
}

interface FieldOfStudy {
  id: number;
  field_of_study: string;  // Primary field name in the StudyField model
  code?: string;          // Optional in StudyField model
  department: number;
  department_name?: string;
  college: number;
  college_name?: string;
  description?: string;
  status: boolean;
  created_at?: string;
  updated_at?: string;
}

const ApplicationFieldOfStudyManagement = () => {
  const [fieldsOfStudy, setFieldsOfStudy] = useState<FieldOfStudy[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentFieldOfStudy, setCurrentFieldOfStudy] = useState<FieldOfStudy | null>(null);
  const [formData, setFormData] = useState({
    field_of_study: '',
    department: '',
    college: '',
    description: '',
    code: '',
    status: true
  });
  const [formErrors, setFormErrors] = useState({
    field_of_study: '',
    department: '',
    college: '',
    description: '',
    code: '',
    general: ''
  });
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch fields of study, departments, and colleges on component mount
  useEffect(() => {
    fetchFieldsOfStudy();
    fetchDepartments();
    fetchColleges();
  }, []);

  const fetchFieldsOfStudy = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to view fields of study');
        setLoading(false);
        return;
      }

      // Get API base URL from environment
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const baseUrl = apiBaseUrl.replace('/api', '');

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try study-fields endpoint (application system)
        { url: `${baseUrl}/api/study-fields/`, auth: true },
        // Try regular fields-of-study endpoints
        { url: `${baseUrl}/api/fields-of-study/`, auth: true },
        { url: `${baseUrl}/api/fields-of-study/public/`, auth: false },
        // Try verification fields-of-study endpoints
        { url: `${baseUrl}/api/verification-fields-of-study/`, auth: true },
        { url: `${baseUrl}/api/verification-fields-of-study/public/`, auth: false }
      ];

      let fieldsOfStudyData = null;
      let workingEndpoint = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint.url}`);
          const headers: HeadersInit = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            headers['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            fieldsOfStudyData = await response.json();
            workingEndpoint = endpoint.url;
            console.log(`Successfully fetched data from ${endpoint.url}`);
            break;
          }
        } catch (endpointError) {
          console.error(`Error with endpoint ${endpoint.url}:`, endpointError);
        }
      }

      if (!fieldsOfStudyData) {
        throw new Error('Could not fetch fields of study from any endpoint');
      }

      console.log('Fields of study data:', fieldsOfStudyData);

      // Store the working endpoint for future use
      localStorage.setItem('fieldsOfStudyEndpoint', workingEndpoint);

      // Process fields of study data based on the endpoint format
      let processedFields = [];

      if (workingEndpoint && workingEndpoint.includes('study-fields')) {
        // For study-fields endpoint, use the StudyField model format
        processedFields = fieldsOfStudyData.map((field: any) => ({
          id: field.id,
          field_of_study: field.field_of_study,
          code: field.code || '',
          department: field.department,
          department_name: field.department_name || '',
          college: field.college,
          college_name: field.college_name || '',
          description: field.description || '',
          status: field.status !== undefined ? field.status : true,
          created_at: field.created_at,
          updated_at: field.updated_at
        }));
      } else if (workingEndpoint && (workingEndpoint.includes('verification-fields-of-study') || workingEndpoint.includes('fields-of-study'))) {
        // For verification-fields-of-study or fields-of-study endpoint
        processedFields = await Promise.all(
          fieldsOfStudyData.map(async (field: any) => {
            const departmentName = field.department_name || await getDepartmentName(field.department);
            return {
              id: field.id,
              field_of_study: field.name, // Map name to field_of_study
              code: field.code || '',
              department: field.department,
              department_name: departmentName,
              college: field.department?.college || 0,
              college_name: field.college_name || '',
              description: '',
              status: true
            };
          })
        );
      }

      console.log('Processed fields of study:', processedFields);
      setFieldsOfStudy(processedFields);
    } catch (error) {
      console.error('Error fetching fields of study:', error);
      toast.error(`Failed to fetch fields of study: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to view departments');
        return;
      }

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try regular departments endpoints
        { url: 'http://localhost:8000/api/departments/', auth: true },
        { url: 'http://localhost:8000/api/departments/public/', auth: false },
        // Try setup departments endpoint
        { url: 'http://localhost:8000/api/setup/departments/', auth: true }
      ];

      let departmentData = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying department endpoint: ${endpoint.url}`);
          const headers: HeadersInit = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            headers['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            departmentData = await response.json();
            console.log(`Successfully fetched departments from ${endpoint.url}`);
            break;
          }
        } catch (endpointError) {
          console.error(`Error with department endpoint ${endpoint.url}:`, endpointError);
        }
      }

      if (!departmentData) {
        throw new Error('Could not fetch departments from any endpoint');
      }

      console.log('Department data:', departmentData);
      setDepartments(departmentData);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast.error(`Failed to fetch departments: ${error.message}`);
    }
  };

  const fetchColleges = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to view colleges');
        return;
      }

      // Get API base URL from environment
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const baseUrl = apiBaseUrl.replace('/api', '');

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try regular colleges endpoints
        { url: `${baseUrl}/api/colleges/`, auth: true },
        { url: `${baseUrl}/api/colleges/public/`, auth: false },
        // Try setup colleges endpoint
        { url: `${baseUrl}/api/setup/colleges/`, auth: true },
        // Try verification colleges endpoint
        { url: `${baseUrl}/api/verification-colleges/`, auth: true },
        { url: `${baseUrl}/api/verification-colleges/public/`, auth: false }
      ];

      let collegeData = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying college endpoint: ${endpoint.url}`);
          const headers: HeadersInit = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            headers['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            collegeData = await response.json();
            console.log(`Successfully fetched colleges from ${endpoint.url}`);
            break;
          }
        } catch (endpointError) {
          console.error(`Error with college endpoint ${endpoint.url}:`, endpointError);
        }
      }

      if (!collegeData) {
        throw new Error('Could not fetch colleges from any endpoint');
      }

      console.log('College data:', collegeData);
      setColleges(collegeData);
    } catch (error) {
      console.error('Error fetching colleges:', error);
      toast.error(`Failed to fetch colleges: ${error.message}`);
    }
  };

  const getDepartmentName = async (departmentId: number): Promise<string> => {
    // First check if we already have the department in our state
    const department = departments.find(d => d.id === departmentId);
    if (department) {
      return department.name;
    }

    // If we don't have the department in state, just return a placeholder
    // This avoids making individual API calls for each department
    return 'Department ' + departmentId;
  };

  const getCollegeName = (collegeId: number): string => {
    // Check if we have the college in our state
    const college = colleges.find(c => c.id === collegeId);
    if (college) {
      return college.name;
    }
    return 'College ' + collegeId;
  };

  // Helper functions for form handling
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Auto-format code to uppercase and remove non-alphanumeric characters
    let newValue = value;
    if (name === 'code') {
      // Remove non-alphanumeric characters and convert to uppercase
      newValue = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    }

    setFormData({
      ...formData,
      [name]: newValue,
    });

    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleSelectChange = (value: string) => {
    // Find the selected department
    const selectedDept = departments.find(d => d.id.toString() === value);

    if (selectedDept) {
      // Automatically set the college based on the selected department
      setFormData({
        ...formData,
        department: value,
        college: selectedDept.college.toString() // Set college to match department's college
      });

      console.log(`Auto-setting college to ${selectedDept.college} based on selected department`);

      // Clear errors when user selects a department
      setFormErrors({
        ...formErrors,
        department: '',
        college: '' // Also clear college error
      });
    } else {
      // If department not found, just update the department field
      setFormData({
        ...formData,
        department: value,
      });

      // Clear error when user selects a department
      if (formErrors.department) {
        setFormErrors({
          ...formErrors,
          department: ''
        });
      }
    }
  };

  const handleFilterDepartmentChange = (value: string) => {
    setFormData({
      ...formData,
      department: value === 'all' ? '' : value
    });
  };

  const resetForm = () => {
    setFormData({
      field_of_study: '',
      department: '',
      college: '',
      description: '',
      code: '',
      status: true
    });
    setFormErrors({
      field_of_study: '',
      department: '',
      college: '',
      description: '',
      code: '',
      general: ''
    });
    setCurrentFieldOfStudy(null);
  };

  const validateForm = (): boolean => {
    let valid = true;
    const newErrors = {
      field_of_study: '',
      department: '',
      college: '',
      description: '',
      code: '',
      general: ''
    };

    // Validate department
    if (!formData.department) {
      newErrors.department = 'Department is required';
      valid = false;
    }

    // Validate college
    if (!formData.college) {
      newErrors.college = 'College is required';
      valid = false;
    }

    // Validate field_of_study (name)
    if (!formData.field_of_study.trim()) {
      newErrors.field_of_study = 'Field of study name is required';
      valid = false;
    } else if (formData.field_of_study.length > 200) {
      newErrors.field_of_study = 'Field of study name must be less than 200 characters';
      valid = false;
    } else {
      // Check for duplicate field of study in the same department
      const departmentId = parseInt(formData.department);
      const fieldOfStudyName = formData.field_of_study.trim();

      // Only check for duplicates if we have a valid department and field of study name
      if (departmentId && fieldOfStudyName) {
        const existingFieldOfStudy = fieldsOfStudy.find(fos =>
          fos.department === departmentId &&
          fos.field_of_study.toLowerCase() === fieldOfStudyName.toLowerCase() &&
          (!currentFieldOfStudy || fos.id !== currentFieldOfStudy.id)
        );

        if (existingFieldOfStudy) {
          newErrors.field_of_study = 'A field of study with this name already exists in the selected department';
          valid = false;
        }
      }
    }

    // Validate code (optional but must be unique if provided)
    if (formData.code.trim()) {
      if (formData.code.length > 20) {
        newErrors.code = 'Code must be less than 20 characters';
        valid = false;
      } else if (!formData.code.trim().match(/^[A-Za-z0-9]+$/)) {
        newErrors.code = 'Code must contain only letters and numbers';
        valid = false;
      } else {
        // Check for duplicate code in the same department
        const departmentId = parseInt(formData.department);
        const code = formData.code.trim().toUpperCase();

        // Only check for duplicates if we have a valid department and code
        if (departmentId && code) {
          const existingFieldOfStudy = fieldsOfStudy.find(fos =>
            fos.department === departmentId &&
            fos.code?.toUpperCase() === code &&
            (!currentFieldOfStudy || fos.id !== currentFieldOfStudy.id)
          );

          if (existingFieldOfStudy) {
            newErrors.code = 'A field of study with this code already exists in the selected department';
            valid = false;
          }
        }
      }
    }

    setFormErrors(newErrors);
    return valid;
  };

  // Helper function to determine the correct API endpoint
  const getApiEndpoint = () => {
    // Always use the study-fields endpoint
    return 'http://localhost:8000/api/study-fields/';
  };



  const handleAddFieldOfStudy = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add a field of study');
        return;
      }

      // Get the department ID
      const departmentId = parseInt(formData.department);

      // Validate that we have a department
      if (!departmentId) {
        toast.error('Please select a department');
        return;
      }

      // Find the selected department
      const selectedDept = departments.find(d => d.id === departmentId);
      if (!selectedDept) {
        toast.error('Department not found');
        return;
      }

      // Use the exact endpoint from the backend URLs
      const endpoint = 'http://localhost:8000/api/study-fields/';
      console.log(`Using endpoint: ${endpoint}`);

      // Get the college ID from the selected department
      const collegeId = selectedDept.college;

      // Format the code field (remove non-alphanumeric characters and convert to uppercase)
      const formattedCode = formData.code.trim()
        ? formData.code.trim().replace(/[^A-Za-z0-9]/g, '').toUpperCase()
        : null;

      // Prepare the data payload with all required fields
      const requestData = {
        field_of_study: formData.field_of_study.trim(),
        department: departmentId,
        college: collegeId, // Include the college ID
        code: formattedCode,
        description: formData.description.trim(), // Include description
        status: formData.status
      };

      console.log('Request data:', requestData);
      console.log('JSON data:', JSON.stringify(requestData, null, 2));
      console.log('Department ID type:', typeof departmentId, 'Value:', departmentId);
      console.log('College ID type:', typeof collegeId, 'Value:', collegeId);
      console.log('Code type:', typeof formData.code.trim(), 'Value:', formData.code.trim() || null);
      console.log('Description type:', typeof formData.description.trim(), 'Value:', formData.description.trim());
      console.log('Status type:', typeof formData.status, 'Value:', formData.status);

      try {
        // Import axios
        const axios = (await import('axios')).default;

        console.log(`Making request with axios: ${endpoint}`);
        console.log('Request headers:', {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        });

        // Make the request using axios
        const response = await axios.post(endpoint, requestData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Axios response:', response);
        console.log('Response status:', response.status);
        console.log('Response data:', response.data);

        toast.success('Field of study added successfully');
        setIsAddDialogOpen(false);
        resetForm();
        fetchFieldsOfStudy(); // Refresh the list after adding
      } catch (error) {
        console.error('Axios error:', error);

        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.log('Error response data:', error.response.data);
          console.log('Error response status:', error.response.status);
          console.log('Error response headers:', error.response.headers);
          console.log('Full error response:', JSON.stringify(error.response.data, null, 2));

          // Check if there are non-field errors
          if (error.response.data && error.response.data.non_field_errors) {
            console.log('Non-field errors:', error.response.data.non_field_errors);
          }

          // Check if there are field errors
          const fieldErrors = {};
          for (const key in error.response.data) {
            if (key !== 'detail' && key !== 'non_field_errors') {
              fieldErrors[key] = error.response.data[key];
            }
          }
          if (Object.keys(fieldErrors).length > 0) {
            console.log('Field errors:', fieldErrors);
          }

          // Handle specific error cases
          const responseData = error.response.data;
          if (responseData && responseData.detail) {
            toast.error(`Error: ${responseData.detail}`);
          } else if (responseData && responseData.field_of_study) {
            const fieldError = Array.isArray(responseData.field_of_study)
              ? responseData.field_of_study[0]
              : responseData.field_of_study;
            toast.error(`Field of Study Error: ${fieldError}`);
            setFormErrors(prev => ({ ...prev, field_of_study: fieldError }));
          } else if (responseData && responseData.department) {
            const deptError = Array.isArray(responseData.department)
              ? responseData.department[0]
              : responseData.department;
            toast.error(`Department Error: ${deptError}`);
            setFormErrors(prev => ({ ...prev, department: deptError }));
          } else if (responseData && responseData.college) {
            const collegeError = Array.isArray(responseData.college)
              ? responseData.college[0]
              : responseData.college;
            toast.error(`College Error: ${collegeError}`);
            setFormErrors(prev => ({ ...prev, college: collegeError }));
          } else if (responseData && responseData.code) {
            const codeError = Array.isArray(responseData.code)
              ? responseData.code[0]
              : responseData.code;
            toast.error(`Code Error: ${codeError}`);
            setFormErrors(prev => ({ ...prev, code: codeError }));
          } else {
            toast.error(`Failed to add field of study: ${error.response.status}`);
          }
        } else if (error.request) {
          // The request was made but no response was received
          console.log('Error request:', error.request);
          toast.error('No response received from server');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.log('Error message:', error.message);
          toast.error(`Error: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('Error adding field of study:', error);
      toast.error(`Failed to add field of study: ${error.message}`);
    }
  };

  const handleEditFieldOfStudy = async () => {
    if (!currentFieldOfStudy) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit a field of study');
        return;
      }

      // Get the correct endpoint
      const baseEndpoint = getApiEndpoint();
      console.log(`Using endpoint for editing: ${baseEndpoint}`);

      // Prepare the data based on the endpoint
      let requestData: { [key: string]: any };
      if (baseEndpoint.includes('study-fields')) {
        // Format for study-fields endpoint
        // Find the selected department
        const selectedDepartment = departments.find(d => d.id.toString() === formData.department);

        // Make sure we have a valid college ID
        let collegeId;
        if (selectedDepartment && selectedDepartment.college) {
          // Use the college from the selected department
          collegeId = selectedDepartment.college;
        } else if (formData.college) {
          // Fallback to the selected college
          collegeId = parseInt(formData.college);
        } else {
          // If no college is found, show an error
          setFormErrors(prev => ({ ...prev, college: 'College is required' }));
          throw new Error('College is required');
        }

        // For study-fields endpoint, we need to send the department and college as integers (pk values)
        // Always use the college ID from the selected department to ensure consistency
        const selectedDept = departments.find(d => d.id.toString() === formData.department);
        if (!selectedDept) {
          throw new Error('Selected department not found');
        }

        // Use the college ID from the selected department
        const collegeIdFromDept = selectedDept.college;

        // Format the code field (remove non-alphanumeric characters and convert to uppercase)
        const formattedCode = formData.code.trim()
          ? formData.code.trim().replace(/[^A-Za-z0-9]/g, '').toUpperCase()
          : null;

        requestData = {
          field_of_study: formData.field_of_study.trim(),
          department: parseInt(formData.department),
          college: collegeIdFromDept, // Use the college ID from the department
          description: formData.description.trim(),
          status: formData.status,
          code: formattedCode
        };
      } else {
        // Format for fields-of-study endpoint
        // For fields-of-study endpoint, we need to send the department as an integer (pk value)

        // Format the code field (remove non-alphanumeric characters and convert to uppercase)
        const formattedCode = formData.code.trim()
          ? formData.code.trim().replace(/[^A-Za-z0-9]/g, '').toUpperCase()
          : null;

        requestData = {
          name: formData.field_of_study.trim(),
          department: parseInt(formData.department),
          code: formattedCode
        };
      }

      console.log('Sending update data:', requestData);
      console.log('College ID type:', typeof formData.college, 'Value:', formData.college);
      console.log('Department ID type:', typeof formData.department, 'Value:', formData.department);

      // Check for duplicate field of study in the same department
      const departmentId = parseInt(formData.department);
      const fieldOfStudyName = formData.field_of_study.trim();
      const existingFieldOfStudy = fieldsOfStudy.find(fos =>
        fos.department === departmentId &&
        fos.field_of_study.toLowerCase() === fieldOfStudyName.toLowerCase() &&
        (!currentFieldOfStudy || fos.id !== currentFieldOfStudy.id)
      );

      if (existingFieldOfStudy) {
        console.warn('WARNING: Duplicate field of study found:', existingFieldOfStudy);
      }

      // Log the exact JSON being sent
      console.log('Request body JSON:', JSON.stringify(requestData, null, 2));

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentFieldOfStudy.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);

        // Reset form errors
        const newErrors = {
          field_of_study: '',
          department: '',
          college: '',
          description: '',
          code: ''
        };

        let hasFieldErrors = false;

        // Check if there are field-specific errors
        if (errorData.field_of_study) {
          const fieldError = Array.isArray(errorData.field_of_study)
            ? errorData.field_of_study[0]
            : errorData.field_of_study;
          newErrors.field_of_study = fieldError;
          hasFieldErrors = true;
        }
        if (errorData.department) {
          const deptError = Array.isArray(errorData.department)
            ? errorData.department[0]
            : errorData.department;
          newErrors.department = deptError;
          hasFieldErrors = true;
        }
        if (errorData.college) {
          const collegeError = Array.isArray(errorData.college)
            ? errorData.college[0]
            : errorData.college;
          newErrors.college = collegeError;
          hasFieldErrors = true;
        }
        if (errorData.code) {
          const codeError = Array.isArray(errorData.code)
            ? errorData.code[0]
            : errorData.code;
          newErrors.code = codeError;
          hasFieldErrors = true;
        }

        // Update form errors
        setFormErrors(newErrors);

        // Log the full error response for debugging
        console.log('Full error response:', JSON.stringify(errorData, null, 2));

        // Determine the error message to display
        let errorMessage;
        if (errorData.detail) {
          errorMessage = errorData.detail;
          // If we get the generic error message, add more context
          if (errorMessage === 'Could not update field of study. Please check that all fields are valid.' ||
              errorMessage === 'Could not create field of study. Please check that all fields are valid.') {
            if (hasFieldErrors) {
              // If we have field errors, include them in the message
              const fieldErrors = [];
              if (newErrors.field_of_study) fieldErrors.push(`Field of Study: ${newErrors.field_of_study}`);
              if (newErrors.department) fieldErrors.push(`Department: ${newErrors.department}`);
              if (newErrors.college) fieldErrors.push(`College: ${newErrors.college}`);
              if (newErrors.description) fieldErrors.push(`Description: ${newErrors.description}`);
              if (newErrors.code) fieldErrors.push(`Code: ${newErrors.code}`);

              if (fieldErrors.length > 0) {
                errorMessage = `${errorMessage} Issues found: ${fieldErrors.join(', ')}`;
              }
            }
          }
        } else if (hasFieldErrors) {
          // Create a more detailed error message from the field errors
          const fieldErrors = [];
          if (newErrors.field_of_study) fieldErrors.push(`Field of Study: ${newErrors.field_of_study}`);
          if (newErrors.department) fieldErrors.push(`Department: ${newErrors.department}`);
          if (newErrors.college) fieldErrors.push(`College: ${newErrors.college}`);
          if (newErrors.description) fieldErrors.push(`Description: ${newErrors.description}`);
          if (newErrors.code) fieldErrors.push(`Code: ${newErrors.code}`);

          if (fieldErrors.length > 0) {
            errorMessage = `Please fix the following errors: ${fieldErrors.join(', ')}`;
          } else {
            errorMessage = 'Please fix the errors in the form';
          }
        } else if (errorData.non_field_errors) {
          errorMessage = Array.isArray(errorData.non_field_errors)
            ? errorData.non_field_errors[0]
            : errorData.non_field_errors;
        } else {
          errorMessage = `Error: ${response.status}`;
        }

        throw new Error(errorMessage);
      }

      toast.success('Field of study updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchFieldsOfStudy(); // Refresh the list after editing
    } catch (error) {
      console.error('Error updating field of study:', error);
      toast.error(`Failed to update field of study: ${error.message}`);
    }
  };

  const handleDeleteFieldOfStudy = async () => {
    if (!currentFieldOfStudy) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete a field of study');
        return;
      }

      // Use the specific delete endpoint from the URL pattern
      const deleteUrl = `http://localhost:8000/api/study-fields/delete/${currentFieldOfStudy.id}/`;
      console.log(`Using delete endpoint: ${deleteUrl}`);

      // Import axios
      const axios = (await import('axios')).default;

      // Make the request using axios instead of fetch
      try {
        const response = await axios.delete(deleteUrl, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Delete response:', response);

        // Success!
        toast.success('Field of study deleted successfully');
        setIsDeleteDialogOpen(false);
        fetchFieldsOfStudy(); // Refresh the list after deleting
      } catch (axiosError) {
        console.error('Delete error:', axiosError);

        // Show detailed error message
        const errorMessage = axiosError.response?.data?.detail || axiosError.message;
        throw new Error(`Failed to delete field of study: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error deleting field of study:', error);
      toast.error(`Failed to delete field of study: ${error.message}`);
    }
  };

  const openEditDialog = (fieldOfStudy: FieldOfStudy) => {
    setCurrentFieldOfStudy(fieldOfStudy);
    setFormData({
      field_of_study: fieldOfStudy.field_of_study,
      department: fieldOfStudy.department.toString(),
      college: fieldOfStudy.college?.toString() || '',
      description: fieldOfStudy.description || '',
      code: fieldOfStudy.code || '',
      status: fieldOfStudy.status
    });
    setFormErrors({
      field_of_study: '',
      department: '',
      college: '',
      description: '',
      code: ''
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (fieldOfStudy: FieldOfStudy) => {
    setCurrentFieldOfStudy(fieldOfStudy);
    setIsDeleteDialogOpen(true);
  };

  // Filter fields of study based on search term, status, college, and department
  const filteredFieldsOfStudy = useMemo(() => {
    if (!Array.isArray(fieldsOfStudy)) return [];

    return fieldsOfStudy.filter((fieldOfStudy) => {
      // Filter by search term
      const departmentName = fieldOfStudy.department_name || getDepartmentName(fieldOfStudy.department);
      const matchesSearch =
        fieldOfStudy.field_of_study.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (typeof departmentName === 'string' && departmentName.toLowerCase().includes(searchTerm.toLowerCase()));

      // Filter by status
      let matchesStatus = true;
      if (statusFilter === 'active') {
        matchesStatus = fieldOfStudy.status === true;
      } else if (statusFilter === 'inactive') {
        matchesStatus = fieldOfStudy.status === false;
      }

      // Filter by college
      let matchesCollege = true;
      if (formData.college && formData.college !== '' && formData.college !== 'all') {
        matchesCollege = fieldOfStudy.college.toString() === formData.college;
      }

      // Filter by department
      let matchesDepartment = true;
      if (formData.department && formData.department !== '' && formData.department !== 'all') {
        matchesDepartment = fieldOfStudy.department.toString() === formData.department;
      }

      return matchesSearch && matchesStatus && matchesCollege && matchesDepartment;
    });
  }, [fieldsOfStudy, searchTerm, statusFilter, formData.college, formData.department]);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredFieldsOfStudy.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredFieldsOfStudy.length / itemsPerPage);

  // Reset to first page when any filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter, formData.college, formData.department]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Pencil className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Fields of Study Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete fields of study for the application portal
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                className="border-blue-200 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200"
                onClick={fetchFieldsOfStudy}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Field of Study
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                        <Plus className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <DialogTitle className="text-xl font-semibold text-gray-900">Add New Field of Study</DialogTitle>
                        <DialogDescription className="text-gray-600 mt-1">
                          Create a new field of study by providing the required information below
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>
                  <div className="p-6 space-y-8">
                    {formErrors.general && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center">
                          <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          <p className="text-sm text-red-700">{formErrors.general}</p>
                        </div>
                      </div>
                    )}

                    {/* Academic Structure Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Building className="h-4 w-4 text-blue-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Academic Structure</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <Label htmlFor="college" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            College
                          </Label>
                          <Select
                            value={formData.college}
                            onValueChange={(value) => {
                              setFormData({
                                ...formData,
                                college: value,
                                department: '' // Reset department when college changes
                              });
                              if (formErrors.college) {
                                setFormErrors({
                                  ...formErrors,
                                  college: ''
                                });
                              }
                            }}
                          >
                            <SelectTrigger
                              id="college"
                              className={cn(
                                "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                                formErrors.college ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              )}
                            >
                              <SelectValue placeholder="Choose a college..." />
                            </SelectTrigger>
                            <SelectContent className="max-h-60">
                              {colleges.map(college => (
                                <SelectItem key={college.id} value={college.id.toString()}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{college.name}</span>
                                    {college.code && (
                                      <span className="text-sm text-gray-500">{college.code}</span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {formErrors.college ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.college}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Select the college that contains the department</p>
                          )}
                        </div>
                        <div className="space-y-3">
                          <Label htmlFor="department" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Department
                          </Label>
                          <Select
                            value={formData.department}
                            onValueChange={handleSelectChange}
                            disabled={!formData.college}
                          >
                            <SelectTrigger
                              id="department"
                              className={cn(
                                "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                                formErrors.department ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '',
                                !formData.college ? 'bg-gray-50 cursor-not-allowed' : ''
                              )}
                            >
                              <SelectValue placeholder={!formData.college ? "Select college first..." : "Choose a department..."} />
                            </SelectTrigger>
                            <SelectContent>
                              {departments
                                .filter(department => department.college.toString() === formData.college)
                                .map((department) => (
                                  <SelectItem key={department.id} value={department.id.toString()}>
                                    <div className="flex items-center">
                                      <Building className="h-4 w-4 mr-2 text-[#1a73c0]" />
                                      {department.name}
                                    </div>
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                          {formErrors.department ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.department}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Select the department for this field of study</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Field Details Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <FileText className="h-4 w-4 text-green-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Field Information</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <Label htmlFor="field_of_study" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Field of Study Name
                          </Label>
                          <Input
                            id="field_of_study"
                            name="field_of_study"
                            value={formData.field_of_study}
                            onChange={handleInputChange}
                            placeholder="Computer Science, Mathematics, Biology..."
                            className={cn(
                              "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                              formErrors.field_of_study ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                            )}
                          />
                          {formErrors.field_of_study ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.field_of_study}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Enter the full name of the field of study (3-200 characters)</p>
                          )}
                        </div>
                        <div className="space-y-3">
                          <Label htmlFor="code" className="text-sm font-semibold text-gray-700">
                            Field Code
                          </Label>
                          <Input
                            id="code"
                            name="code"
                            value={formData.code}
                            onChange={handleInputChange}
                            placeholder="CS, MATH, BIO..."
                            className={cn(
                              "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                              formErrors.code ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                            )}
                          />
                          {formErrors.code ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.code}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Optional unique code (letters and numbers only, max 20 characters)</p>
                          )}
                        </div>
                      </div>

                      <div className="space-y-3">
                        <Label htmlFor="description" className="text-sm font-semibold text-gray-700">
                          Description
                        </Label>
                        <Textarea
                          id="description"
                          name="description"
                          value={formData.description}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
                          placeholder="Provide a detailed description of this field of study..."
                          rows={3}
                          className={cn(
                            "border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                            formErrors.description ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                          )}
                        />
                        {formErrors.description ? (
                          <p className="text-sm text-red-600 flex items-center">
                            <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            {formErrors.description}
                          </p>
                        ) : (
                          <p className="text-xs text-gray-500">Optional description to help users understand this field of study</p>
                        )}
                      </div>
                    </div>

                    {/* Status Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <CheckCircle className="h-4 w-4 text-purple-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
                      </div>

                      <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <input
                              type="checkbox"
                              id="status"
                              checked={formData.status}
                              onChange={(e) => setFormData({ ...formData, status: e.target.checked })}
                              className="h-5 w-5 rounded border-gray-300 text-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                            />
                            <Label htmlFor="status" className="text-base font-semibold text-gray-700 cursor-pointer">
                              Field of Study Status
                            </Label>
                          </div>
                          <span className={cn(
                            "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                            formData.status
                              ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                              : "bg-gray-100 text-gray-800 border border-gray-200"
                          )}>
                            {formData.status ? "✓ Active" : "○ Inactive"}
                          </span>
                        </div>

                        <div className={cn(
                          "p-4 rounded-lg border transition-all duration-200",
                          formData.status
                            ? "bg-green-50 border-green-200"
                            : "bg-gray-50 border-gray-200"
                        )}>
                          <div className="flex items-start space-x-3">
                            {formData.status ? (
                              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                            ) : (
                              <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                            )}
                            <div>
                              <p className={cn(
                                "text-sm font-medium mb-1",
                                formData.status ? "text-green-800" : "text-gray-700"
                              )}>
                                {formData.status ? "Field of Study is Active" : "Field of Study is Inactive"}
                              </p>
                              <p className="text-xs text-gray-600 leading-relaxed">
                                {formData.status
                                  ? "This field of study will be available for selection in applications and will appear in all relevant lists."
                                  : "This field of study will be hidden from applications and will not be available for selection."}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
                    <div className="flex items-center justify-between w-full">
                      <div className="text-xs text-gray-500">
                        <span className="text-red-500">*</span> Required fields
                      </div>
                      <div className="flex space-x-3">
                        <DialogClose asChild>
                          <Button
                            variant="outline"
                            className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                          >
                            Cancel
                          </Button>
                        </DialogClose>
                        <Button
                          onClick={handleAddFieldOfStudy}
                          className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Create Field of Study
                        </Button>
                      </div>
                    </div>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Loading state */}
          {loading && (
            <div className="flex justify-center items-center h-64">
              <div className="flex flex-col justify-center items-center space-y-3">
                <div className="bg-blue-100 p-3 rounded-full">
                  <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                </div>
                <div className="text-[#1a73c0] font-medium">Loading fields of study...</div>
                <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
              </div>
            </div>
          )}

          {!loading && (
            <>
              <div className="mb-6 space-y-4">
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
                  <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                    <Search className="h-4 w-4 mr-2" />
                    Search & Filter
                  </h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                      <Input
                        placeholder="Search fields of study..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                      />
                    </div>
                    <div className="flex gap-3">
                      <Select
                        value={statusFilter}
                        onValueChange={(value) => setStatusFilter(value)}
                      >
                        <SelectTrigger className="w-[150px] border-blue-200 focus:ring-blue-400 shadow-sm">
                          <SelectValue placeholder="Filter by Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select
                        value={formData.college || 'all'}
                        onValueChange={(value) => {
                          setFormData({
                            ...formData,
                            college: value === 'all' ? '' : value,
                            department: '' // Reset department when college changes
                          });
                        }}
                      >
                        <SelectTrigger className="w-[180px] border-blue-200 focus:ring-blue-400 shadow-sm">
                          <SelectValue placeholder="Filter by College" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Colleges</SelectItem>
                          {colleges.map(college => (
                            <SelectItem key={college.id} value={college.id.toString()}>
                              {college.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Select
                        value={formData.department || 'all'}
                        onValueChange={handleFilterDepartmentChange}
                        disabled={!formData.college}
                      >
                        <SelectTrigger className="w-[180px] border-blue-200 focus:ring-blue-400 shadow-sm">
                          <SelectValue placeholder="Filter by Department" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Departments</SelectItem>
                          {departments
                            .filter(department => !formData.college || formData.college === 'all' || department.college.toString() === formData.college)
                            .map((department) => (
                              <SelectItem key={department.id} value={department.id.toString()}>
                                {department.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
                {(searchTerm || statusFilter !== 'all' || (formData.college && formData.college !== 'all') || (formData.department && formData.department !== 'all')) && (
                  <div className="p-3 border border-blue-100 rounded-md bg-white shadow-sm">
                    <div className="flex flex-wrap items-center gap-2">
                      <span className="text-sm font-medium text-[#1a73c0] flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                        </svg>
                        Active Filters:
                      </span>

                      {searchTerm && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                          Search: {searchTerm}
                          <button
                            onClick={() => setSearchTerm('')}
                            className="ml-2 hover:text-blue-900 transition-colors"
                            aria-label="Remove search filter"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </span>
                      )}

                      {statusFilter !== 'all' && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                          Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                          <button
                            onClick={() => setStatusFilter('all')}
                            className="ml-2 hover:text-blue-900 transition-colors"
                            aria-label="Remove status filter"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </span>
                      )}

                      {formData.college && formData.college !== 'all' && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                          College: {colleges.find(c => c.id.toString() === formData.college)?.name || formData.college}
                          <button
                            onClick={() => setFormData({...formData, college: '', department: ''})}
                            className="ml-2 hover:text-blue-900 transition-colors"
                            aria-label="Remove college filter"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </span>
                      )}

                      {formData.department && formData.department !== 'all' && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                          Department: {departments.find(d => d.id.toString() === formData.department)?.name || formData.department}
                          <button
                            onClick={() => setFormData({...formData, department: ''})}
                            className="ml-2 hover:text-blue-900 transition-colors"
                            aria-label="Remove department filter"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </span>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSearchTerm('');
                          setStatusFilter('all');
                          setFormData({...formData, college: '', department: ''});
                        }}
                        className="text-xs h-7 px-3 ml-auto border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Clear All
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* Fields of Study Table */}
              <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                      <TableRow>
                        <TableHead className="text-[#1a73c0] font-medium">College</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Department</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Field of Study</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Code</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Status</TableHead>
                        <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredFieldsOfStudy.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-12">
                            <div className="flex flex-col justify-center items-center space-y-3">
                              <div className="bg-gray-100 p-3 rounded-full">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <div className="text-gray-700 font-medium">No fields of study found</div>
                              <div className="text-sm text-gray-500 max-w-sm text-center">
                                {searchTerm ?
                                  'Try adjusting your search criteria to find what you\'re looking for.' :
                                  'There are no fields of study available. Click the "Add Field of Study" button to create one.'}
                              </div>

                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        currentItems.map((fieldOfStudy) => (
                          <TableRow key={fieldOfStudy.id} className="hover:bg-blue-50 transition-colors">
                            <TableCell>{fieldOfStudy.college_name || getCollegeName(fieldOfStudy.college) || 'Loading...'}</TableCell>
                            <TableCell>{fieldOfStudy.department_name || 'Loading...'}</TableCell>
                            <TableCell className="font-medium text-[#1a73c0]">{fieldOfStudy.field_of_study}</TableCell>
                            <TableCell>{fieldOfStudy.code || '-'}</TableCell>
                            <TableCell>
                              {fieldOfStudy.status ? (
                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200 shadow-sm">
                                  <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-green-600"></span>
                                  Active
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200 shadow-sm">
                                  <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-red-600"></span>
                                  Inactive
                                </span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openEditDialog(fieldOfStudy)}
                                  title="Edit"
                                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openDeleteDialog(fieldOfStudy)}
                                  className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                                  title="Delete"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </>
          )}
        </CardContent>
        <CardFooter>
          {filteredFieldsOfStudy.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredFieldsOfStudy.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredFieldsOfStudy.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Dynamic page number buttons with ellipsis for large page counts */}
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === number
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show a condensed version with ellipsis
                    <>
                      {/* Always show first page */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === 1
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {currentPage > 3 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number =>
                          number > 1 &&
                          number < totalPages &&
                          (
                            number === currentPage - 1 ||
                            number === currentPage ||
                            number === currentPage + 1 ||
                            (currentPage <= 3 && number <= 4) ||
                            (currentPage >= totalPages - 2 && number >= totalPages - 3)
                          )
                        )
                        .map(number => (
                          <Button
                            key={number}
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={cn(
                              "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                              currentPage === number
                                ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                                : "bg-white text-gray-700 hover:bg-blue-50"
                            )}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {currentPage < totalPages - 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === totalPages
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Pencil className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">Edit Field of Study</DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Update the field of study information below
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6 space-y-8">
            {formErrors.general && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  <p className="text-sm text-red-700">{formErrors.general}</p>
                </div>
              </div>
            )}

            {/* Academic Structure Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Building className="h-4 w-4 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Academic Structure</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="edit-college" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    College
                  </Label>
                  <Select
                    value={formData.college}
                    onValueChange={(value) => {
                      setFormData({
                        ...formData,
                        college: value,
                        department: '' // Reset department when college changes
                      });
                      if (formErrors.college) {
                        setFormErrors({
                          ...formErrors,
                          college: ''
                        });
                      }
                    }}
                  >
                    <SelectTrigger
                      id="edit-college"
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.college ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    >
                      <SelectValue placeholder="Choose a college..." />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      {colleges.map(college => (
                        <SelectItem key={college.id} value={college.id.toString()}>
                          <div className="flex flex-col">
                            <span className="font-medium">{college.name}</span>
                            {college.code && (
                              <span className="text-sm text-gray-500">{college.code}</span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.college ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.college}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Select the college that contains the department</p>
                  )}
                </div>
                <div className="space-y-3">
                  <Label htmlFor="edit-department" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Department
                  </Label>
                  <Select
                    value={formData.department}
                    onValueChange={handleSelectChange}
                    disabled={!formData.college}
                  >
                    <SelectTrigger
                      id="edit-department"
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.department ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '',
                        !formData.college ? 'bg-gray-50 cursor-not-allowed' : ''
                      )}
                    >
                      <SelectValue placeholder={!formData.college ? "Select college first..." : "Choose a department..."} />
                    </SelectTrigger>
                    <SelectContent>
                      {departments
                        .filter(department => department.college.toString() === formData.college)
                        .map((department) => (
                          <SelectItem key={department.id} value={department.id.toString()}>
                            <div className="flex items-center">
                              <Building className="h-4 w-4 mr-2 text-[#1a73c0]" />
                              {department.name}
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  {formErrors.department ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.department}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Select the department for this field of study</p>
                  )}
                </div>
              </div>
            </div>

            {/* Field Details Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FileText className="h-4 w-4 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Field Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="edit-field_of_study" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Field of Study Name
                  </Label>
                  <Input
                    id="edit-field_of_study"
                    name="field_of_study"
                    value={formData.field_of_study}
                    onChange={handleInputChange}
                    placeholder="Computer Science, Mathematics, Biology..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.field_of_study ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.field_of_study ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.field_of_study}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter the full name of the field of study (3-200 characters)</p>
                  )}
                </div>
                <div className="space-y-3">
                  <Label htmlFor="edit-code" className="text-sm font-semibold text-gray-700">
                    Field Code
                  </Label>
                  <Input
                    id="edit-code"
                    name="code"
                    value={formData.code}
                    onChange={handleInputChange}
                    placeholder="CS, MATH, BIO..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.code ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.code ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.code}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Optional unique code (letters and numbers only, max 20 characters)</p>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <Label htmlFor="edit-description" className="text-sm font-semibold text-gray-700">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Provide a detailed description of this field of study..."
                  rows={3}
                  className={cn(
                    "border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                    formErrors.description ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                  )}
                />
                {formErrors.description ? (
                  <p className="text-sm text-red-600 flex items-center">
                    <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {formErrors.description}
                  </p>
                ) : (
                  <p className="text-xs text-gray-500">Optional description to help users understand this field of study</p>
                )}
              </div>
            </div>

            {/* Status Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="edit-status"
                      checked={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.checked })}
                      className="h-5 w-5 rounded border-gray-300 text-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                    />
                    <Label htmlFor="edit-status" className="text-base font-semibold text-gray-700 cursor-pointer">
                      Field of Study Status
                    </Label>
                  </div>
                  <span className={cn(
                    "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                    formData.status
                      ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                      : "bg-gray-100 text-gray-800 border border-gray-200"
                  )}>
                    {formData.status ? "✓ Active" : "○ Inactive"}
                  </span>
                </div>

                <div className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  formData.status
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                )}>
                  <div className="flex items-start space-x-3">
                    {formData.status ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={cn(
                        "text-sm font-medium mb-1",
                        formData.status ? "text-green-800" : "text-gray-700"
                      )}>
                        {formData.status ? "Field of Study is Active" : "Field of Study is Inactive"}
                      </p>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {formData.status
                          ? "This field of study will be available for selection in applications and will appear in all relevant lists."
                          : "This field of study will be hidden from applications and will not be available for selection."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="text-xs text-gray-500">
                <span className="text-red-500">*</span> Required fields
              </div>
              <div className="flex space-x-3">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={handleEditFieldOfStudy}
                  className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Update Field of Study
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the field of study "{currentFieldOfStudy?.field_of_study}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteFieldOfStudy}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ApplicationFieldOfStudyManagement;
