import { useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { Loader2, AlertCircle, FileText, User, Award, CreditCard } from 'lucide-react';
import { applicationAPI } from '@/services/api';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

const NewApplication = () => {
  const [loading, setLoading] = useState(true);
  const [hasPersonalInfo, setHasPersonalInfo] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const checkPersonalInfo = async () => {
      try {
        setLoading(true);
        // Check if the user already has personal information
        const personalInfoResponse = await applicationAPI.getCurrentApplicantInfo();
        console.log('Personal info check response:', personalInfoResponse.data);

        // If the user has personal information, set the flag to true
        if (personalInfoResponse.data && personalInfoResponse.data.length > 0) {
          setHasPersonalInfo(true);
        }
      } catch (error) {
        console.error('Error checking personal information:', error);
        setError('Failed to check personal information. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    checkPersonalInfo();
  }, []);

  const startNewApplication = async () => {
    try {
      setIsCreating(true);

      if (!hasPersonalInfo) {
        // If no personal info, redirect to personal info page
        navigate('/application/personal-info');
        return;
      }

      // Instead of creating a GAT entry here, just redirect to the GAT page with new=true
      // The GAT will be created only when the user submits the GAT form
      navigate('/application/gat?new=true');
    } catch (error) {
      console.error('Error starting new application:', error);
      toast.error('Failed to start new application. Please try again.');
      setIsCreating(false);
    }
  };

  const cancelApplication = () => {
    navigate('/application/status');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <Card className="mb-8 shadow-md">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
              <CardTitle className="text-xl text-gray-800">Starting New Application</CardTitle>
              <CardDescription>Checking your information...</CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <div className="flex items-center justify-center h-64">
                <div className="flex flex-col items-center">
                  <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-6" />
                  <p className="text-gray-700 text-lg mb-2">Checking your information...</p>
                  <p className="text-gray-500">Please wait while we prepare your application</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <Card className="mb-8 shadow-md">
            <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50 border-b border-red-100">
              <CardTitle className="text-xl text-gray-800">Error</CardTitle>
              <CardDescription>There was a problem starting your application</CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <div className="flex items-center justify-center h-64">
                <div className="flex flex-col items-center">
                  <AlertCircle className="h-12 w-12 text-red-500 mb-6" />
                  <p className="text-red-600 text-lg font-medium mb-4">{error}</p>
                  <p className="text-gray-600 mb-8">Please try again or contact support.</p>
                  <Button
                    onClick={() => window.location.reload()}
                    className="bg-[#1a73c0] hover:bg-[#0e4a7d]"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Show confirmation screen
  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        <Card className="mb-8 shadow-md border-0 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
            <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                 style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
            <div className="relative z-10">
              <CardTitle className="text-2xl font-bold text-white">Start New Application</CardTitle>
              <CardDescription className="text-blue-100 mt-1">
                {hasPersonalInfo
                  ? "You're about to start a new graduate program application"
                  : "You need to complete your personal information first"}
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="p-8">
            <div className="space-y-6">
              {hasPersonalInfo ? (
                <>



                </>
              ) : (
                <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-100">
                  <div className="flex items-start">
                    <AlertCircle className="h-6 w-6 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-medium text-yellow-800 mb-2">Personal Information Required</h3>
                      <p className="text-yellow-700 mb-4">
                        Before starting a new application, you need to complete your personal information first.
                      </p>
                      <p className="text-yellow-700 text-sm">
                        You'll be redirected to the personal information form when you click "Continue".
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>

          <CardFooter className="bg-gray-50 px-8 py-6 border-t flex justify-end space-x-4">
            <Button
              variant="outline"
              onClick={cancelApplication}
              className="px-6"
            >
              Cancel
            </Button>

            <Button
              onClick={startNewApplication}
              className="bg-[#1a73c0] hover:bg-[#0e4a7d] px-6"
              disabled={isCreating}
            >
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Continue'
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default NewApplication;
