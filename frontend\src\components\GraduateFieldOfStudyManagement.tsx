import React, { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Search, RefreshCw, Plus, Edit, Trash2, Building, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Pencil } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface Department {
  id: number;
  name: string;
  college: number;
  college_name?: string;
}

interface College {
  id: number;
  name: string;
}

interface FieldOfStudy {
  id: number;
  name: string;
  code: string;
  department: number;
  department_name?: string;
  college_name?: string;
  duration?: number;
  duration_display?: string;
}

const GraduateFieldOfStudyManagement = () => {
  const [fieldsOfStudy, setFieldsOfStudy] = useState<FieldOfStudy[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // CRUD state variables
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentFieldOfStudy, setCurrentFieldOfStudy] = useState<FieldOfStudy | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    department: '',
    duration: '',
  });
  const [formErrors, setFormErrors] = useState({
    name: '',
    code: '',
    department: '',
    duration: '',
  });
  const [apiEndpoint, setApiEndpoint] = useState<string | null>(null);

  // Fetch fields of study, departments, and colleges on component mount
  useEffect(() => {
    fetchFieldsOfStudy();
    fetchDepartments();
    fetchColleges();
    determineApiEndpoint();
  }, []);

  const fetchFieldsOfStudy = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to view fields of study');
        setLoading(false);
        return;
      }

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try verification fields-of-study endpoints
        { url: 'http://localhost:8000/api/verification-fields-of-study/', auth: true },
        { url: 'http://localhost:8000/api/verification-fields-of-study/public/', auth: false },
        // Try regular fields-of-study endpoints
        { url: 'http://localhost:8000/api/fields-of-study/', auth: true },
        { url: 'http://localhost:8000/api/fields-of-study/public/', auth: false },
        // Try study-fields endpoint
        { url: 'http://localhost:8000/api/study-fields/', auth: true },
        { url: 'http://localhost:8000/api/study-fields/public/', auth: false }
      ];

      let fieldsOfStudyData = null;
      let workingEndpoint = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint.url}`);
          const headers: HeadersInit = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            headers['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            fieldsOfStudyData = await response.json();
            workingEndpoint = endpoint.url;
            console.log(`Successfully fetched data from ${endpoint.url}`);
            break;
          }
        } catch (endpointError) {
          console.error(`Error with endpoint ${endpoint.url}:`, endpointError);
        }
      }

      if (!fieldsOfStudyData) {
        throw new Error('Could not fetch fields of study from any endpoint');
      }

      console.log('Fields of study data:', fieldsOfStudyData);

      // Process fields of study data based on the endpoint format
      let processedFields = [];

      if (workingEndpoint && workingEndpoint.includes('verification-fields-of-study')) {
        // For verification-fields-of-study endpoint
        processedFields = fieldsOfStudyData.map((field: any) => ({
          id: field.id,
          name: field.name,
          code: field.code || '',
          department: field.department,
          department_name: field.department_name || '',
          college_name: field.college_name || '',
          duration: field.duration || null,
          duration_display: field.duration_display || null
        }));
      } else if (workingEndpoint && workingEndpoint.includes('fields-of-study')) {
        // For fields-of-study endpoint
        processedFields = fieldsOfStudyData.map((field: any) => ({
          id: field.id,
          name: field.name,
          code: field.code || '',
          department: field.department,
          department_name: field.department_name || '',
          college_name: field.college_name || '',
          duration: field.duration || null,
          duration_display: field.duration_display || null
        }));
      } else if (workingEndpoint && workingEndpoint.includes('study-fields')) {
        // For study-fields endpoint
        processedFields = fieldsOfStudyData.map((field: any) => ({
          id: field.id,
          name: field.field_of_study,
          code: field.code || '',
          department: field.department,
          department_name: field.department_name || '',
          college_name: field.college_name || '',
          duration: field.duration || null,
          duration_display: field.duration_display || null
        }));
      }

      console.log('Processed fields of study:', processedFields);
      setFieldsOfStudy(processedFields);
    } catch (error) {
      console.error('Error fetching fields of study:', error);
      toast.error(`Failed to fetch fields of study: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to view departments');
        return;
      }

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try verification departments endpoints
        { url: 'http://localhost:8000/api/verification-departments/', auth: true },
        { url: 'http://localhost:8000/api/verification-departments/public/', auth: false },
        // Try regular departments endpoints
        { url: 'http://localhost:8000/api/departments/', auth: true },
        { url: 'http://localhost:8000/api/departments/public/', auth: false },
        // Try setup departments endpoint
        { url: 'http://localhost:8000/api/setup/departments/', auth: true }
      ];

      let departmentData = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying department endpoint: ${endpoint.url}`);
          const headers: HeadersInit = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            headers['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            departmentData = await response.json();
            console.log(`Successfully fetched departments from ${endpoint.url}`);
            break;
          }
        } catch (endpointError) {
          console.error(`Error with department endpoint ${endpoint.url}:`, endpointError);
        }
      }

      if (!departmentData) {
        throw new Error('Could not fetch departments from any endpoint');
      }

      console.log('Department data:', departmentData);
      setDepartments(departmentData);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast.error(`Failed to fetch departments: ${error.message}`);
    }
  };

  const fetchColleges = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to view colleges');
        return;
      }

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try verification colleges endpoints
        { url: 'http://localhost:8000/api/verification-colleges/', auth: true },
        { url: 'http://localhost:8000/api/verification-colleges/public/', auth: false },
        // Try regular colleges endpoints
        { url: 'http://localhost:8000/api/colleges/', auth: true },
        { url: 'http://localhost:8000/api/colleges/public/', auth: false },
        // Try setup colleges endpoint
        { url: 'http://localhost:8000/api/setup/colleges/', auth: true }
      ];

      let collegeData = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying college endpoint: ${endpoint.url}`);
          const headers: HeadersInit = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            headers['Authorization'] = `Bearer ${token}`;
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            collegeData = await response.json();
            console.log(`Successfully fetched colleges from ${endpoint.url}`);
            break;
          }
        } catch (endpointError) {
          console.error(`Error with college endpoint ${endpoint.url}:`, endpointError);
        }
      }

      if (!collegeData) {
        throw new Error('Could not fetch colleges from any endpoint');
      }

      console.log('College data:', collegeData);
      setColleges(collegeData);
    } catch (error) {
      console.error('Error fetching colleges:', error);
      toast.error(`Failed to fetch colleges: ${error.message}`);
    }
  };

  const determineApiEndpoint = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to determine API endpoint');
        return;
      }

      // Try all possible endpoints in sequence
      const endpoints = [
        'http://localhost:8000/api/verification-fields-of-study/',
        'http://localhost:8000/api/fields-of-study/'
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`Testing endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.ok) {
            console.log(`Found working endpoint: ${endpoint}`);
            setApiEndpoint(endpoint);
            return endpoint;
          }
        } catch (error) {
          console.error(`Error testing endpoint ${endpoint}:`, error);
        }
      }

      console.warn('Could not determine API endpoint');
      return null;
    } catch (error) {
      console.error('Error determining API endpoint:', error);
      return null;
    }
  };

  const getDepartmentName = (departmentId: number): string => {
    const department = departments.find(d => d.id === departmentId);
    return department ? department.name : `Department ${departmentId}`;
  };

  const getCollegeName = (collegeId: number): string => {
    const college = colleges.find(c => c.id === collegeId);
    return college ? college.name : `College ${collegeId}`;
  };

  // Form validation
  const validateForm = () => {
    let valid = true;
    const newErrors = { name: '', code: '', department: '', duration: '' };

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      valid = false;
    } else if (formData.name.length > 200) {
      newErrors.name = 'Name must be less than 200 characters';
      valid = false;
    }

    // Validate code (optional but must be unique if provided)
    if (formData.code.trim()) {
      if (formData.code.length > 20) {
        newErrors.code = 'Code must be less than 20 characters';
        valid = false;
      } else if (!/^[A-Za-z0-9]+$/.test(formData.code.trim())) {
        newErrors.code = 'Code must contain only letters and numbers';
        valid = false;
      } else {
        // Check for duplicate code in the same department
        const departmentId = parseInt(formData.department);
        const code = formData.code.trim().toUpperCase();

        // Only check for duplicates if we have a valid department and code
        if (departmentId && code) {
          const existingFieldOfStudy = fieldsOfStudy.find(fos =>
            fos.department === departmentId &&
            fos.code?.toUpperCase() === code &&
            (!currentFieldOfStudy || fos.id !== currentFieldOfStudy.id)
          );

          if (existingFieldOfStudy) {
            newErrors.code = 'A field of study with this code already exists in the selected department';
            valid = false;
          }
        }
      }
    }

    // Validate department
    if (!formData.department) {
      newErrors.department = 'Department is required';
      valid = false;
    }

    // Validate duration (optional but must be a positive number if provided)
    if (formData.duration.trim()) {
      const durationValue = parseInt(formData.duration);
      if (isNaN(durationValue)) {
        newErrors.duration = 'Duration must be a number';
        valid = false;
      } else if (durationValue <= 0) {
        newErrors.duration = 'Duration must be a positive number';
        valid = false;
      } else if (durationValue > 10) {
        newErrors.duration = 'Duration cannot exceed 10 years';
        valid = false;
      }
    }

    setFormErrors(newErrors);
    return valid;
  };

  // Form input handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Auto-format code to uppercase and remove non-alphanumeric characters
    let newValue = value;
    if (name === 'code') {
      newValue = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    } else if (name === 'duration') {
      // Only allow positive numbers for duration
      newValue = value.replace(/[^0-9]/g, '');
    }

    setFormData({
      ...formData,
      [name]: newValue,
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // CRUD operations
  const handleAddFieldOfStudy = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to add a field of study');
        return;
      }

      if (!apiEndpoint) {
        const endpoint = await determineApiEndpoint();
        if (!endpoint) {
          toast.error('Could not determine API endpoint');
          return;
        }
      }

      // Find the selected department to get its college
      const departmentId = parseInt(formData.department);
      const selectedDept = departments.find(d => d.id === departmentId);
      if (!selectedDept) {
        toast.error('Selected department not found');
        return;
      }

      // Get the college ID from the selected department
      const collegeId = selectedDept.college;

      // Format the code field (remove non-alphanumeric characters and convert to uppercase)
      const formattedCode = formData.code.trim()
        ? formData.code.trim().replace(/[^A-Za-z0-9]/g, '').toUpperCase()
        : null;

      // Prepare the data payload based on the endpoint format
      let requestData: any;

      if (apiEndpoint && apiEndpoint.includes('verification-fields-of-study')) {
        // For verification-fields-of-study endpoint
        requestData = {
          name: formData.name.trim(),
          department: departmentId,
          code: formattedCode,
          duration: formData.duration.trim() ? parseInt(formData.duration.trim()) : null
        };
      } else if (apiEndpoint && apiEndpoint.includes('fields-of-study')) {
        // For fields-of-study endpoint
        requestData = {
          name: formData.name.trim(),
          department: departmentId,
          code: formattedCode,
          duration: formData.duration.trim() ? parseInt(formData.duration.trim()) : null
        };
      } else {
        toast.error('Unknown API endpoint format');
        return;
      }

      console.log('Request data:', requestData);

      const response = await fetch(apiEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);

        if (errorData.detail) {
          toast.error(`Error: ${errorData.detail}`);
        } else if (errorData.name) {
          toast.error(`Name Error: ${errorData.name}`);
        } else if (errorData.code) {
          toast.error(`Code Error: ${errorData.code}`);
        } else if (errorData.department) {
          toast.error(`Department Error: ${errorData.department}`);
        } else {
          toast.error(`Failed to add field of study: ${response.status}`);
        }
        return;
      }

      const newFieldOfStudy = await response.json();
      console.log('New field of study:', newFieldOfStudy);

      toast.success('Field of study added successfully');
      setIsAddDialogOpen(false);
      setFormData({ name: '', code: '', department: '', duration: '' });
      fetchFieldsOfStudy(); // Refresh the list
    } catch (error) {
      console.error('Error adding field of study:', error);
      toast.error(`Failed to add field of study: ${error.message}`);
    }
  };

  const handleEditFieldOfStudy = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to edit a field of study');
        return;
      }

      if (!apiEndpoint || !currentFieldOfStudy) {
        toast.error('Missing API endpoint or field of study to edit');
        return;
      }

      // Find the selected department to get its college
      const departmentId = parseInt(formData.department);
      const selectedDept = departments.find(d => d.id === departmentId);
      if (!selectedDept) {
        toast.error('Selected department not found');
        return;
      }

      // Format the code field (remove non-alphanumeric characters and convert to uppercase)
      const formattedCode = formData.code.trim()
        ? formData.code.trim().replace(/[^A-Za-z0-9]/g, '').toUpperCase()
        : null;

      // Prepare the data payload based on the endpoint format
      let requestData: any;

      if (apiEndpoint.includes('verification-fields-of-study')) {
        // For verification-fields-of-study endpoint
        requestData = {
          name: formData.name.trim(),
          department: departmentId,
          code: formattedCode,
          duration: formData.duration.trim() ? parseInt(formData.duration.trim()) : null
        };
      } else if (apiEndpoint.includes('fields-of-study')) {
        // For fields-of-study endpoint
        requestData = {
          name: formData.name.trim(),
          department: departmentId,
          code: formattedCode,
          duration: formData.duration.trim() ? parseInt(formData.duration.trim()) : null
        };
      } else {
        toast.error('Unknown API endpoint format');
        return;
      }

      console.log('Request data for edit:', requestData);

      const response = await fetch(`${apiEndpoint}${currentFieldOfStudy.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);

        if (errorData.detail) {
          toast.error(`Error: ${errorData.detail}`);
        } else if (errorData.name) {
          toast.error(`Name Error: ${errorData.name}`);
        } else if (errorData.code) {
          toast.error(`Code Error: ${errorData.code}`);
        } else if (errorData.department) {
          toast.error(`Department Error: ${errorData.department}`);
        } else {
          toast.error(`Failed to update field of study: ${response.status}`);
        }
        return;
      }

      const updatedFieldOfStudy = await response.json();
      console.log('Updated field of study:', updatedFieldOfStudy);

      toast.success('Field of study updated successfully');
      setIsEditDialogOpen(false);
      setCurrentFieldOfStudy(null);
      setFormData({ name: '', code: '', department: '', duration: '' });
      fetchFieldsOfStudy(); // Refresh the list
    } catch (error) {
      console.error('Error updating field of study:', error);
      toast.error(`Failed to update field of study: ${error.message}`);
    }
  };

  const handleDeleteFieldOfStudy = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to delete a field of study');
        return;
      }

      if (!apiEndpoint || !currentFieldOfStudy) {
        toast.error('Missing API endpoint or field of study to delete');
        return;
      }

      const response = await fetch(`${apiEndpoint}${currentFieldOfStudy.id}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        // For DELETE, we might not get a JSON response
        let errorMessage = `Failed to delete field of study: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (e) {
          // If we can't parse JSON, just use the status code message
        }

        toast.error(errorMessage);
        return;
      }

      toast.success('Field of study deleted successfully');
      setIsDeleteDialogOpen(false);
      setCurrentFieldOfStudy(null);
      fetchFieldsOfStudy(); // Refresh the list
    } catch (error) {
      console.error('Error deleting field of study:', error);
      toast.error(`Failed to delete field of study: ${error.message}`);
    }
  };

  // Dialog handlers
  const openAddDialog = () => {
    setFormData({ name: '', code: '', department: '', duration: '' });
    setFormErrors({ name: '', code: '', department: '', duration: '' });
    setIsAddDialogOpen(true);
  };

  const openEditDialog = (fieldOfStudy: FieldOfStudy) => {
    setCurrentFieldOfStudy(fieldOfStudy);
    setFormData({
      name: fieldOfStudy.name,
      code: fieldOfStudy.code || '',
      department: fieldOfStudy.department.toString(),
      duration: fieldOfStudy.duration ? fieldOfStudy.duration.toString() : '',
    });
    setFormErrors({ name: '', code: '', department: '', duration: '' });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (fieldOfStudy: FieldOfStudy) => {
    setCurrentFieldOfStudy(fieldOfStudy);
    setIsDeleteDialogOpen(true);
  };

  // Filter fields of study based on search term
  const filteredFieldsOfStudy = useMemo(() => {
    if (!Array.isArray(fieldsOfStudy)) return [];

    return fieldsOfStudy.filter((fieldOfStudy) => {
      const departmentName = fieldOfStudy.department_name || getDepartmentName(fieldOfStudy.department);
      const duration = fieldOfStudy.duration ? fieldOfStudy.duration.toString() : '';

      return (
        fieldOfStudy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (fieldOfStudy.code && fieldOfStudy.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (typeof departmentName === 'string' && departmentName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (duration && duration.includes(searchTerm))
      );
    });
  }, [fieldsOfStudy, departments, searchTerm]);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredFieldsOfStudy.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredFieldsOfStudy.length / itemsPerPage);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6 overflow-visible">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Graduate Fields of Study</CardTitle>
                <CardDescription className="mt-1">
                  Manage fields of study for graduate verification
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm" onClick={openAddDialog}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Field of Study
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-[95vw] sm:max-w-lg lg:max-w-xl mx-4 sm:mx-auto max-h-[90vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
                        <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">Add New Field of Study</DialogTitle>
                        <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                          Enter the details for the new field of study
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>

                  <div className="p-4 sm:p-5 lg:p-6 space-y-4 lg:space-y-5">
                    {/* Department Selection */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-[#1a73c0]" />
                        <Label htmlFor="department" className="text-sm font-semibold text-gray-800">Department *</Label>
                      </div>
                      <Select
                        value={formData.department}
                        onValueChange={(value) => handleSelectChange('department', value)}
                      >
                        <SelectTrigger className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.department
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}>
                          <SelectValue placeholder="Choose a department..." />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {departments.map((department) => (
                            <SelectItem
                              key={department.id}
                              value={department.id.toString()}
                              className="py-3 px-4 hover:bg-blue-50 focus:bg-blue-50"
                            >
                              <div className="flex items-center space-x-2">
                                <Building className="h-4 w-4 text-[#1a73c0]" />
                                <span>{department.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.department && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.department}</p>
                        </div>
                      )}
                    </div>
                    {/* Field of Study Name */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        <Label htmlFor="name" className="text-sm font-semibold text-gray-800">Field of Study Name *</Label>
                      </div>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="e.g. Computer Science, Mathematics, Biology"
                        className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.name
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.name && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.name}</p>
                        </div>
                      )}
                    </div>

                    {/* Code */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                        </svg>
                        <Label htmlFor="code" className="text-sm font-semibold text-gray-800">Code</Label>
                        <span className="text-xs text-gray-500">(Optional)</span>
                      </div>
                      <Input
                        id="code"
                        name="code"
                        value={formData.code}
                        onChange={handleInputChange}
                        placeholder="e.g. CS, MATH, BIO"
                        className={cn(
                          "h-10 text-sm transition-all duration-200 font-mono",
                          formErrors.code
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.code && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.code}</p>
                        </div>
                      )}
                    </div>

                    {/* Duration */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <Label htmlFor="duration" className="text-sm font-semibold text-gray-800">Duration (Years)</Label>
                        <span className="text-xs text-gray-500">(Optional)</span>
                      </div>
                      <Input
                        id="duration"
                        name="duration"
                        value={formData.duration}
                        onChange={handleInputChange}
                        placeholder="e.g. 2, 3, 4"
                        className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.duration
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.duration && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.duration}</p>
                        </div>
                      )}
                      <p className="text-xs text-gray-500 flex items-center space-x-1">
                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>Enter the program duration in years</span>
                      </p>
                    </div>
                  </div>

                  <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t">
                    <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
                      <DialogClose asChild>
                        <Button
                          variant="outline"
                          className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                        >
                          Cancel
                        </Button>
                      </DialogClose>
                      <Button
                        type="submit"
                        onClick={handleAddFieldOfStudy}
                        className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create Field of Study
                      </Button>
                    </div>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search Fields of Study
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search by department, name, code or duration..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="rounded-lg border border-blue-200 overflow-hidden shadow-sm">
            <Table>
              <TableHeader>
                <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
                  <TableHead className="font-semibold text-[#1a73c0]">Department</TableHead>
                  <TableHead className="font-semibold text-[#1a73c0]">Field of Study</TableHead>
                  <TableHead className="font-semibold text-[#1a73c0]">Code</TableHead>
                  <TableHead className="font-semibold text-[#1a73c0]">Duration (Years)</TableHead>
                  <TableHead className="text-right font-semibold text-[#1a73c0] w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-5 w-5 animate-spin text-[#1a73c0]" />
                        <span className="text-gray-600">Loading fields of study...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredFieldsOfStudy.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-12">
                      <div className="flex flex-col items-center justify-center space-y-3">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                          <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="text-gray-700 font-medium">No fields of study found</div>
                        <div className="text-sm text-gray-500 max-w-sm text-center">
                          {searchTerm ?
                            'Try adjusting your search criteria to find what you\'re looking for.' :
                            'There are no fields of study available. Click the "Add Field of Study" button to create one.'}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentItems.map((fieldOfStudy) => (
                    <TableRow key={fieldOfStudy.id} className="hover:bg-blue-50 transition-colors">
                      <TableCell className="font-medium">{fieldOfStudy.department_name || getDepartmentName(fieldOfStudy.department)}</TableCell>
                      <TableCell className="font-medium text-[#1a73c0]">{fieldOfStudy.name}</TableCell>
                      <TableCell className="text-gray-600">{fieldOfStudy.code || '-'}</TableCell>
                      <TableCell className="text-gray-600">{fieldOfStudy.duration || '-'}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(fieldOfStudy)}
                            title="Edit"
                            className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDeleteDialog(fieldOfStudy)}
                            title="Delete"
                            className="h-8 w-8 p-0 border-red-200 hover:bg-red-100 hover:text-red-700 transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter>
          {filteredFieldsOfStudy.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredFieldsOfStudy.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredFieldsOfStudy.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-lg lg:max-w-xl mx-4 sm:mx-auto max-h-[90vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
                <Pencil className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">Edit Field of Study</DialogTitle>
                <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                  Update the details for this field of study
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-4 sm:p-5 lg:p-6 space-y-4 lg:space-y-5">
            {/* Department Selection */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-[#1a73c0]" />
                <Label htmlFor="edit-department" className="text-sm font-semibold text-gray-800">Department *</Label>
              </div>
              <Select
                value={formData.department}
                onValueChange={(value) => handleSelectChange('department', value)}
              >
                <SelectTrigger className={cn(
                  "h-10 text-sm transition-all duration-200",
                  formErrors.department
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}>
                  <SelectValue placeholder="Choose a department..." />
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  {departments.map((department) => (
                    <SelectItem
                      key={department.id}
                      value={department.id.toString()}
                      className="py-3 px-4 hover:bg-blue-50 focus:bg-blue-50"
                    >
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-[#1a73c0]" />
                        <span>{department.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formErrors.department && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.department}</p>
                </div>
              )}
            </div>

            {/* Field of Study Name */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <Label htmlFor="edit-name" className="text-sm font-semibold text-gray-800">Field of Study Name *</Label>
              </div>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="e.g. Computer Science, Mathematics, Biology"
                className={cn(
                  "h-10 text-sm transition-all duration-200",
                  formErrors.name
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}
              />
              {formErrors.name && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.name}</p>
                </div>
              )}
            </div>

            {/* Code */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                </svg>
                <Label htmlFor="edit-code" className="text-sm font-semibold text-gray-800">Code</Label>
                <span className="text-xs text-gray-500">(Optional)</span>
              </div>
              <Input
                id="edit-code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                placeholder="e.g. CS, MATH, BIO"
                className={cn(
                  "h-10 text-sm transition-all duration-200 font-mono",
                  formErrors.code
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}
              />
              {formErrors.code && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.code}</p>
                </div>
              )}
            </div>

            {/* Duration */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <Label htmlFor="edit-duration" className="text-sm font-semibold text-gray-800">Duration (Years)</Label>
                <span className="text-xs text-gray-500">(Optional)</span>
              </div>
              <Input
                id="edit-duration"
                name="duration"
                value={formData.duration}
                onChange={handleInputChange}
                placeholder="e.g. 2, 3, 4"
                className={cn(
                  "h-10 text-sm transition-all duration-200",
                  formErrors.duration
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}
              />
              {formErrors.duration && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.duration}</p>
                </div>
              )}
              <p className="text-xs text-gray-500 flex items-center space-x-1">
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span>Enter the program duration in years</span>
              </p>
            </div>
          </div>

          <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t">
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
              <DialogClose asChild>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                onClick={handleEditFieldOfStudy}
                className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Pencil className="h-4 w-4 mr-2" />
                Update Field of Study
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-pink-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-red-500 rounded-lg shadow-sm">
                <Trash2 className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-red-600">Confirm Deletion</DialogTitle>
                <DialogDescription className="mt-1">
                  Are you sure you want to delete "{currentFieldOfStudy?.name}"? This action cannot be undone.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">
                <strong>Warning:</strong> Deleting this field of study will permanently remove it from the system.
                This action cannot be undone.
              </p>
            </div>
          </div>
          <DialogFooter className="bg-gray-50 px-4 py-3 rounded-b-lg border-t">
            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button variant="outline" className="border-gray-300 hover:bg-gray-100">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                variant="destructive"
                onClick={handleDeleteFieldOfStudy}
                className="bg-red-600 hover:bg-red-700 transition-all duration-200"
              >
                Delete Field of Study
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GraduateFieldOfStudyManagement;
