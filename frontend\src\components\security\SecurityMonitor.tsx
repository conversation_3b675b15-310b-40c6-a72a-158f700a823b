import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  AlertTriangle, 
  Users, 
  Activity, 
  RefreshCw,
  Download,
  Eye,
  Lock,
  UserX
} from 'lucide-react';
import { useRBAC } from '@/contexts/RBACContext';
import { authAPI } from '@/services/api';
import { toast } from 'sonner';

interface SecurityEvent {
  id: string;
  timestamp: string;
  event_type: 'access_denied' | 'invalid_login' | 'role_violation' | 'permission_check';
  user?: string;
  details: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface UserAuditResult {
  user_id: number;
  username: string;
  email: string;
  is_staff: boolean;
  is_superuser: boolean;
  is_active: boolean;
  roles: string[];
  permission_count: number;
  issues: string[];
  last_login?: string;
  department?: string;
}

const SecurityMonitor: React.FC = () => {
  const { user, isSuperuser, isAdmin } = useRBAC();
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [userAuditResults, setUserAuditResults] = useState<UserAuditResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalStaff: 0,
    usersWithIssues: 0,
    criticalIssues: 0,
    lastAuditTime: null as string | null,
  });

  // Check if user has access to security monitoring
  const hasSecurityAccess = isSuperuser || isAdmin;

  useEffect(() => {
    if (hasSecurityAccess) {
      loadSecurityData();
    }
  }, [hasSecurityAccess]);

  const loadSecurityData = async () => {
    setIsLoading(true);
    try {
      // Load user audit results
      const auditResponse = await authAPI.validateRoles();
      if (auditResponse.data) {
        setUserAuditResults(auditResponse.data.validation_results || []);
        setStats({
          totalStaff: auditResponse.data.total_staff || 0,
          usersWithIssues: auditResponse.data.users_with_issues || 0,
          criticalIssues: auditResponse.data.validation_results?.filter(
            (u: UserAuditResult) => u.issues.length > 0
          ).length || 0,
          lastAuditTime: new Date().toISOString(),
        });
      }

      // Generate mock security events for demonstration
      generateMockSecurityEvents();
      
      toast.success('Security data loaded successfully');
    } catch (error) {
      console.error('Failed to load security data:', error);
      toast.error('Failed to load security data');
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockSecurityEvents = () => {
    const events: SecurityEvent[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        event_type: 'access_denied',
        user: 'staff_user_1',
        details: 'Staff user without assigned roles attempted to access admin panel',
        severity: 'high',
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        event_type: 'permission_check',
        user: 'registrar_1',
        details: 'Permission check failed for user management access',
        severity: 'medium',
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 10800000).toISOString(),
        event_type: 'role_violation',
        user: 'dept_head_1',
        details: 'User attempted to access superuser-only functionality',
        severity: 'high',
      },
    ];
    setSecurityEvents(events);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const exportSecurityReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      stats,
      userAuditResults,
      securityEvents,
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Security report exported successfully');
  };

  if (!hasSecurityAccess) {
    return (
      <Card>
        <CardContent className="pt-6">
          <Alert>
            <Lock className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to access security monitoring features.
              This section requires administrator or superuser privileges.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Security Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{stats.totalStaff}</p>
                <p className="text-xs text-muted-foreground">Total Staff Users</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold">{stats.usersWithIssues}</p>
                <p className="text-xs text-muted-foreground">Users with Issues</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-2xl font-bold">{stats.criticalIssues}</p>
                <p className="text-xs text-muted-foreground">Critical Issues</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{securityEvents.length}</p>
                <p className="text-xs text-muted-foreground">Security Events</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Security Monitoring Interface */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Security Monitoring</span>
            </CardTitle>
            <CardDescription>
              Monitor user access, role assignments, and security events
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadSecurityData}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportSecurityReport}
            >
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="audit" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="audit">User Audit</TabsTrigger>
              <TabsTrigger value="events">Security Events</TabsTrigger>
            </TabsList>

            <TabsContent value="audit" className="space-y-4">
              <div className="space-y-4">
                {userAuditResults.map((user) => (
                  <Card key={user.user_id} className={user.issues.length > 0 ? 'border-red-200' : ''}>
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {user.issues.length > 0 ? (
                              <UserX className="h-4 w-4 text-red-600" />
                            ) : (
                              <Users className="h-4 w-4 text-green-600" />
                            )}
                            <div>
                              <p className="font-medium">{user.username}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                          </div>
                          <div className="flex space-x-1">
                            {user.is_superuser && <Badge variant="destructive">Superuser</Badge>}
                            {user.is_staff && <Badge variant="secondary">Staff</Badge>}
                            {!user.is_active && <Badge variant="outline">Inactive</Badge>}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm">
                            Roles: {user.roles.length > 0 ? user.roles.join(', ') : 'None'}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {user.permission_count} permissions
                          </p>
                        </div>
                      </div>
                      {user.issues.length > 0 && (
                        <div className="mt-3 space-y-1">
                          {user.issues.map((issue, index) => (
                            <Alert key={index} variant="destructive" className="py-2">
                              <AlertTriangle className="h-3 w-3" />
                              <AlertDescription className="text-xs ml-2">
                                {issue}
                              </AlertDescription>
                            </Alert>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="events" className="space-y-4">
              <div className="space-y-4">
                {securityEvents.map((event) => (
                  <Card key={event.id}>
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Eye className="h-4 w-4 text-blue-600" />
                          <div>
                            <p className="font-medium">{event.details}</p>
                            <p className="text-sm text-muted-foreground">
                              User: {event.user || 'Unknown'} • {new Date(event.timestamp).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <Badge variant={getSeverityColor(event.severity) as any}>
                          {event.severity.toUpperCase()}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityMonitor;

// Export additional security components
export { SecurityMonitor };
