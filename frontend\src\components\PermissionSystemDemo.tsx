import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { createPermissionChecker } from '@/utils/permissionChecker';
import { 
  hasMenuAccess, 
  getAccessibleMenus, 
  getAccessibleCategories,
  getUserAccessLevel,
  debugUserMenuAccess 
} from '@/utils/menuPermissionsNew';
import { MENU_PERMISSIONS, FEATURE_PERMISSIONS } from '@/utils/permissionMappings';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  Info, 
  Zap,
  Users,
  Key,
  Settings
} from 'lucide-react';

/**
 * Permission System Demo Component
 * Demonstrates the new permission-only access control system
 */
export const PermissionSystemDemo: React.FC = () => {
  const { user } = useAuth();
  const [selectedMenu, setSelectedMenu] = useState<string>('');
  const [selectedFeature, setSelectedFeature] = useState<string>('');

  const checker = createPermissionChecker(user);
  const accessibleMenus = getAccessibleMenus(user);
  const accessibleCategories = getAccessibleCategories(user);
  const userAccessLevel = getUserAccessLevel(user);

  const handleDebugPermissions = () => {
    debugUserMenuAccess(user);
  };

  const testMenuAccess = (menuKey: string) => {
    const result = checker.checkMenuAccess(menuKey);
    return result;
  };

  const testFeatureAccess = (featureKey: string) => {
    const result = checker.checkFeatureAccess(featureKey);
    const details = checker.getFeatureAccessDetails(featureKey);
    return { result, details };
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Refactored Permission System Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Users className="h-4 w-4" />
                User Info
              </h4>
              <div className="space-y-1 text-sm">
                <p><strong>Username:</strong> {user?.username || 'Not logged in'}</p>
                <p><strong>Access Level:</strong> {userAccessLevel}</p>
                <div className="flex gap-1">
                  <Badge variant={user?.is_active ? "default" : "destructive"}>
                    {user?.is_active ? "Active" : "Inactive"}
                  </Badge>
                  <Badge variant={user?.is_staff ? "default" : "secondary"}>
                    {user?.is_staff ? "Staff" : "Regular"}
                  </Badge>
                  {user?.is_superuser && (
                    <Badge variant="default">Superuser</Badge>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Key className="h-4 w-4" />
                Permissions
              </h4>
              <div className="space-y-1 text-sm">
                <p><strong>Total:</strong> {user?.permissions?.length || 0}</p>
                <p><strong>Groups:</strong> {user?.role_names?.length || 0}</p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleDebugPermissions}
                  className="mt-1"
                >
                  Debug Console
                </Button>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Menu Access
              </h4>
              <div className="space-y-1 text-sm">
                <p><strong>Accessible Menus:</strong> {accessibleMenus.length}</p>
                <p><strong>Categories:</strong> {Object.keys(accessibleCategories).length}</p>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                System Info
              </h4>
              <div className="space-y-1 text-sm">
                <Badge variant="outline" className="text-xs">
                  Permission-Only System
                </Badge>
                <Badge variant="outline" className="text-xs">
                  No Group Dependencies
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">System Overview</TabsTrigger>
          <TabsTrigger value="menu-test">Menu Testing</TabsTrigger>
          <TabsTrigger value="comparison">Before vs After</TabsTrigger>
        </TabsList>

        {/* System Overview */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Accessible Menu Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(accessibleCategories).map(([category, menus]) => (
                    <div key={category} className="border rounded p-3">
                      <h4 className="font-medium mb-2">{category.replace('_', ' ')}</h4>
                      <div className="flex flex-wrap gap-1">
                        {menus.map((menu) => (
                          <Badge key={menu} variant="outline" className="text-xs">
                            {menu}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                  {Object.keys(accessibleCategories).length === 0 && (
                    <p className="text-muted-foreground">No accessible menu categories</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">System Benefits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">No Hardcoded Groups</p>
                      <p className="text-sm text-muted-foreground">
                        Access control based purely on Django permissions
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Automatic Adaptation</p>
                      <p className="text-sm text-muted-foreground">
                        UI adapts automatically when permissions change
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Scalable</p>
                      <p className="text-sm text-muted-foreground">
                        No frontend code changes needed for new groups
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Maintainable</p>
                      <p className="text-sm text-muted-foreground">
                        Single source of truth in Django permissions
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Menu Testing */}
        <TabsContent value="menu-test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Menu Access Testing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-3">Select Menu to Test</h4>
                  <select
                    value={selectedMenu}
                    onChange={(e) => setSelectedMenu(e.target.value)}
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select a menu...</option>
                    {Object.keys(MENU_PERMISSIONS).map((menuKey) => (
                      <option key={menuKey} value={menuKey}>
                        {menuKey}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Test Result</h4>
                  {selectedMenu && (
                    <div className="space-y-2">
                      {(() => {
                        const result = testMenuAccess(selectedMenu);
                        return (
                          <div className="flex items-center gap-2">
                            {result.hasAccess ? (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            ) : (
                              <XCircle className="h-5 w-5 text-red-500" />
                            )}
                            <span className={result.hasAccess ? "text-green-700" : "text-red-700"}>
                              {result.hasAccess ? "Access Granted" : "Access Denied"}
                            </span>
                          </div>
                        );
                      })()}

                      {selectedMenu && (
                        <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                          <p><strong>Required Permissions:</strong></p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {MENU_PERMISSIONS[selectedMenu]?.permissions?.map((perm) => (
                              <Badge key={perm} variant="outline" className="text-xs">
                                {perm}
                              </Badge>
                            )) || <span className="text-muted-foreground">None specified</span>}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Before vs After Comparison */}
        <TabsContent value="comparison" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-red-700">❌ Old System</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="font-medium">Hardcoded Group Lists</p>
                    <code className="text-xs bg-red-50 p-1 rounded">
                      groups: ['Service Manager', 'Verification Clerk']
                    </code>
                  </div>

                  <div>
                    <p className="font-medium">Manual Updates Required</p>
                    <p className="text-muted-foreground">
                      Frontend code changes needed for new groups
                    </p>
                  </div>

                  <div>
                    <p className="font-medium">Dual Dependencies</p>
                    <p className="text-muted-foreground">
                      Both permissions AND groups checked
                    </p>
                  </div>

                  <div>
                    <p className="font-medium">Maintenance Overhead</p>
                    <p className="text-muted-foreground">
                      Multiple files to update for access changes
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-green-700">✅ New System</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="font-medium">Permission-Only Access</p>
                    <code className="text-xs bg-green-50 p-1 rounded">
                      permissions: ['view_service', 'change_document']
                    </code>
                  </div>

                  <div>
                    <p className="font-medium">Automatic Adaptation</p>
                    <p className="text-muted-foreground">
                      No frontend changes needed for new groups
                    </p>
                  </div>

                  <div>
                    <p className="font-medium">Single Source of Truth</p>
                    <p className="text-muted-foreground">
                      Only Django permissions matter
                    </p>
                  </div>

                  <div>
                    <p className="font-medium">Zero Maintenance</p>
                    <p className="text-muted-foreground">
                      Manage access entirely through Django admin
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5 text-blue-500" />
                Migration Benefits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-green-700 mb-2">✅ Backward Compatible</h4>
                  <p className="text-muted-foreground">
                    Existing functionality preserved during transition
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-green-700 mb-2">✅ Gradual Migration</h4>
                  <p className="text-muted-foreground">
                    Can migrate components one by one
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-green-700 mb-2">✅ Same UX</h4>
                  <p className="text-muted-foreground">
                    Users see no difference in functionality
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PermissionSystemDemo;
    const result = checker.checkFeatureAccess(featureKey);
    const details = checker.getFeatureAccessDetails(featureKey);
    return { result, details };
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Refactored Permission System Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Users className="h-4 w-4" />
                User Info
              </h4>
              <div className="space-y-1 text-sm">
                <p><strong>Username:</strong> {user?.username || 'Not logged in'}</p>
                <p><strong>Access Level:</strong> {userAccessLevel}</p>
                <div className="flex gap-1">
                  <Badge variant={user?.is_active ? "default" : "destructive"}>
                    {user?.is_active ? "Active" : "Inactive"}
                  </Badge>
                  <Badge variant={user?.is_staff ? "default" : "secondary"}>
                    {user?.is_staff ? "Staff" : "Regular"}
                  </Badge>
                  {user?.is_superuser && (
                    <Badge variant="default">Superuser</Badge>
                  )}
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Key className="h-4 w-4" />
                Permissions
              </h4>
              <div className="space-y-1 text-sm">
                <p><strong>Total:</strong> {user?.permissions?.length || 0}</p>
                <p><strong>Groups:</strong> {user?.role_names?.length || 0}</p>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleDebugPermissions}
                  className="mt-1"
                >
                  Debug Console
                </Button>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Menu Access
              </h4>
              <div className="space-y-1 text-sm">
                <p><strong>Accessible Menus:</strong> {accessibleMenus.length}</p>
                <p><strong>Categories:</strong> {Object.keys(accessibleCategories).length}</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                System Info
              </h4>
              <div className="space-y-1 text-sm">
                <Badge variant="outline" className="text-xs">
                  Permission-Only System
                </Badge>
                <Badge variant="outline" className="text-xs">
                  No Group Dependencies
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">System Overview</TabsTrigger>
          <TabsTrigger value="menu-test">Menu Testing</TabsTrigger>
          <TabsTrigger value="feature-test">Feature Testing</TabsTrigger>
          <TabsTrigger value="comparison">Before vs After</TabsTrigger>
        </TabsList>

        {/* System Overview */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Accessible Menu Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(accessibleCategories).map(([category, menus]) => (
                    <div key={category} className="border rounded p-3">
                      <h4 className="font-medium mb-2">{category.replace('_', ' ')}</h4>
                      <div className="flex flex-wrap gap-1">
                        {menus.map((menu) => (
                          <Badge key={menu} variant="outline" className="text-xs">
                            {menu}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                  {Object.keys(accessibleCategories).length === 0 && (
                    <p className="text-muted-foreground">No accessible menu categories</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">System Benefits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">No Hardcoded Groups</p>
                      <p className="text-sm text-muted-foreground">
                        Access control based purely on Django permissions
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Automatic Adaptation</p>
                      <p className="text-sm text-muted-foreground">
                        UI adapts automatically when permissions change
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Scalable</p>
                      <p className="text-sm text-muted-foreground">
                        No frontend code changes needed for new groups
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Maintainable</p>
                      <p className="text-sm text-muted-foreground">
                        Single source of truth in Django permissions
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Menu Testing */}
        <TabsContent value="menu-test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Menu Access Testing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-3">Select Menu to Test</h4>
                  <select 
                    value={selectedMenu}
                    onChange={(e) => setSelectedMenu(e.target.value)}
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select a menu...</option>
                    {Object.keys(MENU_PERMISSIONS).map((menuKey) => (
                      <option key={menuKey} value={menuKey}>
                        {menuKey}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Test Result</h4>
                  {selectedMenu && (
                    <div className="space-y-2">
                      {(() => {
                        const result = testMenuAccess(selectedMenu);
                        return (
                          <div className="flex items-center gap-2">
                            {result.hasAccess ? (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            ) : (
                              <XCircle className="h-5 w-5 text-red-500" />
                            )}
                            <span className={result.hasAccess ? "text-green-700" : "text-red-700"}>
                              {result.hasAccess ? "Access Granted" : "Access Denied"}
                            </span>
                          </div>
                        );
                      })()}
                      
                      {selectedMenu && (
                        <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                          <p><strong>Required Permissions:</strong></p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {MENU_PERMISSIONS[selectedMenu]?.permissions?.map((perm) => (
                              <Badge key={perm} variant="outline" className="text-xs">
                                {perm}
                              </Badge>
                            )) || <span className="text-muted-foreground">None specified</span>}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feature Testing */}
        <TabsContent value="feature-test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Feature Access Testing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-3">Select Feature to Test</h4>
                  <select 
                    value={selectedFeature}
                    onChange={(e) => setSelectedFeature(e.target.value)}
                    className="w-full p-2 border rounded"
                  >
                    <option value="">Select a feature...</option>
                    {Object.keys(FEATURE_PERMISSIONS).map((featureKey) => (
                      <option key={featureKey} value={featureKey}>
                        {featureKey}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Feature Access Details</h4>
                  {selectedFeature && (
                    <div className="space-y-2">
                      {(() => {
                        const { result, details } = testFeatureAccess(selectedFeature);
                        return (
                          <div className="space-y-2">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="flex items-center gap-1">
                                {details.canView ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                                <span>Can View</span>
                              </div>
                              <div className="flex items-center gap-1">
                                {details.canEdit ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                                <span>Can Edit</span>
                              </div>
                              <div className="flex items-center gap-1">
                                {details.canCreate ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                                <span>Can Create</span>
                              </div>
                              <div className="flex items-center gap-1">
                                {details.canDelete ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <XCircle className="h-4 w-4 text-red-500" />
                                )}
                                <span>Can Delete</span>
                              </div>
                            </div>
                            
                            <div className="mt-3">
                              <Badge variant="outline">
                                Access Level: {details.accessLevel}
                              </Badge>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Before vs After Comparison */}
        <TabsContent value="comparison" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-red-700">❌ Old System</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="font-medium">Hardcoded Group Lists</p>
                    <code className="text-xs bg-red-50 p-1 rounded">
                      groups: ['Service Manager', 'Verification Clerk']
                    </code>
                  </div>
                  
                  <div>
                    <p className="font-medium">Manual Updates Required</p>
                    <p className="text-muted-foreground">
                      Frontend code changes needed for new groups
                    </p>
                  </div>
                  
                  <div>
                    <p className="font-medium">Dual Dependencies</p>
                    <p className="text-muted-foreground">
                      Both permissions AND groups checked
                    </p>
                  </div>
                  
                  <div>
                    <p className="font-medium">Maintenance Overhead</p>
                    <p className="text-muted-foreground">
                      Multiple files to update for access changes
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg text-green-700">✅ New System</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div>
                    <p className="font-medium">Permission-Only Access</p>
                    <code className="text-xs bg-green-50 p-1 rounded">
                      permissions: ['view_service', 'change_document']
                    </code>
                  </div>
                  
                  <div>
                    <p className="font-medium">Automatic Adaptation</p>
                    <p className="text-muted-foreground">
                      No frontend changes needed for new groups
                    </p>
                  </div>
                  
                  <div>
                    <p className="font-medium">Single Source of Truth</p>
                    <p className="text-muted-foreground">
                      Only Django permissions matter
                    </p>
                  </div>
                  
                  <div>
                    <p className="font-medium">Zero Maintenance</p>
                    <p className="text-muted-foreground">
                      Manage access entirely through Django admin
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5 text-blue-500" />
                Migration Benefits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-green-700 mb-2">✅ Backward Compatible</h4>
                  <p className="text-muted-foreground">
                    Existing functionality preserved during transition
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-green-700 mb-2">✅ Gradual Migration</h4>
                  <p className="text-muted-foreground">
                    Can migrate components one by one
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-green-700 mb-2">✅ Same UX</h4>
                  <p className="text-muted-foreground">
                    Users see no difference in functionality
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PermissionSystemDemo;
