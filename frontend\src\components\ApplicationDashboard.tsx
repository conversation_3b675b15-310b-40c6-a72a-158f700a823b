import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { FileText, Calendar, Clock, CheckCircle, AlertCircle, UserCheck, UserX, Users, Building, HourglassIcon } from 'lucide-react';
import api from '@/services/api';

// Colors for the pie chart
const COLORS = ['#00C49F', '#FFBB28', '#FF8042'];

// Process status by period data to group by period
const processStatusByPeriodData = (data: any[]) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return [];
  }

  // Group data by period
  const groupedData = data.reduce((acc, item) => {
    const period = item.period;
    if (!acc[period]) {
      acc[period] = {
        period,
        Approved: 0,
        Pending: 0,
        Rejected: 0
      };
    }

    // Add count to the appropriate status
    if (item.status === 'Approved') {
      acc[period].Approved += item.count;
    } else if (item.status === 'Pending') {
      acc[period].Pending += item.count;
    } else if (item.status === 'Rejected') {
      acc[period].Rejected += item.count;
    }

    return acc;
  }, {});

  // Convert the grouped data object to an array
  return Object.values(groupedData);
};

const ApplicationDashboard = () => {
  // State for dashboard data
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState({
    totalApplications: 0,
    newApplications: 0,
    fifteenDayApplicants: 0,
    todayApplicants: 0,
    genderDistribution: {
      all_time: { male: 0, female: 0, total: 0 },
      thirty_day: { male: 0, female: 0, total: 0 },
      fifteen_day: { male: 0, female: 0, total: 0 },
      today: { male: 0, female: 0, total: 0 }
    },
    applicationsByMonth: [],
    statusData: [],
    genderData: [],
    programLevelData: [],
    programLevelByYear: [],
    genderByYear: [],
    statusByPeriod: { period: 'monthly', data: [] },
    registrarMetrics: {
      approved: 0,
      rejected: 0,
      pending: 0,
      total: 0,
      all_total: 0,
      approval_rate: 0,
      rejection_rate: 0,
      pending_rate: 0,
      period_name: '',
      period_start_date: null,
      period_end_date: null
    },
    departmentMetrics: {
      approved: 0,
      rejected: 0,
      pending: 0,
      total: 0,
      all_total: 0,
      approval_rate: 0,
      rejection_rate: 0,
      pending_rate: 0,
      period_name: '',
      period_start_date: null,
      period_end_date: null
    },
    applicantsByCollege: []
  });

  // State for the time period selection
  const [timePeriod, setTimePeriod] = useState('monthly');

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch application statistics with the selected time period
        const response = await api.get(`/application-statistics/?period=${timePeriod}`);

        // Process the data
        const data = response.data;

        // If we have real data, use it
        if (data) {
          setDashboardData({
            totalApplications: data.total_applications || 0,
            newApplications: data.new_applications || 0,
            fifteenDayApplicants: data.fifteen_day_applicants || 0,
            todayApplicants: data.today_applicants || 0,
            genderDistribution: data.gender_distribution || {
              all_time: { male: 0, female: 0, total: 0 },
              thirty_day: { male: 0, female: 0, total: 0 },
              fifteen_day: { male: 0, female: 0, total: 0 },
              today: { male: 0, female: 0, total: 0 }
            },
            applicationsByMonth: data.applications_by_month || [],
            statusData: data.status_data || [],
            genderData: data.gender_data || [],
            programLevelData: data.program_level_data || [],
            programLevelByYear: data.program_level_by_year || [],
            genderByYear: data.gender_by_year || [],
            statusByPeriod: data.status_by_period || { period: timePeriod, data: [] },
            registrarMetrics: data.registrar_metrics || {
              approved: 0,
              rejected: 0,
              pending: 0,
              total: 0,
              all_total: 0,
              approval_rate: 0,
              rejection_rate: 0,
              pending_rate: 0
            },
            departmentMetrics: data.department_metrics || {
              approved: 0,
              rejected: 0,
              pending: 0,
              total: 0,
              all_total: 0,
              approval_rate: 0,
              rejection_rate: 0,
              pending_rate: 0
            },
            applicantsByCollege: data.applicants_by_college || []
          });
        } else {
          // API didn't return expected format
          setError('Invalid data format received from server. Please contact support.');
          setDashboardData({
            totalApplications: 0,
            newApplications: 0,
            fifteenDayApplicants: 0,
            todayApplicants: 0,
            genderDistribution: {
              all_time: { male: 0, female: 0, total: 0 },
              thirty_day: { male: 0, female: 0, total: 0 },
              fifteen_day: { male: 0, female: 0, total: 0 },
              today: { male: 0, female: 0, total: 0 }
            },
            applicationsByMonth: [],
            statusData: [],
            genderData: [],
            programLevelData: [],
            programLevelByYear: [],
            genderByYear: [],
            statusByPeriod: { period: timePeriod, data: [] },
            registrarMetrics: {
              approved: 0,
              rejected: 0,
              total: 0,
              approvalRate: 0,
              rejectionRate: 0
            },
            departmentMetrics: {
              approved: 0,
              rejected: 0,
              total: 0,
              approvalRate: 0,
              rejectionRate: 0
            },
            applicantsByCollege: []
          });
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');

        // Reset dashboard data on error
        setDashboardData({
          totalApplications: 0,
          newApplications: 0,
          fifteenDayApplicants: 0,
          todayApplicants: 0,
          genderDistribution: {
            all_time: { male: 0, female: 0, total: 0 },
            thirty_day: { male: 0, female: 0, total: 0 },
            fifteen_day: { male: 0, female: 0, total: 0 },
            today: { male: 0, female: 0, total: 0 }
          },
          applicationsByMonth: [],
          statusData: [],
          genderData: [],
          programLevelData: [],
          programLevelByYear: [],
          genderByYear: [],
          statusByPeriod: { period: timePeriod, data: [] },
          registrarMetrics: {
            approved: 0,
            rejected: 0,
            pending: 0,
            total: 0,
            all_total: 0,
            approval_rate: 0,
            rejection_rate: 0,
            pending_rate: 0,
            period_name: '',
            period_start_date: null,
            period_end_date: null
          },
          departmentMetrics: {
            approved: 0,
            rejected: 0,
            pending: 0,
            total: 0,
            all_total: 0,
            approval_rate: 0,
            rejection_rate: 0,
            pending_rate: 0,
            period_name: '',
            period_start_date: null,
            period_end_date: null
          },
          applicantsByCollege: []
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [timePeriod]);

  // No mock data generation functions
  return (
    <div className="space-y-6">
      {/* Error Message */}
      {error && (
        <Card className="border-red-300 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <p>{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards - Row 1 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total Applications</CardTitle>
            <CardDescription>All time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : dashboardData.totalApplications.toLocaleString()}
              </div>
            </div>
            <div className="mt-3 pt-3 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-blue-600">Male: {loading ? '...' : dashboardData.genderDistribution.all_time.male}</span>
                <span className="text-pink-600">Female: {loading ? '...' : dashboardData.genderDistribution.all_time.female}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full" onClick={() => window.location.href = '/graduate-admin?tab=applicants'}>
              <FileText className="h-4 w-4 mr-2" />
              View All
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">New Applications</CardTitle>
            <CardDescription>Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : dashboardData.newApplications.toLocaleString()}
              </div>
            </div>
            <div className="mt-3 pt-3 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-blue-600">Male: {loading ? '...' : dashboardData.genderDistribution.thirty_day.male}</span>
                <span className="text-pink-600">Female: {loading ? '...' : dashboardData.genderDistribution.thirty_day.female}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full" onClick={() => window.location.href = '/graduate-admin?tab=applicants'}>
              <FileText className="h-4 w-4 mr-2" />
              View Recent
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">15-Day Applicants</CardTitle>
            <CardDescription>Last 15 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : dashboardData.fifteenDayApplicants.toLocaleString()}
              </div>
            </div>
            <div className="mt-3 pt-3 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-blue-600">Male: {loading ? '...' : dashboardData.genderDistribution.fifteen_day.male}</span>
                <span className="text-pink-600">Female: {loading ? '...' : dashboardData.genderDistribution.fifteen_day.female}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full" onClick={() => window.location.href = '/graduate-admin?tab=applicants'}>
              <FileText className="h-4 w-4 mr-2" />
              View Recent
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Today Applicants</CardTitle>
            <CardDescription>Since midnight</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : dashboardData.todayApplicants.toLocaleString()}
              </div>
            </div>
            <div className="mt-3 pt-3 border-t">
              <div className="flex justify-between text-sm">
                <span className="text-blue-600">Male: {loading ? '...' : dashboardData.genderDistribution.today.male}</span>
                <span className="text-pink-600">Female: {loading ? '...' : dashboardData.genderDistribution.today.female}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full" onClick={() => window.location.href = '/graduate-admin?tab=applicants'}>
              <FileText className="h-4 w-4 mr-2" />
              View Today
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Stats Cards - Row 2 (Office Metrics) */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Registrar Office Metrics */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Building className="h-5 w-5 mr-2 text-blue-600" />
              Registrar Office Metrics
            </CardTitle>
            <CardDescription>
              {loading
                ? 'Loading period information...'
                : dashboardData.registrarMetrics.period_name
                  ? `${dashboardData.registrarMetrics.period_name} (${dashboardData.registrarMetrics.period_start_date} to ${dashboardData.registrarMetrics.period_end_date})`
                  : 'Application processing statistics'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Rate Metrics */}
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Approval Rate</div>
                <div className="flex items-center">
                  <UserCheck className="h-5 w-5 text-green-600 mr-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {loading ? '...' : `${dashboardData.registrarMetrics.approval_rate}%`}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Pending Rate</div>
                <div className="flex items-center">
                  <HourglassIcon className="h-5 w-5 text-amber-500 mr-2" />
                  <div className="text-2xl font-bold text-amber-500">
                    {loading ? '...' : `${dashboardData.registrarMetrics.pending_rate}%`}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Rejection Rate</div>
                <div className="flex items-center">
                  <UserX className="h-5 w-5 text-red-600 mr-2" />
                  <div className="text-2xl font-bold text-red-600">
                    {loading ? '...' : `${dashboardData.registrarMetrics.rejection_rate}%`}
                  </div>
                </div>
              </div>
            </div>

            {/* Count Metrics */}
            <div className="mt-4 pt-4 border-t border-blue-200">
              <div className="grid grid-cols-4 gap-2 text-center">
                <div>
                  <div className="text-sm font-medium text-gray-600">Approved</div>
                  <div className="text-lg font-semibold text-green-600">
                    {loading ? '...' : dashboardData.registrarMetrics.approved}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600">Pending</div>
                  <div className="text-lg font-semibold text-amber-500">
                    {loading ? '...' : dashboardData.registrarMetrics.pending}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600">Rejected</div>
                  <div className="text-lg font-semibold text-red-600">
                    {loading ? '...' : dashboardData.registrarMetrics.rejected}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600">Total</div>
                  <div className="text-lg font-semibold text-blue-600">
                    {loading ? '...' : dashboardData.registrarMetrics.all_total}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Department Metrics */}
        <Card className="bg-purple-50 border-purple-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users className="h-5 w-5 mr-2 text-purple-600" />
              Department Office Metrics
            </CardTitle>
            <CardDescription>
              {loading
                ? 'Loading period information...'
                : dashboardData.departmentMetrics.period_name
                  ? `${dashboardData.departmentMetrics.period_name} (${dashboardData.departmentMetrics.period_start_date} to ${dashboardData.departmentMetrics.period_end_date})`
                  : 'Department review statistics'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Rate Metrics */}
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Approval Rate</div>
                <div className="flex items-center">
                  <UserCheck className="h-5 w-5 text-green-600 mr-2" />
                  <div className="text-2xl font-bold text-green-600">
                    {loading ? '...' : `${dashboardData.departmentMetrics.approval_rate}%`}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Pending Rate</div>
                <div className="flex items-center">
                  <HourglassIcon className="h-5 w-5 text-amber-500 mr-2" />
                  <div className="text-2xl font-bold text-amber-500">
                    {loading ? '...' : `${dashboardData.departmentMetrics.pending_rate}%`}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-600">Rejection Rate</div>
                <div className="flex items-center">
                  <UserX className="h-5 w-5 text-red-600 mr-2" />
                  <div className="text-2xl font-bold text-red-600">
                    {loading ? '...' : `${dashboardData.departmentMetrics.rejection_rate}%`}
                  </div>
                </div>
              </div>
            </div>

            {/* Count Metrics */}
            <div className="mt-4 pt-4 border-t border-purple-200">
              <div className="grid grid-cols-4 gap-2 text-center">
                <div>
                  <div className="text-sm font-medium text-gray-600">Approved</div>
                  <div className="text-lg font-semibold text-green-600">
                    {loading ? '...' : dashboardData.departmentMetrics.approved}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600">Pending</div>
                  <div className="text-lg font-semibold text-amber-500">
                    {loading ? '...' : dashboardData.departmentMetrics.pending}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600">Rejected</div>
                  <div className="text-lg font-semibold text-red-600">
                    {loading ? '...' : dashboardData.departmentMetrics.rejected}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600">Total</div>
                  <div className="text-lg font-semibold text-purple-600">
                    {loading ? '...' : dashboardData.departmentMetrics.all_total}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>

      {/* Charts - Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Applications by Month</CardTitle>
            <CardDescription>Monthly application trends</CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={dashboardData.applicationsByMonth}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="count" name="Applications" stroke="#1a73c0" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Application Status</CardTitle>
            <CardDescription>Distribution by status</CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={dashboardData.statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {dashboardData.statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Applications by Status Over Time */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle>Total Applications by Status</CardTitle>
              <CardDescription>View trends based on different time periods</CardDescription>
            </div>
            <Tabs
              value={timePeriod}
              onValueChange={setTimePeriod}
              className="w-full sm:w-auto"
            >
              <TabsList className="grid grid-cols-4 w-full sm:w-auto">
                <TabsTrigger value="daily">Daily</TabsTrigger>
                <TabsTrigger value="weekly">Weekly</TabsTrigger>
                <TabsTrigger value="monthly">Monthly</TabsTrigger>
                <TabsTrigger value="yearly">Yearly</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent className="h-80">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={processStatusByPeriodData(dashboardData.statusByPeriod.data)}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="period"
                  label={{
                    value: 'Time Period',
                    position: 'insideBottomRight',
                    offset: -10
                  }}
                />
                <YAxis
                  label={{
                    value: 'Number of Applications',
                    angle: -90,
                    position: 'insideLeft'
                  }}
                />
                <Tooltip
                  formatter={(value, name) => {
                    return [`${value} Applications`, name];
                  }}
                />
                <Legend />
                <Bar
                  name="Approved"
                  dataKey="Approved"
                  fill="#00C49F"
                />
                <Bar
                  name="Pending"
                  dataKey="Pending"
                  fill="#FFBB28"
                />
                <Bar
                  name="Rejected"
                  dataKey="Rejected"
                  fill="#FF8042"
                />
              </BarChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>

      {/* Charts - Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Gender Distribution</CardTitle>
            <CardDescription>Applicants by gender</CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={dashboardData.genderData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    <Cell key="cell-0" fill="#0088FE" />
                    <Cell key="cell-1" fill="#FF8042" />
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Program Level Distribution</CardTitle>
            <CardDescription>Applicants by program level</CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={dashboardData.programLevelData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    <Cell key="cell-0" fill="#00C49F" />
                    <Cell key="cell-1" fill="#FFBB28" />
                    <Cell key="cell-2" fill="#FF8042" />
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>



      {/* College Applications by Status */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Applications by College</CardTitle>
            <CardDescription>Total applicants grouped by college with department, registrar, and payment status</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-full py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
              </div>
            ) : dashboardData.applicantsByCollege.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No college data available
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-blue-50">
                      <th className="border border-blue-200 px-4 py-2 text-left">College</th>
                      <th className="border border-blue-200 px-4 py-2 text-center">Total Applicants</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-green-50" colSpan={3}>Registrar Status</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-purple-50" colSpan={3}>Department Status</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-blue-50" colSpan={3}>Payment Status</th>
                    </tr>
                    <tr>
                      <th className="border border-blue-200 px-4 py-2 text-left"></th>
                      <th className="border border-blue-200 px-4 py-2 text-center"></th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-green-50">Approved</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-green-50">Pending</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-green-50">Rejected</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-purple-50">Approved</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-purple-50">Pending</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-purple-50">Rejected</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-blue-50">Approved</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-blue-50">Pending</th>
                      <th className="border border-blue-200 px-4 py-2 text-center bg-blue-50">Rejected</th>
                    </tr>
                  </thead>
                  <tbody>
                    {dashboardData.applicantsByCollege.map((college, index) => (
                      <tr key={college.college_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="border border-blue-200 px-4 py-2 font-medium">{college.college_name}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center font-bold">{college.total_applicants}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-green-600 bg-green-50">{college.registrar_status.Approved}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-amber-500 bg-green-50">{college.registrar_status.Pending}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-red-600 bg-green-50">{college.registrar_status.Rejected}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-green-600 bg-purple-50">{college.department_status.Approved}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-amber-500 bg-purple-50">{college.department_status.Pending}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-red-600 bg-purple-50">{college.department_status.Rejected}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-green-600 bg-blue-50">{college.payment_status?.Approved || 0}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-amber-500 bg-blue-50">{college.payment_status?.Pending || 0}</td>
                        <td className="border border-blue-200 px-4 py-2 text-center text-red-600 bg-blue-50">{college.payment_status?.Rejected || 0}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Applications section removed as requested */}
    </div>
  );
};

export default ApplicationDashboard;
