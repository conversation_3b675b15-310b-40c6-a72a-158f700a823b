import { useEffect, useState } from 'react';
import { settingsAPI } from '@/services/api';

const DynamicFavicon: React.FC = () => {
  const [faviconUrl, setFaviconUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchFavicon = async () => {
      setIsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data && response.data.favicon_url) {
          // Ensure the URL is absolute
          const url = response.data.favicon_url.startsWith('http')
            ? response.data.favicon_url
            : `http://localhost:8000${response.data.favicon_url}`;
          setFaviconUrl(url);
        }
      } catch (error) {
        console.error('Error fetching favicon:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFavicon();
  }, []);

  useEffect(() => {
    if (!faviconUrl) return;

    // Get existing favicon elements
    const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
    
    // Remove existing favicons
    existingFavicons.forEach(favicon => {
      document.head.removeChild(favicon);
    });

    // Create new favicon link element
    const link = document.createElement('link');
    link.rel = 'icon';
    link.href = faviconUrl;
    link.type = 'image/x-icon';
    
    // Add the new favicon to the document head
    document.head.appendChild(link);
    
    // Also add apple touch icon for iOS devices
    const appleLink = document.createElement('link');
    appleLink.rel = 'apple-touch-icon';
    appleLink.href = faviconUrl;
    document.head.appendChild(appleLink);

    // Cleanup function to remove the favicon when component unmounts
    return () => {
      const links = document.querySelectorAll('link[rel*="icon"], link[rel="apple-touch-icon"]');
      links.forEach(link => {
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      });
    };
  }, [faviconUrl]);

  // This component doesn't render anything
  return null;
};

export default DynamicFavicon;
