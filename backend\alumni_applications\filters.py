import django_filters
from django.db.models import Q
from .models import AlumniApplication, AlumniApplicationMini, ApplicationDocument


class AlumniApplicationFilter(django_filters.FilterSet):
    """Filter for AlumniApplication (Form1)."""
    
    # Text search across multiple fields
    search = django_filters.CharFilter(method='filter_search', label='Search')
    
    # Status filters
    application_status = django_filters.ChoiceFilter(
        choices=AlumniApplication.APPLICATION_STATUS_CHOICES,
        label='Application Status'
    )
    payment_status = django_filters.ChoiceFilter(
        choices=AlumniApplication.PAYMENT_STATUS_CHOICES,
        label='Payment Status'
    )
    student_status = django_filters.ChoiceFilter(
        choices=AlumniApplication.STUDENT_STATUS_CHOICES,
        label='Student Status'
    )

    # Service and academic filters
    service_type = django_filters.UUIDFilter(field_name='service_type__id', label='Service Type')
    college = django_filters.UUIDFilter(field_name='college__id', label='College')
    department = django_filters.UUIDFilter(field_name='department__id', label='Department')
    admission_type = django_filters.ChoiceFilter(
        choices=AlumniApplication.ADMISSION_TYPE_CHOICES,
        label='Admission Type'
    )
    degree_type = django_filters.ChoiceFilter(
        choices=AlumniApplication.DEGREE_TYPE_CHOICES,
        label='Degree Type'
    )

    # Destination filters (specific to Form1)
    is_uog_destination = django_filters.BooleanFilter(label='UoG Destination')
    order_type = django_filters.ChoiceFilter(
        choices=AlumniApplication.ORDER_TYPE_CHOICES,
        label='Order Type'
    )
    country = django_filters.CharFilter(lookup_expr='icontains', label='Country')
    
    # Date range filters
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte', label='Created After')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte', label='Created Before')
    updated_after = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='gte', label='Updated After')
    updated_before = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='lte', label='Updated Before')
    
    # Year filters
    graduation_year = django_filters.NumberFilter(
        field_name='year_of_graduation_gregorian__year',
        label='Graduation Year'
    )
    leaving_year = django_filters.NumberFilter(
        field_name='year_of_leaving_gregorian__year',
        label='Leaving Year'
    )
    
    class Meta:
        model = AlumniApplication
        fields = [
            'search', 'application_status', 'payment_status', 'student_status',
            'service_type', 'college', 'department', 'admission_type', 'degree_type',
            'is_uog_destination', 'order_type', 'country',
            'created_after', 'created_before', 'updated_after', 'updated_before',
            'graduation_year', 'leaving_year'
        ]
    
    def filter_search(self, queryset, name, value):
        """Search across multiple fields."""
        if value:
            return queryset.filter(
                Q(first_name__icontains=value) |
                Q(last_name__icontains=value) |
                Q(father_name__icontains=value) |
                Q(email__icontains=value) |
                Q(phone_number__icontains=value) |
                Q(student_id__icontains=value) |
                Q(transaction_id__icontains=value) |
                Q(institution_name__icontains=value)
            )
        return queryset


class AlumniApplicationMiniFilter(django_filters.FilterSet):
    """Filter for AlumniApplicationMini (Form2)."""
    
    # Text search across multiple fields
    search = django_filters.CharFilter(method='filter_search', label='Search')
    
    # Status filters
    application_status = django_filters.ChoiceFilter(
        choices=AlumniApplicationMini.APPLICATION_STATUS_CHOICES,
        label='Application Status'
    )
    payment_status = django_filters.ChoiceFilter(
        choices=AlumniApplicationMini.PAYMENT_STATUS_CHOICES,
        label='Payment Status'
    )
    student_status = django_filters.ChoiceFilter(
        choices=AlumniApplicationMini.STUDENT_STATUS_CHOICES,
        label='Student Status'
    )

    # Service and academic filters
    service_type = django_filters.UUIDFilter(field_name='service_type__id', label='Service Type')
    college = django_filters.UUIDFilter(field_name='college__id', label='College')
    department = django_filters.UUIDFilter(field_name='department__id', label='Department')
    admission_type = django_filters.ChoiceFilter(
        choices=AlumniApplicationMini.ADMISSION_TYPE_CHOICES,
        label='Admission Type'
    )
    degree_type = django_filters.ChoiceFilter(
        choices=AlumniApplicationMini.DEGREE_TYPE_CHOICES,
        label='Degree Type'
    )
    
    # Date range filters
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte', label='Created After')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte', label='Created Before')
    updated_after = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='gte', label='Updated After')
    updated_before = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='lte', label='Updated Before')
    
    # Year filters
    graduation_year = django_filters.NumberFilter(
        field_name='year_of_graduation_gregorian__year',
        label='Graduation Year'
    )
    leaving_year = django_filters.NumberFilter(
        field_name='year_of_leaving_gregorian__year',
        label='Leaving Year'
    )
    
    class Meta:
        model = AlumniApplicationMini
        fields = [
            'search', 'application_status', 'payment_status', 'student_status',
            'service_type', 'college', 'department', 'admission_type', 'degree_type',
            'created_after', 'created_before', 'updated_after', 'updated_before',
            'graduation_year', 'leaving_year'
        ]
    
    def filter_search(self, queryset, name, value):
        """Search across multiple fields."""
        if value:
            return queryset.filter(
                Q(first_name__icontains=value) |
                Q(last_name__icontains=value) |
                Q(father_name__icontains=value) |
                Q(email__icontains=value) |
                Q(phone_number__icontains=value) |
                Q(student_id__icontains=value) |
                Q(transaction_id__icontains=value)
            )
        return queryset


class ApplicationDocumentFilter(django_filters.FilterSet):
    """Filter for ApplicationDocument."""
    
    # Search by filename or document type
    search = django_filters.CharFilter(method='filter_search', label='Search')
    
    # Filter by document type name
    document_type_name = django_filters.CharFilter(field_name='document_type_name', lookup_expr='icontains', label='Document Type Name')
    
    # Filter by application type
    application_type = django_filters.ChoiceFilter(
        method='filter_application_type',
        choices=[('form1', 'Form 1'), ('form2', 'Form 2')],
        label='Application Type'
    )
    
    # Date range filters
    uploaded_after = django_filters.DateTimeFilter(field_name='upload_timestamp', lookup_expr='gte', label='Uploaded After')
    uploaded_before = django_filters.DateTimeFilter(field_name='upload_timestamp', lookup_expr='lte', label='Uploaded Before')
    
    # File size filters
    min_file_size = django_filters.NumberFilter(field_name='file_size', lookup_expr='gte', label='Min File Size (bytes)')
    max_file_size = django_filters.NumberFilter(field_name='file_size', lookup_expr='lte', label='Max File Size (bytes)')
    
    # MIME type filter
    mime_type = django_filters.CharFilter(lookup_expr='icontains', label='MIME Type')
    
    class Meta:
        model = ApplicationDocument
        fields = [
            'search', 'document_type_name', 'application_type',
            'uploaded_after', 'uploaded_before',
            'min_file_size', 'max_file_size', 'mime_type'
        ]
    
    def filter_search(self, queryset, name, value):
        """Search across filename and document type."""
        if value:
            return queryset.filter(
                Q(original_filename__icontains=value) |
                Q(document_type_name__icontains=value)
            )
        return queryset
    
    def filter_application_type(self, queryset, name, value):
        """Filter by application type (Form1 or Form2)."""
        if value == 'form1':
            return queryset.filter(application_form1__isnull=False)
        elif value == 'form2':
            return queryset.filter(application_form2__isnull=False)
        return queryset


# Additional utility filters
class DateRangeFilter(django_filters.FilterSet):
    """Reusable date range filter mixin."""
    
    date_from = django_filters.DateFilter(field_name='created_at', lookup_expr='gte')
    date_to = django_filters.DateFilter(field_name='created_at', lookup_expr='lte')
    
    class Meta:
        abstract = True


class StatusFilter(django_filters.FilterSet):
    """Reusable status filter mixin."""
    
    status = django_filters.MultipleChoiceFilter(
        field_name='application_status',
        choices=[
            ('Pending', 'Pending'),
            ('On Review', 'On Review'),
            ('Processing', 'Processing'),
            ('Complete', 'Complete'),
        ]
    )
    
    class Meta:
        abstract = True
