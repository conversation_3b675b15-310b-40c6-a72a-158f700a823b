import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2, Search, RefreshCw, CheckCircle, XCircle, FileText, AlertCircle, Award, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, ToggleLeft, ToggleRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { certificateTypeAPI } from '@/services/api';
import { cn } from '@/lib/utils';

interface CertificateType {
  id: number;
  uuid: string;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const CertificateTypeManagement: React.FC = () => {
  const [certificateTypes, setCertificateTypes] = useState<CertificateType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentCertificateType, setCurrentCertificateType] = useState<CertificateType | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    description: '',
    general: '',
  });

  // Fetch certificate types
  const fetchCertificateTypes = async () => {
    try {
      setLoading(true);
      const params: any = {};
      
      if (searchTerm) {
        params.search = searchTerm;
      }
      
      if (statusFilter !== 'all') {
        params.is_active = statusFilter === 'active';
      }

      const response = await certificateTypeAPI.searchCertificateTypes(params);
      setCertificateTypes(response.data);
    } catch (error) {
      console.error('Error fetching certificate types:', error);
      toast.error('Failed to fetch certificate types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCertificateTypes();
  }, [searchTerm, statusFilter]);

  // Validation function based on model constraints
  const validateForm = () => {
    let valid = true;
    const newErrors = {
      name: '',
      description: '',
      general: '',
    };

    // Name validation - Required field with minimum length
    if (!formData.name || !formData.name.trim()) {
      newErrors.name = 'Certificate type name is required.';
      valid = false;
    } else {
      const trimmedName = formData.name.trim();
      if (trimmedName.length < 2) {
        newErrors.name = 'Certificate type name must be at least 2 characters long.';
        valid = false;
      } else {
        // Check for duplicate names (case-insensitive)
        const duplicate = certificateTypes.find(type => 
          type.name.toLowerCase() === trimmedName.toLowerCase() &&
          (!currentCertificateType || type.uuid !== currentCertificateType.uuid)
        );
        
        if (duplicate) {
          newErrors.name = 'A certificate type with this name already exists.';
          valid = false;
        }
      }
    }

    setFormErrors(newErrors);
    return valid;
  };

  // Handle input changes with real-time validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear errors when user types
    setFormErrors({
      ...formErrors,
      [name]: '',
      general: '',
    });

    // Real-time validation for name field
    if (name === 'name' && value) {
      const trimmedValue = value.trim();
      if (trimmedValue.length > 0 && trimmedValue.length < 2) {
        setFormErrors(prev => ({
          ...prev,
          name: 'Certificate type name must be at least 2 characters long.'
        }));
      } else if (trimmedValue.length >= 2) {
        // Check for duplicates
        const duplicate = certificateTypes.find(type => 
          type.name.toLowerCase() === trimmedValue.toLowerCase() &&
          (!currentCertificateType || type.uuid !== currentCertificateType.uuid)
        );
        
        if (duplicate) {
          setFormErrors(prev => ({
            ...prev,
            name: 'A certificate type with this name already exists.'
          }));
        }
      }
    }
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData({
      ...formData,
      is_active: checked,
    });
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      is_active: true,
    });
    setFormErrors({
      name: '',
      description: '',
      general: '',
    });
    setCurrentCertificateType(null);
  };

  // Handle add certificate type
  const handleAddCertificateType = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const dataToSubmit = {
        ...formData,
        name: formData.name.trim(),
      };

      await certificateTypeAPI.createCertificateType(dataToSubmit);
      toast.success('Certificate type created successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchCertificateTypes();
    } catch (error: any) {
      console.error('Error creating certificate type:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (errorData.name) {
          setFormErrors(prev => ({ ...prev, name: errorData.name[0] }));
        } else {
          setFormErrors(prev => ({ ...prev, general: errorData.detail || 'Failed to create certificate type' }));
        }
      } else {
        toast.error('Failed to create certificate type');
      }
    }
  };

  // Handle edit certificate type
  const openEditDialog = (certificateType: CertificateType) => {
    setCurrentCertificateType(certificateType);
    setFormData({
      name: certificateType.name,
      description: certificateType.description || '',
      is_active: certificateType.is_active,
    });
    setFormErrors({
      name: '',
      description: '',
      general: '',
    });
    setIsEditDialogOpen(true);
  };

  const handleEditCertificateType = async () => {
    if (!validateForm() || !currentCertificateType) {
      return;
    }

    try {
      const dataToSubmit = {
        ...formData,
        name: formData.name.trim(),
      };

      await certificateTypeAPI.updateCertificateType(currentCertificateType.uuid, dataToSubmit);
      toast.success('Certificate type updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchCertificateTypes();
    } catch (error: any) {
      console.error('Error updating certificate type:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (errorData.name) {
          setFormErrors(prev => ({ ...prev, name: errorData.name[0] }));
        } else {
          setFormErrors(prev => ({ ...prev, general: errorData.detail || 'Failed to update certificate type' }));
        }
      } else {
        toast.error('Failed to update certificate type');
      }
    }
  };

  // Handle delete certificate type
  const handleDeleteCertificateType = async (certificateType: CertificateType) => {
    if (!confirm(`Are you sure you want to delete "${certificateType.name}"?`)) {
      return;
    }

    try {
      await certificateTypeAPI.deleteCertificateType(certificateType.uuid);
      toast.success('Certificate type deleted successfully');
      fetchCertificateTypes();
    } catch (error) {
      console.error('Error deleting certificate type:', error);
      toast.error('Failed to delete certificate type');
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (certificateType: CertificateType) => {
    try {
      await certificateTypeAPI.toggleCertificateTypeStatus(certificateType.uuid);
      toast.success(`Certificate type ${certificateType.is_active ? 'deactivated' : 'activated'} successfully`);
      fetchCertificateTypes();
    } catch (error) {
      console.error('Error toggling certificate type status:', error);
      toast.error('Failed to update certificate type status');
    }
  };

  // Filter certificate types based on search and status
  const filteredCertificateTypes = certificateTypes.filter(type => {
    const matchesSearch = type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (type.description && type.description.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'all' ||
                         (statusFilter === 'active' && type.is_active) ||
                         (statusFilter === 'inactive' && !type.is_active);

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredCertificateTypes.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredCertificateTypes.length / itemsPerPage);

  // Reset to first page when search term or status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Award className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Certificate Type Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete certificate types for the application system
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm" onClick={resetForm}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Certificate Type
                  </Button>
                </DialogTrigger>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search certificate types by name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-blue-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40 border-blue-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active Only</SelectItem>
                  <SelectItem value="inactive">Inactive Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters Display */}
          {(searchTerm || statusFilter !== 'all') && (
            <div className="mb-4">
              <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="text-sm font-medium text-[#1a73c0]">Active Filters:</span>

                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Search: {searchTerm}
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove search filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                {statusFilter !== 'all' && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                    <button
                      onClick={() => setStatusFilter('all')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove status filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                  }}
                  className="text-xs h-7 px-3 ml-auto border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Clear All
                </Button>
              </div>
            </div>
          )}

          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[25%] text-[#1a73c0] font-medium">Name</TableHead>
                    <TableHead className="w-[35%] text-[#1a73c0] font-medium">Description</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">Status</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">Created</TableHead>
                    <TableHead className="w-[10%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-blue-100 p-3 rounded-full">
                            <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                          </div>
                          <div className="text-[#1a73c0] font-medium">Loading certificate types...</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredCertificateTypes.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <Award className="h-8 w-8 text-gray-500" />
                          </div>
                          <div className="text-gray-700 font-medium">No certificate types found</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {searchTerm ?
                              'Try adjusting your search criteria to find what you\'re looking for.' :
                              'There are no certificate types available. Click the "Add Certificate Type" button to create one.'}
                          </div>
                          {(searchTerm || statusFilter !== 'all') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSearchTerm('');
                                setStatusFilter('all');
                              }}
                              className="mt-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                            >
                              Clear Filters
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentItems.map((certificateType) => (
                      <TableRow key={certificateType.uuid} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{certificateType.name}</TableCell>
                        <TableCell className="max-w-xs">
                          <div className="truncate text-gray-700" title={certificateType.description}>
                            {certificateType.description || 'No description provided'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {certificateType.is_active ? (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200 shadow-sm">
                                <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-green-600"></span>
                                Active
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200 shadow-sm">
                                <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-red-600"></span>
                                Inactive
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Award className="h-4 w-4 mr-1 text-blue-500" />
                            {new Date(certificateType.created_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            })}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleStatus(certificateType)}
                              className={cn(
                                "h-8 w-8 p-0",
                                certificateType.is_active
                                  ? "hover:bg-orange-50 hover:text-orange-600"
                                  : "hover:bg-green-50 hover:text-green-600"
                              )}
                              title={certificateType.is_active ? "Deactivate certificate type" : "Activate certificate type"}
                            >
                              {certificateType.is_active ? (
                                <ToggleRight className="h-4 w-4" />
                              ) : (
                                <ToggleLeft className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(certificateType)}
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                              title="Edit certificate type"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteCertificateType(certificateType)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete certificate type"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredCertificateTypes.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredCertificateTypes.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredCertificateTypes.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Add Certificate Type Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Add New Certificate Type</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Create a new certificate type for the application system. Provide a clear name and description.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="p-6 space-y-8">
            {/* General Error Display */}
            {formErrors.general && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-red-800">Validation Error</h3>
                    <p className="text-sm text-red-700 mt-1">{formErrors.general}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Certificate Type Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-4 w-4 text-[#1a73c0]" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Certificate Type Information</h3>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="name" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Certificate Type Name
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter certificate type name..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.name ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.name}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter a unique name for the certificate type (minimum 2 characters)</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="description" className="text-sm font-semibold text-gray-700">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter certificate type description (optional)..."
                    rows={4}
                    className={cn(
                      "border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.description ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.description ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.description}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Provide additional details about this certificate type (optional)</p>
                  )}
                </div>
              </div>
            </div>

            {/* Status Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={handleSwitchChange}
                      className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                    />
                    <Label htmlFor="is_active" className="text-base font-semibold text-gray-700">
                      Certificate Type Status
                    </Label>
                  </div>
                  <span className={cn(
                    "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                    formData.is_active
                      ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                      : "bg-gray-100 text-gray-800 border border-gray-200"
                  )}>
                    {formData.is_active ? "✓ Active" : "○ Inactive"}
                  </span>
                </div>

                <div className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  formData.is_active
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                )}>
                  <div className="flex items-start space-x-3">
                    {formData.is_active ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={cn(
                        "text-sm font-medium mb-1",
                        formData.is_active ? "text-green-800" : "text-gray-700"
                      )}>
                        {formData.is_active ? "Certificate Type is Active" : "Certificate Type is Inactive"}
                      </p>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {formData.is_active
                          ? "This certificate type will be available for selection in the application system."
                          : "This certificate type will be hidden and unavailable for selection."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="text-xs text-gray-500">
                <span className="text-red-500">*</span> Required fields
              </div>
              <div className="flex space-x-3">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                    onClick={resetForm}
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={handleAddCertificateType}
                  className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Certificate Type
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Certificate Type Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Pencil className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Edit Certificate Type</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Update the certificate type information. Make sure to review all fields before saving changes.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="p-6 space-y-8">
            {/* General Error Display */}
            {formErrors.general && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-red-800">Validation Error</h3>
                    <p className="text-sm text-red-700 mt-1">{formErrors.general}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Certificate Type Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-4 w-4 text-[#1a73c0]" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Certificate Type Information</h3>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="edit-name" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Certificate Type Name
                  </Label>
                  <Input
                    id="edit-name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter certificate type name..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.name ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.name}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter a unique name for the certificate type (minimum 2 characters)</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="edit-description" className="text-sm font-semibold text-gray-700">
                    Description
                  </Label>
                  <Textarea
                    id="edit-description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter certificate type description (optional)..."
                    rows={4}
                    className={cn(
                      "border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.description ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.description ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.description}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Provide additional details about this certificate type (optional)</p>
                  )}
                </div>
              </div>
            </div>

            {/* Status Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Switch
                      id="edit-is_active"
                      checked={formData.is_active}
                      onCheckedChange={handleSwitchChange}
                      className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                    />
                    <Label htmlFor="edit-is_active" className="text-base font-semibold text-gray-700">
                      Certificate Type Status
                    </Label>
                  </div>
                  <span className={cn(
                    "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                    formData.is_active
                      ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                      : "bg-gray-100 text-gray-800 border border-gray-200"
                  )}>
                    {formData.is_active ? "✓ Active" : "○ Inactive"}
                  </span>
                </div>

                <div className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  formData.is_active
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                )}>
                  <div className="flex items-start space-x-3">
                    {formData.is_active ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={cn(
                        "text-sm font-medium mb-1",
                        formData.is_active ? "text-green-800" : "text-gray-700"
                      )}>
                        {formData.is_active ? "Certificate Type is Active" : "Certificate Type is Inactive"}
                      </p>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {formData.is_active
                          ? "This certificate type will be available for selection in the application system."
                          : "This certificate type will be hidden and unavailable for selection."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="text-xs text-gray-500">
                <span className="text-red-500">*</span> Required fields
              </div>
              <div className="flex space-x-3">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                    onClick={resetForm}
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={handleEditCertificateType}
                  className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Update Certificate Type
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CertificateTypeManagement;
