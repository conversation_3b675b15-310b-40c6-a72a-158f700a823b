from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Count, Q
from django.db.models.functions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>runcDay, TruncWeek, <PERSON>runc<PERSON>ear
from django.utils import timezone
from datetime import timedelta, datetime
from .models import (
    ApplicantProgramSelection,
    ApplicantInformation
)
from setups.college.models import College

class ApplicationStatisticsView(APIView):
    """
    API endpoint for application statistics.
    Accessible to all users.
    """
    # No permission classes required - accessible to all

    def get_applicants_by_college(self):
        """
        Get applicant counts grouped by college, with department, registrar, and payment status breakdowns
        """
        try:
            # Get all colleges
            colleges = College.objects.all().order_by('name')

            # Initialize the result list
            result = []

            # For each college, get the counts by status
            for college in colleges:
                # Get all applications for this college
                college_applications = ApplicantProgramSelection.objects.filter(
                    application_info__college=college
                )

                # Skip colleges with no applications
                if college_applications.count() == 0:
                    continue

                # Count by registrar status
                registrar_status_counts = college_applications.values('registrar_off_status').annotate(
                    count=Count('id')
                ).order_by('registrar_off_status')

                # Count by department status
                department_status_counts = college_applications.values('department_status').annotate(
                    count=Count('id')
                ).order_by('department_status')

                # Count by payment status
                payment_status_counts = college_applications.values('payment_status').annotate(
                    count=Count('id')
                ).order_by('payment_status')

                # Format the data for the frontend
                college_data = {
                    'college_id': college.id,
                    'college_name': college.name,
                    'total_applicants': college_applications.count(),
                    'registrar_status': {
                        'Approved': 0,
                        'Pending': 0,
                        'Rejected': 0
                    },
                    'department_status': {
                        'Approved': 0,
                        'Pending': 0,
                        'Rejected': 0
                    },
                    'payment_status': {
                        'Approved': 0,
                        'Pending': 0,
                        'Rejected': 0
                    }
                }

                # Add registrar status counts
                for status_count in registrar_status_counts:
                    status = status_count['registrar_off_status']
                    if status in college_data['registrar_status']:
                        college_data['registrar_status'][status] = status_count['count']

                # Add department status counts
                for status_count in department_status_counts:
                    status = status_count['department_status']
                    if status in college_data['department_status']:
                        college_data['department_status'][status] = status_count['count']

                # Add payment status counts
                for status_count in payment_status_counts:
                    status = status_count['payment_status']
                    # Normalize "Completed" to "Approved" for consistency
                    if status == "Completed":
                        status = "Approved"
                    if status in college_data['payment_status']:
                        college_data['payment_status'][status] = status_count['count']

                # Add to the result
                result.append(college_data)

            return result
        except Exception as e:
            print(f"Error getting applicants by college: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

    def get_status_by_period(self, request):
        """
        Get application status data grouped by different time periods (daily, weekly, monthly, yearly)
        """
        try:
            # Get the time period from query parameters, default to 'monthly'
            period = request.query_params.get('period', 'monthly')

            # Define the truncation function based on the period
            trunc_function = {
                'daily': TruncDay,
                'weekly': TruncWeek,
                'monthly': TruncMonth,
                'yearly': TruncYear
            }.get(period, TruncMonth)

            # Define the lookback period based on the selected time period
            lookback_days = {
                'daily': 30,  # Last 30 days
                'weekly': 90,  # Last ~12 weeks
                'monthly': 365,  # Last 12 months
                'yearly': 1825  # Last 5 years
            }.get(period, 365)

            # Calculate the start date for the query
            start_date = timezone.now() - timedelta(days=lookback_days)

            # Get the status values we want to track
            statuses = ['Approved', 'Pending', 'Rejected']

            # Initialize the result dictionary
            result = {
                'period': period,
                'data': []
            }

            # Query the database to get counts by period and status
            for status in statuses:
                # Get data for this status
                status_data = list(
                    ApplicantProgramSelection.objects.filter(
                        registrar_off_status=status,
                        created_at__gte=start_date
                    )
                    .annotate(period=trunc_function('created_at'))
                    .values('period')
                    .annotate(count=Count('id'))
                    .order_by('period')
                )

                # Format the data for the frontend
                formatted_data = []
                for item in status_data:
                    # Format the period label based on the selected time period
                    if period == 'daily':
                        period_label = item['period'].strftime('%b %d')
                    elif period == 'weekly':
                        period_label = f"Week {item['period'].strftime('%U')}"
                    elif period == 'monthly':
                        period_label = item['period'].strftime('%b %Y')
                    else:  # yearly
                        period_label = item['period'].strftime('%Y')

                    formatted_data.append({
                        'period': period_label,
                        'status': status,
                        'count': item['count']
                    })

                # Add to the result
                result['data'].extend(formatted_data)

            return result
        except Exception as e:
            print(f"Error getting status by period: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'period': 'monthly',
                'data': []
            }

    def get(self, request):
        """
        Get application statistics
        """
        try:
            # Calculate total applications (using ApplicantProgramSelection as requested)
            total_applications = ApplicantProgramSelection.objects.count()

            # Calculate new applications in the last 30 days
            thirty_days_ago = timezone.now() - timedelta(days=30)
            new_applications = ApplicantProgramSelection.objects.filter(created_at__gte=thirty_days_ago).count()

            # Calculate applications in the last 15 days
            fifteen_days_ago = timezone.now() - timedelta(days=15)
            fifteen_day_applicants = ApplicantProgramSelection.objects.filter(created_at__gte=fifteen_days_ago).count()

            # Calculate applications for today
            today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_applicants = ApplicantProgramSelection.objects.filter(created_at__gte=today_start).count()

            # Calculate gender distribution for all time periods
            # Count all applications by gender (not just unique users)
            # This matches the SQL query: SELECT ai.gender, COUNT(*) FROM ApplicantProgramSelection aps JOIN ApplicantInformation ai ON aps.user_id = ai.author_id GROUP BY ai.gender

            # All-time gender distribution
            all_gender_counts = ApplicantProgramSelection.objects.select_related('user__applicant_info').values('user__applicant_info__gender').annotate(count=Count('id'))

            all_male = 0
            all_female = 0
            for item in all_gender_counts:
                if item['user__applicant_info__gender'] == 'Male':
                    all_male = item['count']
                elif item['user__applicant_info__gender'] == 'Female':
                    all_female = item['count']

            all_gender_distribution = {
                'male': all_male,
                'female': all_female,
                'total': all_male + all_female
            }

            # 30-day gender distribution
            thirty_day_gender_counts = ApplicantProgramSelection.objects.filter(
                created_at__gte=thirty_days_ago
            ).select_related('user__applicant_info').values('user__applicant_info__gender').annotate(count=Count('id'))

            thirty_day_male = 0
            thirty_day_female = 0
            for item in thirty_day_gender_counts:
                if item['user__applicant_info__gender'] == 'Male':
                    thirty_day_male = item['count']
                elif item['user__applicant_info__gender'] == 'Female':
                    thirty_day_female = item['count']

            thirty_day_gender_distribution = {
                'male': thirty_day_male,
                'female': thirty_day_female,
                'total': thirty_day_male + thirty_day_female
            }

            # 15-day gender distribution
            fifteen_day_gender_counts = ApplicantProgramSelection.objects.filter(
                created_at__gte=fifteen_days_ago,
                user__applicant_info__gender__isnull=False
            ).select_related('user__applicant_info').values('user__applicant_info__gender').annotate(count=Count('id'))

            fifteen_day_male = 0
            fifteen_day_female = 0
            for item in fifteen_day_gender_counts:
                if item['user__applicant_info__gender'] == 'Male':
                    fifteen_day_male = item['count']
                elif item['user__applicant_info__gender'] == 'Female':
                    fifteen_day_female = item['count']

            fifteen_day_gender_distribution = {
                'male': fifteen_day_male,
                'female': fifteen_day_female,
                'total': fifteen_day_male + fifteen_day_female
            }

            # Today's gender distribution
            today_gender_counts = ApplicantProgramSelection.objects.filter(
                created_at__gte=today_start,
                user__applicant_info__gender__isnull=False
            ).select_related('user__applicant_info').values('user__applicant_info__gender').annotate(count=Count('id'))

            today_male = 0
            today_female = 0
            for item in today_gender_counts:
                if item['user__applicant_info__gender'] == 'Male':
                    today_male = item['count']
                elif item['user__applicant_info__gender'] == 'Female':
                    today_female = item['count']

            today_gender_distribution = {
                'male': today_male,
                'female': today_female,
                'total': today_male + today_female
            }

            # Calculate pending review applications (keeping for backward compatibility)
            pending_review = ApplicantProgramSelection.objects.filter(
                Q(registrar_off_status='Pending') | Q(department_status='Pending')
            ).count()

            # Use a fixed 50-day period for metrics
            start_date = timezone.now() - timedelta(days=50)
            end_date = timezone.now()
            period_name = "Last 50 days"

            # Calculate registrar metrics for the last 50 days
            registrar_approved = ApplicantProgramSelection.objects.filter(
                registrar_off_status='Approved',
                created_at__gte=start_date,
                created_at__lte=end_date
            ).count()
            registrar_rejected = ApplicantProgramSelection.objects.filter(
                registrar_off_status='Rejected',
                created_at__gte=start_date,
                created_at__lte=end_date
            ).count()
            registrar_pending = ApplicantProgramSelection.objects.filter(
                registrar_off_status='Pending',
                created_at__gte=start_date,
                created_at__lte=end_date
            ).count()
            registrar_all_total = ApplicantProgramSelection.objects.filter(
                created_at__gte=start_date,
                created_at__lte=end_date
            ).exclude(registrar_off_status='').count()

            # Calculate rates based on the same denominator (all_total)
            registrar_approval_rate = int((registrar_approved / registrar_all_total) * 100) if registrar_all_total > 0 else 0
            registrar_rejection_rate = int((registrar_rejected / registrar_all_total) * 100) if registrar_all_total > 0 else 0
            registrar_pending_rate = int((registrar_pending / registrar_all_total) * 100) if registrar_all_total > 0 else 0

            # Ensure the sum is exactly 100%
            total_registrar_rate = registrar_approval_rate + registrar_rejection_rate + registrar_pending_rate
            if total_registrar_rate != 100 and registrar_all_total > 0:
                # Adjust the largest rate to make the total 100%
                max_rate = max(registrar_approval_rate, registrar_rejection_rate, registrar_pending_rate)
                if max_rate == registrar_approval_rate:
                    registrar_approval_rate += (100 - total_registrar_rate)
                elif max_rate == registrar_rejection_rate:
                    registrar_rejection_rate += (100 - total_registrar_rate)
                else:
                    registrar_pending_rate += (100 - total_registrar_rate)

            # Calculate department metrics for the last 50 days
            dept_approved = ApplicantProgramSelection.objects.filter(
                department_status='Approved',
                created_at__gte=start_date,
                created_at__lte=end_date
            ).count()
            dept_rejected = ApplicantProgramSelection.objects.filter(
                department_status='Rejected',
                created_at__gte=start_date,
                created_at__lte=end_date
            ).count()
            dept_pending = ApplicantProgramSelection.objects.filter(
                department_status='Pending',
                created_at__gte=start_date,
                created_at__lte=end_date
            ).count()
            dept_all_total = ApplicantProgramSelection.objects.filter(
                created_at__gte=start_date,
                created_at__lte=end_date
            ).exclude(department_status='').count()

            # Calculate rates based on the same denominator (all_total)
            dept_approval_rate = int((dept_approved / dept_all_total) * 100) if dept_all_total > 0 else 0
            dept_rejection_rate = int((dept_rejected / dept_all_total) * 100) if dept_all_total > 0 else 0
            dept_pending_rate = int((dept_pending / dept_all_total) * 100) if dept_all_total > 0 else 0

            # Ensure the sum is exactly 100%
            total_dept_rate = dept_approval_rate + dept_rejection_rate + dept_pending_rate
            if total_dept_rate != 100 and dept_all_total > 0:
                # Adjust the largest rate to make the total 100%
                max_rate = max(dept_approval_rate, dept_rejection_rate, dept_pending_rate)
                if max_rate == dept_approval_rate:
                    dept_approval_rate += (100 - total_dept_rate)
                elif max_rate == dept_rejection_rate:
                    dept_rejection_rate += (100 - total_dept_rate)
                else:
                    dept_pending_rate += (100 - total_dept_rate)

            # Overall approval rate (for backward compatibility)
            approval_rate = registrar_approval_rate

            # Get applications by month for the past year (using ApplicantProgramSelection as requested)
            one_year_ago = timezone.now() - timedelta(days=365)
            applications_by_month = list(
                ApplicantProgramSelection.objects.filter(created_at__gte=one_year_ago)
                .annotate(month=TruncMonth('created_at'))
                .values('month')
                .annotate(count=Count('id'))
                .order_by('month')
            )

            # Format the month data for the frontend
            formatted_months = []
            for item in applications_by_month:
                month_name = item['month'].strftime('%b')
                formatted_months.append({
                    'month': month_name,
                    'count': item['count']
                })

            # Get application status distribution
            status_data = []

            # Count approved applications
            approved_count = ApplicantProgramSelection.objects.filter(registrar_off_status='Approved').count()
            if approved_count > 0:
                status_data.append({
                    'name': 'Approved',
                    'value': approved_count
                })

            # Count pending applications
            pending_count = ApplicantProgramSelection.objects.filter(registrar_off_status='Pending').count()
            if pending_count > 0:
                status_data.append({
                    'name': 'Pending',
                    'value': pending_count
                })

            # Count rejected applications
            rejected_count = ApplicantProgramSelection.objects.filter(registrar_off_status='Rejected').count()
            if rejected_count > 0:
                status_data.append({
                    'name': 'Rejected',
                    'value': rejected_count
                })

            # Count department pending applications
            dept_pending_count = ApplicantProgramSelection.objects.filter(department_status='Pending').count()
            if dept_pending_count > 0:
                status_data.append({
                    'name': 'Department Pending',
                    'value': dept_pending_count
                })

            # Recent applications removed as requested
            recent_applications = []  # Empty list since we're not including recent applications

            # Get gender distribution statistics based on applications (not unique users)
            gender_data = []
            try:
                # Count applications by gender
                gender_counts = ApplicantProgramSelection.objects.filter(
                    user__applicant_info__gender__isnull=False
                ).values(
                    'user__applicant_info__gender'
                ).annotate(
                    count=Count('id')
                )

                for item in gender_counts:
                    if item['user__applicant_info__gender']:
                        gender_data.append({
                            'name': item['user__applicant_info__gender'],
                            'value': item['count']
                        })
            except Exception as e:
                print(f"Error getting gender distribution: {str(e)}")
                # Provide default data if there's an error
                gender_data = [
                    {'name': 'Male', 'value': 0},
                    {'name': 'Female', 'value': 0}
                ]

            # Get program level distribution statistics
            program_level_data = []
            try:
                program_level_counts = ApplicantInformation.objects.values('program_level').annotate(count=Count('id'))
                for item in program_level_counts:
                    if item['program_level']:
                        program_level_data.append({
                            'name': item['program_level'],
                            'value': item['count']
                        })
            except Exception as e:
                print(f"Error getting program level distribution: {str(e)}")
                # Provide default data if there's an error
                program_level_data = [
                    {'name': 'BSC/BA', 'value': 0},
                    {'name': 'MSC/MBA', 'value': 0},
                    {'name': 'PHD', 'value': 0}
                ]

            # Get program level by year statistics
            program_level_by_year = []
            current_year = timezone.now().year
            years = range(current_year - 4, current_year + 1)  # Last 5 years

            try:
                # Define the program levels we want to track
                program_levels = ['BSC/BA', 'MSC/MBA', 'PHD']

                # Print debug information
                print("DEBUG: Fetching real program level data by year")

                # Use real data from the database
                for year in years:
                    year_start = timezone.make_aware(datetime(year, 1, 1))
                    year_end = timezone.make_aware(datetime(year + 1, 1, 1))

                    # Create a data point for this year
                    year_data = {'year': str(year)}  # Convert year to string for consistent XAxis rendering

                    # Get counts for each program level in this year
                    for level in program_levels:
                        # Query the database for real data
                        count = ApplicantInformation.objects.filter(
                            program_level=level,
                            created_at__gte=year_start,
                            created_at__lt=year_end
                        ).count()

                        # Print debug information
                        print(f"DEBUG: Year {year}, Level {level}, Count: {count}")

                        # Add to the year data - ensure we have at least 1 for visualization
                        # This ensures the chart is visible even with minimal data
                        year_data[level] = max(count, 1)

                    # Add this year's data to the result
                    program_level_by_year.append(year_data)

                # Print the final data structure
                print(f"DEBUG: Final program_level_by_year data: {program_level_by_year}")

            except Exception as e:
                print(f"Error getting program level by year: {str(e)}")
                import traceback
                traceback.print_exc()

                # If there's an error, create minimal data with count=1 for visualization
                for year in years:
                    program_level_by_year.append({
                        'year': str(year),
                        'BSC/BA': 1,
                        'MSC/MBA': 1,
                        'PHD': 1
                    })

            # Get gender by year statistics
            gender_by_year = []

            try:
                # Define the genders we want to track
                genders = ['Male', 'Female']

                # Print debug information
                print("DEBUG: Fetching real gender data by year")

                # Use real data from the database
                for year in years:
                    year_start = timezone.make_aware(datetime(year, 1, 1))
                    year_end = timezone.make_aware(datetime(year + 1, 1, 1))

                    # Create a data point for this year
                    year_data = {'year': str(year)}  # Convert year to string for consistent XAxis rendering

                    # Get counts for each gender in this year
                    for gender in genders:
                        # Query the database for real data
                        count = ApplicantInformation.objects.filter(
                            gender=gender,
                            created_at__gte=year_start,
                            created_at__lt=year_end
                        ).count()

                        # Print debug information
                        print(f"DEBUG: Year {year}, Gender {gender}, Count: {count}")

                        # Add to the year data - ensure we have at least 1 for visualization
                        # This ensures the chart is visible even with minimal data
                        year_data[gender] = max(count, 1)

                    # Add this year's data to the result
                    gender_by_year.append(year_data)

                # Print the final data structure
                print(f"DEBUG: Final gender_by_year data: {gender_by_year}")

            except Exception as e:
                print(f"Error getting gender by year: {str(e)}")
                import traceback
                traceback.print_exc()

                # If there's an error, create minimal data with count=1 for visualization
                for year in years:
                    gender_by_year.append({
                        'year': str(year),
                        'Male': 1,
                        'Female': 1
                    })

            # Get status data by time period (daily, weekly, monthly, yearly)
            status_by_period = self.get_status_by_period(request)

            # Get applicants by college with department and registrar status
            applicants_by_college = self.get_applicants_by_college()

            # Compile all statistics
            statistics = {
                'total_applications': total_applications,
                'new_applications': new_applications,
                'fifteen_day_applicants': fifteen_day_applicants,
                'today_applicants': today_applicants,
                'pending_review': pending_review,  # Keeping for backward compatibility
                'approval_rate': approval_rate,  # Keeping for backward compatibility
                'gender_distribution': {
                    'all_time': all_gender_distribution,
                    'thirty_day': thirty_day_gender_distribution,
                    'fifteen_day': fifteen_day_gender_distribution,
                    'today': today_gender_distribution
                },
                'applications_by_month': formatted_months,
                'status_data': status_data,
                'recent_applications': recent_applications,
                'gender_data': gender_data,
                'program_level_data': program_level_data,
                'program_level_by_year': program_level_by_year,
                'gender_by_year': gender_by_year,
                'status_by_period': status_by_period,
                # New metrics
                'registrar_metrics': {
                    'approved': registrar_approved,
                    'rejected': registrar_rejected,
                    'pending': registrar_pending,
                    'total': registrar_approved + registrar_rejected,  # Total processed (excluding pending)
                    'all_total': registrar_all_total,
                    'approval_rate': registrar_approval_rate,
                    'rejection_rate': registrar_rejection_rate,
                    'pending_rate': registrar_pending_rate,
                    'period_name': period_name,
                    'period_start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
                    'period_end_date': end_date.strftime('%Y-%m-%d') if end_date else None
                },
                'department_metrics': {
                    'approved': dept_approved,
                    'rejected': dept_rejected,
                    'pending': dept_pending,
                    'total': dept_approved + dept_rejected,  # Total processed (excluding pending)
                    'all_total': dept_all_total,
                    'approval_rate': dept_approval_rate,
                    'rejection_rate': dept_rejection_rate,
                    'pending_rate': dept_pending_rate,
                    'period_name': period_name,
                    'period_start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
                    'period_end_date': end_date.strftime('%Y-%m-%d') if end_date else None
                },
                # New data: applicants by college with department and registrar status
                'applicants_by_college': applicants_by_college
            }

            return Response(statistics)

        except Exception as e:
            print(f"Error generating application statistics: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {"error": "An error occurred while generating application statistics."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
