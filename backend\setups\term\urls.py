from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import TermViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'terms', TermViewSet, basename='term')

urlpatterns = [
    path('', include(router.urls)),
]

# URL patterns for terms:
# GET    /terms/                    - List all terms
# POST   /terms/                    - Create new term
# GET    /terms/{id}/               - Retrieve specific term
# PUT    /terms/{id}/               - Update specific term
# PATCH  /terms/{id}/               - Partially update specific term
# DELETE /terms/{id}/               - Delete specific term
