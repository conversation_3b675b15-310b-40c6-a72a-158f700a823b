import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { authAPI } from '@/services/api';
import { useAuth } from './AuthContext';

// Enhanced RBAC Types for Large User Base and Flexible Role System
export interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: string;
  app_label: string;
  model: string;
  description?: string;
}

export interface Role {
  id: number;
  name: string;
  description?: string;
  permissions: Permission[];
  level: number;
  parent_role?: number;
  is_system_role: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserRole {
  id: number;
  user_id: number;
  role: Role;
  granted_by: number;
  granted_at: string;
  expires_at?: string;
  is_active: boolean;
  context?: Record<string, any>; // For context-specific permissions
}

export interface RoleHierarchy {
  role: Role;
  children: RoleHierarchy[];
  inherited_permissions: Permission[];
}

export interface AccessContext {
  resource: string;
  action: string;
  object_id?: string;
  department?: string;
  project?: string;
  time_based?: boolean;
  ip_restrictions?: string[];
  additional_context?: Record<string, any>;
}

export interface EnhancedRBACContextType {
  // User and Authentication
  user: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Roles and Permissions
  userRoles: UserRole[];
  effectivePermissions: Permission[];
  roleHierarchy: RoleHierarchy[];
  
  // Permission Checking
  hasPermission: (permission: string, context?: AccessContext) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyRole: (roleNames: string[]) => boolean;
  hasAllRoles: (roleNames: string[]) => boolean;
  
  // Context-based Access Control
  canAccess: (resource: string, action: string, context?: AccessContext) => boolean;
  canAccessResource: (resource: string, context?: AccessContext) => boolean;
  canPerformAction: (action: string, context?: AccessContext) => boolean;
  
  // Role Management
  assignRole: (userId: number, roleId: number, context?: Record<string, any>) => Promise<void>;
  revokeRole: (userId: number, roleId: number) => Promise<void>;
  
  // Hierarchy and Inheritance
  getInheritedPermissions: (roleId: number) => Permission[];
  getRoleLevel: (roleName: string) => number;
  isRoleHigherThan: (role1: string, role2: string) => boolean;
  
  // Bulk Operations for Large User Base
  bulkAssignRoles: (userIds: number[], roleIds: number[]) => Promise<void>;
  bulkRevokeRoles: (userIds: number[], roleIds: number[]) => Promise<void>;
  
  // Caching and Performance
  refreshPermissions: () => Promise<void>;
  clearCache: () => void;
  
  // Audit and Logging
  getAccessLog: (userId?: number) => Promise<any[]>;
  logAccess: (resource: string, action: string, granted: boolean) => void;
}

const EnhancedRBACContext = createContext<EnhancedRBACContextType | undefined>(undefined);

interface EnhancedRBACProviderProps {
  children: ReactNode;
}

export const EnhancedRBACProvider: React.FC<EnhancedRBACProviderProps> = ({ children }) => {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [effectivePermissions, setEffectivePermissions] = useState<Permission[]>([]);
  const [roleHierarchy, setRoleHierarchy] = useState<RoleHierarchy[]>([]);
  const [permissionCache, setPermissionCache] = useState<Map<string, boolean>>(new Map());

  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserRoles();
    } else {
      setUserRoles([]);
      setEffectivePermissions([]);
      setRoleHierarchy([]);
      setPermissionCache(new Map());
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  const loadUserRoles = async () => {
    try {
      setIsLoading(true);
      
      // Load user roles with hierarchy and permissions
      const [rolesResponse, hierarchyResponse] = await Promise.all([
        authAPI.get(`/auth/users/${user.id}/roles/`),
        authAPI.get('/auth/role-hierarchy/')
      ]);
      
      setUserRoles(rolesResponse.data);
      setRoleHierarchy(hierarchyResponse.data);
      
      // Calculate effective permissions
      const permissions = calculateEffectivePermissions(rolesResponse.data, hierarchyResponse.data);
      setEffectivePermissions(permissions);
      
    } catch (error) {
      console.error('Error loading user roles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateEffectivePermissions = (roles: UserRole[], hierarchy: RoleHierarchy[]): Permission[] => {
    const permissionSet = new Set<string>();
    const permissions: Permission[] = [];
    
    roles.forEach(userRole => {
      if (userRole.is_active && (!userRole.expires_at || new Date(userRole.expires_at) > new Date())) {
        // Add direct permissions
        userRole.role.permissions.forEach(permission => {
          if (!permissionSet.has(permission.codename)) {
            permissionSet.add(permission.codename);
            permissions.push(permission);
          }
        });
        
        // Add inherited permissions from hierarchy
        const inherited = getInheritedPermissions(userRole.role.id);
        inherited.forEach(permission => {
          if (!permissionSet.has(permission.codename)) {
            permissionSet.add(permission.codename);
            permissions.push(permission);
          }
        });
      }
    });
    
    return permissions;
  };

  const hasPermission = (permission: string, context?: AccessContext): boolean => {
    // Check cache first for performance
    const cacheKey = `${permission}:${JSON.stringify(context || {})}`;
    if (permissionCache.has(cacheKey)) {
      return permissionCache.get(cacheKey)!;
    }
    
    // Superuser always has all permissions
    if (user?.is_superuser) {
      permissionCache.set(cacheKey, true);
      return true;
    }
    
    // Check direct permissions
    const hasDirectPermission = effectivePermissions.some(p => 
      p.codename === permission || p.name === permission
    );
    
    if (!hasDirectPermission) {
      permissionCache.set(cacheKey, false);
      return false;
    }
    
    // Context-based validation
    if (context) {
      const contextValid = validateContext(permission, context);
      permissionCache.set(cacheKey, contextValid);
      return contextValid;
    }
    
    permissionCache.set(cacheKey, true);
    return true;
  };

  const validateContext = (permission: string, context: AccessContext): boolean => {
    // Time-based access control
    if (context.time_based) {
      const now = new Date();
      const hour = now.getHours();
      // Example: Only allow during business hours (9 AM - 5 PM)
      if (hour < 9 || hour > 17) {
        return false;
      }
    }
    
    // IP-based restrictions
    if (context.ip_restrictions && context.ip_restrictions.length > 0) {
      // This would need actual IP checking logic
      // For now, we'll assume it's valid
    }
    
    // Department-based access
    if (context.department) {
      const userDepartment = user?.profile?.department;
      if (userDepartment !== context.department) {
        return false;
      }
    }
    
    // Project-based access
    if (context.project) {
      // Check if user has access to specific project
      // This would integrate with project management system
    }
    
    return true;
  };

  const hasRole = (roleName: string): boolean => {
    return userRoles.some(userRole => 
      userRole.is_active && 
      userRole.role.name === roleName &&
      (!userRole.expires_at || new Date(userRole.expires_at) > new Date())
    );
  };

  const hasAnyRole = (roleNames: string[]): boolean => {
    return roleNames.some(roleName => hasRole(roleName));
  };

  const hasAllRoles = (roleNames: string[]): boolean => {
    return roleNames.every(roleName => hasRole(roleName));
  };

  const canAccess = (resource: string, action: string, context?: AccessContext): boolean => {
    const permission = `${action}_${resource}`;
    return hasPermission(permission, context);
  };

  const canAccessResource = (resource: string, context?: AccessContext): boolean => {
    return canAccess(resource, 'view', context);
  };

  const canPerformAction = (action: string, context?: AccessContext): boolean => {
    // Check if user can perform this action on any resource
    return effectivePermissions.some(p => p.codename.includes(action));
  };

  const assignRole = async (userId: number, roleId: number, context?: Record<string, any>): Promise<void> => {
    try {
      await authAPI.post(`/auth/users/${userId}/assign-role/`, {
        role_id: roleId,
        context: context
      });
      
      if (userId === user?.id) {
        await loadUserRoles(); // Refresh current user's roles
      }
    } catch (error) {
      console.error('Error assigning role:', error);
      throw error;
    }
  };

  const revokeRole = async (userId: number, roleId: number): Promise<void> => {
    try {
      await authAPI.post(`/auth/users/${userId}/revoke-role/`, {
        role_id: roleId
      });
      
      if (userId === user?.id) {
        await loadUserRoles(); // Refresh current user's roles
      }
    } catch (error) {
      console.error('Error revoking role:', error);
      throw error;
    }
  };

  const getInheritedPermissions = (roleId: number): Permission[] => {
    const findRoleInHierarchy = (hierarchy: RoleHierarchy[], id: number): RoleHierarchy | null => {
      for (const item of hierarchy) {
        if (item.role.id === id) return item;
        const found = findRoleInHierarchy(item.children, id);
        if (found) return found;
      }
      return null;
    };
    
    const roleNode = findRoleInHierarchy(roleHierarchy, roleId);
    return roleNode?.inherited_permissions || [];
  };

  const getRoleLevel = (roleName: string): number => {
    const role = userRoles.find(ur => ur.role.name === roleName)?.role;
    return role?.level || 0;
  };

  const isRoleHigherThan = (role1: string, role2: string): boolean => {
    return getRoleLevel(role1) > getRoleLevel(role2);
  };

  const bulkAssignRoles = async (userIds: number[], roleIds: number[]): Promise<void> => {
    try {
      await authAPI.post('/auth/bulk-assign-roles/', {
        user_ids: userIds,
        role_ids: roleIds
      });
    } catch (error) {
      console.error('Error in bulk role assignment:', error);
      throw error;
    }
  };

  const bulkRevokeRoles = async (userIds: number[], roleIds: number[]): Promise<void> => {
    try {
      await authAPI.post('/auth/bulk-revoke-roles/', {
        user_ids: userIds,
        role_ids: roleIds
      });
    } catch (error) {
      console.error('Error in bulk role revocation:', error);
      throw error;
    }
  };

  const refreshPermissions = async (): Promise<void> => {
    setPermissionCache(new Map()); // Clear cache
    await loadUserRoles();
  };

  const clearCache = (): void => {
    setPermissionCache(new Map());
  };

  const getAccessLog = async (userId?: number): Promise<any[]> => {
    try {
      const response = await authAPI.get(`/auth/access-log/${userId || user?.id}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching access log:', error);
      return [];
    }
  };

  const logAccess = (resource: string, action: string, granted: boolean): void => {
    // Log access attempt for audit purposes
    authAPI.post('/auth/log-access/', {
      resource,
      action,
      granted,
      timestamp: new Date().toISOString()
    }).catch(error => {
      console.error('Error logging access:', error);
    });
  };

  const value: EnhancedRBACContextType = {
    user,
    isAuthenticated,
    isLoading: isLoading || authLoading,
    userRoles,
    effectivePermissions,
    roleHierarchy,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    canAccess,
    canAccessResource,
    canPerformAction,
    assignRole,
    revokeRole,
    getInheritedPermissions,
    getRoleLevel,
    isRoleHigherThan,
    bulkAssignRoles,
    bulkRevokeRoles,
    refreshPermissions,
    clearCache,
    getAccessLog,
    logAccess,
  };

  return (
    <EnhancedRBACContext.Provider value={value}>
      {children}
    </EnhancedRBACContext.Provider>
  );
};

export const useEnhancedRBAC = (): EnhancedRBACContextType => {
  const context = useContext(EnhancedRBACContext);
  if (context === undefined) {
    throw new Error('useEnhancedRBAC must be used within an EnhancedRBACProvider');
  }
  return context;
};

// Enhanced Route Protection Components
export const ProtectedRoute: React.FC<{
  children: ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  requiredRoles?: string[];
  requireAll?: boolean;
  context?: AccessContext;
  fallback?: ReactNode;
}> = ({ 
  children, 
  requiredPermission, 
  requiredRole, 
  requiredRoles, 
  requireAll = false,
  context,
  fallback 
}) => {
  const { isAuthenticated, isLoading, hasPermission, hasRole, hasAnyRole, hasAllRoles } = useEnhancedRBAC();
  const location = useLocation();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check permission
  if (requiredPermission && !hasPermission(requiredPermission, context)) {
    return fallback || (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
        <p className="text-muted-foreground">You don't have permission to access this resource.</p>
      </div>
    );
  }

  // Check single role
  if (requiredRole && !hasRole(requiredRole)) {
    return fallback || (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
        <p className="text-muted-foreground">You don't have the required role to access this resource.</p>
      </div>
    );
  }

  // Check multiple roles
  if (requiredRoles) {
    const hasAccess = requireAll ? hasAllRoles(requiredRoles) : hasAnyRole(requiredRoles);
    if (!hasAccess) {
      return fallback || (
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">You don't have the required roles to access this resource.</p>
        </div>
      );
    }
  }

  return <>{children}</>;
};

export default EnhancedRBACContext;
