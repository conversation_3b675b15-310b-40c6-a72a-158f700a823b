import { useState, useEffect } from 'react';
import { authAPI } from '@/services/api';

interface User {
  id: number;
  username?: string;
  email: string;
  is_staff?: boolean;
  is_superuser?: boolean;
}

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refresh_token');
    const userDataString = localStorage.getItem('user');

    if (userDataString) {
      try {
        const userData = JSON.parse(userDataString);
        setUser(userData);
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }

    if (!token || !refreshToken) {
      setIsAuthenticated(false);
      setIsLoading(false);
      return;
    }

    const refreshAccessToken = async () => {
      try {
        const response = await authAPI.refreshToken(refreshToken);
        localStorage.setItem('token', response.data.access);
        setIsAuthenticated(true);
      } catch (error) {
        setIsAuthenticated(false);
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('user');
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    refreshAccessToken();
  }, []);

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('user');
    setIsAuthenticated(false);
    setUser(null);
  };

  return { isAuthenticated, isLoading, user, logout };
};