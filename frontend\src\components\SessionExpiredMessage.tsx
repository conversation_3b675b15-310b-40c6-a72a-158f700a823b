import React, { useEffect, useState } from 'react';
import { <PERSON>ert<PERSON>riangle, Clock, Shield, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface SessionExpiredMessageProps {
  reason?: string;
  onDismiss?: () => void;
}

const SessionExpiredMessage: React.FC<SessionExpiredMessageProps> = ({ 
  reason, 
  onDismiss 
}) => {
  const [countdown, setCountdown] = useState(10);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (reason === 'session_expired' && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (countdown === 0) {
      setIsVisible(false);
      if (onDismiss) {
        onDismiss();
      }
    }
  }, [countdown, reason, onDismiss]);

  if (!isVisible || reason !== 'session_expired') {
    return null;
  }

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md border-orange-200 shadow-xl">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 border-b border-orange-100">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-500 rounded-lg">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg text-orange-600 flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Session Expired
              </CardTitle>
              <CardDescription className="text-gray-600">
                Your session has been terminated for security
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-6 space-y-4">
          <Alert className="border-orange-200 bg-orange-50">
            <Clock className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              You were automatically logged out after 15 minutes of inactivity to protect your account.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <h4 className="font-medium text-gray-800">Security Features:</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                Automatic session termination after inactivity
              </li>
              <li className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                Server-side session invalidation
              </li>
              <li className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                Secure token management
              </li>
            </ul>
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              Auto-dismiss in {countdown}s
            </div>
            <Button 
              onClick={handleDismiss}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Continue to Login
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SessionExpiredMessage;
