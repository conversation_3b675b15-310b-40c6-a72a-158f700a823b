import { Link, useLocation } from 'react-router-dom';
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from '@/lib/utils';
import { Home, Package, Download } from 'lucide-react';

const HeaderNav = () => {
  const location = useLocation();

  return (
    <NavigationMenu className="hidden lg:flex overflow-hidden">
      <NavigationMenuList className="gap-0.5 overflow-hidden">
        <NavigationMenuItem>
          <NavigationMenuLink
            asChild
            className={cn(
              navigationMenuTriggerStyle(),
              "text-white bg-transparent hover:bg-[#0e4a7d] transition-colors font-medium text-xs px-2 py-1.5 h-8",
              location.pathname === "/" ? "bg-[#0e4a7d]" : ""
            )}
          >
            <Link to="/" className="flex items-center">
              <Home className="mr-1.5 h-3 w-3" />
              Home
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink
            asChild
            className={cn(
              navigationMenuTriggerStyle(),
              "text-white bg-transparent hover:bg-[#0e4a7d] transition-colors font-medium text-xs px-2 py-1.5 h-8",
              location.pathname.startsWith("/programs") ? "bg-[#0e4a7d]" : ""
            )}
          >
            <Link to="/programs" className="flex items-center">
              <svg className="mr-1.5 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              Programs
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink
            asChild
            className={cn(
              navigationMenuTriggerStyle(),
              "text-white bg-transparent hover:bg-[#0e4a7d] transition-colors font-medium text-xs px-2 py-1.5 h-8",
              location.pathname.startsWith("/graduate-verification") ? "bg-[#0e4a7d]" : ""
            )}
          >
            <Link to="/graduate-verification" className="flex items-center">
              <svg className="mr-1.5 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Verify Graduate
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink
            asChild
            className={cn(
              navigationMenuTriggerStyle(),
              "text-white bg-transparent hover:bg-[#0e4a7d] transition-colors font-medium text-xs px-2 py-1.5 h-8",
              location.pathname.startsWith("/services") ? "bg-[#0e4a7d]" : ""
            )}
          >
            <Link to="/services" className="flex items-center">
              <svg className="mr-1.5 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              Services
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>



        <NavigationMenuItem>
          <NavigationMenuLink
            asChild
            className={cn(
              navigationMenuTriggerStyle(),
              "text-white bg-transparent hover:bg-[#0e4a7d] transition-colors font-medium text-xs px-2 py-1.5 h-8",
              location.pathname.startsWith("/track-official") ? "bg-[#0e4a7d]" : ""
            )}
          >
            <Link to="/track-official" className="flex items-center">
              <Package className="mr-1.5 h-3 w-3" />
              Track Official
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink
            asChild
            className={cn(
              navigationMenuTriggerStyle(),
              "text-white bg-transparent hover:bg-[#0e4a7d] transition-colors font-medium text-xs px-2 py-1.5 h-8",
              location.pathname.startsWith("/downloads") ? "bg-[#0e4a7d]" : ""
            )}
          >
            <Link to="/downloads" className="flex items-center">
              <Download className="mr-1.5 h-3 w-3" />
              Downloads
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export default HeaderNav;
