import React, { useState, useEffect } from 'react';

interface CaptchaProps {
  onVerify: (isVerified: boolean) => void;
}

const Captcha: React.FC<CaptchaProps> = ({ onVerify }) => {
  const [captchaText, setCaptchaText] = useState<string>('');
  const [userInput, setUserInput] = useState<string>('');
  const [isVerified, setIsVerified] = useState<boolean>(false);

  // Generate a random captcha text
  const generateCaptcha = () => {
    // Use only uppercase letters and numbers for better readability
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let result = '';
    // Generate a 5-character code for easier reading
    for (let i = 0; i < 5; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaText(result);
    setUserInput('');
    setIsVerified(false);
    onVerify(false);
  };

  // Generate captcha on component mount
  useEffect(() => {
    generateCaptcha();
  }, []);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUserInput(e.target.value);
  };

  // Handle key press (for Enter key)
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && userInput && !isVerified) {
      handleVerify();
    }
  };

  // Verify captcha (case-insensitive)
  const handleVerify = () => {
    // Make comparison case-insensitive for better user experience
    const verified = userInput.toUpperCase() === captchaText;
    setIsVerified(verified);
    onVerify(verified);

    if (verified) {
      console.log("CAPTCHA verification successful!");
    } else {
      console.log("CAPTCHA verification failed. Generating new CAPTCHA.");
      // If verification fails, generate a new captcha
      generateCaptcha();
    }
  };

  return (
    <div className="flex flex-col items-center space-y-3">
      <div className="flex w-full space-x-3 items-center">
        {/* CAPTCHA Text Display */}
        <div className="bg-gray-100 p-2 rounded-md text-center flex-shrink-0 w-1/3">
          <div
            className="font-mono text-xl font-bold select-none bg-blue-50 p-3 rounded-md border border-blue-200 flex items-center justify-center"
            style={{
              fontFamily: 'monospace',
              letterSpacing: '0.4em',
              color: '#1a73c0',
              textShadow: '1px 1px 0 rgba(0,0,0,0.1)',
              background: 'repeating-linear-gradient(45deg, #f0f9ff, #f0f9ff 10px, #e0f2fe 10px, #e0f2fe 20px)',
              height: '42px'
            }}
          >
            {captchaText}
          </div>
        </div>

        {/* Input and Buttons */}
        <div className="flex flex-1 space-x-2">
          <input
            type="text"
            value={userInput}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter the code shown"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-center uppercase"
            disabled={isVerified}
            autoComplete="off"
            maxLength={5}
            autoFocus
          />

          <button
            type="button"
            onClick={handleVerify}
            className={`px-4 py-2 rounded-md ${
              isVerified
                ? 'bg-green-500 text-white cursor-not-allowed'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
            disabled={isVerified || !userInput}
          >
            {isVerified ? 'Verified' : 'Verify'}
          </button>

          <button
            type="button"
            onClick={generateCaptcha}
            className="px-3 py-2 bg-gray-200 hover:bg-gray-300 rounded-md"
            title="Generate new code"
          >
            ↻
          </button>
        </div>
      </div>

      {isVerified && (
        <div className="text-green-500 text-sm flex items-center">
          <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Verification successful
        </div>
      )}
    </div>
  );
};

export default Captcha;
