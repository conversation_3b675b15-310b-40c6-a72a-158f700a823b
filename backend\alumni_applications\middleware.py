"""
Security middleware for alumni applications.
"""
import time
import logging
from django.http import JsonResponse
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

# Optional cache import
try:
    from django.core.cache import cache
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False
    cache = None

logger = logging.getLogger(__name__)

class FileUploadRateLimitMiddleware(MiddlewareMixin):
    """Rate limiting middleware for file uploads."""
    
    def __init__(self, get_response):
        self.get_response = get_response
        # Rate limiting configuration
        self.max_uploads_per_minute = getattr(settings, 'MAX_FILE_UPLOADS_PER_MINUTE', 10)
        self.max_uploads_per_hour = getattr(settings, 'MAX_FILE_UPLOADS_PER_HOUR', 50)
        self.max_file_size_per_minute = getattr(settings, 'MAX_FILE_SIZE_PER_MINUTE', 50 * 1024 * 1024)  # 50MB
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process incoming requests for file upload rate limiting."""
        # Only apply to file upload endpoints
        if not self._is_file_upload_request(request):
            return None
        
        # Get client identifier
        client_id = self._get_client_identifier(request)
        
        # Check rate limits
        rate_limit_result = self._check_rate_limits(request, client_id)
        
        if not rate_limit_result['allowed']:
            logger.warning(f"Rate limit exceeded for client {client_id}: {rate_limit_result['reason']}")
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': rate_limit_result['reason'],
                'retry_after': rate_limit_result.get('retry_after', 60)
            }, status=429)
        
        return None
    
    def process_response(self, request, response):
        """Process response to update rate limiting counters."""
        if self._is_file_upload_request(request) and response.status_code == 201:
            # Successful upload, update counters
            client_id = self._get_client_identifier(request)
            self._update_rate_limit_counters(request, client_id)
        
        return response
    
    def _is_file_upload_request(self, request):
        """Check if request is a file upload."""
        return (
            request.method == 'POST' and
            request.content_type and
            'multipart/form-data' in request.content_type and
            ('upload_document' in request.path or '/documents/' in request.path)
        )
    
    def _get_client_identifier(self, request):
        """Get unique client identifier for rate limiting."""
        # Use IP address as primary identifier
        ip_address = self._get_client_ip(request)
        
        # If user is authenticated, use user ID as well
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"user_{request.user.id}_{ip_address}"
        
        return f"ip_{ip_address}"
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _check_rate_limits(self, request, client_id):
        """Check if client has exceeded rate limits."""
        if not CACHE_AVAILABLE or not cache:
            # If cache is not available, allow the request but log a warning
            logger.warning("Cache not available for rate limiting - allowing request")
            return {'allowed': True}

        current_time = int(time.time())

        # Check uploads per minute
        minute_key = f"upload_count_minute_{client_id}_{current_time // 60}"
        minute_count = cache.get(minute_key, 0)

        if minute_count >= self.max_uploads_per_minute:
            return {
                'allowed': False,
                'reason': f'Too many uploads per minute (max {self.max_uploads_per_minute})',
                'retry_after': 60
            }

        # Check uploads per hour
        hour_key = f"upload_count_hour_{client_id}_{current_time // 3600}"
        hour_count = cache.get(hour_key, 0)

        if hour_count >= self.max_uploads_per_hour:
            return {
                'allowed': False,
                'reason': f'Too many uploads per hour (max {self.max_uploads_per_hour})',
                'retry_after': 3600
            }

        # Check file size per minute
        if hasattr(request, 'FILES') and request.FILES:
            total_size = sum(f.size for f in request.FILES.values())
            size_key = f"upload_size_minute_{client_id}_{current_time // 60}"
            minute_size = cache.get(size_key, 0)

            if minute_size + total_size > self.max_file_size_per_minute:
                return {
                    'allowed': False,
                    'reason': f'File size limit exceeded per minute (max {self.max_file_size_per_minute // (1024*1024)}MB)',
                    'retry_after': 60
                }

        return {'allowed': True}
    
    def _update_rate_limit_counters(self, request, client_id):
        """Update rate limiting counters after successful upload."""
        if not CACHE_AVAILABLE or not cache:
            # If cache is not available, just log and return
            logger.warning("Cache not available for rate limiting counter update")
            return

        current_time = int(time.time())

        # Update minute counter
        minute_key = f"upload_count_minute_{client_id}_{current_time // 60}"
        cache.set(minute_key, cache.get(minute_key, 0) + 1, 60)

        # Update hour counter
        hour_key = f"upload_count_hour_{client_id}_{current_time // 3600}"
        cache.set(hour_key, cache.get(hour_key, 0) + 1, 3600)

        # Update size counter
        if hasattr(request, 'FILES') and request.FILES:
            total_size = sum(f.size for f in request.FILES.values())
            size_key = f"upload_size_minute_{client_id}_{current_time // 60}"
            cache.set(size_key, cache.get(size_key, 0) + total_size, 60)


class FileUploadSecurityMiddleware(MiddlewareMixin):
    """Security middleware for file uploads."""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Add security headers and validation for file uploads."""
        if self._is_file_upload_request(request):
            # Add security headers
            self._add_security_headers(request)
            
            # Validate request
            validation_result = self._validate_upload_request(request)
            if not validation_result['valid']:
                logger.warning(f"Invalid upload request: {validation_result['reason']}")
                return JsonResponse({
                    'error': 'Invalid request',
                    'message': validation_result['reason']
                }, status=400)
        
        return None
    
    def process_response(self, request, response):
        """Add security headers to response."""
        if self._is_file_upload_request(request):
            # Add security headers
            response['X-Content-Type-Options'] = 'nosniff'
            response['X-Frame-Options'] = 'DENY'
            response['X-XSS-Protection'] = '1; mode=block'
            response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        return response
    
    def _is_file_upload_request(self, request):
        """Check if request is a file upload."""
        return (
            request.method == 'POST' and
            request.content_type and
            'multipart/form-data' in request.content_type and
            ('upload_document' in request.path or '/documents/' in request.path)
        )
    
    def _add_security_headers(self, request):
        """Add security headers to request."""
        # Log upload attempt
        logger.info(f"File upload attempt from {self._get_client_ip(request)} to {request.path}")
    
    def _validate_upload_request(self, request):
        """Validate upload request for security issues."""
        # Check for suspicious headers
        suspicious_headers = ['X-Forwarded-Host', 'X-Original-URL', 'X-Rewrite-URL']
        for header in suspicious_headers:
            if header in request.META:
                return {
                    'valid': False,
                    'reason': f'Suspicious header detected: {header}'
                }
        
        # Check content length
        content_length = request.META.get('CONTENT_LENGTH')
        if content_length:
            try:
                length = int(content_length)
                max_length = 15 * 1024 * 1024  # 15MB (slightly more than file limit for form data)
                if length > max_length:
                    return {
                        'valid': False,
                        'reason': f'Request too large: {length} bytes (max {max_length})'
                    }
            except ValueError:
                return {
                    'valid': False,
                    'reason': 'Invalid Content-Length header'
                }
        
        return {'valid': True}
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
