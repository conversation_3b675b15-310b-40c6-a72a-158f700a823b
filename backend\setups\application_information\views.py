from django.utils import timezone
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import ApplicationInformation
from .serializers import ApplicationInformationSerializer
from setups.registration_period.models import RegistrationPeriod
from setups.program.models import Program

# Create your views here.

# Filter view for ApplicationInformation
class ApplicationInformationFilter(generics.ListAPIView):
    serializer_class = ApplicationInformationSerializer
    permission_classes = [AllowAny]  # Allow any user to search

    def get_queryset(self):
        # Start with all active application information records
        queryset = ApplicationInformation.objects.filter(status=True)

        # Print the total number of records for debugging
        print(f"Total ApplicationInformation records: {queryset.count()}")

        # Get query parameters
        college_id = self.request.query_params.get('college')
        department_id = self.request.query_params.get('department')
        program_id = self.request.query_params.get('program')
        admission_type_id = self.request.query_params.get('admission_type')

        # Print the filter parameters for debugging
        print(f"Filter parameters: college={college_id}, department={department_id}, program={program_id}, admission_type={admission_type_id}")

        # If no filters are provided, return all records
        if not any([college_id, department_id, program_id, admission_type_id]):
            print("No filters provided, returning all records")
            return queryset

        # If all filters are '0' (meaning 'All'), return all records
        if (college_id == '0' or not college_id) and \
           (department_id == '0' or not department_id) and \
           (program_id == '0' or not program_id) and \
           (admission_type_id == '0' or not admission_type_id):
            print("All filters are 'All', returning all records")
            return queryset

        # Apply filters if parameters are provided
        if college_id and college_id != '0':
            try:
                # Try to filter by college_id
                queryset = queryset.filter(college_id=int(college_id))
                print(f"Filtered by college_id={college_id}, remaining records: {queryset.count()}")
            except (ValueError, Exception) as e:
                print(f"Error filtering by college_id: {e}")
                # If there's an error, don't apply this filter
                pass

        if department_id and department_id != '0':
            try:
                # Try to filter by department_id
                queryset = queryset.filter(department_id=int(department_id))
                print(f"Filtered by department_id={department_id}, remaining records: {queryset.count()}")
            except (ValueError, Exception) as e:
                print(f"Error filtering by department_id: {e}")
                # If there's an error, don't apply this filter
                pass

        if program_id and program_id != '0':
            try:
                # Try to filter by program_id
                queryset = queryset.filter(program_id=int(program_id))
                print(f"Filtered by program_id={program_id}, remaining records: {queryset.count()}")
            except (ValueError, Exception) as e:
                print(f"Error filtering by program_id: {e}")
                # If there's an error, don't apply this filter
                pass

        if admission_type_id and admission_type_id != '0':
            try:
                # Check if admission_type_id is a string like 'Regular', 'Extension', etc.
                if admission_type_id in ['Regular', 'Extension', 'Distance']:
                    # If it's a name, filter by the name
                    queryset = queryset.filter(admission_type__name=admission_type_id)
                    print(f"Filtered by admission_type_name={admission_type_id}, remaining records: {queryset.count()}")
                else:
                    try:
                        # Try to use it as an ID
                        queryset = queryset.filter(admission_type_id=int(admission_type_id))
                        print(f"Filtered by admission_type_id={admission_type_id}, remaining records: {queryset.count()}")
                    except ValueError:
                        # If it's not a valid ID, don't apply this filter
                        print(f"Invalid admission_type_id: {admission_type_id}, skipping this filter")
                        pass
            except Exception as e:
                print(f"Error filtering by admission_type_id: {e}")
                # If there's an error, don't apply this filter
                pass

        # If no records match the filters, return an empty queryset
        if queryset.count() == 0:
            print("No records match the filters")

        return queryset

# List and Create view for ApplicationInformation (GET, POST)
class ApplicationInformationList(generics.ListCreateAPIView):
    serializer_class = ApplicationInformationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ApplicationInformation.objects.all()

    def perform_create(self, serializer):
        if serializer.is_valid():
            serializer.save()
        else:
            print(serializer.errors)

# Delete view for AdmissionType (DELETE)
class ApplicationInformationDelete(generics.DestroyAPIView):
    serializer_class = ApplicationInformationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ApplicationInformation.objects.all()

# Retrieve and Update view for AdmissionType (GET, PUT)
class ApplicationInformationDetail(generics.RetrieveUpdateAPIView):
    serializer_class = ApplicationInformationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ApplicationInformation.objects.all()


# Program-specific ApplicationInformation with registration period check
class ProgramApplicationView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        # Get the program ID from the query parameters
        program_id = request.query_params.get('program_id')

        if not program_id:
            return Response(
                {"error": "Program ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get the current datetime
            now = timezone.now()

            # Check if there's a registration period for this program where current time is between open and close dates
            registration_period = RegistrationPeriod.objects.filter(
                program_id=program_id,
                open_date__lte=now,
                close_date__gt=now
            ).first()

            # Also check for any registration period for this program (for status messages)
            any_registration_period = RegistrationPeriod.objects.filter(
                program_id=program_id
            ).first() if not registration_period else registration_period

            # Get application information for this program where:
            # 1. Application information program equals the selected program
            # 2. Application information program equals registration period program
            # 3. Current datetime is between registration period open_date and close_date

            # First, get all active registration periods for this program where:
            # 1. Current time is between open and close dates
            # 2. The registration period is marked as active in the system
            active_registration_periods = RegistrationPeriod.objects.filter(
                program_id=program_id,
                open_date__lte=now,
                close_date__gt=now,
                is_active=True
            )

            # Always start with empty application info
            application_info = ApplicationInformation.objects.none()

            # Only get application information if there are active registration periods
            if active_registration_periods.exists():
                # Get application information for this program
                application_info = ApplicationInformation.objects.filter(
                    program_id=program_id,
                    status=True,  # Only active application information
                ).distinct()  # Use distinct to avoid duplicates if multiple registration periods exist

            # Serialize the application information
            serializer = ApplicationInformationSerializer(application_info, many=True)

            # Prepare the response
            response_data = {
                "application_info": serializer.data,
                "registration_active": False,
                "registration_message": "Registration is not currently open for this program.",
                "registration_period": None
            }

            # If there's a registration period where current time is between open and close dates
            if registration_period:
                # Check if the registration period is marked as active
                if registration_period.is_active:
                    response_data["registration_active"] = True
                    response_data["registration_message"] = "Registration is currently open for this program."
                    response_data["registration_period"] = {
                        "id": registration_period.id,
                        "program": registration_period.program_id,
                        "program_name": registration_period.program.program_name,
                        "open_date": registration_period.open_date,
                        "close_date": registration_period.close_date,
                        "is_active": registration_period.is_active
                    }
                else:
                    response_data["registration_message"] = "Registration period exists but is not active in the system."
            elif any_registration_period:
                # If there's a registration period but current time is not between open and close dates
                if any_registration_period.open_date > now:
                    # Future registration period
                    response_data["registration_message"] = f"Registration will open on {any_registration_period.open_date.strftime('%Y-%m-%d %H:%M')}."
                    response_data["registration_period"] = {
                        "id": any_registration_period.id,
                        "program": any_registration_period.program_id,
                        "program_name": any_registration_period.program.program_name,
                        "open_date": any_registration_period.open_date,
                        "close_date": any_registration_period.close_date,
                        "is_active": any_registration_period.is_active
                    }
                elif any_registration_period.close_date <= now:
                    # Past registration period
                    response_data["registration_message"] = f"Registration closed on {any_registration_period.close_date.strftime('%Y-%m-%d %H:%M')}."
                    response_data["registration_period"] = {
                        "id": any_registration_period.id,
                        "program": any_registration_period.program_id,
                        "program_name": any_registration_period.program.program_name,
                        "open_date": any_registration_period.open_date,
                        "close_date": any_registration_period.close_date,
                        "is_active": any_registration_period.is_active
                    }
                else:
                    # This shouldn't happen, but just in case
                    response_data["registration_message"] = "Registration period exists but has an invalid status."
            else:
                # Check if there's a future registration period
                future_period = RegistrationPeriod.objects.filter(
                    program_id=program_id,
                    open_date__gt=now
                ).order_by('open_date').first()

                if future_period:
                    response_data["registration_message"] = f"Registration will open on {future_period.open_date.strftime('%Y-%m-%d %H:%M')}."
                    response_data["registration_period"] = {
                        "id": future_period.id,
                        "program": future_period.program_id,
                        "program_name": future_period.program.program_name,
                        "open_date": future_period.open_date,
                        "close_date": future_period.close_date,
                        "is_active": future_period.is_active
                    }

            return Response(response_data)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# API endpoint to get all programs with their registration status and application info
class ProgramsWithRegistrationStatusView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # Get the current datetime
            now = timezone.now()
            print(f"Current datetime: {now}")

            # Get all programs
            programs = Program.objects.all().order_by('program_name')
            print(f"Found {programs.count()} programs")

            result = []

            for program in programs:
                print(f"Processing program: {program.program_code} (ID: {program.id})")

                # Check if there's an active registration period for this program
                # using the is_open() logic: current time between dates AND is_active=True
                registration_periods = RegistrationPeriod.objects.filter(
                    program=program,
                    open_date__lte=now,
                    close_date__gt=now,
                    is_active=True
                )

                # Determine if registration is active
                registration_active = registration_periods.exists()

                # Get registration message
                registration_message = "Registration is not currently open for this program."
                if registration_active:
                    registration_message = "Registration is currently open for this program."
                    print(f"Program {program.id} has active registration")
                else:
                    print(f"Program {program.id} has no active registration")

                # Get application information for this program
                # We get application info regardless of registration status,
                # but we'll only display it in the frontend if registration is active
                app_info = ApplicationInformation.objects.filter(
                    program=program,
                    status=True  # Only get active application information
                )

                application_info = []
                if app_info.exists():
                    serializer = ApplicationInformationSerializer(app_info, many=True)
                    application_info = serializer.data
                    print(f"Found {len(application_info)} application info records for program {program.id}")
                else:
                    print(f"No application info found for program {program.id}")

                # Add program with registration status to result
                program_data = {
                    "id": program.id,
                    "program_code": program.program_code,
                    "program_name": program.program_name,
                    "program_type": self._infer_program_type(program),
                    "registration_fee": program.registration_fee,
                    "registration_active": registration_active,
                    "registration_message": registration_message,
                    "application_info": application_info
                }

                result.append(program_data)

            print(f"Returning {len(result)} programs with status")
            return Response(result)

        except Exception as e:
            import traceback
            print(f"Error in ProgramsWithRegistrationStatusView: {str(e)}")
            print(traceback.format_exc())
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _infer_program_type(self, program):
        """Infer program type from program code"""
        if not program.program_code:
            return "undergraduate"

        code = program.program_code.lower()

        # Check for postgraduate indicators
        if any(indicator in code for indicator in ['msc', 'phd', 'ma', 'mba', 'postgrad']):
            return "postgraduate"

        # Check for undergraduate indicators
        if any(indicator in code for indicator in ['bsc', 'ba', 'undergrad']):
            return "undergraduate"

        # Default to undergraduate
        return "undergraduate"