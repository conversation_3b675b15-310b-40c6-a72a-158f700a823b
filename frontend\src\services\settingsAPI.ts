import axios from 'axios';
import api from './api';
import settingsCacheService from './settingsCacheService';

// Use the configured API instance

// Organization Settings Types
export interface OrganizationSetting {
  id?: number;
  system_name: string;
  organization: string;
  copyright: string;
  contact_info: string;
  contact_number: string;
  support_email: string;
  address: string;
  po_box?: string;
  header_logo?: File | null;
  footer_logo?: File | null;
  favicon?: File | null;
  header_logo_url?: string;
  footer_logo_url?: string;
  favicon_url?: string;
  primary_color: string;
  secondary_color: string;
  account_inactivity_period?: number;
  updated_at?: string;
}

// Quick Link Types
export interface QuickLink {
  id?: number;
  name: string;
  url: string;
  description?: string;
  is_external: boolean;
  order: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

// Social Media Link Types
export interface SocialMediaLink {
  id?: number;
  platform: string;
  platform_display?: string;
  url: string;
  icon?: File | null;
  icon_url?: string;
  display_name?: string;
  order: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

// Reorder Item Type
export interface ReorderItem {
  id: number;
  order: number;
}

// SMTP Settings Types
export interface SMTPSettings {
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_password: string;
  smtp_use_tls: boolean;
  smtp_use_ssl: boolean;
  smtp_from_email: string;
  smtp_from_name: string;
  smtp_timeout: number;
  smtp_enabled: boolean;
}

export interface SMTPTestRequest extends SMTPSettings {
  test_email: string;
}

// Settings API Service
const settingsAPI = {
  // Organization Settings
  getOrganizationSettings: async () => {
    return api.get(`/settings/organization/`);
  },

  getPublicOrganizationSettings: async () => {
    // Check if we have cached data
    const cachedSettings = settingsCacheService.getOrganizationSettings();
    if (cachedSettings) {
      // Return cached data in the same format as the API response
      return { data: cachedSettings };
    }

    // If no cache or expired, fetch from API
    const response = await api.get(`/settings/organization/public/`);

    // Cache the response data
    if (response.data) {
      settingsCacheService.setOrganizationSettings(response.data);
    }

    return response;
  },

  updateOrganizationSettings: async (data: OrganizationSetting) => {
    // Use FormData to handle file uploads
    const formData = new FormData();

    // Add all text fields
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && !['header_logo', 'footer_logo', 'favicon'].includes(key)) {
        formData.append(key, value.toString());
      }
    });

    // Add file fields if they exist
    if (data.header_logo instanceof File) {
      formData.append('header_logo', data.header_logo);
    }

    if (data.footer_logo instanceof File) {
      formData.append('footer_logo', data.footer_logo);
    }

    if (data.favicon instanceof File) {
      formData.append('favicon', data.favicon);
    }

    const response = await api.put(`/settings/organization/1/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Clear the cache when settings are updated
    settingsCacheService.clearCache();

    return response;
  },

  // Quick Links
  getAllQuickLinks: async () => {
    return api.get(`/settings/quick-links/`);
  },

  getPublicQuickLinks: async () => {
    // Check if we have cached data
    const cachedLinks = settingsCacheService.getQuickLinks();
    if (cachedLinks) {
      // Return cached data in the same format as the API response
      return { data: cachedLinks };
    }

    // If no cache or expired, fetch from API
    const response = await api.get(`/settings/quick-links/public/`);

    // Cache the response data
    if (response.data) {
      settingsCacheService.setQuickLinks(response.data);
    }

    return response;
  },

  createQuickLink: async (data: QuickLink) => {
    const response = await api.post(`/settings/quick-links/`, data);
    settingsCacheService.clearCache();
    return response;
  },

  updateQuickLink: async (id: number, data: QuickLink) => {
    const response = await api.put(`/settings/quick-links/${id}/`, data);
    settingsCacheService.clearCache();
    return response;
  },

  deleteQuickLink: async (id: number) => {
    const response = await api.delete(`/settings/quick-links/${id}/`);
    settingsCacheService.clearCache();
    return response;
  },

  reorderQuickLinks: async (items: ReorderItem[]) => {
    const response = await api.post(`/settings/quick-links/reorder/`, items);
    settingsCacheService.clearCache();
    return response;
  },

  // Social Media Links
  getAllSocialMediaLinks: async () => {
    return api.get(`/settings/social-media/`);
  },

  getPublicSocialMediaLinks: async () => {
    // Check if we have cached data
    const cachedLinks = settingsCacheService.getSocialMediaLinks();
    if (cachedLinks) {
      // Return cached data in the same format as the API response
      return { data: cachedLinks };
    }

    // If no cache or expired, fetch from API
    const response = await api.get(`/settings/social-media/public/`);

    // Cache the response data
    if (response.data) {
      settingsCacheService.setSocialMediaLinks(response.data);
    }

    return response;
  },

  createSocialMediaLink: async (data: SocialMediaLink) => {
    // Use FormData to handle file uploads
    const formData = new FormData();

    // Add all text fields
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && key !== 'icon') {
        formData.append(key, value.toString());
      }
    });

    // Add icon if it exists
    if (data.icon instanceof File) {
      formData.append('icon', data.icon);
    }

    const response = await api.post(`/settings/social-media/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Clear the cache when settings are updated
    settingsCacheService.clearCache();

    return response;
  },

  updateSocialMediaLink: async (id: number, data: SocialMediaLink) => {
    // Use FormData to handle file uploads
    const formData = new FormData();

    // Add all text fields
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && key !== 'icon') {
        formData.append(key, value.toString());
      }
    });

    // Add icon if it exists
    if (data.icon instanceof File) {
      formData.append('icon', data.icon);
    }

    const response = await api.put(`/settings/social-media/${id}/`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Clear the cache when settings are updated
    settingsCacheService.clearCache();

    return response;
  },

  deleteSocialMediaLink: async (id: number) => {
    const response = await api.delete(`/settings/social-media/${id}/`);
    settingsCacheService.clearCache();
    return response;
  },

  reorderSocialMediaLinks: async (items: ReorderItem[]) => {
    const response = await api.post(`/settings/social-media/reorder/`, items);
    settingsCacheService.clearCache();
    return response;
  },

  // SMTP Settings
  getSMTPSettings: async () => {
    return api.get(`/settings/smtp/`);
  },

  updateSMTPSettings: async (data: SMTPSettings) => {
    const response = await api.post(`/settings/smtp/`, data);
    return response;
  },

  testSMTPConnection: async (data: { recipient: string }) => {
    return api.post(`/settings/smtp/test/`, data);
  },

  getSMTPProviders: async () => {
    return api.get(`/settings/smtp/providers/`);
  },

  applySMTPProvider: async (data: { provider: string }) => {
    return api.post(`/settings/smtp/apply-provider/`, data);
  },

  getSMTPDebugInfo: async () => {
    return api.get(`/settings/smtp/debug/`);
  },
};

export default settingsAPI;
