import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import {
  Download,
  FileText,
  Calendar,
  HardDrive,
  ExternalLink,
  Eye
} from 'lucide-react';
import { downloadableAPI, Downloadable } from '@/services/downloadableAPI';

const DownloadableList = () => {
  const [downloadables, setDownloadables] = useState<Downloadable[]>([]);
  const [loading, setLoading] = useState(true);

  // Memoized fetch function to prevent unnecessary re-renders
  const fetchDownloadables = useCallback(async () => {
    try {
      setLoading(true);
      const response = await downloadableAPI.getDownloadables();
      // Filter only active downloadables for public view
      const activeDownloadables = response.results.filter(item => item.is_active);
      setDownloadables(activeDownloadables);
    } catch (error) {
      console.error('Error fetching downloadables:', error);
      toast.error('Failed to fetch downloadable files');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDownloadables();
  }, [fetchDownloadables]);

  // Memoized download handler
  const handleDownload = useCallback(async (id: string, title: string) => {
    try {
      await downloadableAPI.downloadFile(id);
      toast.success(`Downloading ${title}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Failed to download file');
    }
  }, []);

  // Memoized view handler
  const handleView = useCallback(async (id: string, title: string) => {
    try {
      await downloadableAPI.viewFile(id);
      toast.success(`Opening ${title}`);
    } catch (error) {
      console.error('Error viewing file:', error);
      toast.error('Failed to open file');
    }
  }, []);

  // Memoized file item component to prevent unnecessary re-renders
  const FileItem = useMemo(() => React.memo(({ downloadable }: { downloadable: Downloadable }) => {
    const isViewable = downloadableAPI.isViewableFile(downloadable.file_name || '');

    return (
      <Card key={downloadable.id} className="border border-gray-200 hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-base font-medium text-gray-900 truncate">
                  {downloadable.title}
                </CardTitle>
              </div>
            </div>
            <Badge variant="default" className="bg-green-100 text-green-800">
              Available
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* File Information */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <FileText className="h-4 w-4" />
                <span className="truncate">
                  {downloadable.file_name || 'Download file'}
                </span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <HardDrive className="h-4 w-4" />
                <span>
                  {downloadableAPI.formatFileSize(downloadable.file_size)}
                </span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Calendar className="h-4 w-4" />
                <span>
                  Added {new Date(downloadable.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="pt-2">
              {isViewable ? (
                <div className="flex space-x-2">
                  <Button
                    onClick={() => handleView(downloadable.id, downloadable.title)}
                    variant="outline"
                    className="flex-1 border-green-500 text-green-600 hover:bg-green-500 hover:text-white hover:border-green-500 transition-all duration-200"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button
                    onClick={() => handleDownload(downloadable.id, downloadable.title)}
                    className="flex-1 bg-[#1a73c0] hover:bg-blue-600 text-white transition-all duration-200"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              ) : (
                <Button
                  onClick={() => handleDownload(downloadable.id, downloadable.title)}
                  className="w-full bg-[#1a73c0] hover:bg-blue-600 text-white transition-all duration-200"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }), [handleView, handleDownload]);

  return (
    <div className="space-y-6">
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0] mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading files...</p>
        </div>
      ) : downloadables.length === 0 ? (
        <div className="text-center py-8">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No files available</h3>
          <p className="text-gray-600">There are currently no downloadable files available.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {downloadables.map((downloadable) => (
            <FileItem key={downloadable.id} downloadable={downloadable} />
          ))}
        </div>
      )}
    </div>
  );
};

export default DownloadableList;
