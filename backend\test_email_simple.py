#!/usr/bin/env python3
"""
Simple Email Test for Alumni Applications
Tests email sending functionality with minimal setup
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings
from settings_manager.smtp_models import SMTPSettings
from alumni_applications.email_service import AlumniApplicationEmailService
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleEmailTest:
    def __init__(self):
        self.test_results = []
        
    def log_result(self, test_name, success, message):
        """Log test result"""
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}: {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })

    def test_smtp_settings(self):
        """Test SMTP settings configuration"""
        print("\n🔧 Testing SMTP Settings...")
        
        try:
            smtp_settings = SMTPSettings.load()
            
            if smtp_settings and smtp_settings.host:
                self.log_result("SMTP Configuration", True, f"Host: {smtp_settings.host}:{smtp_settings.port}")
                self.log_result("SMTP Username", True, f"Username: {smtp_settings.username}")
                self.log_result("SMTP From Email", True, f"From: {smtp_settings.from_email}")
                return True
            else:
                self.log_result("SMTP Configuration", False, "No SMTP host configured")
                return False
                
        except Exception as e:
            self.log_result("SMTP Configuration", False, f"Error: {e}")
            return False

    def test_django_settings(self):
        """Test Django email settings"""
        print("\n⚙️ Testing Django Settings...")
        
        try:
            self.log_result("EMAIL_BACKEND", True, f"{settings.EMAIL_BACKEND}")
            
            # Check if we have basic email settings
            if hasattr(settings, 'EMAIL_HOST'):
                self.log_result("EMAIL_HOST", True, f"{settings.EMAIL_HOST}")
            else:
                self.log_result("EMAIL_HOST", False, "Not configured")
            
            return True
            
        except Exception as e:
            self.log_result("Django Settings", False, f"Error: {e}")
            return False

    def test_console_email(self):
        """Test email with console backend"""
        print("\n📧 Testing Console Email...")
        
        try:
            # Force console backend for testing
            original_backend = settings.EMAIL_BACKEND
            settings.EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
            
            result = send_mail(
                subject='Test Email - Alumni Portal',
                message='This is a test email from the Alumni Portal.',
                from_email='<EMAIL>',
                recipient_list=['<EMAIL>'],
                fail_silently=False
            )
            
            # Restore original backend
            settings.EMAIL_BACKEND = original_backend
            
            self.log_result("Console Email", True, f"Email sent (result: {result})")
            return True
            
        except Exception as e:
            self.log_result("Console Email", False, f"Error: {e}")
            return False

    def test_email_service(self):
        """Test the alumni email service"""
        print("\n🎓 Testing Alumni Email Service...")
        
        try:
            # Create a mock application object
            class MockApplication:
                def __init__(self):
                    self.id = 'test-123'
                    self.first_name = 'Test'
                    self.father_name = 'Email'
                    self.last_name = 'User'
                    self.email = '<EMAIL>'
                    self.transaction_id = 'ABC123'
                    self.created_at = django.utils.timezone.now()
                    self.service_type = MockServiceType()
            
            class MockServiceType:
                def __init__(self):
                    self.name = 'Official Transcript'
                    self.fee = 150.00
            
            # Force console backend for testing
            original_backend = settings.EMAIL_BACKEND
            settings.EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
            
            mock_app = MockApplication()
            
            # Test email service
            result = AlumniApplicationEmailService.send_application_confirmation(
                application=mock_app,
                is_form1=True
            )
            
            # Restore original backend
            settings.EMAIL_BACKEND = original_backend
            
            self.log_result("Email Service", result, 
                          "Email service working" if result else "Email service failed")
            return result
            
        except Exception as e:
            self.log_result("Email Service", False, f"Error: {e}")
            return False

    def test_template_rendering(self):
        """Test email template rendering"""
        print("\n📄 Testing Template Rendering...")
        
        try:
            from django.template.loader import render_to_string
            
            context = {
                'applicant_name': 'Test User',
                'transaction_id': 'ABC123',
                'service_type': 'Official Transcript',
                'service_fee': '150.00',
                'application_type': 'Complete Application',
                'form_type': 'form1',
                'application_id': 'test-123',
                'submission_date': 'January 15, 2024 at 10:30 AM',
                'support_email': '<EMAIL>',
                'portal_url': 'http://localhost:8080',
            }
            
            # Test text template
            try:
                text_content = render_to_string('alumni_applications/confirmation_email.txt', context)
                self.log_result("Text Template", True, f"Rendered {len(text_content)} characters")
            except Exception as e:
                self.log_result("Text Template", False, f"Error: {e}")
            
            # Test HTML template
            try:
                html_content = render_to_string('alumni_applications/confirmation_email.html', context)
                self.log_result("HTML Template", True, f"Rendered {len(html_content)} characters")
            except Exception as e:
                self.log_result("HTML Template", False, f"Error: {e}")
            
            return True
            
        except Exception as e:
            self.log_result("Template Rendering", False, f"Error: {e}")
            return False

    def check_email_notifications(self):
        """Check email notification logging"""
        print("\n📝 Checking Email Notifications...")
        
        try:
            from communication.models import EmailNotification
            
            # Check total notifications
            total_notifications = EmailNotification.objects.count()
            self.log_result("Total Notifications", True, f"Found {total_notifications} notifications")
            
            # Check failed notifications
            failed_notifications = EmailNotification.objects.filter(status='failed').count()
            self.log_result("Failed Notifications", True, f"Found {failed_notifications} failed notifications")
            
            # Check recent notifications
            from django.utils import timezone
            from datetime import timedelta
            
            recent_notifications = EmailNotification.objects.filter(
                sent_time__gte=timezone.now() - timedelta(hours=24)
            ).count()
            self.log_result("Recent Notifications", True, f"Found {recent_notifications} notifications in last 24 hours")
            
            return True
            
        except Exception as e:
            self.log_result("Email Notifications", False, f"Error: {e}")
            return False

    def run_tests(self):
        """Run all email tests"""
        print("🚀 Starting Simple Email Tests...")
        print("=" * 50)
        
        tests = [
            self.test_smtp_settings,
            self.test_django_settings,
            self.test_template_rendering,
            self.test_console_email,
            self.test_email_service,
            self.check_email_notifications
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"  ❌ Test failed with exception: {e}")
        
        # Generate report
        print("\n" + "=" * 50)
        print("📊 EMAIL TEST REPORT")
        print("=" * 50)
        
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        print("\n🔧 TROUBLESHOOTING GUIDE:")
        print("1. Check SMTP Configuration:")
        print("   - Go to Django Admin: /admin/settings_manager/smtpsettings/")
        print("   - Configure your email provider settings")
        print("   - Test with a real email address")
        
        print("\n2. Check Application Submission:")
        print("   - Submit a test application at /alumni-application?form=form1")
        print("   - Check Django console output for email content")
        print("   - Check server logs for any errors")
        
        print("\n3. Check Email Templates:")
        print("   - Verify templates exist in alumni_applications/templates/")
        print("   - Check template syntax and context variables")
        
        print("\n4. Check Email Notifications:")
        print("   - Go to Django Admin: /admin/communication/emailnotification/")
        print("   - Look for failed email entries")
        print("   - Check error messages in failed notifications")
        
        print("\n📧 EMAIL FLOW:")
        print("  Application Submitted → perform_create() → send_application_confirmation() → Email Sent")
        
        return passed == total

if __name__ == "__main__":
    test = SimpleEmailTest()
    success = test.run_tests()
    sys.exit(0 if success else 1)
