# Generated by Django 5.2.3 on 2025-06-15 19:56

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alumni_applications', '0002_alter_studentapplicationform_father_name_and_more'),
        ('college', '0001_initial'),
        ('department', '0001_initial'),
        ('service_type', '0003_alter_servicetype_document_types'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RenameModel(
            old_name='StudentApplicationForm',
            new_name='AlumniApplication',
        ),
        migrations.RenameModel(
            old_name='StudentApplicationFormSimplified',
            new_name='AlumniApplicationMini',
        ),
        migrations.AlterModelOptions(
            name='alumniapplication',
            options={'ordering': ['-created_at'], 'verbose_name': 'Alumni Application (Complete)', 'verbose_name_plural': 'Alumni Applications (Complete)'},
        ),
        migrations.AlterModelOptions(
            name='alumniapplicationmini',
            options={'ordering': ['-created_at'], 'verbose_name': 'Alumni Application (Mini)', 'verbose_name_plural': 'Alumni Applications (Mini)'},
        ),
        migrations.RemoveConstraint(
            model_name='alumniapplication',
            name='unique_form1_email',
        ),
        migrations.RemoveConstraint(
            model_name='alumniapplicationmini',
            name='unique_form2_email',
        ),
        migrations.RenameIndex(
            model_name='alumniapplication',
            new_name='alumni_appl_transac_6f516a_idx',
            old_name='alumni_appl_transac_c3c982_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplication',
            new_name='alumni_appl_email_6e5c9b_idx',
            old_name='alumni_appl_email_254c90_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplication',
            new_name='alumni_appl_service_b5cae6_idx',
            old_name='alumni_appl_service_bb658c_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplication',
            new_name='alumni_appl_applica_dadb78_idx',
            old_name='alumni_appl_applica_3f15be_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplication',
            new_name='alumni_appl_payment_0d68d9_idx',
            old_name='alumni_appl_payment_c5df02_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplication',
            new_name='alumni_appl_created_4cff19_idx',
            old_name='alumni_appl_created_b551b6_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplicationmini',
            new_name='alumni_appl_transac_98c25a_idx',
            old_name='alumni_appl_transac_afe271_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplicationmini',
            new_name='alumni_appl_email_f17345_idx',
            old_name='alumni_appl_email_20cdab_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplicationmini',
            new_name='alumni_appl_service_11829d_idx',
            old_name='alumni_appl_service_d90e5b_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplicationmini',
            new_name='alumni_appl_applica_9ee998_idx',
            old_name='alumni_appl_applica_e4b943_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplicationmini',
            new_name='alumni_appl_payment_9fa6c3_idx',
            old_name='alumni_appl_payment_8ecc61_idx',
        ),
        migrations.RenameIndex(
            model_name='alumniapplicationmini',
            new_name='alumni_appl_created_51320f_idx',
            old_name='alumni_appl_created_8cac67_idx',
        ),
        migrations.AddConstraint(
            model_name='alumniapplication',
            constraint=models.UniqueConstraint(fields=('email',), name='unique_alumni_application_email'),
        ),
        migrations.AddConstraint(
            model_name='alumniapplicationmini',
            constraint=models.UniqueConstraint(fields=('email',), name='unique_alumni_application_mini_email'),
        ),
    ]
