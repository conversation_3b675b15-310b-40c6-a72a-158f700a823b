import axios from 'axios';

const API_URL = 'http://localhost:8000/api';

export interface Service {
  id: number;
  service_name: string;
  description: string;
  service_fee: number;
  icon_name: string;
  is_active?: boolean;
  order?: number;
  created_at?: string;
  updated_at?: string;
}

const serviceAPI = {
  // Public endpoint - no authentication required
  getPublicServices: async (): Promise<Service[]> => {
    try {
      const response = await axios.get(`${API_URL}/services/public/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching public services:', error);
      throw error;
    }
  },

  // Admin endpoints - authentication required
  getAllServices: async (): Promise<Service[]> => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const response = await axios.get(`${API_URL}/services/`, {
        headers
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching all services:', error);
      throw error;
    }
  },

  getService: async (id: number): Promise<Service> => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const response = await axios.get(`${API_URL}/services/${id}/`, {
        headers
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching service with id ${id}:`, error);
      throw error;
    }
  },

  createService: async (service: Omit<Service, 'id' | 'created_at' | 'updated_at'>): Promise<Service> => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const response = await axios.post(`${API_URL}/services/`, service, {
        headers
      });
      return response.data;
    } catch (error) {
      console.error('Error creating service:', error);
      throw error;
    }
  },

  updateService: async (id: number, service: Partial<Service>): Promise<Service> => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      const response = await axios.patch(`${API_URL}/services/${id}/`, service, {
        headers
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating service with id ${id}:`, error);
      throw error;
    }
  },

  deleteService: async (id: number): Promise<void> => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      await axios.delete(`${API_URL}/services/${id}/`, {
        headers
      });
    } catch (error) {
      console.error(`Error deleting service with id ${id}:`, error);
      throw error;
    }
  }
};

export default serviceAPI;
