from django.db import models
from django.core.exceptions import ValidationError
import uuid

class CertificateType(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Certificate Type'
        verbose_name_plural = 'Certificate Types'

    def clean(self):
        """Validate the certificate type data."""
        super().clean()

        # Validate name
        if not self.name or not self.name.strip():
            raise ValidationError({'name': 'Certificate type name is required.'})

        # Clean and validate name format
        self.name = self.name.strip()
        if len(self.name) < 2:
            raise ValidationError({'name': 'Certificate type name must be at least 2 characters long.'})

        # Check for duplicate names (case-insensitive)
        existing = CertificateType.objects.filter(
            name__iexact=self.name
        ).exclude(pk=self.pk if self.pk else None)

        if existing.exists():
            raise ValidationError({'name': 'A certificate type with this name already exists.'})

    def save(self, *args, **kwargs):
        """Run full validation before saving."""
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name