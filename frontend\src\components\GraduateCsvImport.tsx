import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { FileSpreadsheet, Upload, Loader2, Check, AlertCircle, HelpCircle, FileText, Database, Plus, X } from 'lucide-react';
import { toast } from 'sonner';
import { graduateVerificationAPI } from '@/services/api';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface GraduateCsvImportProps {
  onSuccess?: () => void;
}

const GraduateCsvImport = ({ onSuccess }: GraduateCsvImportProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [importResults, setImportResults] = useState<{ created: number; updated: number; errors: string[] } | null>(null);

  // Parse error message to make it more user-friendly
  const parseErrorMessage = (error: string): { rowNumber: string; errorType: string; detail: string; missingEntity?: string; entityId?: string } => {
    // Default values
    let result = {
      rowNumber: 'Unknown',
      errorType: 'Unknown error',
      detail: error,
      missingEntity: undefined,
      entityId: undefined
    };

    // Extract row number
    const rowMatch = error.match(/Row (\d+):/i);
    if (rowMatch) {
      result.rowNumber = rowMatch[1];
    }

    // Check for foreign key constraint violations
    if (error.includes('violates foreign key constraint')) {
      result.errorType = 'Missing Reference';

      // Look for specific constraint patterns
      const fkPattern = /Key \(([^)]+)\)=\(([^)]+)\) is not present in table "([^"]+)"/;
      const fkMatch = error.match(fkPattern);

      if (fkMatch) {
        const fieldName = fkMatch[1];
        const fieldValue = fkMatch[2];
        const tableName = fkMatch[3];

        // Map field names to user-friendly entity names
        if (fieldName.includes('admission_classification_id')) {
          result.missingEntity = 'Admission Classification';
          result.entityId = fieldValue;
          result.detail = `Admission Classification with ID ${fieldValue} does not exist in the database.`;
        } else if (fieldName.includes('college_id')) {
          result.missingEntity = 'College';
          result.entityId = fieldValue;
          result.detail = `College with ID ${fieldValue} does not exist in the database.`;
        } else if (fieldName.includes('department_id')) {
          result.missingEntity = 'Department';
          result.entityId = fieldValue;
          result.detail = `Department with ID ${fieldValue} does not exist in the database.`;
        } else if (fieldName.includes('field_of_study_id')) {
          result.missingEntity = 'Field of Study';
          result.entityId = fieldValue;
          result.detail = `Field of Study with ID ${fieldValue} does not exist in the database.`;
        } else if (fieldName.includes('program_id')) {
          result.missingEntity = 'Program';
          result.entityId = fieldValue;
          result.detail = `Program with ID ${fieldValue} does not exist in the database.`;
        } else {
          // Generic handling for other foreign key constraints
          const friendlyFieldName = fieldName
            .replace(/_id$/, '')
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

          result.missingEntity = friendlyFieldName;
          result.entityId = fieldValue;
          result.detail = `${friendlyFieldName} with ID ${fieldValue} does not exist in the database (table: ${tableName}).`;
        }
      } else {
        // Fallback for other foreign key errors
        result.detail = 'A required reference is missing. Please check that all IDs in your CSV file exist in the database.';
      }
    } else if (error.includes('duplicate key value violates unique constraint')) {
      result.errorType = 'Duplicate Record';

      if (error.includes('student_id')) {
        const idMatch = error.match(/Key \(student_id\)=\((.+?)\)/);
        if (idMatch) {
          result.detail = `Student ID ${idMatch[1]} already exists in the database.`;
        }
      } else {
        // Generic handling for other unique constraints
        const keyPattern = /Key \(([^)]+)\)=\(([^)]+)\)/;
        const keyMatch = error.match(keyPattern);

        if (keyMatch) {
          const fieldName = keyMatch[1];
          const fieldValue = keyMatch[2];

          const friendlyFieldName = fieldName
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

          result.detail = `${friendlyFieldName} with value '${fieldValue}' already exists in the database.`;
        }
      }
    }

    // Add debugging info
    console.log('Parsed error:', { original: error, parsed: result });

    return result;
  };

  // Check if admission classifications exist when component mounts
  useEffect(() => {
    const checkAdmissionClassifications = async () => {
      try {
        const response = await graduateVerificationAPI.getAdmissionClassifications();
        if (!response?.data || response.data.length === 0) {
          toast.warning(
            'No Admission Classifications found. You need to create at least one before importing graduates.',
            { duration: 6000 }
          );
        }
      } catch (error) {
        console.error('Error checking admission classifications:', error);
      }
    };

    checkAdmissionClassifications();
  }, []);

  // Automatically scroll to error details when errors are present
  useEffect(() => {
    if (importResults?.errors && importResults.errors.length > 0) {
      // Add a small delay to ensure the DOM is updated
      const timer = setTimeout(() => {
        document.getElementById('error-details')?.scrollIntoView({ behavior: 'smooth' });
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [importResults]);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;

    if (selectedFile) {
      // Check if file is CSV
      if (!selectedFile.name.endsWith('.csv')) {
        toast.error('Please select a CSV file');
        return;
      }

      setFile(selectedFile);
      setError(null);
      setUploadSuccess(false);
      setImportResults(null);
    }
  };

  // Handle removing selected file
  const handleRemoveFile = () => {
    setFile(null);
    setError(null);
    setUploadSuccess(false);
    setImportResults(null);
    // Reset the file input
    const fileInput = document.getElementById('csv-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!file) {
      setError('Please select a CSV file first');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const response = await graduateVerificationAPI.importCSV(file);
      const success = response.status === 200;

      if (success) {
        // Store and show detailed results
        const { created, updated, errors } = response.data;
        setImportResults({ created, updated, errors });

        // Show appropriate toast message based on results
        if (created === 0 && updated === 0) {
          if (errors && errors.length > 0) {
            toast.warning(`Import completed with no changes. ${errors.length} error(s) encountered.`);
          } else {
            toast.info('Import completed with no changes. The data might already exist or no valid records were found.');
          }
        } else {
          toast.success(`Import successful: ${created} records created, ${updated} records updated`);
        }

        // If there were any errors, show them in the UI
        if (errors && errors.length > 0) {
          // Set a more descriptive error message
          if (created === 0 && updated === 0) {
            setError(`Import failed with ${errors.length} error(s). No records were created or updated. Please check the error details below.`);
          } else {
            setError(`Import completed with ${errors.length} error(s). Some records may have been imported successfully. See details below.`);
          }
          // Log errors for debugging
          console.error('CSV import errors:', errors);
        }

        setUploadSuccess(true);
        setFile(null);
        // Reset file input
        const fileInput = document.getElementById('csv-upload') as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      console.error('Error uploading CSV:', err);
      setError('Failed to upload CSV file. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Generate sample CSV with names
  const downloadSampleCSVWithNames = async () => {
    try {
      // Fetch available entity IDs from the API
      let collegeId = null;
      let departmentId = null;
      let fieldOfStudyId = null;
      let programId = null;
      let admissionClassificationId = null;

      // Get colleges
      const collegesResponse = await graduateVerificationAPI.getColleges();
      if (collegesResponse?.data && collegesResponse.data.length > 0) {
        collegeId = collegesResponse.data[0].id;
      } else {
        toast.error('No colleges found in the database. Please add colleges first.');
        return;
      }

      // Get departments
      const departmentsResponse = await graduateVerificationAPI.getDepartments();
      if (departmentsResponse?.data && departmentsResponse.data.length > 0) {
        departmentId = departmentsResponse.data[0].id;
      } else {
        toast.error('No departments found in the database. Please add departments first.');
        return;
      }

      // Get fields of study
      const fieldsResponse = await graduateVerificationAPI.getFieldsOfStudy();
      if (fieldsResponse?.data && fieldsResponse.data.length > 0) {
        fieldOfStudyId = fieldsResponse.data[0].id;
      } else {
        toast.error('No fields of study found in the database. Please add fields of study first.');
        return;
      }

      // Get programs
      const programsResponse = await graduateVerificationAPI.getPrograms();
      if (programsResponse?.data && programsResponse.data.length > 0) {
        programId = programsResponse.data[0].id;
      } else {
        toast.error('No programs found in the database. Please add programs first.');
        return;
      }

      // Get admission classifications
      const admissionsResponse = await graduateVerificationAPI.getAdmissionClassifications();
      if (admissionsResponse?.data && admissionsResponse.data.length > 0) {
        admissionClassificationId = admissionsResponse.data[0].id;
      } else {
        toast.error('No admission classifications found in the database. Please add admission classifications first.');

        // Ask if they want to create one now
        if (confirm('No Admission Classifications found. Would you like to create one now?')) {
          window.open('/admin/admission-classifications', '_blank');
        }
        return;
      }

      // Ensure all required IDs are available
      if (!collegeId || !departmentId || !fieldOfStudyId || !programId || !admissionClassificationId) {
        toast.error('Missing required data in the database. Please ensure all required entities exist.');
        return;
      }

      // Get actual names for the sample
      const collegeName = collegesResponse.data[0].name;
      const departmentName = departmentsResponse.data[0].name;
      const fieldOfStudyName = fieldsResponse.data[0].name;
      const programName = programsResponse.data[0].name;
      const admissionClassificationName = admissionsResponse.data[0].name;

      // Create sample with both ID and name options
      const headers = 'student_id,first_name,middle_name,last_name,year_of_entry,year_of_graduation,gpa,gender,college_name,department_name,field_of_study_name,program_name,admission_classification_name';
      const sampleData = [
        `12345,John,M,Doe,2018,2022,3.75,Male,"${collegeName}","${departmentName}","${fieldOfStudyName}","${programName}","${admissionClassificationName}"`,
        `67890,Jane,,Smith,2017,2021,3.90,Female,"${collegeName}","${departmentName}","${fieldOfStudyName}","${programName}","${admissionClassificationName}"`
      ].join('\n');

      const csvContent = `${headers}\n${sampleData}`;

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'graduate_sample_with_names.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Sample CSV with names downloaded successfully');
    } catch (error) {
      console.error('Error generating sample CSV with names:', error);
      toast.error('Failed to generate sample CSV with names. Please ensure all required data exists in the database.');
    }
  };

  // Generate sample CSV with IDs
  const downloadSampleCSVWithIDs = async () => {
    try {
      // Fetch available entity IDs from the API
      let collegeId = null;
      let departmentId = null;
      let fieldOfStudyId = null;
      let programId = null;
      let admissionClassificationId = null;

      // Get colleges
      const collegesResponse = await graduateVerificationAPI.getColleges();
      if (collegesResponse?.data && collegesResponse.data.length > 0) {
        collegeId = collegesResponse.data[0].id;
      } else {
        toast.error('No colleges found in the database. Please add colleges first.');
        return;
      }

      // Get departments
      const departmentsResponse = await graduateVerificationAPI.getDepartments();
      if (departmentsResponse?.data && departmentsResponse.data.length > 0) {
        departmentId = departmentsResponse.data[0].id;
      } else {
        toast.error('No departments found in the database. Please add departments first.');
        return;
      }

      // Get fields of study
      const fieldsResponse = await graduateVerificationAPI.getFieldsOfStudy();
      if (fieldsResponse?.data && fieldsResponse.data.length > 0) {
        fieldOfStudyId = fieldsResponse.data[0].id;
      } else {
        toast.error('No fields of study found in the database. Please add fields of study first.');
        return;
      }

      // Get programs
      const programsResponse = await graduateVerificationAPI.getPrograms();
      if (programsResponse?.data && programsResponse.data.length > 0) {
        programId = programsResponse.data[0].id;
      } else {
        toast.error('No programs found in the database. Please add programs first.');
        return;
      }

      // Get admission classifications
      const admissionsResponse = await graduateVerificationAPI.getAdmissionClassifications();
      if (admissionsResponse?.data && admissionsResponse.data.length > 0) {
        admissionClassificationId = admissionsResponse.data[0].id;
      } else {
        toast.error('No admission classifications found in the database. Please add admission classifications first.');
        return;
      }

      // Ensure all required IDs are available
      if (!collegeId || !departmentId || !fieldOfStudyId || !programId || !admissionClassificationId) {
        toast.error('Missing required data in the database. Please ensure all required entities exist.');
        return;
      }

      // Create sample with IDs
      const headers = 'student_id,first_name,middle_name,last_name,year_of_entry,year_of_graduation,gpa,gender,college_id,department_id,field_of_study_id,program_id,admission_classification_id';
      const sampleData = [
        `12345,John,M,Doe,2018,2022,3.75,Male,${collegeId},${departmentId},${fieldOfStudyId},${programId},${admissionClassificationId}`,
        `67890,Jane,,Smith,2017,2021,3.90,Female,${collegeId},${departmentId},${fieldOfStudyId},${programId},${admissionClassificationId}`
      ].join('\n');

      const csvContent = `${headers}\n${sampleData}`;

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'graduate_sample_with_ids.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Sample CSV with IDs downloaded successfully');
    } catch (error) {
      console.error('Error generating sample CSV with IDs:', error);
      toast.error('Failed to generate sample CSV with IDs. Please ensure all required data exists in the database.');
    }
  };



  return (
    <div className="space-y-6">


          {/* Setup Requirements Alert */}
         
          {/* Success Alert */}
          {uploadSuccess && (
            <Alert
              className={`${(importResults?.created === 0 && importResults?.updated === 0)
                ? (importResults?.errors && importResults.errors.length > 0
                  ? 'bg-amber-50 border-amber-200'
                  : 'bg-blue-50 border-blue-200')
                : 'bg-green-50 border-green-200'}`}
            >
              {(importResults?.created === 0 && importResults?.updated === 0)
                ? (importResults?.errors && importResults.errors.length > 0
                  ? <AlertCircle className="h-4 w-4 text-amber-600" />
                  : <HelpCircle className="h-4 w-4 text-blue-600" />)
                : <Check className="h-4 w-4 text-green-600" />}
              <AlertTitle
                className={`${(importResults?.created === 0 && importResults?.updated === 0)
                  ? (importResults?.errors && importResults.errors.length > 0
                    ? 'text-amber-800'
                    : 'text-blue-800')
                  : 'text-green-800'}`}
              >
                {(importResults?.created === 0 && importResults?.updated === 0)
                  ? 'Import Completed With No Changes'
                  : 'Import Successful'}
              </AlertTitle>
              <AlertDescription
                className={`${(importResults?.created === 0 && importResults?.updated === 0)
                  ? (importResults?.errors && importResults.errors.length > 0
                    ? 'text-amber-700'
                    : 'text-blue-700')
                  : 'text-green-700'}`}
              >
                {(importResults?.created === 0 && importResults?.updated === 0) && (
                  <p className="mb-2">
                    {importResults?.errors && importResults.errors.length > 0
                      ? `Import failed with ${importResults.errors.length} error(s). Please check the error details below and fix your CSV file.`
                      : 'The data might already exist or no valid records were found in the CSV file.'}
                  </p>
                )}
                <div className="mt-2 space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={`${importResults?.created > 0 ? 'bg-green-600' : 'bg-gray-400'}`}>
                      {importResults?.created || 0}
                    </Badge>
                    <span>New records created</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={`${importResults?.updated > 0 ? 'bg-blue-600' : 'bg-gray-400'}`}>
                      {importResults?.updated || 0}
                    </Badge>
                    <span>Existing records updated</span>
                  </div>
                  {importResults?.errors && importResults.errors.length > 0 && (
                    <div className="flex items-center gap-2">
                      <Badge variant="destructive">{importResults.errors.length}</Badge>
                      <span>Errors encountered</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-2 text-xs h-7 px-2 border-red-300 text-red-600 hover:bg-red-50"
                        onClick={() => {
                          document.getElementById('error-details')?.scrollIntoView({ behavior: 'smooth' });
                        }}
                      >
                        <AlertCircle className="h-3 w-3 mr-1" />
                        View Errors
                      </Button>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Import Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Import Card */}
          <Card className="border-2 border-dashed border-gray-200 hover:border-gray-300 transition-colors">
            <CardContent className="p-6">
              <div className="flex flex-col items-center justify-center text-center space-y-4">
                <div className="p-3 rounded-full bg-blue-50 text-blue-600">
                  <FileSpreadsheet className="h-8 w-8" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Upload CSV File</h3>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto">
                    Select a CSV file containing graduate data to import. The file should include student information and related entity IDs.
                  </p>
                </div>

                <div className="w-full max-w-sm">
                  <Input
                    type="file"
                    accept=".csv"
                    id="csv-upload"
                    className="hidden"
                    onChange={handleFileChange}
                  />

                  {!file ? (
                    <Label
                      htmlFor="csv-upload"
                      className="flex h-12 w-full items-center justify-center rounded-md border-2 border-gray-200 px-4 py-3 text-sm font-medium cursor-pointer hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center">
                        <Upload className="h-4 w-4 mr-2" />
                        <span>Choose CSV File</span>
                      </div>
                    </Label>
                  ) : (
                    <div className="space-y-3">
                      {/* Selected file display */}
                      <div className="flex items-center justify-between p-3 bg-blue-50 border-2 border-blue-300 rounded-md">
                        <div className="flex items-center flex-1 min-w-0">
                          <FileText className="h-4 w-4 mr-2 text-blue-600 flex-shrink-0" />
                          <span className="text-blue-600 font-medium truncate">{file.name}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleRemoveFile}
                          className="ml-2 h-8 w-8 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50 transition-colors flex-shrink-0"
                          title="Remove file"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Change file button */}
                      <Label
                        htmlFor="csv-upload"
                        className="flex h-10 w-full items-center justify-center rounded-md border border-gray-300 px-4 py-2 text-sm font-medium cursor-pointer hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center">
                          <Upload className="h-4 w-4 mr-2" />
                          <span>Choose Different File</span>
                        </div>
                      </Label>
                    </div>
                  )}
                </div>

                {file && (
                  <div className="w-full max-w-sm space-y-2">
                    <Button
                      className="w-full bg-[#1a73c0] hover:bg-[#145da1] h-10"
                      onClick={handleUpload}
                      disabled={uploading}
                    >
                      {uploading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          <span>Importing...</span>
                          <Progress value={65} className="h-1 w-12 ml-2" />
                        </>
                      ) : (
                        <>
                          <Database className="mr-2 h-4 w-4" />
                          <span>Import Data</span>
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Error Details */}
          {importResults?.errors && importResults.errors.length > 0 && (
            <div id="error-details">
              <Card className="border-2 border-red-300 shadow-md">
                <CardContent className="p-4">
                  <h4 className="text-base font-medium mb-3 flex items-center text-red-600">
                    <AlertCircle className="h-5 w-5 mr-2" />
                    Import Errors ({importResults.errors.length})
                  </h4>
                  <p className="text-sm mb-3 text-red-700">
                    The following errors were encountered during import. Please fix these issues in your CSV file and try again.
                  </p>

                  {/* Quick Fix Alert for Admission Classification */}
                  {importResults.errors && importResults.errors.some(error =>
                    error.includes('admission_classification_id') &&
                    error.includes('violates foreign key constraint')
                  ) && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <HelpCircle className="h-5 w-5 text-blue-600" aria-hidden="true" />
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-blue-800">Missing Admission Classification</h3>
                          <div className="mt-2 text-sm text-blue-700">
                            <p>
                              It looks like you need to create an Admission Classification before importing. This is a common first-time setup requirement.
                            </p>
                            <div className="mt-3">
                              <Button
                                variant="outline"
                                className="bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200"
                                onClick={() => window.open('/admin/admission-classifications', '_blank')}
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Create Admission Classification
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Error Summary */}
                  {importResults.errors && importResults.errors.length > 0 && (() => {
                    // Count error types
                    const errorSummary: Record<string, { count: number, entities: Record<string, number> }> = {};

                    importResults.errors.forEach(error => {
                      const parsedError = parseErrorMessage(error);

                      if (!errorSummary[parsedError.errorType]) {
                        errorSummary[parsedError.errorType] = { count: 0, entities: {} };
                      }

                      errorSummary[parsedError.errorType].count++;

                      if (parsedError.missingEntity) {
                        if (!errorSummary[parsedError.errorType].entities[parsedError.missingEntity]) {
                          errorSummary[parsedError.errorType].entities[parsedError.missingEntity] = 0;
                        }
                        errorSummary[parsedError.errorType].entities[parsedError.missingEntity]++;
                      }
                    });

                    return (
                      <div className="bg-amber-50 p-3 rounded-md border border-amber-200 mb-3">
                        <h5 className="text-sm font-medium text-amber-800 mb-2">Error Summary</h5>
                        <ul className="text-xs text-amber-700 space-y-1">
                          {Object.entries(errorSummary).map(([errorType, data], index) => (
                            <li key={index}>
                              <strong>{errorType}:</strong> {data.count} occurrence{data.count !== 1 ? 's' : ''}
                              {data.entities && Object.keys(data.entities || {}).length > 0 && (
                                <ul className="ml-4 mt-1 space-y-1">
                                  {Object.entries(data.entities).map(([entity, count], idx) => (
                                    <li key={idx}>
                                      <span className="text-amber-600">{entity}:</span> {count} reference{count !== 1 ? 's' : ''} missing
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </li>
                          ))}
                        </ul>
                        <div className="mt-3 text-xs text-amber-800">
                          <div className="flex items-center justify-between">
                            <div>
                              <strong>Tip:</strong> Make sure all referenced IDs exist in the database before importing.
                            </div>
                            {Object.entries(errorSummary).some(([type, data]) =>
                              type === 'Missing Reference' &&
                              data.entities &&
                              Object.keys(data.entities).includes('Admission Classification')
                            ) && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-7 text-xs border-amber-300 text-amber-700 hover:bg-amber-50"
                                onClick={() => {
                                  window.open('/admin/admission-classifications', '_blank');
                                }}
                              >
                                <Plus className="h-3 w-3 mr-1" />
                                Create Admission Classification
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                  <div className="bg-red-50 p-4 rounded-md border border-red-200 text-xs h-60 overflow-y-auto">
                    {importResults.errors.map((error, index) => {
                      const parsedError = parseErrorMessage(error);
                      return (
                        <div key={index} className="mb-4 pb-3 border-b border-red-100 last:border-0 text-red-700">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant="destructive" className="font-normal">
                              Row {parsedError.rowNumber}
                            </Badge>
                            <Badge variant="outline" className="border-red-200 text-red-700 font-normal">
                              {parsedError.errorType}
                            </Badge>
                            {parsedError.missingEntity && (
                              <Badge variant="outline" className="border-amber-200 text-amber-700 font-normal">
                                {parsedError.missingEntity}
                              </Badge>
                            )}
                          </div>
                          <div className="mt-1 font-normal">
                            {parsedError.detail}
                            {parsedError.missingEntity && (
                              <div className="mt-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-6 text-[10px] border-blue-200 text-blue-700 hover:bg-blue-50"
                                  onClick={() => {
                                    // Navigate to the appropriate admin section based on the missing entity
                                    let path = '';
                                    switch(parsedError.missingEntity) {
                                      case 'Admission Classification':
                                        path = '/admin/admission-classifications';
                                        break;
                                      case 'College':
                                        path = '/admin/verification-colleges';
                                        break;
                                      case 'Department':
                                        path = '/admin/verification-departments';
                                        break;
                                      case 'Field of Study':
                                        path = '/admin/verification-fields-of-study';
                                        break;
                                      case 'Program':
                                        path = '/admin/verification-programs';
                                        break;
                                    }
                                    if (path) {
                                      window.open(path, '_blank');
                                    }
                                  }}
                                >
                                  <Plus className="h-3 w-3 mr-1" />
                                  Create {parsedError.missingEntity}
                                </Button>
                              </div>
                            )}
                          </div>
                          <div className="mt-2 text-gray-500 text-[10px] font-mono bg-gray-50 p-1 rounded">
                            <details>
                              <summary className="cursor-pointer hover:text-gray-700">Technical details</summary>
                              <div className="mt-1 whitespace-pre-wrap break-all">{error}</div>
                            </details>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <div className="mt-3 flex flex-col sm:flex-row gap-2 justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-green-300 text-green-600 hover:bg-green-50"
                      onClick={downloadSampleCSVWithNames}
                    >
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Sample with Names
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-blue-300 text-blue-600 hover:bg-blue-50"
                      onClick={downloadSampleCSVWithIDs}
                    >
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Sample with IDs
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}



          {/* Sample Download Options */}
          <div className="space-y-3 pt-4">
            <div className="text-center">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Download Sample CSV Templates</h4>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                variant="outline"
                onClick={downloadSampleCSVWithNames}
                className="text-green-600 border-green-300 hover:bg-green-50"
                size="sm"
              >
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Sample with Names
              </Button>
              <Button
                variant="outline"
                onClick={downloadSampleCSVWithIDs}
                className="text-blue-600 border-blue-300 hover:bg-blue-50"
                size="sm"
              >
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Sample with IDs
              </Button>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500 max-w-md mx-auto">
                Choose "Names" for easier reading or "IDs" for traditional format
              </p>
            </div>
          </div>
    </div>
  );
};

export default GraduateCsvImport;
