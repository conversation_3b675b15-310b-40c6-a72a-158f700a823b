import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Shield, 
  Search, 
  Filter, 
  RefreshCw,
  Save,
  Download,
  Upload,
  Eye,
  EyeOff,
  Grid,
  List,
  BarChart3,
  Users,
  Lock,
  Unlock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import api from '@/services/api';

interface Permission {
  id: number;
  name: string;
  codename: string;
  content_type: string;
  app_label: string;
  model: string;
  description?: string;
}

interface Role {
  id: number;
  name: string;
  description: string;
  level: number;
  permissions: Permission[];
  user_count: number;
  is_system_role: boolean;
  is_active: boolean;
}

interface PermissionMatrix {
  [roleId: number]: {
    [permissionId: number]: boolean;
  };
}

interface PermissionUsage {
  permission: Permission;
  role_count: number;
  user_count: number;
  usage_percentage: number;
  is_critical: boolean;
}

const PermissionMatrix: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [matrix, setMatrix] = useState<PermissionMatrix>({});
  const [permissionUsage, setPermissionUsage] = useState<PermissionUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'matrix' | 'usage' | 'conflicts'>('matrix');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterApp, setFilterApp] = useState('');
  const [filterRole, setFilterRole] = useState('');
  const [showSystemRoles, setShowSystemRoles] = useState(true);
  const [showInactiveRoles, setShowInactiveRoles] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [rolesResponse, permissionsResponse, matrixResponse, usageResponse] = await Promise.all([
        api.get('/auth/roles/'),
        api.get('/auth/permissions/'),
        api.get('/auth/permission-matrix/'),
        api.get('/auth/permission-usage/')
      ]);
      
      setRoles(rolesResponse.data.results || rolesResponse.data);
      setPermissions(permissionsResponse.data.results || permissionsResponse.data);
      setMatrix(matrixResponse.data);
      setPermissionUsage(usageResponse.data);
    } catch (error) {
      toast.error('Failed to load permission data');
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionToggle = (roleId: number, permissionId: number) => {
    setMatrix(prev => ({
      ...prev,
      [roleId]: {
        ...prev[roleId],
        [permissionId]: !prev[roleId]?.[permissionId]
      }
    }));
    setHasChanges(true);
  };

  const handleSaveChanges = async () => {
    try {
      await api.post('/auth/permission-matrix/', { matrix });
      toast.success('Permission matrix updated successfully');
      setHasChanges(false);
      loadData(); // Reload to get updated usage statistics
    } catch (error) {
      toast.error('Failed to update permission matrix');
      console.error('Error saving matrix:', error);
    }
  };

  const handleBulkAssign = async (roleIds: number[], permissionIds: number[], assign: boolean) => {
    try {
      await api.post('/auth/bulk-permission-assignment/', {
        role_ids: roleIds,
        permission_ids: permissionIds,
        assign
      });
      toast.success(`Permissions ${assign ? 'assigned' : 'revoked'} successfully`);
      loadData();
    } catch (error) {
      toast.error(`Failed to ${assign ? 'assign' : 'revoke'} permissions`);
      console.error('Error in bulk assignment:', error);
    }
  };

  const handleExportMatrix = async () => {
    try {
      const response = await api.get('/auth/permission-matrix/export/', { responseType: 'blob' });
      const blob = new Blob([response.data], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'permission_matrix.json';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Permission matrix exported successfully');
    } catch (error) {
      toast.error('Failed to export permission matrix');
      console.error('Error exporting matrix:', error);
    }
  };

  const getApps = () => {
    const apps = permissions
      .map(p => p.app_label)
      .filter((app, index, arr) => arr.indexOf(app) === index)
      .sort();
    return apps;
  };

  const getFilteredRoles = () => {
    return roles.filter(role => {
      if (!showSystemRoles && role.is_system_role) return false;
      if (!showInactiveRoles && !role.is_active) return false;
      if (filterRole && !role.name.toLowerCase().includes(filterRole.toLowerCase())) return false;
      return true;
    });
  };

  const getFilteredPermissions = () => {
    return permissions.filter(permission => {
      if (filterApp && permission.app_label !== filterApp) return false;
      if (searchTerm && !permission.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !permission.codename.toLowerCase().includes(searchTerm.toLowerCase())) return false;
      return true;
    });
  };

  const getPermissionConflicts = () => {
    const conflicts: Array<{
      permission: Permission;
      conflicting_roles: Role[];
      severity: 'high' | 'medium' | 'low';
      description: string;
    }> = [];

    // Check for permissions that might conflict (e.g., delete permissions with view-only roles)
    permissions.forEach(permission => {
      if (permission.codename.includes('delete')) {
        const rolesWithPermission = roles.filter(role => 
          matrix[role.id]?.[permission.id] && role.name.toLowerCase().includes('view')
        );
        
        if (rolesWithPermission.length > 0) {
          conflicts.push({
            permission,
            conflicting_roles: rolesWithPermission,
            severity: 'high',
            description: 'Delete permission assigned to view-only roles'
          });
        }
      }
    });

    return conflicts;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-red-100 text-red-800';
    if (percentage >= 60) return 'bg-yellow-100 text-yellow-800';
    if (percentage >= 40) return 'bg-blue-100 text-blue-800';
    return 'bg-gray-100 text-gray-800';
  };

  const filteredRoles = getFilteredRoles();
  const filteredPermissions = getFilteredPermissions();
  const conflicts = getPermissionConflicts();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Permission Matrix</h1>
          <p className="text-muted-foreground">
            Visualize and manage role-permission relationships at scale
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportMatrix}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={loadData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {hasChanges && (
            <Button onClick={handleSaveChanges}>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          )}
        </div>
      </div>

      {/* View Mode Selector */}
      <Card>
        <CardHeader>
          <CardTitle>View Mode</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Button
              variant={viewMode === 'matrix' ? 'default' : 'outline'}
              onClick={() => setViewMode('matrix')}
            >
              <Grid className="h-4 w-4 mr-2" />
              Permission Matrix
            </Button>
            <Button
              variant={viewMode === 'usage' ? 'default' : 'outline'}
              onClick={() => setViewMode('usage')}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Usage Analysis
            </Button>
            <Button
              variant={viewMode === 'conflicts' ? 'default' : 'outline'}
              onClick={() => setViewMode('conflicts')}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Conflicts ({conflicts.length})
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="search">Search Permissions</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search permissions..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="app-filter">Application</Label>
              <Select value={filterApp} onValueChange={setFilterApp}>
                <SelectTrigger>
                  <SelectValue placeholder="All apps" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All apps</SelectItem>
                  {getApps().map(app => (
                    <SelectItem key={app} value={app}>
                      {app}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="role-filter">Role Filter</Label>
              <Input
                id="role-filter"
                placeholder="Filter roles..."
                value={filterRole}
                onChange={(e) => setFilterRole(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Role Types</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="system-roles"
                  checked={showSystemRoles}
                  onCheckedChange={setShowSystemRoles}
                />
                <Label htmlFor="system-roles" className="text-sm">System Roles</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="inactive-roles"
                  checked={showInactiveRoles}
                  onCheckedChange={setShowInactiveRoles}
                />
                <Label htmlFor="inactive-roles" className="text-sm">Inactive Roles</Label>
              </div>
            </div>
            
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setFilterApp('');
                  setFilterRole('');
                  setShowSystemRoles(true);
                  setShowInactiveRoles(false);
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{permissions.length}</div>
            <p className="text-xs text-muted-foreground">
              {filteredPermissions.length} filtered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Roles</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(r => r.is_active).length}
            </div>
            <p className="text-xs text-muted-foreground">
              {filteredRoles.length} filtered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permission Assignments</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(matrix).reduce((sum, rolePerms) => 
                sum + Object.values(rolePerms).filter(Boolean).length, 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Total assignments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conflicts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{conflicts.length}</div>
            <p className="text-xs text-muted-foreground">
              Potential issues
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Based on View Mode */}
      {viewMode === 'matrix' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Grid className="h-5 w-5" />
              <span>Permission Matrix</span>
            </CardTitle>
            <CardDescription>
              Interactive matrix showing role-permission assignments
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                <span>Loading permission matrix...</span>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="sticky left-0 bg-background">Permission</TableHead>
                      {filteredRoles.map(role => (
                        <TableHead key={role.id} className="text-center min-w-[120px]">
                          <div className="space-y-1">
                            <div className="font-medium">{role.name}</div>
                            <Badge variant="outline" className="text-xs">
                              {role.user_count} users
                            </Badge>
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPermissions.map(permission => (
                      <TableRow key={permission.id}>
                        <TableCell className="sticky left-0 bg-background">
                          <div className="space-y-1">
                            <div className="font-medium">{permission.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {permission.app_label}.{permission.model}
                            </div>
                            <code className="text-xs bg-muted px-1 py-0.5 rounded">
                              {permission.codename}
                            </code>
                          </div>
                        </TableCell>
                        {filteredRoles.map(role => (
                          <TableCell key={role.id} className="text-center">
                            <Checkbox
                              checked={matrix[role.id]?.[permission.id] || false}
                              onCheckedChange={() => handlePermissionToggle(role.id, permission.id)}
                              disabled={role.is_system_role}
                            />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {viewMode === 'usage' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Permission Usage Analysis</span>
            </CardTitle>
            <CardDescription>
              Analyze permission utilization across roles and users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {permissionUsage.map(usage => (
                <div key={usage.permission.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{usage.permission.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {usage.permission.app_label}.{usage.permission.model}
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <div className="text-sm font-medium">{usage.role_count}</div>
                      <div className="text-xs text-muted-foreground">Roles</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium">{usage.user_count}</div>
                      <div className="text-xs text-muted-foreground">Users</div>
                    </div>
                    <Badge
                      variant="outline"
                      className={getUsageColor(usage.usage_percentage)}
                    >
                      {usage.usage_percentage.toFixed(1)}%
                    </Badge>
                    {usage.is_critical && (
                      <Badge variant="destructive">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Critical
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {viewMode === 'conflicts' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>Permission Conflicts</span>
            </CardTitle>
            <CardDescription>
              Identify and resolve potential permission conflicts
            </CardDescription>
          </CardHeader>
          <CardContent>
            {conflicts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <h3 className="text-lg font-medium mb-2">No Conflicts Found</h3>
                <p className="text-muted-foreground">
                  Your permission assignments look good!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {conflicts.map((conflict, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{conflict.permission.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {conflict.description}
                        </p>
                      </div>
                      <Badge
                        variant={conflict.severity === 'high' ? 'destructive' :
                                conflict.severity === 'medium' ? 'default' : 'secondary'}
                      >
                        {conflict.severity.toUpperCase()}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Conflicting Roles:</div>
                      <div className="flex flex-wrap gap-2">
                        {conflict.conflicting_roles.map(role => (
                          <Badge key={role.id} variant="outline">
                            {role.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PermissionMatrix;
