import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { settingsAPI } from '@/services/api';
import BackToTop from './BackToTop';
import HeaderLogo from './HeaderLogo';
import {
  Home,
  Users,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  Bell,
  Search,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  UserCheck,
  Globe,
  HelpCircle,
  Mail,
  Shield,
  Sun,
  Moon,
  KeyRound,
  Award,
  Cog,
  GraduationCap,
  Package,
  Building2,
  BookOpen,
  Database,
  MessageSquare,
  BarChart3,
  Clock,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useSimpleRBAC as useRBAC } from '@/contexts/SimpleRBACContext';
import { useMenuAccess } from '@/hooks/useMenuAccess';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import ChangePasswordModal from '@/components/ChangePasswordModal';
import { hasMenuAccess } from '@/utils/menuPermissionsNew';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const NewAdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const { isSuperuser } = useRBAC();
  const { staffMenuCategories, adminMenuCategories, debugPermissions } = useMenuAccess();
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth >= 768);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarHovered, setSidebarHovered] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<number | null>(null);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [settings, setSettings] = useState<{
    copyright: string;
    systemName: string;
    organizationName: string;
    primaryColor: string;
    headerLogoUrl: string | null;
  }>({
    copyright: '',
    systemName: '',
    organizationName: '',
    primaryColor: '#1a73c0', // Default primary color
    headerLogoUrl: null
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Get user data
  const userDataString = localStorage.getItem('user');
  const userData = userDataString ? JSON.parse(userDataString) : null;
  const userRole = userData?.is_superuser ? 'Super Admin' : userData?.is_staff ? 'Staff' : 'User';
  const userName = userData?.first_name && userData?.last_name
    ? `${userData.first_name} ${userData.last_name}`
    : userData?.username || 'Admin User';

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Fetch organization settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          // Log the response data to see what we're getting
          console.log('Organization settings response:', response.data);

          // Log the header logo URL for debugging
          const headerLogoUrl = response.data.header_logo_url || null;
          console.log('Header logo URL from API:', headerLogoUrl);

          setSettings({
            copyright: response.data.copyright || '',
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || '',
            primaryColor: response.data.primary_color || '#1a73c0',
            headerLogoUrl: headerLogoUrl
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Handle window resize for responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setSidebarOpen(true);
      } else {
        setSidebarOpen(false);
      }
    };

    // Call handleResize on initial load to set the correct state based on screen size
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = async () => {
    try {
      // Use the AuthContext logout function to properly clear state
      await logout();
      toast.success('Logged out successfully');
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Logout failed. Please try again.');
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleSidebarCollapse = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    // You would typically add/remove a class to the document here
    // or use a theme provider to handle dark mode properly
    document.documentElement.classList.toggle('dark-theme');
  };

  // Function to handle menu item clicks
  const handleMenuItemClick = (_index: number, isSubmenuItem: boolean = false, parentIndex?: number) => {
    // If clicking on a non-submenu item, close any open submenu
    if (!isSubmenuItem) {
      setActiveSubmenu(null);
    }
    // If clicking on a submenu item, keep the parent menu open
    else if (parentIndex !== undefined) {
      setActiveSubmenu(parentIndex);
    }
  };

  // NEW: Build menu items directly from permission system
  const buildPermissionBasedMenu = () => {
    const { user } = useAuth();
    const convertedItems: any[] = [];

    // Error handling for permission system
    try {

    // Define menu structure with permission-based access
    const menuStructure = [
      // Dashboard Menus
      {
        title: 'Graduation Dashboard',
        icon: <GraduationCap className="h-5 w-5" />,
        path: '/graduate-admin?tab=graduation-dashboard',
        permissionKey: 'graduation-dashboard',
        submenu: false
      },
      {
        title: 'Application Dashboard',
        icon: <FileText className="h-5 w-5" />,
        path: '/graduate-admin?tab=application-dashboard',
        permissionKey: 'application-dashboard',
        submenu: false
      },
      {
        title: 'Service Fee Dashboard',
        icon: <Package className="h-5 w-5" />,
        path: '/graduate-admin?tab=service-fee-dashboard',
        permissionKey: 'service-fee-dashboard',
        submenu: false
      },

      // Graduate Verification Dropdown
      {
        title: 'Graduate Verification',
        icon: <GraduationCap className="h-5 w-5" />,
        submenu: true,
        items: [
          {
            title: 'Manage Graduates',
            path: '/graduate-admin?tab=manage-graduates',
            permissionKey: 'manage-graduates'
          },
          {
            title: 'Manage Colleges',
            path: '/graduate-admin?tab=manage-colleges',
            permissionKey: 'manage-colleges'
          },
          {
            title: 'Manage Departments',
            path: '/graduate-admin?tab=manage-departments',
            permissionKey: 'manage-departments'
          },
          {
            title: 'Graduate Fields of Study',
            path: '/graduate-admin?tab=graduate-fields-of-study',
            permissionKey: 'graduate-fields-of-study'
          },
          {
            title: 'Manage Programs',
            path: '/graduate-admin?tab=manage-programs',
            permissionKey: 'manage-programs'
          }
        ]
      },

      // Application Portal Dropdown
      {
        title: 'Application Portal',
        icon: <FileText className="h-5 w-5" />,
        submenu: true,
        items: [
          {
            title: 'Manage Colleges',
            path: '/graduate-admin?tab=manage-colleges-app',
            permissionKey: 'manage-colleges-app'
          },
          {
            title: 'Manage Departments',
            path: '/graduate-admin?tab=manage-departments-app',
            permissionKey: 'manage-departments-app'
          },
          {
            title: 'Manage Programs',
            path: '/graduate-admin?tab=manage-programs-app',
            permissionKey: 'manage-programs-app'
          },
          {
            title: 'Manage Study Programs',
            path: '/graduate-admin?tab=manage-study-programs',
            permissionKey: 'manage-study-programs'
          },
          {
            title: 'Manage Admission Types',
            path: '/graduate-admin?tab=manage-admission-types',
            permissionKey: 'manage-admission-types'
          },
          {
            title: 'Manage Registration Periods',
            path: '/graduate-admin?tab=manage-registration-periods',
            permissionKey: 'manage-registration-periods'
          },
          {
            title: 'Manage Fields of Study',
            path: '/graduate-admin?tab=manage-fields-of-study-app',
            permissionKey: 'manage-fields-of-study-app'
          },
          {
            title: 'Manage Years',
            path: '/graduate-admin?tab=manage-years',
            permissionKey: 'manage-years'
          },
          {
            title: 'Manage Terms',
            path: '/graduate-admin?tab=manage-terms',
            permissionKey: 'manage-terms'
          },
          {
            title: 'Application Information',
            path: '/graduate-admin?tab=application-information',
            permissionKey: 'application-information'
          },
          {
            title: 'Manage Applicants',
            path: '/graduate-admin?tab=manage-applicants',
            permissionKey: 'manage-applicants'
          }
        ]
      },

      // Service Dropdown
      {
        title: 'Service',
        icon: <Package className="h-5 w-5" />,
        submenu: true,
        items: [
          {
            title: 'Service Types',
            path: '/graduate-admin?tab=service-types',
            permissionKey: 'service-types'
          },
          {
            title: 'Document Types',
            path: '/graduate-admin?tab=document-types',
            permissionKey: 'document-types'
          },
          {
            title: 'Alumni Applications Service',
            path: '/graduate-admin?tab=alumni-applications-service',
            permissionKey: 'alumni-applications-service'
          },
          {
            title: 'Downloadable Content',
            path: '/graduate-admin?tab=downloadable-content',
            permissionKey: 'downloadable-content'
          }
        ]
      },

      // Officials Dropdown
      {
        title: 'Officials',
        icon: <Award className="h-5 w-5" />,
        submenu: true,
        items: [
          {
            title: 'Certificate Types',
            path: '/graduate-admin?tab=certificate-types',
            permissionKey: 'certificate-types'
          },
          {
            title: 'Official Certificates',
            path: '/graduate-admin?tab=official-certificates',
            permissionKey: 'official-certificates'
          },
          {
            title: 'Announcements',
            path: '/graduate-admin?tab=announcements',
            permissionKey: 'announcements'
          },
          {
            title: 'Official Management',
            path: '/graduate-admin?tab=official-management',
            permissionKey: 'official-management'
          }
        ]
      },

      // Administrative
      {
        title: 'User Management',
        icon: <Users className="h-5 w-5" />,
        path: '/graduate-admin?tab=user-management',
        permissionKey: 'user-management',
        submenu: false
      },
      {
        title: 'General Settings',
        icon: <Settings className="h-5 w-5" />,
        path: '/graduate-admin?tab=general-settings',
        permissionKey: 'general-settings',
        submenu: false
      }
    ];

    // Filter menu items based on permissions
    menuStructure.forEach((menuItem, index) => {
      if (menuItem.submenu && menuItem.items) {
        // Filter submenu items
        const accessibleItems = menuItem.items.filter(item =>
          hasMenuAccess(user, item.permissionKey)
        );

        // Only add the dropdown if it has accessible items
        if (accessibleItems.length > 0) {
          convertedItems.push({
            title: menuItem.title,
            icon: menuItem.icon,
            submenu: true,
            submenuOpen: activeSubmenu === index,
            toggleSubmenu: () => setActiveSubmenu(activeSubmenu === index ? null : index),
            items: accessibleItems.map(item => ({
              title: item.title,
              path: item.path,
              active: location.pathname === '/graduate-admin' && location.search.includes(item.path.split('?tab=')[1])
            }))
          });
        }
      } else {
        // Single menu item
        if (hasMenuAccess(user, menuItem.permissionKey)) {
          convertedItems.push({
            title: menuItem.title,
            icon: menuItem.icon,
            path: menuItem.path,
            submenu: false,
            active: location.pathname === '/graduate-admin' && location.search.includes(menuItem.path.split('?tab=')[1])
          });
        }
      }
    });

    return convertedItems;

    } catch (error) {
      console.error('Error building permission-based menu:', error);
      // Fallback to basic menu for staff users
      if (user?.is_staff) {
        return [
          {
            title: 'Dashboard',
            icon: <Home className="h-5 w-5" />,
            path: '/graduate-admin',
            submenu: false,
            active: location.pathname === '/graduate-admin'
          }
        ];
      }
      return [];
    }
  };

  const menuItems = buildPermissionBasedMenu();

  // Debug menu access (can be removed in production)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 NewAdminLayout - Staff menu categories:', staffMenuCategories.length);
      console.log('🔐 NewAdminLayout - Admin menu categories:', adminMenuCategories.length);
      console.log('🔐 NewAdminLayout - Total menu items:', menuItems.length);
      console.log('🔐 NewAdminLayout - Staff categories:', staffMenuCategories.map(c => c.name));
      console.log('🔐 NewAdminLayout - Admin categories:', adminMenuCategories.map(c => c.name));

      // Detailed menu item breakdown
      console.log('🔐 DETAILED MENU BREAKDOWN:');
      staffMenuCategories.forEach((category, index) => {
        console.log(`  📁 Staff Category ${index}: ${category.name} (${category.items.length} items)`);
        category.items.forEach((item, itemIndex) => {
          console.log(`    📄 ${itemIndex}: ${item.name} -> ${item.path}`);
        });
      });

      adminMenuCategories.forEach((category, index) => {
        console.log(`  📁 Admin Category ${index}: ${category.name} (${category.items.length} items)`);
        category.items.forEach((item, itemIndex) => {
          console.log(`    📄 ${itemIndex}: ${item.name} -> ${item.path}`);
        });
      });

      console.log('🔐 CONVERTED MENU ITEMS:');
      menuItems.forEach((item, index) => {
        console.log(`  ${index}: ${item.title} (submenu: ${item.submenu}, items: ${item.items?.length || 0})`);
        if (item.items) {
          item.items.forEach((subItem, subIndex) => {
            console.log(`    ${subIndex}: ${subItem.title} -> ${subItem.path}`);
          });
        }
      });

      // Auto-debug permissions for superusers
      if (isSuperuser) {
        console.log('🔐 AUTO-DEBUGGING PERMISSIONS FOR SUPERUSER:');
        debugPermissions();
      }
    }
  }, [staffMenuCategories, adminMenuCategories, menuItems.length, debugPermissions, isSuperuser]);

  // Old menu items replaced with permission-based system above
  /*
  const oldMenuItems = [
        {
          title: 'Service Fee Dashboard',
          path: '/graduate-admin?tab=service-fee-dashboard',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=service-fee-dashboard'
        }
      ]
    },
    {
      title: 'Graduate Verification',
      icon: <UserCheck className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 1,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 1 ? null : 1),
      items: [
        {
          title: 'Manage Graduates',
          path: '/graduate-admin?tab=manage',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=manage'
        },
        {
          title: 'Manage Colleges',
          path: '/graduate-admin?tab=colleges',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=colleges'
        },
        {
          title: 'Manage Departments',
          path: '/graduate-admin?tab=departments',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=departments'
        },
        {
          title: 'Graduate Fields of Study',
          path: '/graduate-admin?tab=graduate-fields-of-study',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=graduate-fields-of-study'
        },
        {
          title: 'Manage Admission Classifications',
          path: '/graduate-admin?tab=admission-classifications',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=admission-classifications'
        },
        {
          title: 'Manage Programs',
          path: '/graduate-admin?tab=programs',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=programs'
        }
      ]
    },


    {
      title: 'Application Portal',
      icon: <FileText className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 2,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 2 ? null : 2),
      items: [
        {
          title: 'Manage Colleges',
          path: '/graduate-admin?tab=application-colleges',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-colleges'
        },
        {
          title: 'Manage Departments',
          path: '/graduate-admin?tab=application-departments',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-departments'
        },
        {
          title: 'Manage Programs',
          path: '/graduate-admin?tab=application-programs',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-programs'
        },
        {
          title: 'Manage Study Programs',
          path: '/graduate-admin?tab=application-study-programs',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-study-programs'
        },
        {
          title: 'Manage Admission Types',
          path: '/graduate-admin?tab=application-admission-types',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-admission-types'
        },

        {
          title: 'Manage Registration Periods',
          path: '/graduate-admin?tab=application-registration-periods',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-registration-periods'
        },
        {
          title: 'Manage Fields of Study',
          path: '/graduate-admin?tab=application-fields-of-study',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-fields-of-study'
        },
        {
          title: 'Manage Years',
          path: '/graduate-admin?tab=years',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=years'
        },
        {
          title: 'Manage Terms',
          path: '/graduate-admin?tab=terms',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=terms'
        },
        {
          title: 'Application Information',
          path: '/graduate-admin?tab=application-information',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-information'
        },
        {
          title: 'Manage Applicants',
          path: '/graduate-admin?tab=applicants',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=applicants'
        },
        {
          title: 'Downloadable Content',
          path: '/graduate-admin?tab=downloadable-content',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=downloadable-content'
        }
      ]
    },
    {
      title: 'Services',
      icon: <Cog className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 3,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 3 ? null : 3),
      items: [
        {
          title: 'Service Types',
          path: '/graduate-admin?tab=service-types',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=service-types'
        },
        {
          title: 'Document Types',
          path: '/graduate-admin?tab=document-types',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=document-types'
        },

        {
          title: 'Alumni Applications',
          path: '/graduate-admin?tab=alumni-applications',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=alumni-applications'
        }
      ]
    },
    // Reports menu removed as requested
    {
      title: 'Officials',
      icon: <Award className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 4,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 4 ? null : 4),
      items: [
        {
          title: 'Certificate Types',
          path: '/graduate-admin?tab=certificate-types',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=certificate-types'
        },
        {
          title: 'Official Certificates',
          path: '/graduate-admin?tab=official-certificates',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=official-certificates'
        }
      ]
    },
    // Settings - Only for Superuser
    ...(isSuperuser ? [{
      title: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      path: '/graduate-admin?tab=settings',
      active: location.pathname === '/graduate-admin' && location.search === '?tab=settings'
    }] : []),
    // Authentication & Authorization - For Staff and Superuser
    {
      title: 'Authentication & Authorization',
      icon: <Shield className="h-5 w-5" />,
      path: '/graduate-admin?tab=authentication-management',
      active: location.pathname === '/graduate-admin' && location.search === '?tab=authentication-management'
    },
    {
      title: 'Communication',
      icon: <Mail className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 6,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 6 ? null : 6),
      items: [
        {
          title: 'Announcements',
          path: '/graduate-admin?tab=announcements',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=announcements'
        },
        {
          title: 'Email Notifications',
          path: '/graduate-admin?tab=email-notifications',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=email-notifications'
        },
        {
          title: 'SMS Notifications',
          path: '/graduate-admin?tab=sms-notifications',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=sms-notifications'
        },
        {
          title: 'Message Center',
          path: '/graduate-admin?tab=message-center',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=message-center'
        }
      ]
    }
  ];
  */

  // Format the current time
  const formattedTime = currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  const formattedDate = currentTime.toLocaleDateString([], { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });

  return (
    <div className={cn(
      "flex h-screen bg-gray-50 transition-colors duration-200 overflow-hidden",
      darkMode && "bg-gray-900 text-white"
    )}>
      {/* Mobile menu backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={toggleSidebar}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out",
          "md:translate-x-0 md:static md:inset-auto md:z-auto",
          sidebarOpen ? "translate-x-0" : "-translate-x-full",
          sidebarCollapsed && !sidebarHovered ? "w-12" : "w-[85vw] xs:w-[75vw] sm:w-72 md:w-72 lg:w-80 xl:w-80",
          darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800",
          "flex-shrink-0 border-r shadow-lg md:shadow-none",
          darkMode ? "border-gray-700" : "border-gray-200"
        )}
        onMouseEnter={() => sidebarCollapsed && setSidebarHovered(true)}
        onMouseLeave={() => setSidebarHovered(false)}
      >
        <div className="flex flex-col h-full overflow-hidden">
          {/* Sidebar Header */}
          <div className={cn(
            "flex items-center h-16 px-4 border-b",
            sidebarCollapsed && !sidebarHovered ? "justify-center" : "justify-between",
            darkMode ? "bg-gray-800 border-gray-700" : "",
            "text-white shadow-md"
          )}
          style={{
            backgroundColor: darkMode ? undefined : settings.primaryColor,
            borderColor: darkMode ? undefined : `${settings.primaryColor}99`, // Slightly darker border
            backgroundImage: !darkMode ? `linear-gradient(to right, ${settings.primaryColor}, ${settings.primaryColor}ee)` : undefined
          }}>
            <div className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "flex w-full")}>
              <Link to="/graduate-admin" className="flex items-center gap-3 overflow-hidden">
                <div className="flex-shrink-0">
                  <HeaderLogo
                    logoUrl={settings.headerLogoUrl}
                    organizationName={settings.organizationName}
                    className="bg-white/10 rounded-md p-0.5"
                  />
                </div>
                <span className="text-lg font-bold truncate">{settings.systemName}</span>
              </Link>
            </div>
            <div className="flex items-center space-x-1">
              {/* Collapse toggle - always visible */}
              <button
                className="flex p-2 rounded-md hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-white/20"
                onClick={toggleSidebarCollapse}
                aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {sidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
              </button>

              {/* Close button - visible on mobile */}
              <button
                className="md:hidden p-2 rounded-md hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-white/20"
                onClick={toggleSidebar}
                aria-label="Close menu"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* User Profile in Sidebar */}
          <div className={cn(
            "p-4 border-b",
            sidebarCollapsed && !sidebarHovered ? "px-1" : "p-4",
            darkMode ? "border-gray-700" : "border-gray-200"
          )}>
            <div className={cn(
              "flex items-center",
              sidebarCollapsed && !sidebarHovered ? "justify-center" : "space-x-3"
            )}>
              <div
                className="h-10 w-10 rounded-full flex items-center justify-center text-white font-medium shadow-sm"
                style={{ backgroundColor: darkMode ? undefined : settings.primaryColor }}
              >
                {userName.charAt(0).toUpperCase()}
              </div>
              <div className={cn("flex-1 min-w-0", sidebarCollapsed && !sidebarHovered ? "hidden" : "block")}>
                <p className={cn(
                  "text-sm font-medium truncate",
                  darkMode ? "text-white" : "text-gray-900"
                )}>{userName}</p>
                <div className="flex items-center">
                  <Shield
                    className="h-3 w-3 mr-1"
                    style={{ color: darkMode ? "#a5b4fc" : settings.primaryColor }}
                  />
                  <p className={cn(
                    "text-xs truncate",
                    darkMode ? "text-gray-400" : "text-gray-500"
                  )}>{userRole}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar Content */}
          <div className={cn(
            "flex-1 py-4 relative",
            sidebarCollapsed && !sidebarHovered ?
              "px-0 overflow-hidden" :
              "px-3 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500"
          )}>
            <nav className="space-y-4">
              {/* Section Header - Main */}
              <div className={cn(
                "px-3 py-2 text-xs font-semibold",
                darkMode ? "text-gray-400" : "text-gray-500",
                sidebarCollapsed && !sidebarHovered ? "hidden" : "block"
              )}>
                MAIN NAVIGATION
              </div>

              {/* Dashboard & Main Sections */}
              <div className="space-y-1">
                {menuItems.slice(0, 5).map((item, index) => (
                <div key={index}>
                  {item.submenu ? (
                    <div className="space-y-1">
                      <button
                        onClick={item.toggleSubmenu}
                        title={sidebarCollapsed ? item.title : ""}
                        className={cn(
                          "flex items-center w-full text-sm font-medium rounded-md group transition-colors",
                          sidebarCollapsed && !sidebarHovered ? "justify-center px-1 py-3" : "px-3 py-2",
                          item.active
                            ? darkMode
                              ? sidebarCollapsed
                                ? "bg-indigo-900/50 text-indigo-300 border-t-4 border-l-0 font-medium"
                                : "bg-indigo-900/50 text-indigo-300 border-l-4 font-medium"
                              : sidebarCollapsed
                                ? "bg-opacity-10 border-t-4 border-l-0 font-medium"
                                : "bg-opacity-10 border-l-4 font-medium"
                            : darkMode
                              ? "text-gray-300 hover:bg-gray-700/50"
                              : "text-gray-700 hover:bg-gray-100"
                        )}
                        style={item.active && !darkMode ? {
                          backgroundColor: `${settings.primaryColor}20`, // 20% opacity
                          borderColor: settings.primaryColor,
                          color: settings.primaryColor
                        } : {}}
                      >
                        <span className={cn(
                          sidebarCollapsed && !sidebarHovered ? "mr-0" : "mr-3",
                          item.active
                            ? darkMode ? "text-indigo-300" : ""
                            : darkMode ? "text-gray-400" : "text-gray-500"
                        )}
                        style={item.active && !darkMode ? { color: settings.primaryColor } : {}}>
                          {sidebarCollapsed && !sidebarHovered ?
                            <div className="scale-90">{item.icon}</div> :
                            item.icon
                          }
                        </span>
                        <div className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "flex", "flex-1 items-center justify-between")}>
                          <>
                            <span className="flex-1 text-left">{item.title}</span>
                            <span>
                              {item.submenuOpen ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </span>
                          </>
                        </div>
                      </button>

                      {item.submenuOpen && (
                        <div className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "block")}>
                        <div className={cn(
                          "pl-10 space-y-1 mt-1 mb-1 border-l-2 ml-3 border-opacity-50",
                          darkMode ? "border-gray-600" : "border-gray-300"
                        )}>
                          {/* Submenu items */}
                          {item.items?.map((subItem, subIndex) => (
                            <Link
                              key={subIndex}
                              to={subItem.path}
                              onClick={() => handleMenuItemClick(index, true, index)}
                              className={cn(
                                "flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                                subItem.active
                                  ? darkMode
                                    ? "bg-indigo-900/30 text-indigo-300 border-l-4 font-medium"
                                    : "bg-opacity-10 border-l-4 font-medium"
                                  : darkMode
                                    ? "text-gray-300 hover:bg-gray-700/50"
                                    : "text-gray-600 hover:bg-gray-100"
                              )}
                              style={subItem.active && !darkMode ? {
                                backgroundColor: `${settings.primaryColor}20`, // 20% opacity
                                borderColor: settings.primaryColor,
                                color: settings.primaryColor
                              } : {}}
                            >
                              <span>
                                {subItem.title}
                              </span>
                            </Link>
                          ))}
                        </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      target={item.external ? "_blank" : undefined}
                      onClick={() => handleMenuItemClick(index)}
                      title={sidebarCollapsed ? item.title : ""}
                      className={cn(
                        "flex items-center text-sm font-medium rounded-md transition-colors",
                        sidebarCollapsed ? "justify-center px-2 py-3" : "px-3 py-2",
                        item.active
                          ? darkMode
                            ? sidebarCollapsed
                              ? "bg-indigo-900/50 text-indigo-300 border-t-4 border-l-0 font-medium"
                              : "bg-indigo-900/50 text-indigo-300 border-l-4 font-medium"
                            : sidebarCollapsed
                              ? "bg-opacity-10 border-t-4 border-l-0 font-medium"
                              : "bg-opacity-10 border-l-4 font-medium"
                          : darkMode
                            ? "text-gray-300 hover:bg-gray-700/50"
                            : "text-gray-700 hover:bg-gray-100"
                      )}
                      style={item.active && !darkMode ? {
                        backgroundColor: `${settings.primaryColor}20`, // 20% opacity
                        borderColor: settings.primaryColor,
                        color: settings.primaryColor
                      } : {}}
                    >
                      <span className={cn(
                        sidebarCollapsed && !sidebarHovered ? "mr-0" : "mr-3",
                        item.active
                          ? darkMode ? "text-indigo-300" : ""
                          : darkMode ? "text-gray-400" : "text-gray-500"
                      )}
                      style={item.active && !darkMode ? { color: settings.primaryColor } : {}}>
                        {sidebarCollapsed && !sidebarHovered ?
                          <div className="scale-90">{item.icon}</div> :
                          item.icon
                        }
                      </span>
                      <span className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "inline")}>{item.title}</span>
                    </Link>
                  )}
                </div>
              ))}
              </div>

              {/* Section Header - Administration */}
              <div className={cn(
                "px-3 py-2 text-xs font-semibold mt-6",
                darkMode ? "text-gray-400" : "text-gray-500",
                sidebarCollapsed && !sidebarHovered ? "hidden" : "block"
              )}>
                ADMINISTRATION
              </div>

              {/* Administrative Sections */}
              <div className="space-y-1">
                {menuItems.slice(5, menuItems.length - 1).map((item, index) => (
                <div key={index + 5}>
                  {item.submenu ? (
                    <div className="space-y-1">
                      <button
                        onClick={item.toggleSubmenu}
                        title={sidebarCollapsed ? item.title : ""}
                        className={cn(
                          "flex items-center w-full text-sm font-medium rounded-md group transition-colors",
                          sidebarCollapsed && !sidebarHovered ? "justify-center px-1 py-3" : "px-3 py-2",
                          item.active
                            ? darkMode
                              ? sidebarCollapsed && !sidebarHovered
                                ? "bg-indigo-900/50 text-indigo-300 border-t-4 border-l-0 border-indigo-500 font-medium"
                                : "bg-indigo-900/50 text-indigo-300 border-l-4 border-indigo-500 font-medium"
                              : sidebarCollapsed && !sidebarHovered
                                ? "bg-indigo-50 text-indigo-700 border-t-4 border-l-0 border-indigo-500 font-medium"
                                : "bg-indigo-50 text-indigo-700 border-l-4 border-indigo-500 font-medium"
                            : darkMode
                              ? "text-gray-300 hover:bg-gray-700/50"
                              : "text-gray-700 hover:bg-gray-100"
                        )}
                      >
                        <span className={cn(
                          "flex items-center justify-center",
                          sidebarCollapsed && !sidebarHovered ? "w-5 h-5" : "mr-3",
                          item.active
                            ? darkMode ? "text-indigo-300" : "text-indigo-600"
                            : darkMode ? "text-gray-400" : "text-gray-500"
                        )}>
                          {sidebarCollapsed && !sidebarHovered ?
                            <div className="scale-90">{item.icon}</div> :
                            item.icon
                          }
                        </span>
                        <div className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "flex", "flex-1 items-center justify-between")}>
                          <>
                            <span className="flex-1 text-left">{item.title}</span>
                            <span>
                              {item.submenuOpen ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </span>
                          </>
                        </div>
                      </button>

                      {item.submenuOpen && (
                        <div className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "block")}>
                        <div className={cn(
                          "pl-10 space-y-1 mt-1 mb-1 border-l-2 ml-3 border-opacity-50",
                          darkMode ? "border-gray-600" : "border-gray-300"
                        )}>
                          {/* Submenu items */}
                          {item.items?.map((subItem, subIndex) => (
                            <Link
                              key={subIndex}
                              to={subItem.path}
                              onClick={() => handleMenuItemClick(index + 5, true, index + 5)}
                              className={cn(
                                "flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors",
                                subItem.active
                                  ? darkMode
                                    ? "bg-indigo-900/30 text-indigo-300 border-l-4 border-indigo-500 font-medium"
                                    : "bg-indigo-50 text-indigo-700 border-l-4 border-indigo-500 font-medium"
                                  : darkMode
                                    ? "text-gray-300 hover:bg-gray-700/50"
                                    : "text-gray-600 hover:bg-gray-100"
                              )}
                            >
                              <span className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "inline")}>{subItem.title}</span>
                            </Link>
                          ))}
                        </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      target={item.external ? "_blank" : undefined}
                      onClick={() => handleMenuItemClick(index + 5)}
                      title={sidebarCollapsed ? item.title : ""}
                      className={cn(
                        "flex items-center text-sm font-medium rounded-md transition-colors",
                        sidebarCollapsed && !sidebarHovered ? "justify-center px-2 py-3" : "px-3 py-2",
                        item.active
                          ? darkMode
                            ? sidebarCollapsed && !sidebarHovered
                              ? "bg-indigo-900/50 text-indigo-300 border-t-4 border-l-0 border-indigo-500 font-medium"
                              : "bg-indigo-900/50 text-indigo-300 border-l-4 border-indigo-500 font-medium"
                            : sidebarCollapsed && !sidebarHovered
                              ? "bg-indigo-50 text-indigo-700 border-t-4 border-l-0 border-indigo-500 font-medium"
                              : "bg-indigo-50 text-indigo-700 border-l-4 border-indigo-500 font-medium"
                          : darkMode
                            ? "text-gray-300 hover:bg-gray-700/50"
                            : "text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      <span className={cn(
                        "flex items-center justify-center",
                        sidebarCollapsed && !sidebarHovered ? "w-5 h-5" : "mr-3",
                        item.active
                          ? darkMode ? "text-indigo-300" : "text-indigo-600"
                          : darkMode ? "text-gray-400" : "text-gray-500"
                      )}>
                        {sidebarCollapsed && !sidebarHovered ?
                          <div className="scale-90">{item.icon}</div> :
                          item.icon
                        }
                      </span>
                      <span className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "inline")}>{item.title}</span>
                    </Link>
                  )}
                </div>
              ))}
              </div>

              {/* Public Site - Always visible */}
              <div className="mt-6 sticky bottom-0 bg-inherit pb-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                <Link
                  to={menuItems[menuItems.length - 1].path}
                  target="_blank"
                  title="Public Site"
                  className={cn(
                    "flex items-center text-sm font-medium rounded-md transition-colors",
                    sidebarCollapsed && !sidebarHovered ? "justify-center px-2 py-3" : "px-3 py-2",
                    "dark:bg-indigo-900/30",
                    darkMode
                      ? "text-indigo-300 hover:bg-indigo-900/50"
                      : "hover:bg-opacity-20"
                  )}
                  style={!darkMode ? {
                    backgroundColor: `${settings.primaryColor}10`, // 10% opacity
                    color: settings.primaryColor
                  } : {}}
                >
                  <span className={cn(
                    "flex items-center justify-center",
                    sidebarCollapsed && !sidebarHovered ? "w-5 h-5" : "mr-3",
                    darkMode ? "text-indigo-300" : ""
                  )}
                  style={!darkMode ? { color: settings.primaryColor } : {}}>
                    {sidebarCollapsed && !sidebarHovered ?
                      <div className="scale-90">{menuItems[menuItems.length - 1].icon}</div> :
                      menuItems[menuItems.length - 1].icon
                    }
                  </span>
                  <span className={cn(sidebarCollapsed && !sidebarHovered ? "hidden" : "inline")}>
                    {menuItems[menuItems.length - 1].title}
                  </span>
                </Link>
              </div>
            </nav>
          </div>

          {/* Sidebar Footer */}
          <div className={cn(
            "border-t",
            sidebarCollapsed && !sidebarHovered ? "p-1" : "p-4",
            darkMode ? "border-gray-700 bg-gray-800" : "border-gray-200 bg-gray-50"
          )}>
            {sidebarCollapsed && !sidebarHovered ? (
              <div className="flex flex-col space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  title="Change Password"
                  className={cn(
                    "justify-center",
                    darkMode
                      ? "border-gray-700 text-gray-300 hover:bg-gray-700 hover:text-white"
                      : "border-gray-200 text-gray-700 hover:bg-gray-100"
                  )}
                  onClick={() => setIsPasswordModalOpen(true)}
                >
                  <div className="scale-90"><KeyRound className="h-4 w-4" /></div>
                </Button>

                <Button
                  variant="destructive"
                  size="sm"
                  title="Sign Out"
                  className="justify-center"
                  onClick={handleLogout}
                >
                  <div className="scale-90"><LogOut className="h-4 w-4" /></div>
                </Button>
              </div>
            ) : (
              <div className="flex flex-col">
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className={cn(
                      "flex-1 justify-center",
                      darkMode
                        ? "border-gray-700 text-gray-300 hover:bg-gray-700 hover:text-white"
                        : "border-gray-200 text-gray-700 hover:bg-gray-100"
                    )}
                    onClick={() => setIsPasswordModalOpen(true)}
                  >
                    <KeyRound className="h-4 w-4 mr-1.5" />
                    <span className="text-xs font-medium">Password</span>
                  </Button>

                  <Button
                    variant="destructive"
                    size="sm"
                    className="flex-1 justify-center"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4 mr-1.5" />
                    <span className="text-xs font-medium">Logout</span>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 transition-all duration-300 overflow-hidden">
        {/* Top Navbar */}
        <header className={cn(
          "shadow-sm z-10",
          darkMode ? "bg-gray-800 border-b border-gray-700" : "bg-white border-b border-gray-200"
        )}>
          <div className="flex items-center justify-between h-16 px-2 sm:px-4 md:px-6 overflow-hidden">
            {/* Left side - Title, mobile menu, and date/time */}
            <div className="flex items-center">
              {/* Mobile menu toggle - only visible on small screens */}
              <button
                className={cn(
                  "p-2 rounded-md md:hidden transition-colors focus:outline-none",
                  darkMode
                    ? "text-gray-300 hover:text-white hover:bg-gray-700 focus:ring-2 focus:ring-gray-600 bg-gray-700/50"
                    : "text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:ring-2 focus:ring-gray-200 bg-gray-100/80 shadow-sm"
                )}
                onClick={toggleSidebar}
                aria-label="Toggle mobile menu"
                style={{
                  color: !darkMode ? settings.primaryColor : undefined,
                }}
              >
                <Menu className="h-6 w-6" />
              </button>

              {/* Sidebar collapse toggle - hidden on mobile */}
              <button
                className={cn(
                  "p-2 rounded-md hidden md:flex transition-colors focus:outline-none",
                  darkMode
                    ? "text-gray-300 hover:text-white hover:bg-gray-700 focus:ring-2 focus:ring-gray-600"
                    : "text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:ring-2 focus:ring-gray-200"
                )}
                onClick={toggleSidebarCollapse}
                aria-label={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {sidebarCollapsed ? <ChevronRight className="h-6 w-6" /> : <ChevronLeft className="h-6 w-6" />}
              </button>

              <div className="ml-2 sm:ml-4 overflow-hidden max-w-[150px] sm:max-w-[200px] md:max-w-none">
                <h1 className={cn(
                  "text-base sm:text-lg md:text-xl font-semibold truncate",
                  darkMode ? "text-white" : "text-gray-800"
                )}>
                  {location.pathname === '/graduate-admin' ? `${settings.systemName}` :
                   location.pathname === '/graduate-reports' ? 'Graduate Reports' : `${settings.systemName}`}
                </h1>
              </div>

              {/* Date and Time - visible on medium screens and up */}
              <div className={cn(
                "hidden md:flex flex-col ml-6 border-l pl-6",
                darkMode ? "border-gray-700" : "border-gray-200"
              )}>
                <div className={cn(
                  "text-xs font-medium",
                  darkMode ? "text-gray-300" : "text-gray-700"
                )}>
                  {formattedDate}
                </div>
                <div className={cn(
                  "text-xs",
                  darkMode ? "text-gray-400" : "text-gray-500"
                )}>
                  {formattedTime}
                </div>
              </div>
            </div>

            {/* Right side - Actions and user profile */}
            <div className="flex items-center space-x-1 xs:space-x-2 sm:space-x-3 md:space-x-4">
              {/* Debug button - Development only */}
              {process.env.NODE_ENV === 'development' && (
                <button
                  onClick={debugPermissions}
                  className={cn(
                    "hidden md:flex items-center justify-center h-8 w-8 rounded-full transition-colors",
                    "bg-red-100 text-red-700 hover:bg-red-200 hover:text-red-900"
                  )}
                  title="Debug Menu Permissions"
                >
                  <Shield className="h-4 w-4" />
                </button>
              )}

              {/* Search button */}
              <button className={cn(
                "hidden md:flex items-center justify-center h-8 w-8 rounded-full transition-colors",
                darkMode
                  ? "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900"
              )}>
                <Search className="h-4 w-4" />
              </button>

              {/* Help button */}
              <button className={cn(
                "hidden md:flex items-center justify-center h-8 w-8 rounded-full transition-colors",
                darkMode
                  ? "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900"
              )}>
                <HelpCircle className="h-4 w-4" />
              </button>

              {/* Notifications button */}
              <button className={cn(
                "flex items-center justify-center h-7 w-7 sm:h-8 sm:w-8 rounded-full transition-colors relative",
                darkMode
                  ? "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900"
              )}>
                <Bell className="h-4 w-4" />
                <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
              </button>

              {/* Dark mode toggle for larger screens */}
              <button
                className={cn(
                  "hidden md:flex items-center justify-center h-8 w-8 rounded-full transition-colors",
                  darkMode
                    ? "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900"
                )}
                onClick={toggleDarkMode}
              >
                {darkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </button>

              {/* Change Password button */}
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "flex items-center h-7 w-7 sm:h-auto sm:w-auto p-0 sm:px-3 sm:py-1",
                  darkMode
                    ? "border-gray-700 text-gray-300 hover:bg-gray-700 hover:text-white"
                    : "border-gray-200 text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                )}
                onClick={() => setIsPasswordModalOpen(true)}
              >
                <KeyRound className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Password</span>
              </Button>

              {/* Logout button */}
              <Button
                variant="destructive"
                size="sm"
                className={cn(
                  "flex items-center h-7 w-7 sm:h-auto sm:w-auto p-0 sm:px-3 sm:py-1",
                  darkMode
                    ? "bg-red-600 hover:bg-red-700 text-white"
                    : "bg-red-600 hover:bg-red-700 text-white"
                )}
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Logout</span>
              </Button>

              {/* User profile dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div className={cn(
                    "flex items-center space-x-1 sm:space-x-2 cursor-pointer px-1 sm:px-3 py-1 sm:py-1.5 rounded-full transition-colors",
                    darkMode
                      ? "hover:bg-gray-700"
                      : "hover:bg-gray-100"
                  )}>
                    <div className={cn(
                      "h-7 w-7 sm:h-8 sm:w-8 rounded-full flex items-center justify-center text-white font-medium shadow-sm",
                      "bg-indigo-600"
                    )}>
                      {userName.charAt(0).toUpperCase()}
                    </div>
                    <div className="hidden md:block">
                      <p className={cn(
                        "text-sm font-medium truncate",
                        darkMode ? "text-white" : "text-gray-900"
                      )}>{userName}</p>
                      <div className="flex items-center">
                        <Shield className={cn(
                          "h-3 w-3 mr-1",
                          darkMode ? "text-indigo-400" : "text-indigo-600"
                        )} />
                        <p className={cn(
                          "text-xs truncate",
                          darkMode ? "text-gray-400" : "text-gray-500"
                        )}>{userRole}</p>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-4 w-4 hidden md:block",
                      darkMode ? "text-gray-400" : "text-gray-500"
                    )} />
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className={cn(
                  "w-56 mt-1",
                  darkMode ? "bg-gray-800 border-gray-700 text-white" : ""
                )}>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="font-medium">{userName}</p>
                      <div className="flex items-center">
                        <Shield className={cn(
                          "h-3 w-3 mr-1",
                          darkMode ? "text-indigo-400" : "text-indigo-600"
                        )} />
                        <p className={cn(
                          "text-xs",
                          darkMode ? "text-gray-400" : "text-gray-500"
                        )}>{userRole}</p>
                      </div>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator className={darkMode ? "bg-gray-700" : ""} />
                  <DropdownMenuItem
                    onClick={() => setIsPasswordModalOpen(true)}
                    className={cn(
                      "cursor-pointer",
                      darkMode ? "hover:bg-gray-700 focus:bg-gray-700" : ""
                    )}
                  >
                    <KeyRound className="mr-2 h-4 w-4" />
                    <span>Change Password</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className={darkMode ? "bg-gray-700" : ""} />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className={cn(
                      "text-red-600 focus:text-red-600 cursor-pointer",
                      darkMode ? "hover:bg-gray-700 focus:bg-gray-700" : ""
                    )}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className={cn(
          "flex-1 p-2 sm:p-4 md:p-6 flex flex-col overflow-y-auto",
          darkMode ? "bg-gray-900" : "bg-gray-50"
        )}>
          <div className="flex-1 max-w-full">
            <div className="w-full mx-auto px-0 sm:px-2 md:px-4 container-fluid">
              <div className="w-full sm:max-w-full md:max-w-[1200px] lg:max-w-[1400px] mx-auto overflow-x-hidden">
                {children}
              </div>
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className={cn(
          "py-3 sm:py-4 px-2 sm:px-4 md:px-6 border-t text-center",
          darkMode
            ? "bg-gray-800 border-gray-700 text-gray-400"
            : "bg-white border-gray-200 text-gray-500"
        )}>
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm">
              {isSettingsLoading ? (
                <span className="inline-block h-4 w-40 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></span>
              ) : (
                settings.copyright
              )}
            </p>
            <div className="flex flex-wrap justify-center space-x-2 sm:space-x-4 mt-2 md:mt-0">
              <a href="#" className={cn(
                "text-sm hover:underline",
                darkMode ? "text-gray-400 hover:text-white" : "text-gray-600 hover:text-gray-900"
              )}>Privacy Policy</a>
              <a href="#" className={cn(
                "text-sm hover:underline",
                darkMode ? "text-gray-400 hover:text-white" : "text-gray-600 hover:text-gray-900"
              )}>Terms of Service</a>
              <a href="#" className={cn(
                "text-sm hover:underline",
                darkMode ? "text-gray-400 hover:text-white" : "text-gray-600 hover:text-gray-900"
              )}>Contact</a>
            </div>
          </div>
        </footer>
      </div>

      {/* Password Change Modal */}
      <ChangePasswordModal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
      />

      {/* Back to Top Button */}
      <BackToTop />

      {/* Floating Menu Button for Mobile */}
      {!sidebarOpen && (
        <button
          className="md:hidden fixed bottom-6 right-6 z-50 p-3 rounded-full shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105"
          style={{
            backgroundColor: darkMode ? '#4B5563' : settings.primaryColor,
            color: 'white',
          }}
          onClick={toggleSidebar}
          aria-label="Open menu"
        >
          <Menu className="h-6 w-6" />
        </button>
      )}
    </div>
  );
};

export default NewAdminLayout;
