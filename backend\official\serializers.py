from rest_framework import serializers
from .models import OfficialSent, OfficialReceived
from setups.certificate_type.models import CertificateType


class CertificateTypeSerializer(serializers.ModelSerializer):
    """Serializer for Certificate Type (for nested representation)"""
    class Meta:
        model = CertificateType
        fields = ['uuid', 'name', 'description', 'is_active']


class OfficialSentSerializer(serializers.ModelSerializer):
    """Serializer for OfficialSent model"""
    certificate_type_details = CertificateTypeSerializer(source='certificate_type', read_only=True)
    certificate_type_name = serializers.CharField(source='certificate_type.name', read_only=True)
    full_name = serializers.SerializerMethodField()
    gender_display = serializers.CharField(source='get_gender_display', read_only=True)

    class Meta:
        model = OfficialSent
        fields = [
            'id',
            'first_name',
            'second_name',
            'last_name',
            'full_name',
            'gender',
            'gender_display',
            'receiver_institute',
            'send_date',
            'courier',
            'certificate_type',
            'certificate_type_details',
            'certificate_type_name',
            'tracking_number',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'full_name', 'gender_display', 'certificate_type_details', 'certificate_type_name', 'created_at', 'updated_at']
        extra_kwargs = {
            'first_name': {'required': True},
            'second_name': {'required': True},
            'last_name': {'required': True},
            'gender': {'required': True},
            'receiver_institute': {'required': True},
            'send_date': {'required': True},
            'courier': {'required': True},
            'certificate_type': {'required': True},
            'tracking_number': {'required': True}
        }

    def get_full_name(self, obj):
        """Get full name combining first, second, and last names"""
        names = [obj.first_name]
        if obj.second_name:
            names.append(obj.second_name)
        names.append(obj.last_name)
        return ' '.join(names)

    def validate_tracking_number(self, value):
        """Validate tracking number uniqueness"""
        if self.instance:
            # For updates, exclude current instance
            if OfficialSent.objects.filter(tracking_number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        else:
            # For creation, check if tracking number exists
            if OfficialSent.objects.filter(tracking_number=value).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        return value

    def validate_certificate_type(self, value):
        """Validate certificate type is active"""
        if not value.is_active:
            raise serializers.ValidationError("Cannot assign an inactive certificate type.")
        return value


class OfficialReceivedSerializer(serializers.ModelSerializer):
    """Serializer for OfficialReceived model"""
    certificate_type_details = CertificateTypeSerializer(source='certificate_type', read_only=True)
    certificate_type_name = serializers.CharField(source='certificate_type.name', read_only=True)
    full_name = serializers.SerializerMethodField()
    gender_display = serializers.CharField(source='get_gender_display', read_only=True)

    class Meta:
        model = OfficialReceived
        fields = [
            'id',
            'first_name',
            'second_name',
            'last_name',
            'full_name',
            'gender',
            'gender_display',
            'sender_institute',
            'arival_date',
            'courier',
            'certificate_type',
            'certificate_type_details',
            'certificate_type_name',
            'tracking_number',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'full_name', 'gender_display', 'certificate_type_details', 'certificate_type_name', 'created_at', 'updated_at']
        extra_kwargs = {
            'first_name': {'required': True},
            'second_name': {'required': True},
            'last_name': {'required': True},
            'gender': {'required': True},
            'sender_institute': {'required': True},
            'arival_date': {'required': True},
            'courier': {'required': True},
            'certificate_type': {'required': True},
            'tracking_number': {'required': True}
        }

    def get_full_name(self, obj):
        """Get full name combining first, second, and last names"""
        names = [obj.first_name]
        if obj.second_name:
            names.append(obj.second_name)
        names.append(obj.last_name)
        return ' '.join(names)

    def validate_tracking_number(self, value):
        """Validate tracking number uniqueness"""
        if self.instance:
            # For updates, exclude current instance
            if OfficialReceived.objects.filter(tracking_number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        else:
            # For creation, check if tracking number exists
            if OfficialReceived.objects.filter(tracking_number=value).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        return value

    def validate_certificate_type(self, value):
        """Validate certificate type is active"""
        if not value.is_active:
            raise serializers.ValidationError("Cannot assign an inactive certificate type.")
        return value


class OfficialSentCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating OfficialSent records"""
    class Meta:
        model = OfficialSent
        fields = [
            'first_name',
            'second_name',
            'last_name',
            'gender',
            'receiver_institute',
            'send_date',
            'courier',
            'certificate_type',
            'tracking_number'
        ]
        extra_kwargs = {
            'first_name': {'required': True},
            'second_name': {'required': True},
            'last_name': {'required': True},
            'gender': {'required': True},
            'receiver_institute': {'required': True},
            'send_date': {'required': True},
            'courier': {'required': True},
            'certificate_type': {'required': True},
            'tracking_number': {'required': True}
        }

    def validate_tracking_number(self, value):
        """Validate tracking number uniqueness"""
        if self.instance:
            # For updates, exclude current instance
            if OfficialSent.objects.filter(tracking_number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        else:
            # For creation, check if tracking number exists
            if OfficialSent.objects.filter(tracking_number=value).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        return value

    def validate_certificate_type(self, value):
        """Validate certificate type is active"""
        if not value.is_active:
            raise serializers.ValidationError("Cannot assign an inactive certificate type.")
        return value


class OfficialReceivedCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating OfficialReceived records"""
    class Meta:
        model = OfficialReceived
        fields = [
            'first_name',
            'second_name',
            'last_name',
            'gender',
            'sender_institute',
            'arival_date',
            'courier',
            'certificate_type',
            'tracking_number'
        ]
        extra_kwargs = {
            'first_name': {'required': True},
            'second_name': {'required': True},
            'last_name': {'required': True},
            'gender': {'required': True},
            'sender_institute': {'required': True},
            'arival_date': {'required': True},
            'courier': {'required': True},
            'certificate_type': {'required': True},
            'tracking_number': {'required': True}
        }

    def validate_tracking_number(self, value):
        """Validate tracking number uniqueness"""
        if self.instance:
            # For updates, exclude current instance
            if OfficialReceived.objects.filter(tracking_number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        else:
            # For creation, check if tracking number exists
            if OfficialReceived.objects.filter(tracking_number=value).exists():
                raise serializers.ValidationError("A certificate with this tracking number already exists.")
        return value

    def validate_certificate_type(self, value):
        """Validate certificate type is active"""
        if not value.is_active:
            raise serializers.ValidationError("Cannot assign an inactive certificate type.")
        return value
