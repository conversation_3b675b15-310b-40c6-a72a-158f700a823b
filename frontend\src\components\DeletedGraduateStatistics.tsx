import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Trash2,
  Clock,
  Users,
  Calendar,
  Building,
  BookOpen,
  TrendingDown,
  AlertTriangle,
  BarChart3,
  Pie<PERSON>hart,
  Activity,
  Timer,
  UserX,
  Archive
} from 'lucide-react';
import { graduateVerificationAPI } from '@/services/api';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface DeletedStatistics {
  total_deleted: number;
  time_periods: {
    today: number;
    last_7_days: number;
    last_30_days: number;
    last_90_days: number;
  };
  by_college: Array<{ college__name: string; count: number }>;
  by_department: Array<{ department__name: string; count: number }>;
  by_year: Array<{ year_of_graduation: number; count: number }>;
  by_gender: Array<{ gender: string; count: number }>;
  recent_deletions: Array<{
    id: number;
    full_name: string;
    student_id: string;
    college_name: string;
    department_name: string;
    year_of_graduation: number;
    deleted_at: string;
    deleted_by_name: string;
  }>;
}

const DeletedGraduateStatistics: React.FC = () => {
  const [data, setData] = useState<DeletedStatistics | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchDeletedStatistics = async () => {
    try {
      console.log('🔄 fetchDeletedStatistics called');
      setLoading(true);
      console.log('📡 Calling graduateVerificationAPI.getDeletedStatistics()');
      const response = await graduateVerificationAPI.getDeletedStatistics();
      console.log('📥 Received response:', response);

      if (response && response.data) {
        console.log('✅ Setting data:', response.data);
        setData(response.data);
      } else {
        console.log('❌ No data in response:', response);
        toast.error('No data received from server');
      }
    } catch (error: any) {
      console.error('❌ Error fetching deleted statistics:', error);
      console.error('❌ Error details:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);
      toast.error('Failed to fetch deleted graduate statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeletedStatistics();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">Failed to load deleted graduate statistics</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Deleted */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50 to-pink-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600 mb-1">Total Deleted</p>
                <p className="text-3xl font-bold text-red-700">{data.total_deleted.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg">
                <Trash2 className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Deleted Today */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-amber-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 mb-1">Deleted Today</p>
                <p className="text-3xl font-bold text-orange-700">{data.time_periods.today}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl shadow-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Deleted Last 7 Days */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-orange-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600 mb-1">Last 7 Days</p>
                <p className="text-3xl font-bold text-yellow-700">{data.time_periods.last_7_days}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Deleted Last 30 Days */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-indigo-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 mb-1">Last 30 Days</p>
                <p className="text-3xl font-bold text-purple-700">{data.time_periods.last_30_days}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
                <Timer className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Deleted by College */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500 rounded-lg">
                <Building className="h-5 w-5 text-white" />
              </div>
              <CardTitle className="text-lg text-blue-700">Deleted by College</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {data.by_college.length > 0 ? (
              <div className="space-y-3">
                {data.by_college.slice(0, 5).map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium text-gray-700 truncate flex-1">
                      {item.college__name || 'Unknown College'}
                    </span>
                    <Badge variant="destructive" className="ml-2">
                      {item.count}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No deleted records by college</p>
            )}
          </CardContent>
        </Card>

        {/* Deleted by Department */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-500 rounded-lg">
                <BookOpen className="h-5 w-5 text-white" />
              </div>
              <CardTitle className="text-lg text-green-700">Deleted by Department</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {data.by_department.length > 0 ? (
              <div className="space-y-3">
                {data.by_department.slice(0, 5).map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium text-gray-700 truncate flex-1">
                      {item.department__name || 'Unknown Department'}
                    </span>
                    <Badge variant="destructive" className="ml-2">
                      {item.count}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No deleted records by department</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Deletions */}
      {data.recent_deletions.length > 0 && (
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-red-50">
          <CardHeader className="bg-gradient-to-r from-red-50 to-pink-50 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-500 rounded-lg">
                <UserX className="h-5 w-5 text-white" />
              </div>
              <CardTitle className="text-lg text-red-700">Recent Deletions</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Student</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">College</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Year</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Deleted By</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {data.recent_deletions.slice(0, 5).map((deletion) => (
                    <tr key={deletion.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div>
                          <p className="font-medium text-gray-900">{deletion.full_name}</p>
                          <p className="text-sm text-gray-500">{deletion.student_id}</p>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-700">
                        {deletion.college_name || 'N/A'}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-700">
                        {deletion.year_of_graduation}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-700">
                        {deletion.deleted_by_name || 'System'}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-700">
                        {new Date(deletion.deleted_at).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DeletedGraduateStatistics;
