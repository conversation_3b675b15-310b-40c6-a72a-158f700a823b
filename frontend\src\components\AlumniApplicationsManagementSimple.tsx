import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { alumniApplicationsAPI, ApplicationListParams } from '@/services/alumniApplicationsAPI';

const AlumniApplicationsManagementSimple: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;

  const queryClient = useQueryClient();

  // Query parameters
  const getQueryParams = (): ApplicationListParams => ({
    page: currentPage,
    page_size: pageSize,
    ordering: '-created_at'
  });

  // Fetch Form1 applications
  const { 
    data: form1Data, 
    isLoading: form1Loading, 
    error: form1Error,
    refetch: refetchForm1 
  } = useQuery({
    queryKey: ['alumni-applications-form1', getQueryParams()],
    queryFn: () => alumniApplicationsAPI.getApplications(getQueryParams()),
  });

  // Handle refresh
  const handleRefresh = () => {
    refetchForm1();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Alumni Applications Management</CardTitle>
                <CardDescription className="mt-1">
                  Manage alumni application requests for transcripts, certificates, and other services
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                className="border-blue-200 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200"
                onClick={handleRefresh}
                disabled={form1Loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${form1Loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            {form1Loading ? (
              <div>Loading applications...</div>
            ) : form1Error ? (
              <div className="text-red-500">Error loading applications</div>
            ) : (
              <div>
                <h3 className="text-lg font-medium mb-2">Applications Loaded Successfully</h3>
                <p className="text-muted-foreground">
                  Found {form1Data?.data?.count || 0} applications
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AlumniApplicationsManagementSimple;
