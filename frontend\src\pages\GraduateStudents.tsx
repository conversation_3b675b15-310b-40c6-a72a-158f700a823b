import { useEffect, useState } from 'react';
import Layout from '@/components/Layout';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, ChevronDown, ChevronRight } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import api from '@/services/api';

interface College {
  id: number;
  name: string;
  code: string;
}

interface Department {
  id: number;
  name: string;
  code: string;
  college: number;
  college_name: string;
}

interface FieldOfStudy {
  id: number;
  name: string;
  code: string;
  department: number;
  department_name: string;
  college_name: string;
}

interface Program {
  id: number;
  name: string;
  code: string;
}

interface AdmissionClassification {
  id: number;
  name: string;
  description: string;
}

interface GraduateStudent {
  id: number;
  student_id: string;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  year_of_graduation: number;
  gpa: string;
  gender: string;
  college: number;
  college_name: string;
  department: number;
  department_name: string;
  field_of_study: number;
  field_of_study_name: string;
  program: number;
  program_name: string;
  admission_classification: number;
  admission_classification_name: string;
  full_name: string;
}

interface CollegeGroup {
  id: number;
  name: string;
  students: GraduateStudent[];
}

const GraduateStudents = () => {
  // State for graduate students
  const [students, setStudents] = useState<GraduateStudent[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<GraduateStudent[]>([]);
  const [collegeGroups, setCollegeGroups] = useState<CollegeGroup[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // State for filters
  const [selectedCollegeId, setSelectedCollegeId] = useState<string>('all');
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>('all');
  const [selectedYearId, setSelectedYearId] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // State for colleges and departments
  const [colleges, setColleges] = useState<College[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [years, setYears] = useState<number[]>([]);
  
  // State for expanded colleges
  const [expandedColleges, setExpandedColleges] = useState<{[key: number]: boolean}>({});

  // Function to group students by college
  const groupByCollege = (students: GraduateStudent[]): CollegeGroup[] => {
    const collegeMap: {[key: number]: CollegeGroup} = {};

    // First, initialize the college map with all colleges from the database
    colleges.forEach(college => {
      collegeMap[college.id] = {
        id: college.id,
        name: college.name,
        students: []
      };
    });

    // Then add students to their respective colleges
    students.forEach(student => {
      const collegeId = student.college;

      if (!collegeMap[collegeId]) {
        // If the college doesn't exist in our map (which shouldn't happen if we have all colleges),
        // create it using the data from the student
        collegeMap[collegeId] = {
          id: collegeId,
          name: student.college_name,
          students: []
        };
      }

      collegeMap[collegeId].students.push(student);
    });

    // Return only colleges that have students
    return Object.values(collegeMap).filter(college => college.students.length > 0);
  };

  // Function to toggle college expansion
  const toggleCollegeExpansion = (collegeId: number) => {
    setExpandedColleges(prev => ({
      ...prev,
      [collegeId]: !prev[collegeId]
    }));
  };

  // Handle college selection
  const handleCollegeChange = (collegeId: string) => {
    setSelectedCollegeId(collegeId);
    
    // Reset department selection when college changes
    setSelectedDepartmentId('all');
    
    // Filter students based on selected college and year
    filterStudents(collegeId, 'all', selectedYearId, searchQuery);
  };

  // Handle department selection
  const handleDepartmentChange = (departmentId: string) => {
    setSelectedDepartmentId(departmentId);
    
    // Filter students based on selected college, department, and year
    filterStudents(selectedCollegeId, departmentId, selectedYearId, searchQuery);
  };

  // Handle year selection
  const handleYearChange = (yearId: string) => {
    setSelectedYearId(yearId);
    
    // Filter students based on selected college, department, and year
    filterStudents(selectedCollegeId, selectedDepartmentId, yearId, searchQuery);
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    // Filter students based on selected college, department, year, and search query
    filterStudents(selectedCollegeId, selectedDepartmentId, selectedYearId, query);
  };

  // Function to filter students
  const filterStudents = (collegeId: string, departmentId: string, yearId: string, query: string) => {
    let filtered = [...students];
    
    // Filter by college
    if (collegeId !== 'all') {
      filtered = filtered.filter(student => student.college.toString() === collegeId);
    }
    
    // Filter by department
    if (departmentId !== 'all') {
      filtered = filtered.filter(student => student.department.toString() === departmentId);
    }
    
    // Filter by year
    if (yearId !== 'all') {
      filtered = filtered.filter(student => student.year_of_graduation.toString() === yearId);
    }
    
    // Filter by search query
    if (query) {
      const lowerQuery = query.toLowerCase();
      filtered = filtered.filter(student => 
        student.student_id.toLowerCase().includes(lowerQuery) ||
        student.first_name.toLowerCase().includes(lowerQuery) ||
        (student.middle_name && student.middle_name.toLowerCase().includes(lowerQuery)) ||
        student.last_name.toLowerCase().includes(lowerQuery) ||
        student.full_name.toLowerCase().includes(lowerQuery)
      );
    }
    
    setFilteredStudents(filtered);
    setCollegeGroups(groupByCollege(filtered));
  };

  // Fetch graduate students
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Fetch colleges
        const collegesResponse = await api.get('/colleges/');
        setColleges(collegesResponse.data);
        
        // Fetch departments
        const departmentsResponse = await api.get('/departments/');
        setDepartments(departmentsResponse.data);
        
        // Fetch graduate students
        const studentsResponse = await api.get('/graduate-students/public/');
        setStudents(studentsResponse.data);
        setFilteredStudents(studentsResponse.data);
        
        // Extract unique years
        const uniqueYears = [...new Set(studentsResponse.data.map((student: GraduateStudent) => student.year_of_graduation))];
        setYears(uniqueYears.sort((a, b) => b - a)); // Sort years in descending order
        
        // Group students by college
        setCollegeGroups(groupByCollege(studentsResponse.data));
        
        // Initialize all colleges as expanded
        const initialExpandedState = collegesResponse.data.reduce((acc: {[key: number]: boolean}, college: College) => {
          acc[college.id] = true; // Set all colleges to expanded by default
          return acc;
        }, {});
        setExpandedColleges(initialExpandedState);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.message || 'An error occurred while fetching data');
        toast.error('Failed to load graduate students data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  return (
    <Layout>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6 text-center">Graduate Students</h1>
        
        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <p className="text-xl">Loading graduate students data...</p>
          </div>
        )}
        
        {/* Error State */}
        {error && (
          <Alert variant="destructive" className="my-8 max-w-2xl mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {/* Filters */}
        {!loading && !error && (
          <div className="mb-8 max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Filter Graduate Students</CardTitle>
                <CardDescription>Use the filters below to find specific graduate students</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  {/* College Filter */}
                  <div>
                    <Select
                      value={selectedCollegeId}
                      onValueChange={handleCollegeChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select College" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Colleges</SelectItem>
                        {colleges.map(college => (
                          <SelectItem key={college.id} value={college.id.toString()}>
                            {college.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Department Filter */}
                  <div>
                    <Select
                      value={selectedDepartmentId}
                      onValueChange={handleDepartmentChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Departments</SelectItem>
                        {departments
                          .filter(dept => selectedCollegeId === 'all' || dept.college.toString() === selectedCollegeId)
                          .map(dept => (
                            <SelectItem key={dept.id} value={dept.id.toString()}>
                              {dept.name}
                            </SelectItem>
                          ))
                        }
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Year Filter */}
                  <div>
                    <Select
                      value={selectedYearId}
                      onValueChange={handleYearChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Year" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Years</SelectItem>
                        {years.map(year => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                {/* Search */}
                <div className="mt-4">
                  <Input
                    type="text"
                    placeholder="Search by name or student ID"
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full"
                  />
                </div>
                
                {/* Clear Filters Button */}
                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedCollegeId('all');
                      setSelectedDepartmentId('all');
                      setSelectedYearId('all');
                      setSearchQuery('');
                      filterStudents('all', 'all', 'all', '');
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        
        {/* Results Count */}
        {!loading && !error && (
          <div className="mb-4 text-center">
            <p className="text-gray-600">
              Showing {filteredStudents.length} of {students.length} graduate students
            </p>
          </div>
        )}
        
        {/* No Results State */}
        {!loading && !error && filteredStudents.length === 0 && (
          <div className="text-center py-10">
            <p className="text-xl text-gray-500">No graduate students found matching your filters.</p>
          </div>
        )}
        
        {/* Results */}
        {!loading && !error && filteredStudents.length > 0 && (
          <div className="space-y-6">
            {collegeGroups.map(college => (
              <Card key={college.id} className="overflow-hidden">
                {/* College Header (Clickable) */}
                <div
                  className="bg-gray-100 p-4 flex items-center justify-between cursor-pointer"
                  onClick={() => toggleCollegeExpansion(college.id)}
                >
                  <h3 className="text-lg font-semibold">{college.name}</h3>
                  <div className="flex items-center">
                    <span className="mr-2 text-sm text-gray-600">
                      {college.students.length} {college.students.length === 1 ? 'Student' : 'Students'}
                    </span>
                    {expandedColleges[college.id] ? (
                      <ChevronDown className="h-5 w-5" />
                    ) : (
                      <ChevronRight className="h-5 w-5" />
                    )}
                  </div>
                </div>
                
                {/* College Content (Expandable) */}
                {expandedColleges[college.id] && (
                  <CardContent className="p-0">
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Student ID</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Department</TableHead>
                            <TableHead>Field of Study</TableHead>
                            <TableHead>Program</TableHead>
                            <TableHead>Year</TableHead>
                            <TableHead>GPA</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {college.students.map(student => (
                            <TableRow key={student.id}>
                              <TableCell className="font-medium">{student.student_id}</TableCell>
                              <TableCell>{student.full_name}</TableCell>
                              <TableCell>{student.department_name}</TableCell>
                              <TableCell>{student.field_of_study_name}</TableCell>
                              <TableCell>{student.program_name}</TableCell>
                              <TableCell>{student.year_of_graduation}</TableCell>
                              <TableCell>{student.gpa}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default GraduateStudents;
