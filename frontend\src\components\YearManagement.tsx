import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Plus, Pencil, Trash2, Search, RefreshCw, Calendar, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';

interface Year {
  uuid: string;
  year: string;
  created_at: string;
  updated_at: string;
}

const YearManagement = () => {
  const [years, setYears] = useState<Year[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentYear, setCurrentYear] = useState<Year | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [formData, setFormData] = useState({
    year: '',
  });
  const [formErrors, setFormErrors] = useState({
    year: '',
  });

  // Fetch years on component mount
  useEffect(() => {
    fetchYears();
  }, []);

  const fetchYears = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to view years');
        return;
      }

      // Try both authenticated and public endpoints
      const endpoints = [
        { url: 'http://localhost:8000/api/years/', auth: true },
        { url: 'http://localhost:8000/api/years/public/', auth: false },
      ];

      let yearData = null;
      for (const endpoint of endpoints) {
        try {
          const config = endpoint.auth ? {
            headers: { Authorization: `Bearer ${token}` }
          } : {};

          const response = await axios.get(endpoint.url, config);
          yearData = response.data;
          console.log(`Successfully fetched from ${endpoint.url}:`, yearData);
          break;
        } catch (error: any) {
          console.log(`Failed to fetch from ${endpoint.url}:`, error.response?.status);
          continue;
        }
      }

      if (yearData) {
        // Handle both array and object responses
        const yearsArray = Array.isArray(yearData) ? yearData : 
                          yearData.results ? yearData.results : 
                          [yearData];
        
        setYears(yearsArray);
      } else {
        toast.error('Failed to fetch years from all endpoints');
        setYears([]);
      }
    } catch (error: any) {
      console.error('Error fetching years:', error);
      toast.error('Failed to fetch years');
      setYears([]);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const errors = { year: '' };
    let isValid = true;

    if (!formData.year.trim()) {
      errors.year = 'Year is required';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleAdd = async () => {
    if (!validateForm()) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to add years');
        return;
      }

      const response = await axios.post(
        'http://localhost:8000/api/years/',
        formData,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      toast.success('Year added successfully');
      setIsAddDialogOpen(false);
      setFormData({ year: '' });
      setFormErrors({ year: '' });
      fetchYears();
    } catch (error: any) {
      console.error('Error adding year:', error);
      if (error.response?.data?.year) {
        setFormErrors({ year: error.response.data.year[0] });
      } else {
        toast.error('Failed to add year');
      }
    }
  };

  const handleEdit = async () => {
    if (!validateForm() || !currentYear) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to edit years');
        return;
      }

      const response = await axios.put(
        `http://localhost:8000/api/years/${currentYear.uuid}/`,
        formData,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      toast.success('Year updated successfully');
      setIsEditDialogOpen(false);
      setCurrentYear(null);
      setFormData({ year: '' });
      setFormErrors({ year: '' });
      fetchYears();
    } catch (error: any) {
      console.error('Error updating year:', error);
      if (error.response?.data?.year) {
        setFormErrors({ year: error.response.data.year[0] });
      } else {
        toast.error('Failed to update year');
      }
    }
  };

  const handleDelete = async () => {
    if (!currentYear) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to delete years');
        return;
      }

      await axios.delete(
        `http://localhost:8000/api/years/delete/${currentYear.uuid}/`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      toast.success('Year deleted successfully');
      setIsDeleteDialogOpen(false);
      setCurrentYear(null);
      fetchYears();
    } catch (error: any) {
      console.error('Error deleting year:', error);
      toast.error('Failed to delete year');
    }
  };

  const openEditDialog = (year: Year) => {
    setCurrentYear(year);
    setFormData({ year: year.year });
    setFormErrors({ year: '' });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (year: Year) => {
    setCurrentYear(year);
    setIsDeleteDialogOpen(true);
  };

  const openAddDialog = () => {
    setFormData({ year: '' });
    setFormErrors({ year: '' });
    setIsAddDialogOpen(true);
  };

  // Filter years based on search term
  const filteredYears = years.filter(year =>
    year.year.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredYears.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredYears.length / itemsPerPage);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0] mx-auto mb-4"></div>
            <div className="text-center">
              <p className="text-gray-600 font-medium">Loading years...</p>
              <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Year Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete academic years
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={fetchYears}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 transition-all duration-200"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    onClick={openAddDialog}
                    className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Year
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                        <Calendar className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Add New Year</DialogTitle>
                        <DialogDescription className="text-gray-600 leading-relaxed">
                          Create a new academic year. Fill in the required information below.
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>
                  <div className="p-6 space-y-6">
                    <div className="grid grid-cols-1 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="add-year" className="text-sm font-medium text-gray-700">
                          Year *
                        </Label>
                        <Input
                          id="add-year"
                          value={formData.year}
                          onChange={(e) => setFormData({ ...formData, year: e.target.value })}
                          placeholder="Enter year (e.g., 2024, 2024-2025)"
                          className="border-blue-200 focus:ring-blue-400 focus:border-blue-400 transition-all"
                        />
                        {formErrors.year && (
                          <p className="text-sm text-red-500 mt-1">{formErrors.year}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsAddDialogOpen(false);
                          setFormData({ year: '' });
                          setFormErrors({ year: '' });
                        }}
                        className="border-gray-300 text-gray-700 hover:bg-gray-50"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleAdd}
                        className="bg-[#1a73c0] hover:bg-blue-700 text-white transition-all duration-200"
                        disabled={!formData.year.trim()}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create Year
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Search & Filter Section */}
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
            <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Search & Filter
            </h3>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                <Input
                  placeholder="Search years..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Years Table */}
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[80%] text-[#1a73c0] font-medium">Year</TableHead>
                    <TableHead className="w-[20%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={2} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0]"></div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">Loading years...</p>
                            <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : !Array.isArray(currentItems) || currentItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={2} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="p-4 bg-blue-50 rounded-full">
                            <Calendar className="h-8 w-8 text-blue-400" />
                          </div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">No years found</p>
                            <p className="text-sm text-gray-500">
                              {searchTerm ? 'Try adjusting your search criteria' : 'Get started by adding your first year'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    Array.isArray(currentItems) && currentItems.map((year) => (
                      <TableRow key={year.uuid} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{year.year}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(year)}
                              title="Edit"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDeleteDialog(year)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredYears.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredYears.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredYears.length}</span> records
                </div>
              </div>

              {/* Pagination Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="First page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="Previous page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Dynamic page number buttons */}
                <div className="flex gap-1">
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant={currentPage === number ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={`w-8 h-8 ${currentPage === number ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show smart pagination
                    <>
                      {/* Always show first page */}
                      <Button
                        variant={currentPage === 1 ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={`w-8 h-8 ${currentPage === 1 ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {currentPage > 4 && (
                        <Button
                          variant="outline"
                          size="sm"
                          disabled
                          className="w-8 h-8 border-blue-200"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number => number >= Math.max(2, currentPage - 1) && number <= Math.min(totalPages - 1, currentPage + 1))
                        .map(number => (
                          <Button
                            key={number}
                            variant={currentPage === number ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={`w-8 h-8 ${currentPage === number ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {currentPage < totalPages - 3 && (
                        <Button
                          variant="outline"
                          size="sm"
                          disabled
                          className="w-8 h-8 border-blue-200"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant={currentPage === totalPages ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={`w-8 h-8 ${currentPage === totalPages ? 'bg-[#1a73c0] hover:bg-[#145da1]' : 'border-blue-200 hover:bg-blue-100'}`}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="Next page"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 disabled:opacity-50"
                  title="Last page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Pencil className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Edit Year</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Update the year information below. Make sure to save your changes when finished.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <div className="space-y-2">
                <Label htmlFor="edit-year" className="text-sm font-medium text-gray-700">
                  Year *
                </Label>
                <Input
                  id="edit-year"
                  value={formData.year}
                  onChange={(e) => setFormData({ ...formData, year: e.target.value })}
                  placeholder="Enter year (e.g., 2024, 2024-2025)"
                  className="border-blue-200 focus:ring-blue-400 focus:border-blue-400 transition-all"
                />
                {formErrors.year && (
                  <p className="text-sm text-red-500 mt-1">{formErrors.year}</p>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditDialogOpen(false);
                  setCurrentYear(null);
                  setFormData({ year: '' });
                  setFormErrors({ year: '' });
                }}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
              <Button
                onClick={handleEdit}
                className="bg-[#1a73c0] hover:bg-blue-700 text-white transition-all duration-200"
                disabled={!formData.year.trim()}
              >
                <Pencil className="h-4 w-4 mr-2" />
                Update Year
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-t-lg border-b border-red-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-red-500 rounded-xl shadow-lg">
                <Trash2 className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-red-600 mb-2">Delete Year</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Are you sure you want to delete the year "{currentYear?.year}"? This action cannot be undone.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6">
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setCurrentYear(null);
                }}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                className="bg-red-500 hover:bg-red-600 text-white transition-all duration-200"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Year
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default YearManagement;
