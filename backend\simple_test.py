#!/usr/bin/env python
"""
Simple test to check if the term app is properly configured
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Test 1: Check if the term app files exist and can be imported
print("Testing Term app configuration...")

try:
    # Test imports
    from setups.term import models, views, serializers, urls
    print("✓ All term app modules can be imported")
except ImportError as e:
    print(f"✗ Import error: {e}")

try:
    # Test URL patterns
    from setups.term.urls import urlpatterns
    print(f"✓ Term URL patterns loaded: {len(urlpatterns)} patterns")
    for pattern in urlpatterns:
        print(f"  - {pattern}")
except Exception as e:
    print(f"✗ URL patterns error: {e}")

try:
    # Test if the app is in INSTALLED_APPS
    from backend.settings import INSTALLED_APPS
    if 'setups.term' in INSTALLED_APPS:
        print("✓ setups.term is in INSTALLED_APPS")
    else:
        print("✗ setups.term is NOT in INSTALLED_APPS")
        print(f"INSTALLED_APPS: {INSTALLED_APPS}")
except Exception as e:
    print(f"✗ Settings error: {e}")

print("Test completed!")
