import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2, Award, FileText, AlertTriangle, ChevronRight, Plus, School, Edit, Trash, User } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { toast } from 'sonner';
import { applicationAPI } from '@/services/api';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Define interfaces for our data types
interface GATRecord {
  id: number;
  GAT_No: string;
  GAT_Result: number;
  created_at: string;
  updated_at: string;
  user: number;
}

interface ProgramSelection {
  id: number;
  application_num: string;
  sponsorship: string;
  application_info: any;
  created_at: string;
  updated_at: string;
  user: number;
  gat: number;
  year?: string;
  year_name?: string;
  term?: string;
  term_name?: string;
}

interface Documentation {
  id: number;
  degree?: string;
  sponsorship?: string;
  student_copy?: string;
  recommendation?: string;
  costsharing?: string;
  publication?: string;
  conceptnote?: string;
  support_letter?: string;
}





const ApplicationStatusNew = () => {
  const navigate = useNavigate();
  const [gatRecords, setGatRecords] = useState<GATRecord[]>([]);
  const [programSelections, setProgramSelections] = useState<ProgramSelection[]>([]);
  const [documentations, setDocumentations] = useState<Documentation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<{ id: number, type: 'gat' | 'program' } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [hasApplicantInfo, setHasApplicantInfo] = useState(false);
  const [isCheckingApplicantInfo, setIsCheckingApplicantInfo] = useState(true);

  // Check if the user has completed their personal information
  useEffect(() => {
    const checkApplicantInfo = async () => {
      try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
          console.warn('No authentication token found, redirecting to login');
          navigate('/login');
          return;
        }

        setIsCheckingApplicantInfo(true);
        const response = await applicationAPI.getCurrentApplicantInfo();
        console.log('Applicant info response:', response.data);

        // Check for authentication errors
        if (response.data && response.data.detail === 'Authentication credentials were not provided.') {
          console.warn('Authentication error detected, redirecting to login');
          navigate('/login');
          return;
        }

        // If the response has data and it's not an empty array, the user has completed their personal information
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          setHasApplicantInfo(true);
        } else {
          setHasApplicantInfo(false);
        }
      } catch (error) {
        console.error('Error checking applicant info:', error);

        // Check if error is due to authentication
        if (error.response && error.response.status === 401) {
          console.warn('Authentication error detected, redirecting to login');
          navigate('/login');
          return;
        }

        setHasApplicantInfo(false);
      } finally {
        setIsCheckingApplicantInfo(false);
      }
    };

    checkApplicantInfo();
  }, [navigate]);

  useEffect(() => {
    const fetchAllApplicationData = async () => {
      try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
          console.warn('No authentication token found in fetchAllApplicationData, redirecting to login');
          navigate('/login');
          return;
        }

        setLoading(true);

        // Fetch all GAT records for the current user
        const gatResponse = await applicationAPI.getCurrentGAT();
        console.log('GAT records:', gatResponse.data);

        // Check for authentication errors
        if (gatResponse.data && gatResponse.data.detail === 'Authentication credentials were not provided.') {
          console.warn('Authentication error detected in GAT response, redirecting to login');
          navigate('/login');
          return;
        }

        // Ensure we always set an array, even if the API returns something else
        setGatRecords(Array.isArray(gatResponse.data) ? gatResponse.data : []);

        // Fetch all program selections for the current user
        const programResponse = await applicationAPI.getCurrentProgramSelection();
        console.log('Program selections:', programResponse.data);

        // Check for authentication errors
        if (programResponse.data && programResponse.data.detail === 'Authentication credentials were not provided.') {
          console.warn('Authentication error detected in program selection response, redirecting to login');
          navigate('/login');
          return;
        }

        // Ensure we always set an array, even if the API returns something else
        setProgramSelections(Array.isArray(programResponse.data) ? programResponse.data : []);

        // Fetch all documentation records for the current user
        try {
          const documentationResponse = await applicationAPI.getCurrentDocumentation();
          console.log('Documentation records:', documentationResponse.data);

          // Ensure we always set an array, even if the API returns something else
          setDocumentations(Array.isArray(documentationResponse.data) ? documentationResponse.data : []);
        } catch (docError) {
          console.error('Error fetching documentation data:', docError);
          // Don't fail the whole process if documentation fetch fails
          setDocumentations([]);
        }

      } catch (error) {
        console.error('Error fetching application data:', error);

        // Check if error is due to authentication
        if (error.response && error.response.status === 401) {
          console.warn('Authentication error detected in fetchAllApplicationData, redirecting to login');
          navigate('/login');
          return;
        }

        setError('Failed to load your applications. Please try again later.');
        toast.error('Failed to load your applications');
      } finally {
        setLoading(false);
      }
    };

    fetchAllApplicationData();
  }, [navigate]);



  // Function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return '';
      }

      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Find program selection for a GAT record
  const findProgramSelection = (gatId: number) => {
    return programSelections.find(ps => ps.gat === gatId);
  };

  // Find documentation for a GAT record
  const findDocumentation = (gatId: number) => {
    // First find the program selection for this GAT
    const programSelection = findProgramSelection(gatId);
    if (!programSelection) return null;

    // Then find documentation that matches the user ID
    // Since documentation is linked to the user, not directly to GAT or program selection
    // We check if any documentation exists for this user
    return documentations.length > 0 ? documentations[0] : null;
  };

  // Start a new application
  const startNewApplication = () => {
    // Check if the user has completed their personal information
    if (!hasApplicantInfo) {
      // If not, redirect to the personal information page
      toast.error('You must complete your personal information before starting a new application.');
      navigate('/application/personal-info');
      return;
    }

    // Skip the new application page and go directly to the GAT page with new=true
    navigate('/application/gat?new=true');
  };

  // Continue an existing application
  const continueApplication = (gatId: number) => {
    navigate(`/application/gat?id=${gatId}`);
  };

  // View application details
  const viewApplicationDetails = (programSelectionId: number) => {
    navigate(`/application/details/${programSelectionId}`);
  };

  // Edit GAT record
  const editGatRecord = (gatId: number) => {
    navigate(`/application/gat?id=${gatId}&edit=true`);
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (id: number, type: 'gat' | 'program') => {
    setRecordToDelete({ id, type });
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setRecordToDelete(null);
  };

  // Delete record
  const deleteRecord = async () => {
    if (!recordToDelete) return;

    setIsDeleting(true);

    try {
      if (recordToDelete.type === 'gat') {
        // Check if this GAT record has associated program selections
        const associatedProgram = programSelections.find(ps => ps.gat === recordToDelete.id);

        if (associatedProgram) {
          // Delete the program selection first
          await applicationAPI.deleteProgramSelection(associatedProgram.id);
        }

        // Then delete the GAT record
        await applicationAPI.deleteGAT(recordToDelete.id);

        // Update the state
        setGatRecords(gatRecords.filter(gat => gat.id !== recordToDelete.id));
        setProgramSelections(programSelections.filter(ps => ps.gat !== recordToDelete.id));

        toast.success('Application deleted successfully');
      } else if (recordToDelete.type === 'program') {
        // Delete just the program selection
        await applicationAPI.deleteProgramSelection(recordToDelete.id);

        // Update the state
        setProgramSelections(programSelections.filter(ps => ps.id !== recordToDelete.id));

        toast.success('Program selection deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting record:', error);
      toast.error('Failed to delete record. Please try again.');
    } finally {
      setIsDeleting(false);
      closeDeleteDialog();
    }
  };



  // Removed the separate loading state to avoid flash

  if (error) {
    return (
      <DashboardLayout>
        <div>
          <Card className="mb-8 shadow">
            <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50 border-b border-red-100">
              <CardTitle className="text-xl text-gray-800">Error</CardTitle>
              <CardDescription>There was a problem with your applications</CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <div className="flex items-center justify-center h-64">
                <div className="flex flex-col items-center">
                  <AlertTriangle className="h-12 w-12 text-red-500 mb-6" />
                  <p className="text-red-600 text-lg font-medium mb-4">{error}</p>
                  <p className="text-gray-600 mb-8">Please try again or contact support.</p>
                  <Button
                    onClick={() => window.location.reload()}
                    className="bg-[#1a73c0] hover:bg-[#0e4a7d]"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        {/* Hero Section with Animated Background */}
        <Card className="shadow border-0 overflow-hidden mb-6">
          <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
            <div className="absolute top-0 left-0 w-full h-full opacity-10"
                 style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
            <div className="relative z-10 flex items-center">
              <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                <Award className="h-8 w-8 text-[#1a73c0]" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-white">My Applications</CardTitle>
                <CardDescription className="text-blue-100 mt-1">
                  Manage your graduate program applications
                </CardDescription>
              </div>

              <div className="ml-auto">
                <Button
                  onClick={startNewApplication}
                  className="bg-white hover:bg-white/90 text-[#1a73c0] font-medium px-5 py-2 h-auto text-base shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20 rounded-md"
                >
                  <Plus className="h-5 w-5 mr-2 text-[#1a73c0]" />
                  Start New Application
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Personal Information Alert */}
        {!hasApplicantInfo && !isCheckingApplicantInfo && (
          <Card className="mb-6 border-yellow-200 shadow">
            <CardContent className="p-4 bg-gradient-to-r from-yellow-50 to-amber-50">
              <div className="flex items-start">
                <div className="bg-yellow-100 p-2 rounded-full mr-3 mt-1">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <h3 className="text-base font-medium text-yellow-800">Personal Information Required</h3>
                  <p className="text-sm text-yellow-700 mt-1 mb-3">
                    You must complete your personal information before starting a new application.
                  </p>
                  <Button
                    onClick={() => navigate('/application/personal-info')}
                    className="bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium px-5 py-2 h-auto text-base shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <User className="h-5 w-5 mr-2" />
                    Complete Personal Information
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Applications Section */}
        <Card className="shadow border-0 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-blue-50 border-b border-blue-100 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                  <FileText className="h-5 w-5 text-[#1a73c0]" />
                </div>
                <div>
                  <CardTitle className="text-lg font-medium text-gray-800">Your Applications</CardTitle>
                  <CardDescription className="text-gray-500">
                    {gatRecords.length > 0
                      ? `You have ${gatRecords.length} application${gatRecords.length > 1 ? 's' : ''}`
                      : 'Start your application journey'}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {gatRecords.length > 0 && (
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                    className="border-gray-300 hover:bg-gray-50"
                    size="sm"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-1"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"></path><path d="M16 21h5v-5"></path></svg>
                    Refresh
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            {/* Loading State - Simplified */}
            {loading || isCheckingApplicantInfo ? (
              <div className="p-6 space-y-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse bg-white rounded-lg shadow border border-gray-100 p-4">
                    <div className="flex items-start">
                      <div className="rounded-full bg-gray-200 h-12 w-12 mr-4"></div>
                      <div className="flex-1">
                        <div className="h-5 bg-gray-200 rounded w-1/4 mb-3"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/5"></div>
                      </div>
                      <div className="flex gap-2">
                        <div className="h-8 bg-gray-200 rounded w-20"></div>
                        <div className="h-8 bg-gray-200 rounded w-20"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : gatRecords.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
                <div className="bg-blue-50 p-4 rounded-full mb-4">
                  <FileText className="h-10 w-10 text-[#1a73c0]" />
                </div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">No Applications Yet</h3>
                <p className="text-gray-600 max-w-md mb-6">
                  Start your application process to apply for graduate programs at University of Gondar.
                </p>
                <Button
                  onClick={() => navigate('/application/personal-info')}
                  className="bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium px-5 py-2 h-auto text-base shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <User className="h-5 w-5 mr-2" />
                  Complete Personal Information
                </Button>
              </div>
            ) : (
              <div className="p-6 space-y-4">
                {/* Card-based layout for applications */}
                {gatRecords.map((gat) => {
                  const programSelection = findProgramSelection(gat.id);
                  const documentation = findDocumentation(gat.id);
                  // Application is complete if both program selection and documentation exist
                  const isComplete = !!documentation;

                  return (
                    <div
                      key={gat.id}
                      className={`bg-white rounded-lg shadow border ${isComplete ? 'border-blue-200' : 'border-gray-200'}
                                  hover:shadow transition-all duration-300 overflow-hidden`}
                    >
                      <div className="p-5">
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                          {/* GAT Information */}
                          <div className="flex items-start">
                            <div className={`p-3 rounded-lg mr-4 ${isComplete ? 'bg-blue-100' : 'bg-gray-100'}`}>
                              <Award className={`h-6 w-6 ${isComplete ? 'text-[#1a73c0]' : 'text-gray-500'}`} />
                            </div>
                            <div>
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-medium text-gray-900">GAT Number: {gat.GAT_No}</h3>
                              </div>
                              <p className="text-sm text-gray-600">Score: {gat.GAT_Result}</p>
                              {formatDate(gat.created_at) && (
                                <p className="text-xs text-gray-500 mt-1">
                                  Created: {formatDate(gat.created_at)}
                                </p>
                              )}
                            </div>
                          </div>

                          {/* Program Selection Information */}
                          <div className="flex-1 md:border-l md:border-r border-gray-200 md:px-4">
                            {programSelection ? (
                              <div className="flex items-start">
                                <div className="bg-blue-100 p-2 rounded-lg mr-3">
                                  <School className="h-5 w-5 text-[#1a73c0]" />
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900">
                                    {programSelection.application_num}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    Sponsorship: {programSelection.sponsorship}
                                  </p>
                                  {programSelection.year_name && (
                                    <p className="text-sm text-gray-600">
                                      Academic Year: {programSelection.year_name}
                                    </p>
                                  )}
                                  {programSelection.term_name && (
                                    <p className="text-sm text-gray-600">
                                      Term: {programSelection.term_name}
                                    </p>
                                  )}
                                  {programSelection.application_info && programSelection.application_info.field_of_study && (
                                    <div className="mt-2">
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {programSelection.application_info.field_of_study.name}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center">
                                <div className="bg-gray-100 p-2 rounded-lg mr-3">
                                  <School className="h-5 w-5 text-gray-400" />
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Program Selection</p>
                                  <p className="text-sm text-gray-400">Not completed yet</p>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-wrap gap-2 justify-end">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => editGatRecord(gat.id)}
                              className="border-gray-300 hover:bg-gray-50"
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDeleteDialog(gat.id, 'gat')}
                              className="border-red-300 text-red-600 hover:bg-red-50"
                            >
                              <Trash className="h-4 w-4 mr-1" />
                              Delete
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => isComplete ? viewApplicationDetails(programSelection!.id) : continueApplication(gat.id)}
                              className="bg-[#1a73c0] hover:bg-[#0e4a7d] text-white"
                            >
                              {isComplete ? 'View Details' : 'Continue'}
                              <ChevronRight className="h-4 w-4 ml-1" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="h-1.5 bg-gray-100 w-full">
                        <div
                          className="h-full bg-[#1a73c0]"
                          style={{ width: isComplete ? '100%' : '50%' }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Delete Application</DialogTitle>
              <DialogDescription>
                <p className="mb-2">Are you sure you want to delete this application? This action cannot be undone.</p>
                <p className="text-red-600 font-medium">Warning: This will delete all related program selection, documentation, and payment information associated with this application.</p>
              </DialogDescription>
            </DialogHeader>

            {recordToDelete && (
              <div className="py-4">
                <div className="flex items-center mb-4">
                  <div className="bg-red-100 p-2 rounded-full mr-3">
                    <Award className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">GAT</h4>
                    <p className="text-sm text-gray-600">
                      GAT Number: <span className="font-medium">
                        {Array.isArray(gatRecords) && gatRecords.find(g => g.id === recordToDelete.id)?.GAT_No}
                      </span> |
                      Score: <span className="font-medium">
                        {Array.isArray(gatRecords) && gatRecords.find(g => g.id === recordToDelete.id)?.GAT_Result}
                      </span>
                    </p>
                  </div>
                </div>

                {recordToDelete.type === 'gat' && programSelections.some(ps => ps.gat === recordToDelete.id) && (
                  <div className="flex items-start mb-4">
                    <div className="bg-red-100 p-2 rounded-full mr-3 mt-1">
                      <School className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Program Selection</h4>
                      <div className="mt-1 space-y-1">
                        <p className="text-sm text-gray-600">
                          This will also delete the associated program selection.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            <DialogFooter className="flex space-x-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={deleteRecord}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  'Delete Application'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
};

export default ApplicationStatusNew;
