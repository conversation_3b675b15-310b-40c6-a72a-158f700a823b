import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => ({
  server: {
    host: process.env.VITE_DEV_SERVER_HOST || "::",
    port: parseInt(process.env.VITE_DEV_SERVER_PORT || '8081'),
    headers: {
      'Content-Security-Policy': `
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.gpteng.co https://cdn.loom.com;
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
        img-src 'self' data: https://placehold.co ${process.env.VITE_CSP_IMG_SRC || 'http://localhost:8000'};
        font-src 'self' data: https://fonts.gstatic.com https://cdn.loom.com;
        connect-src 'self' ${process.env.VITE_CSP_CONNECT_SRC || 'http://localhost:8000 ws://localhost:8081 wss://localhost:8081'};
      `.replace(/\s+/g, ' ').trim()
    },
    hmr: {
      protocol: 'ws',
      host: process.env.VITE_HMR_HOST || 'localhost',
      port: parseInt(process.env.VITE_HMR_PORT || '8081')
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
