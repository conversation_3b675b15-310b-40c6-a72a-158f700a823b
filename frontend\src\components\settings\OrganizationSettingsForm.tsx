import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { settingsAPI } from '@/services/api';
import { OrganizationSetting } from '@/services/settingsAPI';
import { Loader2, Save, Upload, RefreshCw } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';

const OrganizationSettingsForm: React.FC = () => {
  const { organizationSettings, refreshSettings } = useSettings();
  const [formData, setFormData] = useState<OrganizationSetting>({
    system_name: '',
    organization: '',
    copyright: '',
    contact_info: '',
    contact_number: '',
    support_email: '',
    address: '',
    po_box: '',
    primary_color: '#1a73c0',
    secondary_color: '#f5f5f5',
    account_inactivity_period: 0,
  });
  const [headerLogoFile, setHeaderLogoFile] = useState<File | null>(null);
  const [footerLogoFile, setFooterLogoFile] = useState<File | null>(null);
  const [faviconFile, setFaviconFile] = useState<File | null>(null);
  const [headerLogoPreview, setHeaderLogoPreview] = useState<string | null>(null);
  const [footerLogoPreview, setFooterLogoPreview] = useState<string | null>(null);
  const [faviconPreview, setFaviconPreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  // Initialize form with current settings
  useEffect(() => {
    if (organizationSettings) {
      setFormData({
        ...organizationSettings,
      });

      // Set logo previews if available
      if (organizationSettings.header_logo_url) {
        // Ensure the URL is absolute
        const headerLogoUrl = organizationSettings.header_logo_url.startsWith('http')
          ? organizationSettings.header_logo_url
          : `http://localhost:8000${organizationSettings.header_logo_url}`;
        setHeaderLogoPreview(headerLogoUrl);
      }

      if (organizationSettings.footer_logo_url) {
        // Ensure the URL is absolute
        const footerLogoUrl = organizationSettings.footer_logo_url.startsWith('http')
          ? organizationSettings.footer_logo_url
          : `http://localhost:8000${organizationSettings.footer_logo_url}`;
        setFooterLogoPreview(footerLogoUrl);
      }

      if (organizationSettings.favicon_url) {
        // Ensure the URL is absolute
        const faviconUrl = organizationSettings.favicon_url.startsWith('http')
          ? organizationSettings.favicon_url
          : `http://localhost:8000${organizationSettings.favicon_url}`;
        setFaviconPreview(faviconUrl);
      }
    }
  }, [organizationSettings]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle file uploads
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fileType: 'header_logo' | 'footer_logo' | 'favicon') => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);

      // Update state based on file type
      if (fileType === 'header_logo') {
        setHeaderLogoFile(file);
        setHeaderLogoPreview(previewUrl);
      } else if (fileType === 'footer_logo') {
        setFooterLogoFile(file);
        setFooterLogoPreview(previewUrl);
      } else if (fileType === 'favicon') {
        setFaviconFile(file);
        setFaviconPreview(previewUrl);
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Prepare data for submission
      const dataToSubmit: OrganizationSetting = {
        ...formData,
      };

      // Add files if they exist
      if (headerLogoFile) {
        dataToSubmit.header_logo = headerLogoFile;
      }

      if (footerLogoFile) {
        dataToSubmit.footer_logo = footerLogoFile;
      }

      if (faviconFile) {
        dataToSubmit.favicon = faviconFile;
      }

      // Submit the form
      await settingsAPI.updateOrganizationSettings(dataToSubmit);

      // Refresh settings context
      await refreshSettings();

      toast.success('Organization settings updated successfully');
    } catch (error) {
      console.error('Error updating organization settings:', error);
      toast.error('Failed to update organization settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Organization Settings</CardTitle>
        <CardDescription>
          Configure your organization's information and branding
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="branding">Branding</TabsTrigger>
            </TabsList>

            {/* General Settings */}
            <TabsContent value="general">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="system_name">System Name</Label>
                    <Input
                      id="system_name"
                      name="system_name"
                      value={formData.system_name}
                      onChange={handleInputChange}
                      placeholder="Online Application Portal"
                      required
                    />
                    <p className="text-sm text-muted-foreground">
                      Name displayed in the browser title and headers
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="organization">Organization Name</Label>
                    <Input
                      id="organization"
                      name="organization"
                      value={formData.organization}
                      onChange={handleInputChange}
                      placeholder="University of Gondar"
                      required
                    />
                    <p className="text-sm text-muted-foreground">
                      Full legal name of your organization
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="copyright">Copyright Text</Label>
                  <Input
                    id="copyright"
                    name="copyright"
                    value={formData.copyright}
                    onChange={handleInputChange}
                    placeholder="© 2025 University of Gondar. All rights reserved."
                    required
                  />
                  <p className="text-sm text-muted-foreground">
                    Copyright text displayed in the footer
                  </p>
                </div>
              </div>
            </TabsContent>

            {/* Contact Information */}
            <TabsContent value="contact">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contact_number">Contact Number</Label>
                    <Input
                      id="contact_number"
                      name="contact_number"
                      value={formData.contact_number}
                      onChange={handleInputChange}
                      placeholder="+251581141237"
                      required
                    />
                    <p className="text-sm text-muted-foreground">
                      Phone number in international format
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="support_email">Support Email</Label>
                    <Input
                      id="support_email"
                      name="support_email"
                      type="email"
                      value={formData.support_email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      required
                    />
                    <p className="text-sm text-muted-foreground">
                      Main email address for support and inquiries
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Physical Address</Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    placeholder="University of Gondar, Gondar, Ethiopia"
                    required
                  />
                  <p className="text-sm text-muted-foreground">
                    Physical address of your organization
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="po_box">P.O. Box</Label>
                    <Input
                      id="po_box"
                      name="po_box"
                      value={formData.po_box || ''}
                      onChange={handleInputChange}
                      placeholder="P.O. Box 196"
                    />
                    <p className="text-sm text-muted-foreground">
                      Postal box number (optional)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contact_info">Additional Contact Info</Label>
                    <Textarea
                      id="contact_info"
                      name="contact_info"
                      value={formData.contact_info}
                      onChange={handleInputChange}
                      placeholder="Additional contact information or description"
                      required
                    />
                    <p className="text-sm text-muted-foreground">
                      Additional contact information or description
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Security Settings */}
            <TabsContent value="security">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="account_inactivity_period">Account Inactivity Period</Label>
                  <select
                    id="account_inactivity_period"
                    name="account_inactivity_period"
                    value={formData.account_inactivity_period}
                    onChange={(e) => handleInputChange({
                      target: {
                        name: 'account_inactivity_period',
                        value: e.target.value
                      }
                    } as React.ChangeEvent<HTMLInputElement>)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="0">Disabled</option>
                    <option value="30">30 days</option>
                    <option value="60">60 days</option>
                    <option value="90">90 days</option>
                    <option value="180">6 months</option>
                    <option value="365">1 year</option>
                  </select>
                  <p className="text-sm text-muted-foreground">
                    Automatically deactivate user accounts after this period of login inactivity. Set to 'Disabled' to turn off this feature.
                  </p>
                </div>
              </div>
            </TabsContent>

            {/* Branding */}
            <TabsContent value="branding">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primary_color">Primary Color</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="primary_color"
                        name="primary_color"
                        type="color"
                        value={formData.primary_color}
                        onChange={handleInputChange}
                        className="w-12 h-10 p-1"
                      />
                      <Input
                        type="text"
                        value={formData.primary_color}
                        onChange={handleInputChange}
                        name="primary_color"
                        placeholder="#1a73c0"
                        pattern="^#([A-Fa-f0-9]{6})$"
                        className="flex-1"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Primary brand color in hex format
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="secondary_color">Secondary Color</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="secondary_color"
                        name="secondary_color"
                        type="color"
                        value={formData.secondary_color}
                        onChange={handleInputChange}
                        className="w-12 h-10 p-1"
                      />
                      <Input
                        type="text"
                        value={formData.secondary_color}
                        onChange={handleInputChange}
                        name="secondary_color"
                        placeholder="#f5f5f5"
                        pattern="^#([A-Fa-f0-9]{6})$"
                        className="flex-1"
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Secondary brand color in hex format
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Header Logo */}
                  <div className="space-y-2">
                    <Label htmlFor="header_logo_upload">Header Logo</Label>
                    <div className="flex flex-col items-center p-4 border rounded-md">
                      {headerLogoPreview ? (
                        <div className="mb-2 p-2 bg-gray-50 rounded-md">
                          <img
                            src={headerLogoPreview}
                            alt="Header Logo Preview"
                            className="max-h-24 object-contain"
                          />
                        </div>
                      ) : (
                        <div className="mb-2 p-4 bg-gray-50 rounded-md text-center text-gray-400">
                          No logo uploaded
                        </div>
                      )}
                      <div className="w-full">
                        <Label
                          htmlFor="header_logo_upload"
                          className="w-full cursor-pointer"
                        >
                          <div className="flex items-center justify-center p-2 bg-gray-100 hover:bg-gray-200 rounded-md">
                            <Upload className="h-4 w-4 mr-2" />
                            <span>Upload Logo</span>
                          </div>
                          <Input
                            id="header_logo_upload"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileChange(e, 'header_logo')}
                            className="hidden"
                          />
                        </Label>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Logo displayed in the header area
                    </p>
                  </div>

                  {/* Footer Logo */}
                  <div className="space-y-2">
                    <Label htmlFor="footer_logo_upload">Footer Logo</Label>
                    <div className="flex flex-col items-center p-4 border rounded-md">
                      {footerLogoPreview ? (
                        <div className="mb-2 p-2 bg-gray-50 rounded-md">
                          <img
                            src={footerLogoPreview}
                            alt="Footer Logo Preview"
                            className="max-h-24 object-contain"
                          />
                        </div>
                      ) : (
                        <div className="mb-2 p-4 bg-gray-50 rounded-md text-center text-gray-400">
                          No logo uploaded
                        </div>
                      )}
                      <div className="w-full">
                        <Label
                          htmlFor="footer_logo_upload"
                          className="w-full cursor-pointer"
                        >
                          <div className="flex items-center justify-center p-2 bg-gray-100 hover:bg-gray-200 rounded-md">
                            <Upload className="h-4 w-4 mr-2" />
                            <span>Upload Logo</span>
                          </div>
                          <Input
                            id="footer_logo_upload"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileChange(e, 'footer_logo')}
                            className="hidden"
                          />
                        </Label>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Logo displayed in the footer area
                    </p>
                  </div>

                  {/* Favicon */}
                  <div className="space-y-2">
                    <Label htmlFor="favicon_upload">Favicon</Label>
                    <div className="flex flex-col items-center p-4 border rounded-md">
                      {faviconPreview ? (
                        <div className="mb-2 p-2 bg-gray-50 rounded-md">
                          <img
                            src={faviconPreview}
                            alt="Favicon Preview"
                            className="max-h-24 object-contain"
                          />
                        </div>
                      ) : (
                        <div className="mb-2 p-4 bg-gray-50 rounded-md text-center text-gray-400">
                          No favicon uploaded
                        </div>
                      )}
                      <div className="w-full">
                        <Label
                          htmlFor="favicon_upload"
                          className="w-full cursor-pointer"
                        >
                          <div className="flex items-center justify-center p-2 bg-gray-100 hover:bg-gray-200 rounded-md">
                            <Upload className="h-4 w-4 mr-2" />
                            <span>Upload Favicon</span>
                          </div>
                          <Input
                            id="favicon_upload"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileChange(e, 'favicon')}
                            className="hidden"
                          />
                        </Label>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Small icon displayed in browser tabs (32x32px recommended)
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="mt-6 flex justify-end">
            <Button
              type="button"
              variant="outline"
              className="mr-2"
              onClick={() => refreshSettings()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Reset
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default OrganizationSettingsForm;
