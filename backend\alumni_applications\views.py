from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from datetime import timedelta
import logging

from .models import AlumniApplication, AlumniApplicationMini, ApplicationDocument
from .serializers import (
    AlumniApplicationSerializer,
    AlumniApplicationMiniSerializer,
    AlumniApplicationListSerializer,
    AlumniApplicationMiniListSerializer,
    ApplicationDocumentSerializer,
    AlumniApplicationStatusSerializer,
    AlumniApplicationMiniStatusSerializer,
    ServiceTypeLookupSerializer,
    CollegeLookupSerializer,
    DepartmentLookupSerializer,
    DocumentTypeLookupSerializer
)
from .filters import AlumniApplicationFilter, AlumniApplicationMiniFilter
from .email_service import AlumniApplicationEmailService
from setups.service_type.models import ServiceType
from setups.college.models import College
from setups.department.models import Department
from setups.document_type.models import DocumentType
from rest_framework import filters

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='create')
class AlumniApplicationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for AlumniApplication (Form1) with full CRUD operations.
    """

    queryset = AlumniApplication.objects.all().select_related(
        'service_type', 'college', 'department', 'uog_college', 'uog_department'
    ).prefetch_related('documents', 'service_type__document_types')

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = AlumniApplicationFilter
    search_fields = ['first_name', 'last_name', 'email', 'phone_number', 'transaction_id']
    ordering_fields = ['created_at', 'updated_at', 'application_status', 'payment_status']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return AlumniApplicationListSerializer
        elif self.action == 'update_status':
            return AlumniApplicationStatusSerializer
        else:
            return AlumniApplicationSerializer

    def get_permissions(self):
        """Set permissions based on action."""
        if self.action in ['create', 'list', 'retrieve', 'upload_document', 'documents']:
            # Allow anyone to create, list, view applications, and upload documents (for public forms)
            permission_classes = [AllowAny]
        else:
            # Require authentication for other actions (update, delete, etc.)
            permission_classes = [IsAuthenticated]

        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        """Set created_by when creating and send confirmation email."""
        if self.request.user.is_authenticated:
            application = serializer.save(created_by=self.request.user)
        else:
            application = serializer.save()

        # Send confirmation email asynchronously (non-blocking)
        try:
            AlumniApplicationEmailService.send_application_confirmation(
                application=application,
                is_form1=True
            )
            logger.info(f"Confirmation email queued for application {application.id}")
        except Exception as e:
            logger.error(f"Failed to send confirmation email for application {application.id}: {e}")
            # Don't fail the application creation if email fails

    def perform_update(self, serializer):
        """Set updated_by when updating."""
        if self.request.user.is_authenticated:
            serializer.save(updated_by=self.request.user)
        else:
            serializer.save()

    @action(detail=True, methods=['patch'], permission_classes=[IsAuthenticated])
    def update_status(self, request, pk=None):
        """Update only the application status."""
        application = self.get_object()
        serializer = self.get_serializer(application, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save(updated_by=request.user)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def documents(self, request, pk=None):
        """Get all documents for this application."""
        application = self.get_object()
        documents = application.documents.all()
        serializer = ApplicationDocumentSerializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[AllowAny])
    def upload_document(self, request, pk=None):
        """Upload a document for this application."""
        application = self.get_object()

        # Create document with application_form1 linked
        data = request.data.copy()
        serializer = ApplicationDocumentSerializer(data=data)

        if serializer.is_valid():
            serializer.save(
                application_form1=application,
                uploaded_by=request.user if request.user.is_authenticated else None
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='create')
class AlumniApplicationMiniViewSet(viewsets.ModelViewSet):
    """
    ViewSet for AlumniApplicationMini (Form2) with full CRUD operations.
    """

    queryset = AlumniApplicationMini.objects.all().select_related(
        'service_type', 'college', 'department'
    ).prefetch_related('documents', 'service_type__document_types')

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = AlumniApplicationMiniFilter
    search_fields = ['first_name', 'last_name', 'email', 'phone_number', 'transaction_id']
    ordering_fields = ['created_at', 'updated_at', 'application_status', 'payment_status']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return AlumniApplicationMiniListSerializer
        elif self.action == 'update_status':
            return AlumniApplicationMiniStatusSerializer
        else:
            return AlumniApplicationMiniSerializer

    def get_permissions(self):
        """Set permissions based on action."""
        if self.action in ['create', 'list', 'retrieve', 'upload_document', 'documents']:
            # Allow anyone to create, list, view applications, and upload documents (for public forms)
            permission_classes = [AllowAny]
        else:
            # Require authentication for other actions (update, delete, etc.)
            permission_classes = [IsAuthenticated]

        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        """Set created_by when creating and send confirmation email."""
        if self.request.user.is_authenticated:
            application = serializer.save(created_by=self.request.user)
        else:
            application = serializer.save()

        # Send confirmation email asynchronously (non-blocking)
        try:
            AlumniApplicationEmailService.send_application_confirmation(
                application=application,
                is_form1=False
            )
            logger.info(f"Confirmation email queued for mini application {application.id}")
        except Exception as e:
            logger.error(f"Failed to send confirmation email for mini application {application.id}: {e}")
            # Don't fail the application creation if email fails

    def perform_update(self, serializer):
        """Set updated_by when updating."""
        if self.request.user.is_authenticated:
            serializer.save(updated_by=self.request.user)
        else:
            serializer.save()

    @action(detail=True, methods=['patch'], permission_classes=[IsAuthenticated])
    def update_status(self, request, pk=None):
        """Update only the application status."""
        application = self.get_object()
        serializer = self.get_serializer(application, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save(updated_by=request.user)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def documents(self, request, pk=None):
        """Get all documents for this application."""
        application = self.get_object()
        documents = application.documents.all()
        serializer = ApplicationDocumentSerializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[AllowAny])
    def upload_document(self, request, pk=None):
        """Upload a document for this application."""
        application = self.get_object()

        # Create document with application_form2 linked
        data = request.data.copy()
        serializer = ApplicationDocumentSerializer(data=data)

        if serializer.is_valid():
            serializer.save(
                application_form2=application,
                uploaded_by=request.user if request.user.is_authenticated else None
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ApplicationDocumentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing application documents.
    """

    queryset = ApplicationDocument.objects.all().select_related(
        'application_form1', 'application_form2', 'uploaded_by'
    )
    serializer_class = ApplicationDocumentSerializer
    permission_classes = [IsAuthenticated]

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['original_filename', 'document_type_name']
    ordering_fields = ['upload_timestamp', 'file_size']
    ordering = ['-upload_timestamp']

    def perform_create(self, serializer):
        """Set uploaded_by when creating."""
        serializer.save(uploaded_by=self.request.user)


# Lookup ViewSets for dropdown population
class ServiceTypeLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only ViewSet for service type lookups."""

    queryset = ServiceType.objects.filter(is_active=True).prefetch_related('document_types')
    serializer_class = ServiceTypeLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None

    @action(detail=True, methods=['get'])
    def required_documents(self, request, pk=None):
        """Get required document types for this service type."""
        service_type = self.get_object()
        required_docs = service_type.document_types.filter(is_active=True)
        serializer = DocumentTypeLookupSerializer(required_docs, many=True)

        return Response({
            'service_type': {
                'id': service_type.id,
                'name': service_type.name,
                'fee': service_type.fee
            },
            'required_document_types': serializer.data,
            'required_count': required_docs.count()
        })


class CollegeLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only ViewSet for college lookups."""

    queryset = College.objects.all()
    serializer_class = CollegeLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None


class DepartmentLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only ViewSet for department lookups."""

    queryset = Department.objects.all().select_related('college')
    serializer_class = DepartmentLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None

    def get_queryset(self):
        """Filter departments by college if specified."""
        queryset = super().get_queryset()
        college_id = self.request.query_params.get('college', None)
        if college_id:
            queryset = queryset.filter(college_id=college_id)
        return queryset


class DocumentTypeLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only ViewSet for document type lookups."""

    queryset = DocumentType.objects.filter(is_active=True)
    serializer_class = DocumentTypeLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None

    def get_queryset(self):
        """Filter document types by service type if specified."""
        queryset = super().get_queryset()
        service_type_id = self.request.query_params.get('service_type', None)
        if service_type_id:
            try:
                service_type = ServiceType.objects.get(id=service_type_id)
                queryset = service_type.document_types.filter(is_active=True)
            except ServiceType.DoesNotExist:
                queryset = queryset.none()
        return queryset


class EmailTestView(APIView):
    """
    API endpoint for testing email functionality.
    """
    permission_classes = [AllowAny]  # Allow access for testing

    def post(self, request):
        """Send a test email."""
        try:
            recipient_email = request.data.get('email', '<EMAIL>')

            # Send test email
            success = AlumniApplicationEmailService.send_test_email(
                recipient_email=recipient_email,
                test_message="This is a test email from the Alumni Portal Email Service."
            )

            if success:
                return Response({
                    'success': True,
                    'message': f'Test email sent successfully to {recipient_email}'
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Failed to send test email. Check SMTP configuration.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error sending test email: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ApplicationStatisticsView(APIView):
    """
    API endpoint for alumni application statistics.
    """
    permission_classes = [AllowAny]  # Allow access for now, can be restricted later

    def get(self, request):
        """Get alumni application statistics."""
        try:
            # Get total counts
            form1_count = AlumniApplication.objects.count()
            form2_count = AlumniApplicationMini.objects.count()
            total_requests = form1_count + form2_count

            # Get status counts for Form1
            form1_status_counts = AlumniApplication.objects.values('application_status').annotate(
                count=Count('id')
            )

            # Get status counts for Form2
            form2_status_counts = AlumniApplicationMini.objects.values('application_status').annotate(
                count=Count('id')
            )

            # Combine status counts
            status_counts = {}
            for item in form1_status_counts:
                status = item['application_status'] or 'Pending'
                status_counts[status] = status_counts.get(status, 0) + item['count']

            for item in form2_status_counts:
                status = item['application_status'] or 'Pending'
                status_counts[status] = status_counts.get(status, 0) + item['count']

            # Map statuses to our expected format (using new status names)
            by_status = {
                'pending': status_counts.get('Pending', 0),
                'processing': status_counts.get('On Review', 0) + status_counts.get('Processing', 0),
                'completed': status_counts.get('Complete', 0),
                'rejected': 0  # No rejected status in new model
            }

            # Get payment status counts and revenue calculations
            form1_payment_counts = AlumniApplication.objects.values('payment_status').annotate(
                count=Count('id')
            )

            form2_payment_counts = AlumniApplicationMini.objects.values('payment_status').annotate(
                count=Count('id')
            )

            payment_counts = {}
            for item in form1_payment_counts:
                status = item['payment_status'] or 'Pending'
                payment_counts[status] = payment_counts.get(status, 0) + item['count']

            for item in form2_payment_counts:
                status = item['payment_status'] or 'Pending'
                payment_counts[status] = payment_counts.get(status, 0) + item['count']

            # Calculate actual revenue by summing service fees
            # Paid revenue (Completed + Verified)
            paid_revenue_form1 = AlumniApplication.objects.filter(
                payment_status__in=['Completed', 'Verified']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0

            paid_revenue_form2 = AlumniApplicationMini.objects.filter(
                payment_status__in=['Completed', 'Verified']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0

            paid_revenue = paid_revenue_form1 + paid_revenue_form2

            # Pending revenue (Pending + Processing + Initiated)
            pending_revenue_form1 = AlumniApplication.objects.filter(
                payment_status__in=['Pending', 'Processing', 'Initiated']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0

            pending_revenue_form2 = AlumniApplicationMini.objects.filter(
                payment_status__in=['Pending', 'Processing', 'Initiated']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0

            pending_revenue = pending_revenue_form1 + pending_revenue_form2

            # Unpaid revenue (Failed + Cancelled + Expired)
            unpaid_revenue_form1 = AlumniApplication.objects.filter(
                payment_status__in=['Failed', 'Cancelled', 'Expired']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0

            unpaid_revenue_form2 = AlumniApplicationMini.objects.filter(
                payment_status__in=['Failed', 'Cancelled', 'Expired']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0

            unpaid_revenue = unpaid_revenue_form1 + unpaid_revenue_form2

            # Total potential revenue
            total_revenue_form1 = AlumniApplication.objects.aggregate(
                total=Sum('service_type__fee')
            )['total'] or 0

            total_revenue_form2 = AlumniApplicationMini.objects.aggregate(
                total=Sum('service_type__fee')
            )['total'] or 0

            total_potential_revenue = total_revenue_form1 + total_revenue_form2

            by_payment_status = {
                'paid': payment_counts.get('Completed', 0) + payment_counts.get('Verified', 0),
                'unpaid': payment_counts.get('Failed', 0) + payment_counts.get('Cancelled', 0) + payment_counts.get('Expired', 0),
                'pending': payment_counts.get('Pending', 0) + payment_counts.get('Processing', 0) + payment_counts.get('Initiated', 0)
            }

            # Add revenue information
            revenue_info = {
                'paid_revenue': float(paid_revenue),
                'pending_revenue': float(pending_revenue),
                'unpaid_revenue': float(unpaid_revenue),
                'total_potential_revenue': float(total_potential_revenue)
            }

            # Get recent applications (last 30 days)
            thirty_days_ago = timezone.now() - timedelta(days=30)
            recent_form1 = AlumniApplication.objects.filter(created_at__gte=thirty_days_ago).count()
            recent_form2 = AlumniApplicationMini.objects.filter(created_at__gte=thirty_days_ago).count()
            recent_applications = recent_form1 + recent_form2

            # Get time-based statistics
            now = timezone.now()

            # Today's applications
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            today_form1 = AlumniApplication.objects.filter(created_at__gte=today_start).count()
            today_form2 = AlumniApplicationMini.objects.filter(created_at__gte=today_start).count()
            today_total = today_form1 + today_form2

            # Last 3 days
            three_days_ago = now - timedelta(days=3)
            three_days_form1 = AlumniApplication.objects.filter(created_at__gte=three_days_ago).count()
            three_days_form2 = AlumniApplicationMini.objects.filter(created_at__gte=three_days_ago).count()
            three_days_total = three_days_form1 + three_days_form2

            # Last week
            one_week_ago = now - timedelta(days=7)
            one_week_form1 = AlumniApplication.objects.filter(created_at__gte=one_week_ago).count()
            one_week_form2 = AlumniApplicationMini.objects.filter(created_at__gte=one_week_ago).count()
            one_week_total = one_week_form1 + one_week_form2

            # Last month (same as recent_applications)
            one_month_total = recent_applications

            time_based = {
                'today': today_total,
                'three_days': three_days_total,
                'one_week': one_week_total,
                'one_month': one_month_total
            }

            # Debug logging
            print(f"Statistics Debug:")
            print(f"  Total requests: {total_requests}")
            print(f"  Payment counts: {payment_counts}")
            print(f"  By payment status: {by_payment_status}")
            print(f"  Revenue info: {revenue_info}")
            print(f"  Time based: {time_based}")

            response_data = {
                'total_requests': total_requests,
                'form1_count': form1_count,
                'form2_count': form2_count,
                'by_status': by_status,
                'by_payment_status': by_payment_status,
                'recent_applications': recent_applications,
                'time_based': time_based,
                'revenue': revenue_info
            }

            return Response(response_data)

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch statistics: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
