from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Service
from .serializers import ServiceSerializer, PublicServiceSerializer
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

class IsStaffOrReadPublicOnly(permissions.BasePermission):
    """
    Custom permission to only allow staff users to edit services.
    Non-staff users can only view active services through the public endpoint.
    """
    def has_permission(self, request, view):
        # Allow all users to access the public list endpoint
        if view.action == 'public_list':
            return True

        # Staff users can access all actions
        return request.user.is_authenticated and request.user.is_staff

class ServiceViewSet(viewsets.ModelViewSet):
    """
    API endpoint for services.
    Staff users can perform all CRUD operations.
    Non-authenticated users can only view active services through the public_list endpoint.
    """
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    permission_classes = [IsStaffOrReadPublicOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['service_name', 'description']
    ordering_fields = ['service_name', 'service_fee', 'order', 'created_at']
    ordering = ['order', 'service_name']

    @action(detail=False, methods=['get'], url_path='public')
    def public_list(self, request):
        """
        Public endpoint to list active services.
        This endpoint is accessible without authentication.
        """
        services = Service.objects.filter(is_active=True).order_by('order', 'service_name')
        serializer = PublicServiceSerializer(services, many=True)
        return Response(serializer.data)
