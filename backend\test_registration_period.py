#!/usr/bin/env python
"""
Test script for registration period with year field
"""

import os
import sys
import django
from datetime import datetime, timed<PERSON><PERSON>

# Add the backend directory to the Python path
backend_path = os.path.dirname(__file__)
sys.path.insert(0, backend_path)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.registration_period.models import RegistrationPeriod
from setups.registration_period.serializers import RegistrationPeriodSerializer
from setups.year.models import Year
from setups.program.models import Program

def test_registration_period():
    """Test the registration period model with year field"""
    try:
        print("=== Testing Registration Period with Year Field ===")
        
        # Check existing registration periods
        periods = RegistrationPeriod.objects.all()
        print(f"Total registration periods: {periods.count()}")
        
        # Check available years
        years = Year.objects.all()
        print(f"Available years: {[y.year for y in years]}")
        
        # Check available programs
        programs = Program.objects.all()
        print(f"Available programs: {[p.program_name for p in programs]}")
        
        # Test serializer with existing data
        if periods.exists():
            print("\n=== Testing Serializer ===")
            for period in periods:
                print(f"\nPeriod ID: {period.id}")
                print(f"Program: {period.program.program_name if period.program else 'None'}")
                print(f"Year: {period.year.year if period.year else 'None'}")
                print(f"Open Date: {period.open_date}")
                print(f"Close Date: {period.close_date}")
                print(f"Is Active: {period.is_active}")
                
                # Test serializer
                serializer = RegistrationPeriodSerializer(period)
                print(f"Serialized data: {serializer.data}")
                print("---")
        
        # Test creating a new registration period (if we have data)
        if years.exists() and programs.exists():
            print("\n=== Testing Creation ===")
            year = years.first()
            program = programs.first()
            
            # Check if a period already exists for this program and year
            existing = RegistrationPeriod.objects.filter(program=program, year=year).first()
            if existing:
                print(f"Registration period already exists for {program.program_name} - {year.year}")
            else:
                print(f"Would create new period for {program.program_name} - {year.year}")
                # Don't actually create to avoid conflicts
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_registration_period()
