from django.core.management.base import BaseCommand
from setups.document_type.models import DocumentType

class Command(BaseCommand):
    help = 'Populate initial document types'

    def handle(self, *args, **options):
        document_types = [
            {
                'name': 'Birth Certificate',
                'description': 'Official birth certificate issued by government authorities'
            },
            {
                'name': 'National ID Card',
                'description': 'Government-issued national identification card'
            },
            {
                'name': 'Passport',
                'description': 'International travel document issued by government'
            },
            {
                'name': 'Driver\'s License',
                'description': 'Official license to operate motor vehicles'
            },
            {
                'name': 'Academic Transcript',
                'description': 'Official record of academic achievements and grades'
            },
            {
                'name': 'Diploma Certificate',
                'description': 'Official certificate of academic degree completion'
            },
            {
                'name': 'Medical Certificate',
                'description': 'Medical document certifying health status or fitness'
            },
            {
                'name': 'Police Clearance',
                'description': 'Certificate confirming absence of criminal record'
            },
            {
                'name': 'Employment Letter',
                'description': 'Official letter from employer confirming employment status'
            },
            {
                'name': 'Bank Statement',
                'description': 'Official financial statement from banking institution'
            }
        ]

        created_count = 0
        for doc_type_data in document_types:
            doc_type, created = DocumentType.objects.get_or_create(
                name=doc_type_data['name'],
                defaults={'description': doc_type_data['description']}
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created document type: {doc_type.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Document type already exists: {doc_type.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} new document types')
        )
