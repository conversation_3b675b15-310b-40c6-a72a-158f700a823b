/**
 * Refactored Menu Permission System - Permission-Only Access Control
 * 
 * This new system eliminates hardcoded group lists and uses ONLY Django permissions
 * for access control. Groups are still used for administrative convenience but
 * the frontend ignores group membership and only checks permissions.
 * 
 * Benefits:
 * - More scalable: No need to update frontend code when groups change
 * - More maintainable: Single source of truth (Django permissions)
 * - More flexible: Access control adapts automatically to permission changes
 */

import { User } from '@/contexts/AuthContext';
import { createPermissionChecker, AccessResult } from './permissionChecker';
import { MENU_PERMISSIONS as PERMISSION_MAPPINGS } from './permissionMappings';

/**
 * Check if user has access to a specific menu item
 * Uses ONLY permission-based checking, ignoring group membership
 */
export const hasMenuAccess = (user: User | null, menuKey: string): boolean => {
  const checker = createPermissionChecker(user);
  const result = checker.checkMenuAccess(menuKey);
  
  // Log access decision for debugging
  if (result.hasAccess) {
    console.log(`✅ Menu Access Granted: ${menuKey}`, {
      reason: result.reason,
      matchedPermissions: result.matchedPermissions
    });
  } else {
    console.log(`🔒 Menu Access Denied: ${menuKey}`, {
      reason: result.reason,
      requiredPermissions: PERMISSION_MAPPINGS[menuKey]?.permissions
    });
  }
  
  return result.hasAccess;
};

/**
 * Get detailed access information for a menu item
 */
export const getMenuAccessDetails = (user: User | null, menuKey: string): AccessResult => {
  const checker = createPermissionChecker(user);
  return checker.checkMenuAccess(menuKey);
};

/**
 * Get all menu items that the user can access
 */
export const getAccessibleMenus = (user: User | null): string[] => {
  const checker = createPermissionChecker(user);
  return checker.getAccessibleMenus();
};

/**
 * Check if user has access to any menu in a category
 */
export const hasCategoryAccess = (user: User | null, menuKeys: string[]): boolean => {
  return menuKeys.some(menuKey => hasMenuAccess(user, menuKey));
};

/**
 * Filter menu items based on user permissions
 */
export const filterMenusByAccess = (user: User | null, menuKeys: string[]): string[] => {
  return menuKeys.filter(menuKey => hasMenuAccess(user, menuKey));
};

/**
 * BACKWARD COMPATIBILITY: Legacy menu permission checking
 * This maintains compatibility with the old system while using the new permission-only logic
 */
export const checkMenuPermission = (user: User | null, menuKey: string): boolean => {
  return hasMenuAccess(user, menuKey);
};

/**
 * Menu categories for organization
 */
export const MENU_CATEGORIES = {
  APPLICANT: [
    'dashboard',
    'personal-information',
    'application',
    'application-status',
    'notifications'
  ],
  
  DASHBOARDS: [
    'graduation-dashboard',
    'application-dashboard',
    'service-fee-dashboard'
  ],
  
  GRADUATE_VERIFICATION: [
    'manage-graduates',
    'manage-colleges',
    'manage-departments',
    'graduate-fields-of-study',
    'manage-programs'
  ],
  
  APPLICATION_PORTAL: [
    'manage-colleges-app',
    'manage-departments-app',
    'manage-programs-app',
    'manage-study-programs',
    'manage-admission-types',
    'manage-registration-periods',
    'manage-fields-of-study-app',
    'manage-years',
    'manage-terms',
    'application-information',
    'manage-applicants'
  ],
  
  SERVICES: [
    'service-types',
    'document-types',
    'alumni-applications-service',
    'downloadable-content'
  ],
  
  OFFICIALS: [
    'certificate-types',
    'official-certificates'
  ],
  
  COMMUNICATION: [
    'announcements',
    'official-management'
  ],
  
  ADMINISTRATIVE: [
    'user-management',
    'general-settings'
  ],
  
  REPORTS: [
    'reports',
    'analytics'
  ]
};

/**
 * Get accessible menu categories for a user
 */
export const getAccessibleCategories = (user: User | null) => {
  const result: Record<string, string[]> = {};
  
  Object.entries(MENU_CATEGORIES).forEach(([category, menuKeys]) => {
    const accessibleMenus = filterMenusByAccess(user, menuKeys);
    if (accessibleMenus.length > 0) {
      result[category] = accessibleMenus;
    }
  });
  
  return result;
};

/**
 * Check if user has staff-level access (can access any admin menu)
 */
export const hasStaffAccess = (user: User | null): boolean => {
  if (!user) return false;
  if (user.is_superuser) return true;
  if (!user.is_staff) return false;
  
  // Check if user has access to any staff-level menu
  const staffMenus = [
    ...MENU_CATEGORIES.DASHBOARDS,
    ...MENU_CATEGORIES.GRADUATE_VERIFICATION,
    ...MENU_CATEGORIES.APPLICATION_PORTAL,
    ...MENU_CATEGORIES.SERVICES,
    ...MENU_CATEGORIES.OFFICIALS,
    ...MENU_CATEGORIES.COMMUNICATION,
    ...MENU_CATEGORIES.ADMINISTRATIVE,
    ...MENU_CATEGORIES.REPORTS
  ];
  
  return staffMenus.some(menuKey => hasMenuAccess(user, menuKey));
};

/**
 * Get user's access level based on their permissions
 */
export const getUserAccessLevel = (user: User | null): 'none' | 'applicant' | 'staff' | 'admin' | 'superuser' => {
  if (!user) return 'none';
  if (user.is_superuser) return 'superuser';
  
  const accessibleCategories = getAccessibleCategories(user);
  const categoryCount = Object.keys(accessibleCategories).length;
  
  if (categoryCount === 0) return 'none';
  if (categoryCount === 1 && accessibleCategories.APPLICANT) return 'applicant';
  if (accessibleCategories.ADMINISTRATIVE || accessibleCategories.REPORTS) return 'admin';
  if (categoryCount > 1) return 'staff';
  
  return 'applicant';
};

/**
 * Debug function to show user's menu access
 */
export const debugUserMenuAccess = (user: User | null): void => {
  if (!user) {
    console.log('🔍 Debug: No user provided');
    return;
  }
  
  console.log('🔍 Debug: User Menu Access Analysis', {
    user: user.username,
    isStaff: user.is_staff,
    isSuperuser: user.is_superuser,
    accessLevel: getUserAccessLevel(user),
    hasStaffAccess: hasStaffAccess(user)
  });
  
  const accessibleCategories = getAccessibleCategories(user);
  Object.entries(accessibleCategories).forEach(([category, menus]) => {
    console.log(`📂 ${category}:`, menus);
  });
  
  // Show some permission details
  const checker = createPermissionChecker(user);
  console.log('🔑 User Permissions:', user.permissions?.length || 0, 'permissions');
  console.log('👥 User Groups:', user.role_names?.length || 0, 'groups');
};

// Export the new system as default
export default {
  hasMenuAccess,
  getMenuAccessDetails,
  getAccessibleMenus,
  hasCategoryAccess,
  filterMenusByAccess,
  checkMenuPermission,
  MENU_CATEGORIES,
  getAccessibleCategories,
  hasStaffAccess,
  getUserAccessLevel,
  debugUserMenuAccess
};
