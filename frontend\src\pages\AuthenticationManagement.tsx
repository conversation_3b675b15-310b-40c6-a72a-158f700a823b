import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Shield, 
  Settings, 
  Activity, 
  Database,
  Lock,
  UserCheck,
  AlertTriangle,
  BarChart3,
  FileText,
  Download,
  Upload,
  RefreshCw,
  Layers,
  Grid
} from 'lucide-react';
import { useSimpleRBAC } from '@/contexts/SimpleRBACContext';
import UserManagement from '@/components/auth/UserManagement';
import GroupManagement from '@/components/auth/GroupManagement';
import PermissionManagement from '@/components/auth/PermissionManagement';
import AuthDashboard from '@/components/auth/AuthDashboard';
import DjangoAuthManagement from '@/components/auth/DjangoAuthManagement';

const AuthenticationManagement: React.FC = () => {
  // Simply render the Django Auth Management component
  return <DjangoAuthManagement />;

  // Check if user has access to authentication management
  if (!isSuperuser && !isStaff) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lock className="h-5 w-5" />
              <span>Access Denied</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              You don't have permission to access the authentication management system.
              Please contact your administrator.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'dashboard':
        return <BarChart3 className="h-4 w-4" />;
      case 'users':
        return <Users className="h-4 w-4" />;
      case 'groups':
        return <Shield className="h-4 w-4" />;
      case 'permissions':
        return <Database className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  const getTabDescription = (tab: string) => {
    switch (tab) {
      case 'dashboard':
        return 'Overview of authentication statistics and security events';
      case 'users':
        return 'Manage user accounts, roles, and permissions';
      case 'groups':
        return 'Manage user groups and group permissions';
      case 'permissions':
        return 'View and manage system permissions';
      case 'role-assignment':
        return 'Assign users to groups and manage role hierarchies';
      case 'user-profiles':
        return 'Manage detailed user profiles and personal information';
      case 'access-control':
        return 'Manage access control rules and security policies';
      case 'enhanced-roles':
        return 'Advanced role management for large-scale systems';
      case 'permission-matrix':
        return 'Visualize and manage complex permission relationships';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Authentication & Authorization</h1>
          <p className="text-muted-foreground">
            Comprehensive user and permission management system
          </p>
          {!isSuperuser && isStaff && (
            <div className="mt-2">
              <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Staff Access - Some advanced features may be limited
              </Badge>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isSuperuser ? "default" : "secondary"} className={isSuperuser ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}>
            <Shield className="h-3 w-3 mr-1" />
            {isSuperuser ? "Superuser Access" : "Staff Access"}
          </Badge>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh All
          </Button>
        </div>
      </div>

      {/* Staff Access Information */}
      {!isSuperuser && isStaff && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-yellow-800">
              <Shield className="h-5 w-5" />
              <span>Staff Access Level</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-yellow-800 mb-2">Available Features:</h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• View and manage users</li>
                  <li>• Assign users to groups</li>
                  <li>• View permissions and roles</li>
                  <li>• Access user profiles</li>
                  <li>• View authentication dashboard</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-yellow-800 mb-2">Limited Features:</h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Creating/deleting system roles</li>
                  <li>• Modifying core permissions</li>
                  <li>• Advanced security settings</li>
                  <li>• System-level configurations</li>
                  <li>• Bulk administrative operations</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('dashboard')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dashboard</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Analytics</div>
            <p className="text-xs text-muted-foreground">
              View statistics and security events
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('users')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">User Management</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Users</div>
            <p className="text-xs text-muted-foreground">
              Create, edit, and manage user accounts
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('groups')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Group Management</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Groups</div>
            <p className="text-xs text-muted-foreground">
              Manage user groups and roles
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('permissions')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permissions</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Permissions</div>
            <p className="text-xs text-muted-foreground">
              View and manage system permissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Additional Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('role-assignment')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Role Assignment</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Roles</div>
            <p className="text-xs text-muted-foreground">
              Assign users to groups and manage role hierarchies
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('user-profiles')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">User Profiles</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Profiles</div>
            <p className="text-xs text-muted-foreground">
              Manage detailed user profiles and information
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('access-control')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Access Control</CardTitle>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Security</div>
            <p className="text-xs text-muted-foreground">
              Manage access control rules and security policies
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Features for Large-Scale Systems */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('enhanced-roles')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Enhanced Role Management</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Advanced</div>
            <p className="text-xs text-muted-foreground">
              Hierarchical roles with context-based permissions for large user bases
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('permission-matrix')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permission Matrix</CardTitle>
            <Grid className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Matrix</div>
            <p className="text-xs text-muted-foreground">
              Visualize and manage complex role-permission relationships
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="space-y-2">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="dashboard" className="flex items-center space-x-2">
              {getTabIcon('dashboard')}
              <span>Dashboard</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center space-x-2">
              {getTabIcon('users')}
              <span>Users</span>
            </TabsTrigger>
            <TabsTrigger value="groups" className="flex items-center space-x-2">
              {getTabIcon('groups')}
              <span>Groups</span>
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center space-x-2">
              {getTabIcon('permissions')}
              <span>Permissions</span>
            </TabsTrigger>
          </TabsList>

          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="role-assignment" className="flex items-center space-x-2">
              <UserCheck className="h-4 w-4" />
              <span>Role Assignment</span>
            </TabsTrigger>
            <TabsTrigger value="user-profiles" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>User Profiles</span>
            </TabsTrigger>
            <TabsTrigger value="access-control" className="flex items-center space-x-2">
              <Lock className="h-4 w-4" />
              <span>Access Control</span>
            </TabsTrigger>
          </TabsList>

          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="enhanced-roles" className="flex items-center space-x-2">
              <Layers className="h-4 w-4" />
              <span>Enhanced Roles</span>
            </TabsTrigger>
            <TabsTrigger value="permission-matrix" className="flex items-center space-x-2">
              <Grid className="h-4 w-4" />
              <span>Permission Matrix</span>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Tab Descriptions */}
        <Card className="bg-muted/50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              {getTabIcon(activeTab)}
              <span className="text-sm text-muted-foreground">
                {getTabDescription(activeTab)}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-4">
          <AuthDashboard />
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          <UserManagement />
        </TabsContent>

        {/* Groups Tab */}
        <TabsContent value="groups" className="space-y-4">
          <GroupManagement />
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-4">
          <PermissionManagement />
        </TabsContent>

        {/* Role Assignment Tab */}
        <TabsContent value="role-assignment" className="space-y-4">
          <RoleAssignmentInterface />
        </TabsContent>

        {/* User Profiles Tab */}
        <TabsContent value="user-profiles" className="space-y-4">
          <UserProfileManagement />
        </TabsContent>

        {/* Access Control Tab */}
        <TabsContent value="access-control" className="space-y-4">
          <AccessControlInterface />
        </TabsContent>

        {/* Enhanced Role Management Tab */}
        <TabsContent value="enhanced-roles" className="space-y-4">
          <EnhancedRoleManagement />
        </TabsContent>

        {/* Permission Matrix Tab */}
        <TabsContent value="permission-matrix" className="space-y-4">
          <PermissionMatrix />
        </TabsContent>
      </Tabs>

      {/* Footer Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>System Actions</span>
          </CardTitle>
          <CardDescription>
            Administrative actions and system utilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Users
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Groups
            </Button>
            <Button variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button variant="outline" size="sm">
              <Activity className="h-4 w-4 mr-2" />
              View Audit Log
            </Button>
            {isSuperuser && (
              <>
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Import Users
                </Button>
                <Button variant="outline" size="sm">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Security Settings
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthenticationManagement;
