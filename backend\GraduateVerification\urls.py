from django.urls import path
from . import views

urlpatterns = [
    # Graduate Verification endpoints
    path('graduate-verifications/', views.GraduateVerificationList.as_view(), name='graduate-verification-list'),
    path('graduate-verifications/<int:pk>/', views.GraduateVerificationDetail.as_view(), name='graduate-verification-detail'),
    path('graduate-verifications/delete/<int:pk>/', views.GraduateVerificationDelete.as_view(), name='graduate-verification-delete'),
    path('graduate-verifications/soft-delete/<int:pk>/', views.GraduateVerificationSoftDelete.as_view(), name='graduate-verification-soft-delete'),
    path('graduate-verifications/restore/<int:pk>/', views.GraduateVerificationRestore.as_view(), name='graduate-verification-restore'),
    path('graduate-verifications/deleted/', views.GraduateVerificationDeletedList.as_view(), name='graduate-verification-deleted-list'),
    path('graduate-verifications/deleted-statistics/', views.DeletedGraduateStatisticsView.as_view(), name='deleted-graduate-statistics'),
    path('graduate-verifications/public/', views.PublicGraduateVerificationList.as_view(), name='public-graduate-verification-list'),
    path('graduate-verifications/filter/', views.GraduateVerificationFilter.as_view(), name='graduate-verification-filter'),

    # CSV Import endpoint
    path('graduate-verifications/import-csv/', views.GraduateVerificationCSVImport.as_view(), name='graduate-verification-import-csv'),

    # Recent graduates endpoint
    path('graduate-verifications/recent/', views.RecentGraduatesView.as_view(), name='recent-graduates'),

    # Report endpoints
    path('graduate-verifications/report/by-department/', views.GraduateVerificationReportByDepartment.as_view(), name='graduate-verification-report-by-department'),
    path('graduate-verifications/report/by-college/', views.GraduateVerificationReportByCollege.as_view(), name='graduate-verification-report-by-college'),
    path('graduate-verifications/report/by-gender/', views.GraduateVerificationReportByGender.as_view(), name='graduate-verification-report-by-gender'),
    path('graduate-verifications/report/by-year/', views.GraduateVerificationReportByYear.as_view(), name='graduate-verification-report-by-year'),
    path('graduate-verifications/report/gender-trends-by-year/', views.GraduateVerificationGenderTrendsByYear.as_view(), name='graduate-verification-gender-trends-by-year'),

    path('graduate-verifications/report/program-gpa-trends/', views.GraduateVerificationProgramGPATrends.as_view(), name='graduate-verification-program-gpa-trends'),
    path('graduate-verifications/report/by-program/', views.GraduateVerificationReportByProgram.as_view(), name='graduate-verification-report-by-program'),
    path('graduate-verifications/report/by-admission-classification/', views.GraduateVerificationReportByAdmissionClassification.as_view(), name='graduate-verification-report-by-admission-classification'),
    path('graduate-verifications/report/gpa-stats/', views.GraduateVerificationReportGPAStats.as_view(), name='graduate-verification-report-gpa-stats'),
    path('graduate-verifications/debug/', views.GraduateStudentDebug.as_view(), name='graduate-student-debug'),
    path('graduate-verifications/available-years/', views.AvailableYearsView.as_view(), name='available-years'),
    path('graduate-verifications/available-colleges/', views.AvailableCollegesView.as_view(), name='available-colleges'),

    # VerificationCollege endpoints
    path('verification-colleges/', views.CollegeList.as_view(), name='verification-college-list'),
    path('verification-colleges/<int:pk>/', views.CollegeDetail.as_view(), name='verification-college-detail'),
    path('verification-colleges/public/', views.PublicCollegeList.as_view(), name='public-verification-college-list'),

    # VerificationDepartment endpoints
    path('verification-departments/', views.DepartmentList.as_view(), name='verification-department-list'),
    path('verification-departments/<int:pk>/', views.DepartmentDetail.as_view(), name='verification-department-detail'),
    path('verification-departments/public/', views.PublicDepartmentList.as_view(), name='public-verification-department-list'),

    # VerificationFieldOfStudy endpoints
    path('verification-fields-of-study/', views.FieldOfStudyList.as_view(), name='verification-field-of-study-list'),
    path('verification-fields-of-study/<int:pk>/', views.FieldOfStudyDetail.as_view(), name='verification-field-of-study-detail'),
    path('verification-fields-of-study/public/', views.PublicFieldOfStudyList.as_view(), name='public-verification-field-of-study-list'),

    # VerificationProgram endpoints
    path('verification-programs/', views.ProgramList.as_view(), name='verification-program-list'),
    path('verification-programs/<int:pk>/', views.ProgramDetail.as_view(), name='verification-program-detail'),
    path('verification-programs/public/', views.PublicProgramList.as_view(), name='public-verification-program-list'),

    # Admission Classification endpoints
    path('admission-classifications/', views.AdmissionClassificationList.as_view(), name='admission-classification-list'),
    path('admission-classifications/<int:pk>/', views.AdmissionClassificationDetail.as_view(), name='admission-classification-detail'),
    path('admission-classifications/public/', views.PublicAdmissionClassificationList.as_view(), name='public-admission-classification-list'),
]