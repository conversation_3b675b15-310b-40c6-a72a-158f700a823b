# Generated by Django 5.2.3 on 2025-06-15 21:01

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alumni_applications', '0003_rename_models'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='applicationdocument',
            name='alumni_appl_applica_d7b047_idx',
        ),
        migrations.RemoveIndex(
            model_name='applicationdocument',
            name='alumni_appl_applica_257a1b_idx',
        ),
        migrations.RemoveField(
            model_name='applicationdocument',
            name='document_type',
        ),
        migrations.AddField(
            model_name='applicationdocument',
            name='document_type_name',
            field=models.CharField(default='Unknown Document', help_text='Name of the document type (must match one from service type requirements)', max_length=100),
            preserve_default=False,
        ),
        migrations.AddIndex(
            model_name='applicationdocument',
            index=models.Index(fields=['application_form1', 'document_type_name'], name='alumni_appl_applica_28526e_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdocument',
            index=models.Index(fields=['application_form2', 'document_type_name'], name='alumni_appl_applica_d9196b_idx'),
        ),
    ]
