# Generated by Django 5.2.1 on 2025-05-31 15:54

import django.db.models.deletion
from django.db import migrations, models


def convert_year_to_foreign_key(apps, schema_editor):
    """
    Convert year field from PositiveIntegerField to ForeignKey to Year model
    """
    ApplicantProgramSelection = apps.get_model('registration', 'ApplicantProgramSelection')
    Year = apps.get_model('year', 'Year')
    
    # Get the default year (should be the only one that exists)
    try:
        default_year = Year.objects.first()
        if not default_year:
            # Create a default year if none exists
            default_year = Year.objects.create(year='2024/2025')
        
        # Update all existing ApplicantProgramSelection records to use the default year
        ApplicantProgramSelection.objects.all().update(year_temp=default_year.uuid)
        
    except Exception as e:
        print(f"Error during year conversion: {e}")
        # If there's an error, we'll let the migration continue
        # The new field will be nullable temporarily


def reverse_year_conversion(apps, schema_editor):
    """
    Reverse the conversion - convert back to integer field
    """
    ApplicantProgramSelection = apps.get_model('registration', 'ApplicantProgramSelection')
    
    # Convert all foreign key references back to the year value as integer
    for selection in ApplicantProgramSelection.objects.all():
        if selection.year_temp:
            # Extract year from the year string and convert to integer
            year_str = selection.year_temp.year
            # Try to extract the first year from formats like "2024/2025"
            try:
                year_int = int(year_str.split('/')[0])
                selection.year = year_int
                selection.save()
            except (ValueError, AttributeError):
                # If conversion fails, use current year
                from datetime import datetime
                selection.year = datetime.now().year
                selection.save()


class Migration(migrations.Migration):

    dependencies = [
        ('registration', '0001_initial'),
        ('year', '0001_initial'),
    ]

    operations = [
        # Step 1: Add a temporary field for the new foreign key
        migrations.AddField(
            model_name='applicantprogramselection',
            name='year_temp',
            field=models.ForeignKey(
                null=True, 
                blank=True,
                on_delete=django.db.models.deletion.CASCADE, 
                related_name='applicant_program_selections_temp', 
                to='year.year'
            ),
        ),
        
        # Step 2: Populate the temporary field with data
        migrations.RunPython(
            convert_year_to_foreign_key,
            reverse_year_conversion
        ),
        
        # Step 3: Remove the old year field
        migrations.RemoveField(
            model_name='applicantprogramselection',
            name='year',
        ),
        
        # Step 4: Rename the temporary field to year
        migrations.RenameField(
            model_name='applicantprogramselection',
            old_name='year_temp',
            new_name='year',
        ),
        
        # Step 5: Make the field non-nullable
        migrations.AlterField(
            model_name='applicantprogramselection',
            name='year',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, 
                related_name='applicant_program_selections', 
                to='year.year'
            ),
        ),
    ]
