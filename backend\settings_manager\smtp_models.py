from django.db import models
from django.core.exceptions import ValidationError
from django.conf import settings
import os


class SingletonModel(models.Model):
    """
    Abstract base class for singleton models.
    """
    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.pk = 1
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        pass

    @classmethod
    def load(cls):
        obj, created = cls.objects.get_or_create(pk=1)
        return obj


class SMTPSettings(SingletonModel):
    """
    SMTP server settings for email functionality.
    """

    # Provider choices
    PROVIDER_CHOICES = [
        ('custom', 'Custom SMTP'),
        ('gmail', 'Gmail'),
        ('office365', 'Office 365'),
    ]

    provider = models.CharField(max_length=20, choices=PROVIDER_CHOICES, default='custom')
    host = models.CharField(max_length=255, blank=True, default='')
    port = models.IntegerField(default=587)
    username = models.CharField(max_length=255, blank=True, default='')
    password = models.CharField(max_length=255, blank=True, default='')
    from_email = models.EmailField(blank=True, default='')
    use_tls = models.BooleanField(default=True)
    use_ssl = models.BooleanField(default=False)
    timeout = models.IntegerField(default=60)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"SMTP Settings ({self.get_provider_display()}: {self.host}:{self.port})"

    def clean(self):
        """Validate SMTP settings"""
        if self.use_tls and self.use_ssl:
            raise ValidationError("Cannot use both TLS and SSL simultaneously")

        if self.port < 1 or self.port > 65535:
            raise ValidationError("Port must be between 1 and 65535")

        if self.timeout < 1:
            raise ValidationError("Timeout must be at least 1 second")

    def get_provider_config(self):
        """Get provider-specific default configuration"""
        configs = {
            'gmail': {
                'host': 'smtp.gmail.com',
                'port': 587,
                'use_tls': True,
                'use_ssl': False,
                'timeout': 60,
            },
            'office365': {
                'host': 'smtp.office365.com',
                'port': 587,
                'use_tls': True,
                'use_ssl': False,
                'timeout': 60,
            },
            'custom': {
                'host': '',
                'port': 587,
                'use_tls': True,
                'use_ssl': False,
                'timeout': 60,
            }
        }
        return configs.get(self.provider, configs['custom'])

    def apply_provider_defaults(self):
        """Apply provider-specific default settings"""
        if self.provider in ['gmail', 'office365']:
            config = self.get_provider_config()
            self.host = config['host']
            self.port = config['port']
            self.use_tls = config['use_tls']
            self.use_ssl = config['use_ssl']
            self.timeout = config['timeout']

    def get_provider_instructions(self):
        """Get provider-specific setup instructions"""
        instructions = {
            'gmail': {
                'title': 'Gmail SMTP Setup',
                'steps': [
                    'Enable 2-Factor Authentication on your Gmail account',
                    'Generate an App Password for this application',
                    'Use your Gmail address as username',
                    'Use the App Password (not your regular password)',
                    'Ensure "Less secure app access" is enabled if not using App Password'
                ],
                'notes': 'Gmail requires App Passwords for enhanced security'
            },
            'office365': {
                'title': 'Office 365 SMTP Setup',
                'steps': [
                    'Use your full Office 365 email address as username',
                    'Use your Office 365 password or App Password',
                    'Ensure SMTP authentication is enabled in your Office 365 admin',
                    'Check that your account has permission to send via SMTP',
                    'Consider using App Passwords for enhanced security'
                ],
                'notes': 'Office 365 may require modern authentication for some accounts'
            },
            'custom': {
                'title': 'Custom SMTP Setup',
                'steps': [
                    'Enter your SMTP server hostname',
                    'Configure the appropriate port (587 for TLS, 465 for SSL, 25 for plain)',
                    'Set up authentication credentials',
                    'Choose appropriate security settings (TLS/SSL)',
                    'Test the configuration before saving'
                ],
                'notes': 'Contact your email provider for specific SMTP settings'
            }
        }
        return instructions.get(self.provider, instructions['custom'])

    def apply_to_settings(self):
        """
        Apply these settings to Django's settings module.
        """
        settings.EMAIL_HOST = self.host
        settings.EMAIL_PORT = self.port
        settings.EMAIL_HOST_USER = self.username
        settings.EMAIL_HOST_PASSWORD = self.password
        settings.EMAIL_USE_TLS = self.use_tls
        settings.EMAIL_USE_SSL = self.use_ssl
        settings.DEFAULT_FROM_EMAIL = self.from_email
        settings.EMAIL_TIMEOUT = self.timeout

    def get_email_backend_config(self):
        """Get email backend configuration dictionary"""
        if self.host:
            return {
                'EMAIL_BACKEND': 'django.core.mail.backends.smtp.EmailBackend',
                'EMAIL_HOST': self.host,
                'EMAIL_PORT': self.port,
                'EMAIL_HOST_USER': self.username,
                'EMAIL_HOST_PASSWORD': self.password,
                'EMAIL_USE_TLS': self.use_tls,
                'EMAIL_USE_SSL': self.use_ssl,
                'EMAIL_TIMEOUT': self.timeout,
                'DEFAULT_FROM_EMAIL': self.from_email,
                'SERVER_EMAIL': self.from_email,
            }
        else:
            return {
                'EMAIL_BACKEND': 'django.core.mail.backends.console.EmailBackend',
            }
