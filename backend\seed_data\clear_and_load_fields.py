import os
import json
import django
import sys
import random
import string

# Add the project root directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models
from GraduateVerification.models import (
    VerificationDepartment,
    VerificationFieldOfStudy
)

def generate_unique_code(name, used_codes, department_code):
    """Generate a unique code for a field of study"""
    # Start with a simple approach: first letter of each word
    name_parts = name.split()
    if len(name_parts) == 1:
        # If name is a single word, use the first 4 letters
        base_code = name_parts[0][:4].upper()
    else:
        # If name has multiple words, use the first letter of each word
        base_code = ''.join(word[0] for word in name_parts).upper()

        # If code is too short, add more letters from the first word
        if len(base_code) < 2:
            base_code = name_parts[0][:4].upper()

    # Add department code prefix to make it more unique
    code = f"{department_code[:2]}_{base_code}"

    # If the code is already used, add a random suffix
    original_code = code
    counter = 1
    while code in used_codes:
        code = f"{original_code}{counter}"
        counter += 1

    return code

def clear_and_load_fields():
    print("Clearing existing fields of study...")
    VerificationFieldOfStudy.objects.all().delete()

    print("Loading fields of study with unique codes...")
    with open('seed_data/verification_fields_of_study.json', 'r', encoding='utf-8') as f:
        fields_data = json.load(f)

    used_codes = set()

    for field_data in fields_data:
        department = VerificationDepartment.objects.get(code=field_data['department_code'])

        # Generate a unique code
        code = generate_unique_code(field_data['name'], used_codes, department.code)
        used_codes.add(code)

        # Create the field of study
        VerificationFieldOfStudy.objects.create(
            name=field_data['name'],
            code=code,
            department=department
        )

    print(f"Loaded {len(fields_data)} fields of study with unique codes")

if __name__ == '__main__':
    clear_and_load_fields()
