
import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { User, LogOut, Home, Menu, X, Package, Download } from 'lucide-react';
import HeaderNav from './HeaderNav';
import Footer from './Footer';
import BackToTop from './BackToTop';
import { settingsAPI } from '@/services/api';



interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const isHomePage = location.pathname === '/';
  const isDashboardPage = location.pathname.startsWith('/dashboard') ||
                         location.pathname.startsWith('/graduate-admin') ||
                         location.pathname.startsWith('/application') ||
                         location.pathname.startsWith('/personal-information') ||
                         location.pathname.startsWith('/notifications') ||
                         location.pathname.startsWith('/status') ||
                         location.pathname.startsWith('/rbac-test') ||
                         location.pathname.startsWith('/role-management') ||
                         location.pathname.startsWith('/user-management') ||
                         location.pathname.startsWith('/system-settings') ||
                         location.pathname.startsWith('/department-management') ||
                         location.pathname.startsWith('/faculty-management');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [settings, setSettings] = useState<{
    systemName: string;
    organizationName: string;
    primary_color?: string;
    secondary_color?: string;
    header_logo_url?: string;
  }>({
    systemName: '',
    organizationName: '',
    primary_color: '',
    secondary_color: '',
    header_logo_url: ''
  });

  const [isLoading, setIsLoading] = useState(true);

  // Fetch organization settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          // Get header logo URL and ensure it's an absolute URL
          let headerLogoUrl = '';
          if (response.data.header_logo_url) {
            headerLogoUrl = response.data.header_logo_url.startsWith('http')
              ? response.data.header_logo_url
              : `http://localhost:8000${response.data.header_logo_url}`;
          }

          setSettings({
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || '',
            primary_color: response.data.primary_color || '#1a73c0',
            secondary_color: response.data.secondary_color || '#f5f5f5',
            header_logo_url: headerLogoUrl
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Get user data and check if user is staff
  const userDataString = localStorage.getItem('user');
  const userData = userDataString ? JSON.parse(userDataString) : null;
  const isStaff = userData?.is_staff === true || userData?.is_superuser === true;

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('user');
    window.location.href = '/login';
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-[#1a73c0] text-white shadow-lg overflow-x-hidden" style={{ backgroundColor: 'var(--brand-primary, #1a73c0)' }}>
        <div className="container mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center overflow-hidden">
          <div className="flex items-center space-x-4">
            <Link to="/" className="flex items-center space-x-3 group">
              {settings.header_logo_url ? (
                <div className="flex items-center space-x-3">
                  <img
                    src={settings.header_logo_url}
                    alt={`${settings.organizationName} Logo`}
                    className="h-10 bg-white p-1 rounded"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  <div className="flex flex-col">
                    {isLoading ? (
                      <>
                        <div className="h-7 w-48 bg-white/20 animate-pulse rounded"></div>
                        <div className="h-4 w-36 bg-white/10 animate-pulse rounded mt-1"></div>
                      </>
                    ) : (
                      <>
                        <h1 className="text-xl font-heading font-bold tracking-tight text-white">{settings.organizationName}</h1>
                        <span className="text-xs text-white/80 font-medium">{settings.systemName}</span>
                      </>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col">
                  {isLoading ? (
                    <>
                      <div className="h-7 w-48 bg-white/20 animate-pulse rounded"></div>
                      <div className="h-4 w-36 bg-white/10 animate-pulse rounded mt-1"></div>
                    </>
                  ) : (
                    <>
                      <h1 className="text-xl font-heading font-bold tracking-tight text-white">{settings.organizationName}</h1>
                      <span className="text-xs text-white/80 font-medium">{settings.systemName}</span>
                    </>
                  )}
                </div>
              )}
            </Link>

            {/* Desktop Navigation */}
            <HeaderNav />
          </div>

          <div className="flex items-center space-x-3">
            {/* Mobile menu button */}
            <button
              className="lg:hidden p-2 rounded-md hover:bg-[#0e4a7d] transition-colors text-white"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label="Toggle menu"
            >
              {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>

            {isAuthenticated && !isDashboardPage ? (
              <>
                {/* Only show My Account button for non-staff users */}
                {!isStaff && (
                  <Link to="/dashboard">
                    <Button
                      variant="outline"
                      className="text-[#1a73c0] bg-white hover:bg-gray-100 border-white hover:border-white transition-all"
                    >
                      <User className="h-4 w-4 mr-2" />
                      <span className="hidden md:inline">My Account</span>
                    </Button>
                  </Link>
                )}
                <Button
                  variant="ghost"
                  className="text-white hover:bg-[#0e4a7d] transition-all"
                  onClick={handleLogout}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  <span className="hidden md:inline">Logout</span>
                </Button>
                {/* Show Dashboard button for staff users */}
                {isStaff && (
                  <Link to="/graduate-admin">
                    <Button
                      variant="outline"
                      className="text-[#1a73c0] bg-white hover:bg-gray-100 border-white hover:border-white transition-all"
                    >
                      <User className="h-4 w-4 mr-2" />
                      <span className="hidden md:inline">Dashboard</span>
                    </Button>
                  </Link>
                )}
              </>
            ) : !isAuthenticated && (
              !isHomePage && (
                <>
                  <Link to="/login">
                    <Button
                      variant="ghost"
                      className="text-white hover:bg-[#0e4a7d] transition-all font-medium"
                    >
                      Sign In
                    </Button>
                  </Link>
                  <Link to="/register">
                    <Button
                      variant="outline"
                      className="text-[#1a73c0] bg-white hover:bg-gray-100 border-white transition-all font-medium"
                    >
                      Register
                    </Button>
                  </Link>
                </>
              )
            )}
          </div>
        </div>

        {/* Mobile Navigation Menu */}
{mobileMenuOpen && (
          <div className="lg:hidden border-t border-[#0e4a7d]/20 animate-fade-in">
            <nav className="container mx-auto px-4">
              <ul className="py-3 space-y-1">
                <li>
                  <Link
                    to="/"
                    className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors ${location.pathname === '/' ? 'bg-[#0e4a7d] text-white' : 'text-white/90 hover:bg-[#0e4a7d] hover:text-white'}`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <Home className="h-4 w-4 mr-3" />
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    to="/programs"
                    className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors ${location.pathname.startsWith('/programs') ? 'bg-[#0e4a7d] text-white' : 'text-white/90 hover:bg-[#0e4a7d] hover:text-white'}`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <svg className="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    Programs
                  </Link>
                </li>
                <li>
                  <Link
                    to="/graduate-verification"
                    className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors ${location.pathname.startsWith('/graduate-verification') ? 'bg-[#0e4a7d] text-white' : 'text-white/90 hover:bg-[#0e4a7d] hover:text-white'}`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <svg className="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    Verify Graduate
                  </Link>
                </li>

                <li>
                  <Link
                    to="/services"
                    className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors ${location.pathname.startsWith('/services') ? 'bg-[#0e4a7d] text-white' : 'text-white/90 hover:bg-[#0e4a7d] hover:text-white'}`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <svg className="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    Services
                  </Link>
                </li>

                <li>
                  <Link
                    to="/track-official"
                    className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors ${location.pathname.startsWith('/track-official') ? 'bg-[#0e4a7d] text-white' : 'text-white/90 hover:bg-[#0e4a7d] hover:text-white'}`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <Package className="h-4 w-4 mr-3" />
                    Track Official
                  </Link>
                </li>
                <li>
                  <Link
                    to="/downloads"
                    className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors ${location.pathname.startsWith('/downloads') ? 'bg-[#0e4a7d] text-white' : 'text-white/90 hover:bg-[#0e4a7d] hover:text-white'}`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <Download className="h-4 w-4 mr-3" />
                    Downloads
                  </Link>
                </li>
              </ul>
            </nav>
          </div>
        )}
      </header>

      <main className="flex-1 bg-gray-50 flex flex-col overflow-x-hidden">
        <div className="flex-1 overflow-y-auto">
          {children}
        </div>
      </main>

      <Footer />

      {/* Show back to top button for all users on public-facing pages */}
      <BackToTop />
    </div>
  );
};

export default Layout;
