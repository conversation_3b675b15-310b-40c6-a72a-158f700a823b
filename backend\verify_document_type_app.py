#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to verify the document_type app is working correctly.
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.document_type.models import DocumentType
from setups.document_type.serializers import DocumentTypeSerializer

def main():
    print("=" * 60)
    print("DOCUMENT TYPE APP VERIFICATION")
    print("=" * 60)
    
    # Test 1: Check if model is accessible
    try:
        print("\n1. Testing Model Access...")
        count = DocumentType.objects.count()
        print(f"   ✓ DocumentType model accessible")
        print(f"   ✓ Found {count} document types in database")
    except Exception as e:
        print(f"   ✗ Error accessing model: {e}")
        return False
    
    # Test 2: Test model creation
    try:
        print("\n2. Testing Model Creation...")
        test_doc = DocumentType.objects.create(
            name="Test Document Type",
            description="This is a test document type"
        )
        print(f"   ✓ Created test document type: {test_doc}")
        
        # Clean up
        test_doc.delete()
        print("   ✓ Test document type deleted")
    except Exception as e:
        print(f"   ✗ Error creating model: {e}")
        return False
    
    # Test 3: Test serializer
    try:
        print("\n3. Testing Serializer...")
        doc_types = DocumentType.objects.all()[:3]
        serializer = DocumentTypeSerializer(doc_types, many=True)
        data = serializer.data
        print(f"   ✓ Serializer working correctly")
        print(f"   ✓ Serialized {len(data)} document types")
        if data:
            print(f"   ✓ Sample data: {data[0]['name']}")
    except Exception as e:
        print(f"   ✗ Error with serializer: {e}")
        return False
    
    # Test 4: Test validation
    try:
        print("\n4. Testing Model Validation...")
        # Test duplicate name validation
        existing_doc = DocumentType.objects.first()
        if existing_doc:
            try:
                duplicate = DocumentType(name=existing_doc.name)
                duplicate.full_clean()
                print("   ✗ Duplicate validation failed")
                return False
            except django.core.exceptions.ValidationError:
                print("   ✓ Duplicate name validation working")
        
        # Test empty name validation
        try:
            empty_doc = DocumentType(name="")
            empty_doc.full_clean()
            print("   ✗ Empty name validation failed")
            return False
        except django.core.exceptions.ValidationError:
            print("   ✓ Empty name validation working")
            
    except Exception as e:
        print(f"   ✗ Error with validation: {e}")
        return False
    
    # Test 5: List all document types
    try:
        print("\n5. Current Document Types in Database:")
        doc_types = DocumentType.objects.all().order_by('name')
        for i, doc_type in enumerate(doc_types, 1):
            status = "Active" if doc_type.is_active else "Inactive"
            print(f"   {i:2d}. {doc_type.name} ({status})")
            if doc_type.description:
                print(f"       Description: {doc_type.description[:50]}...")
    except Exception as e:
        print(f"   ✗ Error listing document types: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✓ ALL TESTS PASSED - DOCUMENT TYPE APP IS WORKING CORRECTLY!")
    print("=" * 60)
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
