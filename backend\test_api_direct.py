#!/usr/bin/env python
"""
Test the Service Type API directly to check if it's working.
"""
import os
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def get_jwt_token():
    """Get JWT token for API testing."""
    try:
        # Get or create test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
        
        # Generate JWT token
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        return access_token
    except Exception as e:
        print(f"Error getting JWT token: {e}")
        return None

def test_service_type_api():
    """Test the Service Type API endpoints."""
    print("Testing Service Type API...")
    
    # Get JWT token
    token = get_jwt_token()
    if not token:
        print("❌ Failed to get JWT token")
        return
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000/api'
    
    # Test 1: List service types
    print("\n1. Testing GET /api/service-types/")
    try:
        response = requests.get(f'{base_url}/service-types/', headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data)} service types")
            if data:
                print(f"First service type: {data[0].get('name', 'Unknown')}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Create a service type
    print("\n2. Testing POST /api/service-types/")
    try:
        test_data = {
            'name': 'API Test Service',
            'fee': 15.50,
            'is_active': True,
            'document_type_ids': []
        }
        
        response = requests.post(
            f'{base_url}/service-types/',
            headers=headers,
            data=json.dumps(test_data)
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Created service type: {data.get('name', 'Unknown')}")
            service_id = data.get('id')
            
            # Test 3: Update the service type
            print(f"\n3. Testing PUT /api/service-types/{service_id}/")
            update_data = {
                'name': 'Updated API Test Service',
                'fee': 20.00,
                'is_active': True,
                'document_type_ids': []
            }
            
            response = requests.put(
                f'{base_url}/service-types/{service_id}/',
                headers=headers,
                data=json.dumps(update_data)
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Updated service type: {data.get('name', 'Unknown')}")
            else:
                print(f"❌ Update error: {response.text}")
            
            # Test 4: Delete the service type
            print(f"\n4. Testing DELETE /api/service-types/{service_id}/")
            response = requests.delete(f'{base_url}/service-types/{service_id}/', headers=headers)
            print(f"Status: {response.status_code}")
            if response.status_code == 204:
                print("✅ Service type deleted successfully")
            else:
                print(f"❌ Delete error: {response.text}")
                
        else:
            print(f"❌ Create error: {response.text}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("API TESTING COMPLETED")
    print("=" * 50)

if __name__ == '__main__':
    test_service_type_api()
