#!/usr/bin/env python
"""
Verify audit trail implementation
"""
import os
import sys
import django
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

def verify_implementation():
    """Verify audit trail implementation"""
    print("🔍 Verifying Audit Trail Implementation")
    print("=" * 50)
    
    try:
        # Test database columns
        print("1. Checking database columns...")
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'GraduateVerification_graduatestudent' 
                AND column_name IN ('created_by_id', 'updated_by_id');
            """)
            columns = [row[0] for row in cursor.fetchall()]
            
            if len(columns) == 2:
                print("   ✓ Database columns exist")
            else:
                print(f"   ✗ Missing columns. Found: {columns}")
                return False
        
        # Test model fields
        print("2. Checking model fields...")
        from GraduateVerification.models import GraduateStudent
        model_fields = [field.name for field in GraduateStudent._meta.fields]
        
        if 'created_by' in model_fields and 'updated_by' in model_fields:
            print("   ✓ Model fields exist")
        else:
            print("   ✗ Model fields missing")
            return False
        
        # Test queries
        print("3. Testing database queries...")
        total = GraduateStudent.objects.count()
        audit_query = GraduateStudent.objects.select_related('created_by', 'updated_by').count()
        print(f"   ✓ Queries successful: {total} total, {audit_query} with audit")
        
        print("\n🎉 Audit trail implementation verified successfully!")
        print("\n💡 Next steps:")
        print("1. Restart Django server")
        print("2. Test Recent Graduates Dashboard")
        print("3. Import CSV files - audit fields will be null")
        print("4. Create/update records - audit fields will be populated")
        
        return True
        
    except Exception as e:
        print(f"✗ Verification failed: {e}")
        return False

if __name__ == '__main__':
    verify_implementation()
