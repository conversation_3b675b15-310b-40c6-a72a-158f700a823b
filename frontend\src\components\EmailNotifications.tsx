import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { format } from 'date-fns';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Mail,
  Plus,
  Trash,
  Edit,
  Eye,
  Send,
  Calendar,
  BarChart3,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Search,
  Filter,
  RefreshCw
} from 'lucide-react';
import communicationAPI, { EmailNotification } from '@/services/communicationAPI';
import { cn } from '@/lib/utils';

const EmailNotifications = () => {
  const [emails, setEmails] = useState<EmailNotification[]>([]);
  const [selectedEmail, setSelectedEmail] = useState<EmailNotification | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedEmails, setSelectedEmails] = useState<number[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<Partial<EmailNotification>>();

  // Fetch email notifications
  const fetchEmails = async () => {
    setIsLoading(true);
    try {
      const response = await communicationAPI.getEmailNotifications();
      // Check if the response is an array
      if (Array.isArray(response)) {
        setEmails(response);
      } else if (response && typeof response === 'object' && Array.isArray(response.results)) {
        // Handle DRF pagination format
        setEmails(response.results);
      } else {
        console.error('Unexpected response format:', response);
        setEmails([]);
        toast.error('Received invalid data format from server');
      }
    } catch (error) {
      console.error('Error fetching email notifications:', error);
      toast.error('Failed to load email notifications');
      setEmails([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEmails();
  }, []);



  // Handle form submission
  const onSubmit = async (data: Partial<EmailNotification>) => {
    setIsLoading(true);
    try {
      // Clean the data before sending
      const cleanData = {
        subject: data.subject,
        content: data.content,
        recipients: data.recipients,
        status: data.status,
        scheduled_time: data.scheduled_time || null
      };

      // Remove empty scheduled_time to avoid validation issues
      if (!cleanData.scheduled_time) {
        delete cleanData.scheduled_time;
      }

      console.log('Form submission data:', cleanData);

      if (isEditMode && selectedEmail) {
        await communicationAPI.updateEmailNotification(selectedEmail.id, cleanData);
        toast.success('Email notification updated successfully');
      } else {
        await communicationAPI.createEmailNotification(cleanData);
        toast.success('Email notification created successfully');
      }
      fetchEmails();
      setIsDialogOpen(false);
      reset();
    } catch (error: any) {
      console.error('Error saving email notification:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });

      let errorMessage = 'Failed to save email notification';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.non_field_errors) {
          errorMessage = error.response.data.non_field_errors.join(', ');
        } else {
          // Handle field-specific errors
          const fieldErrors = [];
          for (const [field, errors] of Object.entries(error.response.data)) {
            if (Array.isArray(errors)) {
              fieldErrors.push(`${field}: ${errors.join(', ')}`);
            } else if (typeof errors === 'string') {
              fieldErrors.push(`${field}: ${errors}`);
            }
          }
          if (fieldErrors.length > 0) {
            errorMessage = fieldErrors.join('; ');
          }
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this email notification?')) {
      setIsLoading(true);
      try {
        await communicationAPI.deleteEmailNotification(id);
        toast.success('Email notification deleted successfully');
        fetchEmails();
      } catch (error) {
        console.error('Error deleting email notification:', error);
        toast.error('Failed to delete email notification');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle send email
  const handleSendEmail = async (id: number) => {
    setIsLoading(true);
    try {
      await communicationAPI.sendEmailNotification(id);
      toast.success('Email sent successfully');
      fetchEmails();
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('Failed to send email');
    } finally {
      setIsLoading(false);
    }
  };

  // Open create dialog
  const openCreateDialog = () => {
    setIsEditMode(false);
    setSelectedEmail(null);
    reset({
      subject: '',
      content: '',
      recipients: '',
      status: 'draft',
      scheduled_time: ''
    });
    setIsDialogOpen(true);
  };

  // Open edit dialog
  const openEditDialog = (email: EmailNotification) => {
    setIsEditMode(true);
    setSelectedEmail(email);
    setValue('subject', email.subject);
    setValue('content', email.content);
    setValue('recipients', email.recipients);
    setValue('status', email.status);
    setValue('scheduled_time', email.scheduled_time ? email.scheduled_time.slice(0, 16) : '');
    setIsDialogOpen(true);
  };

  // Open view dialog
  const openViewDialog = (email: EmailNotification) => {
    setSelectedEmail(email);
    setIsViewDialogOpen(true);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return (
          <Badge
            variant="outline"
            className="bg-gray-100 text-gray-700 border-gray-300 font-medium"
          >
            Draft
          </Badge>
        );
      case 'scheduled':
        return (
          <Badge
            variant="outline"
            className="bg-blue-100 text-blue-700 border-blue-300 font-medium"
          >
            Scheduled
          </Badge>
        );
      case 'sent':
        return (
          <Badge
            variant="outline"
            className="bg-green-100 text-green-700 border-green-300 font-medium"
          >
            Sent
          </Badge>
        );
      case 'failed':
        return (
          <Badge
            variant="outline"
            className="bg-red-100 text-red-700 border-red-300 font-medium"
          >
            Failed
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="bg-gray-100 text-gray-700 border-gray-300 font-medium"
          >
            {status}
          </Badge>
        );
    }
  };

  // Bulk operations
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmails(filteredEmails.map(email => email.id));
    } else {
      setSelectedEmails([]);
    }
  };

  const handleSelectEmail = (emailId: number, checked: boolean) => {
    if (checked) {
      setSelectedEmails(prev => [...prev, emailId]);
    } else {
      setSelectedEmails(prev => prev.filter(id => id !== emailId));
    }
  };

  const handleBulkSend = async () => {
    if (selectedEmails.length === 0) {
      toast.error('Please select emails to send');
      return;
    }

    // Check if selected emails can be sent
    const selectedEmailObjects = currentItems.filter(email => selectedEmails.includes(email.id));
    const sendableEmails = selectedEmailObjects.filter(email =>
      email.status === 'draft' || email.status === 'failed'
    );

    if (sendableEmails.length === 0) {
      const statusCounts = selectedEmailObjects.reduce((acc, email) => {
        acc[email.status] = (acc[email.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const statusMessages = Object.entries(statusCounts).map(([status, count]) =>
        `${count} ${status}`
      ).join(', ');

      toast.error(`Cannot send selected emails. Found: ${statusMessages}. Only draft or failed emails can be sent.`);
      return;
    }

    if (sendableEmails.length < selectedEmails.length) {
      const unsendableCount = selectedEmails.length - sendableEmails.length;
      if (!confirm(`${unsendableCount} of the selected emails cannot be sent (already sent or scheduled). Continue with ${sendableEmails.length} emails?`)) {
        return;
      }
    }

    setIsLoading(true);
    try {
      const result = await communicationAPI.bulkSendEmailNotifications(selectedEmails);

      if (result.sent_count > 0 || result.failed_count > 0) {
        toast.success(`Bulk send completed. ${result.sent_count} sent, ${result.failed_count} failed.`);
      } else {
        toast.warning('No emails were sent. Check email statuses and try again.');
      }

      setSelectedEmails([]);
      fetchEmails();
    } catch (error: any) {
      console.error('Error in bulk send:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to send emails';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedEmails.length === 0) {
      toast.error('Please select emails to delete');
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedEmails.length} email(s)?`)) {
      return;
    }

    setIsLoading(true);
    try {
      const result = await communicationAPI.bulkDeleteEmailNotifications(selectedEmails);
      toast.success(`Successfully deleted ${result.deleted_count} emails`);
      setSelectedEmails([]);
      fetchEmails();
    } catch (error) {
      console.error('Error in bulk delete:', error);
      toast.error('Failed to delete emails');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const stats = await communicationAPI.getEmailNotificationStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Load statistics on component mount
  useEffect(() => {
    fetchStatistics();
  }, []);

  // Helper function to get sendable emails count
  const getSendableEmailsCount = () => {
    const selectedEmailObjects = currentItems.filter(email => selectedEmails.includes(email.id));
    return selectedEmailObjects.filter(email =>
      email.status === 'draft' || email.status === 'failed'
    ).length;
  };

  // Filter emails based on search term and active tab
  const filteredEmails = Array.isArray(emails)
    ? emails.filter(email => {
        // Search filter
        const matchesSearch = searchTerm === '' ||
          email.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
          email.recipients.toLowerCase().includes(searchTerm.toLowerCase());

        // Tab filter
        const matchesTab = activeTab === 'all' ||
          activeTab === 'statistics' ||
          email.status === activeTab;

        return matchesSearch && matchesTab;
      })
    : [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredEmails.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredEmails.length / itemsPerPage);

  // Reset to first page when search term or tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, activeTab]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Email Notifications Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, send, and manage email communications with users
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              {selectedEmails.length > 0 && (
                <>
                  <Button
                    variant="outline"
                    onClick={handleBulkSend}
                    disabled={isLoading || getSendableEmailsCount() === 0}
                    className={cn(
                      "border-[#1a73c0] text-[#1a73c0] hover:bg-blue-50",
                      getSendableEmailsCount() === 0 && "opacity-50 cursor-not-allowed"
                    )}
                    title={getSendableEmailsCount() === 0 ? "No sendable emails selected (only draft or failed emails can be sent)" : "Send selected emails"}
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Send Selected ({getSendableEmailsCount()}/{selectedEmails.length})
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleBulkDelete}
                    disabled={isLoading}
                    className="border-red-500 text-red-500 hover:bg-red-50"
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete Selected ({selectedEmails.length})
                  </Button>
                </>
              )}
              <Button
                onClick={openCreateDialog}
                className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Email
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* Search and Filter Section */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by subject or recipients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                />
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={fetchEmails}
                disabled={isLoading}
                className="h-11 px-4 border-gray-300 hover:bg-gray-50"
              >
                <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
                Refresh
              </Button>
              {statistics && (
                <div className="flex items-center gap-4 text-sm bg-gray-50 px-4 py-2 rounded-lg border">
                  <div className="flex items-center gap-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-medium">{statistics.status_summary?.sent || 0}</span>
                    <span className="text-gray-600">Sent</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">{statistics.status_summary?.draft || 0}</span>
                    <span className="text-gray-600">Draft</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span className="font-medium">{statistics.status_summary?.failed || 0}</span>
                    <span className="text-gray-600">Failed</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Tabs for filtering */}
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6 mb-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="draft">Draft</TabsTrigger>
              <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
              <TabsTrigger value="sent">Sent</TabsTrigger>
              <TabsTrigger value="failed">Failed</TabsTrigger>
              <TabsTrigger value="statistics">
                <BarChart3 className="h-4 w-4 mr-2" />
                Statistics
              </TabsTrigger>
            </TabsList>
        <TabsContent value="statistics" className="mt-4">
          {statistics && (
            <div className="grid gap-6">
              {/* Statistics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Emails</p>
                        <p className="text-2xl font-bold">{statistics.total_notifications}</p>
                      </div>
                      <Mail className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Sent Today</p>
                        <p className="text-2xl font-bold text-green-600">{statistics.today_notifications}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Recent (30 days)</p>
                        <p className="text-2xl font-bold text-blue-600">{statistics.recent_notifications}</p>
                      </div>
                      <Calendar className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Failed</p>
                        <p className="text-2xl font-bold text-red-600">{statistics.status_summary?.failed || 0}</p>
                      </div>
                      <XCircle className="h-8 w-8 text-red-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Status Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Email Status Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(statistics.status_summary || {}).map(([status, count]) => (
                      <div key={status} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStatusBadge(status)}
                          <span className="capitalize">{status}</span>
                        </div>
                        <span className="font-semibold">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

            {activeTab !== 'statistics' && (
              <TabsContent value={activeTab} className="mt-0">
                <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                        <TableRow>
                          <TableHead className="w-12 text-[#1a73c0] font-medium">
                            <Checkbox
                              checked={selectedEmails.length === currentItems.length && currentItems.length > 0}
                              onCheckedChange={handleSelectAll}
                              className="data-[state=checked]:bg-[#1a73c0] data-[state=checked]:border-[#1a73c0]"
                            />
                          </TableHead>
                          <TableHead className="w-[25%] text-[#1a73c0] font-medium">Subject</TableHead>
                          <TableHead className="w-[15%] text-[#1a73c0] font-medium">Recipients</TableHead>
                          <TableHead className="w-[12%] text-[#1a73c0] font-medium">Status</TableHead>
                          <TableHead className="w-[15%] text-[#1a73c0] font-medium">Scheduled</TableHead>
                          <TableHead className="w-[15%] text-[#1a73c0] font-medium">Sent</TableHead>
                          <TableHead className="w-[18%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoading ? (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8">
                              <div className="flex items-center justify-center">
                                <RefreshCw className="h-5 w-5 animate-spin mr-2 text-[#1a73c0]" />
                                <span className="text-gray-600">Loading email notifications...</span>
                              </div>
                            </TableCell>
                          </TableRow>
                        ) : currentItems.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8">
                              <div className="flex flex-col items-center">
                                <Mail className="h-12 w-12 text-gray-300 mb-3" />
                                <span className="text-gray-600 font-medium">No email notifications found</span>
                                <span className="text-gray-400 text-sm">Try adjusting your search or filters</span>
                              </div>
                            </TableCell>
                          </TableRow>
                        ) : (
                          currentItems.map((email) => {
                            const canBeSent = email.status === 'draft' || email.status === 'failed';
                            const rowClassName = cn(
                              "transition-colors",
                              canBeSent
                                ? "hover:bg-blue-50/50"
                                : "bg-gray-50/50 hover:bg-gray-100/50"
                            );

                            return (
                              <TableRow key={email.id} className={rowClassName}>
                                <TableCell>
                                  <Checkbox
                                    checked={selectedEmails.includes(email.id)}
                                    onCheckedChange={(checked) => handleSelectEmail(email.id, checked as boolean)}
                                    className={cn(
                                      "data-[state=checked]:bg-[#1a73c0] data-[state=checked]:border-[#1a73c0]",
                                      !canBeSent && "opacity-50"
                                    )}
                                    title={canBeSent ? "Select for bulk operations" : "Cannot be sent (already sent or scheduled)"}
                                  />
                                </TableCell>
                              <TableCell className={cn("font-medium", canBeSent ? "text-gray-900" : "text-gray-500")}>
                                <div className="max-w-xs truncate flex items-center gap-2" title={email.subject}>
                                  <span>{email.subject}</span>
                                  {!canBeSent && (
                                    <span className="text-xs text-gray-400 bg-gray-100 px-1.5 py-0.5 rounded" title="Cannot be sent">
                                      Read-only
                                    </span>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className="text-gray-600">
                                <div className="flex items-center">
                                  <Mail className="h-4 w-4 mr-1 text-gray-400" />
                                  {email.recipients.split(',').length}
                                </div>
                              </TableCell>
                              <TableCell>{getStatusBadge(email.status)}</TableCell>
                              <TableCell className="text-gray-600 text-sm">
                                {email.scheduled_time
                                  ? format(new Date(email.scheduled_time), 'MMM d, HH:mm')
                                  : '-'}
                              </TableCell>
                              <TableCell className="text-gray-600 text-sm">
                                {email.sent_time
                                  ? format(new Date(email.sent_time), 'MMM d, HH:mm')
                                  : '-'}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center justify-center space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openViewDialog(email)}
                                    className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-[#1a73c0]"
                                    title="View email"
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                  {email.status === 'draft' && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => openEditDialog(email)}
                                      className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-[#1a73c0]"
                                      title="Edit email"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  )}
                                  {(email.status === 'draft' || email.status === 'scheduled') && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleSendEmail(email.id)}
                                      className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600"
                                      title="Send email"
                                    >
                                      <Send className="h-4 w-4" />
                                    </Button>
                                  )}
                                  {email.status === 'draft' && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleDelete(email.id)}
                                      className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                                      title="Delete email"
                                    >
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                            );
                          })
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </TabsContent>
            )}
          </Tabs>
        </CardContent>

        {/* Pagination Footer */}
        <CardFooter>
          {filteredEmails.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredEmails.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredEmails.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Dynamic page number buttons */}
                  {totalPages <= 7 ? (
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === number
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === 1
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        1
                      </Button>

                      {currentPage > 3 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number =>
                          number > 1 &&
                          number < totalPages &&
                          (
                            number === currentPage - 1 ||
                            number === currentPage ||
                            number === currentPage + 1 ||
                            (currentPage <= 3 && number <= 4) ||
                            (currentPage >= totalPages - 2 && number >= totalPages - 3)
                          )
                        )
                        .map(number => (
                          <Button
                            key={number}
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={cn(
                              "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                              currentPage === number
                                ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                                : "bg-white text-gray-700 hover:bg-blue-50"
                            )}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {currentPage < totalPages - 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {totalPages > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === totalPages
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? 'Edit Email Notification' : 'Create New Email Notification'}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? 'Update the email notification details below'
                : 'Fill in the details to create a new email notification'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="subject" className="text-right">
                  Subject
                </Label>
                <Input
                  id="subject"
                  className="col-span-3"
                  {...register('subject', { required: 'Subject is required' })}
                />
                {errors.subject && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.subject.message}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="recipients" className="text-right">
                  Recipients
                </Label>
                <Input
                  id="recipients"
                  className="col-span-3"
                  placeholder="<EMAIL>, <EMAIL>"
                  {...register('recipients', { required: 'Recipients are required' })}
                />
                {errors.recipients && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.recipients.message}
                  </p>
                )}
                <p className="text-gray-500 text-sm col-span-3 col-start-2">
                  Comma-separated list of email addresses or recipient groups
                </p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="content" className="text-right">
                  Content
                </Label>
                <Textarea
                  id="content"
                  className="col-span-3"
                  rows={8}
                  {...register('content', { required: 'Content is required' })}
                />
                {errors.content && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.content.message}
                  </p>
                )}
                <p className="text-gray-500 text-sm col-span-3 col-start-2">
                  HTML formatting is supported
                </p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <Select
                  defaultValue="draft"
                  onValueChange={(value) => setValue('status', value as 'draft' | 'scheduled')}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="scheduled_time" className="text-right">
                  Schedule Time
                </Label>
                <Input
                  id="scheduled_time"
                  type="datetime-local"
                  className="col-span-3"
                  {...register('scheduled_time')}
                />
                <p className="text-gray-500 text-sm col-span-3 col-start-2">
                  Optional. Leave blank for drafts or to send immediately.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : isEditMode ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {selectedEmail?.subject}
            </DialogTitle>
            <div className="flex items-center gap-2 mt-2">
              {selectedEmail && getStatusBadge(selectedEmail.status)}
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="mb-4">
              <h4 className="text-sm font-semibold mb-1">Recipients:</h4>
              <p className="text-sm">{selectedEmail?.recipients}</p>
            </div>
            <div className="prose max-w-none">
              <div dangerouslySetInnerHTML={{ __html: selectedEmail?.content || '' }} />
            </div>
            <div className="mt-4 text-sm text-gray-500 flex flex-col gap-1">
              {selectedEmail?.scheduled_time && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    Scheduled: {format(new Date(selectedEmail.scheduled_time), 'PPpp')}
                  </span>
                </div>
              )}
              {selectedEmail?.sent_time && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    Sent: {format(new Date(selectedEmail.sent_time), 'PPpp')}
                  </span>
                </div>
              )}
              <div className="mt-2">
                Created by: {selectedEmail?.sender_details?.full_name || 'Unknown'}
              </div>
            </div>
          </div>
          <DialogFooter>
            {(selectedEmail?.status === 'draft' || selectedEmail?.status === 'scheduled') && (
              <Button
                variant="secondary"
                onClick={() => {
                  handleSendEmail(selectedEmail.id);
                  setIsViewDialogOpen(false);
                }}
              >
                <Send className="h-4 w-4 mr-2" />
                Send Now
              </Button>
            )}
            <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmailNotifications;
