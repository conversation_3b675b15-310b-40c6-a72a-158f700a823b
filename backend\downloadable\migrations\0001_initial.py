# Generated by Django 5.2.1 on 2025-06-02 16:47

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Downloadable',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('file', models.FileField(upload_to='downloads/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'downloadable_files',
                'ordering': ['-created_at'],
            },
        ),
    ]
