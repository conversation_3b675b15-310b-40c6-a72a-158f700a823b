# Generated by Django 5.2.1 on 2025-06-07 14:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings_manager', '0002_smtpsettings'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='smtpsettings',
            options={},
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_enabled',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_from_email',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_from_name',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_host',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_password',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_port',
        ),
        migrations.Remo<PERSON><PERSON><PERSON>(
            model_name='smtpsettings',
            name='smtp_timeout',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_use_ssl',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_use_tls',
        ),
        migrations.RemoveField(
            model_name='smtpsettings',
            name='smtp_username',
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='from_email',
            field=models.EmailField(blank=True, default='', max_length=254),
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='host',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='password',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='port',
            field=models.IntegerField(default=587),
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='timeout',
            field=models.IntegerField(default=60),
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='use_ssl',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='use_tls',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='smtpsettings',
            name='username',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
    ]
