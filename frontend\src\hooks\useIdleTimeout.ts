import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { authAPI } from '@/services/api';

interface UseIdleTimeoutProps {
  idleTime?: number; // Time in minutes before logout
  onIdle?: () => void; // Callback when user becomes idle
  warningTime?: number; // Time in minutes before warning
}

/**
 * Hook to handle idle timeout and auto-logout
 * @param idleTime Time in minutes before logout (default: 5)
 * @param onIdle Callback when user becomes idle
 * @param warningTime Time in minutes before warning (default: 1)
 */
const useIdleTimeout = ({
  idleTime = 5,
  onIdle,
  warningTime = 1
}: UseIdleTimeoutProps = {}) => {
  const [isIdle, setIsIdle] = useState(false);
  const [isWarned, setIsWarned] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [warningDisplayed, setWarningDisplayed] = useState(false);

  useEffect(() => {
    // Force disable idle timeout
    localStorage.setItem('idleTimeoutEnabled', 'false');
    return; // Don't set up any timers

    // Convert minutes to milliseconds
    const idleTimeoutMs = idleTime * 60 * 1000;
    const warningTimeoutMs = (idleTime - warningTime) * 60 * 1000;

    let idleTimer: NodeJS.Timeout;
    let warningTimer: NodeJS.Timeout;
    let countdownInterval: NodeJS.Timeout;

    // Function to handle user activity
    const handleUserActivity = () => {
      // Only update if the user is authenticated
      const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
      if (!isAuthenticated) return;

      // Update last activity timestamp
      localStorage.setItem('lastActivity', Date.now().toString());

      // Reset idle state
      setIsIdle(false);

      // Clear warning state
      if (isWarned) {
        setIsWarned(false);
        setTimeRemaining(null);
        setWarningDisplayed(false);
        toast.dismiss('idle-warning');
      }

      // Clear existing timers
      clearTimeout(idleTimer);
      clearTimeout(warningTimer);
      clearInterval(countdownInterval);

      // Set new warning timer
      warningTimer = setTimeout(() => {
        setIsWarned(true);
        setTimeRemaining(warningTime * 60); // Set initial countdown in seconds

        // Show warning toast
        if (!warningDisplayed) {
          toast.warning(
            `You will be logged out in ${warningTime} minute${warningTime !== 1 ? 's' : ''} due to inactivity`,
            {
              id: 'idle-warning',
              duration: Infinity,
              action: {
                label: 'Stay Logged In',
                onClick: () => handleUserActivity()
              }
            }
          );
          setWarningDisplayed(true);
        }

        // Start countdown
        countdownInterval = setInterval(() => {
          setTimeRemaining(prev => {
            if (prev === null || prev <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }, warningTimeoutMs);

      // Set new idle timer
      idleTimer = setTimeout(async () => {
        setIsIdle(true);
        if (onIdle) {
          onIdle();
        } else {
          // Enhanced logout behavior with server-side session termination
          try {
            // Attempt to logout on server side
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              await authAPI.logout(refreshToken);
            }
          } catch (error) {
            console.error('Error during server-side logout:', error);
            // Continue with client-side logout even if server logout fails
          }

          // Clear all authentication data
          localStorage.removeItem('isAuthenticated');
          localStorage.removeItem('token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('user');
          localStorage.removeItem('lastActivity');

          // Clear any cached data
          localStorage.removeItem('public_certificate_stats');

          // Show logout toast and redirect
          toast.error('You have been logged out due to inactivity', {
            duration: 5000,
            description: 'For your security, you were automatically logged out after 15 minutes of inactivity.',
          });

          // Redirect to login page with session expired parameter
          window.location.href = '/login?reason=session_expired';
        }
      }, idleTimeoutMs);
    };

    // Initialize timers on mount
    handleUserActivity();

    // Add event listeners for user activity
    const events = ['mousedown', 'keydown', 'scroll', 'touchstart', 'mousemove'];
    events.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });

    // Clean up
    return () => {
      clearTimeout(idleTimer);
      clearTimeout(warningTimer);
      clearInterval(countdownInterval);
      events.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });
    };
  }, [idleTime, onIdle, warningTime, isWarned, warningDisplayed]);

  return {
    isIdle,
    isWarned,
    timeRemaining,
    resetTimeout: () => {
      // Update last activity timestamp
      localStorage.setItem('lastActivity', Date.now().toString());
      // Dismiss warning toast
      toast.dismiss('idle-warning');
    }
  };
};

export default useIdleTimeout;
