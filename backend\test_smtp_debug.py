#!/usr/bin/env python
"""
Debug script to test SMTP configuration
"""
import os
import django
import sys

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from settings_manager.smtp_models import SMTPSettings
from settings_manager.smtp_serializers import SMTPTestSerializer

def test_smtp_settings():
    """Test current SMTP settings"""
    print("=== SMTP Settings Debug ===")
    
    try:
        # Load current settings
        settings = SMTPSettings.load()
        print(f"✅ SMTP Settings loaded successfully")
        print(f"Provider: {settings.provider}")
        print(f"Host: {settings.host}")
        print(f"Port: {settings.port}")
        print(f"Username: {settings.username}")
        print(f"From Email: {settings.from_email}")
        print(f"Has Password: {bool(settings.password)}")
        print(f"Password Length: {len(settings.password) if settings.password else 0}")
        print(f"Use TLS: {settings.use_tls}")
        print(f"Use SSL: {settings.use_ssl}")
        print(f"Timeout: {settings.timeout}")
        print(f"Created: {settings.created_at}")
        print(f"Updated: {settings.updated_at}")
        
        # Check if settings are configured
        is_configured = bool(settings.host and settings.from_email)
        print(f"\n✅ SMTP Configured: {is_configured}")
        
        if not is_configured:
            print("❌ SMTP is not properly configured!")
            print("Missing required fields:")
            if not settings.host:
                print("  - Host is empty")
            if not settings.from_email:
                print("  - From Email is empty")
        
        # Test serializer validation
        print("\n=== Testing Serializer ===")
        test_data = {'recipient': '<EMAIL>'}
        serializer = SMTPTestSerializer(data=test_data)
        is_valid = serializer.is_valid()
        print(f"✅ Serializer Valid: {is_valid}")
        
        if not is_valid:
            print(f"❌ Serializer Errors: {serializer.errors}")
        else:
            print(f"✅ Validated Data: {serializer.validated_data}")
            
        return settings, is_configured
        
    except Exception as e:
        print(f"❌ Error loading SMTP settings: {e}")
        return None, False

def configure_gmail_smtp():
    """Configure Gmail SMTP settings"""
    print("\n=== Configuring Gmail SMTP ===")
    
    try:
        settings = SMTPSettings.load()
        settings.provider = 'gmail'
        settings.host = 'smtp.gmail.com'
        settings.port = 587
        settings.use_tls = True
        settings.use_ssl = False
        settings.timeout = 60
        
        # You'll need to set these manually
        print("⚠️  Please set these manually:")
        print("settings.username = '<EMAIL>'")
        print("settings.password = 'your-app-password'")
        print("settings.from_email = '<EMAIL>'")
        print("settings.save()")
        
        return settings
        
    except Exception as e:
        print(f"❌ Error configuring Gmail SMTP: {e}")
        return None

if __name__ == "__main__":
    # Test current settings
    settings, is_configured = test_smtp_settings()
    
    if not is_configured:
        print("\n" + "="*50)
        print("SMTP is not configured. Here's how to configure Gmail:")
        print("="*50)
        configure_gmail_smtp()
        
        print("\nTo configure manually, run:")
        print("python manage.py shell")
        print("Then execute:")
        print("""
from settings_manager.smtp_models import SMTPSettings
settings = SMTPSettings.load()
settings.provider = 'gmail'
settings.host = 'smtp.gmail.com'
settings.port = 587
settings.use_tls = True
settings.use_ssl = False
settings.timeout = 60
settings.username = '<EMAIL>'
settings.password = 'your-app-password'
settings.from_email = '<EMAIL>'
settings.save()
print('Gmail SMTP configured!')
""")
