import axios from 'axios';

export interface Applicant {
  id: number;
  user: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    date_joined: string;
    last_login: string;
  };
  application_num: string;
  transaction_id: string;
  sponsorship: string;
  application_info: {
    id: number;
    college: {
      id: number;
      name: string;
    };
    department: {
      id: number;
      name: string;
    };
    program: {
      id: number;
      name: string;
    };
    field_of_study?: {
      id: number;
      name: string;
    };
    status: string;
  };
  applicant_info: {
    id: string;
    grandfather_name: string;
    gender: string;
    dob: string;
    mobile: string;
    program_level: string;
    ug_university: string;
    ug_field_of_study: string;
    ug_CGPA: number;
    pg_university: string;
    pg_field_of_study: string;
    pg_CGPA: number;
  } | null;
  gat: {
    id: number;
    GAT_No: string;
    GAT_Result: number;
    gat_no: string;
  };
  registrar_off_status: string;
  department_status: string;
  payment_status: string;
  year: {
    uuid: string;
    year: string;
  } | null;
  year_name: string | null;
  term: {
    id: string;
    name: string;
  } | null;
  term_name: string | null;
  created_at: string;
  updated_at: string;
}

export interface ApplicantDetail extends Applicant {
  remark: string;
  has_documents: boolean;
  documents?: {
    degree?: string;
    sponsorship?: string;
    student_copy?: string;
    recommendation?: string;
    publication?: string;
    conceptnote?: string;
    grade_12_certificate?: string;
    grade_9_12_transcript?: string;
  };
  reg_approved_by?: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  dep_approved_by?: {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export interface ApplicantStatusUpdate {
  registrar_off_status?: string;
  department_status?: string;
  payment_status?: string;
  remark?: string;
}

export interface ApplicantListParams {
  college_id?: number;
  department_id?: number;
  program_id?: number;
  registrar_off_status?: string;
  department_status?: string;
  payment_status?: string;
  sponsorship?: string;
  search?: string;
  year?: string;
  term?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

import { API_BASE_URL } from '../config';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL.endsWith('/') ? API_BASE_URL : `${API_BASE_URL}/`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const token = localStorage.getItem('token');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }

    // Log the request for debugging
    console.log('API Request:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      params: config.params
    });

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

export const applicantAPI = {
  getApplicants: async (params?: ApplicantListParams) => {
    try {
      // Log the params for debugging
      console.log('Fetching applicants with params:', params);

      // Process the params to ensure correct types
      const processedParams = { ...params };

      // Remove any NaN values
      Object.keys(processedParams).forEach(key => {
        const value = processedParams[key as keyof ApplicantListParams];
        if (value !== undefined && value !== null) {
          // Check if it's NaN for numeric fields
          if (typeof value === 'number' && isNaN(value)) {
            delete processedParams[key as keyof ApplicantListParams];
          }
        }
      });

      // Make sure program_id is a number if present
      if (processedParams.program_id !== undefined && processedParams.program_id !== null) {
        if (typeof processedParams.program_id === 'string') {
          const parsed = parseInt(processedParams.program_id);
          if (!isNaN(parsed)) {
            processedParams.program_id = parsed;
          } else {
            delete processedParams.program_id;
          }
        }
      }

      // Make sure college_id is a number if present
      if (processedParams.college_id !== undefined && processedParams.college_id !== null) {
        if (typeof processedParams.college_id === 'string') {
          const parsed = parseInt(processedParams.college_id);
          if (!isNaN(parsed)) {
            processedParams.college_id = parsed;
          } else {
            delete processedParams.college_id;
          }
        } else if (typeof processedParams.college_id === 'number' && isNaN(processedParams.college_id)) {
          delete processedParams.college_id;
        }
      }

      // Make sure department_id is a number if present
      if (processedParams.department_id !== undefined && processedParams.department_id !== null) {
        if (typeof processedParams.department_id === 'string') {
          const parsed = parseInt(processedParams.department_id);
          if (!isNaN(parsed)) {
            processedParams.department_id = parsed;
          } else {
            delete processedParams.department_id;
          }
        }
      }

      // Log the processed params
      console.log('Processed params for API request:', processedParams);

      // Make the API request
      const response = await api.get('staff/applicants/', { params: processedParams });

      // Log the raw response to see its structure
      console.log('Raw applicants response:', response.data);

      // Return the raw data without modifications
      return response.data;
    } catch (error) {
      console.error('Error fetching applicants:', error);
      throw error;
    }
  },

  getApplicantById: async (id: number) => {
    const response = await api.get(`staff/applicants/${id}/`);
    return response.data;
  },

  updateApplicantStatus: async (id: number, data: ApplicantStatusUpdate) => {
    const response = await api.patch(`staff/applicants/${id}/update-status/`, data);
    return response.data;
  },

  getApplicantDocuments: async (userId: number) => {
    try {
      console.log('API Request: Fetching documents for user ID:', userId);
      const url = `staff/applicants/${userId}/documents/`;
      console.log('API URL:', url);

      const response = await api.get(url);
      console.log('API Response for documents:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching applicant documents:', error);
      console.error('Error details:', JSON.stringify(error));
      return null;
    }
  }
};
