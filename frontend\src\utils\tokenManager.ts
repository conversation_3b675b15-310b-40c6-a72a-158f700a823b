/**
 * Token Management Utility
 * Handles JWT token storage, validation, and session management
 */

interface TokenData {
  access: string;
  refresh: string;
  expiresAt?: number;
  issuedAt?: number;
}

interface SessionData {
  user: any;
  lastActivity: number;
  sessionId: string;
}

class TokenManager {
  private static instance: TokenManager;
  private readonly ACCESS_TOKEN_KEY = 'token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'user';
  private readonly SESSION_KEY = 'session_data';
  private readonly LAST_ACTIVITY_KEY = 'lastActivity';
  private readonly IS_AUTHENTICATED_KEY = 'isAuthenticated';

  private constructor() {}

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Store JWT tokens securely
   */
  setTokens(tokenData: TokenData): void {
    try {
      localStorage.setItem(this.ACCESS_TOKEN_KEY, tokenData.access);
      localStorage.setItem(this.REFRESH_TOKEN_KEY, tokenData.refresh);
      localStorage.setItem(this.IS_AUTHENTICATED_KEY, 'true');
      localStorage.setItem(this.LAST_ACTIVITY_KEY, Date.now().toString());

      // Store token metadata
      const tokenInfo = this.decodeToken(tokenData.access);
      if (tokenInfo) {
        const metadata = {
          expiresAt: tokenInfo.exp * 1000, // Convert to milliseconds
          issuedAt: tokenInfo.iat * 1000,
          userId: tokenInfo.user_id,
        };
        localStorage.setItem('token_metadata', JSON.stringify(metadata));
      }

      console.log('Tokens stored successfully');
    } catch (error) {
      console.error('Error storing tokens:', error);
      throw new Error('Failed to store authentication tokens');
    }
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    try {
      return localStorage.getItem(this.ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Error retrieving access token:', error);
      return null;
    }
  }

  /**
   * Get refresh token
   */
  getRefreshToken(): string | null {
    try {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Error retrieving refresh token:', error);
      return null;
    }
  }

  /**
   * Update access token (after refresh)
   */
  updateAccessToken(newToken: string): void {
    try {
      localStorage.setItem(this.ACCESS_TOKEN_KEY, newToken);
      localStorage.setItem(this.LAST_ACTIVITY_KEY, Date.now().toString());

      // Update token metadata
      const tokenInfo = this.decodeToken(newToken);
      if (tokenInfo) {
        const metadata = {
          expiresAt: tokenInfo.exp * 1000,
          issuedAt: tokenInfo.iat * 1000,
          userId: tokenInfo.user_id,
        };
        localStorage.setItem('token_metadata', JSON.stringify(metadata));
      }

      console.log('Access token updated successfully');
    } catch (error) {
      console.error('Error updating access token:', error);
      throw new Error('Failed to update access token');
    }
  }

  /**
   * Clear all authentication data
   */
  clearTokens(): void {
    try {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
      localStorage.removeItem(this.SESSION_KEY);
      localStorage.removeItem(this.LAST_ACTIVITY_KEY);
      localStorage.removeItem(this.IS_AUTHENTICATED_KEY);
      localStorage.removeItem('token_metadata');

      console.log('All authentication data cleared');
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    try {
      const token = this.getAccessToken();
      const refreshToken = this.getRefreshToken();
      const isAuthFlag = localStorage.getItem(this.IS_AUTHENTICATED_KEY);

      return !!(token && refreshToken && isAuthFlag === 'true');
    } catch (error) {
      console.error('Error checking authentication status:', error);
      return false;
    }
  }

  /**
   * Decode JWT token
   */
  decodeToken(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token?: string): boolean {
    try {
      const tokenToCheck = token || this.getAccessToken();
      if (!tokenToCheck) return true;

      const decoded = this.decodeToken(tokenToCheck);
      if (!decoded || !decoded.exp) return true;

      const currentTime = Date.now() / 1000;
      return decoded.exp < currentTime;
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  }

  /**
   * Check if token is about to expire (within specified minutes)
   */
  isTokenExpiring(token?: string, minutesBeforeExpiry: number = 5): boolean {
    try {
      const tokenToCheck = token || this.getAccessToken();
      if (!tokenToCheck) return true;

      const decoded = this.decodeToken(tokenToCheck);
      if (!decoded || !decoded.exp) return true;

      const currentTime = Date.now() / 1000;
      const expiryTime = decoded.exp;
      const timeUntilExpiry = expiryTime - currentTime;
      const minutesUntilExpiry = timeUntilExpiry / 60;

      return minutesUntilExpiry <= minutesBeforeExpiry;
    } catch (error) {
      console.error('Error checking token expiry:', error);
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpirationTime(token?: string): Date | null {
    try {
      const tokenToCheck = token || this.getAccessToken();
      if (!tokenToCheck) return null;

      const decoded = this.decodeToken(tokenToCheck);
      if (!decoded || !decoded.exp) return null;

      return new Date(decoded.exp * 1000);
    } catch (error) {
      console.error('Error getting token expiration time:', error);
      return null;
    }
  }

  /**
   * Store user data
   */
  setUserData(userData: any): void {
    try {
      localStorage.setItem(this.USER_KEY, JSON.stringify(userData));
      
      // Update session data
      const sessionData: SessionData = {
        user: userData,
        lastActivity: Date.now(),
        sessionId: this.generateSessionId(),
      };
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(sessionData));

      console.log('User data stored successfully');
    } catch (error) {
      console.error('Error storing user data:', error);
      throw new Error('Failed to store user data');
    }
  }

  /**
   * Get user data
   */
  getUserData(): any {
    try {
      const userData = localStorage.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  }

  /**
   * Update last activity timestamp
   */
  updateLastActivity(): void {
    try {
      localStorage.setItem(this.LAST_ACTIVITY_KEY, Date.now().toString());
      
      // Update session data
      const sessionData = this.getSessionData();
      if (sessionData) {
        sessionData.lastActivity = Date.now();
        localStorage.setItem(this.SESSION_KEY, JSON.stringify(sessionData));
      }
    } catch (error) {
      console.error('Error updating last activity:', error);
    }
  }

  /**
   * Get last activity timestamp
   */
  getLastActivity(): number {
    try {
      const lastActivity = localStorage.getItem(this.LAST_ACTIVITY_KEY);
      return lastActivity ? parseInt(lastActivity, 10) : 0;
    } catch (error) {
      console.error('Error getting last activity:', error);
      return 0;
    }
  }

  /**
   * Check if session is expired based on inactivity
   */
  isSessionExpired(maxInactiveMinutes: number = 30): boolean {
    try {
      const lastActivity = this.getLastActivity();
      if (!lastActivity) return true;

      const currentTime = Date.now();
      const inactiveTime = currentTime - lastActivity;
      const inactiveMinutes = inactiveTime / (1000 * 60);

      return inactiveMinutes > maxInactiveMinutes;
    } catch (error) {
      console.error('Error checking session expiration:', error);
      return true;
    }
  }

  /**
   * Get session data
   */
  private getSessionData(): SessionData | null {
    try {
      const sessionData = localStorage.getItem(this.SESSION_KEY);
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error('Error getting session data:', error);
      return null;
    }
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get authorization header
   */
  getAuthHeader(): { Authorization: string } | {} {
    const token = this.getAccessToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Validate stored tokens
   */
  validateStoredTokens(): boolean {
    try {
      const accessToken = this.getAccessToken();
      const refreshToken = this.getRefreshToken();

      if (!accessToken || !refreshToken) {
        console.log('Missing tokens');
        return false;
      }

      // Check if access token is valid (not expired)
      if (this.isTokenExpired(accessToken)) {
        console.log('Access token is expired');
        return false;
      }

      // Check if refresh token is valid
      if (this.isTokenExpired(refreshToken)) {
        console.log('Refresh token is expired');
        this.clearTokens();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating stored tokens:', error);
      return false;
    }
  }
}

}

/**
 * Session Restoration Hook
 */
export const useSessionRestoration = () => {
  const restoreSession = async (): Promise<{
    success: boolean;
    user?: any;
    error?: string;
  }> => {
    try {
      const manager = TokenManager.getInstance();

      // Check if we have valid tokens
      if (!manager.validateStoredTokens()) {
        return {
          success: false,
          error: 'No valid tokens found'
        };
      }

      // Get stored user data
      const userData = manager.getUserData();
      if (!userData) {
        return {
          success: false,
          error: 'No user data found'
        };
      }

      // Check session expiration
      if (manager.isSessionExpired()) {
        manager.clearTokens();
        return {
          success: false,
          error: 'Session expired due to inactivity'
        };
      }

      // Update last activity
      manager.updateLastActivity();

      return {
        success: true,
        user: userData
      };

    } catch (error) {
      console.error('Session restoration error:', error);
      return {
        success: false,
        error: 'Failed to restore session'
      };
    }
  };

  return { restoreSession };
};

// Export singleton instance
export const tokenManager = TokenManager.getInstance();
export default tokenManager;
