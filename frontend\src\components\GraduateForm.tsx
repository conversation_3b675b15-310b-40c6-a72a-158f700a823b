import React, { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Plus, Pencil } from 'lucide-react';

import { graduateVerificationAPI } from '@/services/api';

// Define the form schema with validation
const graduateFormSchema = z.object({
  student_id: z.string().min(1, 'Student ID is required'),
  first_name: z.string().min(1, 'First name is required'),
  middle_name: z.string().optional(),
  last_name: z.string().min(1, 'Last name is required'),
  year_of_entry: z.string().min(1, 'Year of entry is required'),
  year_of_graduation: z.string().min(1, 'Year of graduation is required'),
  gpa: z.string()
    .min(1, 'GPA is required')
    .refine(val => !isNaN(parseFloat(val)), 'GPA must be a number')
    .refine(val => parseFloat(val) >= 2.0 && parseFloat(val) <= 4.0, 'GPA must be between 2.0 and 4.0'),
  gender: z.string().min(1, 'Gender is required'),
  college: z.string().min(1, 'College is required'),
  department: z.string().min(1, 'Department is required'),
  field_of_study: z.string().min(1, 'Field of study is required'),
  program: z.string().min(1, 'Program is required'),
  admission_classification: z.string().min(1, 'Admission classification is required'),
});

// Define the props for the component
interface GraduateFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  graduateData?: any; // For edit mode
  isEditMode?: boolean;
}

// Define the options interfaces
interface SelectOption {
  id: number;
  name: string;
  code?: string;
}

const GraduateForm: React.FC<GraduateFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  graduateData,
  isEditMode = false,
}) => {
  // State for dropdown options
  const [colleges, setColleges] = useState<SelectOption[]>([]);
  const [departments, setDepartments] = useState<SelectOption[]>([]);
  const [fieldsOfStudy, setFieldsOfStudy] = useState<SelectOption[]>([]);
  const [programs, setPrograms] = useState<SelectOption[]>([]);
  const [admissionClassifications, setAdmissionClassifications] = useState<SelectOption[]>([]);

  // State for filtered options
  const [filteredDepartments, setFilteredDepartments] = useState<SelectOption[]>([]);
  const [filteredFieldsOfStudy, setFilteredFieldsOfStudy] = useState<SelectOption[]>([]);

  // Loading state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [editFormValues, setEditFormValues] = useState<any>(null);

  // Initialize the form
  const form = useForm<z.infer<typeof graduateFormSchema>>({
    resolver: zodResolver(graduateFormSchema),
    defaultValues: {
      student_id: '',
      first_name: '',
      middle_name: '',
      last_name: '',
      year_of_entry: '',
      year_of_graduation: '',
      gpa: '',
      gender: '',
      college: '',
      department: '',
      field_of_study: '',
      program: '',
      admission_classification: '',
    },
  });

  // Fetch dropdown options
  const fetchDropdownOptions = async () => {
    setIsLoading(true);
    try {
      const [collegesResponse, departmentsResponse, fieldsOfStudyResponse, programsResponse, admissionClassificationsResponse] = await Promise.all([
        graduateVerificationAPI.getColleges(),
        graduateVerificationAPI.getDepartments(),
        graduateVerificationAPI.getFieldsOfStudy(),
        graduateVerificationAPI.getPrograms(),
        graduateVerificationAPI.getAdmissionClassifications(),
      ]);

      setColleges(collegesResponse.data || []);
      setDepartments(departmentsResponse.data || []);
      setFieldsOfStudy(fieldsOfStudyResponse.data || []);
      setPrograms(programsResponse.data || []);
      setAdmissionClassifications(admissionClassificationsResponse.data || []);
      setFilteredDepartments(departmentsResponse.data || []);
      setFilteredFieldsOfStudy(fieldsOfStudyResponse.data || []);
    } catch (error) {
      console.error('Error fetching dropdown options:', error);
      toast.error('Failed to load form options');
    } finally {
      setIsLoading(false);
    }
  };

  // Set form values when in edit mode
  useEffect(() => {
    if (isOpen) {
      fetchDropdownOptions();
    }
  }, [isOpen]);

  // Set form values after dropdown options are loaded
  useEffect(() => {
    if (isOpen && !isLoading && colleges.length > 0 && programs.length > 0 && admissionClassifications.length > 0 && departments.length > 0 && fieldsOfStudy.length > 0) {
      if (isEditMode && graduateData) {
        console.log('=== EDIT MODE DEBUG START ===');
        console.log('Setting form values for edit mode:', graduateData);
        console.log('Graduate college data:', graduateData.college);
        console.log('Graduate department data:', graduateData.department);
        console.log('Graduate field_of_study data:', graduateData.field_of_study);
        console.log('Graduate program data:', graduateData.program);
        console.log('Graduate admission_classification data:', graduateData.admission_classification);

        // Log the raw data types
        console.log('College type:', typeof graduateData.college, graduateData.college);
        console.log('Department type:', typeof graduateData.department, graduateData.department);
        console.log('Field of study type:', typeof graduateData.field_of_study, graduateData.field_of_study);
        console.log('Program type:', typeof graduateData.program, graduateData.program);
        console.log('Admission classification type:', typeof graduateData.admission_classification, graduateData.admission_classification);

        // Set form values for edit mode
        // Handle both nested object structure (from detail API) and flat structure (from list API)
        const extractId = (field: any, fieldName: string) => {
          console.log(`Extracting ID for ${fieldName}:`, field);

          if (!field) {
            console.log(`${fieldName}: field is null/undefined, returning empty string`);
            return '';
          }

          // If it's already a number or string ID
          if (typeof field === 'number' || typeof field === 'string') {
            console.log(`${fieldName}: field is number/string, returning:`, field.toString());
            return field.toString();
          }

          // If it's an object with id property
          if (typeof field === 'object' && field.id) {
            console.log(`${fieldName}: field is object with id, returning:`, field.id.toString());
            return field.id.toString();
          }

          // Fallback to checking for field_id pattern
          const idField = `${fieldName}_id`;
          if (graduateData[idField]) {
            console.log(`${fieldName}: using fallback ${idField}, returning:`, graduateData[idField].toString());
            return graduateData[idField].toString();
          }

          console.log(`${fieldName}: no valid ID found, returning empty string`);
          return '';
        };

        const formValues = {
          student_id: graduateData.student_id || '',
          first_name: graduateData.first_name || '',
          middle_name: graduateData.middle_name || '',
          last_name: graduateData.last_name || '',
          year_of_entry: graduateData.year_of_entry?.toString() || '',
          year_of_graduation: graduateData.year_of_graduation?.toString() || '',
          gpa: graduateData.gpa?.toString() || '',
          gender: graduateData.gender || '',
          college: extractId(graduateData.college, 'college'),
          department: extractId(graduateData.department, 'department'),
          field_of_study: extractId(graduateData.field_of_study, 'field_of_study'),
          program: extractId(graduateData.program, 'program'),
          admission_classification: extractId(graduateData.admission_classification, 'admission_classification'),
        };

        console.log('=== EXTRACTED FORM VALUES ===');
        console.log('Form values to set:', formValues);
        console.log('College ID extracted:', formValues.college);
        console.log('Department ID extracted:', formValues.department);
        console.log('Field of study ID extracted:', formValues.field_of_study);
        console.log('Program ID extracted:', formValues.program);
        console.log('Admission classification ID extracted:', formValues.admission_classification);

        console.log('=== AVAILABLE OPTIONS ===');
        console.log('Available colleges:', colleges.map(c => ({ id: c.id, name: c.name })));
        console.log('Available departments:', departments.map(d => ({ id: d.id, name: d.name })));
        console.log('Available programs:', programs.map(p => ({ id: p.id, name: p.name })));
        console.log('Available admission classifications:', admissionClassifications.map(a => ({ id: a.id, name: a.name })));
        console.log('=== DEBUG END ===');

        // Store form values for defaultValue usage
        setEditFormValues(formValues);

        // Use setTimeout to ensure DOM is updated
        setTimeout(() => {
          // Clear any existing validation errors first
          form.clearErrors();

          // First, reset the form with all values at once
          form.reset(formValues);
          console.log('Reset form with all values:', formValues);

          // Set cascading fields with validation disabled initially
          if (formValues.college) {
            form.setValue('college', formValues.college, { shouldValidate: false });
            console.log('Set college value:', formValues.college);

            // Wait for department filtering to complete, then set department
            setTimeout(() => {
              if (formValues.department) {
                form.setValue('department', formValues.department, { shouldValidate: false });
                console.log('Set department value:', formValues.department);

                // Wait for field of study filtering to complete, then set field of study
                setTimeout(() => {
                  if (formValues.field_of_study) {
                    form.setValue('field_of_study', formValues.field_of_study, { shouldValidate: false });
                    console.log('Set field of study value:', formValues.field_of_study);
                  }

                  // After all values are set, trigger validation
                  setTimeout(() => {
                    form.trigger();
                    console.log('Triggered form validation after setting all values');
                  }, 100);
                }, 150);
              } else {
                // If no department value, still trigger validation
                setTimeout(() => {
                  form.trigger();
                  console.log('Triggered form validation after setting college (no department)');
                }, 100);
              }
            }, 150);
          } else {
            // If no college value, still trigger validation
            setTimeout(() => {
              form.trigger();
              console.log('Triggered form validation (no college value)');
            }, 100);
          }

          // Verify form values were set correctly
          setTimeout(() => {
            console.log('Form values after setting:', form.getValues());
            console.log('College field value:', form.getValues('college'));
            console.log('Department field value:', form.getValues('department'));
            console.log('Field of study field value:', form.getValues('field_of_study'));
            console.log('Program field value:', form.getValues('program'));
            console.log('Admission classification field value:', form.getValues('admission_classification'));

            // Check if the values exist in the available options
            const collegeValue = form.getValues('college');
            const departmentValue = form.getValues('department');
            const fieldOfStudyValue = form.getValues('field_of_study');
            const programValue = form.getValues('program');
            const admissionClassificationValue = form.getValues('admission_classification');

            console.log('College exists in options:', colleges.some(c => c.id.toString() === collegeValue));
            console.log('Department exists in filtered options:', filteredDepartments.some(d => d.id.toString() === departmentValue));
            console.log('Field of study exists in filtered options:', filteredFieldsOfStudy.some(f => f.id.toString() === fieldOfStudyValue));
            console.log('Program exists in options:', programs.some(p => p.id.toString() === programValue));
            console.log('Admission classification exists in options:', admissionClassifications.some(a => a.id.toString() === admissionClassificationValue));

            // Force trigger form state update and clear any remaining validation errors
            form.trigger();

            // Final validation clear for edit mode
            if (isEditMode) {
              setTimeout(() => {
                form.clearErrors();
                console.log('Cleared validation errors for edit mode');
              }, 200);
            }
          }, 800);
        }, 200);
      } else {
        // Reset form for create mode
        form.reset({
          student_id: '',
          first_name: '',
          middle_name: '',
          last_name: '',
          year_of_entry: '',
          year_of_graduation: '',
          gpa: '',
          gender: '',
          college: '',
          department: '',
          field_of_study: '',
          program: '',
          admission_classification: '',
        });
      }
    }
  }, [isOpen, isEditMode, graduateData, form, isLoading, colleges.length, programs.length, admissionClassifications.length, departments.length, fieldsOfStudy.length]);

  // Filter departments when college changes
  useEffect(() => {
    const collegeId = form.watch('college');
    if (collegeId) {
      const filtered = departments.filter(dept => dept.college === parseInt(collegeId));
      setFilteredDepartments(filtered);
    } else {
      setFilteredDepartments(departments);
    }
  }, [form.watch('college'), departments]);

  // Filter fields of study when department changes
  useEffect(() => {
    const departmentId = form.watch('department');
    if (departmentId) {
      const filtered = fieldsOfStudy.filter(field => field.department === parseInt(departmentId));
      setFilteredFieldsOfStudy(filtered);
    } else {
      setFilteredFieldsOfStudy(fieldsOfStudy);
    }
  }, [form.watch('department'), fieldsOfStudy]);

  // Initialize filtered options for edit mode
  useEffect(() => {
    if (isEditMode && graduateData && departments.length > 0 && fieldsOfStudy.length > 0) {
      console.log('Initializing filtered options for edit mode');

      // Helper function to extract ID (same as in form values)
      const extractId = (field: any, fieldName: string) => {
        if (!field) return null;

        // If it's already a number or string ID
        if (typeof field === 'number' || typeof field === 'string') {
          return parseInt(field.toString());
        }

        // If it's an object with id property
        if (typeof field === 'object' && field.id) {
          return parseInt(field.id.toString());
        }

        // Fallback to checking for field_id pattern
        const idField = `${fieldName}_id`;
        if (graduateData[idField]) {
          return parseInt(graduateData[idField].toString());
        }

        return null;
      };

      // Filter departments based on the graduate's college
      const collegeId = extractId(graduateData.college, 'college');
      if (collegeId) {
        const filtered = departments.filter(dept => dept.college === collegeId);
        console.log('Filtered departments for college', collegeId, ':', filtered);
        setFilteredDepartments(filtered);
      }

      // Filter fields of study based on the graduate's department
      const departmentId = extractId(graduateData.department, 'department');
      if (departmentId) {
        const filtered = fieldsOfStudy.filter(field => field.department === departmentId);
        console.log('Filtered fields of study for department', departmentId, ':', filtered);
        setFilteredFieldsOfStudy(filtered);
      }
    }
  }, [isEditMode, graduateData, departments, fieldsOfStudy]);

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof graduateFormSchema>) => {
    setIsSubmitting(true);
    try {
      console.log('Form submission data:', data);
      console.log('Is edit mode:', isEditMode);
      console.log('Graduate data:', graduateData);

      // Convert string values to appropriate types
      const formattedData = {
        ...data,
        year_of_entry: parseInt(data.year_of_entry),
        year_of_graduation: parseInt(data.year_of_graduation),
        gpa: parseFloat(data.gpa),
        college: parseInt(data.college),
        department: parseInt(data.department),
        field_of_study: parseInt(data.field_of_study),
        program: parseInt(data.program),
        admission_classification: parseInt(data.admission_classification),
      };

      console.log('Formatted data for API:', formattedData);

      if (isEditMode && graduateData) {
        console.log('Updating graduate with ID:', graduateData.id);
        // Update existing graduate
        const response = await graduateVerificationAPI.updateGraduate(graduateData.id, formattedData);
        console.log('Update response:', response);
        toast.success('Graduate updated successfully');
      } else {
        console.log('Creating new graduate');
        // Create new graduate
        const response = await graduateVerificationAPI.createGraduate(formattedData);
        console.log('Create response:', response);
        toast.success('Graduate created successfully');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
      console.error('Error details:', error.response?.data);

      // Show more specific error message
      let errorMessage = isEditMode ? 'Failed to update graduate' : 'Failed to create graduate';
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        } else {
          // Show field-specific errors
          const fieldErrors = Object.entries(error.response.data)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          if (fieldErrors) {
            errorMessage = fieldErrors;
          }
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] sm:max-w-2xl lg:max-w-4xl mx-4 sm:mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b sticky top-0 z-10">
          <div className="flex items-start space-x-3">
            <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
              {isEditMode ? (
                <Pencil className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              ) : (
                <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">
                {isEditMode ? 'Edit Graduate' : 'Add New Graduate'}
              </DialogTitle>
              <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                {isEditMode
                  ? 'Update the graduate information in the form below.'
                  : 'Fill in the graduate information in the form below.'}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading form data...</span>
          </div>
        ) : (
          <div className="p-4 sm:p-5 lg:p-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
                {/* Student ID */}
                <FormField
                  control={form.control}
                  name="student_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Student ID</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter student ID" autoComplete="off" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Gender */}
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gender</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        key={`gender-${isEditMode ? editFormValues?.gender || 'edit' : 'new'}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Male">Male</SelectItem>
                          <SelectItem value="Female">Female</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* First Name */}
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter first name" autoComplete="given-name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Middle Name */}
                <FormField
                  control={form.control}
                  name="middle_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Middle Name (Optional)</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter middle name" autoComplete="additional-name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Last Name */}
                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter last name" autoComplete="family-name" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Year of Entry */}
                <FormField
                  control={form.control}
                  name="year_of_entry"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Year of Entry</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter entry year"
                          type="number"
                          min="2000"
                          max="2100"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Year of Graduation */}
                <FormField
                  control={form.control}
                  name="year_of_graduation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Year of Graduation</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter graduation year"
                          type="number"
                          min="2000"
                          max="2100"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* GPA */}
                <FormField
                  control={form.control}
                  name="gpa"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>GPA</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter GPA (2.0-4.0)"
                          type="number"
                          step="0.01"
                          min="2.0"
                          max="4.0"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* College */}
                <FormField
                  control={form.control}
                  name="college"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>College</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        key={`college-${isEditMode ? editFormValues?.college || 'edit' : 'new'}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select college" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {colleges.map((college) => (
                            <SelectItem key={college.id} value={college.id.toString()}>
                              {college.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Department */}
                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={!form.watch('college')}
                        key={`department-${isEditMode ? editFormValues?.department || 'edit' : 'new'}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {filteredDepartments.map((department) => (
                            <SelectItem key={department.id} value={department.id.toString()}>
                              {department.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Field of Study */}
                <FormField
                  control={form.control}
                  name="field_of_study"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Field of Study</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={!form.watch('department')}
                        key={`field_of_study-${isEditMode ? editFormValues?.field_of_study || 'edit' : 'new'}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select field of study" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {filteredFieldsOfStudy.map((fieldOfStudy) => (
                            <SelectItem key={fieldOfStudy.id} value={fieldOfStudy.id.toString()}>
                              {fieldOfStudy.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Program */}
                <FormField
                  control={form.control}
                  name="program"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Program</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        key={`program-${isEditMode ? editFormValues?.program || 'edit' : 'new'}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select program" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {programs.map((program) => (
                            <SelectItem key={program.id} value={program.id.toString()}>
                              {program.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Admission Classification */}
                <FormField
                  control={form.control}
                  name="admission_classification"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admission Classification</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        key={`admission_classification-${isEditMode ? editFormValues?.admission_classification || 'edit' : 'new'}`}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select classification" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {admissionClassifications.map((classification) => (
                            <SelectItem key={classification.id} value={classification.id.toString()}>
                              {classification.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

                <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t mt-6 sticky bottom-0 z-10">
                  <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={onClose}
                      className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {isEditMode ? (
                        <>
                          <Pencil className="h-4 w-4 mr-2" />
                          Update Graduate
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Graduate
                        </>
                      )}
                    </Button>
                  </div>
                </DialogFooter>
              </form>
            </Form>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default GraduateForm;
