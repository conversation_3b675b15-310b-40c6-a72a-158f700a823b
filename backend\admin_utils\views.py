from django.db import connection
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAdminUser
from django.db import models
from django.apps import apps

class ResetSequenceView(APIView):
    """
    View to reset a database sequence for a specific table.
    This is useful when you encounter "duplicate key value violates unique constraint" errors.
    """
    permission_classes = [IsAdminUser]
    
    def post(self, request):
        table_name = request.data.get('table_name')
        
        if not table_name:
            return Response(
                {"error": "table_name is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get the model for the table
            model = None
            for app_config in apps.get_app_configs():
                try:
                    model = apps.get_model(app_config.label, table_name)
                    break
                except LookupError:
                    continue
            
            if not model:
                return Response(
                    {"error": f"Model for table {table_name} not found"}, 
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Get the actual table name from the model's Meta
            db_table = model._meta.db_table
            
            # Get the primary key field name
            pk_field = model._meta.pk.name
            
            # Execute SQL to reset the sequence
            with connection.cursor() as cursor:
                # For PostgreSQL
                cursor.execute(
                    f"SELECT setval(pg_get_serial_sequence('{db_table}', '{pk_field}'), "
                    f"(SELECT MAX({pk_field}) FROM {db_table}));"
                )
            
            return Response(
                {"message": f"Sequence for {table_name} reset successfully"}, 
                status=status.HTTP_200_OK
            )
        
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class CheckSequenceView(APIView):
    """
    View to check if a database sequence is out of sync for a specific table.
    """
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        table_name = request.query_params.get('table_name')
        
        if not table_name:
            return Response(
                {"error": "table_name is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get the model for the table
            model = None
            for app_config in apps.get_app_configs():
                try:
                    model = apps.get_model(app_config.label, table_name)
                    break
                except LookupError:
                    continue
            
            if not model:
                return Response(
                    {"error": f"Model for table {table_name} not found"}, 
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Get the actual table name from the model's Meta
            db_table = model._meta.db_table
            
            # Get the primary key field name
            pk_field = model._meta.pk.name
            
            # Execute SQL to check the sequence
            with connection.cursor() as cursor:
                # For PostgreSQL
                cursor.execute(
                    f"SELECT MAX({pk_field}) as max_id, "
                    f"nextval(pg_get_serial_sequence('{db_table}', '{pk_field}')) as next_id "
                    f"FROM {db_table};"
                )
                row = cursor.fetchone()
                max_id = row[0] or 0
                next_id = row[1]
            
            is_out_of_sync = max_id >= next_id
            
            return Response({
                "table_name": table_name,
                "max_id": max_id,
                "next_id": next_id,
                "is_out_of_sync": is_out_of_sync
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class NextIdView(APIView):
    """
    View to get the next available ID for a specific table.
    """
    permission_classes = [IsAdminUser]
    
    def get(self, request):
        table_name = request.query_params.get('table_name')
        
        if not table_name:
            return Response(
                {"error": "table_name is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get the model for the table
            model = None
            for app_config in apps.get_app_configs():
                try:
                    model = apps.get_model(app_config.label, table_name)
                    break
                except LookupError:
                    continue
            
            if not model:
                return Response(
                    {"error": f"Model for table {table_name} not found"}, 
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Get the maximum ID and add 1
            max_id = model.objects.all().aggregate(models.Max(model._meta.pk.name))[f"{model._meta.pk.name}__max"] or 0
            next_id = max_id + 1
            
            return Response({
                "table_name": table_name,
                "max_id": max_id,
                "next_id": next_id
            }, status=status.HTTP_200_OK)
        
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
