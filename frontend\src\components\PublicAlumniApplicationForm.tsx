import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, memo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import {
  User,
  School,
  CreditCard,
  MapPin,
  Save,
  Loader2,
  FileText,
  Upload,
  X,
  CheckCircle,
  AlertCircle,
  Eye,
  ArrowLeft,
  ArrowRight,
  Check,
  Shield
} from 'lucide-react';
import {
  publicAlumniApplicationsAPI,
  AlumniApplication,
  AlumniApplicationMini,
  ServiceType,
  College,
  Department,
  DocumentType
} from '@/services/alumniApplicationsAPI';
import { FileSecurityValidator, UploadRateLimiter, DEFAULT_SECURITY_CONFIG } from '@/utils/fileSecurityUtils';
import { useAlumniFormData } from '@/hooks/useAlumniFormData';
import { useFormValidation, ValidationRules } from '@/hooks/useFormValidation';
import { PerformanceMonitor } from '@/components/PerformanceMonitor';

interface PublicAlumniApplicationFormProps {
  formType: 'form1' | 'form2';
}

// Validation rules for the form
const getValidationRules = (formType: 'form1' | 'form2'): ValidationRules => ({
  first_name: { required: true, minLength: 2, maxLength: 50, pattern: /^[a-zA-Z\s'-]+$/ },
  father_name: { required: true, minLength: 2, maxLength: 50, pattern: /^[a-zA-Z\s'-]+$/ },
  last_name: { required: true, minLength: 2, maxLength: 50, pattern: /^[a-zA-Z\s'-]+$/ },
  email: { required: true, email: true, maxLength: 100 },
  phone_number: { required: true, phone: true },
  student_id: { required: false, pattern: /^[a-zA-Z0-9/-]+$/, maxLength: 20 },
  admission_type: { required: true },
  degree_type: { required: true },
  student_status: { required: true },
  service_type: { required: true },
  college: {
    required: true,
    custom: (value, formData) => {
      if (formData.is_other_college) return null;
      return value ? null : 'College is required';
    }
  },
  department: {
    required: true,
    custom: (value, formData) => {
      if (formData.is_other_college) return null;
      return value ? null : 'Department is required';
    }
  },
  other_college_name: {
    custom: (value, formData) => {
      if (!formData.is_other_college) return null;
      return value ? null : 'College name is required';
    }
  },
  other_department_name: {
    custom: (value, formData) => {
      if (!formData.is_other_college) return null;
      return value ? null : 'Department name is required';
    }
  },
  current_year: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Active') return null;
      return value ? null : 'Current year is required for active students';
    }
  },
  year_of_leaving_ethiopian: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Inactive') return null;
      return value ? null : 'Year of leaving (Ethiopian) is required for inactive students';
    }
  },
  year_of_leaving_gregorian: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Inactive') return null;
      return value ? null : 'Year of leaving (Gregorian) is required for inactive students';
    }
  },
  year_of_graduation_ethiopian: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Graduated') return null;
      return value ? null : 'Year of graduation (Ethiopian) is required for graduated students';
    }
  },
  year_of_graduation_gregorian: {
    custom: (value, formData) => {
      if (formData.student_status !== 'Graduated') return null;
      return value ? null : 'Year of graduation (Gregorian) is required for graduated students';
    }
  },
  // Form1 specific validations
  ...(formType === 'form1' && {
    order_type: {
      custom: (value, formData) => {
        if (formData.is_uog_destination) return null;
        return value ? null : 'Order type is required for external destinations';
      }
    },
    institution_name: {
      custom: (value, formData) => {
        if (formData.is_uog_destination) return null;
        return value ? null : 'Institution name is required for external destinations';
      }
    },
    country: {
      custom: (value, formData) => {
        if (formData.is_uog_destination) return null;
        return value ? null : 'Country is required for external destinations';
      }
    },
    institution_address: {
      custom: (value, formData) => {
        if (formData.is_uog_destination) return null;
        return value ? null : 'Institution address is required for external destinations';
      }
    },
    mailing_agent: {
      custom: (value, formData) => {
        if (formData.is_uog_destination) return null;
        return value ? null : 'Mailing agent is required for external destinations';
      }
    },
    uog_college: {
      custom: (value, formData) => {
        if (!formData.is_uog_destination) return null;
        return value ? null : 'UoG college is required for UoG destinations';
      }
    },
    uog_department: {
      custom: (value, formData) => {
        if (!formData.is_uog_destination) return null;
        return value ? null : 'UoG department is required for UoG destinations';
      }
    }
  })
});

const PublicAlumniApplicationForm: React.FC<PublicAlumniApplicationFormProps> = memo(({ formType }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [submitting, setSubmitting] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<{[key: string]: File}>({});

  // Initialize form data with default values
  const initialFormData = useMemo(() => ({
    first_name: '',
    father_name: '',
    last_name: '',
    student_id: '',
    phone_number: '',
    email: '',
    admission_type: '',
    degree_type: '',
    college: '',
    department: '',
    is_other_college: false,
    other_college_name: '',
    other_department_name: '',
    student_status: '',
    current_year: '',
    year_of_leaving_ethiopian: '',
    year_of_leaving_gregorian: '',
    year_of_graduation_ethiopian: '',
    year_of_graduation_gregorian: '',
    service_type: '',
    application_status: 'Pending',
    payment_status: 'Pending',
    // Form1 specific fields
    ...(formType === 'form1' && {
      is_uog_destination: false,
      uog_college: '',
      uog_department: '',
      order_type: '',
      institution_name: '',
      country: '',
      institution_address: '',
      mailing_agent: '',
    })
  }), [formType]);

  // Use custom hooks for optimized data management
  const {
    serviceTypes,
    colleges,
    departments,
    requiredDocuments,
    loading,
    loadDepartments,
    loadRequiredDocuments
  } = useAlumniFormData();

  // Use form validation hook
  const validationRules = useMemo(() => getValidationRules(formType), [formType]);
  const {
    formData,
    errors: validationErrors,
    updateField,
    validateForm,
    isValid: isFormValid,
    resetForm
  } = useFormValidation(initialFormData, validationRules, {
    debounceMs: 300,
    validateOnChange: true,
    sanitizeInputs: true
  });

  // Security components
  const [fileValidator] = useState(() => new FileSecurityValidator(DEFAULT_SECURITY_CONFIG));
  const [rateLimiter] = useState(() => new UploadRateLimiter(10, 60000));

  const isForm1 = formType === 'form1';

  // Define steps configuration
  const steps = [
    {
      id: 'personal',
      title: 'Personal Information',
      icon: User,
      description: 'Basic personal details',
      color: 'blue'
    },
    {
      id: 'academic',
      title: 'Academic Information',
      icon: School,
      description: 'Educational background',
      color: 'blue'
    },
    {
      id: 'service',
      title: 'Service Information',
      icon: FileText,
      description: 'Service selection and documents',
      color: 'blue'
    },
    ...(isForm1 ? [{
      id: 'destination',
      title: 'Destination',
      icon: MapPin,
      description: 'Delivery information',
      color: 'blue'
    }] : [])
  ];

  const totalSteps = steps.length;

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < totalSteps - 1) {
      // Mark current step as completed
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  // Validation for each step
  const validateStep = (stepIndex: number): boolean => {
    const step = steps[stepIndex];

    switch (step.id) {
      case 'personal':
        return !!(formData.first_name && formData.father_name && formData.last_name && formData.email && formData.phone_number);
      case 'academic':
        const hasBasicAcademicInfo = !!(formData.admission_type && formData.degree_type && formData.student_status);
        const hasCollegeInfo = formData.is_other_college
          ? !!(formData.other_college_name && formData.other_department_name)
          : !!(formData.college && formData.department);

        let hasStatusSpecificInfo = true;
        if (formData.student_status === 'Active') {
          hasStatusSpecificInfo = !!formData.current_year;
        } else if (formData.student_status === 'Inactive') {
          hasStatusSpecificInfo = !!(formData.year_of_leaving_ethiopian && formData.year_of_leaving_gregorian);
        } else if (formData.student_status === 'Graduated') {
          hasStatusSpecificInfo = !!(formData.year_of_graduation_ethiopian && formData.year_of_graduation_gregorian);
        }

        return hasBasicAcademicInfo && hasCollegeInfo && hasStatusSpecificInfo;
      case 'service':
        // Check if service type is selected
        if (!formData.service_type) {
          return false;
        }

        // Check if all required documents are uploaded
        if (currentRequiredDocuments.length > 0) {
          const uploadedDocumentTypes = Object.keys(uploadedDocuments);
          const allRequiredUploaded = currentRequiredDocuments.every(docType =>
            uploadedDocumentTypes.includes(docType.name)
          );
          return allRequiredUploaded;
        }

        return true;
      case 'destination':
        if (!isForm1) return true;

        const alumniFormData = formData as AlumniApplication;

        // Check if destination type is selected
        if (alumniFormData.is_uog_destination === undefined || alumniFormData.is_uog_destination === null) {
          return false;
        }

        // Validate UoG destination
        if (alumniFormData.is_uog_destination) {
          return !!(alumniFormData.uog_college && alumniFormData.uog_department);
        }

        // Validate external destination
        return !!(
          alumniFormData.order_type &&
          alumniFormData.institution_name &&
          alumniFormData.country &&
          alumniFormData.institution_address &&
          alumniFormData.mailing_agent
        );
      default:
        return true;
    }
  };

  const isStepValid = validateStep(currentStep);
  const canProceed = isStepValid;

  // Get validation message for current step
  const getValidationMessage = (): string => {
    const step = steps[currentStep];

    switch (step.id) {
      case 'service':
        if (!formData.service_type) {
          return 'Please select a service type to continue';
        }
        if (currentRequiredDocuments.length > 0) {
          const uploadedDocumentTypes = Object.keys(uploadedDocuments);
          const missingDocuments = currentRequiredDocuments.filter(docType =>
            !uploadedDocumentTypes.includes(docType.name)
          );
          if (missingDocuments.length > 0) {
            return `Please upload all required documents: ${missingDocuments.map(doc => doc.name).join(', ')}`;
          }
        }
        return 'Please fill in all required fields to continue';
      default:
        return 'Please fill in all required fields to continue';
    }
  };

  // Get required documents for current service type
  const currentRequiredDocuments = useMemo(() => {
    if (!formData.service_type) return [];
    return requiredDocuments[formData.service_type as string] || [];
  }, [formData.service_type, requiredDocuments]);

  // Get departments for current college
  const currentDepartments = useMemo(() => {
    if (!formData.college || formData.is_other_college) return [];
    return departments[formData.college as string] || [];
  }, [formData.college, formData.is_other_college, departments]);

  // Get UoG departments for destination (Form1 only)
  const uogDepartments = useMemo(() => {
    if (!isForm1) return [];
    const alumniFormData = formData as AlumniApplication;
    if (!alumniFormData.uog_college || !alumniFormData.is_uog_destination) return [];
    return departments[alumniFormData.uog_college as string] || [];
  }, [isForm1, formData, departments]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);

      // Basic validation
      if (!formData.first_name || !formData.last_name || !formData.email || !formData.phone_number) {
        toast.error('Please fill in all required fields');
        return;
      }

      if (!formData.service_type) {
        toast.error('Please select a service type');
        return;
      }

      // Prepare data for submission
      const submissionData = { ...formData };

      // Remove transaction_id as it's auto-generated by backend
      delete (submissionData as any).transaction_id;

      // Convert year strings to integers for Gregorian years
      if (submissionData.year_of_leaving_gregorian) {
        submissionData.year_of_leaving_gregorian = parseInt(submissionData.year_of_leaving_gregorian as string);
      }
      if (submissionData.year_of_graduation_gregorian) {
        submissionData.year_of_graduation_gregorian = parseInt(submissionData.year_of_graduation_gregorian as string);
      }

      // Clean up empty string values to null for optional fields
      Object.keys(submissionData).forEach(key => {
        if (submissionData[key as keyof typeof submissionData] === '') {
          (submissionData as any)[key] = null;
        }
      });

      // Submit application
      if (isForm1) {
        await publicAlumniApplicationsAPI.createApplication(submissionData);
      } else {
        await publicAlumniApplicationsAPI.createMiniApplication(submissionData);
      }

      toast.success('Application submitted successfully!');
      
      // Redirect to a success page or back to services
      navigate('/services?success=true');

    } catch (error: any) {
      console.error('Error submitting application:', error);

      if (error.response?.data) {
        const errorData = error.response.data;

        // Handle specific error cases
        if (errorData.email && errorData.email.includes('already exists')) {
          toast.error('An application with this email address already exists. Please use a different email or contact support if this is your application.');
        } else {
          // Handle other validation errors
          const errorMessages: string[] = [];
          Object.entries(errorData).forEach(([, messages]) => {
            if (Array.isArray(messages)) {
              errorMessages.push(...messages);
            } else if (typeof messages === 'string') {
              errorMessages.push(messages);
            }
          });

          if (errorMessages.length > 0) {
            toast.error(`Please fix the following errors: ${errorMessages.join(', ')}`);
          } else {
            toast.error('Failed to submit application. Please check your information and try again.');
          }
        }
      } else {
        toast.error('Failed to submit application. Please check your internet connection and try again.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Handle input changes with validation and performance optimization
  const handleInputChange = useCallback((field: string, value: any) => {
    updateField(field, value);

    // Trigger dependent data loading
    if (field === 'college' && !formData.is_other_college) {
      loadDepartments(value);
    } else if (field === 'service_type') {
      loadRequiredDocuments(value);
      // Clear uploaded documents when service type changes
      setUploadedDocuments({});
    } else if (field === 'uog_college' && formData.is_uog_destination) {
      loadDepartments(value);
    }
  }, [updateField, formData.is_other_college, formData.is_uog_destination, loadDepartments, loadRequiredDocuments]);

  // Enhanced file upload with comprehensive security
  const handleFileUpload = useCallback(async (documentTypeName: string, file: File) => {
    try {
      // Check rate limiting
      if (!rateLimiter.canUpload()) {
        const timeUntilReset = Math.ceil(rateLimiter.getTimeUntilReset() / 1000);
        toast.error(`Upload rate limit exceeded. Please wait ${timeUntilReset} seconds before trying again.`);
        return;
      }

      // Show loading state
      toast.loading('Validating file...', { id: `validate-${documentTypeName}` });

      // Comprehensive security validation
      const validation = await fileValidator.validateFile(file);

      toast.dismiss(`validate-${documentTypeName}`);

      if (!validation.isValid) {
        toast.error(`Security validation failed: ${validation.errors.join(', ')}`);
        return;
      }

      // Show warnings if any
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          toast.warning(warning);
        });
      }

      // Record upload attempt
      rateLimiter.recordAttempt();

      // Store file with security metadata
      const fileWithMetadata = Object.assign(file, {
        clientHash: validation.fileInfo.hash,
        uploadTimestamp: new Date().toISOString(),
        sanitizedName: validation.fileInfo.sanitizedName,
        securityScore: validation.fileInfo.securityScore,
        securityValidated: true,
        validationResult: validation
      });

      setUploadedDocuments(prev => ({
        ...prev,
        [documentTypeName]: fileWithMetadata
      }));

      toast.success(`${documentTypeName} validated and ready for upload`, {
        description: `Security score: ${validation.fileInfo.securityScore}/100 (${validation.performance.validationTime.toFixed(0)}ms)`
      });

    } catch (error) {
      console.error('Error in file upload validation:', error);
      toast.error('Failed to validate file. Please try again.');
    }
  }, [fileValidator, rateLimiter]);

  // Remove uploaded file
  const removeUploadedFile = (documentTypeName: string) => {
    setUploadedDocuments(prev => {
      const newUploads = { ...prev };
      delete newUploads[documentTypeName];
      return newUploads;
    });
  };

  if (loading.serviceTypes || loading.colleges) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="bg-gradient-to-br from-[#1a73c0] to-[#155a9c] p-4 rounded-2xl w-20 h-20 mx-auto mb-6 flex items-center justify-center">
            <Loader2 className="h-10 w-10 animate-spin text-white" />
          </div>
          <p className="text-gray-600 text-lg font-medium">Loading application form...</p>
          <p className="text-gray-500 text-sm mt-2">Please wait while we prepare your form</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 max-w-5xl py-8">
        {/* Enhanced Header */}
        <div className="mb-8">
          <Button
            variant="outline"
            onClick={() => navigate('/services')}
            className="mb-6 border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0] hover:text-white transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Button>

          {/* Enhanced Form Header Card */}
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] text-white relative overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0 bg-white transform rotate-12 scale-150"></div>
              </div>

              <div className="relative z-10 flex items-center space-x-4">
                <div className="p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-2xl font-bold text-white mb-2">
                    Alumni Application
                  </CardTitle>
                  <CardDescription className="text-blue-100 text-base">
                    {isForm1
                      ? 'Application form for official transcripts and comprehensive academic services'
                      : 'Application form for basic services and certificate requests'
                    }
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Enhanced Multi-Step Form */}
        <Card className="border-0 shadow-2xl bg-white rounded-3xl overflow-hidden">
          <CardContent className="p-8">
            {/* Progress Indicator */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Step {currentStep + 1} of {totalSteps}
                </h3>
                <span className="text-sm text-gray-500">
                  {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
                </span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
                <div
                  className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
                ></div>
              </div>

              {/* Step Indicators */}
              <div className="flex items-center justify-between">
                {steps.map((step, index) => {
                  const StepIcon = step.icon;
                  const isActive = index === currentStep;
                  const isCompleted = completedSteps.includes(index);
                  const isAccessible = index <= currentStep || completedSteps.includes(index);

                  return (
                    <div key={step.id} className="flex flex-col items-center">
                      <button
                        type="button"
                        onClick={() => isAccessible && goToStep(index)}
                        disabled={!isAccessible}
                        className={`
                          w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-all duration-200
                          ${isActive
                            ? 'bg-gradient-to-r from-[#1a73c0] to-[#155a9c] text-white shadow-lg scale-110'
                            : isCompleted
                            ? 'bg-green-500 text-white shadow-md hover:shadow-lg'
                            : isAccessible
                            ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          }
                        `}
                      >
                        {isCompleted && !isActive ? (
                          <Check className="h-5 w-5" />
                        ) : (
                          <StepIcon className="h-5 w-5" />
                        )}
                      </button>
                      <span className={`text-xs text-center max-w-20 leading-tight ${
                        isActive ? 'text-[#1a73c0] font-semibold' :
                        isCompleted ? 'text-green-600 font-medium' :
                        'text-gray-500'
                      }`}>
                        {step.title}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Current Step Content */}
              <div className="min-h-[400px]">

                {/* Step Content */}
                {currentStep === 0 && (
                  <div className="space-y-6">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                    <h3 className="text-lg font-semibold text-[#1a73c0] mb-4 flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Personal Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="first_name" className="text-sm font-medium text-gray-700">
                          First Name <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="first_name"
                          value={formData.first_name || ''}
                          onChange={(e) => handleInputChange('first_name', e.target.value)}
                          required
                          className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="father_name" className="text-sm font-medium text-gray-700">
                          Father Name <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="father_name"
                          value={formData.father_name || ''}
                          onChange={(e) => handleInputChange('father_name', e.target.value)}
                          required
                          className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          placeholder="Enter your father's name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="last_name" className="text-sm font-medium text-gray-700">
                          Last Name <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="last_name"
                          value={formData.last_name || ''}
                          onChange={(e) => handleInputChange('last_name', e.target.value)}
                          required
                          className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          placeholder="Enter your last name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="student_id" className="text-sm font-medium text-gray-700">
                          Student ID <span className="text-gray-400">(Optional)</span>
                        </Label>
                        <Input
                          id="student_id"
                          value={formData.student_id || ''}
                          onChange={(e) => handleInputChange('student_id', e.target.value)}
                          className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          placeholder="Enter your student ID"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone_number" className="text-sm font-medium text-gray-700">
                          Phone Number <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="phone_number"
                          value={formData.phone_number || ''}
                          onChange={(e) => handleInputChange('phone_number', e.target.value)}
                          required
                          className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          placeholder="Enter your phone number"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                          Email Address <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email || ''}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          required
                          className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          placeholder="Enter your email address"
                        />
                      </div>
                    </div>
                  </div>
                  </div>
                )}

                {/* Academic Information Step */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                    <h3 className="text-lg font-semibold text-[#1a73c0] mb-4 flex items-center">
                      <School className="h-5 w-5 mr-2" />
                      Academic Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="admission_type" className="text-sm font-medium text-gray-700">
                          Admission Type <span className="text-red-500">*</span>
                        </Label>
                        <Select value={formData.admission_type || ''} onValueChange={(value) => handleInputChange('admission_type', value)}>
                          <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                            <SelectValue placeholder="Select admission type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Regular">Regular</SelectItem>
                            <SelectItem value="Evening">Evening</SelectItem>
                            <SelectItem value="Summer">Summer</SelectItem>
                            <SelectItem value="Distance">Distance</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="degree_type" className="text-sm font-medium text-gray-700">
                          Degree Type <span className="text-red-500">*</span>
                        </Label>
                        <Select value={formData.degree_type || ''} onValueChange={(value) => handleInputChange('degree_type', value)}>
                          <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                            <SelectValue placeholder="Select degree type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Diploma">Diploma</SelectItem>
                            <SelectItem value="Degree">Degree</SelectItem>
                            <SelectItem value="Master's">Master's</SelectItem>
                            <SelectItem value="PHD">PHD</SelectItem>
                            <SelectItem value="Sp.Certificate">Sp.Certificate</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="college" className="text-sm font-medium text-gray-700">
                          College <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={formData.college || ''}
                          onValueChange={(value) => handleInputChange('college', value)}
                          disabled={formData.is_other_college}
                        >
                          <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                            <SelectValue placeholder="Select college" />
                          </SelectTrigger>
                          <SelectContent>
                            {colleges.map((college) => (
                              <SelectItem key={college.id} value={college.id.toString()}>
                                {college.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="department" className="text-sm font-medium text-gray-700">
                          Department <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={formData.department || ''}
                          onValueChange={(value) => handleInputChange('department', value)}
                          disabled={formData.is_other_college}
                        >
                          <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                          <SelectContent>
                            {currentDepartments.map((department) => (
                              <SelectItem key={department.id} value={department.id.toString()}>
                                {department.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* College is not in the list checkbox */}
                    <div className="mt-4">
                      <div className="flex items-center space-x-3 p-4 bg-white rounded-xl border border-blue-200">
                        <Checkbox
                          id="is_other_college"
                          checked={formData.is_other_college || false}
                          onCheckedChange={(checked) => {
                            handleInputChange('is_other_college', checked);
                            if (checked) {
                              handleInputChange('college', '');
                              handleInputChange('department', '');
                            } else {
                              handleInputChange('other_college_name', '');
                              handleInputChange('other_department_name', '');
                            }
                          }}
                          className="border-2 border-blue-300 data-[state=checked]:bg-[#1a73c0] data-[state=checked]:border-[#1a73c0]"
                        />
                        <Label htmlFor="is_other_college" className="text-sm font-medium text-gray-700 cursor-pointer">
                          College is not in the list
                        </Label>
                      </div>
                    </div>

                    {/* Other college and department fields */}
                    {formData.is_other_college && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <div className="space-y-2">
                          <Label htmlFor="other_college_name" className="text-sm font-medium text-gray-700">
                            Other College Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="other_college_name"
                            value={formData.other_college_name || ''}
                            onChange={(e) => handleInputChange('other_college_name', e.target.value)}
                            placeholder="Enter college name"
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="other_department_name" className="text-sm font-medium text-gray-700">
                            Other Department Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="other_department_name"
                            value={formData.other_department_name || ''}
                            onChange={(e) => handleInputChange('other_department_name', e.target.value)}
                            placeholder="Enter department name"
                            className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                          />
                        </div>
                      </div>
                    )}

                    {/* Student Status */}
                    <div className="mt-6">
                      <div className="space-y-2">
                        <Label htmlFor="student_status" className="text-sm font-medium text-gray-700">
                          Student Status <span className="text-red-500">*</span>
                        </Label>
                        <Select value={formData.student_status || ''} onValueChange={(value) => handleInputChange('student_status', value)}>
                          <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                            <SelectValue placeholder="Select student status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Active">Active</SelectItem>
                            <SelectItem value="Inactive">Inactive</SelectItem>
                            <SelectItem value="Graduated">Graduated</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Conditional fields based on student status */}
                    {formData.student_status === 'Active' && (
                      <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="space-y-3">
                          <Label className="text-sm font-medium text-gray-700">
                            Current Year <span className="text-red-500">*</span>
                          </Label>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                            {['1st year', '2nd year', '3rd year', '4th year', '5th year', '6th year', '7th year', '8th year'].map((year) => (
                              <div key={year} className="flex items-center space-x-2">
                                <input
                                  type="radio"
                                  id={`current_year_${year}`}
                                  name="current_year"
                                  value={year}
                                  checked={formData.current_year === year}
                                  onChange={(e) => handleInputChange('current_year', e.target.value)}
                                  className="w-4 h-4 text-[#1a73c0] border-2 border-blue-300 focus:ring-[#1a73c0]"
                                />
                                <Label htmlFor={`current_year_${year}`} className="text-sm text-gray-700 cursor-pointer">
                                  {year}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    {formData.student_status === 'Inactive' && (
                      <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label htmlFor="year_of_leaving_ethiopian" className="text-sm font-medium text-gray-700">
                              Year of Leaving (Ethiopian Calendar) <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="year_of_leaving_ethiopian"
                              value={formData.year_of_leaving_ethiopian || ''}
                              onChange={(e) => handleInputChange('year_of_leaving_ethiopian', e.target.value)}
                              placeholder={`e.g., ${new Date().getFullYear() - 7}`}
                              className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="year_of_leaving_gregorian" className="text-sm font-medium text-gray-700">
                              Year of Leaving (Gregorian Calendar) <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="year_of_leaving_gregorian"
                              type="number"
                              min="1900"
                              max={new Date().getFullYear()}
                              value={formData.year_of_leaving_gregorian || ''}
                              onChange={(e) => handleInputChange('year_of_leaving_gregorian', e.target.value)}
                              placeholder={`e.g., ${new Date().getFullYear()}`}
                              className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {formData.student_status === 'Graduated' && (
                      <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label htmlFor="year_of_graduation_ethiopian" className="text-sm font-medium text-gray-700">
                              Year of Graduation (Ethiopian Calendar) <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="year_of_graduation_ethiopian"
                              value={formData.year_of_graduation_ethiopian || ''}
                              onChange={(e) => handleInputChange('year_of_graduation_ethiopian', e.target.value)}
                              placeholder={`e.g., ${new Date().getFullYear() - 7}`}
                              className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="year_of_graduation_gregorian" className="text-sm font-medium text-gray-700">
                              Year of Graduation (Gregorian Calendar) <span className="text-red-500">*</span>
                            </Label>
                            <Input
                              id="year_of_graduation_gregorian"
                              type="number"
                              min="1900"
                              max={new Date().getFullYear()}
                              value={formData.year_of_graduation_gregorian || ''}
                              onChange={(e) => handleInputChange('year_of_graduation_gregorian', e.target.value)}
                              placeholder={`e.g., ${new Date().getFullYear()}`}
                              className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  </div>
                )}

                {/* Service Information Step */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                    <h3 className="text-lg font-semibold text-[#1a73c0] mb-4 flex items-center">
                      <FileText className="h-5 w-5 mr-2" />
                      Service Information
                    </h3>
                    <div className="space-y-2">
                      <Label htmlFor="service_type" className="text-sm font-medium text-gray-700">
                        Service Type <span className="text-red-500">*</span>
                      </Label>
                      <Select value={formData.service_type || ''} onValueChange={(value) => handleInputChange('service_type', value)}>
                        <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                          <SelectValue placeholder="Select service type" />
                        </SelectTrigger>
                        <SelectContent>
                          {serviceTypes.map((serviceType) => (
                            <SelectItem key={serviceType.id} value={serviceType.id.toString()}>
                              <div className="flex items-center justify-between w-full">
                                <span>{serviceType.name}</span>
                                <span className="text-green-600 font-semibold ml-2">${serviceType.fee}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Required Document Types */}
                    {currentRequiredDocuments.length > 0 && (
                      <div className="mt-6">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="text-lg font-semibold text-[#1a73c0] flex items-center">
                            <Upload className="h-5 w-5 mr-2" />
                            Required Document Types
                          </h4>
                          <div className="flex items-center text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full">
                            <Shield className="h-4 w-4 mr-1" />
                            Security Protected
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {currentRequiredDocuments.map((docType) => (
                            <div key={docType.id} className={`p-4 bg-white rounded-xl border-2 transition-all duration-200 ${
                              uploadedDocuments[docType.name]
                                ? 'border-green-200 bg-green-50'
                                : 'border-red-200 bg-red-50 hover:border-red-300'
                            }`}>
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2">
                                    <h5 className="font-medium text-gray-900">{docType.name}</h5>
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                      Required
                                    </span>
                                  </div>
                                  {docType.description && (
                                    <p className="text-sm text-gray-600 mt-1">{docType.description}</p>
                                  )}
                                </div>
                                {uploadedDocuments[docType.name] ? (
                                  <CheckCircle className="h-5 w-5 text-green-500" />
                                ) : (
                                  <AlertCircle className="h-5 w-5 text-red-500" />
                                )}
                              </div>

                              {uploadedDocuments[docType.name] ? (
                                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                                  <div className="flex items-center space-x-2">
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                    <span className="text-sm font-medium text-green-700">
                                      {uploadedDocuments[docType.name].name}
                                    </span>
                                  </div>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeUploadedFile(docType.name)}
                                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              ) : (
                                <div className="relative">
                                  <input
                                    type="file"
                                    id={`file-${docType.id}`}
                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                    onChange={async (e) => {
                                      const file = e.target.files?.[0];
                                      if (file) {
                                        // Show loading state
                                        toast.loading('Validating file...', { id: `validate-${docType.name}` });

                                        try {
                                          await handleFileUpload(docType.name, file);
                                          toast.dismiss(`validate-${docType.name}`);
                                        } catch (error) {
                                          toast.dismiss(`validate-${docType.name}`);
                                          toast.error('File validation failed');
                                        }
                                      }
                                    }}
                                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                  />
                                  <div className="flex items-center justify-center p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 cursor-pointer">
                                    <div className="text-center">
                                      <Upload className="h-6 w-6 text-[#1a73c0] mx-auto mb-2" />
                                      <p className="text-sm font-medium text-[#1a73c0]">Click to upload</p>
                                      <p className="text-xs text-gray-500 mt-1">PDF, JPG, PNG, DOC (max 10MB)</p>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>

                        {currentRequiredDocuments.length > 0 && (
                          <div className="mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
                            <p className="text-sm text-red-700 flex items-center">
                              <AlertCircle className="h-4 w-4 mr-2" />
                              <strong>Required:</strong> All document types must be uploaded before proceeding to the next step.
                            </p>
                            <div className="mt-2 text-xs text-red-600">
                              Uploaded: {Object.keys(uploadedDocuments).length} of {currentRequiredDocuments.length} required documents
                            </div>
                          </div>
                        )}
                      </div>
                    )}


                  </div>
                  </div>
                )}

                {/* Destination Step (Form1 only) */}
                {isForm1 && currentStep === 3 && (
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100">
                      <h3 className="text-lg font-semibold text-[#1a73c0] mb-4 flex items-center">
                        <MapPin className="h-5 w-5 mr-2" />
                        Destination Information
                      </h3>

                      {/* Destination Type Selection */}
                      <div className="mb-6">
                        <Label className="text-sm font-medium text-gray-700 mb-3 block">
                          Destination Type <span className="text-red-500">*</span>
                        </Label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div
                            className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                              (formData as AlumniApplication).is_uog_destination
                                ? 'border-[#1a73c0] bg-blue-50'
                                : 'border-gray-200 hover:border-blue-300'
                            }`}
                            onClick={() => {
                              handleInputChange('is_uog_destination', true);
                              // Clear external fields
                              handleInputChange('order_type', '');
                              handleInputChange('institution_name', '');
                              handleInputChange('country', '');
                              handleInputChange('institution_address', '');
                              handleInputChange('mailing_agent', '');
                            }}
                          >
                            <div className="flex items-center space-x-3">
                              <input
                                type="radio"
                                name="destination_type"
                                checked={(formData as AlumniApplication).is_uog_destination === true}
                                onChange={() => {}}
                                className="w-4 h-4 text-[#1a73c0] border-2 border-blue-300 focus:ring-[#1a73c0]"
                              />
                              <div>
                                <h4 className="font-medium text-gray-900">University of Gondar</h4>
                                <p className="text-sm text-gray-600">Internal UoG destination</p>
                              </div>
                            </div>
                          </div>
                          <div
                            className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                              (formData as AlumniApplication).is_uog_destination === false
                                ? 'border-[#1a73c0] bg-blue-50'
                                : 'border-gray-200 hover:border-blue-300'
                            }`}
                            onClick={() => {
                              handleInputChange('is_uog_destination', false);
                              // Clear internal fields
                              handleInputChange('uog_college', '');
                              handleInputChange('uog_department', '');
                            }}
                          >
                            <div className="flex items-center space-x-3">
                              <input
                                type="radio"
                                name="destination_type"
                                checked={(formData as AlumniApplication).is_uog_destination === false}
                                onChange={() => {}}
                                className="w-4 h-4 text-[#1a73c0] border-2 border-blue-300 focus:ring-[#1a73c0]"
                              />
                              <div>
                                <h4 className="font-medium text-gray-900">External Institution</h4>
                                <p className="text-sm text-gray-600">External destination</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* UoG Destination Fields */}
                      {(formData as AlumniApplication).is_uog_destination && (
                        <div className="space-y-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                          <h4 className="text-lg font-semibold text-[#1a73c0] mb-4">University of Gondar Destination</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="uog_college" className="text-sm font-medium text-gray-700">
                                UoG College <span className="text-red-500">*</span>
                              </Label>
                              <Select
                                value={(formData as AlumniApplication).uog_college || ''}
                                onValueChange={(value) => handleInputChange('uog_college', value)}
                              >
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select UoG college" />
                                </SelectTrigger>
                                <SelectContent>
                                  {colleges.map((college) => (
                                    <SelectItem key={college.id} value={college.id.toString()}>
                                      {college.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="uog_department" className="text-sm font-medium text-gray-700">
                                UoG Department <span className="text-red-500">*</span>
                              </Label>
                              <Select
                                value={(formData as AlumniApplication).uog_department || ''}
                                onValueChange={(value) => handleInputChange('uog_department', value)}
                              >
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select UoG department" />
                                </SelectTrigger>
                                <SelectContent>
                                  {uogDepartments.map((department) => (
                                    <SelectItem key={department.id} value={department.id.toString()}>
                                      {department.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* External Destination Fields */}
                      {(formData as AlumniApplication).is_uog_destination === false && (
                        <div className="space-y-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                          <h4 className="text-lg font-semibold text-[#1a73c0] mb-4">External Institution Destination</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="order_type" className="text-sm font-medium text-gray-700">
                                Order Type <span className="text-red-500">*</span>
                              </Label>
                              <Select
                                value={(formData as AlumniApplication).order_type || ''}
                                onValueChange={(value) => handleInputChange('order_type', value)}
                              >
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select order type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Local">Local</SelectItem>
                                  <SelectItem value="International">International</SelectItem>
                                  <SelectItem value="Legal Delegate">Legal Delegate</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="mailing_agent" className="text-sm font-medium text-gray-700">
                                Mailing Agent <span className="text-red-500">*</span>
                              </Label>
                              <Select
                                value={(formData as AlumniApplication).mailing_agent || ''}
                                onValueChange={(value) => handleInputChange('mailing_agent', value)}
                              >
                                <SelectTrigger className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200">
                                  <SelectValue placeholder="Select mailing agent" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Normal Postal">Normal Postal</SelectItem>
                                  <SelectItem value="DHL">DHL</SelectItem>
                                  <SelectItem value="SMS">SMS</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="institution_name" className="text-sm font-medium text-gray-700">
                                Institution Name <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="institution_name"
                                value={(formData as AlumniApplication).institution_name || ''}
                                onChange={(e) => handleInputChange('institution_name', e.target.value)}
                                placeholder="Enter institution name"
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="country" className="text-sm font-medium text-gray-700">
                                Country <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="country"
                                value={(formData as AlumniApplication).country || ''}
                                onChange={(e) => handleInputChange('country', e.target.value)}
                                placeholder="Enter country"
                                className="h-12 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="institution_address" className="text-sm font-medium text-gray-700">
                              Institution Address <span className="text-red-500">*</span>
                            </Label>
                            <textarea
                              id="institution_address"
                              value={(formData as AlumniApplication).institution_address || ''}
                              onChange={(e) => handleInputChange('institution_address', e.target.value)}
                              placeholder="Enter complete institution address"
                              rows={3}
                              className="w-full p-3 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl transition-all duration-200 resize-none"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

              </div>

              {/* Step Navigation Controls */}
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-2xl border border-gray-200">
                <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 sm:space-x-4">
                  <div className="text-center sm:text-left">
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">
                      {currentStep === totalSteps - 1 ? 'Ready to Submit?' : `Step ${currentStep + 1}: ${steps[currentStep].title}`}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {currentStep === totalSteps - 1
                        ? 'Please review all information before submitting your application.'
                        : steps[currentStep].description
                      }
                    </p>
                    {!isStepValid && (
                      <p className="text-sm text-red-600 mt-1 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {getValidationMessage()}
                      </p>
                    )}
                  </div>

                  <div className="flex space-x-4">
                    {/* Back Button */}
                    {currentStep > 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={goToPreviousStep}
                        className="border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-xl"
                      >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                      </Button>
                    )}

                    {/* Cancel Button (only on first step) */}
                    {currentStep === 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate('/services')}
                        className="border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-xl"
                      >
                        Cancel
                      </Button>
                    )}

                    {/* Next/Submit Button */}
                    {currentStep < totalSteps - 1 ? (
                      <Button
                        type="button"
                        onClick={goToNextStep}
                        disabled={!canProceed}
                        className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#0f4a7a] text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                      >
                        Next Step
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    ) : (
                      <Button
                        type="submit"
                        disabled={submitting || !canProceed}
                        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                      >
                        {submitting ? (
                          <>
                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                            Submitting Application...
                          </>
                        ) : (
                          <>
                            <Save className="h-5 w-5 mr-2" />
                            Submit Application
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Performance Monitor */}
      <PerformanceMonitor
        enabled={process.env.NODE_ENV === 'development'}
        position="bottom-right"
      />
    </div>
  );
});

export default PublicAlumniApplicationForm;
