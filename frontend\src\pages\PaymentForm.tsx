import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, DollarSign, CreditCard, AlertTriangle } from 'lucide-react';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { applicationAPI, authAPI } from '@/services/api';
// Import the Telebirr logo
import telebirrLogo from '../assets/telebirr-logo.svg';
// Import utility functions
import { hasCompletedAllRequiredSteps, getNextIncompleteStep } from '@/utils/applicationUtils';

// Extend Window interface to include Telebirr-specific properties
declare global {
  interface Window {
    handleinitDataCallback?: () => void;
    consumerapp?: {
      evaluate: (jsonString: string) => void;
    };
    weui?: {
      loading: (text: string, options: any) => { hide: () => void };
    };
  }
}

// Define the schema for the form
const paymentSchema = z.object({
  payment_amount: z.string().min(1, 'Payment amount is required')
    .refine(val => parseFloat(val) >= 0, {
      message: 'Payment amount cannot be negative',
    }),
  payment_method: z.string().min(1, 'Payment method is required'),
  telebirr_id: z.string().optional(),
  transaction_id: z.string().optional(),
  payment_date: z.string().min(1, 'Payment date is required'),
  payment_time: z.string().min(1, 'Payment time is required'),
});

type PaymentFormValues = z.infer<typeof paymentSchema>;

const PaymentForm = () => {
  const [userInfo, setUserInfo] = useState<any>(null);
  const [programSelection, setProgramSelection] = useState<any>(null);
  const [applicationInfo, setApplicationInfo] = useState<any>(null);
  const [gatInfo, setGatInfo] = useState<any>(null);
  const [paymentInfo, setPaymentInfo] = useState<any>(null);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const gatId = queryParams.get('gat');

  // Define baseUrl variable
  const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

  // Get the current price from applicationInfo
  const getPrice = () => {
    return applicationInfo?.program?.registration_fee ||
           paymentInfo?.program_info?.registration_fee ||
           1000;
  };

  // Create a ref for the payment button
  const paymentButtonRef = useRef<HTMLButtonElement>(null);

  // Function to handle Telebirr payment
  function startPay() {
    // Set up callback function for when payment is complete
    window.handleinitDataCallback = function () {
      window.location.href = window.location.origin;
    };

    // Get current price
    const price = getPrice();

    // Check if price is available
    if (!price) {
      toast.error("Payment amount is not available");
      return;
    }

    // Show loading indicator - use weui if available, otherwise use toast
    let loading: { hide: () => void } | null = null;
    if (window.weui) {
      loading = window.weui.loading("loading", {});
    } else {
      const toastId = toast.loading("Processing payment request...");
      // Create a compatible loading object for toast
      loading = {
        hide: () => toast.dismiss(toastId)
      };
    }

    console.log("Using base URL:", baseUrl);

    // Make API request to create order
    window
      .fetch(baseUrl + "/create/order", {
        method: "post",
        mode: "cors",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: "diamond_" + price,
          amount: price + "",
        }),
      })
      .then((res) => {
        res.text().then((rawRequest) => {
          if (!rawRequest) {
            toast.error("Failed to create payment request");
            return;
          }

          // Check if running in Telebirr app
          if (!window.consumerapp) {
            console.log("This page is not open in Telebirr app");
            toast.error("Please open this page in the Telebirr app");
            return;
          }

          // Initiate payment through Telebirr app
          window.consumerapp.evaluate(
            JSON.stringify({
              functionName: "js_fun_start_pay",
              params: {
                rawRequest: rawRequest,
                functionCallBackName: "handleinitDataCallback",
              },
            })
          );

          toast.success("Payment initiated successfully");
        });
      })
      .catch(error => {
        console.error("Payment request failed:", error);
        toast.error("Payment request failed. Please try again.");
      })
      .finally(() => {
        // Hide loading indicator
        if (loading) {
          loading.hide();
        }
      });
  };

  // Log the GAT ID for debugging
  console.log('GAT ID from URL:', gatId);

  // Effect to add the onclick attribute for external scripts
  useEffect(() => {
    // If the payment button ref exists, add the onclick attribute
    if (paymentButtonRef.current) {
      // Using setAttribute to add the HTML attribute for external scripts
      paymentButtonRef.current.setAttribute('onclick', 'startPay();');
    }
  }, [paymentButtonRef]);

  // State to store the current user ID
  const [currentUserId, setCurrentUserId] = useState<number | null>(null);

  // Initialize form
  const form = useForm<PaymentFormValues>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      payment_amount: '1000',
      payment_method: '',
      telebirr_id: '',
      transaction_id: '',
      payment_date: new Date().toISOString().split('T')[0],
      payment_time: new Date().toTimeString().split(' ')[0].substring(0, 5),
    },
  });

  // Check if all required information is available
  const [missingInformation, setMissingInformation] = useState<boolean>(false);
  const [nextStep, setNextStep] = useState<string | null>(null);
  const [invalidGatId, setInvalidGatId] = useState<boolean>(false);

  // Check if the GAT ID from URL is valid
  useEffect(() => {
    const validateGatId = async () => {
      // Only validate if a GAT ID is provided in the URL
      if (gatId) {
        try {
          console.log(`Validating GAT ID: ${gatId}`);

          // Try to fetch the GAT information
          const gatResponse = await applicationAPI.getGAT(Number(gatId));

          // If the response is successful but doesn't belong to the current user
          if (gatResponse.data) {
            // Get all GATs for the current user to check if this one belongs to them
            const userGatsResponse = await applicationAPI.getCurrentGAT();
            if (userGatsResponse.data && userGatsResponse.data.length > 0) {
              const userGats = userGatsResponse.data;

              // Check if the requested GAT ID belongs to this user
              const matchingGat = userGats.find(
                (g: any) => g.id.toString() === gatId.toString()
              );

              if (!matchingGat) {
                console.error('The requested GAT does not belong to this user');
                setInvalidGatId(true);
                toast.error('This GAT ID does not belong to your account');
              }
            }
          }
        } catch (error) {
          console.error('Error validating GAT ID:', error);
          setInvalidGatId(true);
          toast.error('Invalid GAT ID. This GAT does not exist.');
        }
      }
    };

    validateGatId();
  }, [gatId]);

  // Check for all required information before showing the payment page
  useEffect(() => {
    const checkRequiredInformation = async () => {
      try {
        // Get personal information
        const personalInfoResponse = await applicationAPI.getCurrentApplicantInfo();
        const personalInfo = personalInfoResponse.data && personalInfoResponse.data.length > 0 ?
          personalInfoResponse.data[0] : null;

        // Get GAT information
        const gatResponse = await applicationAPI.getCurrentGAT();
        const gatInfo = gatResponse.data && gatResponse.data.length > 0 ?
          gatResponse.data[0] : null;

        // Get program selection
        const programSelectionResponse = await applicationAPI.getCurrentProgramSelection();
        const programSelection = programSelectionResponse.data && programSelectionResponse.data.length > 0 ?
          programSelectionResponse.data[0] : null;

        // Get documentation
        const documentationResponse = await applicationAPI.getCurrentDocumentation();
        const documentation = documentationResponse.data && documentationResponse.data.length > 0 ?
          documentationResponse.data[0] : null;

        // Check if all required information is available
        const hasAllRequiredInfo = hasCompletedAllRequiredSteps(
          personalInfo,
          gatInfo,
          programSelection,
          documentation
        );

        if (!hasAllRequiredInfo) {
          setMissingInformation(true);
          // Get the next step to complete
          const nextIncompleteStep = getNextIncompleteStep(
            personalInfo,
            gatInfo,
            programSelection,
            documentation
          );
          setNextStep(nextIncompleteStep);

          console.log('Missing required information. Next step:', nextIncompleteStep);
        } else {
          setMissingInformation(false);
          setNextStep(null);
        }
      } catch (error) {
        console.error('Error checking required information:', error);
      }
    };

    // Only check required information if the GAT ID is valid or not provided
    if (!invalidGatId) {
      checkRequiredInformation();
    }
  }, [invalidGatId]);

  // Fetch user information and applicant info
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // Fetch user info
        const userResponse = await authAPI.getCurrentUser();
        const userData = userResponse.data;
        setUserInfo(userData);

        // Store the current user ID for program selection filtering
        if (userData && userData.id) {
          setCurrentUserId(userData.id);
          console.log('Current user ID set:', userData.id);
        }

        console.log('User info:', userData);
        console.log('User data keys:', Object.keys(userData));

        // Try to get phone number from user data
        let phoneNumber =
          userData.phone_number ||
          userData.phoneNumber ||
          userData.phone ||
          userData.mobile ||
          userData.contact_number ||
          userData.contactNumber;

        console.log('Phone number from user data:', phoneNumber);

        // If no phone number in user data, try to get it from applicant info
        if (!phoneNumber) {
          try {
            const applicantResponse = await applicationAPI.getCurrentApplicantInfo();
            console.log('Full applicant response:', applicantResponse);

            if (applicantResponse.data && applicantResponse.data.length > 0) {
              const applicantInfo = applicantResponse.data[0];
              console.log('Applicant info:', applicantInfo);
              console.log('Applicant info keys:', Object.keys(applicantInfo));

              // Check all possible phone number fields
              phoneNumber =
                applicantInfo.phone_number ||
                applicantInfo.phoneNumber ||
                applicantInfo.phone ||
                applicantInfo.mobile ||
                applicantInfo.contact_number ||
                applicantInfo.contactNumber;

              console.log('Found phone number in applicant info:', phoneNumber);
            }
          } catch (applicantError) {
            console.error('Error fetching applicant info:', applicantError);
          }
        }

        // If still no phone number, try to get it from personal info API
        if (!phoneNumber) {
          try {
            // Use the new API endpoint to get the phone number
            const response = await fetch('/api/get-phone-number/', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              }
            });

            if (response.ok) {
              const data = await response.json();
              console.log('Phone number from API endpoint:', data);
              if (data && data.phone_number) {
                phoneNumber = data.phone_number;
              }
            }
          } catch (directQueryError) {
            console.error('Error fetching phone number from API:', directQueryError);
          }
        }

        // Update user info with phone number if found
        if (phoneNumber) {
          console.log('Updating user info with phone number:', phoneNumber);
          setUserInfo((prevUserInfo: any) => ({
            ...prevUserInfo,
            phone_number: phoneNumber
          }));
        } else {
          console.warn('No phone number found in any source');
        }
      } catch (error) {
        console.error('Error fetching user info:', error);
        toast.error('Failed to load user information');
      }
    };

    fetchUserData();
  }, []);

  // Fetch program selection, application info, and payment info
  useEffect(() => {
    const fetchProgramData = async () => {
      // Don't fetch program data if currentUserId is not set yet or if the GAT ID is invalid
      if (!currentUserId || invalidGatId) {
        setIsDataLoading(false);
        return;
      }

      setIsDataLoading(true);
      try {
        console.log('Fetching program data with user ID:', currentUserId, 'and GAT ID:', gatId);

        // First try to get program selection by GAT ID if available
        if (gatId) {
          try {
            // Get the GAT information first
            try {
              console.log(`Fetching GAT info for ID: ${gatId}, type: ${typeof gatId}`);
              const gatResponse = await applicationAPI.getGAT(Number(gatId));
              if (gatResponse.data) {
                setGatInfo(gatResponse.data);
                console.log('GAT info:', gatResponse.data);
              }
            } catch (gatError) {
              console.error('Error fetching GAT info:', gatError);
            }

            // Try to get payment information for this GAT ID
            try {
              console.log(`Fetching payment info for GAT ID: ${gatId}, type: ${typeof gatId}`);
              const paymentResponse = await applicationAPI.getPaymentByGAT(Number(gatId));
              console.log('Payment API response:', paymentResponse);

              if (paymentResponse.data) {
                // Check if this is a "no payment found" response with message
                if (paymentResponse.data.message && paymentResponse.data.message.includes("No payment found")) {
                  console.log('No payment found for this GAT ID, but received a valid response');
                  // Still set the payment info so we know it's a valid GAT ID
                  setPaymentInfo(paymentResponse.data);
                } else {
                  // Normal payment data
                  setPaymentInfo(paymentResponse.data);
                  console.log('Payment info for GAT ID:', paymentResponse.data);

                  // If payment has program info, use it directly
                  if (paymentResponse.data.program_info) {
                    setApplicationInfo(paymentResponse.data.program_info);
                    console.log('Using program info from payment:', paymentResponse.data.program_info);

                    // Exit early since we have all the information we need
                    setIsDataLoading(false);
                    return;
                  }
                }
              }
            } catch (paymentError) {
              console.log('Payment not found or error fetching payment info. This is normal for new applications.');
              // We'll continue with the flow to get program selection and application info
            }

            // Now get program selections that match this GAT ID
            const gatProgramResponse = await applicationAPI.getProgramSelectionByGAT(Number(gatId));

            if (gatProgramResponse.data && gatProgramResponse.data.length > 0) {
              console.log('Program selections for GAT ID:', gatProgramResponse.data);

              // Filter to only include selections for the current user
              const userGatSelections = gatProgramResponse.data.filter(
                (selection: any) => selection.user === currentUserId
              );

              if (userGatSelections.length > 0) {
                // If multiple selections exist for this user and GAT, use the most recent one
                const sortedSelections = userGatSelections.sort((a: any, b: any) => {
                  return new Date(b.created_at || b.updated_at || 0).getTime() -
                         new Date(a.created_at || a.updated_at || 0).getTime();
                });

                const selection = sortedSelections[0];
                console.log('Selected program selection for payment:', selection);
                setProgramSelection(selection);

                // Fetch application info based on program selection
                if (selection.application_info) {
                  const appInfoResponse = await applicationAPI.getApplicationInfo(selection.application_info);
                  setApplicationInfo(appInfoResponse.data);
                  console.log('Application info:', appInfoResponse.data);

                  // Log program and registration fee information
                  if (appInfoResponse.data.program) {
                    console.log('Program:', appInfoResponse.data.program);
                    console.log('Registration Fee:', appInfoResponse.data.program.registration_fee);
                  } else {
                    console.log('No program information available in application info');
                  }

                  // Update form with registration fee if available
                  if (appInfoResponse.data?.program?.registration_fee) {
                    form.setValue('payment_amount', appInfoResponse.data.program.registration_fee.toString());
                    console.log('Setting payment amount to registration fee:', appInfoResponse.data.program.registration_fee);
                  }
                }

                // Exit early since we've found a match
                setIsDataLoading(false);
                return;
              } else {
                // If no selections found for this user with this GAT ID, check if the GAT belongs to this user
                console.log('No program selections found for current user with this GAT ID');

                // Try to get all GATs for the current user
                try {
                  const userGatsResponse = await applicationAPI.getCurrentGAT();
                  if (userGatsResponse.data && userGatsResponse.data.length > 0) {
                    const userGats = userGatsResponse.data;
                    console.log('All GATs for current user:', userGats);

                    // Check if the requested GAT ID belongs to this user
                    const matchingGat = userGats.find(
                      (g: any) => g.id.toString() === gatId.toString()
                    );

                    if (matchingGat) {
                      console.log('The requested GAT belongs to this user, but no program selection exists');
                      toast.error('You have not selected a program for this GAT ID yet');
                    } else {
                      console.error('The requested GAT does not belong to this user');
                      toast.error('This GAT ID does not belong to your account');
                    }
                  }
                } catch (gatsError) {
                  console.error('Error fetching user GATs:', gatsError);
                }
              }
            } else {
              console.error('No program selections found for this GAT ID');
              toast.error('No program selections found for this GAT ID');
            }
          } catch (error) {
            console.error('Error fetching program selection by GAT:', error);
            toast.error('Error loading program selection data');
          }
        } else {
          // If no GAT ID is provided, show the most recent program selection for payment
          try {
            const response = await applicationAPI.getCurrentProgramSelection();
            if (response.data && response.data.length > 0) {
              // Filter to only include selections for the current user
              const userSelections = response.data.filter(
                (selection: any) => selection.user === currentUserId
              );

              if (userSelections.length > 0) {
                // Sort by created_at date (most recent first) and take the first one
                const sortedSelections = userSelections.sort((a: any, b: any) => {
                  return new Date(b.created_at || b.updated_at || 0).getTime() -
                         new Date(a.created_at || a.updated_at || 0).getTime();
                });

                const selection = sortedSelections[0];
                setProgramSelection(selection);
                console.log('Most recent program selection for current user:', selection);

                // Fetch application info based on program selection
                if (selection.application_info) {
                  const appInfoResponse = await applicationAPI.getApplicationInfo(selection.application_info);
                  setApplicationInfo(appInfoResponse.data);
                  console.log('Application info:', appInfoResponse.data);

                  // Log program and registration fee information
                  if (appInfoResponse.data.program) {
                    console.log('Program:', appInfoResponse.data.program);
                    console.log('Registration Fee:', appInfoResponse.data.program.registration_fee);
                  } else {
                    console.log('No program information available in application info');
                  }

                  // Update form with registration fee if available
                  if (appInfoResponse.data?.program?.registration_fee) {
                    form.setValue('payment_amount', appInfoResponse.data.program.registration_fee.toString());
                    console.log('Setting payment amount to registration fee:', appInfoResponse.data.program.registration_fee);
                  }
                }

                // Fetch GAT information if available
                if (selection.gat) {
                  try {
                    const gatResponse = await applicationAPI.getGAT(selection.gat);
                    if (gatResponse.data) {
                      setGatInfo(gatResponse.data);
                      console.log('GAT info:', gatResponse.data);

                      // Try to get payment information for this GAT ID
                      try {
                        const paymentResponse = await applicationAPI.getPaymentByGAT(selection.gat);
                        if (paymentResponse.data) {
                          // Check if this is a "no payment found" response with message
                          if (paymentResponse.data.message && paymentResponse.data.message.includes("No payment found")) {
                            console.log('No payment found for this GAT ID, but received a valid response');
                            // Still set the payment info so we know it's a valid GAT ID
                            setPaymentInfo(paymentResponse.data);
                          } else {
                            // Normal payment data
                            setPaymentInfo(paymentResponse.data);
                            console.log('Payment info for GAT ID:', paymentResponse.data);
                          }
                        }
                      } catch (paymentError) {
                        console.log('No payment found for this GAT ID');
                      }
                    }
                  } catch (gatError) {
                    console.error('Error fetching GAT info:', gatError);
                  }
                }
              } else {
                console.error('No program selections found for the current user');
                toast.error('No program selections found for your account');
              }
            }
          } catch (error) {
            console.error('Error fetching current program selection:', error);
            toast.error('Error loading program selection data');
          }
        }
      } catch (error) {
        console.error('Error fetching program data:', error);
        toast.error('Failed to load program information');
      } finally {
        setIsDataLoading(false);
      }
    };

    fetchProgramData();
  }, [currentUserId, gatId, form, invalidGatId]);

  // We no longer need to fetch payment information
  // This section has been removed to avoid database errors

  // Form submission is handled by the "Make Payment" button
  // which navigates to the TeleBirr payment page

  return (
    <DashboardLayout>
      <div>
        {isDataLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-gondar" />
            <span className="ml-3 text-lg text-gray-600">Loading application details...</span>
          </div>
        ) : invalidGatId ? (
          <div className="max-w-6xl mx-auto">
            <Card className="shadow-lg border-0 overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-red-500 to-orange-500 p-6 relative">
                <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                     style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
                <div className="relative z-10 flex items-center">
                  <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-white">Invalid GAT ID</CardTitle>
                    <CardDescription className="text-red-100 mt-1">
                      The GAT ID provided in the URL does not exist or does not belong to your account
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8">
                <div className="bg-red-50 p-6 rounded-lg border border-red-100 mb-6">
                  <div className="flex items-start">
                    <AlertTriangle className="h-6 w-6 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium text-red-800 text-lg mb-2">Invalid GAT ID: {gatId}</h3>
                      <p className="text-red-700 mb-4">
                        The GAT ID you provided in the URL does not exist in our database or does not belong to your account.
                        Please use a valid GAT ID or go to your application status page to select a valid application.
                      </p>
                      <Button
                        onClick={() => navigate('/application/status')}
                        className="bg-red-600 hover:bg-red-700 text-white"
                      >
                        Go to Application Status
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : missingInformation && nextStep ? (
          <div className="max-w-6xl mx-auto">
            <Card className="shadow-lg border-0 overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-red-500 to-orange-500 p-6 relative">
                <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                     style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
                <div className="relative z-10 flex items-center">
                  <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-white">Missing Information</CardTitle>
                    <CardDescription className="text-red-100 mt-1">
                      You need to complete all required steps before proceeding to payment
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8">
                <div className="bg-red-50 p-6 rounded-lg border border-red-100 mb-6">
                  <div className="flex items-start">
                    <AlertTriangle className="h-6 w-6 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium text-red-800 text-lg mb-2">Incomplete Application</h3>
                      <p className="text-red-700 mb-4">
                        Your application is incomplete. You must complete all required steps before proceeding to payment.
                      </p>
                      <Button
                        onClick={() => navigate(nextStep)}
                        className="bg-red-600 hover:bg-red-700 text-white"
                      >
                        Continue to Next Step
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="space-y-8">


            {/* Program Information Card */}
            <Card className="shadow-lg border-0 overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
                <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                     style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
                <div className="relative z-10 flex items-center">
                <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                    <CreditCard className="h-8 w-8 text-[#1a73c0]" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold text-white">Payment Information</CardTitle>
                    <CardDescription className="text-blue-100 mt-1">
                      Review your payment details
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-8">
                {(programSelection && applicationInfo) || (gatInfo && paymentInfo) ? (
                  <div className="space-y-6">
                    <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 program-details-section">
                      <div className="flex items-start mb-4">
                        <div className="h-6 w-6 text-[#1a73c0] mt-0.5 mr-3 flex-shrink-0">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="16" x2="12" y2="12"></line>
                            <line x1="12" y1="8" x2="12.01" y2="8"></line>
                          </svg>
                        </div>
                        <h3 className="font-medium text-[#1a73c0] text-lg">Payment Details</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-sm">
                        {/* Left Column */}
                        <div className="space-y-4 bg-white p-4 rounded-lg shadow-sm">
                          <h4 className="font-medium text-[#1a73c0] border-b border-blue-100 pb-2 mb-3">Personal Information</h4>

                          <div className="space-y-3">
                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Full Name</span>
                              <span className="font-semibold text-gray-700">
                                {userInfo ?
                                  `${userInfo.first_name || userInfo.firstName || ''} ${userInfo.last_name || userInfo.lastName || ''}` :
                                  'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">GAT Number</span>
                              <span className="font-semibold text-gray-700">
                                {gatInfo ?
                                  (gatInfo.GAT_No || gatInfo.gat_no || paymentInfo?.gat_no || '').replace('#', '') :
                                  (paymentInfo?.gat_no || '').replace('#', '') || 'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">GAT Result</span>
                              <span className="font-semibold text-gray-700">
                                {gatInfo ?
                                  gatInfo.GAT_Result || gatInfo.gat_result || paymentInfo?.gat_result || 'Not available' :
                                  paymentInfo?.gat_result || 'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Application Number</span>
                              <span className="font-semibold text-gray-700">
                                {programSelection?.application_num ?
                                 programSelection.application_num :
                                 'Not assigned'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Transaction ID</span>
                              <span className="font-semibold text-gray-700">
                                {programSelection?.transaction_id || 'Not available'}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Right Column */}
                        <div className="space-y-4 bg-white p-4 rounded-lg shadow-sm">
                          <h4 className="font-medium text-[#1a73c0] border-b border-blue-100 pb-2 mb-3">Program Information</h4>

                          <div className="space-y-3">
                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Program</span>
                              <span className="font-semibold text-gray-700">
                                {applicationInfo?.program_name ||
                                 (applicationInfo?.program && applicationInfo.program.program_name) ||
                                 (paymentInfo?.program_info?.program) ||
                                 'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Field of Study</span>
                              <span className="font-semibold text-gray-700">
                                {applicationInfo?.field_of_study_name ||
                                 (applicationInfo?.field_of_study && applicationInfo.field_of_study.name) ||
                                 (paymentInfo?.program_info?.field_of_study) ||
                                 'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Department</span>
                              <span className="font-semibold text-gray-700">
                                {applicationInfo?.department_name ||
                                 (applicationInfo?.department && applicationInfo.department.name) ||
                                 (paymentInfo?.program_info?.department) ||
                                 'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">College</span>
                              <span className="font-semibold text-gray-700">
                                {applicationInfo?.college_name ||
                                 (applicationInfo?.college && applicationInfo.college.name) ||
                                 (paymentInfo?.program_info?.college) ||
                                 'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Study Program</span>
                              <span className="font-semibold text-gray-700">
                                {applicationInfo?.study_program_name ||
                                 (applicationInfo?.study_program && applicationInfo.study_program.name) ||
                                 (paymentInfo?.program_info?.study_program) ||
                                 'Not available'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Duration</span>
                              <span className="font-semibold text-gray-700">
                                {applicationInfo?.duration ||
                                 paymentInfo?.program_info?.duration ||
                                 '2'} Years
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Admission Type</span>
                              <span className="font-semibold text-gray-700">
                                {applicationInfo?.admission_type_name ||
                                 (applicationInfo?.admission_type && applicationInfo.admission_type.name) ||
                                 (paymentInfo?.program_info?.admission_type) ||
                                 'Regular'}
                              </span>
                            </div>

                            <div className="flex flex-col">
                              <span className="text-gray-500 text-xs uppercase tracking-wider mb-1">Sponsorship</span>
                              <span className="font-semibold text-gray-700">
                                {programSelection?.sponsorship ||
                                 'Not selected'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Registration Fee Section */}
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <span className="font-semibold text-[#1a73c0]">Registration Fee:</span>
                        </div>
                        <div className="text-xl font-bold text-[#1a73c0]">
                          {paymentInfo?.program_info?.registration_fee ||
                           (applicationInfo?.program?.registration_fee) ||
                           'Not available'} ETB
                        </div>
                      </div>
                      <div className="space-y-3 mt-2">
                        <p className="text-sm text-blue-600">
                          This is the required registration fee of {applicationInfo?.program?.registration_fee || 'Not available'} ETB for the {applicationInfo?.program_name || (applicationInfo?.program && applicationInfo.program.program_name) || 'selected program'} program.
                        </p>

                        <div className="border-t border-blue-100 pt-3">
                          <div className="flex items-center">
                            <svg className="h-4 w-4 text-[#1a73c0] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <span className="font-medium text-[#1a73c0]">Transaction ID:</span>
                            <span className="font-semibold ml-2 text-gray-700">{programSelection?.transaction_id || paymentInfo?.transaction_id || 'Not available'}</span>
                          </div>

                          <div className="mt-4">
                            <div className="flex items-center justify-center">
                              <button
                                ref={paymentButtonRef}
                                type="button"
                                id="buy"
                                className="flex items-center bg-[#0078C8] hover:bg-[#0066B0] text-white rounded-md overflow-hidden"
                                onClick={startPay}
                                style={{ boxShadow: '0 2px 4px rgba(0,0,0,0.2)' }}
                                // Using ref and useEffect to add the onclick attribute for external scripts
                              >
                                <span className="font-medium text-lg px-4 py-2">Pay using</span>
                                <div className="bg-white h-full flex items-center justify-center px-3 py-2 border-l border-[#0066B0]" style={{ minWidth: '100px' }}>
                                  {/* Telebirr logo as text */}
                                  <div className="flex flex-col items-center">
                                    <div style={{ color: '#0078C8', fontWeight: 'bold', fontSize: '16px', lineHeight: '1.2' }}>
                                      <span style={{ fontFamily: 'Arial, sans-serif' }}>ቴሌብር</span>
                                    </div>
                                    <div style={{ color: '#FFBF00', fontWeight: 'bold', fontSize: '16px', lineHeight: '1.2' }}>
                                      <span style={{ fontFamily: 'Arial, sans-serif' }}>telebirr</span>
                                    </div>
                                  </div>
                                </div>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gray-50 p-8 rounded-lg border border-gray-100 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <div className="bg-white p-4 rounded-full shadow-sm mb-4">
                        <svg className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <p className="text-lg font-medium text-gray-700">Payment information not available</p>
                      <p className="text-sm mt-1 text-gray-500">Please try selecting a program first</p>
                      <Button
                        type="button"
                        variant="outline"
                        className="mt-4"
                        onClick={() => navigate('/application/program-selection')}
                      >
                        Go to Program Selection
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default PaymentForm;
