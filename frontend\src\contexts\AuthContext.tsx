import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '@/services/api';
import { toast } from 'sonner';

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
  is_active?: boolean;
  date_joined?: string;
  last_login?: string;
  roles?: Array<{ id: number; name: string }>;
  permissions?: string[];
  role_names?: string[];
  profile?: {
    department?: string;
    phone?: string;
    employee_id?: string;
    full_name?: string;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  getAuthHeader: () => { Authorization: string } | {};
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => false,
  logout: async () => {},
  getAuthHeader: () => ({}),
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      
      const token = localStorage.getItem('token');
      const refreshToken = localStorage.getItem('refresh_token');
      const storedUser = localStorage.getItem('user');
      
      if (!token || !refreshToken) {
        setIsAuthenticated(false);
        setUser(null);
        setIsLoading(false);
        return;
      }

      try {
        // Try to refresh the token
        await authAPI.refreshToken(refreshToken);
        
        // Set user from localStorage
        if (storedUser) {
          setUser(JSON.parse(storedUser));
        } else {
          // If no user in localStorage, fetch from API
          const userResponse = await authAPI.getCurrentUser();
          if (userResponse.data) {
            setUser(userResponse.data);
            localStorage.setItem('user', JSON.stringify(userResponse.data));
          }
        }
        
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Error initializing auth:', error);
        setIsAuthenticated(false);
        setUser(null);
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        localStorage.removeItem('isAuthenticated');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      // Login and get tokens
      const response = await authAPI.login({ username, password });

      // Check if we have a valid response with tokens
      if (!response.data || !response.data.access || !response.data.refresh) {
        console.error('Invalid login response:', response);
        toast.error('Authentication failed: Invalid server response');
        return false;
      }

      // Store tokens
      localStorage.setItem('token', response.data.access);
      localStorage.setItem('refresh_token', response.data.refresh);
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('lastActivity', Date.now().toString());

      // Get user information
      const userResponse = await authAPI.getCurrentUser();
      if (!userResponse.data) {
        throw new Error('Invalid user data response');
      }

      // Store user information
      const userData = userResponse.data;
      localStorage.setItem('user', JSON.stringify(userData));
      setUser(userData);
      setIsAuthenticated(true);

      return true;
    } catch (error) {
      console.error('Login error:', error);
      setIsAuthenticated(false);
      setUser(null);
      return false;
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    setIsAuthenticated(false);
    setUser(null);
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    localStorage.removeItem('lastActivity');
  };

  // Get auth header for API requests
  const getAuthHeader = () => {
    const token = localStorage.getItem('token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  };

  const value = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    getAuthHeader,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
