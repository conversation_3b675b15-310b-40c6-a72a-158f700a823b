import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '@/services/authAPI';
import { tokenManager } from '@/utils/tokenManager';
import { toast } from 'sonner';

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
  is_active?: boolean;
  date_joined?: string;
  last_login?: string;
  roles?: Array<{ id: number; name: string }>;
  permissions?: string[];
  role_names?: string[];
  profile?: {
    department?: string;
    phone?: string;
    employee_id?: string;
    full_name?: string;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  hasAllRoles: (roles: string[]) => boolean;
  isAdmin: () => boolean;
  isStaff: () => boolean;
  getAuthHeader: () => { Authorization: string } | {};
  validateToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => false,
  logout: async () => {},
  refreshUser: async () => {},
  hasPermission: () => false,
  hasRole: () => false,
  hasAnyRole: () => false,
  hasAllRoles: () => false,
  isAdmin: () => false,
  isStaff: () => false,
  getAuthHeader: () => ({}),
  validateToken: async () => false,
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Initialize auth state using token manager
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      try {
        // Check if we have valid tokens
        if (!tokenManager.validateStoredTokens()) {
          setIsAuthenticated(false);
          setUser(null);
          setIsLoading(false);
          return;
        }

        // Get stored user data
        const userData = tokenManager.getUserData();
        if (!userData) {
          // Try to fetch user data from API
          try {
            const userResponse = await authAPI.getCurrentUser();
            if (userResponse.data) {
              tokenManager.setUserData(userResponse.data);
              setUser(userResponse.data);
              setIsAuthenticated(true);
            } else {
              throw new Error('No user data received');
            }
          } catch (error) {
            console.error('Failed to fetch user data:', error);
            tokenManager.clearTokens();
            setIsAuthenticated(false);
            setUser(null);
          }
        } else {
          // Check session expiration
          if (tokenManager.isSessionExpired()) {
            tokenManager.clearTokens();
            setIsAuthenticated(false);
            setUser(null);
          } else {
            // Update last activity and restore session
            tokenManager.updateLastActivity();
            setUser(userData);
            setIsAuthenticated(true);
            console.log('Session restored successfully');
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        setIsAuthenticated(false);
        setUser(null);
        tokenManager.clearTokens();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      // Login and get tokens using auth API
      const response = await authAPI.login({ username, password });

      // Check if we have a valid response with tokens
      if (!response.data || !response.data.access || !response.data.refresh) {
        console.error('Invalid login response:', response);
        toast.error('Authentication failed: Invalid server response');
        return false;
      }

      // Store tokens using token manager
      tokenManager.setTokens({
        access: response.data.access,
        refresh: response.data.refresh
      });

      // Get user information
      const userResponse = await authAPI.getCurrentUser();
      if (!userResponse.data) {
        throw new Error('Invalid user data response');
      }

      // Store user information using token manager
      const userData = userResponse.data;
      tokenManager.setUserData(userData);
      setUser(userData);
      setIsAuthenticated(true);

      return true;
    } catch (error) {
      console.error('Login error:', error);
      setIsAuthenticated(false);
      setUser(null);
      tokenManager.clearTokens();
      return false;
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      // Clear tokens using token manager
      tokenManager.clearTokens();

      // Clear state
      setIsAuthenticated(false);
      setUser(null);

      console.log('Logout completed successfully');
    } catch (error) {
      console.error('Error during logout:', error);
      // Force clear even if there's an error
      setIsAuthenticated(false);
      setUser(null);
      tokenManager.clearTokens();
    }
  };

  // Refresh user data
  const refreshUser = async (): Promise<void> => {
    try {
      const userResponse = await authAPI.getCurrentUser();
      if (userResponse.data) {
        setUser(userResponse.data);
        localStorage.setItem('user', JSON.stringify(userResponse.data));
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  // Permission and role checking functions
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.is_superuser) return true;

    // Try different permission formats
    // Format 1: permission_strings array
    if (user.permissions?.permission_strings?.includes(permission)) {
      return true;
    }

    // Format 2: Direct array of permissions
    if (Array.isArray(user.permissions) && user.permissions.includes(permission)) {
      return true;
    }

    // Format 3: Array of permission objects with codename
    if (Array.isArray(user.permissions)) {
      const hasPermissionObject = user.permissions.some((perm: any) => {
        if (typeof perm === 'object' && perm.codename) {
          // Check if permission matches format "app.codename"
          const permissionString = `${perm.app_label || 'unknown'}.${perm.codename}`;
          return permissionString === permission || perm.codename === permission;
        }
        return false;
      });
      if (hasPermissionObject) return true;
    }

    // Format 4: Check if user has any permissions at all (for debugging)
    console.log('🔍 Permission check failed for:', permission, 'User permissions:', user.permissions);

    return false;
  };

  const hasRole = (role: string): boolean => {
    if (!user) return false;
    if (user.is_superuser) return true;
    return user.role_names?.includes(role) || false;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    if (!user) return false;
    if (user.is_superuser) return true;
    return roles.some(role => user.role_names?.includes(role)) || false;
  };

  const hasAllRoles = (roles: string[]): boolean => {
    if (!user) return false;
    if (user.is_superuser) return true;
    return roles.every(role => user.role_names?.includes(role)) || false;
  };

  const isAdmin = (): boolean => {
    if (!user) return false;
    return user.is_superuser || user.is_admin || false;
  };

  const isStaff = (): boolean => {
    if (!user) return false;
    return user.is_staff || user.is_superuser || false;
  };

  // Get auth header for API requests
  const getAuthHeader = () => {
    return tokenManager.getAuthHeader();
  };

  // Validate current token
  const validateToken = async (): Promise<boolean> => {
    try {
      const response = await authAPI.validateToken();
      return response.data.valid || false;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  };

  const value = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshUser,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isAdmin,
    isStaff,
    getAuthHeader,
    validateToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
