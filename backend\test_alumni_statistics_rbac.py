#!/usr/bin/env python3
"""
Quick test for Alumni Applications Statistics RBAC
Tests the newly added revenue calculations and time-based statistics
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User, Group
from django.test import Client
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from alumni_applications.models import AlumniApplication, AlumniApplicationMini
from setups.service_type.models import ServiceType
from setups.college.models import College
from decimal import Decimal

class AlumniStatisticsRBACTest:
    def __init__(self):
        self.client = APIClient()
        self.test_users = {}
        
    def setup_test_data(self):
        """Create test data for statistics"""
        print("🔧 Setting up test data...")

        # Create service types with different fees
        service_types = [
            {'name': 'Official Transcript', 'fee': Decimal('150.00')},
            {'name': 'Temporary Certificate', 'fee': Decimal('100.00')},
            {'name': 'Student Copy', 'fee': Decimal('50.00')},
        ]

        created_services = []
        for service_data in service_types:
            service, created = ServiceType.objects.get_or_create(
                name=service_data['name'],
                defaults={'fee': service_data['fee'], 'description': f"Test {service_data['name']}"}
            )
            created_services.append(service)
            if created:
                print(f"  ✅ Created service type: {service.name} ({service.fee} ETB)")

        # Create test applications with different payment statuses
        test_applications = [
            {
                'service_type': created_services[0],  # 150 ETB
                'payment_status': 'Completed',
                'application_status': 'Complete'
            },
            {
                'service_type': created_services[1],  # 100 ETB
                'payment_status': 'Pending',
                'application_status': 'Pending'
            },
            {
                'service_type': created_services[2],  # 50 ETB
                'payment_status': 'Failed',
                'application_status': 'Pending'
            }
        ]

        # Get or create a test college
        test_college, created = College.objects.get_or_create(
            name='Test Engineering College',
            defaults={'description': 'Test college for RBAC testing'}
        )
        if created:
            print(f"  ✅ Created test college: {test_college.name}")

        # Clear existing test applications
        AlumniApplication.objects.filter(email__contains='test@rbac').delete()
        AlumniApplicationMini.objects.filter(email__contains='test@rbac').delete()

        for i, app_data in enumerate(test_applications):
            # Create Form1 application (use other_department_name instead of department FK)
            AlumniApplication.objects.create(
                first_name=f'Test{i+1}',
                father_name='Father',
                last_name='User',
                email=f'test{i+1}@rbac.com',
                phone_number=f'+25191234567{i}',
                admission_type='Regular',
                degree_type='Degree',
                college=test_college,
                other_department_name='Computer Science',  # Use text field instead of FK
                is_other_college=False,
                student_status='Graduated',
                year_of_graduation_ethiopian='2015',
                year_of_graduation_gregorian=2023,
                service_type=app_data['service_type'],
                payment_status=app_data['payment_status'],
                application_status=app_data['application_status'],
                is_uog_destination=True,
                uog_college=test_college
            )

            # Create Form2 application (use other_department_name instead of department FK)
            AlumniApplicationMini.objects.create(
                first_name=f'Mini{i+1}',
                father_name='Father',
                last_name='User',
                email=f'mini{i+1}@rbac.com',
                phone_number=f'+25191234568{i}',
                admission_type='Regular',
                degree_type='Degree',
                college=test_college,
                other_department_name='Computer Science',  # Use text field instead of FK
                is_other_college=False,
                student_status='Graduated',
                year_of_graduation_ethiopian='2015',
                year_of_graduation_gregorian=2023,
                service_type=app_data['service_type'],
                payment_status=app_data['payment_status'],
                application_status=app_data['application_status']
            )

        print(f"  ✅ Created {len(test_applications)} Form1 and {len(test_applications)} Form2 test applications")

        # Expected revenue calculations
        print("\n📊 Expected Revenue Calculations:")
        print(f"  • Paid Revenue: 150 + 150 = 300 ETB (2 completed applications)")
        print(f"  • Pending Revenue: 100 + 100 = 200 ETB (2 pending applications)")
        print(f"  • Unpaid Revenue: 50 + 50 = 100 ETB (2 failed applications)")
        print(f"  • Total Potential: 300 + 200 + 100 = 600 ETB")

    def setup_test_users(self):
        """Create test users with different roles"""
        print("\n👥 Setting up test users...")
        
        # Create groups
        groups = ['Super Admin', 'Administrator', 'Staff']
        for group_name in groups:
            Group.objects.get_or_create(name=group_name)
        
        # Create test users
        users_config = {
            'admin': {
                'username': 'test_admin_stats',
                'password': 'TestPass123!',
                'groups': ['Administrator'],
                'is_staff': True
            },
            'staff': {
                'username': 'test_staff_stats',
                'password': 'TestPass123!',
                'groups': ['Staff'],
                'is_staff': True
            },
            'super_admin': {
                'username': 'test_super_admin_stats',
                'password': 'TestPass123!',
                'groups': ['Super Admin'],
                'is_superuser': True,
                'is_staff': True
            }
        }
        
        for role, config in users_config.items():
            # Delete existing user
            User.objects.filter(username=config['username']).delete()
            
            # Create new user
            user = User.objects.create_user(
                username=config['username'],
                password=config['password'],
                is_superuser=config.get('is_superuser', False),
                is_staff=config.get('is_staff', False)
            )
            
            # Add to groups
            for group_name in config['groups']:
                group = Group.objects.get(name=group_name)
                user.groups.add(group)
            
            self.test_users[role] = user
            print(f"  ✅ Created user: {config['username']} ({role})")

    def get_auth_token(self, user):
        """Get JWT token for user"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def test_statistics_access(self):
        """Test statistics endpoint access with different roles"""
        print("\n🔐 Testing Statistics Endpoint Access...")
        
        # Test anonymous access (should fail)
        self.client.credentials()
        response = self.client.get('/api/applications/statistics/')
        status = "✅" if response.status_code == 401 else "❌"
        print(f"  {status} Anonymous access: {response.status_code} (expected 401)")
        
        # Test authenticated access
        for role, user in self.test_users.items():
            token = self.get_auth_token(user)
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            response = self.client.get('/api/applications/statistics/')
            status = "✅" if response.status_code == 200 else "❌"
            print(f"  {status} {role.title()} access: {response.status_code}")
            
            if response.status_code == 200:
                self.validate_statistics_response(response.data, role)

    def validate_statistics_response(self, data, role):
        """Validate the statistics response structure and data"""
        print(f"\n📊 Validating Statistics Response for {role.title()}...")
        
        # Check required fields
        required_fields = [
            'total_requests', 'form1_count', 'form2_count',
            'by_status', 'by_payment_status', 'recent_applications'
        ]
        
        for field in required_fields:
            if field in data:
                print(f"  ✅ {field}: {data[field]}")
            else:
                print(f"  ❌ Missing field: {field}")
        
        # Check revenue data (new functionality)
        if 'revenue' in data:
            revenue = data['revenue']
            print(f"  ✅ Revenue data present:")
            print(f"    - Paid Revenue: {revenue.get('paid_revenue', 'N/A')} ETB")
            print(f"    - Pending Revenue: {revenue.get('pending_revenue', 'N/A')} ETB")
            print(f"    - Unpaid Revenue: {revenue.get('unpaid_revenue', 'N/A')} ETB")
            print(f"    - Total Potential: {revenue.get('total_potential_revenue', 'N/A')} ETB")
            
            # Validate revenue calculations
            expected_paid = 300.0  # 2 completed applications × 150 ETB
            expected_pending = 200.0  # 2 pending applications × 100 ETB
            expected_unpaid = 100.0  # 2 failed applications × 50 ETB
            expected_total = 600.0  # Total of all applications
            
            actual_paid = revenue.get('paid_revenue', 0)
            actual_pending = revenue.get('pending_revenue', 0)
            actual_unpaid = revenue.get('unpaid_revenue', 0)
            actual_total = revenue.get('total_potential_revenue', 0)
            
            print(f"\n  💰 Revenue Validation:")
            print(f"    Paid: {actual_paid} ETB (expected {expected_paid}) {'✅' if actual_paid == expected_paid else '❌'}")
            print(f"    Pending: {actual_pending} ETB (expected {expected_pending}) {'✅' if actual_pending == expected_pending else '❌'}")
            print(f"    Unpaid: {actual_unpaid} ETB (expected {expected_unpaid}) {'✅' if actual_unpaid == expected_unpaid else '❌'}")
            print(f"    Total: {actual_total} ETB (expected {expected_total}) {'✅' if actual_total == expected_total else '❌'}")
        else:
            print(f"  ❌ Revenue data missing!")
        
        # Check time-based statistics (new functionality)
        if 'time_based' in data:
            time_based = data['time_based']
            print(f"  ✅ Time-based statistics present:")
            print(f"    - Today: {time_based.get('today', 'N/A')}")
            print(f"    - 3 Days: {time_based.get('three_days', 'N/A')}")
            print(f"    - 1 Week: {time_based.get('one_week', 'N/A')}")
            print(f"    - 1 Month: {time_based.get('one_month', 'N/A')}")
        else:
            print(f"  ❌ Time-based statistics missing!")

    def test_revenue_calculation_accuracy(self):
        """Test the accuracy of revenue calculations"""
        print("\n🧮 Testing Revenue Calculation Accuracy...")
        
        # Use admin user for this test
        admin_user = self.test_users['admin']
        token = self.get_auth_token(admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/applications/statistics/')
        
        if response.status_code == 200 and 'revenue' in response.data:
            revenue = response.data['revenue']
            
            # Manual calculation verification
            from django.db.models import Sum
            
            # Calculate expected values using Django ORM
            paid_form1 = AlumniApplication.objects.filter(
                payment_status__in=['Completed', 'Verified']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0
            
            paid_form2 = AlumniApplicationMini.objects.filter(
                payment_status__in=['Completed', 'Verified']
            ).aggregate(total=Sum('service_type__fee'))['total'] or 0
            
            expected_paid = float(paid_form1 + paid_form2)
            actual_paid = revenue.get('paid_revenue', 0)
            
            print(f"  Manual calculation verification:")
            print(f"    Form1 paid revenue: {paid_form1} ETB")
            print(f"    Form2 paid revenue: {paid_form2} ETB")
            print(f"    Expected total paid: {expected_paid} ETB")
            print(f"    API returned paid: {actual_paid} ETB")
            print(f"    Match: {'✅' if actual_paid == expected_paid else '❌'}")
        else:
            print("  ❌ Could not retrieve revenue data for verification")

    def run_all_tests(self):
        """Run all alumni statistics RBAC tests"""
        print("🚀 Starting Alumni Statistics RBAC Tests...")
        print("=" * 60)
        
        self.setup_test_data()
        self.setup_test_users()
        self.test_statistics_access()
        self.test_revenue_calculation_accuracy()
        
        print("\n" + "=" * 60)
        print("✅ Alumni Statistics RBAC Tests Complete!")
        print("=" * 60)

if __name__ == "__main__":
    tester = AlumniStatisticsRBACTest()
    tester.run_all_tests()
