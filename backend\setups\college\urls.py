from django.urls import path
from setups.college import views
from user_management.views import CheckUserView

urlpatterns = [
     path('colleges/', views.CollegeList.as_view(), name='college-list'),
     path('colleges/<int:pk>/', views.CollegeDetail.as_view(), name='college-detail'),
     path('colleges/delete/<int:pk>/', views.CollegeDelete.as_view(), name='college-delete'),
     path('colleges/public/', views.PublicCollegeList.as_view(), name='public-college-list'),
     path('check-user/', CheckUserView.as_view(), name='check-user'),
]
