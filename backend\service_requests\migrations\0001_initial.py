# Generated by Django 5.2.1 on 2025-06-10 11:46

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('admission_type', '0001_initial'),
        ('certificate_type', '0002_remove_certificatetype_id_alter_certificatetype_uuid'),
        ('college', '0001_initial'),
        ('department', '0001_initial'),
        ('service_type', '0001_initial'),
        ('study_program', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('mobile', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('college_other', models.CharField(blank=True, max_length=200, null=True)),
                ('is_college_other', models.BooleanField(default=False)),
                ('department_other', models.CharField(blank=True, max_length=200, null=True)),
                ('is_department_other', models.BooleanField(default=False)),
                ('student_status', models.CharField(blank=True, choices=[('active', 'Active Student'), ('inactive', 'Inactive Student'), ('graduate', 'Graduate')], max_length=20, null=True)),
                ('year_ec', models.IntegerField(blank=True, help_text='Ethiopian Calendar year', null=True)),
                ('year_gc', models.IntegerField(blank=True, help_text='Gregorian Calendar year', null=True)),
                ('year_type', models.CharField(blank=True, choices=[('current', 'Current Year'), ('leaving', 'Leaving Year'), ('graduation', 'Graduation Year')], max_length=20, null=True)),
                ('graduation_year_ec', models.IntegerField(blank=True, null=True)),
                ('graduation_year_gc', models.IntegerField(blank=True, null=True)),
                ('mailing_destination', models.CharField(blank=True, choices=[('uog', 'University of Gondar'), ('external', 'External Institution')], max_length=20, null=True)),
                ('institute_name', models.CharField(blank=True, max_length=300, null=True)),
                ('institute_country', models.CharField(blank=True, max_length=100, null=True)),
                ('institute_address', models.TextField(blank=True, null=True)),
                ('mailing_agent', models.CharField(blank=True, choices=[('dhl', 'DHL'), ('ems', 'EMS'), ('other', 'Other')], max_length=20, null=True)),
                ('mailing_agent_other', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('admission_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_requests', to='admission_type.admissiontype')),
                ('college', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='service_requests', to='college.college')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='created_service_requests', to=settings.AUTH_USER_MODEL)),
                ('degree', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_requests', to='study_program.studyprogram')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='deleted_service_requests', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='service_requests', to='department.department')),
                ('mailing_college', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='mailing_service_requests', to='college.college')),
                ('mailing_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='mailing_service_requests', to='department.department')),
                ('service_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='service_requests', to='service_type.servicetype')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='updated_service_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Service Request',
                'verbose_name_plural': 'Service Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentUpload',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(help_text='Upload the required document', upload_to='service_requests/documents/%Y/%m/%d/')),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField(help_text='File size in bytes')),
                ('content_type', models.CharField(max_length=100)),
                ('is_verified', models.BooleanField(default=False)),
                ('verification_notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='document_uploads', to='certificate_type.certificatetype')),
                ('uploaded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='uploaded_documents', to=settings.AUTH_USER_MODEL)),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='verified_documents', to=settings.AUTH_USER_MODEL)),
                ('service_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_uploads', to='service_requests.servicerequest')),
            ],
            options={
                'verbose_name': 'Document Upload',
                'verbose_name_plural': 'Document Uploads',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='servicerequest',
            index=models.Index(fields=['service_type', 'status'], name='service_req_service_fa4b88_idx'),
        ),
        migrations.AddIndex(
            model_name='servicerequest',
            index=models.Index(fields=['created_at'], name='service_req_created_6be6a8_idx'),
        ),
        migrations.AddIndex(
            model_name='servicerequest',
            index=models.Index(fields=['email'], name='service_req_email_7ca545_idx'),
        ),
        migrations.AddIndex(
            model_name='servicerequest',
            index=models.Index(fields=['is_deleted'], name='service_req_is_dele_0aea3b_idx'),
        ),
        migrations.AddIndex(
            model_name='documentupload',
            index=models.Index(fields=['service_request', 'document_type'], name='service_req_service_d6d410_idx'),
        ),
        migrations.AddIndex(
            model_name='documentupload',
            index=models.Index(fields=['is_verified'], name='service_req_is_veri_95ff97_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='documentupload',
            unique_together={('service_request', 'document_type')},
        ),
    ]
