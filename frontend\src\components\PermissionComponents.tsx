/**
 * Permission-Aware UI Components
 * Provides conditional rendering based on user permissions and roles
 */
import React, { ReactNode } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface BasePermissionProps {
  children: ReactNode;
  fallback?: ReactNode;
  showFallback?: boolean;
}

interface HasPermissionProps extends BasePermissionProps {
  permission: string;
}

interface HasPermissionsProps extends BasePermissionProps {
  permissions: string[];
  requireAll?: boolean;
}

interface HasRoleProps extends BasePermissionProps {
  role: string;
}

interface HasRolesProps extends BasePermissionProps {
  roles: string[];
  requireAll?: boolean;
}

interface HasAnyProps extends BasePermissionProps {
  permissions?: string[];
  roles?: string[];
}

interface ConditionalRenderProps extends BasePermissionProps {
  condition: boolean;
}

/**
 * Renders children only if user has the specified permission
 */
export const HasPermission: React.FC<HasPermissionProps> = ({
  children,
  permission,
  fallback = null,
  showFallback = true,
}) => {
  const { hasPermission } = useAuth();

  if (hasPermission(permission)) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children only if user has the specified permissions
 */
export const HasPermissions: React.FC<HasPermissionsProps> = ({
  children,
  permissions,
  requireAll = false,
  fallback = null,
  showFallback = true,
}) => {
  const { hasPermission } = useAuth();

  const hasRequiredPermissions = requireAll
    ? permissions.every(permission => hasPermission(permission))
    : permissions.some(permission => hasPermission(permission));

  if (hasRequiredPermissions) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children only if user has the specified role
 */
export const HasRole: React.FC<HasRoleProps> = ({
  children,
  role,
  fallback = null,
  showFallback = true,
}) => {
  const { hasRole } = useAuth();

  if (hasRole(role)) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children only if user has the specified roles
 */
export const HasRoles: React.FC<HasRolesProps> = ({
  children,
  roles,
  requireAll = false,
  fallback = null,
  showFallback = true,
}) => {
  const { hasAnyRole, hasAllRoles } = useAuth();

  const hasRequiredRoles = requireAll
    ? hasAllRoles(roles)
    : hasAnyRole(roles);

  if (hasRequiredRoles) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children if user has any of the specified permissions or roles
 */
export const HasAny: React.FC<HasAnyProps> = ({
  children,
  permissions = [],
  roles = [],
  fallback = null,
  showFallback = true,
}) => {
  const { hasPermission, hasRole } = useAuth();

  const hasAnyPermission = permissions.some(permission => hasPermission(permission));
  const hasAnyRole = roles.some(role => hasRole(role));

  if (hasAnyPermission || hasAnyRole) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children only for admin users
 */
export const AdminOnly: React.FC<BasePermissionProps> = ({
  children,
  fallback = null,
  showFallback = true,
}) => {
  const { isAdmin } = useAuth();

  if (isAdmin()) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children only for staff users
 */
export const StaffOnly: React.FC<BasePermissionProps> = ({
  children,
  fallback = null,
  showFallback = true,
}) => {
  const { isStaff } = useAuth();

  if (isStaff()) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children only for authenticated users
 */
export const AuthenticatedOnly: React.FC<BasePermissionProps> = ({
  children,
  fallback = null,
  showFallback = true,
}) => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Renders children only for unauthenticated users
 */
export const UnauthenticatedOnly: React.FC<BasePermissionProps> = ({
  children,
  fallback = null,
  showFallback = true,
}) => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Generic conditional renderer
 */
export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  children,
  condition,
  fallback = null,
  showFallback = true,
}) => {
  if (condition) {
    return <>{children}</>;
  }

  return showFallback ? <>{fallback}</> : null;
};

/**
 * Higher-order component for permission-based rendering
 */
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  permission: string,
  fallback?: ReactNode
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <HasPermission permission={permission} fallback={fallback}>
        <Component {...props} />
      </HasPermission>
    );
  };
}

/**
 * Higher-order component for role-based rendering
 */
export function withRole<P extends object>(
  Component: React.ComponentType<P>,
  role: string,
  fallback?: ReactNode
) {
  return function RoleWrappedComponent(props: P) {
    return (
      <HasRole role={role} fallback={fallback}>
        <Component {...props} />
      </HasRole>
    );
  };
}

/**
 * Higher-order component for admin-only rendering
 */
export function withAdminOnly<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function AdminOnlyComponent(props: P) {
    return (
      <AdminOnly fallback={fallback}>
        <Component {...props} />
      </AdminOnly>
    );
  };
}

/**
 * Hook for permission checking in components
 */
export const usePermissions = () => {
  const {
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isAdmin,
    isStaff,
    user,
    isAuthenticated
  } = useAuth();

  return {
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isAdmin: isAdmin(),
    isStaff: isStaff(),
    isAuthenticated,
    user,
    // Convenience methods
    canAccess: (permissions: string[], roles: string[] = []) => {
      if (!isAuthenticated) return false;
      if (isAdmin()) return true;
      
      const hasAnyPermission = permissions.some(permission => hasPermission(permission));
      const hasAnyRole = roles.some(role => hasRole(role));
      
      return hasAnyPermission || hasAnyRole;
    },
    canAccessAll: (permissions: string[], roles: string[] = []) => {
      if (!isAuthenticated) return false;
      if (isAdmin()) return true;
      
      const hasAllPermissions = permissions.every(permission => hasPermission(permission));
      const hasAllRoles = roles.every(role => hasRole(role));
      
      return hasAllPermissions && hasAllRoles;
    },
  };
};

/**
 * Permission-aware button component
 */
interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  permission?: string;
  role?: string;
  requireAdmin?: boolean;
  requireStaff?: boolean;
  fallbackText?: string;
  showWhenDisabled?: boolean;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  children,
  permission,
  role,
  requireAdmin = false,
  requireStaff = false,
  fallbackText = 'Access Denied',
  showWhenDisabled = true,
  disabled,
  ...props
}) => {
  const { hasPermission, hasRole, isAdmin, isStaff } = useAuth();

  let hasAccess = true;

  if (requireAdmin && !isAdmin()) hasAccess = false;
  if (requireStaff && !isStaff()) hasAccess = false;
  if (permission && !hasPermission(permission)) hasAccess = false;
  if (role && !hasRole(role)) hasAccess = false;

  if (!hasAccess && !showWhenDisabled) {
    return null;
  }

  return (
    <button
      {...props}
      disabled={disabled || !hasAccess}
      title={!hasAccess ? fallbackText : props.title}
    >
      {!hasAccess && showWhenDisabled ? fallbackText : children}
    </button>
  );
};

export default {
  HasPermission,
  HasPermissions,
  HasRole,
  HasRoles,
  HasAny,
  AdminOnly,
  StaffOnly,
  AuthenticatedOnly,
  UnauthenticatedOnly,
  ConditionalRender,
  PermissionButton,
  withPermission,
  withRole,
  withAdminOnly,
  usePermissions,
};
