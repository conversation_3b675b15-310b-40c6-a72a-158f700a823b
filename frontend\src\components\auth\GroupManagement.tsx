import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Shield, 
  UserPlus,
  UserMinus,
  Copy,
  RefreshCw,
  Settings,
  Lock
} from 'lucide-react';
import { toast } from 'sonner';
import { groupAPI, permissionAPI, userAPI } from '@/services/authAPI';
import { Group, Permission, User, CreateGroupRequest, UpdateGroupRequest } from '@/types/auth';

const GroupManagement: React.FC = () => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showMembersDialog, setShowMembersDialog] = useState(false);
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Form states
  const [createForm, setCreateForm] = useState<CreateGroupRequest>({
    name: '',
    description: '',
    permissions: []
  });

  const [editForm, setEditForm] = useState<UpdateGroupRequest>({});

  useEffect(() => {
    loadGroups();
    loadPermissions();
    loadUsers();
  }, []);

  const loadGroups = async () => {
    try {
      setLoading(true);
      const response = await groupAPI.getGroups({ search: searchTerm });
      setGroups(response.data.results || []);
    } catch (error) {
      console.warn('Groups API not available:', error);
      setGroups([]); // Ensure it's always an array
      // Don't show error toast for missing/rate-limited API endpoints
    } finally {
      setLoading(false);
    }
  };

  const loadPermissions = async () => {
    try {
      const response = await permissionAPI.getPermissions();
      setPermissions(response.data.results || []);
    } catch (error) {
      console.warn('Permissions API not available:', error);
      setPermissions([]); // Ensure it's always an array
    }
  };

  const loadUsers = async () => {
    try {
      const response = await userAPI.getUsers();
      setUsers(response.data.results || []);
    } catch (error) {
      console.warn('Users API not available:', error);
      setUsers([]); // Ensure it's always an array
    }
  };

  const handleCreateGroup = async () => {
    try {
      await groupAPI.createGroup(createForm);
      toast.success('Group created successfully');
      setShowCreateDialog(false);
      setCreateForm({ name: '', description: '', permissions: [] });
      loadGroups();
    } catch (error) {
      toast.error('Failed to create group');
      console.error('Error creating group:', error);
    }
  };

  const handleUpdateGroup = async () => {
    if (!editingGroup) return;
    
    try {
      await groupAPI.updateGroup(editingGroup.id, editForm);
      toast.success('Group updated successfully');
      setShowEditDialog(false);
      setEditingGroup(null);
      setEditForm({});
      loadGroups();
    } catch (error) {
      toast.error('Failed to update group');
      console.error('Error updating group:', error);
    }
  };

  const handleDeleteGroup = async (groupId: number) => {
    if (!confirm('Are you sure you want to delete this group?')) return;
    
    try {
      await groupAPI.deleteGroup(groupId);
      toast.success('Group deleted successfully');
      loadGroups();
    } catch (error) {
      toast.error('Failed to delete group');
      console.error('Error deleting group:', error);
    }
  };

  const handleCloneGroup = async (group: Group) => {
    const newName = prompt('Enter name for cloned group:', `${group.name} (Copy)`);
    if (!newName) return;
    
    try {
      await groupAPI.cloneGroup(group.id, newName);
      toast.success('Group cloned successfully');
      loadGroups();
    } catch (error) {
      toast.error('Failed to clone group');
      console.error('Error cloning group:', error);
    }
  };

  const openEditDialog = (group: Group) => {
    setEditingGroup(group);
    setEditForm({
      name: group.name,
      description: group.description || '',
      permissions: group.permissions.map(p => p.id),
    });
    setShowEditDialog(true);
  };

  const openMembersDialog = async (group: Group) => {
    setSelectedGroup(group);
    setShowMembersDialog(true);
  };

  const openPermissionsDialog = (group: Group) => {
    setSelectedGroup(group);
    setShowPermissionsDialog(true);
  };

  const getPermissionsByApp = () => {
    const permissionsByApp: Record<string, Permission[]> = {};
    permissions.forEach(permission => {
      const app = permission.app_label || 'Unknown';
      if (!permissionsByApp[app]) {
        permissionsByApp[app] = [];
      }
      permissionsByApp[app].push(permission);
    });
    return permissionsByApp;
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Group Management</h1>
          <p className="text-muted-foreground">
            Manage user groups and their permissions
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Group
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Search Groups</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search groups..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <Button variant="outline" onClick={loadGroups}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Groups Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Groups ({filteredGroups.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Group Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Members</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading groups...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredGroups.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="text-muted-foreground">
                        <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No groups found</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredGroups.map((group) => (
                    <TableRow key={group.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{group.name}</div>
                          <div className="text-sm text-muted-foreground">
                            ID: {group.id}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs truncate">
                          {group.description || 'No description'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {group.user_count || 0} members
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {group.permissions.length} permissions
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openEditDialog(group)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openMembersDialog(group)}>
                              <Users className="h-4 w-4 mr-2" />
                              Manage Members
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openPermissionsDialog(group)}>
                              <Shield className="h-4 w-4 mr-2" />
                              Manage Permissions
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleCloneGroup(group)}>
                              <Copy className="h-4 w-4 mr-2" />
                              Clone Group
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteGroup(group.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create Group Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Group</DialogTitle>
            <DialogDescription>
              Create a new group and assign permissions.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="create-name">Group Name *</Label>
              <Input
                id="create-name"
                value={createForm.name}
                onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
                placeholder="Enter group name"
              />
            </div>

            <div>
              <Label htmlFor="create-description">Description</Label>
              <Textarea
                id="create-description"
                value={createForm.description || ''}
                onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
                placeholder="Enter group description"
                rows={3}
              />
            </div>

            <div>
              <Label>Permissions</Label>
              <div className="max-h-64 overflow-y-auto border rounded-md p-4">
                {Object.entries(getPermissionsByApp()).map(([app, appPermissions]) => (
                  <div key={app} className="mb-4">
                    <h4 className="font-medium mb-2 text-sm text-muted-foreground uppercase">
                      {app}
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {appPermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`create-perm-${permission.id}`}
                            checked={createForm.permissions?.includes(permission.id)}
                            onCheckedChange={(checked) => {
                              const currentPermissions = createForm.permissions || [];
                              if (checked) {
                                setCreateForm({
                                  ...createForm,
                                  permissions: [...currentPermissions, permission.id]
                                });
                              } else {
                                setCreateForm({
                                  ...createForm,
                                  permissions: currentPermissions.filter(id => id !== permission.id)
                                });
                              }
                            }}
                          />
                          <Label htmlFor={`create-perm-${permission.id}`} className="text-sm">
                            {permission.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateGroup}>
              Create Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GroupManagement;
