# Generated by Django 5.2.3 on 2025-06-16 16:29

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('alumni_applications', '0004_remove_document_type_fk'),
    ]

    operations = [
        # Step 1: Add temporary integer columns
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplication
            ADD COLUMN year_of_leaving_gregorian_temp INTEGER;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplication
            DROP COLUMN year_of_leaving_gregorian_temp;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplication
            ADD COLUMN year_of_graduation_gregorian_temp INTEGER;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplication
            DROP COLUMN year_of_graduation_gregorian_temp;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplicationmini
            ADD COLUMN year_of_leaving_gregorian_temp INTEGER;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplicationmini
            DROP COLUMN year_of_leaving_gregorian_temp;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplicationmini
            ADD COLUMN year_of_graduation_gregorian_temp INTEGER;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplicationmini
            DROP COLUMN year_of_graduation_gregorian_temp;
            """
        ),

        # Step 2: Copy data from date columns to temp integer columns
        migrations.RunSQL(
            """
            UPDATE alumni_applications_alumniapplication
            SET year_of_leaving_gregorian_temp = EXTRACT(YEAR FROM year_of_leaving_gregorian)::integer
            WHERE year_of_leaving_gregorian IS NOT NULL;
            """,
            reverse_sql=""
        ),
        migrations.RunSQL(
            """
            UPDATE alumni_applications_alumniapplication
            SET year_of_graduation_gregorian_temp = EXTRACT(YEAR FROM year_of_graduation_gregorian)::integer
            WHERE year_of_graduation_gregorian IS NOT NULL;
            """,
            reverse_sql=""
        ),
        migrations.RunSQL(
            """
            UPDATE alumni_applications_alumniapplicationmini
            SET year_of_leaving_gregorian_temp = EXTRACT(YEAR FROM year_of_leaving_gregorian)::integer
            WHERE year_of_leaving_gregorian IS NOT NULL;
            """,
            reverse_sql=""
        ),
        migrations.RunSQL(
            """
            UPDATE alumni_applications_alumniapplicationmini
            SET year_of_graduation_gregorian_temp = EXTRACT(YEAR FROM year_of_graduation_gregorian)::integer
            WHERE year_of_graduation_gregorian IS NOT NULL;
            """,
            reverse_sql=""
        ),

        # Step 3: Drop old date columns
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplication
            DROP COLUMN year_of_leaving_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplication
            ADD COLUMN year_of_leaving_gregorian DATE;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplication
            DROP COLUMN year_of_graduation_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplication
            ADD COLUMN year_of_graduation_gregorian DATE;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplicationmini
            DROP COLUMN year_of_leaving_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplicationmini
            ADD COLUMN year_of_leaving_gregorian DATE;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplicationmini
            DROP COLUMN year_of_graduation_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplicationmini
            ADD COLUMN year_of_graduation_gregorian DATE;
            """
        ),

        # Step 4: Rename temp columns to original names
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplication
            RENAME COLUMN year_of_leaving_gregorian_temp TO year_of_leaving_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplication
            RENAME COLUMN year_of_leaving_gregorian TO year_of_leaving_gregorian_temp;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplication
            RENAME COLUMN year_of_graduation_gregorian_temp TO year_of_graduation_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplication
            RENAME COLUMN year_of_graduation_gregorian TO year_of_graduation_gregorian_temp;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplicationmini
            RENAME COLUMN year_of_leaving_gregorian_temp TO year_of_leaving_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplicationmini
            RENAME COLUMN year_of_leaving_gregorian TO year_of_leaving_gregorian_temp;
            """
        ),
        migrations.RunSQL(
            """
            ALTER TABLE alumni_applications_alumniapplicationmini
            RENAME COLUMN year_of_graduation_gregorian_temp TO year_of_graduation_gregorian;
            """,
            reverse_sql="""
            ALTER TABLE alumni_applications_alumniapplicationmini
            RENAME COLUMN year_of_graduation_gregorian TO year_of_graduation_gregorian_temp;
            """
        ),
    ]
