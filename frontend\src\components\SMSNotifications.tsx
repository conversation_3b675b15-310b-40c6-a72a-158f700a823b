import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { format } from 'date-fns';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { MessageSquare, Plus, Trash, Edit, Eye, Send, Calendar } from 'lucide-react';
import communicationAPI, { SMSNotification } from '@/services/communicationAPI';

const SMSNotifications = () => {
  const [smsMessages, setSMSMessages] = useState<SMSNotification[]>([]);
  const [selectedSMS, setSelectedSMS] = useState<SMSNotification | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [messageLength, setMessageLength] = useState(0);

  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = useForm<Partial<SMSNotification>>();

  // Watch message content to update character count
  const messageContent = watch('message', '');

  useEffect(() => {
    setMessageLength(messageContent?.length || 0);
  }, [messageContent]);

  // Fetch SMS notifications
  const fetchSMSMessages = async () => {
    setIsLoading(true);
    try {
      const response = await communicationAPI.getSMSNotifications();
      // Check if the response is an array
      if (Array.isArray(response)) {
        setSMSMessages(response);
      } else if (response && typeof response === 'object' && Array.isArray(response.results)) {
        // Handle DRF pagination format
        setSMSMessages(response.results);
      } else {
        console.error('Unexpected response format:', response);
        setSMSMessages([]);
        toast.error('Received invalid data format from server');
      }
    } catch (error) {
      console.error('Error fetching SMS notifications:', error);
      toast.error('Failed to load SMS notifications');
      setSMSMessages([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSMSMessages();
  }, []);

  // Filter SMS messages based on active tab
  const filteredSMSMessages = Array.isArray(smsMessages)
    ? smsMessages.filter(sms => {
        if (activeTab === 'all') return true;
        if (activeTab === 'draft') return sms.status === 'draft';
        if (activeTab === 'scheduled') return sms.status === 'scheduled';
        if (activeTab === 'sent') return sms.status === 'sent';
        if (activeTab === 'failed') return sms.status === 'failed';
        return true;
      })
    : [];

  // Handle form submission
  const onSubmit = async (data: Partial<SMSNotification>) => {
    setIsLoading(true);
    try {
      if (isEditMode && selectedSMS) {
        await communicationAPI.updateSMSNotification(selectedSMS.id, data);
        toast.success('SMS notification updated successfully');
      } else {
        await communicationAPI.createSMSNotification(data);
        toast.success('SMS notification created successfully');
      }
      fetchSMSMessages();
      setIsDialogOpen(false);
      reset();
    } catch (error) {
      console.error('Error saving SMS notification:', error);
      toast.error('Failed to save SMS notification');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this SMS notification?')) {
      setIsLoading(true);
      try {
        await communicationAPI.deleteSMSNotification(id);
        toast.success('SMS notification deleted successfully');
        fetchSMSMessages();
      } catch (error) {
        console.error('Error deleting SMS notification:', error);
        toast.error('Failed to delete SMS notification');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle send SMS
  const handleSendSMS = async (id: number) => {
    setIsLoading(true);
    try {
      await communicationAPI.sendSMSNotification(id);
      toast.success('SMS sent successfully');
      fetchSMSMessages();
    } catch (error) {
      console.error('Error sending SMS:', error);
      toast.error('Failed to send SMS');
    } finally {
      setIsLoading(false);
    }
  };

  // Open create dialog
  const openCreateDialog = () => {
    setIsEditMode(false);
    setSelectedSMS(null);
    reset({
      message: '',
      recipients: '',
      status: 'draft',
      scheduled_time: ''
    });
    setMessageLength(0);
    setIsDialogOpen(true);
  };

  // Open edit dialog
  const openEditDialog = (sms: SMSNotification) => {
    setIsEditMode(true);
    setSelectedSMS(sms);
    setValue('message', sms.message);
    setValue('recipients', sms.recipients);
    setValue('status', sms.status);
    setValue('scheduled_time', sms.scheduled_time ? sms.scheduled_time.slice(0, 16) : '');
    setMessageLength(sms.message.length);
    setIsDialogOpen(true);
  };

  // Open view dialog
  const openViewDialog = (sms: SMSNotification) => {
    setSelectedSMS(sms);
    setIsViewDialogOpen(true);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'scheduled':
        return <Badge variant="secondary">Scheduled</Badge>;
      case 'sent':
        return <Badge variant="success" className="bg-green-100 text-green-800">Sent</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get character count color
  const getCharCountColor = () => {
    if (messageLength > 160) return 'text-red-500';
    if (messageLength > 140) return 'text-yellow-500';
    return 'text-gray-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">SMS Notifications</h2>
          <p className="text-muted-foreground">
            Manage SMS communications with users
          </p>
        </div>
        <Button onClick={openCreateDialog}>
          <Plus className="h-4 w-4 mr-2" />
          New SMS
        </Button>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="draft">Draft</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="sent">Sent</TabsTrigger>
          <TabsTrigger value="failed">Failed</TabsTrigger>
        </TabsList>
        <TabsContent value={activeTab} className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Message</TableHead>
                    <TableHead>Recipients</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Length</TableHead>
                    <TableHead>Scheduled Time</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        Loading SMS notifications...
                      </TableCell>
                    </TableRow>
                  ) : filteredSMSMessages.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No SMS notifications found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredSMSMessages.map((sms) => (
                      <TableRow key={sms.id}>
                        <TableCell className="font-medium">
                          {sms.message.length > 40
                            ? `${sms.message.substring(0, 40)}...`
                            : sms.message}
                        </TableCell>
                        <TableCell>{sms.recipients.split(',').length} recipients</TableCell>
                        <TableCell>{getStatusBadge(sms.status)}</TableCell>
                        <TableCell>
                          <span className={sms.message_length > 160 ? 'text-red-500' : ''}>
                            {sms.message_length} chars
                          </span>
                        </TableCell>
                        <TableCell>
                          {sms.scheduled_time
                            ? format(new Date(sms.scheduled_time), 'MMM d, yyyy HH:mm')
                            : 'Not scheduled'}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openViewDialog(sms)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {sms.status === 'draft' && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => openEditDialog(sms)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                            {(sms.status === 'draft' || sms.status === 'scheduled') && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleSendSMS(sms.id)}
                              >
                                <Send className="h-4 w-4" />
                              </Button>
                            )}
                            {sms.status === 'draft' && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDelete(sms.id)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? 'Edit SMS Notification' : 'Create New SMS Notification'}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? 'Update the SMS notification details below'
                : 'Fill in the details to create a new SMS notification'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="message" className="text-right">
                  Message
                </Label>
                <div className="col-span-3 space-y-2">
                  <Textarea
                    id="message"
                    className="resize-none"
                    rows={4}
                    maxLength={160}
                    {...register('message', {
                      required: 'Message is required',
                      maxLength: {
                        value: 160,
                        message: 'Message cannot exceed 160 characters'
                      }
                    })}
                  />
                  <div className={`text-xs flex justify-end ${getCharCountColor()}`}>
                    {messageLength}/160 characters
                  </div>
                </div>
                {errors.message && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.message.message}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="recipients" className="text-right">
                  Recipients
                </Label>
                <Input
                  id="recipients"
                  className="col-span-3"
                  placeholder="+251912345678, +251987654321"
                  {...register('recipients', { required: 'Recipients are required' })}
                />
                {errors.recipients && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.recipients.message}
                  </p>
                )}
                <p className="text-gray-500 text-sm col-span-3 col-start-2">
                  Comma-separated list of phone numbers or recipient groups
                </p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <Select
                  defaultValue="draft"
                  onValueChange={(value) => setValue('status', value as 'draft' | 'scheduled')}
                  value={selectedSMS?.status}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="scheduled_time" className="text-right">
                  Schedule Time
                </Label>
                <Input
                  id="scheduled_time"
                  type="datetime-local"
                  className="col-span-3"
                  {...register('scheduled_time')}
                />
                <p className="text-gray-500 text-sm col-span-3 col-start-2">
                  Optional. Leave blank for drafts or to send immediately.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || messageLength > 160}
              >
                {isLoading ? 'Saving...' : isEditMode ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              SMS Notification
            </DialogTitle>
            <div className="flex items-center gap-2 mt-2">
              {selectedSMS && getStatusBadge(selectedSMS.status)}
              <Badge variant={selectedSMS?.message_length > 160 ? 'destructive' : 'outline'}>
                {selectedSMS?.message_length} characters
              </Badge>
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="mb-4">
              <h4 className="text-sm font-semibold mb-1">Recipients:</h4>
              <p className="text-sm">{selectedSMS?.recipients}</p>
            </div>
            <div className="p-4 bg-gray-50 rounded-md border">
              <p className="whitespace-pre-wrap">{selectedSMS?.message}</p>
            </div>
            <div className="mt-4 text-sm text-gray-500 flex flex-col gap-1">
              {selectedSMS?.scheduled_time && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    Scheduled: {format(new Date(selectedSMS.scheduled_time), 'PPpp')}
                  </span>
                </div>
              )}
              {selectedSMS?.sent_time && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    Sent: {format(new Date(selectedSMS.sent_time), 'PPpp')}
                  </span>
                </div>
              )}
              <div className="mt-2">
                Created by: {selectedSMS?.sender_details?.full_name || 'Unknown'}
              </div>
            </div>
          </div>
          <DialogFooter>
            {(selectedSMS?.status === 'draft' || selectedSMS?.status === 'scheduled') && (
              <Button
                variant="secondary"
                onClick={() => {
                  handleSendSMS(selectedSMS.id);
                  setIsViewDialogOpen(false);
                }}
              >
                <Send className="h-4 w-4 mr-2" />
                Send Now
              </Button>
            )}
            <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SMSNotifications;
