import { OrganizationSetting, QuickLink, SocialMediaLink } from './settingsAPI';

// Cache expiration time in milliseconds (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

interface CacheItem<T> {
  data: T;
  timestamp: number;
}

class SettingsCacheService {
  private organizationSettingsCache: CacheItem<OrganizationSetting> | null = null;
  private quickLinksCache: CacheItem<QuickLink[]> | null = null;
  private socialMediaLinksCache: CacheItem<SocialMediaLink[]> | null = null;

  // Store organization settings in cache
  setOrganizationSettings(data: OrganizationSetting): void {
    this.organizationSettingsCache = {
      data,
      timestamp: Date.now()
    };
  }

  // Get organization settings from cache if valid
  getOrganizationSettings(): OrganizationSetting | null {
    if (!this.organizationSettingsCache) return null;
    
    const isExpired = Date.now() - this.organizationSettingsCache.timestamp > CACHE_EXPIRATION;
    return isExpired ? null : this.organizationSettingsCache.data;
  }

  // Store quick links in cache
  setQuickLinks(data: QuickLink[]): void {
    this.quickLinksCache = {
      data,
      timestamp: Date.now()
    };
  }

  // Get quick links from cache if valid
  getQuickLinks(): QuickLink[] | null {
    if (!this.quickLinksCache) return null;
    
    const isExpired = Date.now() - this.quickLinksCache.timestamp > CACHE_EXPIRATION;
    return isExpired ? null : this.quickLinksCache.data;
  }

  // Store social media links in cache
  setSocialMediaLinks(data: SocialMediaLink[]): void {
    this.socialMediaLinksCache = {
      data,
      timestamp: Date.now()
    };
  }

  // Get social media links from cache if valid
  getSocialMediaLinks(): SocialMediaLink[] | null {
    if (!this.socialMediaLinksCache) return null;
    
    const isExpired = Date.now() - this.socialMediaLinksCache.timestamp > CACHE_EXPIRATION;
    return isExpired ? null : this.socialMediaLinksCache.data;
  }

  // Clear all caches
  clearCache(): void {
    this.organizationSettingsCache = null;
    this.quickLinksCache = null;
    this.socialMediaLinksCache = null;
  }
}

// Create a singleton instance
const settingsCacheService = new SettingsCacheService();

export default settingsCacheService;
