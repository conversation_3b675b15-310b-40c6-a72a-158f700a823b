import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './card';
import { TrendingUp, TrendingDown, Users, Building, GraduationCap, BookOpen } from 'lucide-react';

interface SummaryCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  icon: React.ReactNode;
  color: string;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ 
  title, 
  value, 
  subtitle, 
  trend, 
  icon, 
  color 
}) => {
  return (
    <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
            <div className="text-2xl font-bold text-gray-900 mt-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </div>
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-xl shadow-lg ${color}`}>
            {icon}
          </div>
        </div>
      </CardHeader>
      {trend && (
        <CardContent className="pt-0">
          <div className="flex items-center space-x-2">
            {trend.isPositive ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
            <span className={`text-sm font-medium ${
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {trend.isPositive ? '+' : ''}{trend.value}%
            </span>
            <span className="text-xs text-gray-500">vs {trend.period}</span>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

interface EnhancedSummaryCardsProps {
  totalGraduates: number;
  filteredGraduates: number;
  activeColleges: number;
  totalColleges: number;
  activeDepartments: number;
  previousPeriodData?: {
    totalGraduates: number;
    filteredGraduates: number;
  };
  isFiltered: boolean;
}

export const EnhancedSummaryCards: React.FC<EnhancedSummaryCardsProps> = ({
  totalGraduates,
  filteredGraduates,
  activeColleges,
  totalColleges,
  activeDepartments,
  previousPeriodData,
  isFiltered
}) => {
  // Calculate trends
  const totalTrend = previousPeriodData ? {
    value: Math.round(((totalGraduates - previousPeriodData.totalGraduates) / previousPeriodData.totalGraduates) * 100),
    isPositive: totalGraduates > previousPeriodData.totalGraduates,
    period: 'previous period'
  } : undefined;

  const filteredTrend = previousPeriodData && isFiltered ? {
    value: Math.round(((filteredGraduates - previousPeriodData.filteredGraduates) / previousPeriodData.filteredGraduates) * 100),
    isPositive: filteredGraduates > previousPeriodData.filteredGraduates,
    period: 'previous period'
  } : undefined;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <SummaryCard
        title="Total Graduates"
        value={totalGraduates}
        subtitle="All-time count"
        trend={totalTrend}
        icon={<GraduationCap className="h-6 w-6 text-white" />}
        color="bg-gradient-to-br from-blue-500 to-blue-600"
      />
      
      <SummaryCard
        title={isFiltered ? "Filtered Graduates" : "Current Graduates"}
        value={isFiltered ? filteredGraduates : totalGraduates}
        subtitle={isFiltered ? "Based on current filters" : "Total graduates"}
        trend={isFiltered ? filteredTrend : totalTrend}
        icon={<Users className="h-6 w-6 text-white" />}
        color="bg-gradient-to-br from-green-500 to-green-600"
      />
      
      <SummaryCard
        title="Active Colleges"
        value={isFiltered ? `${activeColleges} of ${totalColleges}` : activeColleges}
        subtitle={isFiltered ? "colleges with graduates" : "total colleges"}
        icon={<Building className="h-6 w-6 text-white" />}
        color="bg-gradient-to-br from-purple-500 to-purple-600"
      />
      
      <SummaryCard
        title="Active Departments"
        value={activeDepartments}
        subtitle={isFiltered ? "across selected colleges" : "total departments"}
        icon={<BookOpen className="h-6 w-6 text-white" />}
        color="bg-gradient-to-br from-orange-500 to-orange-600"
      />
    </div>
  );
};

export default EnhancedSummaryCards;
