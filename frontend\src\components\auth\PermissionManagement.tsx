import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { 
  Shield, 
  Search, 
  Filter, 
  RefreshCw,
  Lock,
  Unlock,
  Database,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'sonner';
import { permissionAPI, groupAPI, userAPI } from '@/services/authAPI';
import { Permission, Group, User, PermissionFilters } from '@/types/auth';

const PermissionManagement: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [contentTypes, setContentTypes] = useState<Array<{ id: number; app_label: string; model: string; name: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<PermissionFilters>({});
  const [selectedPermissions, setSelectedPermissions] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState('permissions');

  useEffect(() => {
    loadPermissions();
    loadGroups();
    loadUsers();
    loadContentTypes();
  }, [filters]);

  const loadPermissions = async () => {
    try {
      setLoading(true);
      const response = await permissionAPI.getPermissions(filters);
      setPermissions(response.data.results);
    } catch (error) {
      toast.error('Failed to load permissions');
      console.error('Error loading permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadGroups = async () => {
    try {
      const response = await groupAPI.getGroups();
      setGroups(response.data.results);
    } catch (error) {
      console.error('Error loading groups:', error);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await userAPI.getUsers();
      setUsers(response.data.results);
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const loadContentTypes = async () => {
    try {
      const response = await permissionAPI.getContentTypes();
      setContentTypes(response.data);
    } catch (error) {
      console.error('Error loading content types:', error);
    }
  };

  const getPermissionsByApp = () => {
    const permissionsByApp: Record<string, Permission[]> = {};
    permissions.forEach(permission => {
      const app = permission.app_label || 'Unknown';
      if (!permissionsByApp[app]) {
        permissionsByApp[app] = [];
      }
      permissionsByApp[app].push(permission);
    });
    return permissionsByApp;
  };

  const getPermissionsByContentType = () => {
    const permissionsByContentType: Record<string, Permission[]> = {};
    permissions.forEach(permission => {
      const contentType = permission.content_type_name || 'Unknown';
      if (!permissionsByContentType[contentType]) {
        permissionsByContentType[contentType] = [];
      }
      permissionsByContentType[contentType].push(permission);
    });
    return permissionsByContentType;
  };

  const getPermissionIcon = (codename: string) => {
    if (codename.includes('view')) return <Eye className="h-4 w-4" />;
    if (codename.includes('add')) return <Shield className="h-4 w-4" />;
    if (codename.includes('change')) return <Settings className="h-4 w-4" />;
    if (codename.includes('delete')) return <Lock className="h-4 w-4" />;
    return <Database className="h-4 w-4" />;
  };

  const getPermissionColor = (codename: string) => {
    if (codename.includes('view')) return 'bg-blue-100 text-blue-800';
    if (codename.includes('add')) return 'bg-green-100 text-green-800';
    if (codename.includes('change')) return 'bg-yellow-100 text-yellow-800';
    if (codename.includes('delete')) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getGroupsWithPermission = (permissionId: number) => {
    return groups.filter(group => 
      group.permissions.some(p => p.id === permissionId)
    );
  };

  const getUsersWithPermission = (permissionId: number) => {
    return users.filter(user => 
      user.user_permissions.some(p => p.id === permissionId) ||
      user.groups.some(group => 
        group.permissions.some(p => p.id === permissionId)
      )
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Permission Management</h1>
          <p className="text-muted-foreground">
            View and manage system permissions
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search permissions..."
                  className="pl-10"
                  value={filters.search || ''}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="app-filter">App</Label>
              <Select
                value={filters.app_label || ''}
                onValueChange={(value) => setFilters({ ...filters, app_label: value || undefined })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All apps" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All apps</SelectItem>
                  {Array.from(new Set(permissions.map(p => p.app_label).filter(Boolean))).map(app => (
                    <SelectItem key={app} value={app!}>{app}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="model-filter">Model</Label>
              <Select
                value={filters.model || ''}
                onValueChange={(value) => setFilters({ ...filters, model: value || undefined })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All models" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All models</SelectItem>
                  {Array.from(new Set(permissions.map(p => p.model).filter(Boolean))).map(model => (
                    <SelectItem key={model} value={model!}>{model}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button variant="outline" onClick={loadPermissions}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="permissions">All Permissions</TabsTrigger>
          <TabsTrigger value="by-app">By Application</TabsTrigger>
          <TabsTrigger value="by-model">By Model</TabsTrigger>
        </TabsList>

        {/* All Permissions Tab */}
        <TabsContent value="permissions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>All Permissions ({permissions.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedPermissions.length === permissions.length && permissions.length > 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedPermissions(permissions.map(p => p.id));
                            } else {
                              setSelectedPermissions([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Permission</TableHead>
                      <TableHead>Codename</TableHead>
                      <TableHead>App</TableHead>
                      <TableHead>Model</TableHead>
                      <TableHead>Groups</TableHead>
                      <TableHead>Users</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="flex items-center justify-center space-x-2">
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            <span>Loading permissions...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : permissions.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="text-muted-foreground">
                            <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No permissions found</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      permissions.map((permission) => {
                        const groupsWithPermission = getGroupsWithPermission(permission.id);
                        const usersWithPermission = getUsersWithPermission(permission.id);
                        
                        return (
                          <TableRow key={permission.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedPermissions.includes(permission.id)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setSelectedPermissions([...selectedPermissions, permission.id]);
                                  } else {
                                    setSelectedPermissions(selectedPermissions.filter(id => id !== permission.id));
                                  }
                                }}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                {getPermissionIcon(permission.codename)}
                                <span className="font-medium">{permission.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge 
                                variant="outline" 
                                className={getPermissionColor(permission.codename)}
                              >
                                {permission.codename}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">
                                {permission.app_label || 'Unknown'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {permission.model || 'Unknown'}
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {groupsWithPermission.slice(0, 2).map((group) => (
                                  <Badge key={group.id} variant="outline" className="text-xs">
                                    {group.name}
                                  </Badge>
                                ))}
                                {groupsWithPermission.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{groupsWithPermission.length - 2} more
                                  </Badge>
                                )}
                                {groupsWithPermission.length === 0 && (
                                  <span className="text-muted-foreground text-sm">None</span>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {usersWithPermission.slice(0, 2).map((user) => (
                                  <Badge key={user.id} variant="outline" className="text-xs">
                                    {user.username}
                                  </Badge>
                                ))}
                                {usersWithPermission.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{usersWithPermission.length - 2} more
                                  </Badge>
                                )}
                                {usersWithPermission.length === 0 && (
                                  <span className="text-muted-foreground text-sm">None</span>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* By Application Tab */}
        <TabsContent value="by-app">
          <div className="space-y-4">
            {Object.entries(getPermissionsByApp()).map(([app, appPermissions]) => (
              <Card key={app}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Database className="h-5 w-5" />
                    <span>{app} ({appPermissions.length} permissions)</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {appPermissions.map((permission) => (
                      <div key={permission.id} className="border rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          {getPermissionIcon(permission.codename)}
                          <span className="font-medium text-sm">{permission.name}</span>
                        </div>
                        <div className="space-y-2">
                          <Badge
                            variant="outline"
                            className={`${getPermissionColor(permission.codename)} text-xs`}
                          >
                            {permission.codename}
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            Model: {permission.model || 'Unknown'}
                          </div>
                          <div className="text-xs">
                            <span className="font-medium">Groups:</span> {getGroupsWithPermission(permission.id).length}
                          </div>
                          <div className="text-xs">
                            <span className="font-medium">Users:</span> {getUsersWithPermission(permission.id).length}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* By Model Tab */}
        <TabsContent value="by-model">
          <div className="space-y-4">
            {Object.entries(getPermissionsByContentType()).map(([contentType, modelPermissions]) => (
              <Card key={contentType}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>{contentType} ({modelPermissions.length} permissions)</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {modelPermissions.map((permission) => (
                      <div key={permission.id} className="border rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-2">
                          {getPermissionIcon(permission.codename)}
                          <Badge
                            variant="outline"
                            className={`${getPermissionColor(permission.codename)} text-xs`}
                          >
                            {permission.codename.split('_')[0]}
                          </Badge>
                        </div>
                        <div className="text-sm font-medium mb-1">
                          {permission.name}
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>App: {permission.app_label}</div>
                          <div>Groups: {getGroupsWithPermission(permission.id).length}</div>
                          <div>Users: {getUsersWithPermission(permission.id).length}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PermissionManagement;
