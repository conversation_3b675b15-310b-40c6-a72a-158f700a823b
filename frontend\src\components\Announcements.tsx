import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { format } from 'date-fns';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Megaphone, Plus, Trash, Edit, Eye, Calendar } from 'lucide-react';
import communicationAPI, { Announcement } from '@/services/communicationAPI';

const Announcements = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<Partial<Announcement>>();

  // Fetch announcements
  const fetchAnnouncements = async () => {
    setIsLoading(true);
    try {
      const response = await communicationAPI.getAnnouncements();
      // Check if the response is an array
      if (Array.isArray(response)) {
        setAnnouncements(response);
      } else if (response && typeof response === 'object' && Array.isArray(response.results)) {
        // Handle DRF pagination format
        setAnnouncements(response.results);
      } else {
        console.error('Unexpected response format:', response);
        setAnnouncements([]);
        toast.error('Received invalid data format from server');
      }
    } catch (error) {
      console.error('Error fetching announcements:', error);
      toast.error('Failed to load announcements');
      setAnnouncements([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  // Filter announcements based on active tab
  const filteredAnnouncements = Array.isArray(announcements)
    ? announcements.filter(announcement => {
        if (activeTab === 'all') return true;
        if (activeTab === 'active') return announcement.is_active && !announcement.is_expired;
        if (activeTab === 'inactive') return !announcement.is_active || announcement.is_expired;
        return true;
      })
    : [];

  // Handle form submission
  const onSubmit = async (data: Partial<Announcement>) => {
    setIsLoading(true);
    try {
      // Clean the data before sending
      const formatDateForAPI = (dateString: string) => {
        if (!dateString || dateString.trim() === '') return null;

        try {
          // Create a Date object from the input
          let date: Date;

          if (dateString.includes('T')) {
            // If it's already in datetime format
            if (dateString.length === 16) {
              // Add seconds if missing (YYYY-MM-DDTHH:MM -> YYYY-MM-DDTHH:MM:SS)
              dateString = dateString + ':00';
            }
            date = new Date(dateString);
          } else {
            // If it's just a date, assume it's local time
            date = new Date(dateString);
          }

          // Return ISO string which includes timezone information
          return date.toISOString();
        } catch (error) {
          console.error('Error formatting date:', error);
          return null;
        }
      };

      const cleanData = {
        title: data.title,
        content: data.content,
        priority: data.priority || 'medium',
        target_audience: data.target_audience || 'all',
        is_active: data.is_active !== undefined ? data.is_active : true,
        start_date: formatDateForAPI(data.start_date || ''),
        end_date: formatDateForAPI(data.end_date || '')
      };

      console.log('Sending announcement data:', cleanData);

      if (isEditMode && selectedAnnouncement) {
        await communicationAPI.updateAnnouncement(selectedAnnouncement.id, cleanData);
        toast.success('Announcement updated successfully');
      } else {
        await communicationAPI.createAnnouncement(cleanData);
        toast.success('Announcement created successfully');
      }
      fetchAnnouncements();
      setIsDialogOpen(false);
      reset();
    } catch (error: any) {
      console.error('Error saving announcement:', error);
      console.error('Error response:', error.response?.data);

      let errorMessage = 'Failed to save announcement';
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.response?.data) {
        // Handle field-specific errors
        const errors = error.response.data;
        const errorMessages = Object.entries(errors).map(([field, messages]) => {
          if (Array.isArray(messages)) {
            return `${field}: ${messages.join(', ')}`;
          }
          return `${field}: ${messages}`;
        });
        errorMessage = errorMessages.join('; ');
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this announcement?')) {
      setIsLoading(true);
      try {
        await communicationAPI.deleteAnnouncement(id);
        toast.success('Announcement deleted successfully');
        fetchAnnouncements();
      } catch (error) {
        console.error('Error deleting announcement:', error);
        toast.error('Failed to delete announcement');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Open create dialog
  const openCreateDialog = () => {
    setIsEditMode(false);
    setSelectedAnnouncement(null);

    // Format current date for datetime-local input
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);

    reset({
      title: '',
      content: '',
      priority: 'medium',
      target_audience: 'all',
      is_active: true,
      start_date: localDateTime,
      end_date: ''
    });
    setIsDialogOpen(true);
  };

  // Open edit dialog
  const openEditDialog = (announcement: Announcement) => {
    setIsEditMode(true);
    setSelectedAnnouncement(announcement);

    // Format dates for datetime-local inputs
    const formatDateForInput = (dateString: string) => {
      const date = new Date(dateString);
      return new Date(date.getTime() - date.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    };

    setValue('title', announcement.title);
    setValue('content', announcement.content);
    setValue('priority', announcement.priority);
    setValue('target_audience', announcement.target_audience);
    setValue('is_active', announcement.is_active);
    setValue('start_date', formatDateForInput(announcement.start_date));
    setValue('end_date', announcement.end_date ? formatDateForInput(announcement.end_date) : '');
    setIsDialogOpen(true);
  };

  // Open view dialog
  const openViewDialog = (announcement: Announcement) => {
    setSelectedAnnouncement(announcement);
    setIsViewDialogOpen(true);
  };

  // Get priority badge color
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">High</Badge>;
      case 'medium':
        return <Badge variant="default">Medium</Badge>;
      case 'low':
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  // Get status badge
  const getStatusBadge = (announcement: Announcement) => {
    if (announcement.is_expired) {
      return <Badge variant="outline" className="bg-gray-200 text-gray-700">Expired</Badge>;
    }
    return announcement.is_active
      ? <Badge variant="success" className="bg-green-100 text-green-800">Active</Badge>
      : <Badge variant="secondary" className="bg-gray-100 text-gray-800">Inactive</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Announcements</h2>
          <p className="text-muted-foreground">
            Manage system-wide announcements for users
          </p>
        </div>
        <Button onClick={openCreateDialog}>
          <Plus className="h-4 w-4 mr-2" />
          New Announcement
        </Button>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive/Expired</TabsTrigger>
        </TabsList>
        <TabsContent value={activeTab} className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Target Audience</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        Loading announcements...
                      </TableCell>
                    </TableRow>
                  ) : filteredAnnouncements.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        No announcements found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAnnouncements.map((announcement) => (
                      <TableRow key={announcement.id}>
                        <TableCell className="font-medium">{announcement.title}</TableCell>
                        <TableCell>{getPriorityBadge(announcement.priority)}</TableCell>
                        <TableCell className="capitalize">{announcement.target_audience}</TableCell>
                        <TableCell>{getStatusBadge(announcement)}</TableCell>
                        <TableCell>{format(new Date(announcement.start_date), 'MMM d, yyyy')}</TableCell>
                        <TableCell>
                          {announcement.end_date
                            ? format(new Date(announcement.end_date), 'MMM d, yyyy')
                            : 'No end date'}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openViewDialog(announcement)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openEditDialog(announcement)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(announcement.id)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? 'Edit Announcement' : 'Create New Announcement'}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? 'Update the announcement details below'
                : 'Fill in the details to create a new announcement'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="title" className="text-right">
                  Title
                </Label>
                <Input
                  id="title"
                  className="col-span-3"
                  {...register('title', { required: 'Title is required' })}
                />
                {errors.title && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.title.message}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="content" className="text-right">
                  Content
                </Label>
                <Textarea
                  id="content"
                  className="col-span-3"
                  rows={5}
                  {...register('content', { required: 'Content is required' })}
                />
                {errors.content && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.content.message}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">
                  Priority
                </Label>
                <Select
                  defaultValue="medium"
                  onValueChange={(value) => setValue('priority', value as 'low' | 'medium' | 'high')}
                  value={selectedAnnouncement?.priority}
                >
                  <SelectTrigger className="col-span-3" id="priority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">
                  Target Audience
                </Label>
                <Select
                  defaultValue="all"
                  onValueChange={(value) => setValue('target_audience', value as 'all' | 'students' | 'staff' | 'applicants')}
                  value={selectedAnnouncement?.target_audience}
                >
                  <SelectTrigger className="col-span-3" id="target_audience">
                    <SelectValue placeholder="Select audience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    <SelectItem value="students">Students</SelectItem>
                    <SelectItem value="staff">Staff</SelectItem>
                    <SelectItem value="applicants">Applicants</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="start_date" className="text-right">
                  Start Date
                </Label>
                <Input
                  id="start_date"
                  type="datetime-local"
                  className="col-span-3"
                  {...register('start_date', { required: 'Start date is required' })}
                />
                {errors.start_date && (
                  <p className="text-red-500 text-sm col-span-3 col-start-2">
                    {errors.start_date.message}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="end_date" className="text-right">
                  End Date
                </Label>
                <Input
                  id="end_date"
                  type="datetime-local"
                  className="col-span-3"
                  {...register('end_date')}
                />
                <p className="text-gray-500 text-sm col-span-3 col-start-2">
                  Optional. Leave blank for announcements with no end date.
                </p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="is_active" className="text-right">
                  Active
                </Label>
                <div className="col-span-3 flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    className="mr-2 h-4 w-4"
                    {...register('is_active')}
                  />
                  <span className="text-sm text-gray-600">Make this announcement active</span>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : isEditMode ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Megaphone className="h-5 w-5" />
              {selectedAnnouncement?.title}
            </DialogTitle>
            <div className="flex items-center gap-2 mt-2">
              {selectedAnnouncement && getPriorityBadge(selectedAnnouncement.priority)}
              {selectedAnnouncement && getStatusBadge(selectedAnnouncement)}
              <Badge variant="outline" className="capitalize">
                {selectedAnnouncement?.target_audience}
              </Badge>
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="prose max-w-none">
              <p>{selectedAnnouncement?.content}</p>
            </div>
            <div className="mt-4 text-sm text-gray-500 flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>
                  Start: {selectedAnnouncement?.start_date && format(new Date(selectedAnnouncement.start_date), 'PPpp')}
                </span>
              </div>
              {selectedAnnouncement?.end_date && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    End: {format(new Date(selectedAnnouncement.end_date), 'PPpp')}
                  </span>
                </div>
              )}
              <div className="mt-2">
                Created by: {selectedAnnouncement?.author_details?.full_name || 'Unknown'}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Announcements;
