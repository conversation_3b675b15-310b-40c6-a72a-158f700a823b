from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from registration.models import ApplicantProgramSelection, ApplicantGAT, ApplicantInformation
from setups.application_information.models import ApplicationInformation
from setups.college.models import College
from setups.department.models import Department
from setups.program.models import Program
from setups.admission_type.models import AdmissionType
from setups.study_field.models import StudyField
from setups.study_program.models import StudyProgram
import random
from django.utils import timezone
import uuid

class Command(BaseCommand):
    help = 'Creates test applicants for development'

    def handle(self, *args, **kwargs):
        # Check if we already have test applicants
        if ApplicantProgramSelection.objects.filter(user__username__startswith='testapplicant').exists():
            self.stdout.write(self.style.WARNING('Test applicants already exist. Skipping creation.'))
            return

        # Create test colleges if they don't exist
        colleges = []
        for i in range(1, 4):
            college, created = College.objects.get_or_create(
                name=f'Test College {i}',
                defaults={
                    'description': f'Test College {i} Description'
                }
            )
            colleges.append(college)
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created college: {college.name}'))

        # Create test departments
        departments = []
        for college in colleges:
            for i in range(1, 3):
                department, created = Department.objects.get_or_create(
                    name=f'{college.name} Department {i}',
                    college=college,
                    defaults={
                        'description': f'Test Department {i} for {college.name}'
                    }
                )
                departments.append(department)
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created department: {department.name}'))

        # Create test programs
        programs = []
        for department in departments:
            for i in range(1, 3):
                program, created = Program.objects.get_or_create(
                    program_name=f'{department.name} Program {i}',
                    defaults={
                        'program_code': f'TP{department.id}{i}',
                        'registration_fee': 1000.00
                    }
                )
                programs.append(program)
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created program: {program.program_name}'))

        # Create test admission types
        admission_type, created = AdmissionType.objects.get_or_create(
            name='Regular',
            defaults={
                'description': 'Regular admission type'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created admission type: {admission_type.name}'))

        # Create test study programs
        study_program, created = StudyProgram.objects.get_or_create(
            program_code='MSC',
            defaults={
                'program_name': 'Master of Science'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created study program: {study_program.program_name}'))

        # Create test study fields and application information
        app_infos = []
        for department in departments:
            # Create study field
            study_field, created = StudyField.objects.get_or_create(
                field_of_study=f'{department.name} Field',
                college=department.college,
                department=department,
                defaults={
                    'code': f'SF{department.id}',
                    'description': f'Study field for {department.name}',
                    'status': True
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created study field: {study_field.field_of_study}'))

            # Create application information
            for program in programs:
                app_info, created = ApplicationInformation.objects.get_or_create(
                    college=department.college,
                    department=department,
                    program=program,
                    field_of_study=study_field,
                    study_program=study_program,
                    admission_type=admission_type,
                    defaults={
                        'spacial_case': '',
                        'duration': '2',
                        'status': True
                    }
                )
                app_infos.append(app_info)
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created application info for: {program.program_name}'))

        # Create test users and applicants
        statuses = ['Pending', 'Approved', 'Rejected']
        sponsorships = ['Ministry of Education', 'Other Government Office', 'University of Gondar', 'Private Office', 'Self']

        for i in range(1, 21):
            # Create user
            username = f'testapplicant{i}'
            email = f'testapplicant{i}@example.com'

            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': f'Test{i}',
                    'last_name': f'Applicant{i}',
                    'is_active': True
                }
            )

            if created:
                user.set_password('password123')
                user.save()
                self.stdout.write(self.style.SUCCESS(f'Created user: {username}'))

            # Create GAT
            gat, created = ApplicantGAT.objects.get_or_create(
                user=user,
                defaults={
                    'GAT_No': f'GAT{i}2023',
                    'GAT_Result': random.randint(60, 95)
                }
            )

            if created:
                self.stdout.write(self.style.SUCCESS(f'Created GAT for: {username}'))

            # Create applicant information
            applicant_info, created = ApplicantInformation.objects.get_or_create(
                author=user,
                defaults={
                    'gender': random.choice(['Male', 'Female']),
                    'dob': timezone.now().date().replace(year=random.randint(1980, 2000), month=random.randint(1, 12), day=random.randint(1, 28)),
                    'mobile': f'+25191{random.randint(1000000, 9999999)}',
                    'program_level': random.choice(['Masters', 'PHD']),
                    'ug_university': f'University {random.randint(1, 5)}',
                    'ug_field_of_study': f'Field {random.randint(1, 10)}',
                    'ug_CGPA': round(random.uniform(2.5, 4.0), 2),
                    'pg_university': f'University {random.randint(1, 5)}' if random.choice([True, False]) else '',
                    'pg_field_of_study': f'Field {random.randint(1, 10)}' if random.choice([True, False]) else '',
                    'pg_CGPA': round(random.uniform(3.0, 4.0), 2) if random.choice([True, False]) else None,
                }
            )

            if created:
                self.stdout.write(self.style.SUCCESS(f'Created applicant info for: {username}'))

            # Create program selection
            app_info = random.choice(app_infos)

            selection, created = ApplicantProgramSelection.objects.get_or_create(
                user=user,
                defaults={
                    'application_num': f'APP{i}2023',
                    'transaction_id': str(uuid.uuid4())[:8].upper(),
                    'sponsorship': random.choice(sponsorships),
                    'application_info': app_info,
                    'gat': gat,
                    'registrar_off_status': random.choice(statuses),
                    'department_status': random.choice(statuses),
                    'payment_status': random.choice(statuses),
                    'remark': f'Test remark for applicant {i}' if random.choice([True, False]) else ''
                }
            )

            if created:
                self.stdout.write(self.style.SUCCESS(f'Created program selection for: {username}'))

        self.stdout.write(self.style.SUCCESS('Successfully created test applicants'))
