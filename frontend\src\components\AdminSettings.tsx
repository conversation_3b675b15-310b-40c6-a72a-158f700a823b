import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {
  Settings,
  Save,
  Clock,
  Bell,
  Shield,
  Globe,
  Mail,
  Palette,
  FileText,
  RefreshCw,
  Database
} from 'lucide-react';

const AdminSettings = () => {
  // System Settings
  const [systemName, setSystemName] = useState('');
  const [systemLogo, setSystemLogo] = useState('');
  const [primaryColor, setPrimaryColor] = useState('');
  const [maintenanceMode, setMaintenanceMode] = useState(false);

  // Registration Settings
  const [registrationEnabled, setRegistrationEnabled] = useState(true);
  const [verificationEnabled, setVerificationEnabled] = useState(true);
  const [applicationEnabled, setApplicationEnabled] = useState(true);
  const [defaultRegistrationPeriod, setDefaultRegistrationPeriod] = useState('30');

  // Security Settings
  const [sessionTimeout, setSessionTimeout] = useState('30');
  const [showTimeoutWarning, setShowTimeoutWarning] = useState(true);
  const [timeoutWarningPeriod, setTimeoutWarningPeriod] = useState('1');
  const [maxLoginAttempts, setMaxLoginAttempts] = useState('5');
  const [requireStrongPasswords, setRequireStrongPasswords] = useState(true);

  // Email Settings
  const [emailSender, setEmailSender] = useState('');
  const [emailSignature, setEmailSignature] = useState('');
  const [sendWelcomeEmail, setSendWelcomeEmail] = useState(true);
  const [sendVerificationEmail, setSendVerificationEmail] = useState(true);

  // Load settings from localStorage on component mount
  useEffect(() => {
    // System settings
    const storedSystemName = localStorage.getItem('systemName');
    const storedPrimaryColor = localStorage.getItem('primaryColor');
    const storedMaintenanceMode = localStorage.getItem('maintenanceMode');

    // Registration settings
    const storedRegistrationEnabled = localStorage.getItem('registrationEnabled');
    const storedVerificationEnabled = localStorage.getItem('verificationEnabled');
    const storedApplicationEnabled = localStorage.getItem('applicationEnabled');
    const storedDefaultRegistrationPeriod = localStorage.getItem('defaultRegistrationPeriod');

    // Security settings
    const storedSessionTimeout = localStorage.getItem('idleTimeoutDuration');
    const storedShowTimeoutWarning = localStorage.getItem('idleTimeoutWarningEnabled');
    const storedTimeoutWarningPeriod = localStorage.getItem('idleTimeoutWarningDuration');
    const storedMaxLoginAttempts = localStorage.getItem('maxLoginAttempts');
    const storedRequireStrongPasswords = localStorage.getItem('requireStrongPasswords');

    // Email settings
    const storedEmailSender = localStorage.getItem('emailSender');
    const storedEmailSignature = localStorage.getItem('emailSignature');
    const storedSendWelcomeEmail = localStorage.getItem('sendWelcomeEmail');
    const storedSendVerificationEmail = localStorage.getItem('sendVerificationEmail');

    // Apply stored values if they exist
    if (storedSystemName) setSystemName(storedSystemName);
    if (storedPrimaryColor) setPrimaryColor(storedPrimaryColor);
    if (storedMaintenanceMode !== null) setMaintenanceMode(storedMaintenanceMode === 'true');

    if (storedRegistrationEnabled !== null) setRegistrationEnabled(storedRegistrationEnabled === 'true');
    if (storedVerificationEnabled !== null) setVerificationEnabled(storedVerificationEnabled === 'true');
    if (storedApplicationEnabled !== null) setApplicationEnabled(storedApplicationEnabled === 'true');
    if (storedDefaultRegistrationPeriod) setDefaultRegistrationPeriod(storedDefaultRegistrationPeriod);

    if (storedSessionTimeout) setSessionTimeout(storedSessionTimeout);
    if (storedShowTimeoutWarning !== null) setShowTimeoutWarning(storedShowTimeoutWarning === 'true');
    if (storedTimeoutWarningPeriod) setTimeoutWarningPeriod(storedTimeoutWarningPeriod);
    if (storedMaxLoginAttempts) setMaxLoginAttempts(storedMaxLoginAttempts);
    if (storedRequireStrongPasswords !== null) setRequireStrongPasswords(storedRequireStrongPasswords === 'true');

    if (storedEmailSender) setEmailSender(storedEmailSender);
    if (storedEmailSignature) setEmailSignature(storedEmailSignature);
    if (storedSendWelcomeEmail !== null) setSendWelcomeEmail(storedSendWelcomeEmail === 'true');
    if (storedSendVerificationEmail !== null) setSendVerificationEmail(storedSendVerificationEmail === 'true');
  }, []);

  // Save system settings
  const saveSystemSettings = () => {
    localStorage.setItem('systemName', systemName);
    localStorage.setItem('primaryColor', primaryColor);
    localStorage.setItem('maintenanceMode', maintenanceMode.toString());

    toast.success('System settings saved successfully');
  };

  // Save registration settings
  const saveRegistrationSettings = () => {
    localStorage.setItem('registrationEnabled', registrationEnabled.toString());
    localStorage.setItem('verificationEnabled', verificationEnabled.toString());
    localStorage.setItem('applicationEnabled', applicationEnabled.toString());
    localStorage.setItem('defaultRegistrationPeriod', defaultRegistrationPeriod);

    toast.success('Registration settings saved successfully');
  };

  // Save security settings
  const saveSecuritySettings = () => {
    localStorage.setItem('idleTimeoutDuration', sessionTimeout);
    localStorage.setItem('idleTimeoutWarningEnabled', showTimeoutWarning.toString());
    localStorage.setItem('idleTimeoutWarningDuration', timeoutWarningPeriod);
    localStorage.setItem('maxLoginAttempts', maxLoginAttempts);
    localStorage.setItem('requireStrongPasswords', requireStrongPasswords.toString());

    toast.success('Security settings saved successfully');

    // Reload the page to apply the new session timeout settings
    window.location.reload();
  };

  // Save email settings
  const saveEmailSettings = () => {
    localStorage.setItem('emailSender', emailSender);
    localStorage.setItem('emailSignature', emailSignature);
    localStorage.setItem('sendWelcomeEmail', sendWelcomeEmail.toString());
    localStorage.setItem('sendVerificationEmail', sendVerificationEmail.toString());

    toast.success('Email settings saved successfully');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Admin Settings</CardTitle>
                <CardDescription className="mt-1">
                  Configure system settings and preferences
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">

      <Tabs defaultValue="system" className="w-full">
        <TabsList className="grid w-full grid-cols-4 mb-6">
          <TabsTrigger
            value="system"
            className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
          >
            <Settings className="h-4 w-4" />
            <span>System</span>
          </TabsTrigger>
          <TabsTrigger
            value="registration"
            className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
          >
            <FileText className="h-4 w-4" />
            <span>Registration</span>
          </TabsTrigger>
          <TabsTrigger
            value="security"
            className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
          >
            <Shield className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
          <TabsTrigger
            value="email"
            className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
          >
            <Mail className="h-4 w-4" />
            <span>Email</span>
          </TabsTrigger>
        </TabsList>

        {/* System Settings */}
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
              <CardDescription>
                Configure general system settings and appearance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="system-name">System Name</Label>
                  <Input
                    id="system-name"
                    value={systemName}
                    onChange={(e) => setSystemName(e.target.value)}
                    placeholder="Enter system name"
                  />
                  <p className="text-xs text-muted-foreground">
                    The name displayed in the browser title and header
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="primary-color">Primary Color</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="primary-color"
                      type="color"
                      value={primaryColor}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      className="w-12 h-10 p-1"
                    />
                    <Input
                      value={primaryColor}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      placeholder="#1a73c0"
                      className="flex-1"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    The main color used throughout the application
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="maintenance-mode" className="font-medium">
                      Maintenance Mode
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      When enabled, only administrators can access the system
                    </p>
                  </div>
                  <Switch
                    id="maintenance-mode"
                    checked={maintenanceMode}
                    onCheckedChange={setMaintenanceMode}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSystemSettings} className="ml-auto">
                <Save className="h-4 w-4 mr-2" />
                Save System Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Registration Settings */}
        <TabsContent value="registration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Registration Settings</CardTitle>
              <CardDescription>
                Configure registration and application settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="registration-enabled" className="font-medium">
                      User Registration
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Allow new users to register accounts
                    </p>
                  </div>
                  <Switch
                    id="registration-enabled"
                    checked={registrationEnabled}
                    onCheckedChange={setRegistrationEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="verification-enabled" className="font-medium">
                      Graduate Verification
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Allow users to verify graduate information
                    </p>
                  </div>
                  <Switch
                    id="verification-enabled"
                    checked={verificationEnabled}
                    onCheckedChange={setVerificationEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="application-enabled" className="font-medium">
                      Program Applications
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Allow users to submit program applications
                    </p>
                  </div>
                  <Switch
                    id="application-enabled"
                    checked={applicationEnabled}
                    onCheckedChange={setApplicationEnabled}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="default-registration-period">Default Registration Period (days)</Label>
                <Select
                  value={defaultRegistrationPeriod}
                  onValueChange={setDefaultRegistrationPeriod}
                >
                  <SelectTrigger id="default-registration-period">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 days</SelectItem>
                    <SelectItem value="14">14 days</SelectItem>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="60">60 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Default duration for new registration periods
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveRegistrationSettings} className="ml-auto">
                <Save className="h-4 w-4 mr-2" />
                Save Registration Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Configure security and session settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                  <Select
                    value={sessionTimeout}
                    onValueChange={setSessionTimeout}
                  >
                    <SelectTrigger id="session-timeout">
                      <SelectValue placeholder="Select timeout" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5 minutes</SelectItem>
                      <SelectItem value="10">10 minutes</SelectItem>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">60 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Time of inactivity before automatic logout
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-login-attempts">Max Login Attempts</Label>
                  <Select
                    value={maxLoginAttempts}
                    onValueChange={setMaxLoginAttempts}
                  >
                    <SelectTrigger id="max-login-attempts">
                      <SelectValue placeholder="Select attempts" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3">3 attempts</SelectItem>
                      <SelectItem value="5">5 attempts</SelectItem>
                      <SelectItem value="10">10 attempts</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Maximum failed login attempts before account lockout
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="timeout-warning" className="font-medium">
                    Show Timeout Warning
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Display a warning before automatic logout
                  </p>
                </div>
                <Switch
                  id="timeout-warning"
                  checked={showTimeoutWarning}
                  onCheckedChange={setShowTimeoutWarning}
                />
              </div>

              {showTimeoutWarning && (
                <div className="space-y-2">
                  <Label htmlFor="timeout-warning-period">Warning Time (minutes)</Label>
                  <Select
                    value={timeoutWarningPeriod}
                    onValueChange={setTimeoutWarningPeriod}
                  >
                    <SelectTrigger id="timeout-warning-period">
                      <SelectValue placeholder="Select warning time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.5">30 seconds</SelectItem>
                      <SelectItem value="1">1 minute</SelectItem>
                      <SelectItem value="2">2 minutes</SelectItem>
                      <SelectItem value="3">3 minutes</SelectItem>
                      <SelectItem value="5">5 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Time before session timeout to show warning
                  </p>
                </div>
              )}

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="strong-passwords" className="font-medium">
                    Require Strong Passwords
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Enforce password complexity requirements
                  </p>
                </div>
                <Switch
                  id="strong-passwords"
                  checked={requireStrongPasswords}
                  onCheckedChange={setRequireStrongPasswords}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSecuritySettings} className="ml-auto">
                <Save className="h-4 w-4 mr-2" />
                Save Security Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Email Settings */}
        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Settings</CardTitle>
              <CardDescription>
                Configure email notifications and templates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email-sender">Email Sender</Label>
                  <Input
                    id="email-sender"
                    value={emailSender}
                    onChange={(e) => setEmailSender(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                  <p className="text-xs text-muted-foreground">
                    The email address used to send system emails
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email-signature">Email Signature</Label>
                  <Input
                    id="email-signature"
                    value={emailSignature}
                    onChange={(e) => setEmailSignature(e.target.value)}
                    placeholder="University of Gondar Team"
                  />
                  <p className="text-xs text-muted-foreground">
                    Signature appended to all system emails
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="welcome-email" className="font-medium">
                      Send Welcome Email
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Send welcome email to new users upon registration
                    </p>
                  </div>
                  <Switch
                    id="welcome-email"
                    checked={sendWelcomeEmail}
                    onCheckedChange={setSendWelcomeEmail}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="verification-email" className="font-medium">
                      Send Verification Email
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Send email verification to new users
                    </p>
                  </div>
                  <Switch
                    id="verification-email"
                    checked={sendVerificationEmail}
                    onCheckedChange={setSendVerificationEmail}
                  />
                </div>
              </div>

              <div className="bg-muted p-4 rounded-md">
                <h3 className="text-sm font-medium mb-2">Email Templates</h3>
                <p className="text-xs text-muted-foreground mb-4">
                  Customize email templates for different system notifications
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <Button variant="outline" size="sm" disabled>
                    <Mail className="h-4 w-4 mr-2" />
                    Welcome Email
                  </Button>
                  <Button variant="outline" size="sm" disabled>
                    <Mail className="h-4 w-4 mr-2" />
                    Verification Email
                  </Button>
                  <Button variant="outline" size="sm" disabled>
                    <Mail className="h-4 w-4 mr-2" />
                    Password Reset
                  </Button>
                  <Button variant="outline" size="sm" disabled>
                    <Mail className="h-4 w-4 mr-2" />
                    Application Confirmation
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Email template customization will be available in a future update
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveEmailSettings} className="ml-auto">
                <Save className="h-4 w-4 mr-2" />
                Save Email Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminSettings;
