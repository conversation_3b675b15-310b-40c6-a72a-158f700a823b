/**
 * Django-Compatible Permission Components
 * 
 * These components work exactly like Django's permission system:
 * - PermissionGate: Shows content only if user has required permissions
 * - LoginRequired: Redirects to login if user is not authenticated
 * - StaffRequired: Shows content only for staff users
 * - SuperuserRequired: Shows content only for superusers
 * - GroupRequired: Shows content only for users in specific groups
 */

import React, { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock, UserX, AlertTriangle } from 'lucide-react';

interface BasePermissionProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
  showMessage?: boolean;
  customMessage?: string;
}

/**
 * Django's permission_required equivalent
 * Shows content only if user has the required permissions
 */
interface PermissionGateProps extends BasePermissionProps {
  permissions: string | string[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission.
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  permissions,
  requireAll = true,
  children,
  fallback,
  redirectTo,
  showMessage = true,
  customMessage
}) => {
  const { hasPerm, hasPerms, isAuthenticated } = useAuth();

  // Check authentication first
  if (!isAuthenticated) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Lock className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || 'You must be logged in to view this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  // Check permissions
  const permArray = Array.isArray(permissions) ? permissions : [permissions];
  const hasPermission = hasPerms(permArray, requireAll);

  if (!hasPermission) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Shield className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || `You don't have permission to view this content. Required permissions: ${permArray.join(', ')}`}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  return <>{children}</>;
};

/**
 * Django's @login_required equivalent
 * Redirects to login if user is not authenticated
 */
interface LoginRequiredProps extends BasePermissionProps {
  loginUrl?: string;
}

export const LoginRequired: React.FC<LoginRequiredProps> = ({
  children,
  fallback,
  redirectTo = '/login',
  loginUrl,
  showMessage = true,
  customMessage
}) => {
  const { isAuthenticated } = useAuth();
  const redirectUrl = loginUrl || redirectTo;

  if (!isAuthenticated) {
    if (redirectUrl) {
      return <Navigate to={redirectUrl} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-blue-200 bg-blue-50">
          <Lock className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            {customMessage || 'Please log in to access this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  return <>{children}</>;
};

/**
 * Django's @staff_member_required equivalent
 * Shows content only for staff users
 */
export const StaffRequired: React.FC<BasePermissionProps> = ({
  children,
  fallback,
  redirectTo,
  showMessage = true,
  customMessage
}) => {
  const { isAuthenticated, isStaff } = useAuth();

  if (!isAuthenticated) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Lock className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || 'You must be logged in to view this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  if (!isStaff()) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <UserX className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || 'You must be a staff member to access this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  return <>{children}</>;
};

/**
 * Superuser required component
 * Shows content only for superusers
 */
export const SuperuserRequired: React.FC<BasePermissionProps> = ({
  children,
  fallback,
  redirectTo,
  showMessage = true,
  customMessage
}) => {
  const { isAuthenticated, isSuperuser } = useAuth();

  if (!isAuthenticated) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Lock className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || 'You must be logged in to view this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  if (!isSuperuser()) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || 'You must be a superuser to access this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  return <>{children}</>;
};

/**
 * Group required component
 * Shows content only for users in specific groups
 */
interface GroupRequiredProps extends BasePermissionProps {
  groups: string | string[];
  requireAll?: boolean; // If true, user must be in ALL groups. If false, user needs to be in ANY group.
}

export const GroupRequired: React.FC<GroupRequiredProps> = ({
  groups,
  requireAll = false,
  children,
  fallback,
  redirectTo,
  showMessage = true,
  customMessage
}) => {
  const { isAuthenticated, isInGroup, isInAnyGroup, getGroups } = useAuth();

  if (!isAuthenticated) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Lock className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || 'You must be logged in to view this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  // Check group membership
  const groupArray = Array.isArray(groups) ? groups : [groups];
  const userGroups = getGroups();
  
  const hasGroup = requireAll
    ? groupArray.every(group => userGroups.includes(group))
    : groupArray.some(group => userGroups.includes(group));

  if (!hasGroup) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Users className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || `You must be a member of the required group(s): ${groupArray.join(', ')}`}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  return <>{children}</>;
};

/**
 * Utility component for testing user status
 */
interface UserTestProps extends BasePermissionProps {
  test: (user: any) => boolean;
  testName?: string;
}

export const UserTest: React.FC<UserTestProps> = ({
  test,
  testName = 'custom test',
  children,
  fallback,
  redirectTo,
  showMessage = true,
  customMessage
}) => {
  const auth = useAuth();

  if (!auth.isAuthenticated) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <Lock className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || 'You must be logged in to view this content.'}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  if (!test(auth.user)) {
    if (redirectTo) {
      return <Navigate to={redirectTo} replace />;
    }
    
    if (fallback) {
      return <>{fallback}</>;
    }
    
    if (showMessage) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {customMessage || `You don't meet the requirements for ${testName}.`}
          </AlertDescription>
        </Alert>
      );
    }
    
    return null;
  }

  return <>{children}</>;
};
