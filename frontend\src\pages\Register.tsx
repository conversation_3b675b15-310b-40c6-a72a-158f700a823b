
import React, { useState, useEffect } from 'react';
import { Link, Navigate, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Mail, User, Lock, UserCheck } from 'lucide-react';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from 'sonner';
import { authAPI, settingsAPI } from '@/services/api';
import Captcha from '@/components/Captcha';

const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  first_name: z.string().min(2, 'First name must be at least 2 characters'),
  last_name: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password1: z.string().min(6, 'Password must be at least 6 characters'),
  password2: z.string().min(6, 'Password must be at least 6 characters')
}).refine(data => data.password1 === data.password2, {
  message: "Passwords don't match",
  path: ["password2"]
});

type RegisterFormValues = z.infer<typeof registerSchema>;

const Register = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCaptchaVerified, setIsCaptchaVerified] = useState(false);
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';

  const navigate = useNavigate();

  // State for organization settings
  const [settings, setSettings] = useState<{
    systemName: string;
    organizationName: string;
  }>({
    systemName: '',
    organizationName: ''
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Check if user is already authenticated
  useEffect(() => {
    const token = localStorage.getItem('token');
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    const userData = localStorage.getItem('user');
    const user = userData ? JSON.parse(userData) : null;

    if (token && isAuthenticated && user) {
      // User is already logged in, redirect to appropriate dashboard
      if (user.is_staff || user.is_superuser) {
        navigate('/graduate-admin');
      } else {
        navigate('/dashboard');
      }
    }
  }, [navigate]);

  // Fetch organization settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          setSettings({
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || ''
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      first_name: '',
      last_name: '',
      email: '',
      password1: '',
      password2: ''
    }
  });

  const onSubmit = async (data: RegisterFormValues) => {
    // Check if CAPTCHA is verified
    if (!isCaptchaVerified) {
      toast.error('Please verify the CAPTCHA before submitting');
      console.log("CAPTCHA not verified");
      return;
    }

    console.log("CAPTCHA verified, proceeding with form submission");
    setIsLoading(true);

    try {
      // Register the user with Django backend
      const response = await authAPI.register(data);

      // If registration is successful, show success message
      toast.success('Registration successful! Please log in.');

      // Redirect to login page
      navigate('/login');
    } catch (error: any) {
      // Handle registration errors
      console.error('Registration error:', error);

      if (error.response?.data) {
        // Handle specific error messages from the backend
        const errorData = error.response.data;

        if (typeof errorData === 'object') {
          // Handle field-specific errors
          Object.entries(errorData).forEach(([field, messages]) => {
            if (Array.isArray(messages) && messages.length > 0) {
              toast.error(`${field}: ${messages[0]}`);
            } else if (typeof messages === 'string') {
              toast.error(`${field}: ${messages}`);
            }
          });
        } else {
          toast.error(errorData.toString());
        }
      } else {
        toast.error('Registration failed. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <Layout>
      {/* Hero Section with Animated Patterned Background - Half Height */}
      <section className="relative py-4 md:py-6 h-[175px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(/images/university-pattern.svg)` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          <h1 className="text-xl md:text-2xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
            {isSettingsLoading ? (
              <div className="h-8 w-64 bg-white/20 animate-pulse rounded mx-auto"></div>
            ) : (
              <>{settings.organizationName} Registration</>
            )}
          </h1>
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Create your account to access our application portal and begin your academic journey with us.
          </p>
        </div>
      </section>

      <div className="container mx-auto py-8 px-4">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - How to Register */}
          <div className="xl:col-span-1">
            <Card className="border-t-4 border-[#1a73c0] shadow-lg h-full">
              <CardHeader className="bg-gray-50 rounded-t-lg">
                <div className="flex items-center space-x-3">
                  <div className="bg-[#1a73c0] p-2 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <CardTitle className="text-[#1a73c0]">How to Register</CardTitle>
                    <CardDescription>
                      Follow these steps to create your account
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-4 px-4">
                <div className="flow-chart-container space-y-6 relative">
                  {/* Connecting line */}
                  <div className="absolute left-[18px] top-[30px] bottom-[30px] w-0.5 bg-gradient-to-b from-[#1a73c0] to-[#1a73c0]/30 z-0"></div>

                  {/* Step 1 */}
                  <div className="flow-step relative z-10">
                    <div className="flow-box bg-white hover:bg-blue-50 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 p-4 rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <div className="bg-[#1a73c0] text-white rounded-full w-9 h-9 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800">Create Your Account</h3>
                          <p className="text-sm text-gray-600">Fill in your personal details to register</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Step 2 */}
                  <div className="flow-step relative z-10">
                    <div className="flow-box bg-white hover:bg-blue-50 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 p-4 rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <div className="bg-[#1a73c0] text-white rounded-full w-9 h-9 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800">Verify Your Email</h3>
                          <p className="text-sm text-gray-600">Check your inbox for a verification link</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Step 3 */}
                  <div className="flow-step relative z-10">
                    <div className="flow-box bg-white hover:bg-blue-50 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 p-4 rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <div className="bg-[#1a73c0] text-white rounded-full w-9 h-9 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800">Complete Your Profile</h3>
                          <p className="text-sm text-gray-600">Add additional information to your profile</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Step 4 */}
                  <div className="flow-step relative z-10">
                    <div className="flow-box bg-white hover:bg-blue-50 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 p-4 rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <div className="bg-[#1a73c0] text-white rounded-full w-9 h-9 flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-800">Apply for Programs</h3>
                          <p className="text-sm text-gray-600">Start applying to your desired programs</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 text-sm bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg border-l-4 border-[#1a73c0] shadow-md relative overflow-hidden">
                  <div className="absolute right-0 top-0 h-full w-32 bg-contain bg-right-top bg-no-repeat opacity-5" style={{ backgroundImage: 'url(/images/university-pattern.svg)' }}></div>

                  <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Important Information
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex items-start space-x-2 shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                      <div className="bg-blue-100 p-1.5 rounded-full flex-shrink-0">
                        <svg className="h-3.5 w-3.5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <span className="text-gray-700 text-xs">Use a valid email address that you check regularly</span>
                    </div>

                    <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex items-start space-x-2 shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                      <div className="bg-blue-100 p-1.5 rounded-full flex-shrink-0">
                        <svg className="h-3.5 w-3.5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                      <span className="text-gray-700 text-xs">Password should be at least 6 characters long</span>
                    </div>

                    <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex items-start space-x-2 shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                      <div className="bg-blue-100 p-1.5 rounded-full flex-shrink-0">
                        <svg className="h-3.5 w-3.5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      </div>
                      <span className="text-gray-700 text-xs">Keep your login credentials secure</span>
                    </div>

                    <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex items-start space-x-2 shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                      <div className="bg-blue-100 p-1.5 rounded-full flex-shrink-0">
                        <svg className="h-3.5 w-3.5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span className="text-gray-700 text-xs">
                        {isSettingsLoading ? (
                          <span className="inline-block h-4 w-40 bg-gray-200 animate-pulse rounded"></span>
                        ) : (
                          <>For assistance, contact support@{settings.organizationName.toLowerCase().replace(/\s+/g, '')}.edu.et</>
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Registration Form */}
          <div className="xl:col-span-2">
            <Card className="border-t-4 border-[#1a73c0] shadow-lg">
              <CardHeader className="bg-gray-50 rounded-t-lg">
                <div className="flex items-center space-x-3">
                  <div className="bg-[#1a73c0] p-2 rounded-full">
                    <UserCheck className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle>Create an Account</CardTitle>
                    <CardDescription>
                      Fill in your details to create an application account
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="px-8 py-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700 flex items-center">
                        <span>Username</span> <span className="text-red-500 ml-1">*</span>
                        <div className="ml-1 group relative">
                          <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                            Choose a unique username for your account
                          </div>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <UserCheck className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                          <Input
                            placeholder="Enter a unique username"
                            className="pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all hover:border-blue-300"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-xs mt-1 flex items-center">
                        {form.formState.errors.username && (
                          <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        )}
                      </FormMessage>
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="first_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700 flex items-center">
                          <span>First Name</span> <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <User className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                            <Input
                              placeholder="First Name"
                              className="pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all hover:border-blue-300"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500 text-xs mt-1 flex items-center">
                          {form.formState.errors.first_name && (
                            <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                          )}
                        </FormMessage>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="last_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700 flex items-center">
                          <span>Last Name</span> <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <User className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                            <Input
                              placeholder="Last Name"
                              className="pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all hover:border-blue-300"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500 text-xs mt-1 flex items-center">
                          {form.formState.errors.last_name && (
                            <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                          )}
                        </FormMessage>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700 flex items-center">
                        <span>Email</span> <span className="text-red-500 ml-1">*</span>
                        <div className="ml-1 group relative">
                          <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                            We'll send a verification link to this email
                          </div>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                          <Input
                            placeholder="<EMAIL>"
                            className="pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all hover:border-blue-300"
                            type="email"
                            autoComplete="email"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-xs mt-1 flex items-center">
                        {form.formState.errors.email && (
                          <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        )}
                      </FormMessage>
                    </FormItem>
                  )}
                />



                <div className="space-y-4 pt-2">

                  <FormField
                    control={form.control}
                    name="password1"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium text-gray-700">Password</FormLabel>
                        <FormControl>
                          <div className="relative group">
                            <div className="absolute left-0 inset-y-0 w-10 flex items-center justify-center text-gray-400 group-focus-within:text-[#1a73c0] transition-colors">
                              <Lock className="h-5 w-5" />
                            </div>
                            <Input
                              type={showPassword ? 'text' : 'password'}
                              placeholder="••••••••"
                              className="pl-10 pr-10 border-gray-300 focus:border-[#1a73c0] focus:ring focus:ring-[#1a73c0]/20 transition-all rounded-md"
                              autoComplete="new-password"
                              {...field}
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
                              tabIndex={-1}
                            >
                              {showPassword ? (
                                <EyeOff className="h-5 w-5" />
                              ) : (
                                <Eye className="h-5 w-5" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500 text-xs font-medium" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="password2"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium text-gray-700">Confirm Password</FormLabel>
                        <FormControl>
                          <div className="relative group">
                            <div className="absolute left-0 inset-y-0 w-10 flex items-center justify-center text-gray-400 group-focus-within:text-[#1a73c0] transition-colors">
                              <Lock className="h-5 w-5" />
                            </div>
                            <Input
                              type={showConfirmPassword ? 'text' : 'password'}
                              placeholder="••••••••"
                              className="pl-10 pr-10 border-gray-300 focus:border-[#1a73c0] focus:ring focus:ring-[#1a73c0]/20 transition-all rounded-md"
                              autoComplete="new-password"
                              {...field}
                            />
                            <button
                              type="button"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
                              tabIndex={-1}
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-5 w-5" />
                              ) : (
                                <Eye className="h-5 w-5" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-500 text-xs font-medium" />
                      </FormItem>
                    )}
                  />
                </div>

                {/* CAPTCHA Component */}
                <div className="pt-4 pb-2">
                  <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-medium text-gray-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                        Security Verification
                      </h3>
                      <div className="flex items-center">
                        {isCaptchaVerified ? (
                          <span className="text-xs text-green-500 flex items-center">
                            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Verified
                          </span>
                        ) : (
                          <span className="text-xs text-gray-500">Verification required</span>
                        )}
                      </div>
                    </div>
                    <Captcha
                      onVerify={(isVerified) => {
                        console.log("CAPTCHA verification status:", isVerified);
                        setIsCaptchaVerified(isVerified);
                      }}
                    />
                  </div>
                </div>

                <div className="pt-2">
                  <Button
                    type="submit"
                    className="w-full bg-[#1a73c0] hover:bg-[#145da1] text-white font-medium py-2 h-11 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 active:translate-y-0"
                    disabled={isLoading || !isCaptchaVerified}
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating your account...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        {isCaptchaVerified ? (
                          <>
                            <svg className="h-4 w-4 mr-2 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Create Account
                          </>
                        ) : (
                          <>
                            <svg className="h-4 w-4 mr-2 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                            Complete Security Verification
                          </>
                        )}
                      </div>
                    )}
                  </Button>

                  <div className="relative flex items-center justify-center mt-6 mb-2">
                    <div className="absolute border-t border-gray-200 w-full"></div>
                    <div className="relative bg-white px-4 text-sm text-gray-500">Or</div>
                  </div>

                  <p className="text-center text-sm text-gray-600 mb-2">
                    Already have an account?{' '}
                    <Link to="/login" className="text-[#1a73c0] hover:text-[#145da1] font-medium transition-colors">
                      Sign in here
                    </Link>
                  </p>
                </div>
              </form>
            </Form>
          </CardContent>
          <div className="bg-gray-50 px-8 py-4 border-t border-gray-100 text-center">
            <p className="text-xs text-gray-500 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Protected by industry standard security protocols
            </p>
          </div>
            </Card>
          </div>
        </div>
      </div>

      {/* BackToTop button is already included in the Layout component */}
    </Layout>
  );
};

export default Register;
