# 🧪 Authentication System Testing Guide

## ✅ Quick Test Checklist

### 1. **Frontend Application Access**
- ✅ **Frontend URL**: http://localhost:8081
- ✅ **Backend URL**: http://localhost:8000
- ✅ **Both servers running**: Django + React development servers

### 2. **Basic Authentication Flow Test**

#### **Step 1: Test Login Page**
1. Navigate to http://localhost:8081/login
2. Check if the login form loads without errors
3. Verify no console errors related to imports

#### **Step 2: Test Authentication API**
```bash
# Test Django JWT endpoint
curl -X POST http://localhost:8000/api/token/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

#### **Step 3: Test Enhanced Auth Endpoints**
```bash
# Test token validation
curl -X POST http://localhost:8000/api/user/auth/token/validate/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Test user permissions
curl -X GET http://localhost:8000/api/user/auth/permissions/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Test current user endpoint
curl -X GET http://localhost:8000/api/user/me/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. **Frontend Component Testing**

#### **Test Permission Components**
Create a test page to verify permission-aware components:

```jsx
import { HasPermission, AdminOnly, ProtectedRoute } from '@/components/PermissionComponents';

function TestPage() {
  return (
    <div>
      <HasPermission permission="auth.add_user">
        <button>Add User (Permission Required)</button>
      </HasPermission>
      
      <AdminOnly>
        <div>Admin Only Content</div>
      </AdminOnly>
    </div>
  );
}
```

#### **Test Protected Routes**
```jsx
import ProtectedRoute from '@/components/ProtectedRoute';

// In your router
<ProtectedRoute requireAdmin>
  <AdminDashboard />
</ProtectedRoute>

<ProtectedRoute requiredRoles={['Staff']} requireStaff>
  <StaffPanel />
</ProtectedRoute>
```

### 4. **Token Management Testing**

#### **Test Auto-Refresh**
1. Login to get tokens
2. Wait for token to near expiration (or manually set short expiration)
3. Make API calls and verify automatic token refresh
4. Check browser console for refresh logs

#### **Test Session Restoration**
1. Login successfully
2. Refresh the browser page
3. Verify user session is restored
4. Check that user data and authentication state persist

### 5. **Security Testing**

#### **Test Permission Validation**
1. Login as a regular user
2. Try to access admin-only routes
3. Verify proper access denial and redirects
4. Check that UI components hide appropriately

#### **Test Token Expiration**
1. Login successfully
2. Manually expire tokens (or wait for natural expiration)
3. Verify automatic logout and redirect to login
4. Check that all authentication data is cleared

### 6. **Error Handling Testing**

#### **Test Network Errors**
1. Disconnect from internet
2. Try to login or make authenticated requests
3. Verify proper error messages and fallback behavior

#### **Test Invalid Credentials**
1. Try login with wrong username/password
2. Verify proper error messages
3. Check rate limiting behavior

### 7. **Browser Console Checks**

#### **Expected Console Messages**
- ✅ "Session restored successfully" (on page refresh)
- ✅ "Token refreshed successfully" (during auto-refresh)
- ✅ "Logout completed successfully" (on logout)

#### **No Error Messages Should Appear**
- ❌ Import errors for tokenManager.ts or authAPI.ts
- ❌ TypeScript compilation errors
- ❌ React component errors

## 🔧 Troubleshooting Common Issues

### **Import Errors**
If you see import errors:
```bash
# Restart the development server
npm run dev
```

### **TypeScript Errors**
```bash
# Check for TypeScript errors
npx tsc --noEmit
```

### **Authentication Failures**
1. Check Django server is running on port 8000
2. Verify CORS settings allow frontend origin
3. Check JWT settings in Django settings.py

### **Permission Issues**
1. Verify user has proper roles/permissions in Django admin
2. Check permission strings match between frontend and backend
3. Verify user data includes permissions in API response

## 🎯 Success Criteria

### **✅ Authentication Working If:**
1. Login form loads without errors
2. Successful login redirects to appropriate dashboard
3. Token refresh happens automatically
4. Session restoration works on page refresh
5. Logout clears all authentication data
6. Protected routes properly restrict access
7. Permission-aware components show/hide correctly

### **✅ Security Working If:**
1. Unauthorized users cannot access protected content
2. Tokens are properly validated on backend
3. Expired tokens trigger automatic logout
4. Permission checks work on both frontend and backend

## 📝 Test Results Template

```
Date: ___________
Tester: ___________

[ ] Frontend loads without import errors
[ ] Login form works correctly
[ ] Token refresh works automatically
[ ] Session restoration works
[ ] Protected routes work
[ ] Permission components work
[ ] Logout works properly
[ ] Security validation works

Issues Found:
_________________________________
_________________________________

Notes:
_________________________________
_________________________________
```

## 🚀 Next Steps After Testing

1. **If tests pass**: System is ready for production deployment
2. **If tests fail**: Check the troubleshooting section and fix issues
3. **Performance testing**: Test with multiple users and concurrent sessions
4. **Security audit**: Run security scans and penetration tests

The authentication system is comprehensive and should handle all common use cases securely!
