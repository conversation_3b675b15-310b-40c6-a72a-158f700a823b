from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission, User
from django.contrib.contenttypes.models import ContentType
from user_management.models import Role, PermissionCategory


class Command(BaseCommand):
    help = 'Set up default RBAC roles and permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all roles and permissions before creating new ones',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting existing roles and permissions...')
            Role.objects.all().delete()
            Group.objects.all().delete()
            PermissionCategory.objects.all().delete()

        self.stdout.write('Setting up RBAC system...')

        # Create permission categories
        categories = [
            {'name': 'User Management', 'description': 'Permissions related to user management'},
            {'name': 'Academic Management', 'description': 'Permissions related to academic data'},
            {'name': 'System Administration', 'description': 'System-level permissions'},
            {'name': 'Content Management', 'description': 'Content and communication permissions'},
            {'name': 'Registration Management', 'description': 'Student registration and application permissions'},
        ]

        for cat_data in categories:
            category, created = PermissionCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'Created permission category: {category.name}')

        # Define roles and their permissions
        roles_config = {
            'Super Admin': {
                'description': 'Highest level access with full system control',
                'permissions': [
                    # User management
                    'auth.add_user', 'auth.change_user', 'auth.delete_user', 'auth.view_user',
                    'auth.add_group', 'auth.change_group', 'auth.delete_group', 'auth.view_group',
                    'auth.add_permission', 'auth.change_permission', 'auth.delete_permission', 'auth.view_permission',

                    # Registration management
                    'registration.add_applicantprogramselection', 'registration.change_applicantprogramselection',
                    'registration.delete_applicantprogramselection', 'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.add_official', 'official.change_official', 'official.delete_official', 'official.view_official',

                    # Setup models
                    'college.add_college', 'college.change_college', 'college.delete_college', 'college.view_college',
                    'department.add_department', 'department.change_department', 'department.delete_department', 'department.view_department',
                    'program.add_program', 'program.change_program', 'program.delete_program', 'program.view_program',
                ]
            },
            'Administrator': {
                'description': 'High-level administrative access with most system features',
                'permissions': [
                    # User management (limited)
                    'auth.add_user', 'auth.change_user', 'auth.view_user',
                    'auth.view_group', 'auth.view_permission',

                    # Registration management
                    'registration.add_applicantprogramselection', 'registration.change_applicantprogramselection',
                    'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.add_official', 'official.change_official', 'official.view_official',

                    # Setup models
                    'college.add_college', 'college.change_college', 'college.view_college',
                    'department.add_department', 'department.change_department', 'department.view_department',
                    'program.add_program', 'program.change_program', 'program.view_program',
                ]
            },
            'Main Registrar': {
                'description': 'Senior registrar with oversight responsibilities and full registration access',
                'permissions': [
                    # Limited user management
                    'auth.view_user', 'auth.view_group',

                    # Registration management
                    'registration.change_applicantprogramselection', 'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.change_official', 'official.view_official',

                    # Setup models (view and change)
                    'college.change_college', 'college.view_college',
                    'department.change_department', 'department.view_department',
                    'program.change_program', 'program.view_program',
                ]
            },
            'Registrar Officer': {
                'description': 'Standard registrar with operational duties and limited approval authority',
                'permissions': [
                    # Basic user viewing
                    'auth.view_user',

                    # Registration management
                    'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.view_official',

                    # Setup models (view only)
                    'college.view_college',
                    'department.view_department',
                    'program.view_program',
                ]
            },
            'Department Head': {
                'description': 'Academic department leadership with department-specific access and management',
                'permissions': [
                    # View only permissions
                    'auth.view_user',
                    'registration.view_applicantprogramselection',
                    'official.view_official',
                    'college.view_college',
                    'department.view_department',
                    'program.view_program',
                ]
            },
            'Applicant': {
                'description': 'End users applying to the university with access to their own application data',
                'permissions': [
                    # Basic permissions for applicants
                    'registration.view_applicantprogramselection',  # Only their own data
                ]
            },
        }

        # Create roles and assign permissions
        for role_name, role_config in roles_config.items():
            # Create or get the group
            group, created = Group.objects.get_or_create(name=role_name)
            if created:
                self.stdout.write(f'Created group: {role_name}')

            # Create or get the role
            role, created = Role.objects.get_or_create(
                group=group,
                defaults={
                    'description': role_config['description'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created role: {role_name}')

            # Assign permissions
            permissions_to_add = []
            for perm_codename in role_config['permissions']:
                try:
                    app_label, codename = perm_codename.split('.')
                    permission = Permission.objects.get(
                        codename=codename,
                        content_type__app_label=app_label
                    )
                    permissions_to_add.append(permission)
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {perm_codename}')
                    )
                except ValueError:
                    self.stdout.write(
                        self.style.ERROR(f'Invalid permission format: {perm_codename}')
                    )

            group.permissions.set(permissions_to_add)
            self.stdout.write(f'Assigned {len(permissions_to_add)} permissions to {role_name}')

        # Create a default super admin user if it doesn't exist
        if not User.objects.filter(is_superuser=True).exists():
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='System',
                last_name='Administrator'
            )
            
            # Add to Super Admin role
            super_admin_group = Group.objects.get(name='Super Admin')
            admin_user.groups.add(super_admin_group)
            
            self.stdout.write(
                self.style.SUCCESS(
                    'Created default super admin user (username: admin, password: admin123)'
                )
            )

        self.stdout.write(
            self.style.SUCCESS('RBAC setup completed successfully!')
        )
        
        # Display summary
        self.stdout.write('\nSummary:')
        self.stdout.write(f'- Permission Categories: {PermissionCategory.objects.count()}')
        self.stdout.write(f'- Roles: {Role.objects.count()}')
        self.stdout.write(f'- Groups: {Group.objects.count()}')
        self.stdout.write(f'- Total Users: {User.objects.count()}')
        self.stdout.write(f'- Admin Users: {User.objects.filter(is_superuser=True).count()}')
