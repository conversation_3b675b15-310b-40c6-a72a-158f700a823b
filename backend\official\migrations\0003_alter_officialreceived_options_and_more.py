# Generated by Django 5.2.1 on 2025-06-01 22:07

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('certificate_type', '0001_initial'),
        ('official', '0002_alter_officialreceived_second_name_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='officialreceived',
            options={'ordering': ['-arival_date', '-created_at'], 'verbose_name': 'Official Received Certificate', 'verbose_name_plural': 'Official Received Certificates'},
        ),
        migrations.AlterModelOptions(
            name='officialsent',
            options={'ordering': ['-send_date', '-created_at'], 'verbose_name': 'Official Sent Certificate', 'verbose_name_plural': 'Official Sent Certificates'},
        ),
        migrations.AddField(
            model_name='officialreceived',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='officialreceived',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='officialsent',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='officialsent',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='arival_date',
            field=models.DateField(help_text='Date when the certificate was received'),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='certificate_type',
            field=models.ForeignKey(help_text='Type of certificate received', on_delete=django.db.models.deletion.PROTECT, related_name='received_certificates', to='certificate_type.certificatetype'),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='courier',
            field=models.CharField(help_text='Courier service used for delivery', max_length=100),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='first_name',
            field=models.CharField(help_text="Recipient's first name", max_length=100),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='gender',
            field=models.CharField(choices=[('M', 'Male'), ('F', 'Female')], help_text="Recipient's gender", max_length=1),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='last_name',
            field=models.CharField(help_text="Recipient's last name", max_length=100),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='second_name',
            field=models.CharField(help_text="Recipient's second/middle name", max_length=100),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='sender_institute',
            field=models.CharField(help_text='Institution that sent the certificate', max_length=255),
        ),
        migrations.AlterField(
            model_name='officialreceived',
            name='tracking_number',
            field=models.CharField(help_text='Unique tracking number for this shipment', max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='certificate_type',
            field=models.ForeignKey(help_text='Type of certificate being sent', on_delete=django.db.models.deletion.PROTECT, related_name='sent_certificates', to='certificate_type.certificatetype'),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='courier',
            field=models.CharField(help_text='Courier service used for delivery', max_length=100),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='first_name',
            field=models.CharField(help_text="Recipient's first name", max_length=100),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='gender',
            field=models.CharField(choices=[('M', 'Male'), ('F', 'Female')], help_text="Recipient's gender", max_length=1),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='last_name',
            field=models.CharField(help_text="Recipient's last name", max_length=100),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='receiver_institute',
            field=models.CharField(help_text='Institution receiving the certificate', max_length=255),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='second_name',
            field=models.CharField(help_text="Recipient's second/middle name", max_length=100),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='send_date',
            field=models.DateField(help_text='Date when the certificate was sent'),
        ),
        migrations.AlterField(
            model_name='officialsent',
            name='tracking_number',
            field=models.CharField(help_text='Unique tracking number for this shipment', max_length=100, unique=True),
        ),
        migrations.AddIndex(
            model_name='officialreceived',
            index=models.Index(fields=['arival_date'], name='official_of_arival__5ca752_idx'),
        ),
        migrations.AddIndex(
            model_name='officialreceived',
            index=models.Index(fields=['tracking_number'], name='official_of_trackin_810dd4_idx'),
        ),
        migrations.AddIndex(
            model_name='officialreceived',
            index=models.Index(fields=['certificate_type'], name='official_of_certifi_a7365d_idx'),
        ),
        migrations.AddIndex(
            model_name='officialsent',
            index=models.Index(fields=['send_date'], name='official_of_send_da_c172cd_idx'),
        ),
        migrations.AddIndex(
            model_name='officialsent',
            index=models.Index(fields=['tracking_number'], name='official_of_trackin_cc362d_idx'),
        ),
        migrations.AddIndex(
            model_name='officialsent',
            index=models.Index(fields=['certificate_type'], name='official_of_certifi_ed9af3_idx'),
        ),
    ]
