#!/usr/bin/env python
"""
Simple script to apply the audit trail migration
"""
import os
import sys
import django
from django.db import connection, transaction

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

def run_migration():
    """Apply the audit trail migration"""
    print("=== Applying Audit Trail Migration ===")
    
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # Check if columns already exist
                print("Checking if audit trail columns exist...")
                cursor.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'GraduateVerification_graduatestudent' 
                    AND column_name IN ('created_by_id', 'updated_by_id');
                """)
                existing_columns = [row[0] for row in cursor.fetchall()]
                
                if 'created_by_id' in existing_columns and 'updated_by_id' in existing_columns:
                    print("✓ Audit trail columns already exist!")
                    return True
                
                print("Adding audit trail columns...")
                
                # Add created_by column
                if 'created_by_id' not in existing_columns:
                    print("  Adding created_by_id column...")
                    cursor.execute("""
                        ALTER TABLE "GraduateVerification_graduatestudent" 
                        ADD COLUMN "created_by_id" integer NULL
                        REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
                    """)
                
                # Add updated_by column  
                if 'updated_by_id' not in existing_columns:
                    print("  Adding updated_by_id column...")
                    cursor.execute("""
                        ALTER TABLE "GraduateVerification_graduatestudent" 
                        ADD COLUMN "updated_by_id" integer NULL
                        REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
                    """)
                
                # Create indexes for better performance
                print("  Creating indexes...")
                try:
                    cursor.execute("""
                        CREATE INDEX "GraduateVerification_graduatestudent_created_by_id_idx" 
                        ON "GraduateVerification_graduatestudent" ("created_by_id");
                    """)
                except Exception as e:
                    print(f"    Created_by index may already exist: {e}")
                
                try:
                    cursor.execute("""
                        CREATE INDEX "GraduateVerification_graduatestudent_updated_by_id_idx" 
                        ON "GraduateVerification_graduatestudent" ("updated_by_id");
                    """)
                except Exception as e:
                    print(f"    Updated_by index may already exist: {e}")
                
                # Update migration table
                print("  Updating migration table...")
                try:
                    cursor.execute("""
                        INSERT INTO "django_migrations" ("app", "name", "applied") 
                        VALUES ('GraduateVerification', '0003_add_audit_trail_fields', NOW());
                    """)
                except Exception as e:
                    print(f"    Migration record may already exist: {e}")
                
                print("✅ Audit trail migration applied successfully!")
                return True
                
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure your database is running")
        print("2. Check database permissions")
        print("3. Verify table names match your database")
        print("4. Try running: python manage.py migrate")
        return False

def test_audit_fields():
    """Test if audit trail fields are working"""
    print("\n=== Testing Audit Trail Fields ===")
    
    try:
        from GraduateVerification.models import GraduateStudent
        
        # Check if model has the fields
        model_fields = [field.name for field in GraduateStudent._meta.fields]
        audit_fields = ['created_by', 'updated_by']
        
        print("Checking model fields:")
        for field in audit_fields:
            if field in model_fields:
                print(f"  ✓ {field}")
            else:
                print(f"  ✗ {field} - MISSING")
        
        # Try to query with audit fields
        try:
            count = GraduateStudent.objects.select_related('created_by', 'updated_by').count()
            print(f"✓ Database query with audit fields successful: {count} records")
        except Exception as e:
            print(f"✗ Database query failed: {e}")
            return False
        
        print("✅ Audit trail fields are working!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == '__main__':
    print("Starting audit trail migration process...")
    
    # Apply migration
    migration_success = run_migration()
    
    if migration_success:
        # Test the fields
        test_success = test_audit_fields()
        
        if test_success:
            print("\n🎉 Migration completed successfully!")
            print("\n💡 Next steps:")
            print("1. Restart your Django server")
            print("2. Test the Recent Graduates Dashboard")
            print("3. Verify audit trail information appears in the frontend")
        else:
            print("\n⚠️ Migration applied but tests failed")
            print("You may need to restart Django server")
    else:
        print("\n❌ Migration failed")
        print("Please check the error messages above")
