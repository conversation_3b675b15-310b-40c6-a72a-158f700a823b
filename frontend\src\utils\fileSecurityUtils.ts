/**
 * Comprehensive file security utilities for frontend validation with performance optimizations
 */

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    name: string;
    size: number;
    type: string;
    hash?: string;
    sanitizedName: string;
    securityScore: number; // 0-100 security score
  };
  performance: {
    validationTime: number;
    fileReadTime: number;
  };
}

export interface SecurityConfig {
  maxFileSize: number;
  allowedExtensions: string[];
  allowedMimeTypes: string[];
  maxUploadsPerMinute: number;
  enableSignatureValidation: boolean;
}

export class FileSecurityValidator {
  private config: SecurityConfig;
  private uploadHistory: { timestamp: number; size: number }[] = [];

  constructor(config?: Partial<SecurityConfig>) {
    this.config = {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],
      allowedMimeTypes: [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ],
      maxUploadsPerMinute: 10,
      enableSignatureValidation: true,
      ...config
    };
  }

  /**
   * Comprehensive file validation with security checks and performance monitoring
   */
  async validateFile(file: File): Promise<FileValidationResult> {
    const startTime = performance.now();
    let fileReadTime = 0;

    const result: FileValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      fileInfo: {
        name: file.name,
        size: file.size,
        type: file.type,
        sanitizedName: this.sanitizeFilename(file.name),
        securityScore: 0
      },
      performance: {
        validationTime: 0,
        fileReadTime: 0
      }
    };

    try {
      // 1. Basic file validation
      this.validateBasicFile(file, result);

      // 2. File size validation
      this.validateFileSize(file, result);

      // 3. File name security validation
      this.validateFilename(file.name, result);

      // 4. File extension validation
      this.validateFileExtension(file.name, result);

      // 5. MIME type validation
      this.validateMimeType(file, result);

      // 6. Rate limiting validation
      this.validateRateLimit(file, result);

      // 7. File signature validation (if enabled)
      if (this.config.enableSignatureValidation) {
        await this.validateFileSignature(file, result);
      }

      // 8. Generate file hash for integrity
      const hashStartTime = performance.now();
      result.fileInfo.hash = await this.generateFileHash(file);
      fileReadTime += performance.now() - hashStartTime;

      // 9. Calculate security score
      result.fileInfo.securityScore = this.calculateSecurityScore(result);

      // Set overall validation result
      result.isValid = result.errors.length === 0;

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Record performance metrics
    const endTime = performance.now();
    result.performance.validationTime = endTime - startTime;
    result.performance.fileReadTime = fileReadTime;

    return result;
  }

  /**
   * Basic file validation
   */
  private validateBasicFile(file: File, result: FileValidationResult): void {
    if (!file) {
      result.errors.push('No file provided');
      return;
    }

    if (!file.name) {
      result.errors.push('File name is missing');
      return;
    }

    if (file.size === 0) {
      result.errors.push('File is empty or corrupted');
      return;
    }
  }

  /**
   * File size validation
   */
  private validateFileSize(file: File, result: FileValidationResult): void {
    if (file.size > this.config.maxFileSize) {
      result.errors.push(
        `File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(this.config.maxFileSize)})`
      );
    }

    if (file.size < 1) {
      result.errors.push('File is empty');
    }
  }

  /**
   * Filename security validation
   */
  private validateFilename(filename: string, result: FileValidationResult): void {
    // Check for directory traversal attempts
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      result.errors.push('Filename contains invalid characters (directory traversal attempt)');
    }

    // Check for null bytes
    if (filename.includes('\x00')) {
      result.errors.push('Filename contains null bytes');
    }

    // Check for excessively long filename
    if (filename.length > 255) {
      result.errors.push('Filename is too long (max 255 characters)');
    }

    // Check for dangerous characters
    const dangerousChars = ['<', '>', ':', '"', '|', '?', '*'];
    if (dangerousChars.some(char => filename.includes(char))) {
      result.warnings.push('Filename contains potentially dangerous characters');
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|scr|pif|com)$/i,
      /^(con|prn|aux|nul|com[1-9]|lpt[1-9])$/i,
      /\.(js|vbs|ps1|sh)$/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(filename))) {
      result.errors.push('Filename has suspicious pattern (possible executable or script)');
    }
  }

  /**
   * File extension validation
   */
  private validateFileExtension(filename: string, result: FileValidationResult): void {
    const fileExtension = '.' + filename.split('.').pop()?.toLowerCase();
    
    if (!this.config.allowedExtensions.includes(fileExtension)) {
      result.errors.push(
        `File extension '${fileExtension}' is not allowed. Allowed types: ${this.config.allowedExtensions.join(', ')}`
      );
    }
  }

  /**
   * MIME type validation
   */
  private validateMimeType(file: File, result: FileValidationResult): void {
    if (!this.config.allowedMimeTypes.includes(file.type)) {
      result.errors.push(
        `MIME type '${file.type}' is not allowed. Allowed types: ${this.config.allowedMimeTypes.join(', ')}`
      );
    }
  }

  /**
   * Rate limiting validation
   */
  private validateRateLimit(file: File, result: FileValidationResult): void {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Clean old entries
    this.uploadHistory = this.uploadHistory.filter(entry => entry.timestamp > oneMinuteAgo);

    // Check upload count
    if (this.uploadHistory.length >= this.config.maxUploadsPerMinute) {
      result.errors.push(`Too many uploads per minute (max ${this.config.maxUploadsPerMinute})`);
      return;
    }

    // Add current upload to history
    this.uploadHistory.push({ timestamp: now, size: file.size });
  }

  /**
   * File signature validation (magic number check)
   */
  private async validateFileSignature(file: File, result: FileValidationResult): Promise<void> {
    try {
      const buffer = await file.slice(0, 8).arrayBuffer();
      const bytes = new Uint8Array(buffer);
      const signature = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');

      const validSignatures: Record<string, string[]> = {
        'pdf': ['25504446'], // %PDF
        'jpg': ['ffd8ffe0', 'ffd8ffe1', 'ffd8ffe2', 'ffd8ffe3', 'ffd8ffe8'], // JPEG
        'jpeg': ['ffd8ffe0', 'ffd8ffe1', 'ffd8ffe2', 'ffd8ffe3', 'ffd8ffe8'], // JPEG
        'png': ['89504e47'], // PNG
        'doc': ['d0cf11e0'], // MS Office
        'docx': ['504b0304'] // ZIP-based (DOCX)
      };

      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (fileExtension && validSignatures[fileExtension]) {
        const isValidSignature = validSignatures[fileExtension].some(sig => 
          signature.startsWith(sig)
        );
        
        if (!isValidSignature) {
          result.errors.push(
            'File content does not match the file extension (possible file type spoofing)'
          );
        }
      }
    } catch (error) {
      result.warnings.push('Unable to verify file signature');
    }
  }

  /**
   * Generate SHA-256 hash of file content
   */
  private async generateFileHash(file: File): Promise<string> {
    try {
      const buffer = await file.arrayBuffer();
      const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.error('Error generating file hash:', error);
      return '';
    }
  }

  /**
   * Sanitize filename for safe storage
   */
  private sanitizeFilename(filename: string): string {
    // Remove path components
    const baseName = filename.split(/[/\\]/).pop() || filename;
    
    // Replace dangerous characters with underscores
    const sanitized = baseName.replace(/[^a-zA-Z0-9._-]/g, '_');
    
    // Limit length
    if (sanitized.length > 100) {
      const parts = sanitized.split('.');
      const extension = parts.pop() || '';
      const name = parts.join('.');
      return name.substring(0, 95 - extension.length) + '.' + extension;
    }
    
    return sanitized;
  }

  /**
   * Calculate security score based on validation results
   */
  private calculateSecurityScore(result: FileValidationResult): number {
    let score = 100;

    // Deduct points for errors (major security issues)
    score -= result.errors.length * 25;

    // Deduct points for warnings (minor security issues)
    score -= result.warnings.length * 10;

    // Bonus points for passing specific checks
    if (result.fileInfo.hash) score += 5; // File integrity check
    if (result.fileInfo.sanitizedName !== result.fileInfo.name) score += 5; // Filename sanitization

    // File size considerations
    const sizeRatio = result.fileInfo.size / this.config.maxFileSize;
    if (sizeRatio < 0.1) score += 5; // Small files are generally safer
    if (sizeRatio > 0.8) score -= 5; // Large files are riskier

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

/**
 * Rate limiting utility for tracking upload attempts
 */
export class UploadRateLimiter {
  private attempts: number[] = [];
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 10, windowMs: number = 60000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  canUpload(): boolean {
    const now = Date.now();
    
    // Remove old attempts
    this.attempts = this.attempts.filter(timestamp => now - timestamp < this.windowMs);
    
    return this.attempts.length < this.maxAttempts;
  }

  recordAttempt(): void {
    this.attempts.push(Date.now());
  }

  getRemainingAttempts(): number {
    const now = Date.now();
    this.attempts = this.attempts.filter(timestamp => now - timestamp < this.windowMs);
    return Math.max(0, this.maxAttempts - this.attempts.length);
  }

  getTimeUntilReset(): number {
    if (this.attempts.length === 0) return 0;
    const oldestAttempt = Math.min(...this.attempts);
    return Math.max(0, this.windowMs - (Date.now() - oldestAttempt));
  }
}

/**
 * Security headers utility for API requests
 */
export const addSecurityHeaders = (headers: Record<string, string> = {}): Record<string, string> => {
  return {
    ...headers,
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  };
};

/**
 * Default security configuration
 */
export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],
  allowedMimeTypes: [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  maxUploadsPerMinute: 10,
  enableSignatureValidation: true
};
