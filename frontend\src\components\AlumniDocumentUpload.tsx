import React, { useState, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  FileText, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { alumniApplicationsAPI, AlumniApplication, AlumniApplicationMini } from '@/services/alumniApplicationsAPI';

interface AlumniDocumentUploadProps {
  application: AlumniApplication | AlumniApplicationMini;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const AlumniDocumentUpload: React.FC<AlumniDocumentUploadProps> = ({
  application,
  onSuccess,
  onCancel
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: (formData: FormData) => alumniApplicationsAPI.uploadDocument(formData),
    onSuccess: () => {
      toast.success('Document uploaded successfully');
      setSelectedFile(null);
      setDocumentType('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      queryClient.invalidateQueries({ queryKey: ['alumni-applications-form1'] });
      queryClient.invalidateQueries({ queryKey: ['alumni-applications-form2'] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to upload document');
    }
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleUpload = () => {
    if (!selectedFile || !documentType) {
      toast.error('Please select a file and document type');
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('document_type_name', documentType);
    
    // Determine which application form to link to
    if ('is_uog_destination' in application) {
      formData.append('application_form1', application.id);
    } else {
      formData.append('application_form2', application.id);
    }

    uploadMutation.mutate(formData);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const isValidFileType = (file: File) => {
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    return allowedTypes.includes(file.type);
  };

  const isValidFileSize = (file: File) => {
    return file.size <= 10 * 1024 * 1024; // 10MB
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Document
        </CardTitle>
        <CardDescription>
          Upload required documents for your application
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Document Type Selection */}
        <div className="space-y-2">
          <Label htmlFor="document_type">Document Type *</Label>
          <Select value={documentType} onValueChange={setDocumentType}>
            <SelectTrigger>
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              {application.required_document_types_list.filter(docType => docType && docType.trim() !== '').map((docType, index) => (
                <SelectItem key={index} value={docType}>
                  {docType}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* File Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragActive 
              ? 'border-primary bg-primary/5' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
            onChange={handleFileSelect}
          />
          
          {selectedFile ? (
            <div className="space-y-2">
              <FileText className="h-12 w-12 mx-auto text-green-500" />
              <div className="font-medium">{selectedFile.name}</div>
              <div className="text-sm text-muted-foreground">
                {formatFileSize(selectedFile.size)}
              </div>
              
              {/* File validation */}
              <div className="space-y-1">
                {!isValidFileType(selectedFile) && (
                  <div className="flex items-center gap-2 text-destructive text-sm">
                    <AlertCircle className="h-4 w-4" />
                    Invalid file type. Allowed: PDF, JPG, PNG, DOC, DOCX
                  </div>
                )}
                {!isValidFileSize(selectedFile) && (
                  <div className="flex items-center gap-2 text-destructive text-sm">
                    <AlertCircle className="h-4 w-4" />
                    File size exceeds 10MB limit
                  </div>
                )}
                {isValidFileType(selectedFile) && isValidFileSize(selectedFile) && (
                  <div className="flex items-center gap-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    File is valid
                  </div>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedFile(null);
                  if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                  }
                }}
              >
                <X className="h-4 w-4 mr-2" />
                Remove
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className="h-12 w-12 mx-auto text-gray-400" />
              <div className="font-medium">Drop files here or click to browse</div>
              <div className="text-sm text-muted-foreground">
                Supports: PDF, JPG, PNG, DOC, DOCX (max 10MB)
              </div>
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
              >
                Browse Files
              </Button>
            </div>
          )}
        </div>

        {/* Upload Progress */}
        {uploadMutation.isPending && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Uploading document...</span>
            </div>
            <Progress value={50} className="w-full" />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={handleUpload}
            disabled={
              !selectedFile ||
              !documentType ||
              !isValidFileType(selectedFile) ||
              !isValidFileSize(selectedFile) ||
              uploadMutation.isPending
            }
            className="flex-1"
          >
            {uploadMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </>
            )}
          </Button>

          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={uploadMutation.isPending}
            >
              Close
            </Button>
          )}
        </div>

        {/* Uploaded Documents */}
        {application.documents && application.documents.length > 0 && (
          <div className="space-y-2">
            <div className="font-medium">Uploaded Documents</div>
            <div className="grid grid-cols-1 gap-2">
              {application.documents.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded bg-muted/50">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <div>
                      <div className="font-medium text-sm">{doc.document_type_name}</div>
                      <div className="text-xs text-muted-foreground">
                        Uploaded: {new Date(doc.uploaded_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="success">Uploaded</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // TODO: Implement document deletion
                        toast.info('Document deletion will be implemented');
                      }}
                      className="text-destructive hover:text-destructive"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Document Status */}
        <div className="space-y-2">
          <div className="font-medium">Document Requirements</div>
          <div className="grid grid-cols-1 gap-2">
            {application.required_document_types_list.map((docType, index) => {
              const isUploaded = application.documents?.some(doc => doc.document_type_name === docType);
              return (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">{docType}</span>
                  <Badge variant={isUploaded ? 'success' : 'secondary'}>
                    {isUploaded ? 'Uploaded' : 'Required'}
                  </Badge>
                </div>
              );
            })}
          </div>

          <div className="text-sm text-muted-foreground">
            {application.document_completion_status.uploaded_count} of {application.document_completion_status.required_count} documents uploaded
            ({application.document_completion_status.completion_percentage.toFixed(0)}% complete)
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AlumniDocumentUpload;
