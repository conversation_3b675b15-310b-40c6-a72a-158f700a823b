import { useState, useEffect, ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { settingsAPI } from '@/services/api';
import { QuickLink } from '@/services/settingsAPI';
import {
  ExternalLink,
  Home,
  Calendar,
  Monitor,
  Users,
  GraduationCap,
  BookOpen,
  Mail,
  Phone,
  FileText,
  Globe
} from 'lucide-react';

const Footer = () => {
  const [settings, setSettings] = useState<{
    copyright: string;
    systemName: string;
    organizationName: string;
    contact_number: string;
    support_email: string;
    address: string;
    po_box?: string;
    footer_logo_url?: string;
    contact_info: string;
  }>({
    copyright: '',
    systemName: '',
    organizationName: '',
    contact_number: '',
    support_email: '',
    address: '',
    po_box: '',
    footer_logo_url: '',
    contact_info: ''
  });

  const [quickLinks, setQuickLinks] = useState<QuickLink[]>([]);
  const [socialMediaLinks, setSocialMediaLinks] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isQuickLinksLoading, setIsQuickLinksLoading] = useState(true);
  const [isSocialMediaLoading, setIsSocialMediaLoading] = useState(true);

  // Function to get appropriate icon based on link name
  const getIconForLink = (name: string): ReactNode => {
    const nameLower = name.toLowerCase();

    if (nameLower.includes('home')) return <Home className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('registrar') || nameLower.includes('academic')) return <Calendar className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('ict') || nameLower.includes('computer')) return <Monitor className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('alumni') || nameLower.includes('student')) return <Users className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('graduate') || nameLower.includes('education')) return <GraduationCap className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('library') || nameLower.includes('book')) return <BookOpen className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('contact') || nameLower.includes('email')) return <Mail className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('phone') || nameLower.includes('call')) return <Phone className="h-5 w-5 mr-2 mt-0.5" />;
    if (nameLower.includes('form') || nameLower.includes('document')) return <FileText className="h-5 w-5 mr-2 mt-0.5" />;

    // Default icon
    return <Globe className="h-5 w-5 mr-2 mt-0.5" />;
  };

  // Function to get social media icon based on platform
  const getSocialIcon = (platform: string): ReactNode => {
    const platformLower = platform.toLowerCase();

    // Return appropriate SVG based on platform
    if (platformLower.includes('facebook')) {
      return (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
        </svg>
      );
    }

    if (platformLower.includes('twitter') || platformLower.includes('x.com')) {
      return (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
        </svg>
      );
    }

    if (platformLower.includes('linkedin')) {
      return (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
        </svg>
      );
    }

    if (platformLower.includes('youtube')) {
      return (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
        </svg>
      );
    }

    if (platformLower.includes('instagram')) {
      return (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
        </svg>
      );
    }

    if (platformLower.includes('telegram')) {
      return (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
        </svg>
      );
    }

    if (platformLower.includes('tiktok')) {
      return (
        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
        </svg>
      );
    }

    // Default icon for other platforms
    return (
      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
      </svg>
    );
  };

  // Fetch organization settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          // Get footer logo URL and ensure it's an absolute URL
          let footerLogoUrl = '';
          if (response.data.footer_logo_url) {
            footerLogoUrl = response.data.footer_logo_url.startsWith('http')
              ? response.data.footer_logo_url
              : `http://localhost:8000${response.data.footer_logo_url}`;
          }

          setSettings({
            copyright: response.data.copyright || '',
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || '',
            contact_number: response.data.contact_number || '',
            support_email: response.data.support_email || '',
            address: response.data.address || '',
            po_box: response.data.po_box || '',
            footer_logo_url: footerLogoUrl,
            contact_info: response.data.contact_info || ''
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Fetch quick links on component mount
  useEffect(() => {
    const fetchQuickLinks = async () => {
      setIsQuickLinksLoading(true);
      try {
        const response = await settingsAPI.getPublicQuickLinks();
        if (response.data) {
          setQuickLinks(response.data);
        }
      } catch (error) {
        console.error('Error fetching quick links:', error);
      } finally {
        setIsQuickLinksLoading(false);
      }
    };

    fetchQuickLinks();
  }, []);

  // Fetch social media links on component mount
  useEffect(() => {
    const fetchSocialMediaLinks = async () => {
      setIsSocialMediaLoading(true);
      try {
        const response = await settingsAPI.getPublicSocialMediaLinks();
        if (response.data) {
          setSocialMediaLinks(response.data);
        }
      } catch (error) {
        console.error('Error fetching social media links:', error);
      } finally {
        setIsSocialMediaLoading(false);
      }
    };

    fetchSocialMediaLinks();
  }, []);

  return (
    <footer className="bg-[#1a73c0] text-white" style={{ backgroundColor: 'var(--brand-primary, #1a73c0)' }}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Logo Section */}
          <div className="col-span-1 flex flex-col items-center">
            <div className="mb-4">
              {isLoading ? (
                <div className="h-24 w-24 bg-white/20 animate-pulse rounded"></div>
              ) : settings.footer_logo_url ? (
                <img
                  src={settings.footer_logo_url}
                  alt={`${settings.organizationName} Logo`}
                  className="h-24 bg-white p-2 rounded"
                  onError={(e) => {
                    // Fallback if image fails to load
                    e.currentTarget.src = "/assets/images/ka8d0x7m.png";
                  }}
                />
              ) : (
                <img src="/assets/images/ka8d0x7m.png" alt={`${settings.organizationName} Logo`} className="h-24 bg-white p-2 rounded" />
              )}
            </div>
            <div className="flex space-x-4 mt-2">
              {isSocialMediaLoading ? (
                // Loading state - show skeleton loaders
                Array(5).fill(0).map((_, index) => (
                  <div key={index} className="h-5 w-5 bg-white/20 animate-pulse rounded-full"></div>
                ))
              ) : socialMediaLinks.length > 0 ? (
                // Display dynamic social media links from API
                socialMediaLinks.map((link, index) => (
                  <a
                    key={link.id || index}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white hover:text-gray-200"
                    title={link.platform_display || link.platform}
                  >
                    {/* Use platform to determine which icon to show */}
                    {getSocialIcon(link.platform)}
                  </a>
                ))
              ) : (
                // Fallback social media links
                <>
                  <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-white hover:text-gray-200">
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                    </svg>
                  </a>
                  <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-white hover:text-gray-200">
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                    </svg>
                  </a>
                  <a href="https://tiktok.com" target="_blank" rel="noopener noreferrer" className="text-white hover:text-gray-200">
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
                    </svg>
                  </a>
                </>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4 text-white">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              {isQuickLinksLoading ? (
                // Loading state - show skeleton loaders
                Array(4).fill(0).map((_, index) => (
                  <li key={index} className="flex items-start">
                    <div className="h-5 w-5 mr-2 mt-0.5 bg-white/20 animate-pulse rounded"></div>
                    <div className="h-5 w-36 bg-white/20 animate-pulse rounded"></div>
                  </li>
                ))
              ) : quickLinks.length > 0 ? (
                // Display dynamic quick links from API
                quickLinks.map((link, index) => (
                  <li key={link.id || index} className="flex items-start">
                    {/* Icon based on link name or default */}
                    {getIconForLink(link.name)}
                    <a
                      href={link.url}
                      target={link.is_external ? "_blank" : undefined}
                      rel={link.is_external ? "noopener noreferrer" : undefined}
                      className="hover:text-gray-200 transition-colors flex items-center"
                    >
                      {link.name}
                      {link.is_external && <ExternalLink className="ml-1 h-3 w-3" />}
                    </a>
                  </li>
                ))
              ) : (
                // Fallback for when there are no quick links
                <li className="flex items-start">
                  <Home className="h-5 w-5 mr-2 mt-0.5" />
                  {isLoading ? (
                    <div className="h-5 w-36 bg-white/20 animate-pulse rounded"></div>
                  ) : (
                    <a href="/" className="hover:text-gray-200 transition-colors">{settings.organizationName || 'Home'}</a>
                  )}
                </li>
              )}
            </ul>
          </div>

          {/* Contact Information */}
          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4 text-white">Contact Us</h3>
            <p className="text-sm text-gray-100 mb-2 font-medium">{settings.contact_info || 'Contact Information'}</p>
            <ul className="space-y-2 text-sm">
              {isLoading ? (
                // Loading state
                Array(4).fill(0).map((_, index) => (
                  <li key={index} className="flex items-start">
                    <div className="h-5 w-5 mr-2 mt-0.5 bg-white/20 animate-pulse rounded"></div>
                    <div className="h-5 w-36 bg-white/20 animate-pulse rounded"></div>
                  </li>
                ))
              ) : (
                <>
                  {/* Phone */}
                  <li className="flex items-start">
                    <Phone className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{settings.contact_number || '(+251) 581-141237'}</span>
                  </li>

                  {/* Email */}
                  <li className="flex items-start">
                    <Mail className="h-5 w-5 mr-2 mt-0.5" />
                    <span>{settings.support_email || '<EMAIL>'}</span>
                  </li>

                  {/* Address */}
                  <li className="flex items-start">
                    <svg className="h-5 w-5 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span>{settings.address || 'Maraki Street, Gondar, Ethiopia'}</span>
                  </li>

                  {/* P.O. Box */}
                  {settings.po_box && (
                    <li className="flex items-start">
                      <svg className="h-5 w-5 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                      <span>{settings.po_box}</span>
                    </li>
                  )}
                </>
              )}
            </ul>
          </div>
        </div>

        <div className="border-t border-blue-400 mt-8 pt-6 text-center text-sm" style={{ borderColor: 'var(--brand-primary-light, #4CB963)' }}>
          {isLoading ? (
            <div className="h-5 w-64 bg-white/20 animate-pulse rounded mx-auto"></div>
          ) : (
            <div className="flex items-center justify-center gap-2 flex-wrap">
              <Link
                to="/developers"
                className="text-white hover:text-blue-200 transition-colors duration-200 underline underline-offset-2 font-medium"
              >
                Developers
              </Link>
              <span className="text-white/60">|</span>
              <span>{settings.copyright}</span>
            </div>
          )}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
