from django.core.management.base import BaseCommand
from GraduateVerification.models import (
    VerificationCollege,
    VerificationDepartment,
    VerificationFieldOfStudy,
    VerificationProgram,
    AdmissionClassification
)

class Command(BaseCommand):
    help = 'Seeds the database with verification data (colleges, departments, etc.)'

    def handle(self, *args, **options):
        # Create colleges
        colleges = [
            {"name": "College of Medicine and Health Sciences", "code": "CMHS"},
            {"name": "College of Business and Economics", "code": "CBE"},
            {"name": "College of Natural and Computational Sciences", "code": "CNCS"},
            {"name": "College of Social Sciences and Humanities", "code": "CSSH"},
            {"name": "School of Technology", "code": "SOT"},
            {"name": "College of Agriculture and Environmental Sciences", "code": "CAES"},
            {"name": "College of Veterinary Medicine and Animal Sciences", "code": "CVMAS"},
            {"name": "School of Law", "code": "SOL"},
            {"name": "School of Education", "code": "SOE"}
        ]
        
        college_objects = {}
        for college_data in colleges:
            college, created = VerificationCollege.objects.get_or_create(
                code=college_data["code"],
                defaults={"name": college_data["name"]}
            )
            college_objects[college.code] = college
            if created:
                self.stdout.write(f"Created college: {college.name}")
            else:
                self.stdout.write(f"College already exists: {college.name}")
        
        # Create departments
        departments = [
            {"name": "Medicine", "code": "MED", "college_code": "CMHS"},
            {"name": "Nursing", "code": "NURS", "college_code": "CMHS"},
            {"name": "Pharmacy", "code": "PHARM", "college_code": "CMHS"},
            {"name": "Public Health", "code": "PH", "college_code": "CMHS"},
            {"name": "Midwifery", "code": "MID", "college_code": "CMHS"},
            
            {"name": "Accounting and Finance", "code": "ACFIN", "college_code": "CBE"},
            {"name": "Management", "code": "MGMT", "college_code": "CBE"},
            {"name": "Economics", "code": "ECON", "college_code": "CBE"},
            {"name": "Tourism and Hotel Management", "code": "THM", "college_code": "CBE"},
            
            {"name": "Computer Science", "code": "CS", "college_code": "CNCS"},
            {"name": "Mathematics", "code": "MATH", "college_code": "CNCS"},
            {"name": "Physics", "code": "PHYS", "college_code": "CNCS"},
            {"name": "Chemistry", "code": "CHEM", "college_code": "CNCS"},
            {"name": "Biology", "code": "BIO", "college_code": "CNCS"},
            {"name": "Statistics", "code": "STAT", "college_code": "CNCS"},
            
            {"name": "Psychology", "code": "PSYCH", "college_code": "CSSH"},
            {"name": "Sociology", "code": "SOC", "college_code": "CSSH"},
            {"name": "History", "code": "HIST", "college_code": "CSSH"},
            {"name": "Geography", "code": "GEO", "college_code": "CSSH"},
            {"name": "English Language and Literature", "code": "ENG", "college_code": "CSSH"},
            {"name": "Amharic Language and Literature", "code": "AMH", "college_code": "CSSH"},
            
            {"name": "Civil Engineering", "code": "CE", "college_code": "SOT"},
            {"name": "Electrical Engineering", "code": "EE", "college_code": "SOT"},
            {"name": "Mechanical Engineering", "code": "ME", "college_code": "SOT"},
            {"name": "Information Technology", "code": "IT", "college_code": "SOT"},
            
            {"name": "Plant Science", "code": "PS", "college_code": "CAES"},
            {"name": "Animal Science", "code": "AS", "college_code": "CAES"},
            {"name": "Natural Resource Management", "code": "NRM", "college_code": "CAES"},
            
            {"name": "Veterinary Medicine", "code": "VM", "college_code": "CVMAS"},
            {"name": "Animal Production", "code": "AP", "college_code": "CVMAS"},
            
            {"name": "Law", "code": "LAW", "college_code": "SOL"},
            
            {"name": "Educational Planning and Management", "code": "EPM", "college_code": "SOE"},
            {"name": "Special Needs Education", "code": "SNE", "college_code": "SOE"}
        ]
        
        department_objects = {}
        for dept_data in departments:
            college = college_objects.get(dept_data["college_code"])
            if not college:
                self.stdout.write(self.style.ERROR(f"College not found for code: {dept_data['college_code']}"))
                continue
                
            department, created = VerificationDepartment.objects.get_or_create(
                code=dept_data["code"],
                college=college,
                defaults={"name": dept_data["name"]}
            )
            department_objects[department.code] = department
            if created:
                self.stdout.write(f"Created department: {department.name}")
            else:
                self.stdout.write(f"Department already exists: {department.name}")
        
        # Create fields of study
        fields_of_study = [
            {"name": "Doctor of Medicine", "code": "MD", "department_code": "MED"},
            {"name": "Bachelor of Science in Nursing", "code": "BSN", "department_code": "NURS"},
            {"name": "Bachelor of Pharmacy", "code": "BPHARM", "department_code": "PHARM"},
            {"name": "Master of Public Health", "code": "MPH", "department_code": "PH"},
            {"name": "Bachelor of Science in Midwifery", "code": "BSM", "department_code": "MID"},
            
            {"name": "Bachelor of Accounting", "code": "BACC", "department_code": "ACFIN"},
            {"name": "Bachelor of Finance", "code": "BFIN", "department_code": "ACFIN"},
            {"name": "Bachelor of Management", "code": "BMGMT", "department_code": "MGMT"},
            {"name": "Bachelor of Economics", "code": "BECON", "department_code": "ECON"},
            {"name": "Bachelor of Tourism and Hotel Management", "code": "BTHM", "department_code": "THM"},
            
            {"name": "Bachelor of Science in Computer Science", "code": "BSCS", "department_code": "CS"},
            {"name": "Master of Science in Computer Science", "code": "MSCS", "department_code": "CS"},
            {"name": "Bachelor of Science in Mathematics", "code": "BSMATH", "department_code": "MATH"},
            {"name": "Bachelor of Science in Physics", "code": "BSPHYS", "department_code": "PHYS"},
            {"name": "Bachelor of Science in Chemistry", "code": "BSCHEM", "department_code": "CHEM"},
            {"name": "Bachelor of Science in Biology", "code": "BSBIO", "department_code": "BIO"},
            {"name": "Bachelor of Science in Statistics", "code": "BSSTAT", "department_code": "STAT"},
            
            {"name": "Bachelor of Arts in Psychology", "code": "BAPSYCH", "department_code": "PSYCH"},
            {"name": "Bachelor of Arts in Sociology", "code": "BASOC", "department_code": "SOC"},
            {"name": "Bachelor of Arts in History", "code": "BAHIST", "department_code": "HIST"},
            {"name": "Bachelor of Arts in Geography", "code": "BAGEO", "department_code": "GEO"},
            {"name": "Bachelor of Arts in English", "code": "BAENG", "department_code": "ENG"},
            {"name": "Bachelor of Arts in Amharic", "code": "BAAMH", "department_code": "AMH"},
            
            {"name": "Bachelor of Science in Civil Engineering", "code": "BSCE", "department_code": "CE"},
            {"name": "Bachelor of Science in Electrical Engineering", "code": "BSEE", "department_code": "EE"},
            {"name": "Bachelor of Science in Mechanical Engineering", "code": "BSME", "department_code": "ME"},
            {"name": "Bachelor of Science in Information Technology", "code": "BSIT", "department_code": "IT"},
            
            {"name": "Bachelor of Science in Plant Science", "code": "BSPS", "department_code": "PS"},
            {"name": "Bachelor of Science in Animal Science", "code": "BSAS", "department_code": "AS"},
            {"name": "Bachelor of Science in Natural Resource Management", "code": "BSNRM", "department_code": "NRM"},
            
            {"name": "Doctor of Veterinary Medicine", "code": "DVM", "department_code": "VM"},
            {"name": "Bachelor of Science in Animal Production", "code": "BSAP", "department_code": "AP"},
            
            {"name": "Bachelor of Laws", "code": "LLB", "department_code": "LAW"},
            
            {"name": "Bachelor of Education in Educational Planning", "code": "BEDEPM", "department_code": "EPM"},
            {"name": "Bachelor of Education in Special Needs", "code": "BEDSNE", "department_code": "SNE"}
        ]
        
        for field_data in fields_of_study:
            department = department_objects.get(field_data["department_code"])
            if not department:
                self.stdout.write(self.style.ERROR(f"Department not found for code: {field_data['department_code']}"))
                continue
                
            field, created = VerificationFieldOfStudy.objects.get_or_create(
                code=field_data["code"],
                department=department,
                defaults={"name": field_data["name"]}
            )
            if created:
                self.stdout.write(f"Created field of study: {field.name}")
            else:
                self.stdout.write(f"Field of study already exists: {field.name}")
        
        # Create programs
        programs = [
            {"name": "Undergraduate Program", "code": "UG"},
            {"name": "Graduate Program", "code": "GR"},
            {"name": "Postgraduate Program", "code": "PG"},
            {"name": "Doctoral Program", "code": "DR"},
            {"name": "Certificate Program", "code": "CP"},
            {"name": "Diploma Program", "code": "DP"}
        ]
        
        for program_data in programs:
            program, created = VerificationProgram.objects.get_or_create(
                code=program_data["code"],
                defaults={"name": program_data["name"]}
            )
            if created:
                self.stdout.write(f"Created program: {program.name}")
            else:
                self.stdout.write(f"Program already exists: {program.name}")
        
        # Create admission classifications
        classifications = [
            {"name": "Regular", "description": "Students admitted through regular entrance examination"},
            {"name": "Transfer", "description": "Students transferred from other institutions"},
            {"name": "Special", "description": "Students admitted through special programs"},
            {"name": "International", "description": "International students"}
        ]
        
        for class_data in classifications:
            classification, created = AdmissionClassification.objects.get_or_create(
                name=class_data["name"],
                defaults={"description": class_data["description"]}
            )
            if created:
                self.stdout.write(f"Created admission classification: {classification.name}")
            else:
                self.stdout.write(f"Admission classification already exists: {classification.name}")
        
        self.stdout.write(self.style.SUCCESS("Successfully seeded verification data"))
