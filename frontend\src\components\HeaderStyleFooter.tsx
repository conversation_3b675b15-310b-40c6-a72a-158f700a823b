import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { settingsAPI } from '@/services/api';
import { Mail, Phone, Globe } from 'lucide-react';

const HeaderStyleFooter: React.FC = () => {
  const [settings, setSettings] = useState<{
    systemName: string | null;
    organizationName: string | null;
    headerLogoUrl: string | null;
    contact_number: string | null;
    support_email: string | null;
  }>({
    systemName: null,
    organizationName: null,
    headerLogoUrl: null,
    contact_number: null,
    support_email: null
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Fetch organization settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          // Get header logo URL and ensure it's an absolute URL
          let headerLogoUrl = null;
          if (response.data.header_logo_url) {
            headerLogoUrl = response.data.header_logo_url.startsWith('http')
              ? response.data.header_logo_url
              : `${import.meta.env.VITE_BACKEND_MEDIA_URL || 'http://localhost:8000'}${response.data.header_logo_url}`;
          }

          setSettings({
            systemName: response.data.system_name || null,
            organizationName: response.data.organization || null,
            headerLogoUrl: headerLogoUrl,
            contact_number: response.data.contact_number || null,
            support_email: response.data.support_email || null
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#1a73c0] text-white shadow-md mt-auto w-full" style={{ backgroundColor: 'var(--brand-primary, #1a73c0)' }}>
      <div className="container mx-auto py-4 px-4 sm:px-6 lg:px-8">
        {/* Main footer content */}
        <div className="flex justify-center items-center">
          {/* Contact information */}
          <div className="flex items-center space-x-6">
            {settings.support_email && (
              <a href={`mailto:${settings.support_email}`} className="text-white hover:text-gray-200 text-sm flex items-center">
                <Mail className="h-4 w-4 mr-1" />
                <span className="sm:inline">{settings.support_email}</span>
              </a>
            )}
            {settings.contact_number && (
              <a href={`tel:${settings.contact_number}`} className="text-white hover:text-gray-200 text-sm flex items-center">
                <Phone className="h-4 w-4 mr-1" />
                <span className="sm:inline">{settings.contact_number}</span>
              </a>
            )}
            <a href="https://www.uog.edu.et" target="_blank" rel="noopener noreferrer" className="text-white hover:text-gray-200 text-sm flex items-center">
              <Globe className="h-4 w-4 mr-1" />
              <span className="sm:inline">uog.edu.et</span>
            </a>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-4 pt-4 border-t border-white/20 text-center text-xs text-white">
          <div className="flex items-center justify-center gap-2 flex-wrap">
            <span>© {currentYear} University of Gondar. All rights reserved.</span>
            <span className="text-white/60">|</span>
            <Link
              to="/developers"
              className="text-white hover:text-blue-200 transition-colors duration-200 underline underline-offset-2 font-medium"
              style={{ color: 'white', textDecoration: 'underline' }}
            >
              Developers
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default HeaderStyleFooter;
