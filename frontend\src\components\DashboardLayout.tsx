import React, { useState, useEffect } from 'react';
import { Link, useLocation, Navigate } from 'react-router-dom';
import { settingsAPI } from '@/services/api';
import {
  Home, FileText, Clock, Bell, User,
  LogOut, ChevronRight, Menu, X,
  UserCircle, BarChart3, Info,
  Settings as SettingsIcon, KeyRound,
  Shield, Users, Database, BookOpen,
  GraduationCap, Building2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import NotificationBadge from './NotificationBadge';
import ChangePasswordModal from '@/components/ChangePasswordModal';
import HeaderStyleFooter from '@/components/HeaderStyleFooter';
import { useRBAC } from '@/contexts/SimpleRBACContext';
// RBAC permissions utilities removed - using simplified system
import { toast } from 'sonner';

interface MenuItem {
  path: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  subPaths?: string[];
  requireSuperuser?: boolean;
  badge?: string;
}



interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const userData = localStorage.getItem('user');
  const user = userData ? JSON.parse(userData) : null;

  // Use simplified RBAC context for role-based access
  const {
    isSuperuser,
    isStaff,
    isRegularUser,
    canAccessAdmin,
    userType,
    isLoading: rbacLoading
  } = useRBAC();

  // Map simplified roles to legacy role checks for backward compatibility
  const isSuperAdmin = isSuperuser;
  const isAdministrator = isStaff;
  const isMainRegistrar = isStaff;
  const isRegistrarOfficer = isStaff;
  const isDepartmentHead = isStaff;
  const isApplicant = isRegularUser;
  const isAdminLevel = canAccessAdmin;
  const isRegistrarLevel = isStaff;
  const isStaffLevel = isStaff;

  // Legacy compatibility functions
  const hasRole = (role: string) => {
    if (role === 'superuser') return isSuperuser;
    if (role === 'staff') return isStaff;
    return false;
  };

  const hasRoleLevel = (level: string) => {
    if (level === 'admin') return canAccessAdmin;
    if (level === 'staff') return isStaff;
    return false;
  };

  const roles = isSuperuser ? ['superuser'] : isStaff ? ['staff'] : ['user'];
  const [settings, setSettings] = useState<{
    systemName: string | null;
    organizationName: string | null;
    headerLogoUrl: string | null;
  }>({
    systemName: null,
    organizationName: null,
    headerLogoUrl: null
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Fetch organization settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          // Get header logo URL and ensure it's an absolute URL
          let headerLogoUrl = null;
          if (response.data.header_logo_url) {
            headerLogoUrl = response.data.header_logo_url.startsWith('http')
              ? response.data.header_logo_url
              : `http://localhost:8000${response.data.header_logo_url}`;
          }

          setSettings({
            systemName: response.data.system_name || null,
            organizationName: response.data.organization || null,
            headerLogoUrl: headerLogoUrl
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // User role is determined by user properties

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    toast.success('Logged out successfully');
    window.location.href = '/login';
  };

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Simplified access check - only superuser can access admin items
  const canAccessMenuItem = (item: MenuItem): boolean => {
    // Check if item requires superuser access
    if (item.requireSuperuser) {
      return user?.is_superuser || false;
    }

    return true; // No restrictions for regular items
  };

  // Define comprehensive menu items based on roles
  const allMenuItems: MenuItem[] = [
    // Common items for all users
    {
      path: '/dashboard',
      name: 'Dashboard',
      icon: Home
    },


    // User items (available to all authenticated users)
    {
      path: '/personal-information',
      name: 'Personal Information',
      icon: UserCircle
    },
    {
      path: '/application',
      name: 'Application',
      icon: FileText,
      subPaths: [
        '/application/personal-info',
        '/application/gat',
        '/application/program-selection',
        '/application/documentation',
        '/application/payment',
        '/application/status'
      ]
    },
    {
      path: '/status',
      name: 'Application Status',
      icon: Clock
    },

    // Admin items (superuser only)
    {
      path: '/graduate-admin?tab=dashboard',
      name: 'Graduate Admin',
      icon: BarChart3,
      requireSuperuser: true,
      badge: 'Admin'
    },
    {
      path: '/system-settings',
      name: 'System Settings',
      icon: SettingsIcon,
      requireSuperuser: true,
      badge: 'Admin'
    },


    {
      path: '/notifications',
      name: 'Notifications',
      icon: Bell
    }
  ];

  // Filter menu items based on user's role
  const menuItems = allMenuItems.filter(canAccessMenuItem);

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-[#1a73c0] text-white shadow-md sticky top-0 z-50" style={{ backgroundColor: 'var(--brand-primary, #1a73c0)' }}>
        <div className="container mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <button
              className="md:hidden text-white p-2 rounded-md hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-white/20"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <X size={24} /> : <Menu size={24} />}
            </button>

            {isSettingsLoading ? (
              <div className="flex items-center">
                <div className="h-12 w-12 bg-white/20 animate-pulse rounded-md mr-3"></div>
                <div className="h-7 w-48 bg-white/20 animate-pulse rounded hidden sm:block"></div>
              </div>
            ) : settings.headerLogoUrl ? (
              <div className="flex items-center">
                <img
                  src={settings.headerLogoUrl}
                  alt={settings.organizationName || 'Organization Logo'}
                  className="h-12 w-auto object-contain mr-3 bg-white/10 rounded-md p-1"
                  onError={(e) => {
                    console.error('Header logo failed to load:', settings.headerLogoUrl);
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <h1 className="text-xl font-heading font-bold text-white truncate max-w-[200px] md:max-w-none">
                  {settings.organizationName || 'Dashboard'}
                </h1>
              </div>
            ) : (
              <div className="flex items-center">
                <div className="h-12 w-12 bg-white/10 rounded-md mr-3 flex items-center justify-center">
                  <span className="text-white font-bold text-lg">
                    {(settings.systemName || 'System').charAt(0).toUpperCase()}
                  </span>
                </div>
                <h1 className="text-xl font-heading font-bold text-white truncate max-w-[200px] md:max-w-none">
                  {settings.organizationName || 'Dashboard'}
                </h1>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <NotificationBadge className="text-white hover:text-gray-200 cursor-pointer" />

            {/* Logout button - always visible */}
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-red-600/30 rounded-md transition-colors flex items-center"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Sign Out</span>
            </Button>

            {/* Mobile buttons - combined in one row */}
            <div className="md:hidden flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-[#0e4a7d] rounded-md p-2"
                onClick={() => setIsPasswordModalOpen(true)}
              >
                <KeyRound className="h-4 w-4" />
                <span className="text-xs ml-1">Password</span>
              </Button>
            </div>

            {/* User profile and actions for desktop */}
            <div className="hidden md:flex items-center space-x-3 border-l border-white/30 pl-4">
              {/* Simplified user type display */}
              {user?.is_superuser && (
                <Badge className="text-xs bg-red-500/20 border-red-300/30 text-white">
                  Superuser
                </Badge>
              )}

              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-[#0e4a7d] rounded-md"
                  onClick={() => setIsPasswordModalOpen(true)}
                >
                  <KeyRound className="h-4 w-4 mr-1" />
                  <span className="text-xs">Password</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar for desktop */}
        <aside className="hidden md:flex flex-col w-64 bg-white border-r border-gray-200 flex-shrink-0 fixed left-0 top-16 h-[calc(100vh-4rem)] z-40">
          <nav className="flex-1 pt-5 pb-4 overflow-y-auto">
            <div className="px-4 space-y-1">
              {menuItems.map((item) => {
                // Check if current path matches the menu item path or any of its sub-paths
                const isActive =
                  location.pathname === item.path ||
                  (item.path !== '/' && location.pathname.startsWith(item.path)) ||
                  (item.subPaths && item.subPaths.some(subPath => location.pathname.startsWith(subPath)));

                // For Graduate Dashboard, check if the URL includes the tab parameter
                const isGraduateDashboard = item.path.includes('/graduate-admin?tab=dashboard');
                const isGraduateDashboardActive = isGraduateDashboard &&
                  location.pathname === '/graduate-admin' &&
                  location.search.includes('tab=dashboard');

                // Determine if this item is active
                const itemIsActive = isGraduateDashboard ? isGraduateDashboardActive : isActive;

                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={cn(
                      "flex items-center px-4 py-3 text-sm font-medium rounded-md group transition-colors",
                      itemIsActive
                        ? "bg-[#1a73c0]/10 text-[#1a73c0] border-l-4 border-[#1a73c0]"
                        : "text-gray-700 hover:bg-[#1a73c0]/10 hover:text-[#1a73c0]"
                    )}
                  >
                    <item.icon className={cn("mr-3 h-5 w-5", itemIsActive ? "text-[#1a73c0]" : "text-gray-500 group-hover:text-[#1a73c0]")} />
                    <span className="flex-1">{item.name}</span>
                    {item.badge && (
                      <Badge
                        variant="outline"
                        className="ml-2 text-xs px-1.5 py-0.5 bg-gray-100 text-gray-600 border-gray-300"
                      >
                        {item.badge}
                      </Badge>
                    )}
                    {itemIsActive && <ChevronRight className="ml-2 h-4 w-4" />}
                  </Link>
                );
              })}
            </div>
          </nav>


        </aside>

        {/* Mobile sidebar */}
        {sidebarOpen && (
          <div className="fixed inset-0 z-40 md:hidden">
            <div className="fixed inset-0 bg-black/30" aria-hidden="true" onClick={() => setSidebarOpen(false)}></div>
            <div className="fixed inset-y-0 left-0 w-72 sm:w-64 bg-white z-40 flex flex-col">
              <div className="h-16 flex items-center justify-between px-4 border-b">
                <h2 className="text-lg font-medium text-gray-900">Menu</h2>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="p-2 rounded-md hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-200"
                >
                  <X className="h-6 w-6 text-gray-500" />
                </button>
              </div>
              <nav className="flex-1 pt-5 pb-4 overflow-y-auto">
                <div className="px-4 space-y-1">
                  {menuItems.map((item) => {
                    // Check if current path matches the menu item path or any of its sub-paths
                    const isActive =
                      location.pathname === item.path ||
                      (item.path !== '/' && location.pathname.startsWith(item.path)) ||
                      (item.subPaths && item.subPaths.some(subPath => location.pathname.startsWith(subPath)));

                    // For Graduate Dashboard, check if the URL includes the tab parameter
                    const isGraduateDashboard = item.path.includes('/graduate-admin?tab=dashboard');
                    const isGraduateDashboardActive = isGraduateDashboard &&
                      location.pathname === '/graduate-admin' &&
                      location.search.includes('tab=dashboard');

                    // Determine if this item is active
                    const itemIsActive = isGraduateDashboard ? isGraduateDashboardActive : isActive;

                    return (
                      <Link
                        key={item.path}
                        to={item.path}
                        className={cn(
                          "flex items-center px-4 py-3 text-sm font-medium rounded-md group transition-colors",
                          itemIsActive
                            ? "bg-[#1a73c0]/10 text-[#1a73c0] border-l-4 border-[#1a73c0]"
                            : "text-gray-700 hover:bg-[#1a73c0]/10 hover:text-[#1a73c0]"
                        )}
                        onClick={() => setSidebarOpen(false)}
                      >
                        <item.icon className={cn("mr-3 h-5 w-5", itemIsActive ? "text-[#1a73c0]" : "text-gray-500 group-hover:text-[#1a73c0]")} />
                        <span className="flex-1">{item.name}</span>
                        {item.badge && (
                          <Badge
                            variant="outline"
                            className="ml-2 text-xs px-1.5 py-0.5 bg-gray-100 text-gray-600 border-gray-300"
                          >
                            {item.badge}
                          </Badge>
                        )}
                        {itemIsActive && <ChevronRight className="ml-2 h-4 w-4" />}
                      </Link>
                    );
                  })}
                </div>
              </nav>

              {/* Mobile Sidebar Footer */}
              <div className="p-4 border-t border-gray-200 bg-gray-50">
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 justify-center text-gray-700 hover:text-[#1a73c0] hover:bg-[#1a73c0]/10 border-gray-200 shadow-sm"
                    onClick={() => {
                      setIsPasswordModalOpen(true);
                      setSidebarOpen(false);
                    }}
                  >
                    <KeyRound className="h-4 w-4 mr-1.5" />
                    <span className="text-xs font-medium">Password</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 justify-center text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 shadow-sm"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4 mr-1.5" />
                    <span className="text-xs font-medium">Logout</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <main className="flex-1 bg-gray-50 p-2 sm:p-4 md:p-6 md:ml-64 flex flex-col">
          <div className="flex-1 max-w-full overflow-x-auto">
            <div className="w-full mx-auto container-fluid">
              <div className="md:max-w-[1200px] lg:max-w-[1400px] mx-auto">
                {children}
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* Footer for non-staff users */}
      {!(user && (user.is_staff || user.is_superuser)) && <HeaderStyleFooter />}

      {/* Password Change Modal */}
      <ChangePasswordModal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
      />
    </div>
  );
};

export default DashboardLayout;
