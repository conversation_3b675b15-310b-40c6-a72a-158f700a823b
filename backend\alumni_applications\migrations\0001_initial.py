# Generated by Django 5.2.3 on 2025-06-15 19:29

import alumni_applications.models
import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('college', '0001_initial'),
        ('department', '0001_initial'),
        ('document_type', '0001_initial'),
        ('service_type', '0003_alter_servicetype_document_types'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentApplicationForm',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('first_name', models.CharField(help_text='First name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='First name must contain only letters and spaces.', regex='^[a-zA-Z\\s]+$')])),
                ('father_name', models.Char<PERSON>ield(help_text="Father's name (letters only)", max_length=50, validators=[django.core.validators.RegexValidator(message='Father name must contain only letters and spaces.', regex='^[a-zA-Z\\s]+$')])),
                ('last_name', models.CharField(help_text='Last name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='Last name must contain only letters and spaces.', regex='^[a-zA-Z\\s]+$')])),
                ('student_id', models.CharField(blank=True, help_text='Student ID format: uog/1254/21 (optional)', max_length=20, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='Student ID must be alphanumeric with forward slashes (e.g., uog/1254/21).', regex='^[a-zA-Z0-9/]+$')])),
                ('phone_number', models.CharField(help_text='International format with country code (e.g., +251912345678)', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number must be in international format with country code.', regex='^\\+?[1-9]\\d{1,14}$')])),
                ('email', models.EmailField(help_text='Valid email address', max_length=100, validators=[django.core.validators.EmailValidator()])),
                ('admission_type', models.CharField(choices=[('Regular', 'Regular'), ('Evening', 'Evening'), ('Summer', 'Summer'), ('Distance', 'Distance')], help_text='Type of admission', max_length=20)),
                ('degree_type', models.CharField(choices=[('Diploma', 'Diploma'), ('Degree', 'Degree'), ("Master's", "Master's"), ('PHD', 'PHD'), ('Sp.Certificate', 'Sp.Certificate')], help_text='Type of degree', max_length=20)),
                ('is_other_college', models.BooleanField(default=False, help_text='Check if college is not in the system')),
                ('other_college_name', models.CharField(blank=True, help_text='College name (if not in system)', max_length=100, null=True)),
                ('other_department_name', models.CharField(blank=True, help_text='Department name (if not in system)', max_length=100, null=True)),
                ('student_status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Graduated', 'Graduated')], help_text='Current student status', max_length=20)),
                ('current_year', models.CharField(blank=True, choices=[('1st year', '1st year'), ('2nd year', '2nd year'), ('3rd year', '3rd year'), ('4th year', '4th year'), ('5th year', '5th year'), ('6th year', '6th year'), ('7th year', '7th year'), ('8th year', '8th year')], help_text='Current year (required when status is Active)', max_length=20, null=True)),
                ('year_of_leaving_ethiopian', models.CharField(blank=True, help_text='Year of leaving in Ethiopian calendar (required when status is Inactive)', max_length=10, null=True)),
                ('year_of_leaving_gregorian', models.DateField(blank=True, help_text='Year of leaving in Gregorian calendar (required when status is Inactive)', null=True)),
                ('year_of_graduation_ethiopian', models.CharField(blank=True, help_text='Year of graduation in Ethiopian calendar (required when status is Graduated)', max_length=10, null=True)),
                ('year_of_graduation_gregorian', models.DateField(blank=True, help_text='Year of graduation in Gregorian calendar (required when status is Graduated)', null=True)),
                ('payment_status', models.CharField(choices=[('Pending', 'Pending'), ('Initiated', 'Initiated'), ('Processing', 'Processing'), ('Completed', 'Completed'), ('Failed', 'Failed'), ('Verified', 'Verified'), ('Expired', 'Expired'), ('Refunded', 'Refunded'), ('Cancelled', 'Cancelled')], default='Pending', help_text='Payment status (backend-managed)', max_length=20)),
                ('transaction_id', models.CharField(default=alumni_applications.models.generate_transaction_id, help_text='Auto-generated 6-character transaction ID', max_length=6, unique=True)),
                ('application_status', models.CharField(choices=[('Pending', 'Pending'), ('On Review', 'On Review'), ('Processing', 'Processing'), ('Complete', 'Complete')], default='Pending', help_text='Application processing status (admin-editable)', max_length=20)),
                ('is_uog_destination', models.BooleanField(help_text='True for internal UoG destination, False for external')),
                ('order_type', models.CharField(blank=True, choices=[('Local', 'Local'), ('International', 'International'), ('Legal Delegate', 'Legal Delegate')], help_text='Order type (required when is_uog_destination=False)', max_length=20, null=True)),
                ('institution_name', models.CharField(blank=True, help_text='External institution name (required when is_uog_destination=False)', max_length=100, null=True)),
                ('country', models.CharField(blank=True, help_text='Country (required when is_uog_destination=False)', max_length=100, null=True)),
                ('institution_address', models.TextField(blank=True, help_text='Institution address (required when is_uog_destination=False)', max_length=500, null=True)),
                ('mailing_agent', models.CharField(blank=True, choices=[('Normal Postal', 'Normal Postal'), ('DHL', 'DHL'), ('SMS', 'SMS')], help_text='Mailing agent (required when is_uog_destination=False)', max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('college', models.ForeignKey(blank=True, help_text='Select college (if available in system)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form1', to='college.college')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='created_alumni_applications_form1', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, help_text='Select department (if available in system)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form1', to='department.department')),
                ('service_type', models.ForeignKey(help_text='Type of service requested', on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form1', to='service_type.servicetype')),
                ('uog_college', models.ForeignKey(blank=True, help_text='UoG college destination (required when is_uog_destination=True)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form1_destination', to='college.college')),
                ('uog_department', models.ForeignKey(blank=True, help_text='UoG department destination (required when is_uog_destination=True)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form1_destination', to='department.department')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='updated_alumni_applications_form1', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Student Application Form (Complete)',
                'verbose_name_plural': 'Student Application Forms (Complete)',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentApplicationFormSimplified',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('first_name', models.CharField(help_text='First name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='First name must contain only letters and spaces.', regex='^[a-zA-Z\\s]+$')])),
                ('father_name', models.CharField(help_text="Father's name (letters only)", max_length=50, validators=[django.core.validators.RegexValidator(message='Father name must contain only letters and spaces.', regex='^[a-zA-Z\\s]+$')])),
                ('last_name', models.CharField(help_text='Last name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='Last name must contain only letters and spaces.', regex='^[a-zA-Z\\s]+$')])),
                ('student_id', models.CharField(blank=True, help_text='Student ID format: uog/1254/21 (optional)', max_length=20, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='Student ID must be alphanumeric with forward slashes (e.g., uog/1254/21).', regex='^[a-zA-Z0-9/]+$')])),
                ('phone_number', models.CharField(help_text='International format with country code (e.g., +251912345678)', max_length=20, validators=[django.core.validators.RegexValidator(message='Phone number must be in international format with country code.', regex='^\\+?[1-9]\\d{1,14}$')])),
                ('email', models.EmailField(help_text='Valid email address', max_length=100, validators=[django.core.validators.EmailValidator()])),
                ('admission_type', models.CharField(choices=[('Regular', 'Regular'), ('Evening', 'Evening'), ('Summer', 'Summer'), ('Distance', 'Distance')], help_text='Type of admission', max_length=20)),
                ('degree_type', models.CharField(choices=[('Diploma', 'Diploma'), ('Degree', 'Degree'), ("Master's", "Master's"), ('PHD', 'PHD'), ('Sp.Certificate', 'Sp.Certificate')], help_text='Type of degree', max_length=20)),
                ('is_other_college', models.BooleanField(default=False, help_text='Check if college is not in the system')),
                ('other_college_name', models.CharField(blank=True, help_text='College name (if not in system)', max_length=100, null=True)),
                ('other_department_name', models.CharField(blank=True, help_text='Department name (if not in system)', max_length=100, null=True)),
                ('student_status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Graduated', 'Graduated')], help_text='Current student status', max_length=20)),
                ('current_year', models.CharField(blank=True, choices=[('1st year', '1st year'), ('2nd year', '2nd year'), ('3rd year', '3rd year'), ('4th year', '4th year'), ('5th year', '5th year'), ('6th year', '6th year'), ('7th year', '7th year'), ('8th year', '8th year')], help_text='Current year (required when status is Active)', max_length=20, null=True)),
                ('year_of_leaving_ethiopian', models.CharField(blank=True, help_text='Year of leaving in Ethiopian calendar (required when status is Inactive)', max_length=10, null=True)),
                ('year_of_leaving_gregorian', models.DateField(blank=True, help_text='Year of leaving in Gregorian calendar (required when status is Inactive)', null=True)),
                ('year_of_graduation_ethiopian', models.CharField(blank=True, help_text='Year of graduation in Ethiopian calendar (required when status is Graduated)', max_length=10, null=True)),
                ('year_of_graduation_gregorian', models.DateField(blank=True, help_text='Year of graduation in Gregorian calendar (required when status is Graduated)', null=True)),
                ('payment_status', models.CharField(choices=[('Pending', 'Pending'), ('Initiated', 'Initiated'), ('Processing', 'Processing'), ('Completed', 'Completed'), ('Failed', 'Failed'), ('Verified', 'Verified'), ('Expired', 'Expired'), ('Refunded', 'Refunded'), ('Cancelled', 'Cancelled')], default='Pending', help_text='Payment status (backend-managed)', max_length=20)),
                ('transaction_id', models.CharField(default=alumni_applications.models.generate_transaction_id, help_text='Auto-generated 6-character transaction ID', max_length=6, unique=True)),
                ('application_status', models.CharField(choices=[('Pending', 'Pending'), ('On Review', 'On Review'), ('Processing', 'Processing'), ('Complete', 'Complete')], default='Pending', help_text='Application processing status (admin-editable)', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('college', models.ForeignKey(blank=True, help_text='Select college (if available in system)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form2', to='college.college')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='created_alumni_applications_form2', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, help_text='Select department (if available in system)', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form2', to='department.department')),
                ('service_type', models.ForeignKey(help_text='Type of service requested', on_delete=django.db.models.deletion.PROTECT, related_name='alumni_applications_form2', to='service_type.servicetype')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='updated_alumni_applications_form2', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Student Application Form (Simplified)',
                'verbose_name_plural': 'Student Application Forms (Simplified)',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ApplicationDocument',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(help_text='Upload the required document (max 10MB)', upload_to=alumni_applications.models.application_document_upload_path)),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField(help_text='File size in bytes')),
                ('mime_type', models.CharField(max_length=100)),
                ('upload_timestamp', models.DateTimeField(auto_now_add=True)),
                ('document_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='application_documents', to='document_type.documenttype')),
                ('uploaded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='uploaded_application_documents', to=settings.AUTH_USER_MODEL)),
                ('application_form1', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='alumni_applications.studentapplicationform')),
                ('application_form2', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='alumni_applications.studentapplicationformsimplified')),
            ],
            options={
                'verbose_name': 'Application Document',
                'verbose_name_plural': 'Application Documents',
                'ordering': ['-upload_timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='studentapplicationform',
            index=models.Index(fields=['transaction_id'], name='alumni_appl_transac_c3c982_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationform',
            index=models.Index(fields=['email'], name='alumni_appl_email_254c90_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationform',
            index=models.Index(fields=['service_type'], name='alumni_appl_service_bb658c_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationform',
            index=models.Index(fields=['application_status'], name='alumni_appl_applica_3f15be_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationform',
            index=models.Index(fields=['payment_status'], name='alumni_appl_payment_c5df02_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationform',
            index=models.Index(fields=['created_at'], name='alumni_appl_created_b551b6_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationformsimplified',
            index=models.Index(fields=['transaction_id'], name='alumni_appl_transac_afe271_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationformsimplified',
            index=models.Index(fields=['email'], name='alumni_appl_email_20cdab_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationformsimplified',
            index=models.Index(fields=['service_type'], name='alumni_appl_service_d90e5b_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationformsimplified',
            index=models.Index(fields=['application_status'], name='alumni_appl_applica_e4b943_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationformsimplified',
            index=models.Index(fields=['payment_status'], name='alumni_appl_payment_8ecc61_idx'),
        ),
        migrations.AddIndex(
            model_name='studentapplicationformsimplified',
            index=models.Index(fields=['created_at'], name='alumni_appl_created_8cac67_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdocument',
            index=models.Index(fields=['application_form1', 'document_type'], name='alumni_appl_applica_d7b047_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdocument',
            index=models.Index(fields=['application_form2', 'document_type'], name='alumni_appl_applica_257a1b_idx'),
        ),
        migrations.AddIndex(
            model_name='applicationdocument',
            index=models.Index(fields=['upload_timestamp'], name='alumni_appl_upload__82d7c1_idx'),
        ),
        migrations.AddConstraint(
            model_name='applicationdocument',
            constraint=models.CheckConstraint(check=models.Q(('application_form1__isnull', False), ('application_form2__isnull', False), _connector='OR'), name='application_document_has_application'),
        ),
        migrations.AddConstraint(
            model_name='applicationdocument',
            constraint=models.CheckConstraint(check=models.Q(('application_form1__isnull', False), ('application_form2__isnull', False), _negated=True), name='application_document_single_application'),
        ),
    ]
