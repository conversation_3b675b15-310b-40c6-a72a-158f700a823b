// Service Request Types for Authenticated Users
export interface ServiceRequest {
  id: string;
  service_type: string;
  service_type_name: string;
  status: string;
  full_name: string;
  email: string;
  phone: string;
  student_id?: string;
  admission_type: string;
  admission_type_name: string;
  degree: string;
  degree_name: string;
  college: string;
  college_display: string;
  department: string;
  department_display: string;
  graduation_year: number;
  graduation_month: number;
  student_status: string;
  current_year?: number;
  year_type?: string;
  gpa?: number;
  mailing_destination: string;
  mailing_agent: string;
  mailing_address?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  created_by_name: string;
  is_deleted: boolean;
}

export interface ServiceRequestFormData {
  service_type: string;
  full_name: string;
  email: string;
  phone: string;
  student_id?: string;
  admission_type: string;
  degree: string;
  college: string;
  department: string;
  graduation_year: number;
  graduation_month: number;
  student_status: string;
  current_year?: number;
  year_type?: string;
  gpa?: number;
  mailing_destination: string;
  mailing_agent: string;
  mailing_address?: string;
}

export interface ServiceRequestFormErrors {
  service_type?: string;
  full_name?: string;
  email?: string;
  phone?: string;
  student_id?: string;
  admission_type?: string;
  degree?: string;
  college?: string;
  department?: string;
  graduation_year?: string;
  graduation_month?: string;
  student_status?: string;
  current_year?: string;
  year_type?: string;
  gpa?: string;
  mailing_destination?: string;
  mailing_agent?: string;
  mailing_address?: string;
}

export interface ServiceRequestListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ServiceRequest[];
}

export interface ServiceRequestFilters {
  name?: string;
  status?: string;
  service_type?: string;
  college?: string;
  department?: string;
  graduation_year?: number;
  created_at_after?: string;
  created_at_before?: string;
}

export interface ServiceRequestStatistics {
  total_requests: number;
  pending_requests: number;
  processing_requests: number;
  completed_requests: number;
  rejected_requests: number;
  requests_by_service_type: Array<{
    service_type: string;
    count: number;
  }>;
  requests_by_status: Array<{
    status: string;
    count: number;
  }>;
}

// Document Upload Types
export interface DocumentUpload {
  id: string;
  service_request: string;
  document_type: string;
  document_type_name: string;
  file: string;
  file_name: string;
  file_size: number;
  uploaded_at: string;
  is_verified: boolean;
  verified_by?: string;
  verified_at?: string;
  notes?: string;
}

// Lookup Types
export interface ServiceTypeLookup {
  id: string;
  name: string;
  description?: string;
  fee: string;
  is_active: boolean;
}

export interface AdmissionTypeLookup {
  id: string;
  name: string;
  is_active: boolean;
}

export interface StudyProgramLookup {
  id: string;
  program_name: string;
  college: string;
  college_name: string;
  department: string;
  department_name: string;
  is_active: boolean;
}

export interface CollegeLookup {
  id: string;
  name: string;
  is_active: boolean;
}

export interface DepartmentLookup {
  id: string;
  name: string;
  college: string;
  college_name: string;
  is_active: boolean;
}

export interface CertificateTypeLookup {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
}

// Status Options
export const STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending' },
  { value: 'processing', label: 'Processing' },
  { value: 'completed', label: 'Completed' },
  { value: 'rejected', label: 'Rejected' }
];

// Student Status Options
export const STUDENT_STATUS_OPTIONS = [
  { value: 'graduated', label: 'Graduated' },
  { value: 'active', label: 'Currently Enrolled' },
  { value: 'withdrawn', label: 'Withdrawn' },
  { value: 'transferred', label: 'Transferred' }
];

// Year Type Options
export const YEAR_TYPE_OPTIONS = [
  { value: '1st', label: '1st Year' },
  { value: '2nd', label: '2nd Year' },
  { value: '3rd', label: '3rd Year' },
  { value: '4th', label: '4th Year' },
  { value: '5th', label: '5th Year' },
  { value: '6th', label: '6th Year' }
];

// Mailing Destination Options
export const MAILING_DESTINATION_OPTIONS = [
  { value: 'ethiopia', label: 'Within Ethiopia' },
  { value: 'international', label: 'International' }
];

// Mailing Agent Options
export const MAILING_AGENT_OPTIONS = [
  { value: 'self', label: 'Pick up myself' },
  { value: 'family', label: 'Family member' },
  { value: 'friend', label: 'Friend' },
  { value: 'courier', label: 'Courier service' },
  { value: 'mail', label: 'Postal mail' }
];
