import csv
import io
from datetime import datetime, timedelta
from django.utils import timezone
from rest_framework import generics, filters, status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, IsAdminUser, AllowAny, AllowAny
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg, Min, Max
from django.db.models.functions import ExtractYear
from django.core.cache import cache
from django.db import connection

from .models import GraduateStudent, VerificationCollege, VerificationDepartment, VerificationFieldOfStudy, VerificationProgram, AdmissionClassification
from .serializers import (
    GraduateVerificationSerializer,
    GraduateVerificationDetailSerializer,
    VerificationCollegeSerializer,
    VerificationDepartmentSerializer,
    VerificationFieldOfStudySerializer,
    VerificationProgramSerializer,
    VerificationAdmissionClassificationSerializer
)

# Custom pagination for large datasets
class LargeDatasetPagination(PageNumberPagination):
    """Optimized pagination for handling 100,000+ records efficiently"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'total_pages': self.page.paginator.num_pages,
            'current_page': self.page.number,
            'page_size': self.get_page_size(self.request),
            'results': data
        })

# Graduate Verification views
class GraduateVerificationList(generics.ListCreateAPIView):
    serializer_class = GraduateVerificationSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = LargeDatasetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = {
        'college': ['exact'],
        'college_id': ['exact'],  # Add college_id for direct ID filtering
        'department': ['exact'],
        'department_id': ['exact'],  # Add department_id for direct ID filtering
        'field_of_study': ['exact'],
        'field_of_study_id': ['exact'],  # Add field_of_study_id for direct ID filtering
        'program': ['exact'],
        'program_id': ['exact'],  # Add program_id for direct ID filtering
        'admission_classification': ['exact'],
        'admission_classification_id': ['exact'],  # Add admission_classification_id for direct ID filtering
        'year_of_entry': ['exact'],
        'year_of_graduation': ['exact'],
        'gender': ['exact'],
        'first_name': ['exact', 'icontains'],
        'last_name': ['exact', 'icontains'],
        'middle_name': ['exact', 'icontains'],
        'student_id': ['exact', 'icontains'],
        'created_by': ['exact'],  # Filter by creator
        'updated_by': ['exact'],  # Filter by last updater
    }
    search_fields = ['student_id', 'first_name', 'middle_name', 'last_name']
    ordering_fields = ['year_of_graduation', 'last_name', 'first_name', 'gpa', 'created_at', 'updated_at']
    ordering = ['-year_of_graduation', 'last_name', 'first_name']  # Default ordering

    def get_queryset(self):
        """Optimized queryset with select_related for foreign keys"""
        try:
            return GraduateStudent.objects.select_related(
                'college', 'department', 'field_of_study', 'program', 'admission_classification',
                'created_by', 'updated_by'
            ).all()
        except Exception:
            # Fallback if audit trail fields don't exist yet
            return GraduateStudent.objects.select_related(
                'college', 'department', 'field_of_study', 'program', 'admission_classification'
            ).all()

    def perform_create(self, serializer):
        """Set the created_by and updated_by fields when creating a new graduate student"""
        try:
            serializer.save(created_by=self.request.user, updated_by=self.request.user)
        except Exception:
            # Fallback if audit trail fields don't exist yet
            serializer.save()

class GraduateVerificationDetail(generics.RetrieveUpdateAPIView):
    queryset = GraduateStudent.objects.all()
    serializer_class = GraduateVerificationDetailSerializer
    permission_classes = [IsAuthenticated]

    def perform_update(self, serializer):
        """Set the updated_by field when updating a graduate student"""
        try:
            serializer.save(updated_by=self.request.user)
        except Exception:
            # Fallback if audit trail fields don't exist yet
            serializer.save()

class GraduateVerificationDelete(generics.DestroyAPIView):
    queryset = GraduateStudent.objects.all_with_deleted()  # Allow access to all records for hard delete
    serializer_class = GraduateVerificationSerializer
    permission_classes = [IsAdminUser]


class GraduateVerificationSoftDelete(APIView):
    """Soft delete a graduate student record"""
    permission_classes = [IsAdminUser]

    def delete(self, request, pk):
        try:
            # Get the graduate using all_with_deleted to ensure we can access it
            try:
                graduate = GraduateStudent.objects.all_with_deleted().get(pk=pk)
            except:
                # Fallback to regular query if custom manager fails
                graduate = GraduateStudent.objects.get(pk=pk)

            # Try to use soft_delete method
            try:
                graduate.soft_delete(user=request.user)
                print(f"Soft deleted graduate {graduate.id} using method")
            except Exception as e:
                print(f"Soft delete method failed: {e}")
                # Fallback to manual soft delete if method fails
                try:
                    graduate.is_deleted = True
                    graduate.deleted_at = timezone.now()
                    graduate.deleted_by = request.user
                    graduate.save()
                    print(f"Soft deleted graduate {graduate.id} manually")
                except Exception as e2:
                    print(f"Manual soft delete failed: {e2}")
                    # If soft delete fields don't exist, just delete normally
                    graduate.delete()
                    return Response(
                        {'message': 'Graduate record deleted successfully (hard delete - soft delete not available)'},
                        status=status.HTTP_200_OK
                    )

            return Response(
                {'message': 'Graduate record soft deleted successfully'},
                status=status.HTTP_200_OK
            )
        except GraduateStudent.DoesNotExist:
            return Response(
                {'error': 'Graduate not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GraduateVerificationRestore(APIView):
    """Restore a soft-deleted graduate student record"""
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        try:
            # Try to get the deleted graduate
            try:
                graduate = GraduateStudent.objects.deleted_only().get(pk=pk)
            except:
                # Fallback to direct query
                try:
                    graduate = GraduateStudent.objects.filter(is_deleted=True).get(pk=pk)
                except:
                    # If soft delete fields don't exist, return error
                    return Response(
                        {'error': 'Soft delete functionality not available'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Try to restore using the method
            try:
                graduate.restore(user=request.user)
            except Exception:
                # Fallback to manual restore
                try:
                    graduate.is_deleted = False
                    graduate.deleted_at = None
                    graduate.deleted_by = None
                    graduate.updated_by = request.user
                    graduate.save()
                except Exception:
                    return Response(
                        {'error': 'Failed to restore graduate record'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            return Response(
                {'message': 'Graduate record restored successfully'},
                status=status.HTTP_200_OK
            )
        except GraduateStudent.DoesNotExist:
            return Response(
                {'error': 'Deleted graduate not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GraduateVerificationDeletedList(generics.ListAPIView):
    """List all soft-deleted graduate student records"""
    serializer_class = GraduateVerificationSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['college', 'department', 'field_of_study', 'program', 'admission_classification', 'year_of_entry', 'year_of_graduation', 'gender']
    search_fields = ['student_id', 'first_name', 'middle_name', 'last_name']
    ordering_fields = ['year_of_graduation', 'last_name', 'first_name', 'gpa', 'deleted_at']
    ordering = ['-deleted_at']

    def get_queryset(self):
        """Get only soft-deleted records"""
        try:
            # Use all_with_deleted() to access all records, then filter for deleted ones
            return GraduateStudent.objects.all_with_deleted().filter(is_deleted=True)
        except Exception as e:
            # Fallback to custom manager method
            try:
                return GraduateStudent.objects.deleted_only()
            except Exception:
                # If soft delete fields don't exist, return empty queryset
                return GraduateStudent.objects.none()

class PublicGraduateVerificationList(generics.ListAPIView):
    queryset = GraduateStudent.objects.all()
    serializer_class = GraduateVerificationDetailSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['college', 'department', 'field_of_study', 'program', 'admission_classification', 'year_of_entry', 'year_of_graduation', 'gender']
    search_fields = ['student_id', 'first_name', 'middle_name', 'last_name']
    ordering_fields = ['year_of_graduation', 'last_name', 'first_name', 'gpa']

    def get_queryset(self):
        # Get required filter parameters from request
        first_name = self.request.query_params.get('first_name', '').strip()
        last_name = self.request.query_params.get('last_name', '').strip()
        year_of_graduation = self.request.query_params.get('year_of_graduation')
        field_of_study = self.request.query_params.get('field_of_study')
        college = self.request.query_params.get('college')
        department = self.request.query_params.get('department')
        program = self.request.query_params.get('program')
        admission_classification = self.request.query_params.get('admission_classification')

        # Get either student_id or gpa
        student_id = self.request.query_params.get('student_id', '').strip()
        gpa = self.request.query_params.get('gpa', '').strip()

        # Validate required parameters
        missing_params = []

        # Check for first_name and last_name (both required)
        if not first_name:
            missing_params.append('first_name')
        if not last_name:
            missing_params.append('last_name')

        # Check for either student_id or gpa
        if not student_id and not gpa:
            missing_params.append('student_id or gpa')

        # Check for year_of_graduation
        if not year_of_graduation:
            missing_params.append('year_of_graduation')

        # Check for field_of_study
        if not field_of_study:
            missing_params.append('field_of_study')

        # These fields are no longer required for public verification
        # college, department, program, and admission_classification
        # have been removed from the form

        # If any required parameters are missing, return an empty queryset
        if missing_params:
            return GraduateStudent.objects.none()

        # Start with all graduate students
        queryset = GraduateStudent.objects.all()

        # Apply required filters with exact matching
        queryset = queryset.filter(
            first_name__iexact=first_name,
            last_name__iexact=last_name,
            year_of_graduation=year_of_graduation,
            field_of_study_id=field_of_study
        )
        if college:
            queryset = queryset.filter(college_id=college)
        if department:
            queryset = queryset.filter(department_id=department)
        if program:
            queryset = queryset.filter(program_id=program)
        if admission_classification:
            queryset = queryset.filter(admission_classification_id=admission_classification)

        # Apply either student_id or gpa filter with exact matching
        if student_id:
            queryset = queryset.filter(student_id__exact=student_id)
        elif gpa:
            try:
                gpa_value = float(gpa)
                queryset = queryset.filter(gpa=gpa_value)
            except ValueError:
                return GraduateStudent.objects.none()

        return queryset

class GraduateVerificationFilter(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        # Get required filter parameters from request
        first_name = request.query_params.get('first_name', '').strip()
        last_name = request.query_params.get('last_name', '').strip()
        year_of_graduation = request.query_params.get('year_of_graduation')
        field_of_study = request.query_params.get('field_of_study')
        college = request.query_params.get('college')
        department = request.query_params.get('department')
        program = request.query_params.get('program')
        admission_classification = request.query_params.get('admission_classification')

        # Get either student_id or gpa
        student_id = request.query_params.get('student_id', '').strip()
        gpa = request.query_params.get('gpa', '').strip()

        # Validate required parameters
        missing_params = []

        # Check for first_name and last_name (both required)
        if not first_name:
            missing_params.append('first_name')
        if not last_name:
            missing_params.append('last_name')

        # Check for either student_id or gpa
        if not student_id and not gpa:
            missing_params.append('student_id or gpa')

        # Check for year_of_graduation
        if not year_of_graduation:
            missing_params.append('year_of_graduation')

        # Check for field_of_study
        if not field_of_study:
            missing_params.append('field_of_study')

        # These fields are no longer required for public verification
        # college, department, program, and admission_classification
        # have been removed from the form

        # If any required parameters are missing, return an error
        if missing_params:
            return Response(
                {
                    'error': 'Missing required parameters',
                    'missing_params': missing_params
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Start with all graduate students
        queryset = GraduateStudent.objects.all()

        # Apply required filters with exact matching
        queryset = queryset.filter(
            first_name__iexact=first_name,
            last_name__iexact=last_name,
            year_of_graduation=year_of_graduation,
            field_of_study_id=field_of_study
        )
        if college:
            queryset = queryset.filter(college_id=college)
        if department:
            queryset = queryset.filter(department_id=department)
        if program:
            queryset = queryset.filter(program_id=program)
        if admission_classification:
            queryset = queryset.filter(admission_classification_id=admission_classification)

        # Apply either student_id or gpa filter with exact matching
        if student_id:
            queryset = queryset.filter(student_id__exact=student_id)
        elif gpa:
            try:
                gpa_value = float(gpa)
                queryset = queryset.filter(gpa=gpa_value)
            except ValueError:
                return Response(
                    {'error': 'Invalid GPA value. Must be a number between 2.0 and 4.0'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Serialize and return the filtered data with detailed information
        serializer = GraduateVerificationDetailSerializer(queryset, many=True)
        return Response(serializer.data)

# CSV Import View
class GraduateVerificationCSVImport(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        try:
            # Check if file is provided
            if 'file' not in request.FILES:
                return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)

            csv_file = request.FILES['file']

            # Check if file is CSV
            if not csv_file.name.endswith('.csv'):
                return Response({'error': 'File must be a CSV'}, status=status.HTTP_400_BAD_REQUEST)

            # Read CSV file
            decoded_file = csv_file.read().decode('utf-8')
            csv_data = csv.DictReader(io.StringIO(decoded_file))

            # Validate CSV headers - support both ID and name columns
            required_base_headers = [
                'student_id', 'first_name', 'last_name', 'year_of_entry', 'year_of_graduation',
                'gpa', 'gender'
            ]

            # For foreign keys, accept either ID or name columns
            foreign_key_mappings = {
                'college': ['college_id', 'college_name', 'college'],
                'department': ['department_id', 'department_name', 'department'],
                'field_of_study': ['field_of_study_id', 'field_of_study_name', 'field_of_study'],
                'program': ['program_id', 'program_name', 'program'],
                'admission_classification': ['admission_classification_id', 'admission_classification_name', 'admission_classification']
            }

            # Get the headers from the CSV file
            headers = csv_data.fieldnames

            # Check if all required base headers are present
            missing_headers = [header for header in required_base_headers if header not in headers]
            if missing_headers:
                return Response(
                    {'error': f'Missing required headers: {missing_headers}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if at least one column exists for each foreign key
            missing_foreign_keys = []
            for entity, possible_columns in foreign_key_mappings.items():
                if not any(col in headers for col in possible_columns):
                    missing_foreign_keys.append(f"{entity} (one of: {', '.join(possible_columns)})")

            if missing_foreign_keys:
                return Response(
                    {'error': f'Missing required foreign key columns: {missing_foreign_keys}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Helper function to resolve name to ID
            def resolve_foreign_key(row, entity_type, row_num):
                """
                Resolve foreign key value from either ID or name column
                Returns (id_value, error_message)
                """
                possible_columns = foreign_key_mappings[entity_type]

                # Try to find a value in any of the possible columns
                value = None
                column_used = None
                for col in possible_columns:
                    if col in row and row[col] and str(row[col]).strip():
                        value = str(row[col]).strip()
                        column_used = col
                        break

                if not value:
                    return None, f'Row {row_num}: No value provided for {entity_type}'

                # If it's an ID column, try to convert to int
                if column_used.endswith('_id'):
                    try:
                        return int(value), None
                    except (ValueError, TypeError):
                        return None, f'Row {row_num}: Invalid ID value "{value}" for {entity_type}'

                # If it's a name column, look up the ID
                try:
                    if entity_type == 'college':
                        obj = VerificationCollege.objects.get(name__iexact=value)
                        return obj.id, None
                    elif entity_type == 'department':
                        obj = VerificationDepartment.objects.get(name__iexact=value)
                        return obj.id, None
                    elif entity_type == 'field_of_study':
                        obj = VerificationFieldOfStudy.objects.get(name__iexact=value)
                        return obj.id, None
                    elif entity_type == 'program':
                        obj = VerificationProgram.objects.get(name__iexact=value)
                        return obj.id, None
                    elif entity_type == 'admission_classification':
                        obj = AdmissionClassification.objects.get(name__iexact=value)
                        return obj.id, None
                except Exception:
                    return None, f'Row {row_num}: {entity_type.replace("_", " ").title()} "{value}" not found in database'

                return None, f'Row {row_num}: Unable to resolve {entity_type} value "{value}"'

            # Process CSV data
            graduates_created = 0
            graduates_updated = 0
            errors = []

            for row_num, row in enumerate(csv_data, start=2):  # Start from 2 to account for header row
                try:
                    # Get or create graduate student
                    student_id = row['student_id']
                    if not student_id:
                        errors.append(f'Row {row_num}: Student ID is required')
                        continue

                    # Try to find existing student
                    try:
                        graduate = GraduateStudent.objects.get(student_id=student_id)
                        is_new = False
                    except GraduateStudent.DoesNotExist:
                        graduate = GraduateStudent(student_id=student_id)
                        is_new = True

                    # Update fields
                    graduate.first_name = row['first_name']
                    graduate.last_name = row['last_name']

                    # Optional fields
                    if 'middle_name' in row and row['middle_name']:
                        graduate.middle_name = row['middle_name']

                    # Required fields with validation
                    try:
                        graduate.year_of_entry = int(row['year_of_entry'])
                        graduate.year_of_graduation = int(row['year_of_graduation'])
                        graduate.gpa = float(row['gpa'])

                        # Validate GPA range
                        if graduate.gpa < 2.0 or graduate.gpa > 4.0:
                            errors.append(f'Row {row_num}: GPA must be between 2.0 and 4.0')
                            continue

                        # Validate gender
                        if row['gender'] not in ['Male', 'Female']:
                            errors.append(f'Row {row_num}: Gender must be either Male or Female')
                            continue
                        graduate.gender = row['gender']

                        # Foreign keys - resolve from either ID or name
                        foreign_key_errors = []

                        # Resolve college
                        college_id, error = resolve_foreign_key(row, 'college', row_num)
                        if error:
                            foreign_key_errors.append(error)
                        else:
                            graduate.college_id = college_id

                        # Resolve department
                        department_id, error = resolve_foreign_key(row, 'department', row_num)
                        if error:
                            foreign_key_errors.append(error)
                        else:
                            graduate.department_id = department_id

                        # Resolve field of study
                        field_of_study_id, error = resolve_foreign_key(row, 'field_of_study', row_num)
                        if error:
                            foreign_key_errors.append(error)
                        else:
                            graduate.field_of_study_id = field_of_study_id

                        # Resolve program
                        program_id, error = resolve_foreign_key(row, 'program', row_num)
                        if error:
                            foreign_key_errors.append(error)
                        else:
                            graduate.program_id = program_id

                        # Resolve admission classification
                        admission_classification_id, error = resolve_foreign_key(row, 'admission_classification', row_num)
                        if error:
                            foreign_key_errors.append(error)
                        else:
                            graduate.admission_classification_id = admission_classification_id

                        # If there were foreign key errors, skip this row
                        if foreign_key_errors:
                            errors.extend(foreign_key_errors)
                            continue

                        # Set audit trail fields (allow null for CSV imports)
                        try:
                            if is_new:
                                # For new records from CSV, set created_by only if user is available
                                # This allows CSV imports to have null audit trail fields
                                if hasattr(graduate, 'created_by'):
                                    graduate.created_by = request.user if request.user.is_authenticated else None
                                if hasattr(graduate, 'updated_by'):
                                    graduate.updated_by = request.user if request.user.is_authenticated else None
                            else:
                                # For updates, only set updated_by
                                if hasattr(graduate, 'updated_by'):
                                    graduate.updated_by = request.user if request.user.is_authenticated else None
                        except Exception:
                            # Ignore if audit trail fields don't exist yet
                            pass

                        # Save the graduate
                        graduate.save()

                        if is_new:
                            graduates_created += 1
                        else:
                            graduates_updated += 1

                    except (ValueError, TypeError):
                        errors.append(f'Row {row_num}: Invalid value for year_of_graduation or gpa')
                        continue

                except Exception as e:
                    errors.append(f'Row {row_num}: {str(e)}')

            # Return results
            return Response({
                'success': True,
                'created': graduates_created,
                'updated': graduates_updated,
                'errors': errors
            })

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Report Views
class GraduateVerificationReportByDepartment(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get department counts
            department_counts = GraduateStudent.objects.values(
                'department__name'
            ).annotate(
                count=Count('id')
            ).order_by('-count')

            # Format for chart
            labels = [item['department__name'] for item in department_counts]
            data = [item['count'] for item in department_counts]

            return Response({
                'labels': labels,
                'data': data
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GraduateVerificationReportByCollege(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get college counts with both name and code
            college_counts = GraduateStudent.objects.values(
                'college__name', 'college__code'
            ).annotate(
                count=Count('id')
            ).order_by('-count')

            # Format for chart - use code instead of name
            labels = [item['college__code'] for item in college_counts]
            data = [item['count'] for item in college_counts]

            # Also include the full data for reference
            colleges_data = [
                {
                    'code': item['college__code'],
                    'name': item['college__name'],
                    'count': item['count']
                } for item in college_counts
            ]

            return Response({
                'labels': labels,
                'data': data,
                'colleges_data': colleges_data
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GraduateVerificationReportByGender(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get gender counts
            gender_counts = GraduateStudent.objects.values(
                'gender'
            ).annotate(
                count=Count('id')
            ).order_by('gender')

            # Format for chart
            labels = [item['gender'] for item in gender_counts]
            data = [item['count'] for item in gender_counts]

            return Response({
                'labels': labels,
                'data': data
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GraduateVerificationReportByYear(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get year counts
            year_counts = GraduateStudent.objects.values(
                'year_of_graduation'
            ).annotate(
                count=Count('id')
            ).order_by('year_of_graduation')

            # Format for chart
            labels = [str(item['year_of_graduation']) for item in year_counts]
            data = [item['count'] for item in year_counts]

            return Response({
                'labels': labels,
                'data': data
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GraduateVerificationGenderTrendsByYear(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get all graduation years
            years = GraduateStudent.objects.values_list(
                'year_of_graduation', flat=True
            ).distinct().order_by('year_of_graduation')

            # Define the genders we want to track
            genders = ['Male', 'Female']

            # Get gender trends by year
            gender_trends = []

            for year in years:
                year_data = {'year': str(year)}

                # Get counts for each gender in this year
                for gender in genders:
                    count = GraduateStudent.objects.filter(
                        year_of_graduation=year,
                        gender=gender
                    ).count()

                    year_data[gender] = count

                # Add total for this year
                year_data['total'] = GraduateStudent.objects.filter(
                    year_of_graduation=year
                ).count()

                gender_trends.append(year_data)

            return Response({
                'gender_trends': gender_trends
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class GraduateVerificationProgramGPATrends(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get top programs by graduate count
            top_programs = GraduateStudent.objects.values(
                'program__name'
            ).annotate(
                count=Count('id')
            ).filter(
                count__gte=1  # Minimum 1 graduate (reduced for testing)
            ).order_by('-count')[:6]  # Top 6 programs

            program_names = [p['program__name'] for p in top_programs]

            # Get all graduation years (limit to last 10 years)
            current_year = datetime.now().year
            min_year = current_year - 9  # Last 10 years including current year

            years = GraduateStudent.objects.values_list(
                'year_of_graduation', flat=True
            ).filter(
                year_of_graduation__gte=min_year
            ).distinct().order_by('year_of_graduation')

            # Get GPA trends by program and year
            program_trends = []

            for year in years:
                year_data = {'year': str(year)}

                for program_name in program_names:
                    avg_gpa = GraduateStudent.objects.filter(
                        year_of_graduation=year,
                        program__name=program_name
                    ).aggregate(avg_gpa=Avg('gpa'))['avg_gpa']

                    year_data[program_name] = round(avg_gpa, 2) if avg_gpa else None

                program_trends.append(year_data)

            return Response({
                'program_trends': program_trends,
                'programs': program_names
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GraduateVerificationReportByProgram(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get program counts
            program_counts = GraduateStudent.objects.values(
                'program__name'
            ).annotate(
                count=Count('id')
            ).order_by('-count')

            # Format for chart
            labels = [item['program__name'] for item in program_counts]
            data = [item['count'] for item in program_counts]

            return Response({
                'labels': labels,
                'data': data
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GraduateVerificationReportByAdmissionClassification(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get admission classification counts
            classification_counts = GraduateStudent.objects.values(
                'admission_classification__name'
            ).annotate(
                count=Count('id')
            ).order_by('-count')

            # Format for chart
            labels = [item['admission_classification__name'] for item in classification_counts]
            data = [item['count'] for item in classification_counts]

            # Get gender distribution for each admission classification
            gender_distribution = []
            for classification in classification_counts:
                classification_name = classification['admission_classification__name']
                gender_counts = GraduateStudent.objects.filter(
                    admission_classification__name=classification_name
                ).values(
                    'gender'
                ).annotate(
                    count=Count('id')
                ).order_by('gender')

                gender_data = {}
                for gender_item in gender_counts:
                    gender_data[gender_item['gender']] = gender_item['count']

                gender_distribution.append({
                    'classification': classification_name,
                    'total': classification['count'],
                    'male': gender_data.get('Male', 0),
                    'female': gender_data.get('Female', 0)
                })

            return Response({
                'labels': labels,
                'data': data,
                'gender_distribution': gender_distribution
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# GPA Statistics Report
class GraduateVerificationReportGPAStats(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get current year
            current_year = datetime.now().year

            # System-wide average GPA
            system_avg_gpa = GraduateStudent.objects.aggregate(avg_gpa=Avg('gpa'))['avg_gpa']

            # Gender-based average GPA
            gender_gpa = GraduateStudent.objects.values(
                'gender'
            ).annotate(
                avg_gpa=Avg('gpa'),
                count=Count('id')
            ).order_by('gender')

            # Gender-based GPA distribution for current year
            gpa_ranges = [
                {'min': 2.0, 'max': 2.5, 'label': '2.0-2.5'},
                {'min': 2.5, 'max': 3.0, 'label': '2.5-3.0'},
                {'min': 3.0, 'max': 3.5, 'label': '3.0-3.5'},
                {'min': 3.5, 'max': 4.01, 'label': '3.5-4.0'},
            ]

            gender_gpa_distribution = []
            genders = ['Male', 'Female']

            for gender in genders:
                gender_data = []
                for range_info in gpa_ranges:
                    count = GraduateStudent.objects.filter(
                        gender=gender,
                        year_of_graduation=current_year,
                        gpa__gte=range_info['min'],
                        gpa__lt=range_info['max']
                    ).count()

                    gender_data.append({
                        'range': range_info['label'],
                        'count': count,
                        'gender': gender
                    })

                gender_gpa_distribution.append({
                    'gender': gender,
                    'data': gender_data
                })

            # College-wide average GPA
            college_gpa = GraduateStudent.objects.values(
                'college__name'
            ).annotate(
                avg_gpa=Avg('gpa'),
                count=Count('id')
            ).filter(
                college__name__isnull=False,  # Ensure college name exists
                gpa__isnull=False,  # Ensure GPA exists
                count__gte=1  # At least 1 graduate (reduced from 5 for testing)
            ).order_by('-avg_gpa')

            # Department-wide average GPA
            department_gpa = GraduateStudent.objects.values(
                'department__name',
                'college__name'
            ).annotate(
                avg_gpa=Avg('gpa'),
                count=Count('id')
            ).filter(
                department__name__isnull=False,  # Ensure department name exists
                college__name__isnull=False,  # Ensure college name exists
                gpa__isnull=False,  # Ensure GPA exists
                count__gte=1  # At least 1 graduate (reduced from 5 for testing)
            ).order_by('-avg_gpa')

            # GPA distribution
            total_graduates = GraduateStudent.objects.count()
            gpa_ranges = [
                {'min': 2.0, 'max': 2.5, 'label': '2.0-2.5'},
                {'min': 2.5, 'max': 3.0, 'label': '2.5-3.0'},
                {'min': 3.0, 'max': 3.5, 'label': '3.0-3.5'},
                {'min': 3.5, 'max': 4.01, 'label': '3.5-4.0'},
            ]

            gpa_distribution = []
            for range_info in gpa_ranges:
                count = GraduateStudent.objects.filter(
                    gpa__gte=range_info['min'],
                    gpa__lt=range_info['max']
                ).count()

                percentage = (count / total_graduates) * 100 if total_graduates > 0 else 0

                gpa_distribution.append({
                    'range': range_info['label'],
                    'count': count,
                    'percentage': round(percentage, 2)
                })

            return Response({
                'system_avg_gpa': round(system_avg_gpa, 2) if system_avg_gpa else 0,
                'gender_gpa': gender_gpa,
                'college_gpa': college_gpa,
                'department_gpa': department_gpa,
                'gpa_distribution': gpa_distribution,
                'gender_gpa_distribution': gender_gpa_distribution,
                'current_year': current_year,
                'total_graduates': total_graduates
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeletedGraduateStatisticsView(APIView):
    """API endpoint for deleted graduate statistics"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get deleted graduates
            deleted_graduates = GraduateStudent.objects.all_with_deleted().filter(is_deleted=True)

            # Basic counts
            total_deleted = deleted_graduates.count()

            # Deleted by time periods
            now = timezone.now()
            today = now.replace(hour=0, minute=0, second=0, microsecond=0)

            deleted_today = deleted_graduates.filter(deleted_at__gte=today).count()
            deleted_last_7_days = deleted_graduates.filter(deleted_at__gte=now - timedelta(days=7)).count()
            deleted_last_30_days = deleted_graduates.filter(deleted_at__gte=now - timedelta(days=30)).count()
            deleted_last_90_days = deleted_graduates.filter(deleted_at__gte=now - timedelta(days=90)).count()

            # Deleted by college
            deleted_by_college = deleted_graduates.values('college__name').annotate(
                count=Count('id')
            ).order_by('-count')[:10]

            # Deleted by department
            deleted_by_department = deleted_graduates.values('department__name').annotate(
                count=Count('id')
            ).order_by('-count')[:10]

            # Deleted by year of graduation
            deleted_by_year = deleted_graduates.values('year_of_graduation').annotate(
                count=Count('id')
            ).order_by('-year_of_graduation')[:10]

            # Deleted by gender
            deleted_by_gender = deleted_graduates.values('gender').annotate(
                count=Count('id')
            ).order_by('-count')

            # Recent deletions (last 10)
            recent_deletions = deleted_graduates.order_by('-deleted_at')[:10]
            recent_deletions_data = []
            for grad in recent_deletions:
                recent_deletions_data.append({
                    'id': grad.id,
                    'full_name': grad.get_full_name(),
                    'student_id': grad.student_id,
                    'college_name': grad.college.name if grad.college else None,
                    'department_name': grad.department.name if grad.department else None,
                    'year_of_graduation': grad.year_of_graduation,
                    'deleted_at': grad.deleted_at,
                    'deleted_by_name': grad.deleted_by.get_full_name() if grad.deleted_by else None,
                })

            return Response({
                'total_deleted': total_deleted,
                'time_periods': {
                    'today': deleted_today,
                    'last_7_days': deleted_last_7_days,
                    'last_30_days': deleted_last_30_days,
                    'last_90_days': deleted_last_90_days,
                },
                'by_college': list(deleted_by_college),
                'by_department': list(deleted_by_department),
                'by_year': list(deleted_by_year),
                'by_gender': list(deleted_by_gender),
                'recent_deletions': recent_deletions_data,
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Debug endpoint to check graduate student data
class GraduateStudentDebug(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get basic counts
            total_students = GraduateStudent.objects.count()
            students_with_gpa = GraduateStudent.objects.filter(gpa__isnull=False).count()
            students_with_college = GraduateStudent.objects.filter(college__isnull=False).count()

            # Get sample data
            sample_students = GraduateStudent.objects.select_related('college').all()[:5]
            sample_data = []
            for student in sample_students:
                sample_data.append({
                    'student_id': student.student_id,
                    'name': student.get_full_name(),
                    'gpa': float(student.gpa) if student.gpa else None,
                    'college': student.college.name if student.college else None,
                    'year_of_graduation': student.year_of_graduation
                })

            # Get college GPA data
            college_gpa_raw = GraduateStudent.objects.values(
                'college__name'
            ).annotate(
                avg_gpa=Avg('gpa'),
                count=Count('id')
            ).filter(
                college__name__isnull=False,
                gpa__isnull=False,
                count__gte=1
            ).order_by('-avg_gpa')

            return Response({
                'total_students': total_students,
                'students_with_gpa': students_with_gpa,
                'students_with_college': students_with_college,
                'sample_students': sample_data,
                'college_gpa_data': list(college_gpa_raw),
                'colleges_count': VerificationCollege.objects.count(),
                'departments_count': VerificationDepartment.objects.count()
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# College views
class CollegeList(generics.ListCreateAPIView):
    queryset = VerificationCollege.objects.all()
    serializer_class = VerificationCollegeSerializer
    permission_classes = [IsAuthenticated]

# Public College view
class PublicCollegeList(generics.ListAPIView):
    queryset = VerificationCollege.objects.all()
    serializer_class = VerificationCollegeSerializer
    permission_classes = [AllowAny]

class CollegeDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = VerificationCollege.objects.all()
    serializer_class = VerificationCollegeSerializer
    permission_classes = [IsAuthenticated]

# Department views
class DepartmentList(generics.ListCreateAPIView):
    queryset = VerificationDepartment.objects.all()
    serializer_class = VerificationDepartmentSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['college']

# Public Department view
class PublicDepartmentList(generics.ListAPIView):
    queryset = VerificationDepartment.objects.all()
    serializer_class = VerificationDepartmentSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['college']

class DepartmentDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = VerificationDepartment.objects.all()
    serializer_class = VerificationDepartmentSerializer
    permission_classes = [IsAuthenticated]

# Field of Study views
class FieldOfStudyList(generics.ListCreateAPIView):
    queryset = VerificationFieldOfStudy.objects.all()
    serializer_class = VerificationFieldOfStudySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['department', 'department__college']

# Public Field of Study view
class PublicFieldOfStudyList(generics.ListAPIView):
    queryset = VerificationFieldOfStudy.objects.all()
    serializer_class = VerificationFieldOfStudySerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['department', 'department__college']

class FieldOfStudyDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = VerificationFieldOfStudy.objects.all()
    serializer_class = VerificationFieldOfStudySerializer
    permission_classes = [IsAuthenticated]

# Program views
class ProgramList(generics.ListCreateAPIView):
    queryset = VerificationProgram.objects.all()
    serializer_class = VerificationProgramSerializer
    permission_classes = [IsAuthenticated]

# Public Program view
class PublicProgramList(generics.ListAPIView):
    queryset = VerificationProgram.objects.all()
    serializer_class = VerificationProgramSerializer
    permission_classes = [AllowAny]

class ProgramDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = VerificationProgram.objects.all()
    serializer_class = VerificationProgramSerializer
    permission_classes = [IsAuthenticated]

# Admission Classification views
class AdmissionClassificationList(generics.ListCreateAPIView):
    queryset = AdmissionClassification.objects.all()
    serializer_class = VerificationAdmissionClassificationSerializer
    permission_classes = [IsAuthenticated]

# Public Admission Classification view
class PublicAdmissionClassificationList(generics.ListAPIView):
    queryset = AdmissionClassification.objects.all()
    serializer_class = VerificationAdmissionClassificationSerializer
    permission_classes = [AllowAny]

class AdmissionClassificationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = AdmissionClassification.objects.all()
    serializer_class = VerificationAdmissionClassificationSerializer
    permission_classes = [IsAuthenticated]

class RecentGraduatesView(APIView):
    """
    API endpoint to get recently added graduates with different time periods
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Get the time period from query parameters (default to 7 days)
            period = request.query_params.get('period', '7')

            # Calculate the date threshold based on period
            now = timezone.now()
            if period == '1':
                threshold = now - timedelta(days=1)
                period_name = 'Today'
            elif period == '3':
                threshold = now - timedelta(days=3)
                period_name = 'Last 3 Days'
            elif period == '7':
                threshold = now - timedelta(days=7)
                period_name = 'Last 7 Days'
            elif period == '15':
                threshold = now - timedelta(days=15)
                period_name = 'Last 15 Days'
            elif period == '30':
                threshold = now - timedelta(days=30)
                period_name = 'Last 30 Days'
            else:
                threshold = now - timedelta(days=7)
                period_name = 'Last 7 Days'

            # Get recent graduates with audit trail fields if available
            try:
                recent_graduates = GraduateStudent.objects.filter(
                    created_at__gte=threshold
                ).select_related(
                    'college', 'department', 'field_of_study', 'program',
                    'admission_classification', 'created_by', 'updated_by'
                ).order_by('-created_at')
            except Exception:
                # Fallback if audit trail fields don't exist yet
                recent_graduates = GraduateStudent.objects.filter(
                    created_at__gte=threshold
                ).select_related(
                    'college', 'department', 'field_of_study', 'program',
                    'admission_classification'
                ).order_by('-created_at')

            # Prepare graduate data manually to avoid serializer issues
            graduates_data = []
            for graduate in recent_graduates:
                graduate_data = {
                    'id': graduate.id,
                    'student_id': graduate.student_id,
                    'first_name': graduate.first_name,
                    'middle_name': graduate.middle_name,
                    'last_name': graduate.last_name,
                    'full_name': graduate.get_full_name(),
                    'gender': graduate.gender,
                    'year_of_graduation': graduate.year_of_graduation,
                    'gpa': str(graduate.gpa),
                    'created_at': graduate.created_at.isoformat() if graduate.created_at else None,
                    'updated_at': graduate.updated_at.isoformat() if graduate.updated_at else None,
                    'college_name': graduate.college.name if graduate.college else None,
                    'department_name': graduate.department.name if graduate.department else None,
                    'field_of_study_name': graduate.field_of_study.name if graduate.field_of_study else None,
                    'program_name': graduate.program.name if graduate.program else None,
                    'admission_classification_name': graduate.admission_classification.name if graduate.admission_classification else None,
                }

                # Try to add audit trail fields if they exist
                try:
                    if hasattr(graduate, 'created_by') and graduate.created_by:
                        graduate_data['created_by_name'] = f"{graduate.created_by.first_name} {graduate.created_by.last_name}".strip() or graduate.created_by.username
                    else:
                        graduate_data['created_by_name'] = None

                    if hasattr(graduate, 'updated_by') and graduate.updated_by:
                        graduate_data['updated_by_name'] = f"{graduate.updated_by.first_name} {graduate.updated_by.last_name}".strip() or graduate.updated_by.username
                    else:
                        graduate_data['updated_by_name'] = None
                except (AttributeError, Exception):
                    graduate_data['created_by_name'] = None
                    graduate_data['updated_by_name'] = None

                graduates_data.append(graduate_data)

            # Get counts for different periods
            counts = {
                'today': GraduateStudent.objects.filter(
                    created_at__gte=now - timedelta(days=1)
                ).count(),
                'last_3_days': GraduateStudent.objects.filter(
                    created_at__gte=now - timedelta(days=3)
                ).count(),
                'last_7_days': GraduateStudent.objects.filter(
                    created_at__gte=now - timedelta(days=7)
                ).count(),
                'last_15_days': GraduateStudent.objects.filter(
                    created_at__gte=now - timedelta(days=15)
                ).count(),
                'last_30_days': GraduateStudent.objects.filter(
                    created_at__gte=now - timedelta(days=30)
                ).count(),
            }

            return Response({
                'period': period,
                'period_name': period_name,
                'count': len(graduates_data),
                'counts': counts,
                'graduates': graduates_data
            })

        except Exception as e:
            import traceback
            error_details = {
                'error': str(e),
                'traceback': traceback.format_exc(),
                'type': type(e).__name__
            }
            return Response(error_details, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AvailableYearsView(APIView):
    """
    API endpoint to get available graduation years for filtering
    """
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # Get distinct graduation years from graduate students
            years = GraduateStudent.objects.values_list(
                'year_of_graduation', flat=True
            ).distinct().order_by('year_of_graduation')

            # Filter out None values and convert to list
            available_years = [year for year in years if year is not None]

            return Response({
                'years': available_years,
                'count': len(available_years)
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AvailableCollegesView(APIView):
    """
    API endpoint to get available colleges for filtering
    """
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # Get colleges that have graduates
            colleges_with_graduates = GraduateStudent.objects.values(
                'college__id', 'college__name'
            ).distinct().filter(
                college__isnull=False
            ).order_by('college__name')

            # Format the response
            colleges = []
            for college in colleges_with_graduates:
                if college['college__name']:  # Ensure name is not None
                    colleges.append({
                        'id': college['college__id'],
                        'name': college['college__name']
                    })

            return Response({
                'colleges': colleges,
                'count': len(colleges)
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

