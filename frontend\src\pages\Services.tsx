import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Layout from '@/components/Layout';
import DocumentTitle from '@/components/DocumentTitle';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  FileText,
  Search,
  ExternalLink,
  GraduationCap,
  Award,
  BookOpen,
  Loader2
} from 'lucide-react';
import { serviceTypeAPI } from '@/services/api';
import { toast } from 'sonner';

interface ServiceType {
  id: string;
  name: string;
  description?: string;
  fee: string;
  url?: string;
  is_active: boolean;
  document_types_count: number;
  active_document_types_count: number;
}

const Services: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [services, setServices] = useState<ServiceType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredServices, setFilteredServices] = useState<ServiceType[]>([]);

  // Custom pattern background for hero section
  const heroPatternImage = '/images/university-pattern.svg';

  // State for organization name
  const [orgName, setOrgName] = useState<string>('University of Gondar');

  // Show success message if redirected from application submission
  useEffect(() => {
    if (searchParams.get('success') === 'true') {
      toast.success('Application submitted successfully! We will process your request and contact you soon.');
    }
  }, [searchParams]);

  // Fetch services
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const response = await serviceTypeAPI.getPublicServiceTypes();
        setServices(response.data);
        setFilteredServices(response.data);
      } catch (error) {
        console.error('Error fetching services:', error);
        toast.error('Failed to load services');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Filter services based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredServices(services);
    } else {
      const filtered = services.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredServices(filtered);
    }
  }, [searchTerm, services]);

  // Handle service application
  const handleApplyForService = (service: ServiceType) => {
    console.log('Applying for service:', service.name, 'URL:', service.url);

    // Create the service type parameter to pass to the form
    const serviceTypeParam = `service_type=${service.id}`;

    // Use the service type's URL to determine which form to use
    if (service.url && service.url.trim()) {
      const url = service.url.trim();

      // Check if URL contains form parameters
      if (url.includes('form=form1') || url.includes('form=complete')) {
        // URL indicates Form1 (Complete Application)
        console.log('Routing to Form1 (Complete Application) with pre-selected service:', service.name);
        navigate(`/alumni-application?form=form1&${serviceTypeParam}`);
      } else if (url.includes('form=form2') || url.includes('form=simplified')) {
        // URL indicates Form2 (Simplified Application)
        console.log('Routing to Form2 (Simplified Application) with pre-selected service:', service.name);
        navigate(`/alumni-application?form=form2&${serviceTypeParam}`);
      } else {
        // URL doesn't specify form type, try to parse and navigate
        try {
          const parsedUrl = new URL(url);

          // Check if it's an internal URL (same origin)
          if (parsedUrl.origin === window.location.origin) {
            // Internal URL - add service type parameter if it's an alumni application URL
            if (parsedUrl.pathname.includes('alumni-application')) {
              const existingParams = parsedUrl.search;
              const separator = existingParams ? '&' : '?';
              const fullUrl = parsedUrl.pathname + existingParams + separator + serviceTypeParam;
              console.log('Navigating to internal alumni application URL with service type:', fullUrl);
              navigate(fullUrl);
            } else {
              // Other internal URL - navigate directly
              console.log('Navigating to internal URL:', parsedUrl.pathname + parsedUrl.search);
              navigate(parsedUrl.pathname + parsedUrl.search);
            }
          } else {
            // External URL - open in new tab
            console.log('Opening external URL in new tab:', url);
            window.open(url, '_blank');
          }
        } catch (error) {
          // Invalid URL format, try relative URL navigation
          console.log('Invalid URL format, trying relative navigation:', url);
          if (url.startsWith('/')) {
            // Add service type parameter if it's an alumni application URL
            if (url.includes('alumni-application')) {
              const separator = url.includes('?') ? '&' : '?';
              const fullUrl = url + separator + serviceTypeParam;
              console.log('Navigating to relative alumni application URL with service type:', fullUrl);
              navigate(fullUrl);
            } else {
              navigate(url);
            }
          } else {
            // Default to simplified form if URL is not parseable
            console.log('Defaulting to Form2 due to unparseable URL with pre-selected service:', service.name);
            navigate(`/alumni-application?form=form2&${serviceTypeParam}`);
          }
        }
      }
    } else {
      // No URL specified, default to simplified form with pre-selected service
      console.log('No URL specified, defaulting to Form2 with pre-selected service:', service.name);
      navigate(`/alumni-application?form=form2&${serviceTypeParam}`);
    }
  };

  // Get service icon based on service name
  const getServiceIcon = (serviceName: string) => {
    const name = serviceName.toLowerCase();
    if (name.includes('transcript')) return <FileText className="h-6 w-6" />;
    if (name.includes('certificate') || name.includes('degree')) return <Award className="h-6 w-6" />;
    if (name.includes('verification')) return <BookOpen className="h-6 w-6" />;
    return <GraduationCap className="h-6 w-6" />;
  };

  if (loading) {
    return (
      <Layout>
        <DocumentTitle pageTitle="Services" />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#1a73c0]" />
              <p className="text-gray-600">Loading services...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <DocumentTitle pageTitle="Services" />
      {/* Hero Section with Animated Patterned Background - Half Height */}
      <section className="relative py-4 md:py-6 h-[175px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          <h1 className="text-xl md:text-2xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
            {orgName} Services
          </h1>
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Explore our comprehensive range of academic services designed to support your educational journey.
            Apply for transcripts, certificates, and other essential academic documents.
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">

        {/* Enhanced Search Section */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="text-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Find Your Service</h2>
              <p className="text-sm text-gray-600">Search through our available academic services</p>
            </div>
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[#1a73c0] h-5 w-5" />
              <Input
                type="text"
                placeholder="Search for transcripts, certificates, verification..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-4 h-14 border-2 border-gray-200 focus:border-[#1a73c0] focus:ring-2 focus:ring-[#1a73c0]/20 rounded-xl text-base placeholder:text-gray-400 transition-all duration-200"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  ✕
                </button>
              )}
            </div>
            {searchTerm && (
              <div className="mt-3 text-sm text-gray-600 text-center">
                {filteredServices.length} service{filteredServices.length !== 1 ? 's' : ''} found
              </div>
            )}
          </div>
        </div>

        {/* Services Grid */}
        {filteredServices.length === 0 ? (
          <div className="text-center py-16">
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-12 max-w-md mx-auto">
              <div className="bg-gradient-to-br from-[#1a73c0] to-[#155a9c] p-4 rounded-2xl w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <GraduationCap className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                {searchTerm ? 'No services found' : 'No services available'}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {searchTerm
                  ? 'Try adjusting your search terms or browse all available services.'
                  : 'Our academic services will be available soon. Please check back later.'
                }
              </p>
              {searchTerm && (
                <Button
                  onClick={() => setSearchTerm('')}
                  variant="outline"
                  className="mt-6 border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0] hover:text-white transition-all duration-200"
                >
                  Clear Search
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredServices.map((service) => (
              <Card key={service.id} className="group hover:shadow-2xl transition-all duration-300 border-0 bg-white rounded-2xl overflow-hidden transform hover:-translate-y-2 hover:scale-105 flex flex-col h-full">
                {/* Card Header with Gradient Background */}
                <div className="bg-gradient-to-br from-[#1a73c0] to-[#155a9c] p-6 text-white relative overflow-hidden">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute inset-0 bg-white transform rotate-12 scale-150"></div>
                  </div>

                  <div className="relative z-10 flex items-center space-x-4">
                    <div className="p-3 bg-white bg-opacity-20 rounded-xl backdrop-blur-sm">
                      <div className="text-white">
                        {getServiceIcon(service.name)}
                      </div>
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-xl font-bold text-white leading-tight mb-1">
                        {service.name}
                      </CardTitle>
                      <div className="flex items-center space-x-2">
                        <span className="text-green-200 font-semibold">{service.fee} ETB</span>
                      </div>
                    </div>
                  </div>
                </div>

                <CardContent className="p-6 flex flex-col flex-grow">
                  {/* Content Area - Takes up available space */}
                  <div className="flex-grow space-y-6">
                    {/* Description */}
                    {service.description && (
                      <CardDescription className="text-gray-700 text-sm leading-relaxed line-clamp-3">
                        {service.description}
                      </CardDescription>
                    )}

                    {/* Service Details */}
                    <div className="space-y-4">
                      {/* Document Requirements */}
                      {service.active_document_types_count > 0 && (
                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-900">Required Documents</span>
                          </div>
                          <Badge className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200">
                            {service.active_document_types_count} document{service.active_document_types_count !== 1 ? 's' : ''}
                          </Badge>
                        </div>
                      )}

                      {/* Service Status */}
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-100">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          <span className="text-sm font-medium text-green-900">Service Status</span>
                        </div>
                        <Badge className="bg-green-100 text-green-800 border-green-200">
                          Available
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Action Button - Always at bottom */}
                  <div className="pt-6 mt-auto">
                    <Button
                      onClick={() => handleApplyForService(service)}
                      className="w-full bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#0f4a7a] text-white transition-all duration-300 transform hover:scale-105 hover:shadow-lg font-medium py-3 rounded-lg"
                    >
                      <GraduationCap className="h-5 w-5 mr-2" />
                      Apply Now
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Services;
