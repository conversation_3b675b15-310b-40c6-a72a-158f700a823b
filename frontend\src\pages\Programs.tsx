import { useEffect, useState } from 'react';
import Layout from '@/components/Layout';
import programAP<PERSON>, { ApplicationInfo, RegistrationPeriod, College } from '@/services/programService';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, CheckCircle, XCircle, ChevronDown, ChevronRight, RefreshCw, GraduationCap } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import DocumentTitle from '@/components/DocumentTitle';
// We're using manual collapsible implementation instead of the Collapsible component

interface CollegeGroup {
  id: number;
  name: string;
  applications: ApplicationInfo[];
}

// Removed ProgramType type as we simplified the UI

interface Program {
  id: number;
  program_code: string;
  program_name: string;
  program_type?: string; // Program type (undergraduate, postgraduate, etc.)
  department?: number;
  college?: number;
  registration_fee?: number;
  registration_active: boolean; // Whether registration is currently active
  registration_message: string; // Message about registration status
  registration_status?: {
    active: boolean; // Same as registration_active (for backward compatibility)
    message: string; // Same as registration_message (for backward compatibility)
  };
  application_info: ApplicationInfo[]; // Application information for this program
}

const Programs = () => {
  // Custom pattern background for hero section
  const heroPatternImage = '/images/university-pattern.svg';

  // State for organization name
  const [orgName, setOrgName] = useState<string>('University of Gondar');

  // State for application information
  const [applicationInfo, setApplicationInfo] = useState<ApplicationInfo[]>([]);
  // We use filteredApplicationInfo in the useEffect but not directly in the JSX
  const [, setFilteredApplicationInfo] = useState<ApplicationInfo[]>([]);
  const [collegeGroups, setCollegeGroups] = useState<CollegeGroup[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // We've removed the program type filter to simplify the UI
  const [selectedProgramId, setSelectedProgramId] = useState<string>('all');
  const [programs, setPrograms] = useState<Program[]>([]);
  const [loadingPrograms, setLoadingPrograms] = useState<boolean>(false);
  const [noResultsFound, setNoResultsFound] = useState<boolean>(false);
  const [registrationMessage, setRegistrationMessage] = useState<string>('There is no active registration for any program.');
  const [expandedColleges, setExpandedColleges] = useState<{[key: number]: boolean}>({});

  // State for registration periods
  const [registrationPeriods, setRegistrationPeriods] = useState<RegistrationPeriod[]>([]);
  const [registrationStatus, setRegistrationStatus] = useState<{[key: number]: {active: boolean, futureOpening: boolean, timeRemaining?: string}}>({});

  // State for colleges
  const [colleges, setColleges] = useState<College[]>([]);

  // Function to group application info by college
  const groupByCollege = (applications: ApplicationInfo[]): CollegeGroup[] => {
    const collegeMap: {[key: number]: CollegeGroup} = {};

    // First, initialize the college map with all colleges from the database
    // This ensures we include colleges even if they have no applications
    colleges.forEach(college => {
      collegeMap[college.id] = {
        id: college.id,
        name: college.name,
        applications: []
      };
    });

    // Then add applications to their respective colleges
    applications.forEach(app => {
      const collegeId = app.college.id;

      if (!collegeMap[collegeId]) {
        // If the college doesn't exist in our map (which shouldn't happen if we have all colleges),
        // create it using the data from the application
        collegeMap[collegeId] = {
          id: collegeId,
          name: app.college.name,
          applications: []
        };
      }

      collegeMap[collegeId].applications.push(app);
    });

    // Return only colleges that have applications
    return Object.values(collegeMap).filter(college => college.applications.length > 0);
  };

  // Simple function to check if registration is active based on database data
  const isRegistrationActive = (periods: RegistrationPeriod[]): boolean => {
    if (!Array.isArray(periods) || periods.length === 0) {
      return false;
    }

    // Check if any period is active
    const hasActive = periods.some(period => {
      // Handle string representation
      if (typeof period.is_active === 'string') {
        return period.is_active.toLowerCase() === 'true';
      }
      // Handle boolean
      return period.is_active === true;
    });

    return hasActive;
  };

  // Function to calculate time remaining until registration closes
  const calculateTimeRemaining = (_openDate: string, closeDate: string): string => {
    const now = new Date();
    const close = new Date(closeDate);

    // If the close date is invalid, return a message
    if (isNaN(close.getTime())) {
      return 'Date unavailable';
    }

    // Format the closing date (used in multiple places)
    const formatDate = (date: Date) => {
      const month = date.toLocaleString('default', { month: 'short' });
      const day = date.getDate();
      const year = date.getFullYear();
      const time = date.toLocaleTimeString('default', { hour: '2-digit', minute: '2-digit' });
      return { month, day, year, time };
    };

    const { month, day, year, time } = formatDate(close);

    // If the close date is in the past, show it's closed
    if (close <= now) {
      return `Closed on: ${month} ${day}, ${year}`;
    }

    // Calculate time remaining until closing
    const remainingMs = close.getTime() - now.getTime();

    // Check if the close date is in the future but very close (less than 24 hours)
    const isCloseToDeadline = remainingMs < (24 * 60 * 60 * 1000);

    // Format the remaining time
    const diffDays = Math.floor(remainingMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((remainingMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));

    // Create a detailed time remaining string
    let timeRemainingStr = '';

    if (diffDays > 0) {
      timeRemainingStr = `${diffDays}d ${diffHours}h left`;
    } else if (diffHours > 0) {
      timeRemainingStr = `${diffHours}h ${diffMinutes}m left`;
    } else {
      timeRemainingStr = `${diffMinutes}m left`;
    }

    // Add an urgency indicator if close to deadline
    if (isCloseToDeadline) {
      timeRemainingStr = `⚠️ ${timeRemainingStr} (Urgent)`;
    }

    // Return both the time remaining and the closing date
    return `${timeRemainingStr} (Closes: ${month} ${day}, ${year} at ${time})`;
  };

  // Function to create status map for all applications
  const createStatusMap = (
    applications: ApplicationInfo[],
    registrationPeriods: RegistrationPeriod[]
  ): {[key: number]: {active: boolean, futureOpening: boolean, timeRemaining?: string}} => {
    const statusMap: {[key: number]: {active: boolean, futureOpening: boolean, timeRemaining?: string}} = {};

    applications.forEach(app => {
      // Find the registration period for this application's program
      const period = registrationPeriods.find(p => p.program === app.program.id);

      let isActive = false;
      let timeRemaining = '';

      if (period) {
        // Convert is_active to boolean if it's a string
        if (typeof period.is_active === 'string') {
          isActive = period.is_active.toLowerCase() === 'true';
        } else {
          isActive = period.is_active === true;
        }

        // Calculate time remaining if active
        if (isActive) {
          timeRemaining = calculateTimeRemaining(period.open_date, period.close_date);
        }
      }

      statusMap[app.id] = {
        active: isActive,
        futureOpening: false,
        timeRemaining: timeRemaining
      };
    });

    return statusMap;
  };

  // We've removed the filterApplicationsByType function as we simplified the UI

  // Function to toggle college expansion
  const toggleCollegeExpansion = (collegeId: number) => {
    setExpandedColleges(prev => ({
      ...prev,
      [collegeId]: !prev[collegeId]
    }));
  };

  // Handle program selection
  const handleProgramChange = async (programId: string) => {
    console.log(`Program selection changed to: ${programId}`);
    setSelectedProgramId(programId);

    // Reset the no results found state
    setNoResultsFound(false);
    setRegistrationMessage('');

    try {
      // If 'all' is selected, show all programs with active registration
      if (programId === 'all') {
        console.log('All programs selected, showing all programs with active registration');

        // Get all application info for programs with active registration
        let allApplicationInfo: ApplicationInfo[] = [];

        programs.forEach(program => {
          // Only include application info if registration is active
          if (program.registration_active && program.application_info?.length > 0) {
            console.log(`Adding ${program.application_info.length} application info records from program ${program.id}`);
            allApplicationInfo = [...allApplicationInfo, ...program.application_info];
          }
        });

        // Remove duplicates by creating a map using application IDs
        const uniqueApplicationsMap = new Map<number, ApplicationInfo>();
        allApplicationInfo.forEach(app => {
          if (!uniqueApplicationsMap.has(app.id)) {
            uniqueApplicationsMap.set(app.id, app);
          }
        });

        // Convert back to array
        allApplicationInfo = Array.from(uniqueApplicationsMap.values());

        console.log(`Found ${allApplicationInfo.length} unique application info records for all programs`);

        if (allApplicationInfo.length > 0) {
          setFilteredApplicationInfo(allApplicationInfo);
          setCollegeGroups(groupByCollege(allApplicationInfo));
        } else {
          setNoResultsFound(true);
          setRegistrationMessage('No active registration found for any program.');
          setFilteredApplicationInfo([]);
          setCollegeGroups([]);
        }

        return;
      }

      // Parse the program ID to a number for specific program selection
      const numericProgramId = parseInt(programId);
      console.log(`Parsed program ID: ${numericProgramId}`);

      if (isNaN(numericProgramId)) {
        console.error(`Invalid program ID: ${programId}`);
        toast.error(`Invalid program ID: ${programId}`);
        return;
      }

      // Find the selected program in our programs list
      const selectedProgram = programs.find(p => p.id === numericProgramId);
      if (!selectedProgram) {
        console.error(`Program with ID ${numericProgramId} not found`);
        toast.error(`Program with ID ${numericProgramId} not found`);
        return;
      }

      console.log('Selected program:', selectedProgram);

      // Check if registration is active for this program
      if (!selectedProgram.registration_active) {
        console.log(`Registration not active: ${selectedProgram.registration_message}`);
        setNoResultsFound(true);
        setRegistrationMessage(selectedProgram.registration_message || 'Registration is not currently open for this program.');
        setFilteredApplicationInfo([]);
        setCollegeGroups([]);
        return;
      }

      // Registration is active, check if we have application info
      if (selectedProgram.application_info && selectedProgram.application_info.length > 0) {
        console.log(`Found ${selectedProgram.application_info.length} application info records for program ${numericProgramId}`);
        setFilteredApplicationInfo(selectedProgram.application_info);
        setCollegeGroups(groupByCollege(selectedProgram.application_info));
      } else {
        console.log(`No application info found for program ${numericProgramId}`);
        setNoResultsFound(true);
        setRegistrationMessage('No application information available for this program.');
        setFilteredApplicationInfo([]);
        setCollegeGroups([]);
      }
    } catch (error) {
      console.error(`Error handling program selection: ${programId}`, error);
      toast.error('An error occurred while filtering programs.');
      setNoResultsFound(true);
      setRegistrationMessage('An error occurred. Please try again later.');
      setFilteredApplicationInfo([]);
      setCollegeGroups([]);
    }
  };

  // Initialize only the first college as expanded, rest collapsed
  useEffect(() => {
    if (collegeGroups.length > 0) {
      const initialExpandedState = collegeGroups.reduce((acc, college, index) => {
        acc[college.id] = index === 0; // Only the first college is expanded, rest are collapsed
        return acc;
      }, {} as {[key: number]: boolean});

      setExpandedColleges(initialExpandedState);
    }
  }, [collegeGroups]);

  // Effect to filter applications when selected program changes
  useEffect(() => {
    const filterApplications = async () => {
      if (applicationInfo.length > 0) {
        console.log(`Filtering applications - Selected Program ID: ${selectedProgramId}`);

        // Use the handleProgramChange function to filter applications
        await handleProgramChange(selectedProgramId);
      }
    };

    filterApplications();
  }, [applicationInfo, selectedProgramId]);



  // Function to fetch programs with registration status
  const fetchProgramTypes = async () => {
    try {
      setLoadingPrograms(true);
      console.log('Fetching programs with registration status...');

      // Use our simplified endpoint that returns programs with registration status and application info
      const programsWithStatus = await programAPI.getProgramsWithStatus();
      console.log('Fetched programs with status');

      if (!Array.isArray(programsWithStatus)) {
        console.error('Expected array of programs but got:', typeof programsWithStatus);
        toast.error('Invalid data format received from server');
        setPrograms([]);
        setLoadingPrograms(false);
        return [];
      }

      if (programsWithStatus.length === 0) {
        console.warn('No programs found in database');
        toast.warning('No programs found. Please contact the administrator.');
        setPrograms([]);
        setLoadingPrograms(false);
        return [];
      }

      // Log the first program for debugging
      if (programsWithStatus.length > 0) {
        console.log('First program example:', programsWithStatus[0]);
      }

      // Set the programs state with the data from database
      setPrograms(programsWithStatus);
      console.log(`Programs state updated with ${programsWithStatus.length} programs`);

      // Set loading to false
      setLoadingPrograms(false);

      // Return the programs data
      return programsWithStatus;
    } catch (error) {
      console.error('Error fetching programs:', error);
      toast.error(`Failed to fetch programs: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setLoadingPrograms(false);
      setPrograms([]);
      return [];
    }
  };

  // Function to log unique program types
  const logUniqueTypes = (programsData: Program[]) => {
    try {
      // Log the unique program types found
      const uniqueTypes = [...new Set(programsData
          .map((p: Program) => p.program_type?.toLowerCase())
          .filter(Boolean) // Remove any undefined/null values
        )];

      console.log('Unique program types found:', uniqueTypes);
      if (programsData.length > 0) {
        console.log('Program data structure example:', programsData[0]);
      }
    } catch (err) {
      console.error('Error processing program types:', err);
    }
  };

  // Function to fetch data that can be called on demand
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all application information directly from the database via API
      console.log('Fetching application information from database...');
      try {
        console.log('Sending request to: http://localhost:8000/api/application-information/filter/');
        const response = await fetch('http://localhost:8000/api/application-information/filter/', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          },
        });

        console.log('Application info response status:', response.status);
        console.log('Application info response headers:', [...response.headers.entries()]);

        if (!response.ok) {
          throw new Error(`API returned status: ${response.status}`);
        }

        const appInfoData = await response.json();
        console.log('Received application data from database:', appInfoData);

        // Debug the first application's program data
        if (appInfoData.length > 0) {
          console.log('First application program data:', appInfoData[0].program);
          console.log('Program name exists:', Boolean(appInfoData[0].program.program_name));
          console.log('Program name value:', appInfoData[0].program.program_name);
          console.log('Registration fee exists:', Boolean(appInfoData[0].program.registration_fee));
          console.log('Registration fee value:', appInfoData[0].program.registration_fee);
          console.log('Registration fee type:', typeof appInfoData[0].program.registration_fee);

          // Log all properties of the program object
          console.log('All program properties:');
          for (const key in appInfoData[0].program) {
            console.log(`- ${key}: ${appInfoData[0].program[key]} (${typeof appInfoData[0].program[key]})`);
          }
        }

        if (!Array.isArray(appInfoData)) {
          console.error('Expected array of application information but got:', typeof appInfoData);
          throw new Error('Invalid data format for application information');
        }

        if (appInfoData.length === 0) {
          console.warn('No application information found in database');
        }

        // Update state with data from database
        setApplicationInfo(appInfoData);
        setFilteredApplicationInfo(appInfoData); // Initially show all

        // Group application info by college
        const groupedByCollege = groupByCollege(appInfoData);
        setCollegeGroups(groupedByCollege);
      } catch (appError) {
        console.error('Error fetching application information from database:', appError);
        // No fallback - we want to use only real data
        console.warn('Could not fetch application information from database');
        setApplicationInfo([]);
        setFilteredApplicationInfo([]);
        setCollegeGroups([]);
      }

      // Fetch registration periods directly from the database via API
      console.log('Fetching registration periods from database...');
      try {
        // Direct API call to ensure we get fresh data from the database
        const periodsResponse = await fetch('http://localhost:8000/api/registration-periods/public/', {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          }
        });
        if (!periodsResponse.ok) {
          throw new Error(`API returned status: ${periodsResponse.status}`);
        }

        // Parse the JSON response from the database
        const periodsData = await periodsResponse.json();
        console.log('Received registration periods from database:', periodsData);

        if (!Array.isArray(periodsData)) {
          console.error('Expected array of registration periods but got:', typeof periodsData);
          throw new Error('Invalid data format for registration periods');
        }

        if (periodsData.length === 0) {
          console.warn('No registration periods found in database');
        }

        // Log each registration period from the database
        periodsData.forEach((period: RegistrationPeriod) => {
          console.log(`Registration period from database: ID=${period.id}, ${period.program_name || 'Program ID=' + period.program}, is_active=${period.is_active} (type: ${typeof period.is_active})`);

          // Force convert to boolean if it's a string (JSON serialization might convert booleans to strings)
          if (typeof period.is_active === 'string') {
            period.is_active = period.is_active.toLowerCase() === 'true';
            console.log(`Converted string to boolean: ${period.is_active}`);
          }
        });

        // Update state with data from database
        setRegistrationPeriods(periodsData);

        // Log the active status based on the database data
        const hasActiveRegistration = periodsData.some(period => {
          if (typeof period.is_active === 'string') {
            return period.is_active.toLowerCase() === 'true';
          }
          return period.is_active === true;
        });
        console.log(`Based on database data, registration is ${hasActiveRegistration ? 'ACTIVE' : 'NOT ACTIVE'}`);

        // Store the registration periods data for later use
        // We'll check the registration status after all data is loaded
        console.log('Storing registration periods data for later status check');

        // Use our new functions to determine registration status
        const isActive = isRegistrationActive(periodsData);
        console.log(`Registration active status from database: ${isActive}`);

        // We'll create the status map after application info is loaded
      } catch (periodsError) {
        console.error('Error fetching registration periods from database:', periodsError);
        // No fallback - we want to use only real data
        console.warn('Could not fetch registration periods from database');
        setRegistrationPeriods([]);
      }

      // Fetch colleges directly from the database via API
      console.log('Fetching colleges from database...');
      try {
        const collegeResponse = await fetch('http://localhost:8000/api/colleges/public/', {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          }
        });
        if (!collegeResponse.ok) {
          throw new Error(`API returned status: ${collegeResponse.status}`);
        }

        const collegesData = await collegeResponse.json();
        console.log('Received colleges data from database:', collegesData);

        if (!Array.isArray(collegesData)) {
          console.error('Expected array of colleges but got:', typeof collegesData);
          throw new Error('Invalid data format for colleges');
        }

        if (collegesData.length === 0) {
          console.warn('No colleges found in database');
        }

        // Update state with data from database
        setColleges(collegesData);
      } catch (collegeError) {
        console.error('Error fetching colleges from database:', collegeError);
        // No fallback - we want to use only real data
        console.warn('Could not fetch colleges from database');
        setColleges([]);
      }

      // We'll let the useEffect hook handle the registration status check
      // after all data has been loaded
      console.log('Data fetching complete, useEffect will handle registration status');

    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load application information');
    } finally {
      setLoading(false);
    }
  };

  // Fetch organization name with localStorage caching
  useEffect(() => {
    const fetchOrganizationName = async () => {
      // Try to get from localStorage first for immediate display
      const cachedOrgName = localStorage.getItem('organizationName');
      if (cachedOrgName) {
        setOrgName(cachedOrgName);
      }

      try {
        const response = await fetch('http://localhost:8000/api/settings/organization/public/', {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data && data.organization) {
            setOrgName(data.organization);
            // Cache the organization name in localStorage
            localStorage.setItem('organizationName', data.organization);
          }
        }
      } catch (error) {
        console.error('Error fetching organization name:', error);
      }
    };

    fetchOrganizationName();
  }, []);

  // Fetch application info, registration periods, and program types on component mount
  useEffect(() => {
    // Check if there's a program_id in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const programIdParam = urlParams.get('program_id');

    if (programIdParam) {
      console.log(`Found program_id in URL: ${programIdParam}`);
      try {
        // Validate that it's a number
        const programId = parseInt(programIdParam);
        if (!isNaN(programId)) {
          console.log(`Setting selected program ID to: ${programIdParam}`);
          setSelectedProgramId(programIdParam);
        } else {
          console.error(`Invalid program ID in URL: ${programIdParam}`);
          setSelectedProgramId('all');
        }
      } catch (error) {
        console.error(`Error parsing program ID from URL: ${programIdParam}`, error);
        setSelectedProgramId('all');
      }
    } else {
      // No program ID in URL, set to 'all'
      setSelectedProgramId('all');
    }

    // First fetch programs to ensure they're available for filtering
    const loadData = async () => {
      try {
        console.log('Starting data loading sequence...');
        const programsData = await fetchProgramTypes(); // Fetch programs first
        console.log('Programs data loaded:', programsData);

        if (programsData && programsData.length > 0) {
          console.log('Programs loaded successfully, now fetching application data...');
          // Log unique program types for debugging
          logUniqueTypes(programsData);
          await fetchData(); // Then fetch application data
        } else {
          console.warn('No programs data available, skipping application data fetch');
          setLoadingPrograms(false);
        }
      } catch (error) {
        console.error('Error in loadData:', error);
        toast.error('Failed to load program data');
        setLoadingPrograms(false);
      }
    };

    loadData();
  }, []);

  // Update registration status whenever registration periods or application info changes
  useEffect(() => {
    console.log('Registration data changed, checking status...');
    console.log(`Registration periods: ${registrationPeriods.length}, Application info: ${applicationInfo.length}`);

    // Make sure we have both registration periods and application info
    if (registrationPeriods.length > 0 && applicationInfo.length > 0) {
      console.log('Both data sources available, determining registration status');

      try {
        // Log all registration periods for debugging
        registrationPeriods.forEach(period => {
          console.log(`Registration period in useEffect: ID=${period.id}, Program=${period.program}, is_active=${period.is_active} (${typeof period.is_active})`);
        });

        // Use our new functions to determine registration status
        const isActive = isRegistrationActive(registrationPeriods);
        console.log(`FINAL REGISTRATION STATUS FROM DATABASE: ${isActive ? 'ACTIVE' : 'NOT ACTIVE'}`);

        // Create status map for all applications with time remaining
        const statusMap = createStatusMap(applicationInfo, registrationPeriods);
        console.log('Setting registration status with map:', statusMap);
        setRegistrationStatus(statusMap);
      } catch (error) {
        console.error('Error determining registration status:', error);
      }
    } else {
      console.log('Waiting for data to be loaded before checking registration status');
    }
  }, [registrationPeriods, applicationInfo]);

  return (
    <Layout>
      <DocumentTitle pageTitle="Programs" />
      {/* Hero Section with Animated Patterned Background - Half Height */}
      <section className="relative py-4 md:py-6 h-[175px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          <h1 className="text-xl md:text-2xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
            {orgName} Programs
          </h1>
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Browse our comprehensive range of academic programs offered across various colleges.
            Find detailed information and apply for programs that match your educational goals.
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">


        {/* Enhanced Program Filter */}
        <div className="mb-16 max-w-lg mx-auto">
          <Card className="shadow-xl border-0 overflow-hidden bg-white/80 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-gondar/5 to-gondar-accent/5 border-b border-gondar/10">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gondar/10 rounded-lg">
                  <GraduationCap className="h-5 w-5 text-gondar" />
                </div>
                <div>
                  <CardTitle className="text-xl text-gondar">Find Your Program</CardTitle>
                  <CardDescription className="text-gray-600">Select a program to view available applications</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-8 pb-8">
              <div className="space-y-5">
                <div className="relative">
                  {loadingPrograms ? (
                    <div className="p-6 border-2 border-dashed border-gondar/20 rounded-xl text-sm text-gray-500 bg-gondar/5 flex items-center justify-center">
                      <Loader2 className="h-5 w-5 animate-spin mr-3 text-gondar" />
                      <span className="font-medium">Loading programs...</span>
                    </div>
                  ) : (
                    <Select
                      value={selectedProgramId}
                      onValueChange={handleProgramChange}
                    >
                      <SelectTrigger className="w-full h-14 border-2 border-gondar/20 hover:border-gondar/40 focus:border-gondar focus:ring-2 focus:ring-gondar/20 transition-all duration-200 rounded-xl bg-white shadow-sm">
                        <SelectValue placeholder="🎓 Select a program to explore" className="text-gray-600" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* Default option */}
                        <SelectItem value="all">All Programs</SelectItem>

                        {/* Group programs by type (without labels) */}
                        {Array.from(new Set(programs.map(p => p.program_type || 'Other'))).sort().map(type => (
                          <SelectGroup key={type}>
                            {programs
                              .filter(program => (program.program_type || 'Other') === type)
                              .sort((a, b) => a.program_name.localeCompare(b.program_name))
                              .map(program => (
                                <SelectItem
                                  key={program.id}
                                  value={program.id.toString()}
                                >
                                  {program.program_name}
                                </SelectItem>
                              ))
                            }
                          </SelectGroup>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>



                <div className="flex space-x-3 pt-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      // Clear filters
                      setSelectedProgramId('all');
                      setNoResultsFound(false);
                      setRegistrationMessage('There is no active registration for any program.');

                      // Remove program_id from URL
                      const url = new URL(window.location.href);
                      url.searchParams.delete('program_id');
                      window.history.pushState({}, '', url.toString());
                    }}
                    className="flex-1 py-2.5 px-4 border border-gray-300 hover:border-[#1a73c0] text-gray-700 hover:text-[#1a73c0] font-medium rounded-md flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                    disabled={loading || selectedProgramId === 'all'}
                  >
                    <span>Clear Filters</span>
                  </Button>

                  <Button
                    variant="default"
                    onClick={() => fetchData()}
                    className="flex-1 py-2.5 px-4 bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium rounded-md flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                    disabled={loading}
                  >
                    {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
                    <span>Refresh Data</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-12 w-12 text-gray-600 animate-spin" />
            <span className="ml-3 text-xl text-gray-600">Loading programs...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Alert variant="destructive" className="my-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* No Programs State */}
        {!loading && !error && programs.length === 0 && (
          <div className="text-center py-20">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertCircle className="h-12 w-12 text-amber-500" />
              <p className="text-xl text-gray-700 font-medium">No programs available at the moment.</p>
              <p className="text-gray-500 max-w-md">The system is currently experiencing technical difficulties. Please try again later or contact the administrator for assistance.</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4 py-2.5 px-4 bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium rounded-md flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                <span>Refresh Page</span>
              </Button>
            </div>
          </div>
        )}

        {/* No Results Found State */}
        {!loading && !error && noResultsFound && (
          <Alert variant="destructive" className="my-8 max-w-2xl mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No Results Found</AlertTitle>
            <AlertDescription>{registrationMessage}</AlertDescription>
          </Alert>
        )}

        {/* Display Programs Grouped by College - Table Format */}
        {!loading && !error && collegeGroups.map((college, index) => (
          <div key={college.id} className="mb-8">
            <Card className="shadow-lg border border-gray-200 bg-white">
              <CardHeader
                className="cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200 border-b border-gray-200"
                onClick={() => toggleCollegeExpansion(college.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="bg-gondar text-white p-2 rounded-lg">
                      <GraduationCap className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-gray-800 font-semibold">
                        {college.name}
                      </CardTitle>
                      <CardDescription className="mt-1 text-gray-600">
                        {college.applications.length} program{college.applications.length !== 1 ? 's' : ''} available
                      </CardDescription>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-800 hover:bg-gray-200">
                    {expandedColleges[college.id] ?
                      <ChevronDown className="h-5 w-5" /> :
                      <ChevronRight className="h-5 w-5" />}
                  </Button>
                </div>
              </CardHeader>

              {/* Table Layout */}
              {expandedColleges[college.id] && (
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <Table className="w-full">
                      <TableHeader className="bg-gray-50">
                        <TableRow className="border-b border-gray-200">
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Department</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Field of Study</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Program</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Admission Type</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Duration</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Term</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Fee (ETB)</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Status</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Closing Date</TableHead>
                          <TableHead className="py-2 px-3 text-gray-700 font-semibold text-sm">Action</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {college.applications.map((app, index) => (
                          <TableRow key={app.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-100 transition-colors duration-200`}>
                            <TableCell className="py-2 px-3 border-b border-gray-100 font-medium text-gray-800">{app.department.name}</TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100 text-gray-700">{app.field_of_study.name}</TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                                {app.program.program_code || 'N/A'}
                              </span>
                            </TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100 text-gray-700">{app.admission_type.name}</TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100 text-gray-700">{app.duration ? `${app.duration} Years` : 'N/A'}</TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100 text-gray-700">
                              {(() => {
                                const period = registrationPeriods.find(p => p.program === app.program.id);
                                return period?.term_name || 'N/A';
                              })()}
                            </TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100">
                              {(() => {
                                if (app.program.registration_fee) {
                                  const fee = typeof app.program.registration_fee === 'string' ?
                                    parseFloat(app.program.registration_fee) :
                                    app.program.registration_fee;

                                  return (
                                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                      {fee.toFixed(2)}
                                    </span>
                                  );
                                } else {
                                  return <span className="text-gray-500 italic">N/A</span>;
                                }
                              })()}
                            </TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100">
                              {registrationStatus[app.id]?.active ? (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  OPEN
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                  <XCircle className="h-3 w-3 mr-1" />
                                  CLOSED
                                </span>
                              )}
                            </TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100">
                              {registrationStatus[app.id]?.active ? (
                                <div className="text-xs">
                                  {(() => {
                                    const period = registrationPeriods.find(p => p.program === app.program.id);

                                    if (period) {
                                      const closeDate = new Date(period.close_date);
                                      const now = new Date();

                                      if (closeDate > now) {
                                        const remainingMs = closeDate.getTime() - now.getTime();
                                        const diffDays = Math.floor(remainingMs / (1000 * 60 * 60 * 24));
                                        const diffHours = Math.floor((remainingMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                                        const diffMinutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));

                                        let timeRemainingStr = '';
                                        if (diffDays > 0) {
                                          timeRemainingStr = `${diffDays}d ${diffHours}h remaining`;
                                        } else if (diffHours > 0) {
                                          timeRemainingStr = `${diffHours}h ${diffMinutes}m remaining`;
                                        } else {
                                          timeRemainingStr = `${diffMinutes}m remaining`;
                                        }

                                        const isUrgent = remainingMs < (24 * 60 * 60 * 1000);

                                        return (
                                          <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                                            isUrgent
                                              ? 'bg-orange-100 text-orange-800 border border-orange-200'
                                              : 'bg-blue-100 text-blue-800 border border-blue-200'
                                          }`}>
                                            {timeRemainingStr}
                                          </span>
                                        );
                                      }
                                    }
                                    return <span className="text-gray-500 text-xs italic">Date not specified</span>;
                                  })()}
                                </div>
                              ) : (
                                <span className="text-gray-500 text-xs italic">Not available</span>
                              )}
                            </TableCell>
                            <TableCell className="py-2 px-3 border-b border-gray-100">
                              {registrationStatus[app.id]?.active ? (
                                <Button asChild size="sm" className="bg-gondar hover:bg-gondar-accent text-white">
                                  <Link to={`/application?program=${app.program.id}`}>
                                    Apply Now
                                  </Link>
                                </Button>
                              ) : (
                                <Button size="sm" variant="outline" disabled className="text-gray-400 border-gray-300">
                                  Apply Now
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                </CardContent>
              )}
            </Card>
          </div>
        ))}
      </div>
    </Layout>
  );
};

export default Programs;
