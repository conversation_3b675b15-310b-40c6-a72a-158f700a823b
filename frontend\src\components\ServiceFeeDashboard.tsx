import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { FileText, TrendingUp, CreditCard, Download, Printer, AlertCircle } from 'lucide-react';
import api from '@/services/api';

// Colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const ServiceFeeDashboard = () => {
  // State for dashboard data
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState({
    totalRevenue: 0,
    monthlyRevenue: 0,
    totalTransactions: 0,
    averageFee: 0,
    revenueData: [],
    paymentMethodData: [],
    recentTransactions: []
  });

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch payment statistics
        const response = await api.get('/payment-statistics/');

        // Process the data
        const data = response.data;

        // If we have real data, use it
        if (data) {
          setDashboardData({
            totalRevenue: data.total_revenue || 0,
            monthlyRevenue: data.monthly_revenue || 0,
            totalTransactions: data.total_transactions || 0,
            averageFee: data.average_fee || 0,
            revenueData: data.revenue_data || [],
            paymentMethodData: data.payment_method_data || [],
            recentTransactions: data.recent_transactions || []
          });
        } else {
          // API didn't return expected format
          setError('Invalid data format received from server. Please contact support.');
          setDashboardData({
            totalRevenue: 0,
            monthlyRevenue: 0,
            totalTransactions: 0,
            averageFee: 0,
            revenueData: [],
            paymentMethodData: [],
            recentTransactions: []
          });
        }
      } catch (err) {
        console.error('Error fetching payment data:', err);
        setError('Failed to load payment data. Please try again later.');

        // Reset dashboard data on error
        setDashboardData({
          totalRevenue: 0,
          monthlyRevenue: 0,
          totalTransactions: 0,
          averageFee: 0,
          revenueData: [],
          paymentMethodData: [],
          recentTransactions: []
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <div className="space-y-6">
      {/* Error Message */}
      {error && (
        <Card className="border-red-300 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <p>{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total Revenue</CardTitle>
            <CardDescription>All time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : `${dashboardData.totalRevenue.toLocaleString()} ETB`}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full">
              <FileText className="h-4 w-4 mr-2" />
              View Report
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Monthly Revenue</CardTitle>
            <CardDescription>Current month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : `${dashboardData.monthlyRevenue.toLocaleString()} ETB`}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full">
              <FileText className="h-4 w-4 mr-2" />
              Monthly Details
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Transactions</CardTitle>
            <CardDescription>Total processed</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <CreditCard className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : dashboardData.totalTransactions.toLocaleString()}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full">
              <FileText className="h-4 w-4 mr-2" />
              View Transactions
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Average Fee</CardTitle>
            <CardDescription>Per application</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-[#1a73c0]">
                {loading ? '...' : `${dashboardData.averageFee.toLocaleString()} ETB`}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" className="w-full">
              <FileText className="h-4 w-4 mr-2" />
              Fee Structure
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
            <CardDescription>Monthly revenue over the past year</CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
              </div>
            ) : dashboardData.revenueData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={dashboardData.revenueData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} ETB`, 'Revenue']} />
                  <Legend />
                  <Area type="monotone" dataKey="amount" name="Revenue" stroke="#1a73c0" fill="#1a73c0" fillOpacity={0.3} />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No revenue data available
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>Distribution by payment type</CardDescription>
          </CardHeader>
          <CardContent className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1a73c0]"></div>
              </div>
            ) : dashboardData.paymentMethodData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={dashboardData.paymentMethodData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {dashboardData.paymentMethodData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No payment method data available
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>Latest payment activities</CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-24">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0]"></div>
            </div>
          ) : dashboardData.recentTransactions.length > 0 ? (
            <div className="space-y-4">
              {dashboardData.recentTransactions.map((transaction, index) => (
                <div key={index} className="flex items-start space-x-4 border-b pb-4 last:border-0">
                  <div className={`${transaction.status === 'Completed' ? 'bg-green-100' : 'bg-yellow-100'} p-2 rounded-full`}>
                    <span className={`text-sm font-bold ${transaction.status === 'Completed' ? 'text-green-600' : 'text-yellow-600'}`}>ETB</span>
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium">Payment #{transaction.id}</p>
                    <p className="text-xs text-gray-500">{transaction.amount} ETB • {transaction.method} • {transaction.time_ago}</p>
                  </div>
                  <div className={`text-sm font-medium ${transaction.status === 'Completed' ? 'text-green-600' : 'text-yellow-600'}`}>
                    {transaction.status}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-24 text-gray-500">
              No recent transactions available
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full">View All Transactions</Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ServiceFeeDashboard;
