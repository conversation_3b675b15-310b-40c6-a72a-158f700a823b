from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
import mimetypes
import os
from .models import Downloadable
from .serializers import (
    DownloadableSerializer,
    DownloadableCreateSerializer,
    DownloadableUpdateSerializer
)


class DownloadableViewSet(viewsets.ModelViewSet):
    queryset = Downloadable.objects.filter(is_active=True)
    serializer_class = DownloadableSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return DownloadableCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return DownloadableUpdateSerializer
        return DownloadableSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['list', 'retrieve', 'download', 'view']:
            # Allow public access to view and download files
            permission_classes = [permissions.AllowAny]
        else:
            # Only staff can create, update, delete
            permission_classes = [permissions.IsAdminUser]

        return [permission() for permission in permission_classes]

    @action(detail=True, methods=['get'], permission_classes=[permissions.AllowAny])
    def download(self, request, pk=None):
        """
        Download the file with proper MIME type
        """
        downloadable = get_object_or_404(Downloadable, pk=pk, is_active=True)

        if not downloadable.file:
            raise Http404("File not found")

        try:
            # Get the filename and detect MIME type
            filename = downloadable.file.name.split("/")[-1]
            mime_type, _ = mimetypes.guess_type(filename)

            # Default to octet-stream if MIME type cannot be determined
            if not mime_type:
                mime_type = 'application/octet-stream'

            response = HttpResponse(
                downloadable.file.read(),
                content_type=mime_type
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
        except (ValueError, OSError):
            raise Http404("File not found")

    @action(detail=True, methods=['get'], permission_classes=[permissions.AllowAny])
    def view(self, request, pk=None):
        """
        View/preview the file inline (for images, PDFs, etc.)
        """
        downloadable = get_object_or_404(Downloadable, pk=pk, is_active=True)

        if not downloadable.file:
            raise Http404("File not found")

        try:
            # Get the filename and detect MIME type
            filename = downloadable.file.name.split("/")[-1]
            mime_type, _ = mimetypes.guess_type(filename)

            # Default to octet-stream if MIME type cannot be determined
            if not mime_type:
                mime_type = 'application/octet-stream'

            response = HttpResponse(
                downloadable.file.read(),
                content_type=mime_type
            )

            # For viewable content, use inline disposition
            if mime_type.startswith(('image/', 'application/pdf', 'text/')):
                response['Content-Disposition'] = f'inline; filename="{filename}"'
            else:
                # For non-viewable content, force download
                response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response
        except (ValueError, OSError):
            raise Http404("File not found")

    def list(self, request, *args, **kwargs):
        """
        List all active downloadable files
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'count': queryset.count(),
            'results': serializer.data
        })

    def create(self, request, *args, **kwargs):
        """
        Create a new downloadable file
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        # Return full serializer data
        instance = serializer.instance
        response_serializer = DownloadableSerializer(instance, context={'request': request})

        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED
        )
