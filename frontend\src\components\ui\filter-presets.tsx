import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Clock, Star, Zap, Calendar, Building2, GraduationCap } from "lucide-react"
import { cn } from "@/lib/utils"

export interface FilterPreset {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  years: string[]
  colleges: string[]
  color: string
}

interface FilterPresetsProps {
  availableYears: Array<{ label: string; value: string }>
  availableColleges: Array<{ label: string; value: string }>
  onApplyPreset: (preset: FilterPreset) => void
  className?: string
}

export function FilterPresets({
  availableYears,
  availableColleges,
  onApplyPreset,
  className
}: FilterPresetsProps) {
  // Generate dynamic presets based on available data
  const generatePresets = (): FilterPreset[] => {
    const currentYear = new Date().getFullYear()
    const years = availableYears.map(y => parseInt(y.value)).sort((a, b) => b - a)
    const lastThreeYears = years.slice(0, 3).map(y => y.toString())
    const lastFiveYears = years.slice(0, 5).map(y => y.toString())
    
    const presets: FilterPreset[] = [
      {
        id: "current-year",
        name: "Current Academic Year",
        description: `${currentYear} graduates only`,
        icon: <Calendar className="h-4 w-4" />,
        years: [currentYear.toString()],
        colleges: [],
        color: "bg-blue-500"
      },
      {
        id: "last-3-years",
        name: "Last 3 Years",
        description: "Recent graduate trends",
        icon: <Clock className="h-4 w-4" />,
        years: lastThreeYears,
        colleges: [],
        color: "bg-green-500"
      },
      {
        id: "last-5-years",
        name: "Last 5 Years",
        description: "Extended trend analysis",
        icon: <Zap className="h-4 w-4" />,
        years: lastFiveYears,
        colleges: [],
        color: "bg-purple-500"
      },
      {
        id: "all-years",
        name: "All Years",
        description: "Complete historical data",
        icon: <GraduationCap className="h-4 w-4" />,
        years: availableYears.map(y => y.value),
        colleges: [],
        color: "bg-indigo-500"
      }
    ]

    // Add college-specific presets
    availableColleges.forEach((college, index) => {
      const colors = ["bg-orange-500", "bg-teal-500", "bg-pink-500", "bg-cyan-500"]
      presets.push({
        id: `college-${college.value}`,
        name: college.label.replace("College of ", ""),
        description: `All graduates from ${college.label}`,
        icon: <Building2 className="h-4 w-4" />,
        years: [],
        colleges: [college.value],
        color: colors[index % colors.length]
      })
    })

    return presets
  }

  const presets = generatePresets()

  return (
    <Card className={cn("border-0 shadow-lg", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
          <Star className="h-5 w-5 mr-2 text-yellow-500" />
          Quick Filters
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {presets.map((preset) => (
            <Button
              key={preset.id}
              variant="outline"
              onClick={() => onApplyPreset(preset)}
              className="h-auto p-3 flex flex-col items-start space-y-2 hover:shadow-md transition-all duration-200 group"
            >
              <div className="flex items-center space-x-2 w-full">
                <div className={cn("p-1.5 rounded-md text-white", preset.color)}>
                  {preset.icon}
                </div>
                <span className="font-medium text-sm text-left flex-1 group-hover:text-blue-600">
                  {preset.name}
                </span>
              </div>
              <p className="text-xs text-gray-500 text-left w-full">
                {preset.description}
              </p>
              <div className="flex flex-wrap gap-1 w-full">
                {preset.years.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {preset.years.length === 1 ? preset.years[0] : `${preset.years.length} years`}
                  </Badge>
                )}
                {preset.colleges.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {preset.colleges.length === 1 ? "1 college" : `${preset.colleges.length} colleges`}
                  </Badge>
                )}
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
