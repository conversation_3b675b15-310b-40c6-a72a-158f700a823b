/**
 * Enhanced Protected Route Component
 * Provides comprehensive authentication and authorization checks
 */
import React, { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'sonner';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAllPermissions?: boolean;
  requireAllRoles?: boolean;
  requireAdmin?: boolean;
  requireStaff?: boolean;
  fallbackPath?: string;
  showAccessDenied?: boolean;
  validateWithBackend?: boolean;
  onAccessDenied?: () => void;
}

interface AccessDeniedProps {
  message: string;
  details?: string;
  onRetry?: () => void;
}

const AccessDenied: React.FC<AccessDeniedProps> = ({ message, details, onRetry }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <svg
            className="h-6 w-6 text-red-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <h3 className="mt-4 text-lg font-medium text-gray-900">Access Denied</h3>
        <p className="mt-2 text-sm text-gray-500">{message}</p>
        {details && (
          <p className="mt-1 text-xs text-gray-400">{details}</p>
        )}
        <div className="mt-6 flex flex-col sm:flex-row gap-3">
          <button
            onClick={() => window.history.back()}
            className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Go Back
          </button>
          {onRetry && (
            <button
              onClick={onRetry}
              className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Retry
            </button>
          )}
        </div>
      </div>
    </div>
  </div>
);

const LoadingSpinner: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
  </div>
);

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredPermissions = [],
  requiredRoles = [],
  requireAllPermissions = false,
  requireAllRoles = false,
  requireAdmin = false,
  requireStaff = false,
  fallbackPath = '/login',
  showAccessDenied = true,
  validateWithBackend = false,
  onAccessDenied,
}) => {
  const {
    user,
    isAuthenticated,
    isLoading,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isAdmin,
    isStaff,
    validateToken
  } = useAuth();
  
  const location = useLocation();
  const [isValidating, setIsValidating] = useState(validateWithBackend);
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    message?: string;
    details?: string;
  }>({ valid: true });

  // Backend validation effect
  useEffect(() => {
    if (validateWithBackend && isAuthenticated && !isLoading) {
      const performValidation = async () => {
        setIsValidating(true);
        try {
          const isValid = await validateToken();
          setValidationResult({
            valid: isValid,
            message: isValid ? undefined : 'Session validation failed',
            details: isValid ? undefined : 'Please log in again to continue'
          });
        } catch (error) {
          console.error('Backend validation error:', error);
          setValidationResult({
            valid: false,
            message: 'Unable to validate session',
            details: 'Please check your connection and try again'
          });
        } finally {
          setIsValidating(false);
        }
      };

      performValidation();
    }
  }, [validateWithBackend, isAuthenticated, isLoading, validateToken]);

  // Show loading spinner while checking auth or validating
  if (isLoading || isValidating) {
    return <LoadingSpinner />;
  }

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    // Store the attempted location for redirect after login
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Backend validation failed
  if (validateWithBackend && !validationResult.valid) {
    if (showAccessDenied) {
      return (
        <AccessDenied
          message={validationResult.message || 'Access denied'}
          details={validationResult.details}
          onRetry={() => window.location.reload()}
        />
      );
    }
    return <Navigate to={fallbackPath} replace />;
  }

  // Check admin requirement
  if (requireAdmin && !isAdmin()) {
    const message = 'Administrator privileges required';
    const details = 'You need administrator access to view this page';
    
    if (onAccessDenied) onAccessDenied();
    toast.error(message, { description: details });
    
    if (showAccessDenied) {
      return <AccessDenied message={message} details={details} />;
    }
    return <Navigate to={fallbackPath} replace />;
  }

  // Check staff requirement
  if (requireStaff && !isStaff()) {
    const message = 'Staff privileges required';
    const details = 'You need staff access to view this page';
    
    if (onAccessDenied) onAccessDenied();
    toast.error(message, { description: details });
    
    if (showAccessDenied) {
      return <AccessDenied message={message} details={details} />;
    }
    return <Navigate to={fallbackPath} replace />;
  }

  // Check role requirements
  if (requiredRoles.length > 0) {
    const hasRequiredRoles = requireAllRoles 
      ? hasAllRoles(requiredRoles)
      : hasAnyRole(requiredRoles);
    
    if (!hasRequiredRoles) {
      const message = `Required role${requiredRoles.length > 1 ? 's' : ''}: ${requiredRoles.join(', ')}`;
      const details = `You need ${requireAllRoles ? 'all' : 'one'} of these roles to access this page`;
      
      if (onAccessDenied) onAccessDenied();
      toast.error('Insufficient role privileges', { description: message });
      
      if (showAccessDenied) {
        return <AccessDenied message="Insufficient role privileges" details={details} />;
      }
      return <Navigate to={fallbackPath} replace />;
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAllPermissions
      ? requiredPermissions.every(permission => hasPermission(permission))
      : requiredPermissions.some(permission => hasPermission(permission));
    
    if (!hasRequiredPermissions) {
      const message = `Required permission${requiredPermissions.length > 1 ? 's' : ''}: ${requiredPermissions.join(', ')}`;
      const details = `You need ${requireAllPermissions ? 'all' : 'one'} of these permissions to access this page`;
      
      if (onAccessDenied) onAccessDenied();
      toast.error('Insufficient permissions', { description: message });
      
      if (showAccessDenied) {
        return <AccessDenied message="Insufficient permissions" details={details} />;
      }
      return <Navigate to={fallbackPath} replace />;
    }
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

// Convenience components for common use cases
export const AdminRoute: React.FC<{ children: ReactNode; fallbackPath?: string }> = ({ 
  children, 
  fallbackPath = '/dashboard' 
}) => (
  <ProtectedRoute requireAdmin fallbackPath={fallbackPath}>
    {children}
  </ProtectedRoute>
);

export const StaffRoute: React.FC<{ children: ReactNode; fallbackPath?: string }> = ({ 
  children, 
  fallbackPath = '/dashboard' 
}) => (
  <ProtectedRoute requireStaff fallbackPath={fallbackPath}>
    {children}
  </ProtectedRoute>
);

export const RoleRoute: React.FC<{ 
  children: ReactNode; 
  roles: string[]; 
  requireAll?: boolean;
  fallbackPath?: string;
}> = ({ children, roles, requireAll = false, fallbackPath = '/dashboard' }) => (
  <ProtectedRoute 
    requiredRoles={roles} 
    requireAllRoles={requireAll}
    fallbackPath={fallbackPath}
  >
    {children}
  </ProtectedRoute>
);

export default ProtectedRoute;
