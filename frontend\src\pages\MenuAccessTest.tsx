/**
 * Menu Access Test Page
 * This page helps test and debug the permission-based menu access control system
 */

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRBAC } from '@/contexts/SimpleRBACContext';
import { useMenuAccess } from '@/hooks/useMenuAccess';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  User, 
  Shield, 
  Users, 
  Settings,
  Eye,
  EyeOff
} from 'lucide-react';

const MenuAccessTest: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { isSuperuser, isStaff, isRegularUser, userType } = useRBAC();
  const {
    applicantMenuItems,
    staffMenuCategories,
    adminMenuCategories,
    allAccessibleMenuItems,
    accessLevel,
    hasAdminAccess,
    debugPermissions
  } = useMenuAccess();

  const handleDebugPermissions = () => {
    debugPermissions();
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-500" />
              Not Authenticated
            </CardTitle>
            <CardDescription>
              Please log in to test menu access permissions.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Menu Access Control Test</h1>
        <Button onClick={handleDebugPermissions} variant="outline">
          <Eye className="h-4 w-4 mr-2" />
          Debug Permissions
        </Button>
      </div>

      {/* User Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Username</label>
              <p className="text-lg font-semibold">{user.username}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Access Level</label>
              <p className="text-lg font-semibold">{accessLevel}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">User Type</label>
              <p className="text-lg font-semibold">{userType}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Admin Access</label>
              <div className="flex items-center gap-2">
                {hasAdminAccess ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                <span>{hasAdminAccess ? 'Yes' : 'No'}</span>
              </div>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Badge variant={isRegularUser ? "default" : "secondary"}>
                Regular User: {isRegularUser ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isStaff ? "default" : "secondary"}>
                Staff: {isStaff ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isSuperuser ? "default" : "secondary"}>
                Superuser: {isSuperuser ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-500">Permissions Count</label>
            <p className="text-lg font-semibold">{user.permissions?.length || 0} permissions</p>
          </div>
        </CardContent>
      </Card>

      {/* Applicant Menu Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Applicant Dashboard Menus ({applicantMenuItems.length})
          </CardTitle>
          <CardDescription>
            Menu items available in the applicant dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {applicantMenuItems.map((item) => (
              <div key={item.id} className="flex items-center gap-3 p-3 border rounded-lg">
                <item.icon className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="font-medium">{item.name}</p>
                  <p className="text-sm text-gray-500">{item.path}</p>
                </div>
              </div>
            ))}
          </div>
          {applicantMenuItems.length === 0 && (
            <p className="text-gray-500 text-center py-4">No applicant menu items accessible</p>
          )}
        </CardContent>
      </Card>

      {/* Staff Menu Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Staff Dashboard Menus ({staffMenuCategories.length} categories)
          </CardTitle>
          <CardDescription>
            Menu categories and items available in the staff dashboard
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {staffMenuCategories.map((category) => (
            <div key={category.id} className="border rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <category.icon className="h-5 w-5 text-green-500" />
                <h3 className="font-semibold">{category.name}</h3>
                <Badge variant="outline">{category.items.length} items</Badge>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 ml-8">
                {category.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                    <item.icon className="h-4 w-4 text-gray-600" />
                    <span className="text-sm">{item.name}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
          {staffMenuCategories.length === 0 && (
            <p className="text-gray-500 text-center py-4">No staff menu categories accessible</p>
          )}
        </CardContent>
      </Card>

      {/* Admin Menu Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Admin Dashboard Menus ({adminMenuCategories.length} categories)
          </CardTitle>
          <CardDescription>
            Menu categories and items available for administrators
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {adminMenuCategories.map((category) => (
            <div key={category.id} className="border rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <category.icon className="h-5 w-5 text-red-500" />
                <h3 className="font-semibold">{category.name}</h3>
                <Badge variant="outline">{category.items.length} items</Badge>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 ml-8">
                {category.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                    <item.icon className="h-4 w-4 text-gray-600" />
                    <span className="text-sm">{item.name}</span>
                    {item.badge && <Badge variant="secondary" className="text-xs">{item.badge}</Badge>}
                  </div>
                ))}
              </div>
            </div>
          ))}
          {adminMenuCategories.length === 0 && (
            <p className="text-gray-500 text-center py-4">No admin menu categories accessible</p>
          )}
        </CardContent>
      </Card>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Access Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{applicantMenuItems.length}</p>
              <p className="text-sm text-blue-600">Applicant Menus</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{staffMenuCategories.length}</p>
              <p className="text-sm text-green-600">Staff Categories</p>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <p className="text-2xl font-bold text-red-600">{adminMenuCategories.length}</p>
              <p className="text-sm text-red-600">Admin Categories</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">{allAccessibleMenuItems.length}</p>
              <p className="text-sm text-purple-600">Total Accessible</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MenuAccessTest;
