import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI } from '@/services/api';
import { toast } from 'sonner';
import { logAuthState } from '@/utils/authDebug';
import { useAuth } from '@/contexts/AuthContext';

// Types for RBAC
export interface RBACUser {
  user_id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  is_admin: boolean;
  roles: string[];
  permissions: string[];
  has_staff_roles: boolean;
  is_staff_with_groups: boolean;
  can_access_admin: boolean;
  department?: string;
  last_login?: string;
  date_joined: string;
}

export interface RBACContextType {
  user: RBACUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Role checking functions
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  hasAllRoles: (roles: string[]) => boolean;
  
  // Permission checking functions
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  
  // Access level checks
  isSuperuser: boolean;
  isAdmin: boolean;
  isStaff: boolean;
  canAccessAdmin: boolean;
  isStaffWithGroups: boolean;
  
  // Utility functions
  refreshUserData: () => Promise<void>;
  checkPermissionAsync: (permission: string) => Promise<boolean>;
  checkRoleAsync: (role: string) => Promise<boolean>;
  
  // Security validation
  validateAccess: () => Promise<boolean>;
}

const RBACContext = createContext<RBACContextType | undefined>(undefined);

export const useRBAC = () => {
  const context = useContext(RBACContext);
  if (!context) {
    throw new Error('useRBAC must be used within an RBACProvider');
  }
  return context;
};

interface RBACProviderProps {
  children: ReactNode;
}

export const RBACProvider: React.FC<RBACProviderProps> = ({ children }) => {
  const [user, setUser] = useState<RBACUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Use existing authentication state
  const authContext = useAuth();
  const isAuthenticated = authContext.isAuthenticated;

  // Initialize RBAC data based on existing auth state
  useEffect(() => {
    const initializeRBAC = async () => {
      if (!isAuthenticated || authContext.isLoading) {
        setIsLoading(false);
        return;
      }

      try {
        await refreshUserData();
        logAuthState('RBAC Initialization Success', user);
      } catch (error) {
        console.error('Failed to initialize RBAC:', error);
        logAuthState('RBAC Initialization Failed', null, { error: error?.message });
        // Don't clear auth state here - let AuthContext handle it
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeRBAC();
  }, [isAuthenticated, authContext.isLoading]);

  // Refresh user data from backend with fallback
  const refreshUserData = async (): Promise<void> => {
    try {
      let response;
      let userData: RBACUser;

      // Try new RBAC endpoint first, fallback to current user endpoint
      try {
        response = await authAPI.getRBACInfo();
        userData = response.data as RBACUser;
      } catch (rbacError) {
        console.warn('RBAC endpoint failed, falling back to current user endpoint:', rbacError);
        // Fallback to the existing current user endpoint
        response = await authAPI.getCurrentUser();
        const basicUserData = response.data;

        // Transform basic user data to RBAC format
        userData = {
          user_id: basicUserData.id,
          username: basicUserData.username,
          email: basicUserData.email,
          first_name: basicUserData.first_name || '',
          last_name: basicUserData.last_name || '',
          is_active: basicUserData.is_active,
          is_staff: basicUserData.is_staff,
          is_superuser: basicUserData.is_superuser,
          is_admin: basicUserData.is_admin || basicUserData.is_superuser,
          roles: basicUserData.roles || [],
          permissions: basicUserData.permissions || [],
          has_staff_roles: (basicUserData.roles && basicUserData.roles.length > 0) || false,
          is_staff_with_groups: basicUserData.is_staff && ((basicUserData.roles && basicUserData.roles.length > 0) || basicUserData.is_superuser),
          can_access_admin: basicUserData.is_admin || basicUserData.is_superuser,
          department: basicUserData.department,
          last_login: basicUserData.last_login,
          date_joined: basicUserData.date_joined || new Date().toISOString(),
        };
      }

      // Dynamic role assignment for superusers
      if (userData.is_superuser) {
        // Superusers get all access regardless of role assignments
        userData.can_access_admin = true;
        userData.is_staff_with_groups = true;
        userData.has_staff_roles = true;
        console.log('🔥 Superuser detected - granting full access');
        logAuthState('Superuser Access Granted', userData);
      } else if (userData.is_staff && !userData.has_staff_roles) {
        // Only warn for non-superuser staff without roles
        console.warn('Security Warning: Staff user without assigned roles detected');
        logAuthState('Staff User Access Denied', userData, { reason: 'No roles assigned' });
        // Don't throw error for now, just log warning
        console.warn('Staff user without roles - allowing access for debugging');
      } else {
        logAuthState('Regular User Access', userData);
      }

      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));

    } catch (error) {
      console.error('Failed to refresh user data:', error);
      throw error;
    }
  };

  // Role checking functions
  const hasRole = (role: string): boolean => {
    return user?.roles?.includes(role) || false;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  const hasAllRoles = (roles: string[]): boolean => {
    return roles.every(role => hasRole(role));
  };

  // Permission checking functions
  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  // Async permission/role checking with backend validation
  const checkPermissionAsync = async (permission: string): Promise<boolean> => {
    try {
      const response = await authAPI.checkPermission(permission);
      return response.data.has_permission || false;
    } catch (error) {
      console.error('Failed to check permission:', error);
      return false;
    }
  };

  const checkRoleAsync = async (role: string): Promise<boolean> => {
    try {
      const response = await authAPI.checkRole(role);
      return response.data.has_role || false;
    } catch (error) {
      console.error('Failed to check role:', error);
      return false;
    }
  };

  // Security validation
  const validateAccess = async (): Promise<boolean> => {
    if (!user) return false;
    
    try {
      // Refresh user data to ensure current permissions
      await refreshUserData();
      
      // Additional security checks
      if (user.is_staff && !user.has_staff_roles) {
        console.error('Security violation: Staff user without roles');
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Access validation failed:', error);
      return false;
    }
  };

  // Computed access level properties
  const isSuperuser = user?.is_superuser || false;
  const isAdmin = user?.is_admin || false;
  const isStaff = user?.is_staff || false;
  const canAccessAdmin = user?.can_access_admin || false;
  const isStaffWithGroups = user?.is_staff_with_groups || false;

  const value: RBACContextType = {
    user,
    isLoading,
    isAuthenticated,
    
    // Role checking
    hasRole,
    hasAnyRole,
    hasAllRoles,
    
    // Permission checking
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    
    // Access levels
    isSuperuser,
    isAdmin,
    isStaff,
    canAccessAdmin,
    isStaffWithGroups,
    
    // Utility functions
    refreshUserData,
    checkPermissionAsync,
    checkRoleAsync,
    validateAccess,
  };

  return (
    <RBACContext.Provider value={value}>
      {children}
    </RBACContext.Provider>
  );
};

export default RBACContext;
