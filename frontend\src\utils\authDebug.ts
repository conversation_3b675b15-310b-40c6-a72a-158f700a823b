/**
 * Authentication debugging utilities
 */

export const logAuthState = (context: string, user: any, additionalInfo?: any) => {
  console.group(`🔐 Auth Debug: ${context}`);
  console.log('User:', user);
  console.log('Is Superuser:', user?.is_superuser);
  console.log('Is Staff:', user?.is_staff);
  console.log('Is Admin:', user?.is_admin);
  console.log('Roles:', user?.roles);
  console.log('Permissions:', user?.permissions?.length || 0, 'permissions');
  console.log('Can Access Admin:', user?.can_access_admin);
  console.log('Is Staff With Groups:', user?.is_staff_with_groups);
  console.log('Has Staff Roles:', user?.has_staff_roles);
  
  if (additionalInfo) {
    console.log('Additional Info:', additionalInfo);
  }
  
  console.groupEnd();
};

export const logRouteAccess = (route: string, hasAccess: boolean, reason?: string) => {
  const emoji = hasAccess ? '✅' : '❌';
  console.log(`${emoji} Route Access: ${route} - ${hasAccess ? 'GRANTED' : 'DENIED'}${reason ? ` (${reason})` : ''}`);
};

export const debugPermissionCheck = (permission: string, result: boolean, user: any) => {
  console.group(`🔑 Permission Check: ${permission}`);
  console.log('Result:', result ? '✅ GRANTED' : '❌ DENIED');
  console.log('User is superuser:', user?.is_superuser);
  console.log('User permissions:', user?.permissions?.includes(permission) ? '✅ Has permission' : '❌ Missing permission');
  console.groupEnd();
};

export const debugRoleCheck = (role: string, result: boolean, user: any) => {
  console.group(`👤 Role Check: ${role}`);
  console.log('Result:', result ? '✅ HAS ROLE' : '❌ MISSING ROLE');
  console.log('User is superuser:', user?.is_superuser);
  console.log('User roles:', user?.roles);
  console.log('Has role:', user?.roles?.includes(role) ? '✅ Yes' : '❌ No');
  console.groupEnd();
};

export default {
  logAuthState,
  logRouteAccess,
  debugPermissionCheck,
  debugRoleCheck,
};
