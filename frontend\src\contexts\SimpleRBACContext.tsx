import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { authAPI } from '@/services/api';
import { useAuth } from './AuthContext';
import { useRedirectLoopPrevention } from '@/utils/redirectLoopPrevention';

// Simplified user types
export enum UserType {
  SUPERUSER = 'Superuser',
  STAFF = 'Staff', 
  USER = 'User'
}

interface SimpleRBACContextType {
  // Basic user info
  userType: UserType;
  isLoading: boolean;
  isAuthenticated: boolean;

  // Simple access checks
  isSuperuser: boolean;
  isStaff: boolean;
  isRegularUser: boolean;

  // Basic permission check
  canAccessAdmin: boolean;

  refreshUserData: () => Promise<void>;
}

const SimpleRBACContext = createContext<SimpleRBACContextType>({
  userType: UserType.USER,
  isLoading: true,
  isAuthenticated: false,
  isSuperuser: false,
  isStaff: false,
  isRegularUser: true,
  canAccessAdmin: false,
  refreshUserData: async () => {},
});

export const useRBAC = () => {
  const context = useContext(SimpleRBACContext);
  if (!context) {
    throw new Error('useRBAC must be used within a SimpleRBACProvider');
  }
  return context;
};

// Keep the old name for backward compatibility
export const useSimpleRBAC = useRBAC;

interface SimpleRBACProviderProps {
  children: ReactNode;
}

export const SimpleRBACProvider: React.FC<SimpleRBACProviderProps> = ({ children }) => {
  const authContext = useAuth();
  const { user, isAuthenticated, isLoading: authLoading } = authContext;

  // Use auth context loading state directly to prevent additional state management
  const isLoading = authLoading;

  // Simple refresh function that delegates to auth context
  const refreshUserData = async (): Promise<void> => {
    // Since we're using the auth context, we don't need to do anything here
    // The auth context handles user data refreshing
    console.log('SimpleRBAC: refreshUserData called - delegating to AuthContext');
  };

  // Debug logging
  useEffect(() => {
    console.log('SimpleRBACProvider state:', {
      isAuthenticated,
      isLoading,
      user: user ? { username: user.username, is_staff: user.is_staff, is_superuser: user.is_superuser } : null
    });
  }, [isAuthenticated, isLoading, user]);

  // Simple user type determination
  const userType = useMemo((): UserType => {
    if (!user) return UserType.USER;
    if (user.is_superuser) return UserType.SUPERUSER;
    if (user.is_staff) return UserType.STAFF;
    return UserType.USER;
  }, [user]);

  // Simple access checks
  const isSuperuser = useMemo(() => Boolean(user?.is_superuser), [user]);
  const isStaff = useMemo(() => Boolean(user?.is_staff), [user]);
  const isRegularUser = useMemo(() => !user?.is_staff && !user?.is_superuser, [user]);
  
  // Admin access: superusers and staff users can access admin areas
  const canAccessAdmin = useMemo(() => Boolean(user?.is_superuser || user?.is_staff), [user]);

  const value: SimpleRBACContextType = {
    userType,
    isLoading,
    isAuthenticated,
    isSuperuser,
    isStaff,
    isRegularUser,
    canAccessAdmin,
    refreshUserData,
  };

  return (
    <SimpleRBACContext.Provider value={value}>
      {children}
    </SimpleRBACContext.Provider>
  );
};

// Simple route protection components
interface SimpleProtectedRouteProps {
  children: ReactNode;
  requireSuperuser?: boolean;
  requireStaff?: boolean;
  fallback?: ReactNode;
}

export const SimpleProtectedRoute: React.FC<SimpleProtectedRouteProps> = ({
  children,
  requireSuperuser = false,
  requireStaff = false,
  fallback = <div className="text-center py-8">Access denied</div>
}) => {
  const { isSuperuser, isStaff, isLoading } = useSimpleRBAC();

  if (isLoading) {
    return <div className="text-center py-8">Loading...</div>;
  }

  // Check access
  if (requireSuperuser && !isSuperuser) {
    return <>{fallback}</>;
  }

  if (requireStaff && !isStaff && !isSuperuser) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Convenience components
export const SuperuserOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <SimpleProtectedRoute requireSuperuser fallback={fallback}>
    {children}
  </SimpleProtectedRoute>
);

export const StaffOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({
  children,
  fallback
}) => (
  <SimpleProtectedRoute requireStaff fallback={fallback}>
    {children}
  </SimpleProtectedRoute>
);

export const AdminOnly: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isSuperuser, isStaff, isLoading, isAuthenticated } = useSimpleRBAC();
  const location = useLocation();
  const { canRedirect } = useRedirectLoopPrevention();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    // Check if redirect is allowed (prevents loops)
    if (!canRedirect(location.pathname, '/login')) {
      console.warn('AdminOnly: Redirect loop prevented, showing access denied instead');
      return (
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto text-center">
            <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
            <p className="text-muted-foreground">
              Please log in to access this page.
            </p>
            <div className="mt-4">
              <a href="/login" className="text-blue-600 hover:underline">
                Go to Login
              </a>
            </div>
          </div>
        </div>
      );
    }

    console.log('AdminOnly: Redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // Allow superusers and staff users
  if (isSuperuser || isStaff) {
    console.log('AdminOnly: Access granted', { isSuperuser, isStaff });
    return <>{children}</>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto text-center">
        <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
        <p className="text-muted-foreground">
          This page requires administrative privileges.
        </p>
        <div className="mt-4 text-xs text-muted-foreground">
          <p>Debug: Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
          <p>Debug: Superuser: {isSuperuser ? 'Yes' : 'No'}, Staff: {isStaff ? 'Yes' : 'No'}</p>
        </div>
      </div>
    </div>
  );
};

export default SimpleRBACContext;
