import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Settings,
  Menu,
  X,
  LogOut,
  Home,
  ChevronDown,
  ChevronRight,
  UserCheck,
  FileText,
  Globe,
  KeyRound,
  Bell,
  Search,
  Calendar,
  HelpCircle,
  Shield,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import ChangePasswordModal from '@/components/ChangePasswordModal';
import { settingsAPI } from '@/services/api';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<number | null>(null);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);

  // State for organization settings
  const [settings, setSettings] = useState<{
    systemName: string;
    organizationName: string;
  }>({
    systemName: '',
    organizationName: ''
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Fetch organization settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          setSettings({
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || ''
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Get user data
  const userDataString = localStorage.getItem('user');
  const userData = userDataString ? JSON.parse(userDataString) : null;
  const userRole = userData?.is_superuser ? 'Super Admin' : userData?.is_staff ? 'Staff' : 'User';
  const userName = userData?.first_name && userData?.last_name
    ? `${userData.first_name} ${userData.last_name}`
    : userData?.username || 'Admin User';

  const handleLogout = async () => {
    try {
      // Use the AuthContext logout function to properly clear state
      await logout();
      toast.success('Logged out successfully');
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Logout failed. Please try again.');
    }
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Function to handle menu item clicks
  const handleMenuItemClick = (_index: number, isSubmenuItem: boolean = false, parentIndex?: number) => {
    // If clicking on a non-submenu item, close any open submenu
    if (!isSubmenuItem) {
      setActiveSubmenu(null);
    }
    // If clicking on a submenu item, keep the parent menu open
    // parentIndex is provided to identify which parent menu should stay open
    else if (parentIndex !== undefined) {
      setActiveSubmenu(parentIndex);
    }
  };

  // Set active submenu based on current location
  useEffect(() => {
    if (location.pathname.startsWith('/application-information')) {
      setActiveSubmenu(2); // Application Portal submenu index
    }
  }, [location.pathname]);

  const menuItems = [
    {
      title: 'Dashboard',
      icon: <Home className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 0,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 0 ? null : 0),
      items: [
        {
          title: 'Graduation Dashboard',
          path: '/graduate-admin?tab=dashboard',
          active: location.pathname === '/graduate-admin' && (location.search === '' || location.search === '?tab=dashboard')
        },
        {
          title: 'Application Dashboard',
          path: '/graduate-admin?tab=application-dashboard',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-dashboard'
        },
        {
          title: 'Service Fee Dashboard',
          path: '/graduate-admin?tab=service-fee-dashboard',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=service-fee-dashboard'
        }
      ]
    },

    {
      title: 'Graduate Verification',
      icon: <UserCheck className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 1,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 1 ? null : 1),
      items: [

        {
          title: 'Manage Graduates',
          path: '/graduate-admin?tab=manage',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=manage'
        },
        {
          title: 'Manage Colleges',
          path: '/graduate-admin?tab=colleges',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=colleges'
        },
        {
          title: 'Manage Departments',
          path: '/graduate-admin?tab=departments',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=departments'
        },
        {
          title: 'Manage Fields of Study',
          path: '/graduate-admin?tab=fields-of-study',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=fields-of-study'
        },
        {
          title: 'Graduate Fields of Study',
          path: '/graduate-admin?tab=graduate-fields-of-study',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=graduate-fields-of-study'
        },
        {
          title: 'Manage Admission Classifications',
          path: '/graduate-admin?tab=admission-classifications',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=admission-classifications'
        },
        {
          title: 'Manage Programs',
          path: '/graduate-admin?tab=programs',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=programs'
        }
      ]
    },
    {
      title: 'Application Portal',
      icon: <FileText className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 2 || location.pathname.startsWith('/application-information'),
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 2 ? null : 2),
      items: [
        {
          title: 'Manage Colleges',
          path: '/graduate-admin?tab=application-colleges',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-colleges'
        },
        {
          title: 'Manage Departments',
          path: '/graduate-admin?tab=application-departments',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-departments'
        },
        {
          title: 'Manage Programs',
          path: '/graduate-admin?tab=application-programs',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-programs'
        },
        {
          title: 'Manage Study Programs',
          path: '/graduate-admin?tab=application-study-programs',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-study-programs'
        },
        {
          title: 'Manage Admission Types',
          path: '/graduate-admin?tab=application-admission-types',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-admission-types'
        },

        {
          title: 'Manage Registration Periods',
          path: '/graduate-admin?tab=application-registration-periods',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-registration-periods'
        },
        {
          title: 'Manage Fields of Study',
          path: '/graduate-admin?tab=application-fields-of-study',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-fields-of-study'
        },
        {
          title: 'Application Information',
          path: '/graduate-admin?tab=application-information',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=application-information'
        },
        {
          title: 'Downloadable Content',
          path: '/graduate-admin?tab=downloadable-content',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=downloadable-content'
        }
      ]
    },
    {
      title: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      path: '/graduate-admin?tab=settings',
      active: location.pathname === '/graduate-admin' && location.search === '?tab=settings'
    },
    {
      title: 'Public Site',
      icon: <Globe className="h-5 w-5" />,
      path: '/',
      active: false,
      external: true
    }
  ];

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Mobile menu backdrop */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={toggleMobileMenu}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-72 sm:w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-auto lg:z-auto",
          mobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Sidebar Header */}
          <div className="flex items-center justify-between h-16 px-4 border-b bg-gradient-to-r from-[#1a73c0] to-[#0e4a7d] text-white">
            <Link to="/graduate-admin" className="flex items-center space-x-2">
              <div className="h-9 w-9 rounded-full bg-white/10 border border-white/20 flex items-center justify-center shadow-sm">
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <span className="text-lg font-bold">{settings.systemName || 'Admin Portal'}</span>
            </Link>
            <button
              className="lg:hidden p-2 rounded-md hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-white/20"
              onClick={toggleMobileMenu}
              aria-label="Close menu"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Sidebar Content */}
          <div className="flex-1 overflow-y-auto py-4 px-3">
            <nav className="space-y-1">
              {menuItems.map((item, index) => (
                <div key={index}>
                  {item.submenu ? (
                    <div className="space-y-1">
                      <button
                          onClick={item.toggleSubmenu}
                          className={cn(
                            "flex items-center w-full px-3 py-2 text-sm font-medium rounded-md group transition-colors",
                            item.active
                              ? "bg-[#1a73c0]/10 text-[#1a73c0] border-l-4 border-[#1a73c0] font-medium"
                              : "text-gray-700 hover:bg-gray-100"
                          )}
                        >
                        <span className="mr-3">{item.icon}</span>
                        <span className="flex-1 text-left">{item.title}</span>
                        <span>
                          {item.submenuOpen ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </span>
                      </button>

                      {item.submenuOpen && (
                        <div className="pl-10 space-y-1">
                          {/* Submenu items */}
                          {item.items?.map((subItem, subIndex) => (
                            <Link
                              key={subIndex}
                              to={subItem.path}
                              onClick={() => handleMenuItemClick(index, true, index)}
                              className={cn(
                                "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                                subItem.active
                                  ? "bg-[#1a73c0]/10 text-[#1a73c0] border-l-4 border-[#1a73c0] font-medium"
                                  : "text-gray-600 hover:bg-gray-100"
                              )}
                            >
                              <span>
                                {subItem.title}
                              </span>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                        to={item.path}
                        target={item.external ? "_blank" : undefined}
                        onClick={() => handleMenuItemClick(index)}
                        className={cn(
                          "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                          item.active
                            ? "bg-[#1a73c0]/10 text-[#1a73c0] border-l-4 border-[#1a73c0] font-medium"
                            : "text-gray-700 hover:bg-gray-100"
                        )}
                      >
                        <span className="mr-3">{item.icon}</span>
                        {item.title}
                      </Link>
                  )}
                </div>
              ))}
            </nav>
          </div>

          {/* Sidebar Footer */}
          <div className="p-4 border-t border-gray-200 md:hidden bg-gray-50">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 justify-center text-gray-700 hover:text-[#1a73c0] hover:bg-[#1a73c0]/10 border-gray-200 shadow-sm"
                onClick={() => setIsPasswordModalOpen(true)}
              >
                <KeyRound className="h-4 w-4 mr-1.5" />
                <span className="text-xs font-medium">Password</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex-1 justify-center text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 shadow-sm"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 mr-1.5" />
                <span className="text-xs font-medium">Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden min-w-0">
        {/* Top Navbar */}
        <header className="bg-gradient-to-r from-[#1a73c0] to-[#0e4a7d] shadow-md z-10">
          <div className="flex items-center justify-between h-16 px-4 md:px-6">
            {/* Left side - Title and mobile menu */}
            <div className="flex items-center">
              <button
                className="p-2 rounded-md text-white hover:text-white hover:bg-white/10 lg:hidden transition-colors focus:outline-none focus:ring-2 focus:ring-white/20"
                onClick={toggleMobileMenu}
                aria-label="Toggle mobile menu"
              >
                <Menu className="h-6 w-6" />
              </button>

              <div className="ml-4 overflow-hidden">
                <h1 className="text-xl font-semibold text-white truncate">
                  {location.pathname === '/graduate-admin' ? `${settings.systemName}` :
                   location.pathname === '/graduate-reports' ? 'Graduate Reports' :
                   location.pathname === '/application-information' ? 'Application Information' : `${settings.systemName}`}
                </h1>
              </div>
            </div>

            {/* Right side - Actions and user profile */}
            <div className="flex items-center space-x-3 md:space-x-4">
              {/* Search button - visible on larger screens */}
              <button className="hidden md:flex items-center justify-center h-8 w-8 rounded-full bg-white/10 text-white hover:bg-white/20 transition-colors">
                <Search className="h-4 w-4" />
              </button>

              {/* Help button - visible on larger screens */}
              <button className="hidden md:flex items-center justify-center h-8 w-8 rounded-full bg-white/10 text-white hover:bg-white/20 transition-colors">
                <HelpCircle className="h-4 w-4" />
              </button>

              {/* Notifications button */}
              <button className="flex items-center justify-center h-8 w-8 rounded-full bg-white/10 text-white hover:bg-white/20 transition-colors relative">
                <Bell className="h-4 w-4" />
                <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
              </button>

              {/* Logout button - always visible */}
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-red-600/30 rounded-md transition-colors flex items-center"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Sign Out</span>
              </Button>

              {/* Online status indicator */}
              <div className="hidden sm:flex items-center">
                <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800 shadow-sm">
                  <span className="h-2 w-2 mr-1 rounded-full bg-emerald-500 animate-pulse"></span>
                  Online
                </span>
              </div>

              {/* Mobile user menu trigger */}
              <div className="md:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button
                      className="h-9 w-9 rounded-full bg-white/10 border border-white/20 flex items-center justify-center text-white font-medium shadow-sm hover:bg-white/20 transition-colors"
                      aria-label="User menu"
                    >
                      {userName.charAt(0).toUpperCase()}
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="font-medium">{userName}</p>
                        <p className="text-xs text-muted-foreground">{userRole}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setIsPasswordModalOpen(true)}>
                      <KeyRound className="mr-2 h-4 w-4" />
                      <span>Change Password</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign Out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Desktop user profile and dropdown */}
              <div className="hidden md:block border-l border-white/20 pl-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div className="flex items-center space-x-2 cursor-pointer hover:bg-white/10 px-3 py-1.5 rounded-full transition-colors">
                      <div className="h-9 w-9 rounded-full bg-white/10 border border-white/20 flex items-center justify-center text-white font-medium shadow-sm">
                        {userName.charAt(0).toUpperCase()}
                      </div>
                      <div className="hidden lg:block text-white">
                        <p className="text-sm font-medium truncate">{userName}</p>
                        <div className="flex items-center">
                          <Shield className="h-3 w-3 mr-1 text-emerald-300" />
                          <p className="text-xs truncate text-emerald-200">{userRole}</p>
                        </div>
                      </div>
                      <ChevronDown className="h-4 w-4 text-white/70 hidden lg:block" />
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56 mt-1">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="font-medium">{userName}</p>
                        <div className="flex items-center">
                          <Shield className="h-3 w-3 mr-1 text-emerald-600" />
                          <p className="text-xs text-muted-foreground">{userRole}</p>
                        </div>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setIsPasswordModalOpen(true)} className="cursor-pointer">
                      <KeyRound className="mr-2 h-4 w-4" />
                      <span>Change Password</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600 cursor-pointer">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Sign Out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 p-2 sm:p-4 md:p-6 pb-0 flex flex-col">
          <div className="flex-1 max-w-full overflow-x-auto">
            <div className="w-full mx-auto container-fluid">
              <div className="md:max-w-[1200px] lg:max-w-[1400px] mx-auto">
                {children}
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* Password Change Modal */}
      <ChangePasswordModal
        isOpen={isPasswordModalOpen}
        onClose={() => setIsPasswordModalOpen(false)}
      />
    </div>
  );
};

export default AdminLayout;
