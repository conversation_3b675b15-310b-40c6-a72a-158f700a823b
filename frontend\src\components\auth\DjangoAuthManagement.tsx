import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Users, 
  Shield, 
  Key, 
  Plus, 
  Edit, 
  Trash2,
  Search, 
  RefreshCw,
  UserPlus,
  UserMinus,
  Settings,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';
import { useSimpleRBAC } from '@/contexts/SimpleRBACContext';
import { userAPI } from '@/services/api';
import { DjangoUser, DjangoGroup, DjangoPermission, DjangoContentType } from '@/services/djangoAuthAPI';

const DjangoAuthManagement: React.FC = () => {
  const { isSuperuser, isStaff } = useSimpleRBAC();
  const [activeTab, setActiveTab] = useState('users');
  
  // State for users
  const [users, setUsers] = useState<DjangoUser[]>([]);
  const [usersLoading, setUsersLoading] = useState(true);
  
  // State for groups
  const [groups, setGroups] = useState<DjangoGroup[]>([]);
  const [groupsLoading, setGroupsLoading] = useState(true);
  
  // State for permissions
  const [permissions, setPermissions] = useState<DjangoPermission[]>([]);
  const [contentTypes, setContentTypes] = useState<DjangoContentType[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(true);
  
  // State for stats
  const [stats, setStats] = useState({
    total_users: 0,
    active_users: 0,
    staff_users: 0,
    superusers: 0,
    total_groups: 0,
    total_permissions: 0
  });
  
  // Dialog states
  const [showUserDialog, setShowUserDialog] = useState(false);
  const [showGroupDialog, setShowGroupDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<DjangoUser | null>(null);
  const [editingGroup, setEditingGroup] = useState<DjangoGroup | null>(null);
  
  // Form states
  const [userForm, setUserForm] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    is_active: true,
    is_staff: false,
    is_superuser: false,
    groups: [] as number[],
    user_permissions: [] as number[]
  });
  
  const [groupForm, setGroupForm] = useState({
    name: '',
    permissions: [] as number[]
  });
  
  // Search and filter states
  const [userSearch, setUserSearch] = useState('');
  const [groupSearch, setGroupSearch] = useState('');
  const [permissionSearch, setPermissionSearch] = useState('');
  const [selectedContentType, setSelectedContentType] = useState('all');

  useEffect(() => {
    const loadAllData = async () => {
      await Promise.all([
        loadUsers(),
        loadGroups(),
        loadPermissions()
      ]);
      // Calculate stats after data is loaded
      loadStats();
    };

    loadAllData();
  }, []);

  // Recalculate stats when data changes
  useEffect(() => {
    loadStats();
  }, [users, groups, permissions]);

  const loadUsers = async () => {
    try {
      setUsersLoading(true);
      const response = await userAPI.getAllUsers();
      // Your backend returns data directly, not in a results wrapper
      const userData = Array.isArray(response.data) ? response.data : [];
      setUsers(userData);
    } catch (error) {
      // Silently handle API unavailability
      setUsers([]);
    } finally {
      setUsersLoading(false);
    }
  };

  const loadGroups = async () => {
    try {
      setGroupsLoading(true);
      const response = await userAPI.getAllRoles();
      console.log('Groups API response:', response);
      // Your backend returns data directly, not in a results wrapper
      const groupData = Array.isArray(response.data) ? response.data : [];
      console.log('Processed groups data:', groupData);
      setGroups(groupData);
    } catch (error) {
      console.error('Error loading groups:', error);
      setGroups([]);
    } finally {
      setGroupsLoading(false);
    }
  };

  const loadPermissions = async () => {
    try {
      setPermissionsLoading(true);
      const [permissionsResponse, contentTypesResponse] = await Promise.all([
        userAPI.getAllPermissions(),
        userAPI.getAllContentTypes()
      ]);
      console.log('Permissions API response:', permissionsResponse);
      console.log('Content Types API response:', contentTypesResponse);
      // Your backend returns data directly, not in a results wrapper
      const permissionData = Array.isArray(permissionsResponse.data) ? permissionsResponse.data : [];
      const contentTypeData = Array.isArray(contentTypesResponse.data) ? contentTypesResponse.data : [];
      console.log('Processed permissions data:', permissionData);
      console.log('Processed content types data:', contentTypeData);
      setPermissions(permissionData);
      setContentTypes(contentTypeData);
    } catch (error) {
      console.error('Error loading permissions:', error);
      setPermissions([]);
      setContentTypes([]);
    } finally {
      setPermissionsLoading(false);
    }
  };

  const loadStats = async () => {
    // Calculate stats from loaded data since there's no dedicated stats endpoint
    const safeUsers = Array.isArray(users) ? users : [];
    const safeGroups = Array.isArray(groups) ? groups : [];
    const safePermissions = Array.isArray(permissions) ? permissions : [];

    setStats({
      total_users: safeUsers.length,
      active_users: safeUsers.filter(u => u.is_active).length,
      staff_users: safeUsers.filter(u => u.is_staff).length,
      superusers: safeUsers.filter(u => u.is_superuser).length,
      total_groups: safeGroups.length,
      total_permissions: safePermissions.length
    });
  };

  const handleCreateUser = async () => {
    try {
      await userAPI.createUser(userForm);
      toast.success('User created successfully');
      setShowUserDialog(false);
      resetUserForm();
      loadUsers();
      loadStats();
    } catch (error) {
      toast.error('Failed to create user');
      console.error('Error creating user:', error);
    }
  };

  const handleUpdateUser = async () => {
    if (!editingUser) return;

    try {
      await userAPI.updateUser(editingUser.id, userForm);
      toast.success('User updated successfully');
      setShowUserDialog(false);
      setEditingUser(null);
      resetUserForm();
      loadUsers();
      loadStats();
    } catch (error) {
      toast.error('Failed to update user');
      console.error('Error updating user:', error);
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      await userAPI.deleteUser(userId);
      toast.success('User deleted successfully');
      loadUsers();
      loadStats();
    } catch (error) {
      toast.error('Failed to delete user');
      console.error('Error deleting user:', error);
    }
  };

  const handleCreateGroup = async () => {
    try {
      await userAPI.createRole(groupForm);
      toast.success('Group created successfully');
      setShowGroupDialog(false);
      resetGroupForm();
      loadGroups();
      loadStats();
    } catch (error) {
      toast.error('Failed to create group');
      console.error('Error creating group:', error);
    }
  };

  const handleUpdateGroup = async () => {
    if (!editingGroup) return;

    try {
      await userAPI.updateRole(editingGroup.id, groupForm);
      toast.success('Group updated successfully');
      setShowGroupDialog(false);
      setEditingGroup(null);
      resetGroupForm();
      loadGroups();
    } catch (error) {
      toast.error('Failed to update group');
      console.error('Error updating group:', error);
    }
  };

  const handleDeleteGroup = async (groupId: number) => {
    if (!confirm('Are you sure you want to delete this group?')) return;

    try {
      await userAPI.deleteRole(groupId);
      toast.success('Group deleted successfully');
      loadGroups();
      loadStats();
    } catch (error) {
      toast.error('Failed to delete group');
      console.error('Error deleting group:', error);
    }
  };

  const openUserDialog = (user?: DjangoUser) => {
    if (user) {
      setEditingUser(user);
      setUserForm({
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        password: '',
        is_active: user.is_active,
        is_staff: user.is_staff,
        is_superuser: user.is_superuser,
        groups: user.groups.map(g => g.id),
        user_permissions: user.user_permissions.map(p => p.id)
      });
    } else {
      setEditingUser(null);
      resetUserForm();
    }
    setShowUserDialog(true);
  };

  const openGroupDialog = (group?: DjangoGroup) => {
    if (group) {
      setEditingGroup(group);
      setGroupForm({
        name: group.name,
        permissions: group.permissions.map(p => p.id)
      });
    } else {
      setEditingGroup(null);
      resetGroupForm();
    }
    setShowGroupDialog(true);
  };

  const resetUserForm = () => {
    setUserForm({
      username: '',
      email: '',
      first_name: '',
      last_name: '',
      password: '',
      is_active: true,
      is_staff: false,
      is_superuser: false,
      groups: [],
      user_permissions: []
    });
  };

  const resetGroupForm = () => {
    setGroupForm({
      name: '',
      permissions: []
    });
  };

  const getStatusBadge = (user: DjangoUser) => {
    if (user.is_superuser) {
      return <Badge variant="destructive">Superuser</Badge>;
    }
    if (user.is_staff) {
      return <Badge variant="default">Staff</Badge>;
    }
    return <Badge variant="secondary">User</Badge>;
  };

  const getActiveBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="outline" className="bg-green-100 text-green-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-red-100 text-red-800">
        <XCircle className="h-3 w-3 mr-1" />
        Inactive
      </Badge>
    );
  };

  // Ensure arrays are always arrays (never undefined)
  const safeUsers = Array.isArray(users) ? users : [];
  const safeGroups = Array.isArray(groups) ? groups : [];
  const safePermissions = Array.isArray(permissions) ? permissions : [];

  const filteredUsers = safeUsers.filter(user =>
    user.username.toLowerCase().includes(userSearch.toLowerCase()) ||
    user.email.toLowerCase().includes(userSearch.toLowerCase()) ||
    `${user.first_name} ${user.last_name}`.toLowerCase().includes(userSearch.toLowerCase())
  );

  const filteredGroups = safeGroups.filter(group =>
    group.name.toLowerCase().includes(groupSearch.toLowerCase())
  );

  const filteredPermissions = safePermissions.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(permissionSearch.toLowerCase()) ||
                         permission.codename.toLowerCase().includes(permissionSearch.toLowerCase());
    const matchesContentType = !selectedContentType || selectedContentType === 'all' || permission.content_type.toString() === selectedContentType;
    return matchesSearch && matchesContentType;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Django Authentication & Authorization</h1>
          <p className="text-muted-foreground">
            Manage users, groups, and permissions using Django's built-in auth system
          </p>
          {!isSuperuser && isStaff && (
            <div className="mt-2">
              <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Staff Access - Some features may be limited
              </Badge>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isSuperuser ? "default" : "secondary"} className={isSuperuser ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}>
            <Shield className="h-3 w-3 mr-1" />
            {isSuperuser ? "Superuser Access" : "Staff Access"}
          </Badge>
          <Button variant="outline" size="sm" onClick={() => {
            loadUsers();
            loadGroups();
            loadPermissions();
            loadStats();
          }}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh All
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_users}</div>
            <p className="text-xs text-muted-foreground">
              {stats.active_users} active, {stats.staff_users} staff
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Groups</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_groups}</div>
            <p className="text-xs text-muted-foreground">
              Permission groups
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permissions</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_permissions}</div>
            <p className="text-xs text-muted-foreground">
              System permissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Users</span>
          </TabsTrigger>
          <TabsTrigger value="groups" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Groups</span>
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center space-x-2">
            <Key className="h-4 w-4" />
            <span>Permissions</span>
          </TabsTrigger>
        </TabsList>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="h-5 w-5" />
                    <span>User Management</span>
                  </CardTitle>
                  <CardDescription>
                    Manage Django users, their permissions and group memberships
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search users..."
                      className="pl-10 w-64"
                      value={userSearch}
                      onChange={(e) => setUserSearch(e.target.value)}
                    />
                  </div>
                  <Button onClick={() => openUserDialog()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add User
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Groups</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {usersLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                          Loading users...
                        </TableCell>
                      </TableRow>
                    ) : filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No users found</p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {user.first_name} {user.last_name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                @{user.username}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>{getActiveBadge(user.is_active)}</TableCell>
                          <TableCell>{getStatusBadge(user)}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {(user.roles || user.groups || []).slice(0, 2).map((group, index) => (
                                <Badge key={group.id || index} variant="outline" className="text-xs">
                                  {group.name}
                                </Badge>
                              ))}
                              {(user.roles || user.groups || []).length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{(user.roles || user.groups || []).length - 2} more
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openUserDialog(user)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              {isSuperuser && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteUser(user.id)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Groups Tab */}
        <TabsContent value="groups" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Group Management</span>
                  </CardTitle>
                  <CardDescription>
                    Manage Django groups and their permissions
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search groups..."
                      className="pl-10 w-64"
                      value={groupSearch}
                      onChange={(e) => setGroupSearch(e.target.value)}
                    />
                  </div>
                  <Button onClick={() => openGroupDialog()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Group
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Group Name</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {groupsLoading ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8">
                          <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                          Loading groups...
                        </TableCell>
                      </TableRow>
                    ) : filteredGroups.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8">
                          <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No groups found</p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredGroups.map((group) => (
                        <TableRow key={group.id}>
                          <TableCell>
                            <div className="font-medium">{group.name}</div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {(group.permissions || []).length} permissions
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-muted-foreground">
                              Django group with assigned permissions
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openGroupDialog(group)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              {isSuperuser && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteGroup(group.id)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Key className="h-5 w-5" />
                    <span>Permission Management</span>
                  </CardTitle>
                  <CardDescription>
                    View Django permissions and content types
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search permissions..."
                      className="pl-10 w-64"
                      value={permissionSearch}
                      onChange={(e) => setPermissionSearch(e.target.value)}
                    />
                  </div>
                  <Select value={selectedContentType} onValueChange={setSelectedContentType}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by content type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All content types</SelectItem>
                      {contentTypes.map((ct) => (
                        <SelectItem key={ct.id} value={ct.id.toString()}>
                          {ct.app_label}.{ct.model}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Permission Name</TableHead>
                      <TableHead>Codename</TableHead>
                      <TableHead>Content Type</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {permissionsLoading ? (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-8">
                          <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                          Loading permissions...
                        </TableCell>
                      </TableRow>
                    ) : filteredPermissions.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={3} className="text-center py-8">
                          <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>No permissions found</p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredPermissions.map((permission) => (
                        <TableRow key={permission.id}>
                          <TableCell>
                            <div className="font-medium">{permission.name}</div>
                          </TableCell>
                          <TableCell>
                            <code className="text-xs bg-muted px-1 py-0.5 rounded">
                              {permission.codename}
                            </code>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {contentTypes.find(ct => ct.id === permission.content_type)?.app_label || 'unknown'}.
                              {contentTypes.find(ct => ct.id === permission.content_type)?.model || 'unknown'}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DjangoAuthManagement;
