from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import logging
from settings_manager.models import OrganizationSetting

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Deactivate user accounts that have been inactive for a specified period'

    def handle(self, *args, **options):
        # Get the organization settings
        org_settings = OrganizationSetting.get_settings()
        inactivity_period = org_settings.account_inactivity_period
        
        # If inactivity period is 0 (disabled), do nothing
        if inactivity_period == 0:
            self.stdout.write(self.style.SUCCESS('Account inactivity check is disabled in settings. Exiting.'))
            return
        
        # Calculate the cutoff date
        cutoff_date = timezone.now() - timedelta(days=inactivity_period)
        
        # Find users who haven't logged in since the cutoff date and are still active
        inactive_users = User.objects.filter(
            is_active=True,
            last_login__lt=cutoff_date
        ).exclude(
            # Don't deactivate superusers
            is_superuser=True
        )
        
        # Count of users to be deactivated
        count = inactive_users.count()
        
        if count == 0:
            self.stdout.write(self.style.SUCCESS('No inactive users found.'))
            return
        
        # Deactivate the users
        for user in inactive_users:
            user.is_active = False
            user.save(update_fields=['is_active'])
            logger.info(f'Deactivated user {user.username} due to inactivity (last login: {user.last_login})')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully deactivated {count} user(s) who have been inactive for more than {inactivity_period} days.'
            )
        )
