from django.db import models
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator, EmailValidator
from django.contrib.auth.models import User
from decimal import Decimal
import uuid
import re
import string
import random


def generate_transaction_id():
    """Generate a unique 6-character alphanumeric transaction ID."""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))


class AlumniApplication(models.Model):
    """
    Complete Alumni Application Form (Form1) with destination logic.
    Includes all fields for comprehensive alumni service requests.
    """

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Personal Information Section
    first_name = models.CharField(
        max_length=50,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z\s\-\'\.]+$',
            message='First name must contain only letters, spaces, hyphens, apostrophes, and periods.'
        )],
        help_text="First name (letters only)"
    )
    father_name = models.Char<PERSON>ield(
        max_length=50,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z\s\-\'\.]+$',
            message='Father name must contain only letters, spaces, hyphens, apostrophes, and periods.'
        )],
        help_text="Father's name (letters only)"
    )
    last_name = models.CharField(
        max_length=50,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z\s\-\'\.]+$',
            message='Last name must contain only letters, spaces, hyphens, apostrophes, and periods.'
        )],
        help_text="Last name (letters only)"
    )
    student_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        unique=True,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z0-9/]+$',
            message='Student ID must be alphanumeric with forward slashes (e.g., uog/1254/21).'
        )],
        help_text="Student ID format: uog/1254/21 (optional)"
    )
    phone_number = models.CharField(
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+?[1-9]\d{1,14}$',
            message='Phone number must be in international format with country code.'
        )],
        help_text="International format with country code (e.g., +251912345678)"
    )
    email = models.EmailField(
        max_length=100,
        validators=[EmailValidator()],
        help_text="Valid email address"
    )

    # Academic Information Section
    ADMISSION_TYPE_CHOICES = [
        ('Regular', 'Regular'),
        ('Evening', 'Evening'),
        ('Summer', 'Summer'),
        ('Distance', 'Distance'),
    ]

    DEGREE_TYPE_CHOICES = [
        ('Diploma', 'Diploma'),
        ('Degree', 'Degree'),
        ('Master\'s', 'Master\'s'),
        ('PHD', 'PHD'),
        ('Sp.Certificate', 'Sp.Certificate'),
    ]

    STUDENT_STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Inactive', 'Inactive'),
        ('Graduated', 'Graduated'),
    ]

    CURRENT_YEAR_CHOICES = [
        ('1st year', '1st year'),
        ('2nd year', '2nd year'),
        ('3rd year', '3rd year'),
        ('4th year', '4th year'),
        ('5th year', '5th year'),
        ('6th year', '6th year'),
        ('7th year', '7th year'),
        ('8th year', '8th year'),
    ]

    admission_type = models.CharField(
        max_length=20,
        choices=ADMISSION_TYPE_CHOICES,
        help_text="Type of admission"
    )
    degree_type = models.CharField(
        max_length=20,
        choices=DEGREE_TYPE_CHOICES,
        help_text="Type of degree"
    )
    is_other_college = models.BooleanField(
        default=False,
        help_text="Check if college is not in the system"
    )
    college = models.ForeignKey(
        'college.College',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='alumni_applications_form1',
        help_text="Select college (if available in system)"
    )
    other_college_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="College name (if not in system)"
    )
    department = models.ForeignKey(
        'department.Department',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='alumni_applications_form1',
        help_text="Select department (if available in system)"
    )
    other_department_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Department name (if not in system)"
    )
    student_status = models.CharField(
        max_length=20,
        choices=STUDENT_STATUS_CHOICES,
        help_text="Current student status"
    )

    # Conditional Academic Fields (based on student_status)
    current_year = models.CharField(
        max_length=20,
        choices=CURRENT_YEAR_CHOICES,
        blank=True,
        null=True,
        help_text="Current year (required when status is Active)"
    )
    year_of_leaving_ethiopian = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        help_text="Year of leaving in Ethiopian calendar (required when status is Inactive)"
    )
    year_of_leaving_gregorian = models.IntegerField(
        blank=True,
        null=True,
        help_text="Year of leaving in Gregorian calendar (required when status is Inactive)"
    )
    year_of_graduation_ethiopian = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        help_text="Year of graduation in Ethiopian calendar (required when status is Graduated)"
    )
    year_of_graduation_gregorian = models.IntegerField(
        blank=True,
        null=True,
        help_text="Year of graduation in Gregorian calendar (required when status is Graduated)"
    )

    # Payment & Transaction Section
    PAYMENT_STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Initiated', 'Initiated'),
        ('Processing', 'Processing'),
        ('Completed', 'Completed'),
        ('Failed', 'Failed'),
        ('Verified', 'Verified'),
        ('Expired', 'Expired'),
        ('Refunded', 'Refunded'),
        ('Cancelled', 'Cancelled'),
    ]

    APPLICATION_STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('On Review', 'On Review'),
        ('Processing', 'Processing'),
        ('Complete', 'Complete'),
    ]

    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='Pending',
        help_text="Payment status (backend-managed)"
    )
    transaction_id = models.CharField(
        max_length=6,
        unique=True,
        default=generate_transaction_id,
        help_text="Auto-generated 6-character transaction ID"
    )
    application_status = models.CharField(
        max_length=20,
        choices=APPLICATION_STATUS_CHOICES,
        default='Pending',
        help_text="Application processing status (admin-editable)"
    )
    service_type = models.ForeignKey(
        'service_type.ServiceType',
        on_delete=models.PROTECT,
        related_name='alumni_applications_form1',
        help_text="Type of service requested"
    )

    # Destination Logic Section (ONLY for Form1)
    is_uog_destination = models.BooleanField(
        help_text="True for internal UoG destination, False for external"
    )
    uog_college = models.ForeignKey(
        'college.College',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='alumni_applications_form1_destination',
        help_text="UoG college destination (required when is_uog_destination=True)"
    )
    uog_department = models.ForeignKey(
        'department.Department',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='alumni_applications_form1_destination',
        help_text="UoG department destination (required when is_uog_destination=True)"
    )

    ORDER_TYPE_CHOICES = [
        ('Local', 'Local'),
        ('International', 'International'),
        ('Legal Delegate', 'Legal Delegate'),
    ]

    MAILING_AGENT_CHOICES = [
        ('Normal Postal', 'Normal Postal'),
        ('DHL', 'DHL'),
        ('SMS', 'SMS'),
    ]

    order_type = models.CharField(
        max_length=20,
        choices=ORDER_TYPE_CHOICES,
        blank=True,
        null=True,
        help_text="Order type (required when is_uog_destination=False)"
    )
    institution_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="External institution name (required when is_uog_destination=False)"
    )
    country = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Country (required when is_uog_destination=False)"
    )
    institution_address = models.TextField(
        max_length=500,
        blank=True,
        null=True,
        help_text="Institution address (required when is_uog_destination=False)"
    )
    mailing_agent = models.CharField(
        max_length=20,
        choices=MAILING_AGENT_CHOICES,
        blank=True,
        null=True,
        help_text="Mailing agent (required when is_uog_destination=False)"
    )

    # Audit Trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='created_alumni_applications_form1'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='updated_alumni_applications_form1'
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Alumni Application (Complete)'
        verbose_name_plural = 'Alumni Applications (Complete)'
        indexes = [
            models.Index(fields=['transaction_id']),
            models.Index(fields=['email']),
            models.Index(fields=['service_type']),
            models.Index(fields=['application_status']),
            models.Index(fields=['payment_status']),
            models.Index(fields=['created_at']),
        ]
        constraints = [
            models.UniqueConstraint(fields=['email'], name='unique_alumni_application_email'),
        ]

    def clean(self):
        """Custom validation for the application form."""
        super().clean()

        # Clean and validate names
        if self.first_name:
            self.first_name = self.first_name.strip()
        if self.father_name:
            self.father_name = self.father_name.strip()
        if self.last_name:
            self.last_name = self.last_name.strip()

        # Validate college selection
        if self.is_other_college:
            if not self.other_college_name:
                raise ValidationError({'other_college_name': 'College name is required when "Other" is selected.'})
            if self.college:
                raise ValidationError({'college': 'Cannot select both system college and other college.'})
        else:
            if not self.college:
                raise ValidationError({'college': 'College selection is required.'})
            if self.other_college_name:
                raise ValidationError({'other_college_name': 'Cannot specify other college name when system college is selected.'})

        # Validate department selection
        if self.is_other_college:
            if not self.other_department_name:
                raise ValidationError({'other_department_name': 'Department name is required when "Other college" is selected.'})
            if self.department:
                raise ValidationError({'department': 'Cannot select both system department and other department.'})
        else:
            if not self.department:
                raise ValidationError({'department': 'Department selection is required.'})
            if self.other_department_name:
                raise ValidationError({'other_department_name': 'Cannot specify other department name when system department is selected.'})

        # Validate conditional academic fields based on student_status
        if self.student_status == 'Active':
            if not self.current_year:
                raise ValidationError({'current_year': 'Current year is required when student status is Active.'})
        elif self.student_status == 'Inactive':
            if not self.year_of_leaving_ethiopian and not self.year_of_leaving_gregorian:
                raise ValidationError('Either Ethiopian or Gregorian year of leaving is required when student status is Inactive.')
        elif self.student_status == 'Graduated':
            if not self.year_of_graduation_ethiopian and not self.year_of_graduation_gregorian:
                raise ValidationError('Either Ethiopian or Gregorian year of graduation is required when student status is Graduated.')

        # Validate destination logic
        if self.is_uog_destination:
            if not self.uog_college:
                raise ValidationError({'uog_college': 'UoG college is required for internal destination.'})
            if not self.uog_department:
                raise ValidationError({'uog_department': 'UoG department is required for internal destination.'})
            # Clear external fields
            self.order_type = None
            self.institution_name = None
            self.country = None
            self.institution_address = None
            self.mailing_agent = None
        else:
            # External destination validation
            required_external_fields = {
                'order_type': 'Order type is required for external destination.',
                'institution_name': 'Institution name is required for external destination.',
                'country': 'Country is required for external destination.',
                'institution_address': 'Institution address is required for external destination.',
                'mailing_agent': 'Mailing agent is required for external destination.',
            }
            for field, message in required_external_fields.items():
                if not getattr(self, field):
                    raise ValidationError({field: message})
            # Clear internal fields
            self.uog_college = None
            self.uog_department = None

    def save(self, *args, **kwargs):
        """Run full validation before saving."""
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.transaction_id}"

    @property
    def full_name(self):
        """Return the full name of the applicant."""
        return f"{self.first_name} {self.father_name} {self.last_name}".strip()

    @property
    def college_name(self):
        """Return the college name (system or other)."""
        return self.college.name if self.college else self.other_college_name

    @property
    def department_name(self):
        """Return the department name (system or other)."""
        return self.department.name if self.department else self.other_department_name

    @property
    def required_document_types(self):
        """Return the required document types for this application's service type."""
        if self.service_type:
            return self.service_type.document_types.filter(is_active=True)
        return []

    @property
    def required_document_types_list(self):
        """Return a list of required document type names."""
        return list(self.required_document_types.values_list('name', flat=True))

    def get_missing_document_types(self):
        """Return document types that are required but not yet uploaded."""
        if not self.service_type:
            return []

        required_types = self.required_document_types
        uploaded_type_names = self.documents.values_list('document_type_name', flat=True)

        return required_types.exclude(name__in=uploaded_type_names)

    def get_missing_document_types_list(self):
        """Return a list of missing document type names."""
        return list(self.get_missing_document_types().values_list('name', flat=True))

    def is_documents_complete(self):
        """Check if all required documents have been uploaded."""
        return not self.get_missing_document_types().exists()

    def get_document_completion_status(self):
        """Return document completion status information."""
        required_count = self.required_document_types.count()
        uploaded_count = self.documents.count()
        missing_types = self.get_missing_document_types_list()

        return {
            'required_count': required_count,
            'uploaded_count': uploaded_count,
            'missing_count': len(missing_types),
            'missing_types': missing_types,
            'is_complete': len(missing_types) == 0,
            'completion_percentage': (uploaded_count / required_count * 100) if required_count > 0 else 100
        }


class AlumniApplicationMini(models.Model):
    """
    Simplified Alumni Application Form (Form2) without destination logic.
    Contains all the same fields as Form1 except the destination section.
    """

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Personal Information Section (identical to Form1)
    first_name = models.CharField(
        max_length=50,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z\s\-\'\.]+$',
            message='First name must contain only letters, spaces, hyphens, apostrophes, and periods.'
        )],
        help_text="First name (letters only)"
    )
    father_name = models.CharField(
        max_length=50,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z\s\-\'\.]+$',
            message='Father name must contain only letters, spaces, hyphens, apostrophes, and periods.'
        )],
        help_text="Father's name (letters only)"
    )
    last_name = models.CharField(
        max_length=50,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z\s\-\'\.]+$',
            message='Last name must contain only letters, spaces, hyphens, apostrophes, and periods.'
        )],
        help_text="Last name (letters only)"
    )
    student_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        unique=True,
        validators=[RegexValidator(
            regex=r'^[a-zA-Z0-9/]+$',
            message='Student ID must be alphanumeric with forward slashes (e.g., uog/1254/21).'
        )],
        help_text="Student ID format: uog/1254/21 (optional)"
    )
    phone_number = models.CharField(
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+?[1-9]\d{1,14}$',
            message='Phone number must be in international format with country code.'
        )],
        help_text="International format with country code (e.g., +251912345678)"
    )
    email = models.EmailField(
        max_length=100,
        validators=[EmailValidator()],
        help_text="Valid email address"
    )

    # Academic Information Section (identical to Form1)
    ADMISSION_TYPE_CHOICES = [
        ('Regular', 'Regular'),
        ('Evening', 'Evening'),
        ('Summer', 'Summer'),
        ('Distance', 'Distance'),
    ]

    DEGREE_TYPE_CHOICES = [
        ('Diploma', 'Diploma'),
        ('Degree', 'Degree'),
        ('Master\'s', 'Master\'s'),
        ('PHD', 'PHD'),
        ('Sp.Certificate', 'Sp.Certificate'),
    ]

    STUDENT_STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Inactive', 'Inactive'),
        ('Graduated', 'Graduated'),
    ]

    CURRENT_YEAR_CHOICES = [
        ('1st year', '1st year'),
        ('2nd year', '2nd year'),
        ('3rd year', '3rd year'),
        ('4th year', '4th year'),
        ('5th year', '5th year'),
        ('6th year', '6th year'),
        ('7th year', '7th year'),
        ('8th year', '8th year'),
    ]

    admission_type = models.CharField(
        max_length=20,
        choices=ADMISSION_TYPE_CHOICES,
        help_text="Type of admission"
    )
    degree_type = models.CharField(
        max_length=20,
        choices=DEGREE_TYPE_CHOICES,
        help_text="Type of degree"
    )
    is_other_college = models.BooleanField(
        default=False,
        help_text="Check if college is not in the system"
    )
    college = models.ForeignKey(
        'college.College',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='alumni_applications_form2',
        help_text="Select college (if available in system)"
    )
    other_college_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="College name (if not in system)"
    )
    department = models.ForeignKey(
        'department.Department',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='alumni_applications_form2',
        help_text="Select department (if available in system)"
    )
    other_department_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Department name (if not in system)"
    )
    student_status = models.CharField(
        max_length=20,
        choices=STUDENT_STATUS_CHOICES,
        help_text="Current student status"
    )

    # Conditional Academic Fields (based on student_status) - identical to Form1
    current_year = models.CharField(
        max_length=20,
        choices=CURRENT_YEAR_CHOICES,
        blank=True,
        null=True,
        help_text="Current year (required when status is Active)"
    )
    year_of_leaving_ethiopian = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        help_text="Year of leaving in Ethiopian calendar (required when status is Inactive)"
    )
    year_of_leaving_gregorian = models.IntegerField(
        blank=True,
        null=True,
        help_text="Year of leaving in Gregorian calendar (required when status is Inactive)"
    )
    year_of_graduation_ethiopian = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        help_text="Year of graduation in Ethiopian calendar (required when status is Graduated)"
    )
    year_of_graduation_gregorian = models.IntegerField(
        blank=True,
        null=True,
        help_text="Year of graduation in Gregorian calendar (required when status is Graduated)"
    )

    # Payment & Transaction Section (identical to Form1)
    PAYMENT_STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Initiated', 'Initiated'),
        ('Processing', 'Processing'),
        ('Completed', 'Completed'),
        ('Failed', 'Failed'),
        ('Verified', 'Verified'),
        ('Expired', 'Expired'),
        ('Refunded', 'Refunded'),
        ('Cancelled', 'Cancelled'),
    ]

    APPLICATION_STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('On Review', 'On Review'),
        ('Processing', 'Processing'),
        ('Complete', 'Complete'),
    ]

    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='Pending',
        help_text="Payment status (backend-managed)"
    )
    transaction_id = models.CharField(
        max_length=6,
        unique=True,
        default=generate_transaction_id,
        help_text="Auto-generated 6-character transaction ID"
    )
    application_status = models.CharField(
        max_length=20,
        choices=APPLICATION_STATUS_CHOICES,
        default='Pending',
        help_text="Application processing status (admin-editable)"
    )
    service_type = models.ForeignKey(
        'service_type.ServiceType',
        on_delete=models.PROTECT,
        related_name='alumni_applications_form2',
        help_text="Type of service requested"
    )

    # NOTE: NO destination logic section for Form2

    # Audit Trail (identical to Form1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='created_alumni_applications_form2'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='updated_alumni_applications_form2'
    )

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Alumni Application (Mini)'
        verbose_name_plural = 'Alumni Applications (Mini)'
        indexes = [
            models.Index(fields=['transaction_id']),
            models.Index(fields=['email']),
            models.Index(fields=['service_type']),
            models.Index(fields=['application_status']),
            models.Index(fields=['payment_status']),
            models.Index(fields=['created_at']),
        ]
        constraints = [
            models.UniqueConstraint(fields=['email'], name='unique_alumni_application_mini_email'),
        ]

    def clean(self):
        """Custom validation for the simplified application form."""
        super().clean()

        # Clean and validate names
        if self.first_name:
            self.first_name = self.first_name.strip()
        if self.father_name:
            self.father_name = self.father_name.strip()
        if self.last_name:
            self.last_name = self.last_name.strip()

        # Validate college selection
        if self.is_other_college:
            if not self.other_college_name:
                raise ValidationError({'other_college_name': 'College name is required when "Other" is selected.'})
            if self.college:
                raise ValidationError({'college': 'Cannot select both system college and other college.'})
        else:
            if not self.college:
                raise ValidationError({'college': 'College selection is required.'})
            if self.other_college_name:
                raise ValidationError({'other_college_name': 'Cannot specify other college name when system college is selected.'})

        # Validate department selection
        if self.is_other_college:
            if not self.other_department_name:
                raise ValidationError({'other_department_name': 'Department name is required when "Other college" is selected.'})
            if self.department:
                raise ValidationError({'department': 'Cannot select both system department and other department.'})
        else:
            if not self.department:
                raise ValidationError({'department': 'Department selection is required.'})
            if self.other_department_name:
                raise ValidationError({'other_department_name': 'Cannot specify other department name when system department is selected.'})

        # Validate conditional academic fields based on student_status
        if self.student_status == 'Active':
            if not self.current_year:
                raise ValidationError({'current_year': 'Current year is required when student status is Active.'})
        elif self.student_status == 'Inactive':
            if not self.year_of_leaving_ethiopian and not self.year_of_leaving_gregorian:
                raise ValidationError('Either Ethiopian or Gregorian year of leaving is required when student status is Inactive.')
        elif self.student_status == 'Graduated':
            if not self.year_of_graduation_ethiopian and not self.year_of_graduation_gregorian:
                raise ValidationError('Either Ethiopian or Gregorian year of graduation is required when student status is Graduated.')

    def save(self, *args, **kwargs):
        """Run full validation before saving."""
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.transaction_id}"

    @property
    def full_name(self):
        """Return the full name of the applicant."""
        return f"{self.first_name} {self.father_name} {self.last_name}".strip()

    @property
    def college_name(self):
        """Return the college name (system or other)."""
        return self.college.name if self.college else self.other_college_name

    @property
    def department_name(self):
        """Return the department name (system or other)."""
        return self.department.name if self.department else self.other_department_name

    @property
    def required_document_types(self):
        """Return the required document types for this application's service type."""
        if self.service_type:
            return self.service_type.document_types.filter(is_active=True)
        return []

    @property
    def required_document_types_list(self):
        """Return a list of required document type names."""
        return list(self.required_document_types.values_list('name', flat=True))

    def get_missing_document_types(self):
        """Return document types that are required but not yet uploaded."""
        if not self.service_type:
            return []

        required_types = self.required_document_types
        uploaded_type_names = self.documents.values_list('document_type_name', flat=True)

        return required_types.exclude(name__in=uploaded_type_names)

    def get_missing_document_types_list(self):
        """Return a list of missing document type names."""
        return list(self.get_missing_document_types().values_list('name', flat=True))

    def is_documents_complete(self):
        """Check if all required documents have been uploaded."""
        return not self.get_missing_document_types().exists()

    def get_document_completion_status(self):
        """Return document completion status information."""
        required_count = self.required_document_types.count()
        uploaded_count = self.documents.count()
        missing_types = self.get_missing_document_types_list()

        return {
            'required_count': required_count,
            'uploaded_count': uploaded_count,
            'missing_count': len(missing_types),
            'missing_types': missing_types,
            'is_complete': len(missing_types) == 0,
            'completion_percentage': (uploaded_count / required_count * 100) if required_count > 0 else 100
        }


def application_document_upload_path(instance, filename):
    """Generate upload path for application documents."""
    from django.utils import timezone

    # Get file extension
    ext = filename.split('.')[-1].lower()

    # Generate unique filename using timestamp and random string
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    new_filename = f"{timestamp}_{random_string}.{ext}"

    # Determine which application type
    if instance.application_form1:
        app_type = 'form1'
        app_id = str(instance.application_form1.id)
    elif instance.application_form2:
        app_type = 'form2'
        app_id = str(instance.application_form2.id)
    else:
        # Fallback for cases where neither is set yet
        app_type = 'temp'
        app_id = 'unknown'

    return f'alumni_applications/{app_type}/{app_id[:8]}/{new_filename}'


class ApplicationDocument(models.Model):
    """
    Junction table for document uploads related to alumni applications.
    Supports both Form1 and Form2 applications.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Foreign keys to application forms (one of these will be set)
    application_form1 = models.ForeignKey(
        AlumniApplication,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='documents'
    )
    application_form2 = models.ForeignKey(
        AlumniApplicationMini,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='documents'
    )

    # Document type name (from ServiceType.document_types)
    document_type_name = models.CharField(
        max_length=100,
        help_text="Name of the document type (must match one from service type requirements)"
    )

    # File upload with validation
    file = models.FileField(
        upload_to=application_document_upload_path,
        help_text="Upload the required document (max 10MB)"
    )

    # File metadata
    original_filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField(help_text="File size in bytes")
    mime_type = models.CharField(max_length=100)

    # Audit fields
    upload_timestamp = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='uploaded_application_documents'
    )

    class Meta:
        ordering = ['-upload_timestamp']
        verbose_name = 'Application Document'
        verbose_name_plural = 'Application Documents'
        indexes = [
            models.Index(fields=['application_form1', 'document_type_name']),
            models.Index(fields=['application_form2', 'document_type_name']),
            models.Index(fields=['upload_timestamp']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(application_form1__isnull=False) | models.Q(application_form2__isnull=False),
                name='application_document_has_application'
            ),
            models.CheckConstraint(
                check=~(models.Q(application_form1__isnull=False) & models.Q(application_form2__isnull=False)),
                name='application_document_single_application'
            ),
        ]

    def clean(self):
        """Validate the document upload."""
        super().clean()

        # Ensure exactly one application is linked
        if not self.application_form1 and not self.application_form2:
            raise ValidationError('Document must be linked to either Form1 or Form2 application.')

        if self.application_form1 and self.application_form2:
            raise ValidationError('Document cannot be linked to both Form1 and Form2 applications.')

        # Validate file size (10MB limit)
        if self.file and self.file.size > 10 * 1024 * 1024:
            raise ValidationError({'file': 'File size cannot exceed 10MB.'})

        # Validate file type
        if self.file:
            allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx']
            file_extension = self.file.name.split('.')[-1].lower()
            if file_extension not in allowed_extensions:
                raise ValidationError({
                    'file': f'File type .{file_extension} is not allowed. Allowed types: {", ".join(allowed_extensions)}'
                })

        # Validate that document type is required for the service type
        application = self.application_form1 or self.application_form2
        if application and self.document_type_name:
            # Get required document types for the service type
            required_document_types = application.service_type.document_types.filter(is_active=True)
            required_names = list(required_document_types.values_list('name', flat=True))

            # Check if the document type name is required
            if self.document_type_name not in required_names:
                raise ValidationError({
                    'document_type_name': f'Document type "{self.document_type_name}" is not required for service "{application.service_type.name}". Required document types: {", ".join(required_names) if required_names else "None"}'
                })

            # Validate that the document type name exists and is active
            from setups.document_type.models import DocumentType
            if not DocumentType.objects.filter(name=self.document_type_name, is_active=True).exists():
                raise ValidationError({
                    'document_type_name': f'Document type "{self.document_type_name}" does not exist or is not active.'
                })

    def save(self, *args, **kwargs):
        """Override save to capture file metadata."""
        if self.file:
            self.original_filename = self.file.name
            self.file_size = self.file.size

            # Determine MIME type based on file extension
            ext = self.file.name.split('.')[-1].lower()
            mime_types = {
                'pdf': 'application/pdf',
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'doc': 'application/msword',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            }
            self.mime_type = mime_types.get(ext, 'application/octet-stream')

        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        application = self.application_form1 or self.application_form2
        return f"{application.full_name} - {self.document_type_name}"

    @property
    def application(self):
        """Return the associated application (Form1 or Form2)."""
        return self.application_form1 or self.application_form2

    @property
    def formatted_file_size(self):
        """Return formatted file size."""
        if not self.file_size:
            return "0 B"

        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
