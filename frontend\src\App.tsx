
import React, { Suspense } from 'react';
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import useSessionTracker from "./hooks/useSessionTracker";
import IdleTimeoutProvider from "./providers/IdleTimeoutProvider";
import BrandingProvider from "./providers/BrandingProvider";
import { AuthProvider } from "./contexts/AuthContext";
import { SimpleRBACProvider as RBACProvider } from "./contexts/SimpleRBACContext";
import DocumentTitle from "./components/DocumentTitle";
import DynamicFavicon from "./components/DynamicFavicon";
// Enhanced RBAC route protection
import { UserRoute, RestrictedRoute } from "./components/SimpleAuthRoute";
// Enhanced access control system
import { EnhancedAccessControl, AdminAccess, SuperuserAccess } from "./components/EnhancedAccessControl";
// Temporarily use simple RBAC components for backward compatibility
import { AdminOnly, SuperuserOnly } from "./contexts/SimpleRBACContext";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";

import Application from "./pages/Application";
import ApplicationStatusNew from "./pages/ApplicationStatusNew";

import ApplicationDetails from "./pages/ApplicationDetails";
import Profile from "./pages/Profile";
import Notifications from "./pages/Notifications";
import NotFound from "./pages/NotFound";
import Programs from "./pages/Programs";
import GraduateAdmin from "./pages/GraduateAdmin";
import Dashboard from "./pages/Dashboard";
import SystemSettings from "./components/SystemSettings";


import Services from "./pages/Services";
import ServiceRequestList from "./components/ServiceRequestList";
import GraduateVerification from "./pages/GraduateVerification";
import ApplicationStatusChecker from "./pages/PublicApplicationStatus";
import TrackOfficial from "./components/TrackOfficial";
import Downloads from "./pages/Downloads";
import AuthRoute from "./components/AuthRoute";
import PublicAlumniApplication from "./pages/PublicAlumniApplication";
import Developers from "./pages/Developers";


import UserCheck from "./pages/UserCheck";
import PersonalInformation from "./pages/PersonalInformation";
import GAT from "./pages/GAT";
import ProgramSelection from "./pages/ProgramSelection";
import DocumentationUpload from "./pages/DocumentationUpload";
import PaymentForm from "./pages/PaymentForm";
import Settings from "./pages/Settings";

const queryClient = new QueryClient();

const App = () => {
  // Initialize session tracker
  useSessionTracker();

  return (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <BrandingProvider>
        <AuthProvider>
          <RBACProvider>
            <IdleTimeoutProvider idleTime={999999} warningTime={999999}>
              <DocumentTitle />
              <DynamicFavicon />
              <Toaster />
              <BrowserRouter future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true
              }}>
            <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<AuthRoute><Login /></AuthRoute>} />
            <Route path="/register" element={<AuthRoute><Register /></AuthRoute>} />
            <Route path="/forgot-password" element={<AuthRoute><ForgotPassword /></AuthRoute>} />

            <Route path="/application" element={<UserRoute><Application /></UserRoute>} />
            <Route path="/application/new" element={<Navigate to="/application/gat?new=true" replace />} />
            <Route path="/application/personal-info" element={<UserRoute><PersonalInformation /></UserRoute>} />
            <Route path="/application/gat" element={<UserRoute><GAT /></UserRoute>} />
            <Route path="/application/program-selection" element={<UserRoute><ProgramSelection /></UserRoute>} />
            <Route path="/application/documentation" element={<UserRoute><DocumentationUpload /></UserRoute>} />
            <Route path="/application/payment" element={<UserRoute><PaymentForm /></UserRoute>} />
            <Route path="/application/status" element={<UserRoute><ApplicationStatusNew /></UserRoute>} />
            <Route path="/application/status/:applicationId" element={<UserRoute><ApplicationStatusNew /></UserRoute>} />
            <Route path="/status/:applicationId" element={<UserRoute><ApplicationStatusNew /></UserRoute>} />
            <Route path="/application/details/:id" element={<UserRoute><ApplicationDetails /></UserRoute>} />
            <Route path="/profile" element={<RestrictedRoute><Profile /></RestrictedRoute>} />
            <Route path="/personal-information" element={<UserRoute><PersonalInformation /></UserRoute>} />
            <Route path="/notifications" element={<UserRoute><Notifications /></UserRoute>} />
            <Route path="/settings" element={<RestrictedRoute><Settings /></RestrictedRoute>} />
            <Route path="/user-check" element={<UserRoute><UserCheck /></UserRoute>} />
            <Route path="/programs" element={<Programs />} />
            <Route path="/developers" element={<Developers />} />
            <Route path="/graduate-admin" element={<AdminAccess><GraduateAdmin /></AdminAccess>} />
            <Route path="/dashboard" element={<UserRoute><Dashboard /></UserRoute>} />

            {/* Enhanced admin routes with proper RBAC */}
            <Route path="/system-settings" element={<SuperuserAccess><SystemSettings /></SuperuserAccess>} />

            {/* Redirect /application-information to graduate-admin with application-information tab */}
            <Route path="/application-information" element={<Navigate to="/graduate-admin?tab=application-information" replace />} />

            <Route path="/services" element={<Services />} />
            <Route path="/alumni-application" element={<PublicAlumniApplication />} />
            <Route path="/service-requests" element={<UserRoute><ServiceRequestList /></UserRoute>} />

            {/* Graduate Verification route - accessible to all users */}
            <Route path="/graduate-verification" element={<GraduateVerification />} />

            {/* Track Official route - accessible to all users */}
            <Route path="/track-official" element={<TrackOfficial />} />

            {/* Downloads route - accessible to all users */}
            <Route path="/downloads" element={<Downloads />} />

            {/* Application Status route - only accessible to logged-in users */}
            <Route path="/status" element={<UserRoute><ApplicationStatusChecker /></UserRoute>} />



            <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
            </IdleTimeoutProvider>
          </RBACProvider>
        </AuthProvider>
      </BrandingProvider>
    </TooltipProvider>
  </QueryClientProvider>
  );
};

export default App;
