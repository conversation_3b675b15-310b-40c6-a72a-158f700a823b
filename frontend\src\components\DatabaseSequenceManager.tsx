import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { toast } from 'sonner';
import { resetDatabaseSequence, checkDatabaseSequence, getNextAvailableId } from '../utils/databaseUtils';
import { AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';

const DatabaseSequenceManager: React.FC = () => {
  const [tableName, setTableName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [checkResult, setCheckResult] = useState<any>(null);
  const [resetResult, setResetResult] = useState<boolean | null>(null);
  const [nextId, setNextId] = useState<number | null>(null);

  const commonTables = [
    { value: 'Department', label: 'Department' },
    { value: 'College', label: 'College' },
    { value: 'Program', label: 'Program' },
    { value: 'AdmissionType', label: 'Admission Type' },
    { value: 'StudyField', label: 'Study Field' },
    { value: 'StudyProgram', label: 'Study Program' },
    { value: 'RegistrationPeriod', label: 'Registration Period' },
    { value: 'ApplicationInformation', label: 'Application Information' },
  ];

  const handleCheck = async () => {
    if (!tableName) {
      toast.error('Please select a table');
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to check database sequences');
        setLoading(false);
        return;
      }

      const result = await checkDatabaseSequence(tableName, token);
      setCheckResult(result);

      // Also get the next available ID
      const nextIdResult = await getNextAvailableId(tableName, token);
      setNextId(nextIdResult);

      toast.success('Database sequence checked successfully');
    } catch (error) {
      console.error('Error checking database sequence:', error);
      toast.error('Failed to check database sequence');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (!tableName) {
      toast.error('Please select a table');
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You must be logged in to reset database sequences');
        setLoading(false);
        return;
      }

      const result = await resetDatabaseSequence(tableName, token);
      setResetResult(result);

      if (result) {
        // Re-check the sequence after reset
        const checkResult = await checkDatabaseSequence(tableName, token);
        setCheckResult(checkResult);

        // Also get the next available ID
        const nextIdResult = await getNextAvailableId(tableName, token);
        setNextId(nextIdResult);

        toast.success('Database sequence reset successfully');
      } else {
        toast.error('Failed to reset database sequence');
      }
    } catch (error) {
      console.error('Error resetting database sequence:', error);
      toast.error('Failed to reset database sequence');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Database Sequence Manager</CardTitle>
        <CardDescription>
          Check and reset database sequences to fix "duplicate key value violates unique constraint" errors
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="tableName">Table Name</Label>
          <Select value={tableName} onValueChange={setTableName}>
            <SelectTrigger id="tableName">
              <SelectValue placeholder="Select a table" />
            </SelectTrigger>
            <SelectContent>
              {commonTables.map((table) => (
                <SelectItem key={table.value} value={table.value}>
                  {table.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            Select the table that is experiencing sequence issues
          </p>
        </div>

        {checkResult && (
          <Alert variant={checkResult.is_out_of_sync ? "destructive" : "default"}>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Sequence Check Result</AlertTitle>
            <AlertDescription>
              <div className="mt-2 space-y-2">
                <p><strong>Table:</strong> {checkResult.table_name}</p>
                <p><strong>Max ID:</strong> {checkResult.max_id}</p>
                <p><strong>Next ID:</strong> {checkResult.next_id}</p>
                <p><strong>Status:</strong> {checkResult.is_out_of_sync
                  ? "Out of sync (needs reset)"
                  : "In sync (working correctly)"}</p>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {nextId !== null && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Next Available ID</AlertTitle>
            <AlertDescription>
              <p className="mt-2">The next available ID for {tableName} is: <strong>{nextId}</strong></p>
              <p className="text-sm text-muted-foreground mt-1">
                Use this ID when manually creating records to avoid conflicts
              </p>
            </AlertDescription>
          </Alert>
        )}

        {resetResult !== null && (
          <Alert variant={resetResult ? "default" : "destructive"}>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Reset Result</AlertTitle>
            <AlertDescription>
              {resetResult
                ? "Database sequence was reset successfully"
                : "Failed to reset database sequence"}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleCheck} disabled={loading}>
          {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
          Check Sequence
        </Button>
        <Button onClick={handleReset} disabled={loading}>
          {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : null}
          Reset Sequence
        </Button>
      </CardFooter>
    </Card>
  );
};

export default DatabaseSequenceManager;
