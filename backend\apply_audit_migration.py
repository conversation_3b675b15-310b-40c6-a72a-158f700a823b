#!/usr/bin/env python
"""
Script to manually apply audit trail migration
"""
import os
import sys
import django
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

def apply_audit_migration():
    """Apply the audit trail migration manually"""
    print("=== Applying Audit Trail Migration ===")
    
    try:
        with connection.cursor() as cursor:
            # Check if columns already exist
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'GraduateVerification_graduatestudent' 
                AND column_name IN ('created_by_id', 'updated_by_id');
            """)
            existing_columns = [row[0] for row in cursor.fetchall()]
            
            if 'created_by_id' in existing_columns and 'updated_by_id' in existing_columns:
                print("✓ Audit trail columns already exist!")
                return True
            
            print("Adding audit trail columns...")
            
            # Add created_by column
            if 'created_by_id' not in existing_columns:
                print("  Adding created_by_id column...")
                cursor.execute("""
                    ALTER TABLE "GraduateVerification_graduatestudent" 
                    ADD COLUMN "created_by_id" integer NULL;
                """)
            
            # Add updated_by column  
            if 'updated_by_id' not in existing_columns:
                print("  Adding updated_by_id column...")
                cursor.execute("""
                    ALTER TABLE "GraduateVerification_graduatestudent" 
                    ADD COLUMN "updated_by_id" integer NULL;
                """)
            
            # Add foreign key constraints
            print("  Adding foreign key constraints...")
            try:
                cursor.execute("""
                    ALTER TABLE "GraduateVerification_graduatestudent" 
                    ADD CONSTRAINT "GraduateVerification_graduatestudent_created_by_id_fkey" 
                    FOREIGN KEY ("created_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
                """)
            except Exception as e:
                print(f"    Created_by constraint may already exist: {e}")
            
            try:
                cursor.execute("""
                    ALTER TABLE "GraduateVerification_graduatestudent" 
                    ADD CONSTRAINT "GraduateVerification_graduatestudent_updated_by_id_fkey" 
                    FOREIGN KEY ("updated_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
                """)
            except Exception as e:
                print(f"    Updated_by constraint may already exist: {e}")
            
            # Create indexes for better performance
            print("  Creating indexes...")
            try:
                cursor.execute("""
                    CREATE INDEX "GraduateVerification_graduatestudent_created_by_id_idx" 
                    ON "GraduateVerification_graduatestudent" ("created_by_id");
                """)
            except Exception as e:
                print(f"    Created_by index may already exist: {e}")
            
            try:
                cursor.execute("""
                    CREATE INDEX "GraduateVerification_graduatestudent_updated_by_id_idx" 
                    ON "GraduateVerification_graduatestudent" ("updated_by_id");
                """)
            except Exception as e:
                print(f"    Updated_by index may already exist: {e}")
            
            # Update migration table
            print("  Updating migration table...")
            try:
                cursor.execute("""
                    INSERT INTO "django_migrations" ("app", "name", "applied") 
                    VALUES ('GraduateVerification', '0003_add_audit_trail_fields', NOW());
                """)
            except Exception as e:
                print(f"    Migration record may already exist: {e}")
            
            print("✅ Audit trail migration applied successfully!")
            print("\n💡 Next steps:")
            print("1. Uncomment the audit trail fields in models.py")
            print("2. Uncomment the audit trail code in views.py and serializers.py")
            print("3. Restart your Django server")
            print("4. Test the Recent Graduates Dashboard")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure your database is running")
        print("2. Check database permissions")
        print("3. Verify table names match your database")
        return False

if __name__ == '__main__':
    apply_audit_migration()
