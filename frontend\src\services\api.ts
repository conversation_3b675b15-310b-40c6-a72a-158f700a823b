import axios from 'axios';
import { API_BASE_URL, API_TIMEOUT, PAYMENT_BASE_URL, MERCHANT_APP_ID } from '../config';
// RBAC middleware removed - using simplified system

// Use configuration from config file
const baseUrl = PAYMENT_BASE_URL;
const merchantAppId = MERCHANT_APP_ID;




// Dynamically set API base URL based on hostname and protocol (fallback for complex scenarios)
const DYNAMIC_API_BASE_URL = (() => {
  // If we have a configured API_BASE_URL, use it
  if (API_BASE_URL && !API_BASE_URL.includes('localhost:8080')) {
    return API_BASE_URL;
  }

  // Get the current protocol (http: or https:)
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;

  if (hostname === 'localhost') {
    return API_BASE_URL || `http://localhost:${import.meta.env.VITE_FALLBACK_API_PORT || '8000'}/api`;
  } else if (hostname === (import.meta.env.VITE_SPECIAL_IP_ADDRESS || '**************')) {
    // For this specific IP, use the same port as the frontend
    const port = window.location.port || '8080';
    return `${protocol}//${hostname}:${port}/api`;
  } else {
    // Use the same protocol as the current page to avoid mixed content issues
    const fallbackPort = import.meta.env.VITE_FALLBACK_API_PORT || '8000';
    return `${protocol}//${hostname}:${fallbackPort}/api`;
  }
})();


console.log('Using API base URL:', DYNAMIC_API_BASE_URL);

// CSRF Token utility functions
const getCSRFToken = (): string | null => {
  // Try to get CSRF token from meta tag first
  const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (metaToken) return metaToken;

  // Try to get from cookie
  const cookieValue = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrftoken='))
    ?.split('=')[1];

  return cookieValue || null;
};

const fetchCSRFToken = async (): Promise<string | null> => {
  try {
    const response = await axios.get(`${DYNAMIC_API_BASE_URL}/csrf/`, {
      withCredentials: true,
    });
    return response.data.csrf_token || response.data.csrftoken || null;
  } catch (error) {
    console.warn('Failed to fetch CSRF token from server, this is normal for JWT-based authentication:', error);
    // For JWT-based authentication, CSRF tokens are not always required
    // Return null and let the request proceed without CSRF token
    return null;
  }
};

// Create axios instance with default config
const api = axios.create({
  baseURL: DYNAMIC_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Important for CORS with credentials
  timeout: API_TIMEOUT, // Use timeout from config
  // Add validation to ensure the request is properly formatted
  validateStatus: (status) => {
    // For token endpoint, only 200 is considered successful
    // For other endpoints, anything less than 500 is considered successful
    const url = window.location.pathname;
    if (url.includes('/token/')) {
      return status === 200; // Only 200 is successful for token endpoints
    }
    return status < 500; // For other endpoints, resolve if status code is less than 500
  }
});

// Add request interceptor to add auth token and CSRF token
api.interceptors.request.use(async (config) => {
  // Add auth token
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Add CSRF token for non-safe methods (POST, PUT, PATCH, DELETE)
  if (config.method && !['get', 'head', 'options'].includes(config.method.toLowerCase())) {
    const csrfToken = getCSRFToken();
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
      config.headers['X-CSRF-Token'] = csrfToken;
    }
    // Note: For JWT-based authentication, CSRF tokens are optional
    // The request will proceed even without a CSRF token
  }

  // RBAC middleware removed - using simplified system
  return config;
});

// Function to check if token is about to expire (within 5 minutes instead of 30)
const isTokenExpiring = () => {
  const token = localStorage.getItem('token');
  if (!token) return true;

  try {
    // Decode the JWT token
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const { exp } = JSON.parse(jsonPayload);

    // Check if token will expire in the next 5 minutes (reduced from 30)
    const expirationTime = exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiry = expirationTime - currentTime;

    // Return true if token expires in less than 5 minutes
    return timeUntilExpiry < 5 * 60 * 1000;
  } catch (error) {
    console.error('Error decoding token:', error);
    return true; // If there's an error, assume token is expiring
  }
};

// Flag to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let refreshPromise: Promise<boolean> | null = null;

// Function to refresh the token
const refreshAuthToken = async () => {
  // If already refreshing, return the existing promise
  if (isRefreshing && refreshPromise) {
    console.log('Token refresh already in progress, waiting...');
    return refreshPromise;
  }

  const refreshToken = localStorage.getItem('refresh_token');
  if (!refreshToken) {
    console.log('No refresh token available');
    return false;
  }

  isRefreshing = true;
  refreshPromise = (async () => {
    try {
      console.log('Attempting to refresh token...');
      const response = await axios.post(`${DYNAMIC_API_BASE_URL}/token/refresh/`, {
        refresh: refreshToken
      }, {
        // Don't use the interceptors for this request to avoid infinite loops
        headers: {
          'Content-Type': 'application/json'
        },
        // Increase timeout for token refresh
        timeout: 10000
      });

      if (response.data && response.data.access) {
        console.log('Token refresh successful');
        localStorage.setItem('token', response.data.access);
        if (response.data.refresh) {
          localStorage.setItem('refresh_token', response.data.refresh);
        }
        return true;
      }
      console.warn('Token refresh response did not contain access token');
      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);

      // If the refresh token is invalid or expired, clear the tokens
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.warn('Clearing invalid tokens from localStorage');
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
      }

      return false;
    } finally {
      isRefreshing = false;
      refreshPromise = null;
    }
  })();

  return refreshPromise;
};

// Add a request interceptor to refresh token if it's about to expire
api.interceptors.request.use(async (config) => {
  // Skip token refresh for token-related endpoints to avoid infinite loops
  if (config.url?.includes('/token/')) {
    return config;
  }

  const token = localStorage.getItem('token');

  // If no token exists, don't try to refresh
  if (!token) {
    // Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.debug('No token available, skipping token refresh');
    }
    return config;
  }

  // Disable proactive token refresh to prevent loops
  // Token refresh will only happen on 401 responses
  // try {
  //   if (isTokenExpiring()) {
  //     console.log('Token is expiring, attempting to refresh');
  //     const refreshed = await refreshAuthToken();
  //     if (refreshed) {
  //       const newToken = localStorage.getItem('token');
  //       if (newToken) {
  //         console.log('Using new token for request');
  //         config.headers.Authorization = `Bearer ${newToken}`;
  //       }
  //     } else {
  //       console.warn('Token refresh failed, proceeding with original token');
  //     }
  //   }
  // } catch (error) {
  //   console.error('Error in token refresh interceptor:', error);
  //   // Continue with the request even if token refresh fails
  // }

  // Log all API requests for debugging (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.debug('API Request:', config.method?.toUpperCase(), config.url);
  }

  return config;
}, (error) => {
  console.error('Request interceptor error:', error);
  return Promise.reject(error);
});

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    // RBAC response interceptor removed - using simplified system
    // Any status code within the range of 2xx causes this function to trigger
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Don't retry token-related requests to avoid infinite loops
    const isTokenRequest = originalRequest?.url?.includes('/token/');

    // Check if the error is due to a timeout
    if (error.code === 'ECONNABORTED' && error.message && error.message.includes('timeout')) {
      console.error('Request timeout:', error.message);

      // Check if we've already retried and it's not a token request
      if (!originalRequest._retry && !isTokenRequest) {
        originalRequest._retry = true;
        originalRequest.timeout = 60000; // Increase timeout for retry

        console.log('Retrying request after timeout...');
        return api(originalRequest); // Retry the request
      }
    }

    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && error.response.status === 401 && !isTokenRequest) {
      console.warn('Received 401 Unauthorized response');

      // Check if we've already tried to refresh the token
      if (!originalRequest._retry) {
        originalRequest._retry = true;

        // Try to refresh the token
        const refreshed = await refreshAuthToken();
        if (refreshed) {
          // If token refresh was successful, update the Authorization header and retry
          const newToken = localStorage.getItem('token');
          if (newToken) {
            console.log('Retrying request with new token after 401');
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return api(originalRequest);
          }
        } else {
          // If token refresh failed, clear tokens and redirect to login
          console.warn('Token refresh failed after 401, clearing tokens');
          localStorage.removeItem('token');
          localStorage.removeItem('refresh_token');

          // Redirect to login page if we're in a browser environment
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
        }
      }
    }

    // Handle other types of errors
    if (error.code === 'ERR_NETWORK') {
      console.error('Network error detected:', error.message);
      // You could dispatch an event or set a global state here to show a network error banner
    } else if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('API Error Response:', error.response.status, error.response.data);

      // Handle 500 errors from token refresh
      if (error.response.status === 500 && isTokenRequest) {
        console.warn('Token refresh returned 500, clearing tokens');
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('API No Response:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('API Error:', error.message);
    }

    // Return the error for further handling
    return Promise.reject(error);
  }
);

// Auth APIs
export const authAPI = {
  login: async (credentials: { username: string; password: string }) => {
    try {
      // First, try to get CSRF token from existing sources
      let csrfToken = getCSRFToken();

      // If no CSRF token found, try to fetch it from the server
      if (!csrfToken) {
        csrfToken = await fetchCSRFToken();
      }

      // Prepare headers
      const headers: any = {
        'Content-Type': 'application/json',
      };

      // Add CSRF token if available (optional for JWT authentication)
      if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
        headers['X-CSRF-Token'] = csrfToken;
      }

      // Special handling for login to ensure proper error handling
      return api.post('/token/', credentials, {
        headers,
        validateStatus: (status) => {
          // Only consider 200 as successful for login
          return status === 200;
        }
      });
    } catch (error) {
      console.error('Login preparation error:', error);
      throw error;
    }
  },
  register: (userData: any) =>
    api.post('/user/register/', userData),
  refreshToken: (refresh: string) =>
    api.post('/token/refresh/', { refresh }),
  getCurrentUser: () =>
    api.get('/user/me/'),
  getRBACInfo: () =>
    api.get('/user/rbac-info/'),
  checkPermission: (permission: string) =>
    api.post('/user/permission-check/', { permission }),
  checkRole: (role: string) =>
    api.post('/user/permission-check/', { role }),
  validateRoles: () =>
    api.get('/user/role-validation/'),
  // Legacy endpoints for backward compatibility
  getRBACLegacy: () =>
    api.get('/user/rbac/'),
  checkPermissionLegacy: (permission: string) =>
    api.post('/user/rbac/', { permission }),
};

// User Management APIs
export const userAPI = {
  // Get all users (admin only)
  getAllUsers: () =>
    api.get('/user/users/'),

  // Get user by ID
  getUserById: (id: number) =>
    api.get(`/user/users/${id}/`),

  // Create new user (admin only)
  createUser: (userData: any) =>
    api.post('/user/users/', userData),

  // Update user (admin only)
  updateUser: (id: number, userData: any) =>
    api.put(`/user/users/${id}/`, userData),

  // Delete user (admin only)
  deleteUser: (id: number) =>
    api.delete(`/user/users/${id}/`),

  // Get all staff members (admin only)
  getAllStaff: () =>
    api.get('/user/staff/'),

  // Get staff member by ID
  getStaffById: (id: number) =>
    api.get(`/user/staff/${id}/`),

  // Create new staff member (admin only)
  createStaff: (staffData: any) =>
    api.post('/user/staff/', staffData),

  // Update staff member (admin only)
  updateStaff: (id: number, staffData: any) =>
    api.put(`/user/staff/${id}/`, staffData),

  // Delete staff member (admin only)
  deleteStaff: (id: number) =>
    api.delete(`/user/staff/${id}/`),

  // Get all roles (admin only)
  getAllRoles: () =>
    api.get('/user/roles/'),

  // Get role by ID
  getRoleById: (id: number) =>
    api.get(`/user/roles/${id}/`),

  // Create new role (admin only)
  createRole: (roleData: any) =>
    api.post('/user/roles/', roleData),

  // Update role (admin only)
  updateRole: (id: number, roleData: any) =>
    api.put(`/user/roles/${id}/`, roleData),

  // Delete role (admin only)
  deleteRole: (id: number) =>
    api.delete(`/user/roles/${id}/`),

  // Get all permissions (admin only)
  getAllPermissions: () =>
    api.get('/user/permissions/'),

  // Get permission by ID
  getPermissionById: (id: number) =>
    api.get(`/user/permissions/${id}/`),

  // Create new permission (admin only)
  createPermission: (permissionData: any) =>
    api.post('/user/permissions/', permissionData),

  // Update permission (admin only)
  updatePermission: (id: number, permissionData: any) =>
    api.put(`/user/permissions/${id}/`, permissionData),

  // Delete permission (admin only)
  deletePermission: (id: number) =>
    api.delete(`/user/permissions/${id}/`),

  // Enhanced RBAC Role management
  getAllRBACRoles: () => api.get('/user/rbac-roles/'),
  getRBACRole: (id: number) => api.get(`/user/rbac-roles/${id}/`),
  createRBACRole: (roleData: any) => api.post('/user/rbac-roles/', roleData),
  updateRBACRole: (id: number, roleData: any) => api.put(`/user/rbac-roles/${id}/`, roleData),
  deleteRBACRole: (id: number) => api.delete(`/user/rbac-roles/${id}/`),
  assignUsersToRole: (roleId: number, userIds: number[]) =>
    api.post(`/user/rbac-roles/${roleId}/assign_users/`, { user_ids: userIds }),
  removeUsersFromRole: (roleId: number, userIds: number[]) =>
    api.post(`/user/rbac-roles/${roleId}/remove_users/`, { user_ids: userIds }),
  getRoleUsers: (roleId: number) => api.get(`/user/rbac-roles/${roleId}/users/`),

  // Permission categories
  getAllPermissionCategories: () => api.get('/user/permission-categories/'),
  getPermissionCategory: (id: number) => api.get(`/user/permission-categories/${id}/`),
  createPermissionCategory: (categoryData: any) => api.post('/user/permission-categories/', categoryData),
  updatePermissionCategory: (id: number, categoryData: any) => api.put(`/user/permission-categories/${id}/`, categoryData),
  deletePermissionCategory: (id: number) => api.delete(`/user/permission-categories/${id}/`),

  // User profiles
  getAllProfiles: () => api.get('/user/profiles/'),
  getProfile: (id: number) => api.get(`/user/profiles/${id}/`),
  updateProfile: (id: number, profileData: any) => api.put(`/user/profiles/${id}/`, profileData),
  getMyProfile: () => api.get('/user/profiles/me/'),
  updateMyProfile: (profileData: any) => api.put('/user/profiles/update_me/', profileData),

  // Content types
  getAllContentTypes: () => api.get('/user/content-types/'),

  // RBAC Statistics
  getRBACStats: () => api.get('/user/rbac/stats/'),

  // Permission Management
  createPermission: (data: any) => api.post('/user/permissions/', data),
  updatePermission: (id: number, data: any) => api.put(`/user/permissions/${id}/`, data),
  deletePermission: (id: number) => api.delete(`/user/permissions/${id}/`),
};

// Application APIs
export const applicationAPI = {
  // Applicant Information
  submitApplicantInfo: (data: any) => {
    // Create a clean copy of the data
    const cleanData = { ...data };

    // Remove any problematic fields that don't exist in the model
    delete cleanData.payment_status;
    delete cleanData.pending;
    delete cleanData.is_pending;

    console.log('Sending clean data to API:', cleanData);
    return api.post('/applicant-info/', cleanData);
  },
  updateApplicantInfo: (id: string | number, data: any) => {
    // Create a clean copy of the data
    const cleanData = { ...data };

    // Remove any problematic fields that don't exist in the model
    delete cleanData.payment_status;
    delete cleanData.pending;
    delete cleanData.is_pending;

    console.log('Sending clean data to API:', cleanData);
    return api.put(`/applicant-info/${id}/`, cleanData);
  },
  getApplicantInfo: (id: string | number) =>
    api.get(`/applicant-info/${id}/`),

  getCurrentApplicantInfo: () =>
    api.get('/applicant-info/'),

  // GAT
  submitGAT: (data: any) => {
    return api.post('/applicant-gat/', data)
      .catch(error => {
        // Check if this is a duplicate GAT number error
        if (error.response?.status === 400 && error.response?.data?.GAT_No) {
          // Create a custom error response with a more user-friendly message
          const customError = { ...error };
          customError.response = { ...error.response };
          customError.response.data = {
            ...error.response.data,
            GAT_No: ["This GAT ID is already taken by someone else."]
          };
          throw customError;
        }
        throw error;
      });
  },
  updateGAT: (id: number, data: any) => {
    return api.put(`/applicant-gat/${id}/`, data)
      .catch(error => {
        // Check if this is a duplicate GAT number error
        if (error.response?.status === 400 && error.response?.data?.GAT_No) {
          // Create a custom error response with a more user-friendly message
          const customError = { ...error };
          customError.response = { ...error.response };
          customError.response.data = {
            ...error.response.data,
            GAT_No: ["This GAT ID is already taken by someone else."]
          };
          throw customError;
        }
        throw error;
      });
  },
  deleteGAT: (id: number) =>
    api.delete(`/applicant-gat/${id}/`),
  getGAT: (id: string | number) =>
    api.get(`/applicant-gat/${id}/`),
  getCurrentGAT: () =>
    api.get('/applicant-gat/'),

  // Program Selection
  submitProgramSelection: (data: any) => {
    console.log('Submitting program selection with data:', data);

    // Create a clean copy of the data
    const cleanData = { ...data };

    // If application_info is not provided, set it to null
    if (!cleanData.application_info) {
      cleanData.application_info = null;
    }

    // Ensure sponsorship is always provided
    if (!cleanData.sponsorship || cleanData.sponsorship === '') {
      console.log('Sponsorship is missing, setting to Self');
      cleanData.sponsorship = 'Self';
    }

    // Validate sponsorship value
    const validSponsorships = [
      'Ministry of Education',
      'Other Government Office',
      'University of Gondar',
      'Private Office ', // Note the space after "Office"
      'Self'
    ];

    // Fix the "Private Office" value to match the model's expected format
    if (cleanData.sponsorship === 'Private Office') {
      console.log('Fixing "Private Office" to "Private Office " to match model format');
      cleanData.sponsorship = 'Private Office ';
    }

    if (!validSponsorships.includes(cleanData.sponsorship)) {
      console.log(`Invalid sponsorship value: ${cleanData.sponsorship}, setting to Self`);
      cleanData.sponsorship = 'Self';
    }

    // If gat is provided, ensure it's properly formatted as a number
    if (cleanData.gat) {
      // Convert to number if it's a string
      if (typeof cleanData.gat === 'string') {
        cleanData.gat = parseInt(cleanData.gat, 10);
      }
      console.log('GAT ID included in program selection:', cleanData.gat, 'type:', typeof cleanData.gat);
    } else {
      console.warn('No GAT ID provided in the request data');
    }

    console.log('Final clean data being sent to API:', cleanData);

    return api.post('/program-selection/', cleanData)
      .then(response => {
        console.log('Program selection submission successful:', response);
        return response;
      })
      .catch(error => {
        console.error('Program selection submission failed:', error);
        console.error('Error response data:', error.response?.data);
        console.error('Error response status:', error.response?.status);
        console.error('Error response headers:', error.response?.headers);
        // Log the full error object for debugging
        console.error('Full error object:', JSON.stringify(error, null, 2));
        throw error;
      });
  },

  // Link GAT to Program Selection
  linkGATToProgramSelection: (programSelectionId: number, gatId: number) => {
    console.log(`Linking GAT ${gatId} to Program Selection ${programSelectionId}`);
    return api.put(`/program-selection/${programSelectionId}/`, { gat: gatId })
      .then(response => {
        console.log('Successfully linked GAT to Program Selection:', response);
        return response;
      })
      .catch(error => {
        console.error('Failed to link GAT to Program Selection:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },
  getProgramSelection: (id: string | number) =>
    api.get(`/program-selection/${id}/`),
  getCurrentProgramSelection: () =>
    api.get('/program-selection/'),

  // Get program selection by GAT ID
  getProgramSelectionByGAT: (gatId: number) => {
    console.log(`Fetching program selection for GAT ID: ${gatId}`);
    return api.get('/program-selection/', {
      params: { gat: gatId }
    })
    .then(response => {
      console.log('Program selection by GAT response:', response);
      return response;
    })
    .catch(error => {
      console.error('Error fetching program selection by GAT:', error);
      console.error('Error response:', error.response?.data);
      throw error;
    });
  },
  updateProgramSelection: (id: number, data: any) => {
    console.log(`Updating program selection ${id} with data:`, data);

    // Create a clean copy of the data
    const cleanData = { ...data };

    // If application_info is not provided, set it to null
    if (!cleanData.application_info) {
      cleanData.application_info = null;
    }

    // Ensure sponsorship is always provided
    if (!cleanData.sponsorship || cleanData.sponsorship === '') {
      console.log('Sponsorship is missing in update, setting to Self');
      cleanData.sponsorship = 'Self';
    }

    // Validate sponsorship value
    const validSponsorships = [
      'Ministry of Education',
      'Other Government Office',
      'University of Gondar',
      'Private Office ', // Note the space after "Office"
      'Self'
    ];

    // Fix the "Private Office" value to match the model's expected format
    if (cleanData.sponsorship === 'Private Office') {
      console.log('Fixing "Private Office" to "Private Office " to match model format');
      cleanData.sponsorship = 'Private Office ';
    }

    if (!validSponsorships.includes(cleanData.sponsorship)) {
      console.log(`Invalid sponsorship value in update: ${cleanData.sponsorship}, setting to Self`);
      cleanData.sponsorship = 'Self';
    }

    // If gat is provided, ensure it's properly formatted as a number
    if (cleanData.gat) {
      // Convert to number if it's a string
      if (typeof cleanData.gat === 'string') {
        cleanData.gat = parseInt(cleanData.gat, 10);
      }
      console.log('GAT ID included in program selection update:', cleanData.gat, 'type:', typeof cleanData.gat);
    } else {
      console.warn('No GAT ID provided in the update request data');
    }

    console.log('Final clean data being sent to API for update:', cleanData);

    return api.put(`/program-selection/${id}/`, cleanData)
      .then(response => {
        console.log('Program selection update successful:', response);
        return response;
      })
      .catch(error => {
        console.error('Program selection update failed:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },
  deleteProgramSelection: (id: number) =>
    api.delete(`/program-selection/${id}/`),

  // Documentation
  submitDocumentation: (data: any) => {
    console.log('Submitting documentation with data:', data);
    return api.post('/documentation/', data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
      .then(response => {
        console.log('Documentation submission successful:', response);
        return response;
      })
      .catch(error => {
        console.error('Documentation submission failed:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },
  getDocumentation: () =>
    api.get('/documentation/'),
  getCurrentDocumentation: () =>
    api.get('/documentation/'),
  updateDocumentation: (id: number, data: any) => {
    console.log(`Updating documentation ${id} with data:`, data);
    return api.put(`/documentation/${id}/`, data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
      .then(response => {
        console.log('Documentation update successful:', response);
        return response;
      })
      .catch(error => {
        console.error('Documentation update failed:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },

  // Payment
  submitPayment: (data: any) => {
    console.log('Submitting payment with data:', data);
    return api.post('/payment/', data)
      .then(response => {
        console.log('Payment submission successful:', response);
        return response;
      })
      .catch(error => {
        console.error('Payment submission failed:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },
  getPayment: () =>
    api.get('/payment/'),
  getCurrentPayment: () =>
    api.get('/payment/'),
  getPaymentByGAT: (gatId: number) => {
    console.log(`Fetching payment for GAT ID: ${gatId}`);
    return api.get(`/payment/gat/${gatId}/`)
      .then(response => {
        console.log('Payment by GAT response:', response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching payment by GAT:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },
  createPaymentByGAT: (gatId: number, paymentData: any) => {
    console.log(`Creating payment for GAT ID: ${gatId} with data:`, paymentData);
    return api.post(`/payment/gat/${gatId}/`, paymentData)
      .then(response => {
        console.log('Payment creation response:', response);
        return response;
      })
      .catch(error => {
        console.error('Error creating payment:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },
  updatePayment: (id: number, data: any) => {
    console.log(`Updating payment ${id} with data:`, data);
    return api.put(`/payment/${id}/`, data)
      .then(response => {
        console.log('Payment update successful:', response);
        return response;
      })
      .catch(error => {
        console.error('Payment update failed:', error);
        console.error('Error response:', error.response?.data);
        throw error;
      });
  },

  // Admission Types
  getAdmissionTypes: () =>
    api.get('/admission-types/').catch(error => {
      console.error('Error fetching admission types:', error);
      return { data: [] };
    }),

  // Public endpoints for filtering
  getPublicColleges: () =>
    api.get('/colleges/public/').catch(error => {
      console.error('Error fetching colleges:', error);
      return { data: [] };
    }),
  getPublicDepartments: (collegeId?: number) =>
    api.get('/departments/public/', { params: { college: collegeId } }).catch(error => {
      console.error('Error fetching departments:', error);
      return { data: [] };
    }),
  getPublicPrograms: () =>
    api.get('/programs/public/').catch(error => {
      console.error('Error fetching programs:', error);
      return { data: [] };
    }),
  getApplicationInfo: (id: number) =>
    api.get(`/application-information/${id}/`),
  filterApplicationInformation: (filters: any) =>
    api.get('/application-information/filter/', {
      params: filters,
      // Add a timeout to prevent long-running requests
      timeout: 5000,
      // Add validation to ensure the request is properly formatted
      validateStatus: (status) => {
        return status < 500; // Resolve only if the status code is less than 500
      }
    }).catch(error => {
      console.error('Error filtering application information:', error);
      return { data: [] };
    }),

  // Get program application info with registration period status
  getProgramApplicationInfo: (programId: number) =>
    api.get('/program-application/', {
      params: { program_id: programId },
      timeout: 5000,
      validateStatus: (status) => {
        return status < 500;
      }
    }).catch(error => {
      console.error(`Error getting program application info for program ${programId}:`, error);
      return {
        data: {
          application_info: [],
          registration_active: false,
          registration_message: 'Error fetching registration information.'
        }
      };
    }),

  // Documentation
  uploadDocuments: (data: FormData) =>
    api.post('/documentation/', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),

  // Payment
  processPayment: (data: any) =>
    api.post('/payment/', data),
  getPaymentStatus: (id: string | number) =>
    api.get(`/payment/${id}/`),
};



// Year APIs
export const yearAPI = {
  // Get all years (authenticated)
  getAllYears: () =>
    api.get('/years/'),

  // Get all years (public)
  getPublicYears: () =>
    api.get('/years/public/'),

  // Get year by UUID
  getYearById: (uuid: string) =>
    api.get(`/years/${uuid}/`),

  // Create new year
  createYear: (data: any) =>
    api.post('/years/', data),

  // Update year
  updateYear: (uuid: string, data: any) =>
    api.put(`/years/${uuid}/`, data),

  // Delete year
  deleteYear: (uuid: string) =>
    api.delete(`/years/delete/${uuid}/`),
};

// Certificate Type APIs
export const certificateTypeAPI = {
  // Get all certificate types
  getAllCertificateTypes: () =>
    api.get('/certificate-types/'),

  // Get active certificate types only
  getActiveCertificateTypes: () =>
    api.get('/certificate-types/active/'),

  // Get certificate type by UUID
  getCertificateTypeById: (uuid: string) =>
    api.get(`/certificate-types/${uuid}/`),

  // Create new certificate type
  createCertificateType: (data: any) =>
    api.post('/certificate-types/', data),

  // Update certificate type
  updateCertificateType: (uuid: string, data: any) =>
    api.put(`/certificate-types/${uuid}/`, data),

  // Delete certificate type
  deleteCertificateType: (uuid: string) =>
    api.delete(`/certificate-types/${uuid}/`),

  // Toggle certificate type status
  toggleCertificateTypeStatus: (uuid: string) =>
    api.post(`/certificate-types/${uuid}/toggle_status/`),

  // Search certificate types
  searchCertificateTypes: (params: any) =>
    api.get('/certificate-types/', { params }),
};

// Document Type APIs
export const documentTypeAPI = {
  // Get all document types
  getAllDocumentTypes: () =>
    api.get('/document-types/'),

  // Get active document types only
  getActiveDocumentTypes: () =>
    api.get('/document-types/active/'),

  // Get document type by ID
  getDocumentTypeById: (id: string) =>
    api.get(`/document-types/${id}/`),

  // Create new document type
  createDocumentType: (data: any) =>
    api.post('/document-types/', data),

  // Update document type
  updateDocumentType: (id: string, data: any) =>
    api.put(`/document-types/${id}/`, data),

  // Delete document type
  deleteDocumentType: (id: string) =>
    api.delete(`/document-types/${id}/`),

  // Toggle document type status
  toggleDocumentTypeStatus: (id: string) =>
    api.post(`/document-types/${id}/toggle_status/`),

  // Search document types
  searchDocumentTypes: (params: any) =>
    api.get('/document-types/', { params }),
};

// Service Type APIs
export const serviceTypeAPI = {
  // Get all service types
  getAllServiceTypes: () =>
    api.get('/service-types/'),

  // Get active service types only
  getActiveServiceTypes: () =>
    api.get('/service-types/active/'),

  // Get active service types for public access (no authentication required)
  getPublicServiceTypes: () =>
    axios.get(`${DYNAMIC_API_BASE_URL}/service-types/public/`),

  // Get service type by ID
  getServiceTypeById: (id: string) =>
    api.get(`/service-types/${id}/`),

  // Create new service type
  createServiceType: (data: any) =>
    api.post('/service-types/', data),

  // Update service type
  updateServiceType: (id: string, data: any) =>
    api.put(`/service-types/${id}/`, data),

  // Delete service type
  deleteServiceType: (id: string) =>
    api.delete(`/service-types/${id}/`),

  // Toggle service type status
  toggleServiceTypeStatus: (id: string) =>
    api.post(`/service-types/${id}/toggle_status/`),

  // Search service types
  searchServiceTypes: (params: any) =>
    api.get('/service-types/', { params }),
};

// Settings APIs
import settingsAPI from './settingsAPI';
export { settingsAPI };

// Communication APIs
import communicationAPI from './communicationAPI';
export { communicationAPI };

// Downloadable APIs
import downloadableAPI from './downloadableAPI';
export { downloadableAPI };

// Simple cache implementation
const apiCache = {
  cache: new Map(),
  get: (key: string) => {
    const cachedItem = apiCache.cache.get(key);
    if (!cachedItem) return null;

    // Check if cache is still valid (5 minutes)
    if (Date.now() - cachedItem.timestamp > 5 * 60 * 1000) {
      apiCache.cache.delete(key);
      return null;
    }

    return cachedItem.data;
  },
  set: (key: string, data: any) => {
    apiCache.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  },
  clear: () => {
    apiCache.cache.clear();
  }
};

// Graduate Verification APIs
export const graduateVerificationAPI = {
  // Graduate data with pagination and caching
  getAllGraduates: (params = {}) => {
    // Add default pagination parameters
    const paginatedParams = {
      page: 1,
      page_size: 20,
      ...params
    };

    const cacheKey = 'graduates-' + JSON.stringify(paginatedParams);
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached graduates data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/', { params: paginatedParams })
      .then(response => {
        // Cache for 2 minutes for paginated data
        apiCache.set(cacheKey, response, 2 * 60 * 1000);
        return response;
      });
  },
  getGraduateById: (id: number) => {
    const cacheKey = `graduate-${id}`;
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached graduate data for ID ${id}`);
      return Promise.resolve(cachedData);
    }

    return api.get(`/graduate-verifications/${id}/`)
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      });
  },
  createGraduate: (data: any) => {
    // Clear relevant caches when creating a new graduate
    return api.post('/graduate-verifications/', data)
      .then(response => {
        // Clear caches that would be affected by this change
        apiCache.cache.delete('all-graduates-{}');
        apiCache.cache.delete('report-by-department');
        apiCache.cache.delete('report-by-college');
        apiCache.cache.delete('report-by-gender');
        apiCache.cache.delete('report-by-year');
        apiCache.cache.delete('report-by-program');
        apiCache.cache.delete('report-by-admission-classification');
        apiCache.cache.delete('gpa-stats');
        // Clear recent graduates caches
        ['1', '3', '7', '15', '30'].forEach(period => {
          apiCache.cache.delete(`recent-graduates-${period}`);
        });
        console.log('Cleared relevant caches after creating graduate');
        return response;
      });
  },
  updateGraduate: (id: number, data: any) => {
    // Clear relevant caches when updating a graduate
    return api.put(`/graduate-verifications/${id}/`, data)
      .then(response => {
        // Clear caches that would be affected by this change
        apiCache.cache.delete(`graduate-${id}`);
        apiCache.cache.delete('all-graduates-{}');
        apiCache.cache.delete('report-by-department');
        apiCache.cache.delete('report-by-college');
        apiCache.cache.delete('report-by-gender');
        apiCache.cache.delete('report-by-year');
        apiCache.cache.delete('report-by-program');
        apiCache.cache.delete('report-by-admission-classification');
        apiCache.cache.delete('gpa-stats');
        // Clear recent graduates caches
        ['1', '3', '7', '15', '30'].forEach(period => {
          apiCache.cache.delete(`recent-graduates-${period}`);
        });
        console.log('Cleared relevant caches after updating graduate');
        return response;
      });
  },
  deleteGraduate: (id: number) => {
    // Clear relevant caches when deleting a graduate
    return api.delete(`/graduate-verifications/delete/${id}/`)
      .then(response => {
        // Clear caches that would be affected by this change
        apiCache.cache.delete(`graduate-${id}`);
        apiCache.cache.delete('all-graduates-{}');
        apiCache.cache.delete('report-by-department');
        apiCache.cache.delete('report-by-college');
        apiCache.cache.delete('report-by-gender');
        apiCache.cache.delete('report-by-year');
        apiCache.cache.delete('report-by-program');
        apiCache.cache.delete('report-by-admission-classification');
        apiCache.cache.delete('gpa-stats');
        // Clear recent graduates caches
        ['1', '3', '7', '15', '30'].forEach(period => {
          apiCache.cache.delete(`recent-graduates-${period}`);
        });
        console.log('Cleared relevant caches after deleting graduate');
        return response;
      });
  },

  // Soft delete a graduate
  softDeleteGraduate: (id: number) => {
    return api.delete(`/graduate-verifications/soft-delete/${id}/`)
      .then(response => {
        // Clear caches that would be affected by this change
        apiCache.cache.delete(`graduate-${id}`);
        apiCache.cache.delete('all-graduates-{}');
        apiCache.cache.delete('report-by-department');
        apiCache.cache.delete('report-by-college');
        apiCache.cache.delete('report-by-gender');
        apiCache.cache.delete('report-by-year');
        apiCache.cache.delete('report-by-program');
        apiCache.cache.delete('report-by-admission-classification');
        apiCache.cache.delete('gpa-stats');
        // Clear recent graduates caches
        ['1', '3', '7', '15', '30'].forEach(period => {
          apiCache.cache.delete(`recent-graduates-${period}`);
        });
        console.log('Cleared relevant caches after soft deleting graduate');
        return response;
      });
  },

  // Restore a soft-deleted graduate
  restoreGraduate: (id: number) => {
    return api.post(`/graduate-verifications/restore/${id}/`)
      .then(response => {
        // Clear caches that would be affected by this change
        apiCache.cache.delete(`graduate-${id}`);
        apiCache.cache.delete('all-graduates-{}');
        apiCache.cache.delete('report-by-department');
        apiCache.cache.delete('report-by-college');
        apiCache.cache.delete('report-by-gender');
        apiCache.cache.delete('report-by-year');
        apiCache.cache.delete('report-by-program');
        apiCache.cache.delete('report-by-admission-classification');
        apiCache.cache.delete('gpa-stats');
        // Clear recent graduates caches
        ['1', '3', '7', '15', '30'].forEach(period => {
          apiCache.cache.delete(`recent-graduates-${period}`);
        });
        console.log('Cleared relevant caches after restoring graduate');
        return response;
      });
  },

  // Get soft-deleted graduates
  getDeletedGraduates: (params = {}) => {
    console.log('🔍 getDeletedGraduates called with params:', params);
    const cacheKey = `deleted-graduates-${JSON.stringify(params)}`;
    console.log('🔑 Cache key:', cacheKey);

    // Bypass cache for debugging
    console.log('📡 Making direct API call to /graduate-verifications/deleted/');
    return api.get('/graduate-verifications/deleted/', { params })
      .then(response => {
        console.log('✅ Direct API response:', response);
        console.log('📊 Response data:', response.data);
        console.log('📈 Data type:', typeof response.data);
        console.log('📈 Is array:', Array.isArray(response.data));
        return response;
      })
      .catch(error => {
        console.error('❌ API error:', error);
        console.error('❌ Error response:', error.response?.data);
        throw error;
      });
  },

  // Get deleted graduate statistics
  getDeletedStatistics: () => {
    console.log('📊 getDeletedStatistics called');

    // Bypass cache for debugging
    console.log('📡 Making direct API call to /graduate-verifications/deleted-statistics/');
    return api.get('/graduate-verifications/deleted-statistics/')
      .then(response => {
        console.log('✅ Deleted statistics API response:', response);
        console.log('📊 Response data:', response.data);
        return response;
      })
      .catch(error => {
        console.error('❌ Deleted statistics API error:', error);
        console.error('❌ Error response:', error.response?.data);
        throw error;
      });
  },

  // Restore a soft-deleted graduate
  restoreGraduate: (id: number) => {
    console.log('🔄 Restoring graduate with ID:', id);
    return api.post(`/graduate-verifications/restore/${id}/`)
      .then(response => {
        console.log('✅ Graduate restored successfully:', response);
        // Clear relevant caches
        apiCache.clear('deleted-graduates');
        apiCache.clear('deleted-graduate-statistics');
        apiCache.clear('graduates');
        return response;
      })
      .catch(error => {
        console.error('❌ Error restoring graduate:', error);
        throw error;
      });
  },

  // Public search
  searchGraduates: (params: any) => {
    console.log('Searching graduates with params:', params);
    return api.get('/graduate-verifications/public/', { params })
      .then(response => {
        console.log('Search graduates response:', response.data);
        return response;
      })
      .catch(error => {
        console.error('Error searching graduates:', error);
        console.error('Error response:', error.response?.data);
        throw error; // Re-throw to handle in the component
      });
  },
  filterGraduates: (params: any) => {
    console.log('Filtering graduates with params:', params);
    return api.get('/graduate-verifications/filter/', { params })
      .then(response => {
        console.log('Filter graduates response:', response.data);
        return response;
      })
      .catch(error => {
        console.error('Error filtering graduates:', error);
        console.error('Error response:', error.response?.data);
        throw error; // Re-throw to handle in the component
      });
  },

  // Admin search with pagination
  searchGraduatesAdmin: (searchParams: any) => {
    // Add pagination to admin search
    const paginatedSearchParams = {
      page: 1,
      page_size: 20,
      ...searchParams
    };

    return api.get('/graduate-verifications/', { params: paginatedSearchParams });
  },

  // Get graduates for export (without pagination)
  getGraduatesForExport: (searchParams = {}) => {
    // For export, we need all results without pagination
    const exportParams = {
      ...searchParams,
      page_size: 10000, // Large page size for export
    };

    return api.get('/graduate-verifications/', { params: exportParams });
  },

  // CSV import
  importCSV: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/graduate-verifications/import-csv/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).catch(error => {
      console.error('Error importing CSV:', error);
      throw error; // Re-throw to handle in the component
    });
  },

  // Recent graduates API
  getRecentGraduates: (period = '7') => {
    const cacheKey = `recent-graduates-${period}`;
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached recent graduates data');
      return Promise.resolve(cachedData);
    }

    console.log(`Making API call to /graduate-verifications/recent/ with period=${period}`);
    return api.get('/graduate-verifications/recent/', { params: { period } })
      .then(response => {
        console.log('Recent graduates API response:', response.data);
        // Cache for 1 minute for recent data
        apiCache.set(cacheKey, response, 1 * 60 * 1000);
        return response;
      })
      .catch(error => {
        console.error('Recent graduates API error:', error);
        console.error('Error response data:', error.response?.data);
        throw error;
      });
  },

  // Reports with caching
  getReportByDepartment: () => {
    const cacheKey = 'report-by-department';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached department report data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/by-department/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching department report:', error);
        return { data: { labels: [], data: [] } };
      });
  },
  getReportByCollege: () => {
    const cacheKey = 'report-by-college';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached college report data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/by-college/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching college report:', error);
        return { data: { labels: [], data: [] } };
      });
  },
  getReportByGender: () => {
    const cacheKey = 'report-by-gender';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached gender report data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/by-gender/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching gender report:', error);
        return { data: { labels: [], data: [] } };
      });
  },
  getReportByYear: () => {
    const cacheKey = 'report-by-year';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached year report data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/by-year/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching year report:', error);
        return { data: { labels: [], data: [] } };
      });
  },
  getReportByProgram: () => {
    const cacheKey = 'report-by-program';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached program report data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/by-program/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching program report:', error);
        return { data: { labels: [], data: [] } };
      });
  },
  getReportByAdmissionClassification: () => {
    const cacheKey = 'report-by-admission-classification';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached admission classification report data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/by-admission-classification/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching admission classification report:', error);
        return { data: { labels: [], data: [] } };
      });
  },
  getGPAStats: () => {
    const cacheKey = 'gpa-stats';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached GPA statistics data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/gpa-stats/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching GPA statistics:', error);
        return {
          data: {
            system_avg_gpa: 0,
            college_gpa: [],
            department_gpa: [],
            gpa_distribution: [],
            total_graduates: 0
          }
        };
      });
  },

  getGenderTrendsByYear: () => {
    const cacheKey = 'gender-trends-by-year';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached gender trends by year data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/gender-trends-by-year/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching gender trends by year:', error);
        return { data: { gender_trends: [] } };
      });
  },



  getProgramGPATrends: () => {
    const cacheKey = 'program-gpa-trends';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached program GPA trends data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/report/program-gpa-trends/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching program GPA trends:', error);
        return { data: { program_trends: [], programs: [] } };
      });
  },

  // Get available years for filtering
  getAvailableYears: () => {
    const cacheKey = 'available-years';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached available years data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/available-years/')
      .then(response => {
        apiCache.set(cacheKey, response);
        return response;
      })
      .catch(error => {
        console.error('Error fetching available years:', error);
        // Fallback: extract years from existing data
        return { data: { years: [] } };
      });
  },

  // Get available colleges for filtering
  getAvailableColleges: () => {
    const cacheKey = 'available-colleges';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached available colleges data');
      return Promise.resolve(cachedData);
    }

    return api.get('/graduate-verifications/available-colleges/')
      .then(response => {
        // Transform the response to match expected format
        const transformedResponse = {
          ...response,
          data: response.data.colleges || []
        };
        apiCache.set(cacheKey, transformedResponse);
        return transformedResponse;
      })
      .catch(error => {
        console.error('Error fetching available colleges:', error);
        // Fallback: use existing colleges endpoint
        return graduateVerificationAPI.getColleges();
      });
  },

  // Cache management
  clearCache: () => {
    apiCache.clear();
    console.log('API cache cleared');
    return Promise.resolve({ success: true });
  },

  // Related data with caching
  getColleges: () => {
    const cacheKey = 'verification-colleges';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached colleges data');
      return Promise.resolve(cachedData);
    }

    return api.get('/verification-colleges/public/')
      .then(response => {
        console.log('Raw colleges API response:', response);

        // Process the response if needed
        let processedResponse = response;

        // If the response has a results array, use that directly
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.results)) {
          processedResponse = { ...response, data: response.data.results };
          console.log('Processed colleges data to extract results array');
        }

        apiCache.set(cacheKey, processedResponse);
        return processedResponse;
      })
      .catch(error => {
        console.error('Error fetching verification colleges:', error);
        return { data: [] };
      });
  },
  getDepartments: () => {
    const cacheKey = 'verification-departments';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached departments data');
      return Promise.resolve(cachedData);
    }

    return api.get('/verification-departments/public/')
      .then(response => {
        console.log('Raw departments API response:', response);

        // Process the response if needed
        let processedResponse = response;

        // If the response has a results array, use that directly
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.results)) {
          processedResponse = { ...response, data: response.data.results };
          console.log('Processed departments data to extract results array');
        }

        apiCache.set(cacheKey, processedResponse);
        return processedResponse;
      })
      .catch(error => {
        console.error('Error fetching verification departments:', error);
        return { data: [] };
      });
  },
  getFieldsOfStudy: () => {
    const cacheKey = 'verification-fields-of-study';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached fields of study data');
      return Promise.resolve(cachedData);
    }

    // Use only the verification endpoint as requested
    return api.get('/verification-fields-of-study/public/')
      .then(response => {
        // Log the raw response to understand its structure
        console.log('Raw fields of study API response:', response);

        // Process the response if needed
        let processedResponse = response;

        // If the response has a results array, use that directly
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.results)) {
          processedResponse = { ...response, data: response.data.results };
          console.log('Processed fields of study data to extract results array');
        }

        apiCache.set(cacheKey, processedResponse);
        return processedResponse;
      })
      .catch(error => {
        console.error('Error fetching verification fields of study:', error);
        return { data: [] };
      });
  },
  getPrograms: () => {
    const cacheKey = 'verification-programs';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached programs data');
      return Promise.resolve(cachedData);
    }

    return api.get('/verification-programs/public/')
      .then(response => {
        console.log('Raw programs API response:', response);

        // Process the response if needed
        let processedResponse = response;

        // If the response has a results array, use that directly
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.results)) {
          processedResponse = { ...response, data: response.data.results };
          console.log('Processed programs data to extract results array');
        }

        apiCache.set(cacheKey, processedResponse);
        return processedResponse;
      })
      .catch(error => {
        console.error('Error fetching verification programs:', error);
        return { data: [] };
      });
  },
  getAdmissionClassifications: () => {
    const cacheKey = 'admission-classifications';
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached admission classifications data');
      return Promise.resolve(cachedData);
    }

    return api.get('/admission-classifications/public/')
      .then(response => {
        console.log('Raw admission classifications API response:', response);

        // Process the response if needed
        let processedResponse = response;

        // If the response has a results array, use that directly
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.results)) {
          processedResponse = { ...response, data: response.data.results };
          console.log('Processed admission classifications data to extract results array');
        }

        apiCache.set(cacheKey, processedResponse);
        return processedResponse;
      })
      .catch(error => {
        console.error('Error fetching admission classifications:', error);
        return { data: [] };
      });
  },
};

// Using real API endpoints instead of mock endpoints

// Add a console log to debug API calls (only in development)
api.interceptors.request.use(config => {
  if (process.env.NODE_ENV === 'development') {
    console.debug('API Request:', config.method?.toUpperCase(), config.url);
  }
  return config;
});

// Export the api instance as default
export default api;

// Handle response and token refresh
api.interceptors.response.use(
  response => {
    // Check if this is a token refresh response and update the session timestamp
    if (response.config.url?.includes('/token/refresh/') && response.data.access) {
      // Update the last activity timestamp
      localStorage.setItem('lastActivity', Date.now().toString());
      console.log('Token refreshed, updated last activity timestamp');
    }
    return response;
  },
  async error => {
    const originalRequest = error.config;

    // If the error is 401 and we haven't already tried to refresh the token
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${DYNAMIC_API_BASE_URL}/token/refresh/`, {
            refresh: refreshToken
          });

          // If we get a new token, update it and retry the original request
          if (response.data.access) {
            localStorage.setItem('token', response.data.access);
            if (response.data.refresh) {
              localStorage.setItem('refresh_token', response.data.refresh);
            }

            // Update the last activity timestamp
            localStorage.setItem('lastActivity', Date.now().toString());

            api.defaults.headers.common['Authorization'] = `Bearer ${response.data.access}`;
            originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;

            console.log('Token refreshed successfully, retrying request');
            return api(originalRequest);
          }
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);

        // Check if the refresh token is expired
        if (refreshError.response?.status === 401) {
          console.log('Refresh token expired, redirecting to login');
          // Show a user-friendly message
          if (typeof window !== 'undefined') {
            // Just use alert for simplicity
            alert('Your session has expired. Please log in again.');
          }
        }

        // If refresh fails, clear auth data and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('user');
        localStorage.removeItem('lastActivity');

        // Only redirect if we're in a browser environment
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }

    // For other errors, just log and reject
    if (error.response) {
      console.error(`API Error ${error.response.status}:`, error.config?.url, error.response.data);
    } else {
      console.error('API Error:', error.message);
    }
    return Promise.reject(error);
  }
);

// Term API
export const termAPI = {
  // Get all terms with optional filtering
  getTerms: (params?: any) => api.get('/terms/', { params }),

  // Get specific term by ID
  getTerm: (id: string) => api.get(`/terms/${id}/`),

  // Create new term
  createTerm: (data: any) => api.post('/terms/', data),

  // Update term
  updateTerm: (id: string, data: any) => api.put(`/terms/${id}/`, data),

  // Partially update term
  patchTerm: (id: string, data: any) => api.patch(`/terms/${id}/`, data),

  // Delete term
  deleteTerm: (id: string) => api.delete(`/terms/${id}/`),
};