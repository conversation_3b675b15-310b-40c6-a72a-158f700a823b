import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { settingsAPI } from '@/services/api';
import { QuickLink, ReorderItem } from '@/services/settingsAPI';
import { Loader2, Plus, Save, Trash2, ExternalLink, GripVertical, RefreshCw } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const QuickLinksManager: React.FC = () => {
  const { quickLinks, refreshSettings } = useSettings();
  const [links, setLinks] = useState<QuickLink[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLink, setEditingLink] = useState<QuickLink | null>(null);
  const [formData, setFormData] = useState<QuickLink>({
    name: '',
    url: '',
    description: '',
    is_external: true,
    order: 0,
    is_active: true,
  });

  // Initialize links from context
  useEffect(() => {
    if (quickLinks) {
      setLinks([...quickLinks].sort((a, b) => a.order - b.order));
    }
  }, [quickLinks]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Open dialog for creating a new link
  const handleAddLink = () => {
    setEditingLink(null);
    setFormData({
      name: '',
      url: '',
      description: '',
      is_external: true,
      order: links.length,
      is_active: true,
    });
    setIsDialogOpen(true);
  };

  // Open dialog for editing an existing link
  const handleEditLink = (link: QuickLink) => {
    setEditingLink(link);
    setFormData({
      ...link,
    });
    setIsDialogOpen(true);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (editingLink) {
        // Update existing link
        await settingsAPI.updateQuickLink(editingLink.id!, formData);
        toast.success('Quick link updated successfully');
      } else {
        // Create new link
        await settingsAPI.createQuickLink(formData);
        toast.success('Quick link created successfully');
      }

      // Refresh settings and close dialog
      await refreshSettings();
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error saving quick link:', error);
      toast.error('Failed to save quick link');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle link deletion
  const handleDeleteLink = async (id: number) => {
    if (!confirm('Are you sure you want to delete this link?')) {
      return;
    }

    setIsLoading(true);

    try {
      await settingsAPI.deleteQuickLink(id);
      toast.success('Quick link deleted successfully');
      await refreshSettings();
    } catch (error) {
      console.error('Error deleting quick link:', error);
      toast.error('Failed to delete quick link');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle moving a link up or down
  const handleMoveLink = async (id: number, direction: 'up' | 'down') => {
    const currentIndex = links.findIndex(link => link.id === id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === links.length - 1)
    ) {
      return; // Can't move further in this direction
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const newLinks = [...links];
    const [movedItem] = newLinks.splice(currentIndex, 1);
    newLinks.splice(newIndex, 0, movedItem);

    // Update local state immediately for better UX
    setLinks(newLinks);

    // Prepare data for API
    const reorderData: ReorderItem[] = newLinks.map((link, index) => ({
      id: link.id!,
      order: index,
    }));

    try {
      await settingsAPI.reorderQuickLinks(reorderData);
      await refreshSettings();
    } catch (error) {
      console.error('Error reordering quick links:', error);
      toast.error('Failed to reorder quick links');
      // Revert to original order on error
      setLinks([...quickLinks].sort((a, b) => a.order - b.order));
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Quick Links</CardTitle>
          <CardDescription>
            Manage quick links displayed in the footer
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => refreshSettings()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleAddLink} disabled={isLoading}>
            <Plus className="h-4 w-4 mr-2" />
            Add Link
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {links.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No quick links found. Click "Add Link" to create one.
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>External</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead style={{ width: '120px' }}>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {links.map((link, index) => (
                  <TableRow key={link.id?.toString() || index.toString()}>
                    <TableCell className="font-medium">{link.name}</TableCell>
                    <TableCell className="max-w-[200px] truncate">
                      <a
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline flex items-center"
                      >
                        {link.url}
                        {link.is_external && (
                          <ExternalLink className="h-3 w-3 ml-1" />
                        )}
                      </a>
                    </TableCell>
                    <TableCell>
                      {link.is_external ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          External
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Internal
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      {link.is_active ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Inactive
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveLink(link.id!, 'up')}
                          disabled={index === 0}
                          className="h-8 w-8 p-0"
                        >
                          <span className="sr-only">Move Up</span>
                          ↑
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveLink(link.id!, 'down')}
                          disabled={index === links.length - 1}
                          className="h-8 w-8 p-0"
                        >
                          <span className="sr-only">Move Down</span>
                          ↓
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditLink(link)}
                          className="h-8 w-8 p-0"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-4 w-4 text-blue-600"
                          >
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3Z" />
                          </svg>
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteLink(link.id!)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4 text-red-600" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Quick Link Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[550px]">
            <DialogHeader>
              <DialogTitle>
                {editingLink ? 'Edit Quick Link' : 'Add Quick Link'}
              </DialogTitle>
              <DialogDescription>
                {editingLink
                  ? 'Update the details of this quick link'
                  : 'Create a new quick link for the footer'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="url" className="text-right">
                    URL
                  </Label>
                  <Input
                    id="url"
                    name="url"
                    type="url"
                    value={formData.url}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="https://example.com"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description || ''}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="Brief description of the link (optional)"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right text-sm font-medium">
                    External Link
                  </div>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch
                      id="is_external"
                      checked={formData.is_external}
                      onCheckedChange={(checked) =>
                        handleSwitchChange('is_external', checked)
                      }
                    />
                    <span className="text-sm cursor-pointer">
                      {formData.is_external
                        ? 'Opens in a new tab'
                        : 'Opens in the same tab'}
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right text-sm font-medium">
                    Active
                  </div>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) =>
                        handleSwitchChange('is_active', checked)
                      }
                    />
                    <span className="text-sm cursor-pointer">
                      {formData.is_active
                        ? 'Link is visible'
                        : 'Link is hidden'}
                    </span>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </DialogClose>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default QuickLinksManager;
