import * as React from "react"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  ComposedChart,
  Area,
  AreaChart,
  LabelList
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  Target,
  Users,
  GraduationCap,
  Award,
  AlertCircle,
  Info,
  CheckCircle
} from "lucide-react"
import { ChartExport } from "./chart-export"

interface AnalyticsData {
  totalGraduates: number
  averageGPA: number
  genderData: Array<{ name: string; value: number }>
  collegeData: Array<{ name: string; value: number }>
  yearData: Array<{ year: number; count: number }>
  graduates: any[]
}

interface AdvancedAnalyticsProps {
  data: AnalyticsData
  overallData?: AnalyticsData
  className?: string
}

export function AdvancedAnalytics({ data, overallData, className }: AdvancedAnalyticsProps) {
  const chartRefs = {
    gpaDistribution: React.useRef<HTMLDivElement>(null),
    graduationTrends: React.useRef<HTMLDivElement>(null),
    departmentBreakdown: React.useRef<HTMLDivElement>(null),
    comparisonMetrics: React.useRef<HTMLDivElement>(null)
  }

  // Calculate GPA distribution
  const calculateGPADistribution = () => {
    const ranges = [
      { range: "2.0-2.5", min: 2.0, max: 2.5, color: "#ef4444" },
      { range: "2.5-3.0", min: 2.5, max: 3.0, color: "#f97316" },
      { range: "3.0-3.5", min: 3.0, max: 3.5, color: "#eab308" },
      { range: "3.5-4.0", min: 3.5, max: 4.0, color: "#22c55e" }
    ]

    return ranges.map(range => {
      const count = data.graduates.filter(grad => 
        grad.gpa >= range.min && grad.gpa < range.max
      ).length
      
      return {
        ...range,
        count,
        percentage: data.totalGraduates > 0 ? (count / data.totalGraduates * 100).toFixed(1) : 0
      }
    })
  }

  // Calculate graduation trends
  const calculateGraduationTrends = () => {
    const yearlyData = data.yearData.map(year => {
      const yearGrads = data.graduates.filter(g => g.year_of_graduation === year.year)
      const avgGPA = yearGrads.length > 0 
        ? yearGrads.reduce((sum, g) => sum + (g.gpa || 0), 0) / yearGrads.length 
        : 0

      return {
        year: year.year,
        graduates: year.count,
        avgGPA: parseFloat(avgGPA.toFixed(2)),
        growth: 0 // Will be calculated below
      }
    }).sort((a, b) => a.year - b.year)

    // Calculate growth rates
    for (let i = 1; i < yearlyData.length; i++) {
      const current = yearlyData[i].graduates
      const previous = yearlyData[i - 1].graduates
      yearlyData[i].growth = previous > 0 ? ((current - previous) / previous * 100) : 0
    }

    return yearlyData
  }

  // Calculate department breakdown within colleges
  const calculateDepartmentBreakdown = () => {
    const deptCounts = data.graduates.reduce((acc, grad) => {
      const dept = grad.department_name || 'Unknown Department'
      const college = grad.college_name || 'Unknown College'
      
      if (!acc[college]) acc[college] = {}
      acc[college][dept] = (acc[college][dept] || 0) + 1
      
      return acc
    }, {} as Record<string, Record<string, number>>)

    return Object.entries(deptCounts).map(([college, departments]) => ({
      college,
      departments: Object.entries(departments)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5), // Top 5 departments per college
      total: Object.values(departments).reduce((sum, count) => sum + count, 0)
    }))
  }

  // Calculate comparison metrics
  const calculateComparisonMetrics = () => {
    if (!overallData) return null

    const metrics = [
      {
        label: "Total Graduates",
        filtered: data.totalGraduates,
        overall: overallData.totalGraduates,
        format: (val: number) => val.toLocaleString(),
        icon: <GraduationCap className="h-4 w-4" />
      },
      {
        label: "Average GPA",
        filtered: data.averageGPA,
        overall: overallData.averageGPA,
        format: (val: number) => val.toFixed(2),
        icon: <Award className="h-4 w-4" />
      },
      {
        label: "Male Graduates",
        filtered: data.genderData.find(g => g.name === 'Male')?.value || 0,
        overall: overallData.genderData.find(g => g.name === 'Male')?.value || 0,
        format: (val: number) => val.toLocaleString(),
        icon: <Users className="h-4 w-4" />
      },
      {
        label: "Female Graduates",
        filtered: data.genderData.find(g => g.name === 'Female')?.value || 0,
        overall: overallData.genderData.find(g => g.name === 'Female')?.value || 0,
        format: (val: number) => val.toLocaleString(),
        icon: <Users className="h-4 w-4" />
      }
    ]

    return metrics.map(metric => {
      const percentage = metric.overall > 0 ? (metric.filtered / metric.overall * 100) : 0
      const difference = metric.filtered - metric.overall
      
      return {
        ...metric,
        percentage: parseFloat(percentage.toFixed(1)),
        difference,
        trend: difference > 0 ? 'up' : difference < 0 ? 'down' : 'neutral'
      }
    })
  }

  const gpaDistribution = calculateGPADistribution()
  const graduationTrends = calculateGraduationTrends()
  const departmentBreakdown = calculateDepartmentBreakdown()
  const comparisonMetrics = calculateComparisonMetrics()

  // Generate insights
  const generateInsights = () => {
    const insights = []

    // GPA insights
    const highPerformers = gpaDistribution.find(d => d.range === "3.5-4.0")
    if (highPerformers && highPerformers.count > 0) {
      const percentage = parseFloat(highPerformers.percentage.toString())
      if (percentage > 50) {
        insights.push({
          type: 'success',
          title: 'High Academic Performance',
          message: `${percentage}% of graduates achieved GPA above 3.5`,
          icon: <CheckCircle className="h-4 w-4" />
        })
      }
    }

    // Growth insights
    const recentGrowth = graduationTrends.slice(-2)
    if (recentGrowth.length === 2) {
      const growth = recentGrowth[1].growth
      if (Math.abs(growth) > 10) {
        insights.push({
          type: growth > 0 ? 'info' : 'warning',
          title: 'Graduation Trend',
          message: `${Math.abs(growth).toFixed(1)}% ${growth > 0 ? 'increase' : 'decrease'} in recent graduates`,
          icon: growth > 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />
        })
      }
    }

    // Gender balance insights
    const maleCount = data.genderData.find(g => g.name === 'Male')?.value || 0
    const femaleCount = data.genderData.find(g => g.name === 'Female')?.value || 0
    const total = maleCount + femaleCount
    
    if (total > 0) {
      const malePercentage = (maleCount / total) * 100
      const femalePercentage = (femaleCount / total) * 100
      const difference = Math.abs(malePercentage - femalePercentage)
      
      if (difference < 10) {
        insights.push({
          type: 'success',
          title: 'Gender Balance',
          message: `Well-balanced gender distribution (${malePercentage.toFixed(1)}% male, ${femalePercentage.toFixed(1)}% female)`,
          icon: <Users className="h-4 w-4" />
        })
      } else {
        insights.push({
          type: 'info',
          title: 'Gender Distribution',
          message: `${malePercentage > femalePercentage ? 'Male' : 'Female'} graduates represent ${Math.max(malePercentage, femalePercentage).toFixed(1)}% of total`,
          icon: <Info className="h-4 w-4" />
        })
      }
    }

    return insights
  }

  const insights = generateInsights()

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Key Insights */}
      {insights.length > 0 && (
        <Card className="border-0 shadow-xl bg-white">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <Target className="h-5 w-5 mr-2 text-blue-600" />
              Key Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {insights.map((insight, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-l-4 ${
                    insight.type === 'success' ? 'border-green-500 bg-green-50' :
                    insight.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                    'border-blue-500 bg-blue-50'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-1 rounded ${
                      insight.type === 'success' ? 'text-green-600' :
                      insight.type === 'warning' ? 'text-yellow-600' :
                      'text-blue-600'
                    }`}>
                      {insight.icon}
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-gray-900">{insight.title}</h4>
                      <p className="text-xs text-gray-600 mt-1">{insight.message}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* GPA Distribution Analysis and Graduation Trends - Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* GPA Distribution Analysis */}
        <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                  <div className="h-2 w-2 bg-purple-600 rounded-full mr-3"></div>
                  GPA Distribution Analysis
                </CardTitle>
                <p className="text-gray-600 mt-1">Graduate performance across GPA ranges</p>
              </div>
              <div className="flex items-center space-x-2">
                <ChartExport
                  chartRef={chartRefs.gpaDistribution}
                  data={gpaDistribution}
                  filename="gpa-distribution"
                  title="GPA Distribution Analysis"
                />
                <div className="bg-purple-600 p-2 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div ref={chartRefs.gpaDistribution} className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={gpaDistribution}
                  margin={{ top: 40, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis
                    dataKey="range"
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    axisLine={{ stroke: '#64748b' }}
                    tickLine={{ stroke: '#64748b' }}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    axisLine={{ stroke: '#64748b' }}
                    tickLine={{ stroke: '#64748b' }}
                    label={{ value: 'Number of Graduates', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip
                    formatter={(value: any, name: string) => [
                      `${value} graduates (${gpaDistribution.find(d => d.count === value)?.percentage}%)`,
                      'Count'
                    ]}
                  />
                  <Bar dataKey="count" name="Graduates" radius={[4, 4, 0, 0]}>
                    <LabelList
                      dataKey="count"
                      position="top"
                      formatter={(value: any) => value.toLocaleString()}
                      style={{
                        fontSize: '11px',
                        fontWeight: 'bold',
                        fill: '#374151'
                      }}
                    />
                    <LabelList
                      dataKey="percentage"
                      position="top"
                      offset={15}
                      formatter={(value: any) => `${value}%`}
                      style={{
                        fontSize: '10px',
                        fontWeight: 'bold',
                        fill: '#6b7280'
                      }}
                    />
                    {gpaDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Graduation Trends Over Time */}
        <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                  <div className="h-2 w-2 bg-green-600 rounded-full mr-3"></div>
                  Graduation Trends Over Time
                </CardTitle>
                <p className="text-gray-600 mt-1">Annual graduation patterns and growth</p>
              </div>
              <div className="flex items-center space-x-2">
                <ChartExport
                  chartRef={chartRefs.graduationTrends}
                  data={graduationTrends}
                  filename="graduation-trends"
                  title="Graduation Trends Over Time"
                />
                <div className="bg-green-600 p-2 rounded-lg">
                  <Activity className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div ref={chartRefs.graduationTrends} className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={graduationTrends}
                  margin={{ top: 40, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis
                    dataKey="year"
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    axisLine={{ stroke: '#64748b' }}
                    tickLine={{ stroke: '#64748b' }}
                  />
                  <YAxis
                    yAxisId="left"
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    axisLine={{ stroke: '#64748b' }}
                    tickLine={{ stroke: '#64748b' }}
                    label={{ value: 'Number of Graduates', angle: -90, position: 'insideLeft' }}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    axisLine={{ stroke: '#64748b' }}
                    tickLine={{ stroke: '#64748b' }}
                    label={{ value: 'Average GPA', angle: 90, position: 'insideRight' }}
                  />
                  <Tooltip
                    formatter={(value: any, name: string) => {
                      if (name === 'graduates') return [`${value} graduates`, 'Graduates']
                      if (name === 'avgGPA') return [`${value} GPA`, 'Average GPA']
                      return [value, name]
                    }}
                  />
                  <Legend />
                  <Bar
                    yAxisId="left"
                    dataKey="graduates"
                    name="Graduates"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                  >
                    <LabelList
                      dataKey="graduates"
                      position="top"
                      formatter={(value: any) => value.toLocaleString()}
                      style={{
                        fontSize: '11px',
                        fontWeight: 'bold',
                        fill: '#374151'
                      }}
                    />
                  </Bar>
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="avgGPA"
                    name="Average GPA"
                    stroke="#10b981"
                    strokeWidth={3}
                    dot={{ r: 4, fill: '#10b981' }}
                    activeDot={{ r: 6, fill: '#10b981' }}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
