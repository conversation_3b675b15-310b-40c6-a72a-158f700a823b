import React, { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Pencil, Trash2, Search, Filter, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, FileText, CheckCircle, XCircle, Clock, Users, RotateCcw, User, Building, MessageSquare, GraduationCap, Calendar, CreditCard, BookOpen, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import axios from 'axios';

interface Department {
  id: number;
  name: string;
  description: string;
  college: number;
  college_name?: string;
}

interface College {
  id: number;
  name: string;
  description: string;
}

const ApplicationDepartmentManagement = () => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState<Department | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    college: '',
  });
  const [formErrors, setFormErrors] = useState({
    name: '',
    description: '',
    college: '',
  });

  // Fetch departments and colleges on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Optimized data fetching - fetch colleges and departments in parallel
  const fetchData = async () => {
    setLoading(true);

    try {
      // Get token once
      const token = localStorage.getItem('token');
      const headers = token ? {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      } : {
        'Content-Type': 'application/json',
      };

      // Fetch colleges and departments in parallel
      const [collegesData, departmentsData] = await Promise.all([
        fetchColleges(headers),
        fetchDepartments(headers)
      ]);

      // Enhance departments with college names
      const enhancedDepartments = departmentsData.map((dept: Department) => {
        const college = collegesData.find(c => c.id === dept.college);
        return {
          ...dept,
          college_name: college ? college.name : 'Unknown College'
        };
      });

      setColleges(collegesData);
      setDepartments(enhancedDepartments);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load departments and colleges');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to fetch colleges
  const fetchColleges = async (headers: any): Promise<College[]> => {
    try {
      // Try authenticated endpoint first
      const response = await axios.get('http://localhost:8000/api/colleges/', { headers });
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching colleges from authenticated endpoint:', error);
    }

    try {
      // Fall back to public endpoint
      const publicResponse = await axios.get('http://localhost:8000/api/colleges/public/');
      if (publicResponse.data && Array.isArray(publicResponse.data)) {
        return publicResponse.data;
      }
    } catch (error) {
      console.error('Error fetching colleges from public endpoint:', error);
    }

    return [];
  };

  // Helper function to fetch departments
  const fetchDepartments = async (headers: any): Promise<Department[]> => {
    try {
      // Try authenticated endpoint first
      const response = await axios.get('http://localhost:8000/api/departments/', { headers });
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      }
    } catch (error) {
      console.error('Error fetching departments from authenticated endpoint:', error);
    }

    try {
      // Fall back to public endpoint
      const publicResponse = await axios.get('http://localhost:8000/api/departments/public/');
      if (publicResponse.data && Array.isArray(publicResponse.data)) {
        return publicResponse.data;
      }
    } catch (error) {
      console.error('Error fetching departments from public endpoint:', error);
    }

    return [];
  };

  // Function to refresh data
  const refreshData = () => {
    fetchData();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user types
    setFormErrors({
      ...formErrors,
      [name]: '',
    });
  };

  const handleSelectChange = (value: string) => {
    setFormData({
      ...formData,
      college: value,
    });

    // Clear error when user selects
    setFormErrors({
      ...formErrors,
      college: '',
    });
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formData.name.trim()) {
      newErrors.name = 'Department name is required';
      valid = false;
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
      valid = false;
    }

    if (!formData.college) {
      newErrors.college = 'College is required';
      valid = false;
    }

    setFormErrors(newErrors);
    return valid;
  };

  const handleAddDepartment = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add a department');
        return;
      }

      // Prepare the data for submission
      const departmentData = {
        name: formData.name,
        description: formData.description,
        college: parseInt(formData.college)
      };

      console.log('Sending department data to server:', departmentData);

      // Show loading state
      const loadingToast = toast.loading('Adding department...');

      // Make the request to the server
      const response = await axios.post('http://localhost:8000/api/departments/', departmentData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss the loading toast
      toast.dismiss(loadingToast);

      console.log('Department created successfully:', response.data);

      // Get the college name for the new department
      const college = colleges.find(c => c.id === parseInt(formData.college));
      const collegeName = college ? college.name : 'Unknown College';

      // Update the state with the new department
      const newDepartment = {
        ...response.data,
        college_name: collegeName
      };

      setDepartments(prevDepartments => [...prevDepartments, newDepartment]);

      // Show a success message
      toast.success('Department added successfully');

      // Close the dialog and reset the form
      setIsAddDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error adding department:', error);

      // Extract error message
      let errorMessage = 'Failed to add department';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);

        if (typeof error.response.data === 'string') {
          errorMessage = `${errorMessage}: ${error.response.data}`;
        } else if (error.response.data?.detail) {
          errorMessage = `${errorMessage}: ${error.response.data.detail}`;
        } else {
          // Check for field-specific errors
          const fieldErrors = [];
          for (const field in error.response.data) {
            if (error.response.data[field]) {
              const errorMsg = Array.isArray(error.response.data[field])
                ? error.response.data[field][0]
                : error.response.data[field];
              fieldErrors.push(`${field}: ${errorMsg}`);
            }
          }
          if (fieldErrors.length > 0) {
            errorMessage = `${errorMessage}: ${fieldErrors.join(', ')}`;
          }
        }
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'Failed to add department: No response received from server';
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = `Failed to add department: ${error.message}`;
      }

      toast.error(errorMessage);
    }
  };

  const handleEditDepartment = async () => {
    if (!currentDepartment) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit a department');
        return;
      }

      // Prepare the data for submission
      const departmentData = {
        name: formData.name,
        description: formData.description,
        college: parseInt(formData.college)
      };

      console.log('Sending updated department data to server:', departmentData);

      // Show loading state
      const loadingToast = toast.loading('Updating department...');

      // Make the request to the server
      const response = await axios.put(`http://localhost:8000/api/departments/${currentDepartment.id}/`, departmentData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss the loading toast
      toast.dismiss(loadingToast);

      console.log('Department updated successfully:', response.data);

      // Get the college name for the updated department
      const college = colleges.find(c => c.id === parseInt(formData.college));
      const collegeName = college ? college.name : 'Unknown College';

      // Update the state with the updated department
      const updatedDepartment = {
        ...response.data,
        college_name: collegeName
      };

      setDepartments(prevDepartments =>
        prevDepartments.map(dept =>
          dept.id === currentDepartment.id ? updatedDepartment : dept
        )
      );

      // Show a success message
      toast.success('Department updated successfully');

      // Close the dialog and reset the form
      setIsEditDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error updating department:', error);

      // Extract error message
      let errorMessage = 'Failed to update department';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);

        if (typeof error.response.data === 'string') {
          errorMessage = `${errorMessage}: ${error.response.data}`;
        } else if (error.response.data?.detail) {
          errorMessage = `${errorMessage}: ${error.response.data.detail}`;
        } else {
          // Check for field-specific errors
          const fieldErrors = [];
          for (const field in error.response.data) {
            if (error.response.data[field]) {
              const errorMsg = Array.isArray(error.response.data[field])
                ? error.response.data[field][0]
                : error.response.data[field];
              fieldErrors.push(`${field}: ${errorMsg}`);
            }
          }
          if (fieldErrors.length > 0) {
            errorMessage = `${errorMessage}: ${fieldErrors.join(', ')}`;
          }
        }
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'Failed to update department: No response received from server';
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = `Failed to update department: ${error.message}`;
      }

      toast.error(errorMessage);
    }
  };

  const handleDeleteDepartment = async () => {
    if (!currentDepartment) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete a department');
        return;
      }

      console.log('Deleting department with ID:', currentDepartment.id);

      // Show loading state
      const loadingToast = toast.loading('Deleting department...');

      // Make the request to the server
      await axios.delete(`http://localhost:8000/api/departments/delete/${currentDepartment.id}/`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss the loading toast
      toast.dismiss(loadingToast);

      console.log('Department deleted successfully');

      // Update the state by removing the deleted department
      setDepartments(prevDepartments =>
        prevDepartments.filter(dept => dept.id !== currentDepartment.id)
      );

      // Show a success message
      toast.success('Department deleted successfully');

      // Close the dialog
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting department:', error);

      // Extract error message
      let errorMessage = 'Failed to delete department';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);

        if (typeof error.response.data === 'string') {
          errorMessage = `${errorMessage}: ${error.response.data}`;
        } else if (error.response.data?.detail) {
          errorMessage = `${errorMessage}: ${error.response.data.detail}`;
        } else {
          // Check for field-specific errors
          const fieldErrors = [];
          for (const field in error.response.data) {
            if (error.response.data[field]) {
              const errorMsg = Array.isArray(error.response.data[field])
                ? error.response.data[field][0]
                : error.response.data[field];
              fieldErrors.push(`${field}: ${errorMsg}`);
            }
          }
          if (fieldErrors.length > 0) {
            errorMessage = `${errorMessage}: ${fieldErrors.join(', ')}`;
          }
        }
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'Failed to delete department: No response received from server';
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = `Failed to delete department: ${error.message}`;
      }

      toast.error(errorMessage);
    }
  };

  const openEditDialog = (department: Department) => {
    setCurrentDepartment(department);
    setFormData({
      name: department.name,
      description: department.description,
      college: department.college.toString(),
    });
    setFormErrors({
      name: '',
      description: '',
      college: '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (department: Department) => {
    setCurrentDepartment(department);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      college: '',
    });
    setFormErrors({
      name: '',
      description: '',
      college: '',
    });
    setCurrentDepartment(null);
  };

  // Get college name by ID
  const getCollegeName = (collegeId: number): string => {
    const college = colleges.find(c => c.id === collegeId);
    return college ? college.name : 'Unknown College';
  };

  // Filter departments based on search term
  const filteredDepartments = departments.filter(department =>
    department.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    department.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (department.college_name && department.college_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDepartments.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredDepartments.length / itemsPerPage);



  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Memoize the table rows to prevent unnecessary re-renders
  const memoizedTableRows = useMemo(() => {
    return currentItems.map((department) => (
      <TableRow key={department.id}>
        <TableCell>{department.college_name || getCollegeName(department.college)}</TableCell>
        <TableCell className="font-medium">{department.name}</TableCell>
        <TableCell>{department.description}</TableCell>
        <TableCell className="text-right">
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => openEditDialog(department)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => openDeleteDialog(department)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    ));
  }, [currentItems, colleges, getCollegeName, openEditDialog, openDeleteDialog]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Application Department Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete departments for the application portal
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Department
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-t-lg border-b">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                        <Plus className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <DialogTitle className="text-lg text-[#1a73c0]">Add New Department</DialogTitle>
                        <DialogDescription className="mt-1">
                          Enter the details for the new department
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="college" className="text-sm font-medium text-gray-700">College</Label>
                      <Select
                        value={formData.college}
                        onValueChange={handleSelectChange}
                      >
                        <SelectTrigger id="college" className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.college ? 'border-red-500 focus-visible:ring-red-400' : '')}>
                          <SelectValue placeholder="Select a college" />
                        </SelectTrigger>
                        <SelectContent>
                          {colleges.map((college) => (
                            <SelectItem key={college.id} value={college.id.toString()}>
                              {college.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.college && (
                        <p className="text-sm text-red-500">{formErrors.college}</p>
                      )}
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="name" className="text-sm font-medium text-gray-700">Department Name</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter department name"
                        className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.name ? 'border-red-500 focus-visible:ring-red-400' : '')}
                      />
                      {formErrors.name && (
                        <p className="text-sm text-red-500">{formErrors.name}</p>
                      )}
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="description" className="text-sm font-medium text-gray-700">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder="Enter department description"
                        rows={3}
                        className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.description ? 'border-red-500 focus-visible:ring-red-400' : '')}
                      />
                      {formErrors.description && (
                        <p className="text-sm text-red-500">{formErrors.description}</p>
                      )}
                    </div>
                  </div>
                  <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
                    <DialogClose asChild>
                      <Button variant="outline" className="border-gray-300">Cancel</Button>
                    </DialogClose>
                    <Button onClick={handleAddDepartment} className="bg-[#1a73c0] hover:bg-blue-700">Save</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search Departments
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search by college, name or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[25%] text-[#1a73c0] font-medium">College</TableHead>
                    <TableHead className="w-[25%] text-[#1a73c0] font-medium">Name</TableHead>
                    <TableHead className="w-[35%] text-[#1a73c0] font-medium">Description</TableHead>
                    <TableHead className="w-[15%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-blue-100 p-3 rounded-full">
                            <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                          </div>
                          <div className="text-[#1a73c0] font-medium">Loading departments...</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredDepartments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div className="text-gray-700 font-medium">No departments found</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {searchTerm ?
                              'Try adjusting your search criteria to find what you\'re looking for.' :
                              'There are no departments available. Click the "Add Department" button to create one.'}
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentItems.map((department) => (
                      <TableRow key={department.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{department.college_name || getCollegeName(department.college)}</TableCell>
                        <TableCell>{department.name}</TableCell>
                        <TableCell>{department.description}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(department)}
                              title="Edit"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDeleteDialog(department)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredDepartments.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredDepartments.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredDepartments.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Pencil className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-[#1a73c0]">Edit Department</DialogTitle>
                <DialogDescription className="mt-1">
                  Update the department information
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-college" className="text-sm font-medium text-gray-700">College</Label>
              <Select
                value={formData.college}
                onValueChange={handleSelectChange}
              >
                <SelectTrigger id="edit-college" className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.college ? 'border-red-500 focus-visible:ring-red-400' : '')}>
                  <SelectValue placeholder="Select a college" />
                </SelectTrigger>
                <SelectContent>
                  {colleges.map((college) => (
                    <SelectItem key={college.id} value={college.id.toString()}>
                      {college.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formErrors.college && (
                <p className="text-sm text-red-500">{formErrors.college}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-name" className="text-sm font-medium text-gray-700">Department Name</Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter department name"
                className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.name ? 'border-red-500 focus-visible:ring-red-400' : '')}
              />
              {formErrors.name && (
                <p className="text-sm text-red-500">{formErrors.name}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-description" className="text-sm font-medium text-gray-700">Description</Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter department description"
                rows={3}
                className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.description ? 'border-red-500 focus-visible:ring-red-400' : '')}
              />
              {formErrors.description && (
                <p className="text-sm text-red-500">{formErrors.description}</p>
              )}
            </div>
          </div>
          <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
            <DialogClose asChild>
              <Button variant="outline" className="border-gray-300">Cancel</Button>
            </DialogClose>
            <Button onClick={handleEditDepartment} className="bg-[#1a73c0] hover:bg-blue-700">Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-orange-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-red-500 rounded-lg shadow-sm">
                <Trash2 className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-red-600">Confirm Deletion</DialogTitle>
                <DialogDescription className="mt-1">
                  This action cannot be undone
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-4 px-1">
            <p className="text-gray-700">
              Are you sure you want to delete the department <span className="font-semibold text-gray-900">"{currentDepartment?.name}"</span>?
            </p>
            <p className="mt-2 text-sm text-gray-500">
              This will permanently remove the department from the system. This action cannot be reversed.
            </p>
          </div>
          <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
            <DialogClose asChild>
              <Button variant="outline" className="border-gray-300">Cancel</Button>
            </DialogClose>
            <Button variant="destructive" onClick={handleDeleteDepartment}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ApplicationDepartmentManagement;
