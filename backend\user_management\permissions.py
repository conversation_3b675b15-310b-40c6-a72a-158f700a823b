from rest_framework import permissions
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
import logging

logger = logging.getLogger(__name__)


class RoleBasedPermission(permissions.BasePermission):
    """
    Custom permission class that checks for specific roles and permissions
    """
    required_roles = []  # Override in subclasses
    required_permissions = []  # Override in subclasses
    allow_superuser = True  # Allow superusers by default

    def has_permission(self, request, view):
        # Check if user is authenticated
        if not request.user or not request.user.is_authenticated:
            return False

        # Allow superusers if enabled
        if self.allow_superuser and request.user.is_superuser:
            return True

        # Check for required roles
        if self.required_roles:
            user_roles = list(request.user.groups.values_list('name', flat=True))
            if not any(role in user_roles for role in self.required_roles):
                logger.warning(
                    f"Access denied for user {request.user.username}. "
                    f"Required roles: {self.required_roles}, User roles: {user_roles}"
                )
                return False

        # Check for required permissions
        if self.required_permissions:
            missing_permissions = [perm for perm in self.required_permissions if not request.user.has_perm(perm)]
            if missing_permissions:
                logger.warning(
                    f"Access denied for user {request.user.username}. "
                    f"Missing permissions: {missing_permissions}"
                )
                return False

        return True


class AdminOnlyPermission(RoleBasedPermission):
    """Permission for admin-only access"""
    required_roles = ['Administrator', 'Super Admin']


class StaffOnlyPermission(RoleBasedPermission):
    """Permission for staff-only access"""
    required_roles = ['Administrator', 'Super Admin', 'Department Head', 'Main Registrar', 'Registrar Officer']


class RegistrarPermission(RoleBasedPermission):
    """Permission for registrar-level access"""
    required_roles = ['Administrator', 'Super Admin', 'Main Registrar', 'Registrar Officer']


class DepartmentHeadPermission(RoleBasedPermission):
    """Permission for department head access"""
    required_roles = ['Administrator', 'Super Admin', 'Main Registrar', 'Registrar Officer', 'Department Head']


class UserManagementPermission(RoleBasedPermission):
    """Permission for user management operations"""
    required_permissions = [
        'auth.view_user',
        'auth.add_user',
        'auth.change_user',
        'auth.delete_user'
    ]


class RoleManagementPermission(RoleBasedPermission):
    """Permission for role management operations"""
    required_permissions = [
        'auth.view_group',
        'auth.add_group',
        'auth.change_group',
        'auth.delete_group'
    ]


class RegistrationManagementPermission(RoleBasedPermission):
    """Permission for registration management"""
    required_permissions = [
        'registration.view_applicantprogramselection',
        'registration.add_applicantprogramselection',
        'registration.change_applicantprogramselection',
        'registration.delete_applicantprogramselection'
    ]


class OfficialCertificatePermission(RoleBasedPermission):
    """Permission for official certificate management"""
    required_permissions = [
        'official.view_official',
        'official.add_official',
        'official.change_official',
        'official.delete_official'
    ]


class DynamicRolePermission(permissions.BasePermission):
    """
    Dynamic permission class that can be configured per view
    """
    def __init__(self, required_roles=None, required_permissions=None, allow_superuser=True):
        self.required_roles = required_roles or []
        self.required_permissions = required_permissions or []
        self.allow_superuser = allow_superuser

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if self.allow_superuser and request.user.is_superuser:
            return True

        if self.required_roles:
            user_roles = request.user.groups.values_list('name', flat=True)
            if not any(role in user_roles for role in self.required_roles):
                return False

        if self.required_permissions:
            if not all(request.user.has_perm(perm) for perm in self.required_permissions):
                return False

        return True


class MethodBasedPermission(permissions.BasePermission):
    """
    Permission class that applies different rules based on HTTP method
    """
    def __init__(self, permissions_map=None):
        """
        permissions_map example:
        {
            'GET': ['Applicant', 'Department Head', 'Registrar Officer', 'Main Registrar', 'Administrator'],
            'POST': ['Department Head', 'Registrar Officer', 'Main Registrar', 'Administrator'],
            'PUT': ['Main Registrar', 'Administrator'],
            'DELETE': ['Administrator']
        }
        """
        self.permissions_map = permissions_map or {}

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        method = request.method
        required_roles = self.permissions_map.get(method, [])

        if not required_roles:
            return True  # No specific requirements for this method

        user_roles = request.user.groups.values_list('name', flat=True)
        return any(role in user_roles for role in required_roles)


def has_role(user, role_name):
    """Helper function to check if user has a specific role"""
    if not user or not user.is_authenticated:
        return False
    return user.groups.filter(name=role_name).exists()


def has_any_role(user, role_names):
    """Helper function to check if user has any of the specified roles"""
    if not user or not user.is_authenticated:
        return False
    return user.groups.filter(name__in=role_names).exists()


def has_permission(user, permission_codename):
    """Helper function to check if user has a specific permission"""
    if not user or not user.is_authenticated:
        return False
    return user.has_perm(permission_codename)


def get_user_roles(user):
    """Helper function to get all roles for a user"""
    if not user or not user.is_authenticated:
        return []
    return list(user.groups.values_list('name', flat=True))


def get_user_permissions(user):
    """Helper function to get all permissions for a user"""
    if not user or not user.is_authenticated:
        return []
    return list(user.get_all_permissions())


class ResourceOwnerPermission(permissions.BasePermission):
    """
    Permission that allows access only to resource owners or admins
    """
    def has_object_permission(self, request, view, obj):
        # Allow superusers
        if request.user.is_superuser:
            return True

        # Allow admins
        if has_any_role(request.user, ['Administrator', 'Super Admin']):
            return True

        # Check if user owns the resource
        if hasattr(obj, 'user') and obj.user == request.user:
            return True

        if hasattr(obj, 'author') and obj.author == request.user:
            return True

        if hasattr(obj, 'created_by') and obj.created_by == request.user:
            return True

        return False


class DepartmentBasedPermission(permissions.BasePermission):
    """
    Permission that restricts access based on department
    """
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        # Check if user has profile and department
        if hasattr(request.user, 'profile') and request.user.profile.department:
            return True

        # Allow users with admin roles regardless of department
        return has_any_role(request.user, ['Administrator', 'Super Admin'])

    def has_object_permission(self, request, view, obj):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        # Allow admins
        if has_any_role(request.user, ['Administrator', 'Super Admin']):
            return True

        # Check department match
        if hasattr(request.user, 'profile') and hasattr(obj, 'department'):
            return request.user.profile.department == obj.department

        return False


class DynamicPermissionCheck(permissions.BasePermission):
    """
    Dynamic permission check based on view attributes
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        # Check for required_permissions attribute on view
        if hasattr(view, 'required_permissions'):
            missing_permissions = []
            for permission in view.required_permissions:
                if not request.user.has_perm(permission):
                    missing_permissions.append(permission)

            if missing_permissions:
                logger.warning(
                    f"Dynamic permission check failed for user {request.user.username}. "
                    f"Missing permissions: {missing_permissions}"
                )
                return False

        # Check for required_roles attribute on view
        if hasattr(view, 'required_roles'):
            user_roles = set(request.user.groups.values_list('name', flat=True))
            required_roles = set(view.required_roles)

            if not user_roles.intersection(required_roles):
                logger.warning(
                    f"Dynamic role check failed for user {request.user.username}. "
                    f"Required roles: {required_roles}, User roles: {user_roles}"
                )
                return False

        return True


class APISecurityPermission(permissions.BasePermission):
    """
    Enhanced security permission for API endpoints
    """

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            logger.warning(f"Unauthenticated access attempt to {request.path}")
            return False

        # Check if user account is active
        if not request.user.is_active:
            logger.warning(f"Inactive user {request.user.username} attempted access to {request.path}")
            return False

        # Log successful authentication
        logger.info(f"Authenticated access: {request.user.username} to {request.path}")
        return True
