import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useSimpleRBAC as useRBAC } from '@/contexts/SimpleRBACContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  User,
  Shield,
  Users,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

/**
 * Demo component to show access control status and test different scenarios
 */
export const AccessControlDemo: React.FC = () => {
  const { user, isAuthenticated, hasPermission, hasAnyRole } = useAuth();
  const {
    isSuperuser,
    isStaff,
    canAccessAdmin,
    isRegularUser
  } = useRBAC();

  if (!isAuthenticated || !user) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <XCircle className="h-5 w-5 text-red-500" />
            Not Authenticated
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>Please log in to see access control information.</p>
        </CardContent>
      </Card>
    );
  }

  const getAccessControlStatus = () => {
    if (!user.is_active) {
      return {
        status: 'inactive',
        title: 'Account Inactive',
        description: 'Your account is inactive. Contact administrator.',
        color: 'destructive',
        icon: XCircle,
        action: 'Redirect to login'
      };
    }

    if (user.is_superuser) {
      return {
        status: 'superuser',
        title: 'Superuser Access',
        description: 'Full access to all areas of the system.',
        color: 'default',
        icon: Shield,
        action: 'Full access granted'
      };
    }

    if (!user.is_staff) {
      return {
        status: 'regular',
        title: 'Regular User',
        description: 'Access to applicant dashboard only.',
        color: 'secondary',
        icon: User,
        action: 'Redirect to applicant dashboard'
      };
    }

    if (user.is_staff) {
      const userHasGroups = user.role_names && user.role_names.length > 0;
      const userHasPermissions = user.permissions && user.permissions.length > 0;

      if (!userHasGroups && !userHasPermissions) {
        return {
          status: 'staff-no-groups',
          title: 'Staff Without Groups',
          description: 'Staff member without assigned groups or permissions.',
          color: 'destructive',
          icon: AlertTriangle,
          action: 'Access denied with message'
        };
      }
    }

    return {
      status: 'staff-with-access',
      title: 'Staff With Access',
      description: 'Staff member with appropriate groups/permissions.',
      color: 'default',
      icon: CheckCircle,
      action: 'Access granted based on permissions'
    };
  };

  const accessStatus = getAccessControlStatus();
  const StatusIcon = accessStatus.icon;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Main Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <StatusIcon className={`h-5 w-5 ${
              accessStatus.color === 'destructive' ? 'text-red-500' :
              accessStatus.color === 'secondary' ? 'text-gray-500' :
              'text-green-500'
            }`} />
            Access Control Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">{accessStatus.title}</h3>
              <p className="text-sm text-muted-foreground">{accessStatus.description}</p>
            </div>
            <Badge variant={accessStatus.color as any}>
              {accessStatus.action}
            </Badge>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className={`text-2xl font-bold ${user.is_active ? 'text-green-600' : 'text-red-600'}`}>
                {user.is_active ? '✓' : '✗'}
              </div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${user.is_staff ? 'text-green-600' : 'text-gray-400'}`}>
                {user.is_staff ? '✓' : '✗'}
              </div>
              <div className="text-xs text-muted-foreground">Staff</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${user.is_superuser ? 'text-green-600' : 'text-gray-400'}`}>
                {user.is_superuser ? '✓' : '✗'}
              </div>
              <div className="text-xs text-muted-foreground">Superuser</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                (user.role_names && user.role_names.length > 0) || (user.permissions && user.permissions.length > 0)
                  ? 'text-green-600' : 'text-gray-400'
              }`}>
                {(user.role_names && user.role_names.length > 0) || (user.permissions && user.permissions.length > 0) ? '✓' : '✗'}
              </div>
              <div className="text-xs text-muted-foreground">Has Groups</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Details Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Username</label>
              <p className="text-sm text-muted-foreground">{user.username}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Email</label>
              <p className="text-sm text-muted-foreground">{user.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Full Name</label>
              <p className="text-sm text-muted-foreground">
                {user.first_name} {user.last_name}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium">Last Login</label>
              <p className="text-sm text-muted-foreground">
                {user.last_login ? new Date(user.last_login).toLocaleString() : 'Never'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permissions & Roles Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permissions & Roles
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Roles/Groups</label>
            <div className="flex flex-wrap gap-1 mt-1">
              {user.role_names && user.role_names.length > 0 ? (
                user.role_names.map((role, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {role}
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">No roles assigned</span>
              )}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">Permissions</label>
            <div className="text-sm text-muted-foreground mt-1">
              {user.permissions && user.permissions.length > 0 ? (
                `${user.permissions.length} permissions assigned`
              ) : (
                'No specific permissions assigned'
              )}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">Access Level</label>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={
                user.is_superuser ? 'default' :
                user.is_staff ? 'secondary' :
                'outline'
              }>
                {user.is_superuser ? 'Superuser' :
                 user.is_staff ? 'Staff' :
                 'Regular User'}
              </Badge>
              {canAccessAdmin && (
                <Badge variant="outline" className="text-xs">
                  Admin Access
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Access Control Rules Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Access Control Rules
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
              <div>
                <strong>Superusers:</strong> Full access to all areas
              </div>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <div>
                <strong>Staff with Groups/Permissions:</strong> Access based on assigned roles
              </div>
            </div>
            <div className="flex items-start gap-2">
              <XCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <strong>Staff without Groups:</strong> Access denied with message
              </div>
            </div>
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
              <div>
                <strong>Regular Users:</strong> Redirected to applicant dashboard
              </div>
            </div>
            <div className="flex items-start gap-2">
              <XCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <strong>Inactive Users:</strong> Redirected to login with message
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccessControlDemo;
