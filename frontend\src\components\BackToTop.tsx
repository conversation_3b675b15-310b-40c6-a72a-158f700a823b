import React, { useState, useEffect } from 'react';
import { ChevronUp } from 'lucide-react';

const BackToTop: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Show button when page is scrolled down
  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  // Set the top scroll position
  const scrollToTop = () => {
    setIsAnimating(true);
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    // Reset animation state after scroll completes
    setTimeout(() => {
      setIsAnimating(false);
    }, 500);
  };

  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  return (
    <div className="fixed bottom-8 right-8 z-50">
      {isVisible && (
        <button
          onClick={scrollToTop}
          aria-label="Back to top"
          className={`
            back-to-top__button
            text-white p-3 rounded-full
            shadow-lg transition-all duration-300
            hover:shadow-xl hover:-translate-y-1
            focus:outline-none focus:ring-2 focus:ring-opacity-50
            ${isAnimating ? 'animate-bounce' : ''}
            animate-fade-in
            border border-white/30
          `}
        >
          <ChevronUp className={`h-6 w-6 ${isAnimating ? 'animate-pulse' : ''}`} />
        </button>
      )}
    </div>
  );
};

// Add these animations to your global CSS or tailwind.config.js
// @keyframes fadeIn {
//   from { opacity: 0; }
//   to { opacity: 1; }
// }
// .animate-fade-in {
//   animation: fadeIn 0.3s ease-in-out;
// }

export default BackToTop;
