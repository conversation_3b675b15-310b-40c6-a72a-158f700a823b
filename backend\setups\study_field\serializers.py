from rest_framework import serializers
from django.db import IntegrityError
from .models import StudyField

class StudyFieldSerializer(serializers.ModelSerializer):
    college_name = serializers.ReadOnlyField(source='college.name')
    department_name = serializers.ReadOnlyField(source='department.name')

    class Meta:
        model = StudyField
        fields = ['id', 'field_of_study', 'code', 'college', 'college_name', 'department', 'department_name', 'description', 'status', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at', 'college_name', 'department_name']
        ref_name = 'StudyField'

    def validate_field_of_study(self, value):
        # Check if the field_of_study is not empty
        if not value.strip():
            raise serializers.ValidationError("Field of study name cannot be empty.")

        # Check if the field_of_study is not too long
        if len(value) > 255:
            raise serializers.ValidationError("Field of study name must be less than 255 characters.")

        return value

    def validate_code(self, value):
        # Code is optional, so if it's empty or None, just return it
        if value is None or value == '':
            return None

        # If code is provided, validate it
        if len(value) > 20:
            raise serializers.ValidationError("Code must be less than 20 characters.")

        # Check if code contains only alphanumeric characters
        if not value.isalnum():
            raise serializers.ValidationError("Code must contain only letters and numbers.")

        return value.upper()  # Convert code to uppercase

    def validate(self, data):
        # Check if the department belongs to the college
        if 'department' in data and 'college' in data:
            if data['department'].college.id != data['college'].id:
                raise serializers.ValidationError({
                    "department": "The selected department does not belong to the selected college."
                })

        # Check for duplicate field_of_study within the same department
        instance = getattr(self, 'instance', None)
        if instance:
            # This is an update operation
            # Check if the field_of_study or department has changed
            if ('field_of_study' in data and data['field_of_study'] != instance.field_of_study) or \
               ('department' in data and data['department'] != instance.department):
                # Check for duplicates
                department = data.get('department', instance.department)
                field_of_study = data.get('field_of_study', instance.field_of_study)
                if StudyField.objects.filter(department=department, field_of_study=field_of_study).exclude(id=instance.id).exists():
                    raise serializers.ValidationError({
                        "field_of_study": "A field of study with this name already exists in the selected department."
                    })

            # Check for duplicate code within the same department if code has changed
            if 'code' in data and data['code'] is not None and (instance.code is None or data['code'] != instance.code):
                department = data.get('department', instance.department)
                code = data['code']
                if StudyField.objects.filter(department=department, code=code).exclude(id=instance.id).exists():
                    raise serializers.ValidationError({
                        "code": "A field of study with this code already exists in the selected department."
                    })
        else:
            # This is a create operation
            if 'department' in data and 'field_of_study' in data:
                if StudyField.objects.filter(department=data['department'], field_of_study=data['field_of_study']).exists():
                    raise serializers.ValidationError({
                        "field_of_study": "A field of study with this name already exists in the selected department."
                    })

            # Check for duplicate code within the same department
            if 'department' in data and 'code' in data and data['code'] is not None:
                if StudyField.objects.filter(department=data['department'], code=data['code']).exists():
                    raise serializers.ValidationError({
                        "code": "A field of study with this code already exists in the selected department."
                    })

        return data

    def create(self, validated_data):
        try:
            return super().create(validated_data)
        except IntegrityError:
            raise serializers.ValidationError({
                "detail": "Could not create field of study. Please check that all fields are valid."
            })

    def update(self, instance, validated_data):
        try:
            return super().update(instance, validated_data)
        except IntegrityError:
            raise serializers.ValidationError({
                "detail": "Could not update field of study. Please check that all fields are valid."
            })