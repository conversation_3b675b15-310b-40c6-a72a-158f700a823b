import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Filter,
  ChevronDown,
  ChevronUp,
  RotateCcw,
  HelpCircle,
  Settings,
  Zap
} from "lucide-react"
import { cn } from "@/lib/utils"
import { MultiSelect, Option } from "./multi-select"
import { FilterPresets, FilterPreset } from "./filter-presets"

interface CollapsibleFilterPanelProps {
  // Filter options
  availableYears: Option[]
  availableColleges: Option[]
  availableDepartments?: Option[]

  // Current selections
  selectedYears: string[]
  selectedColleges: string[]
  selectedDepartments?: string[]

  // Callbacks
  onYearsChange: (years: string[]) => void
  onCollegesChange: (colleges: string[]) => void
  onDepartmentsChange?: (departments: string[]) => void
  onClearFilters: () => void
  onApplyPreset: (preset: FilterPreset) => void

  // State
  isLoading?: boolean
  filterCount?: number

  // UI
  className?: string
  defaultExpanded?: boolean
}

export function CollapsibleFilterPanel({
  availableYears,
  availableColleges,
  availableDepartments = [],
  selectedYears,
  selectedColleges,
  selectedDepartments = [],
  onYearsChange,
  onCollegesChange,
  onDepartmentsChange,
  onClearFilters,
  onApplyPreset,
  isLoading = false,
  filterCount = 0,
  className,
  defaultExpanded = true
}: CollapsibleFilterPanelProps) {
  const [isOpen, setIsOpen] = React.useState(defaultExpanded)
  const [activeSection, setActiveSection] = React.useState<'filters' | 'presets'>('filters')

  const hasActiveFilters = selectedYears.length > 0 || selectedColleges.length > 0 || selectedDepartments.length > 0

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + F to toggle filter panel
      if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
        event.preventDefault()
        setIsOpen(!isOpen)
      }

      // Ctrl/Cmd + R to clear filters
      if ((event.ctrlKey || event.metaKey) && event.key === 'r' && hasActiveFilters) {
        event.preventDefault()
        onClearFilters()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, hasActiveFilters, onClearFilters])

  const getFilterSummary = () => {
    const parts = []
    if (selectedYears.length > 0) {
      parts.push(`${selectedYears.length} year${selectedYears.length > 1 ? 's' : ''}`)
    }
    if (selectedColleges.length > 0) {
      parts.push(`${selectedColleges.length} college${selectedColleges.length > 1 ? 's' : ''}`)
    }
    if (selectedDepartments.length > 0) {
      parts.push(`${selectedDepartments.length} department${selectedDepartments.length > 1 ? 's' : ''}`)
    }
    return parts.join(', ')
  }

  return (
    <div className={cn("w-full", className)}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <Card className="border-0 shadow-xl bg-white overflow-hidden">
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors duration-200 pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg">
                    <Filter className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                      Advanced Filters
                      {hasActiveFilters && (
                        <Badge variant="secondary" className="ml-2">
                          {getFilterSummary()}
                        </Badge>
                      )}
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">
                      {isOpen ? 'Click to collapse filter options' : 'Click to expand filter options'}
                      {filterCount > 0 && ` • ${filterCount.toLocaleString()} results`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {hasActiveFilters && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              onClearFilters()
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Clear all filters (Ctrl+R)</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <div className="text-gray-400">
                    {isOpen ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
                  </div>
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <CardContent className="pt-0">
              {/* Section Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
                <Button
                  variant={activeSection === 'filters' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveSection('filters')}
                  className="flex-1 flex items-center space-x-2"
                >
                  <Settings className="h-4 w-4" />
                  <span>Filters</span>
                </Button>
                <Button
                  variant={activeSection === 'presets' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveSection('presets')}
                  className="flex-1 flex items-center space-x-2"
                >
                  <Zap className="h-4 w-4" />
                  <span>Presets</span>
                </Button>
              </div>

              {/* Filter Section */}
              {activeSection === 'filters' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Year Filter */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <label id="years-filter-label" className="text-sm font-medium text-gray-700">Academic Years</label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Select one or more graduation years to filter the data</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <MultiSelect
                        options={availableYears}
                        selected={selectedYears}
                        onChange={onYearsChange}
                        placeholder="Select years..."
                        searchPlaceholder="Search years..."
                        className="w-full"
                        disabled={isLoading}
                        showSelectAll={true}
                        maxDisplayItems={3}
                      />
                    </div>

                    {/* College Filter */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <label id="colleges-filter-label" className="text-sm font-medium text-gray-700">Colleges</label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Select one or more colleges to filter graduates by institution</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <MultiSelect
                        options={availableColleges}
                        selected={selectedColleges}
                        onChange={onCollegesChange}
                        placeholder="Select colleges..."
                        searchPlaceholder="Search colleges..."
                        className="w-full"
                        disabled={isLoading}
                        showSelectAll={true}
                        maxDisplayItems={2}
                      />
                    </div>

                    {/* Department Filter */}
                    {availableDepartments.length > 0 && onDepartmentsChange && (
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <label id="departments-filter-label" className="text-sm font-medium text-gray-700">Departments</label>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <HelpCircle className="h-4 w-4 text-gray-400" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Select departments within chosen colleges (cascading filter)</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <MultiSelect
                          options={availableDepartments}
                          selected={selectedDepartments}
                          onChange={onDepartmentsChange}
                          placeholder="Select departments..."
                          searchPlaceholder="Search departments..."
                          className="w-full"
                          disabled={isLoading || selectedColleges.length === 0}
                          showSelectAll={true}
                          maxDisplayItems={2}
                        />
                        {selectedColleges.length === 0 && (
                          <p className="text-xs text-gray-500" aria-live="polite">Select colleges first to enable department filtering</p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Filter Summary */}
                  {hasActiveFilters && (
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-blue-900">Active Filters</h4>
                          <div className="mt-1 text-sm text-blue-700 space-y-1">
                            {selectedYears.length > 0 && (
                              <div>Years: {selectedYears.join(', ')}</div>
                            )}
                            {selectedColleges.length > 0 && (
                              <div>
                                Colleges: {selectedColleges.map(id =>
                                  availableColleges.find(c => c.value === id)?.label || id
                                ).join(', ')}
                              </div>
                            )}
                            {selectedDepartments.length > 0 && (
                              <div>
                                Departments: {selectedDepartments.map(id =>
                                  availableDepartments.find(d => d.value === id)?.label || id
                                ).join(', ')}
                              </div>
                            )}
                          </div>
                        </div>
                        {filterCount > 0 && (
                          <div className="text-right">
                            <div className="text-lg font-bold text-blue-900">
                              {filterCount.toLocaleString()}
                            </div>
                            <div className="text-xs text-blue-700">results found</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Presets Section */}
              {activeSection === 'presets' && (
                <FilterPresets
                  availableYears={availableYears}
                  availableColleges={availableColleges}
                  onApplyPreset={onApplyPreset}
                />
              )}



              {/* Keyboard Shortcuts Help */}
              <div className="mt-6 p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600 flex items-center">
                  <HelpCircle className="h-3 w-3 mr-1" />
                  Keyboard shortcuts: Ctrl+F (toggle panel), Ctrl+R (clear filters)
                </p>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>
    </div>
  )
}
