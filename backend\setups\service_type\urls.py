from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import ServiceTypeViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'service-types', ServiceTypeViewSet, basename='service-type')

# URL patterns
urlpatterns = [
    path('', include(router.urls)),
]

# Named URL patterns for easy reference
app_name = 'service_type'
