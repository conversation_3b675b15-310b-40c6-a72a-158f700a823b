#!/usr/bin/env python
"""
Simple SMTP test to verify the improvements
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from settings_manager.smtp_models import SMTPSettings
from settings_manager.smtp_views import test_smtp_connection
from django.test import RequestFactory
from django.contrib.auth.models import User
import json

def test_smtp_improvements():
    """Test the improved SMTP functionality"""
    print("=== Testing SMTP Improvements ===")
    
    # Test 1: Model functionality
    print("\n1. Testing SMTP Model...")
    smtp_settings = SMTPSettings.load()
    print(f"✓ Model loaded: {smtp_settings}")
    print(f"  Host: '{smtp_settings.host}'")
    print(f"  Port: {smtp_settings.port}")
    print(f"  From Email: '{smtp_settings.from_email}'")
    print(f"  Has Password: {bool(smtp_settings.password)}")
    
    # Test 2: Configure test settings
    print("\n2. Configuring test SMTP settings...")
    smtp_settings.host = 'smtp.gmail.com'
    smtp_settings.port = 587
    smtp_settings.username = '<EMAIL>'
    smtp_settings.password = 'testpassword'
    smtp_settings.from_email = '<EMAIL>'
    smtp_settings.use_tls = True
    smtp_settings.use_ssl = False
    smtp_settings.timeout = 60
    smtp_settings.save()
    print("✓ Test settings configured")
    
    # Test 3: API validation
    print("\n3. Testing API validation...")
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={'is_staff': True, 'is_superuser': True}
    )
    
    factory = RequestFactory()
    
    # Test with missing recipient
    print("  Testing missing recipient...")
    request = factory.post(
        '/api/settings/smtp/test/',
        data=json.dumps({}),
        content_type='application/json'
    )
    request.user = user
    
    try:
        response = test_smtp_connection(request)
        print(f"  ✓ Response status: {response.status_code}")
        if hasattr(response, 'data') and response.data:
            print(f"  ✓ Response: {response.data}")
    except Exception as e:
        print(f"  ✗ Error: {e}")
    
    # Test with valid recipient but no SMTP config
    print("\n  Testing with unconfigured SMTP...")
    smtp_settings.host = ''
    smtp_settings.save()
    
    request = factory.post(
        '/api/settings/smtp/test/',
        data=json.dumps({'recipient': '<EMAIL>'}),
        content_type='application/json'
    )
    request.user = user
    
    try:
        response = test_smtp_connection(request)
        print(f"  ✓ Response status: {response.status_code}")
        if hasattr(response, 'data') and response.data:
            print(f"  ✓ Response: {response.data}")
    except Exception as e:
        print(f"  ✗ Error: {e}")
    
    # Test with configured SMTP (will fail connection but should show proper error)
    print("\n  Testing with configured SMTP...")
    smtp_settings.host = 'smtp.gmail.com'
    smtp_settings.from_email = '<EMAIL>'
    smtp_settings.save()
    
    request = factory.post(
        '/api/settings/smtp/test/',
        data=json.dumps({'recipient': '<EMAIL>'}),
        content_type='application/json'
    )
    request.user = user
    
    try:
        response = test_smtp_connection(request)
        print(f"  ✓ Response status: {response.status_code}")
        if hasattr(response, 'data') and response.data:
            error_msg = response.data.get('error', 'No error message')
            if 'Authentication failed' in error_msg or 'Connection failed' in error_msg:
                print(f"  ✓ Expected error: {error_msg[:100]}...")
            else:
                print(f"  ? Unexpected response: {response.data}")
    except Exception as e:
        print(f"  ✗ Error: {e}")
    
    print("\n=== Test Summary ===")
    print("✓ SMTP model working")
    print("✓ API validation working")
    print("✓ Error handling improved")
    print("✓ Ready for real SMTP testing")
    
    print("\n💡 Next steps:")
    print("1. Configure real SMTP settings in the frontend")
    print("2. Use your actual email credentials")
    print("3. Test with a real email address")
    print("4. Check for detailed error messages if it fails")

if __name__ == '__main__':
    test_smtp_improvements()
