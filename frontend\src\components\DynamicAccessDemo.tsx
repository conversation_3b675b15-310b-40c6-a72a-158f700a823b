import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  PermissionGate, 
  AuthRequired, 
  StaffOnly, 
  SuperuserOnly, 
  HasPermission, 
  HasRole,
  FeatureFlag 
} from './PermissionGate';
import { 
  DynamicButton, 
  ViewButton, 
  EditButton, 
  DeleteButton, 
  CreateButton, 
  ManageButton 
} from './DynamicButton';
import { DynamicTable, GraduateTable } from './DynamicTable';
import { useFeatureAccess, useGraduateAccess, useCollegeAccess } from '@/hooks/useFeatureAccess';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Shield, 
  Users, 
  Eye, 
  Edit, 
  Trash2, 
  Plus, 
  Settings,
  Lock,
  Unlock,
  CheckCircle,
  XCircle
} from 'lucide-react';

/**
 * Dynamic Access Control Demo
 * Demonstrates all the dynamic access control features
 */
export const DynamicAccessDemo: React.FC = () => {
  const { user } = useAuth();
  const graduateAccess = useGraduateAccess();
  const collegeAccess = useCollegeAccess();
  
  // Sample data for demonstrations
  const sampleGraduates = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'verified' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'pending' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'verified' }
  ];

  const [actionLog, setActionLog] = useState<string[]>([]);

  const logAction = (action: string) => {
    setActionLog(prev => [...prev.slice(-4), `${new Date().toLocaleTimeString()}: ${action}`]);
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Dynamic Access Control Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="font-medium mb-2">Current User</h4>
              <div className="space-y-1">
                <p className="text-sm">{user?.username || 'Not logged in'}</p>
                <div className="flex gap-1">
                  <Badge variant={user?.is_active ? "default" : "destructive"}>
                    {user?.is_active ? "Active" : "Inactive"}
                  </Badge>
                  <Badge variant={user?.is_staff ? "default" : "secondary"}>
                    {user?.is_staff ? "Staff" : "Regular"}
                  </Badge>
                  {user?.is_superuser && (
                    <Badge variant="default">Superuser</Badge>
                  )}
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Groups</h4>
              <div className="space-y-1">
                {user?.role_names && user.role_names.length > 0 ? (
                  user.role_names.map((role, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {role}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">No groups</p>
                )}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Feature Access</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  {graduateAccess.canView ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="text-sm">Graduate Management</span>
                </div>
                <div className="flex items-center gap-2">
                  {collegeAccess.canView ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="text-sm">College Management</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="components" className="space-y-4">
        <TabsList>
          <TabsTrigger value="components">Permission Components</TabsTrigger>
          <TabsTrigger value="buttons">Dynamic Buttons</TabsTrigger>
          <TabsTrigger value="tables">Dynamic Tables</TabsTrigger>
          <TabsTrigger value="features">Feature Access</TabsTrigger>
        </TabsList>

        {/* Permission Components Demo */}
        <TabsContent value="components" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Permission Gate Components</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-medium">Authentication Gates</h4>
                  
                  <AuthRequired fallback={<Badge variant="destructive">Login Required</Badge>}>
                    <Badge variant="default">✓ Authenticated Content</Badge>
                  </AuthRequired>

                  <StaffOnly fallback={<Badge variant="destructive">Staff Only</Badge>}>
                    <Badge variant="default">✓ Staff Content</Badge>
                  </StaffOnly>

                  <SuperuserOnly fallback={<Badge variant="destructive">Superuser Only</Badge>}>
                    <Badge variant="default">✓ Superuser Content</Badge>
                  </SuperuserOnly>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Permission & Role Gates</h4>
                  
                  <HasPermission 
                    permissions={['unknown.view_graduatestudent']}
                    fallback={<Badge variant="destructive">Missing Permission</Badge>}
                  >
                    <Badge variant="default">✓ Has Graduate View Permission</Badge>
                  </HasPermission>

                  <HasRole 
                    roles={['Verification Clerk']}
                    fallback={<Badge variant="destructive">Not Verification Clerk</Badge>}
                  >
                    <Badge variant="default">✓ Verification Clerk Role</Badge>
                  </HasRole>

                  <FeatureFlag 
                    feature="advanced-features"
                    groups={['Administrator']}
                    fallback={<Badge variant="destructive">Feature Disabled</Badge>}
                  >
                    <Badge variant="default">✓ Advanced Feature Enabled</Badge>
                  </FeatureFlag>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Dynamic Buttons Demo */}
        <TabsContent value="buttons" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Dynamic Action Buttons</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-medium">Graduate Management Actions</h4>
                  <div className="flex flex-wrap gap-2">
                    <ViewButton 
                      feature="graduate"
                      onClick={() => logAction('View Graduate')}
                    />
                    <EditButton 
                      feature="graduate"
                      permissions={['unknown.change_graduatestudent']}
                      onClick={() => logAction('Edit Graduate')}
                    />
                    <DeleteButton 
                      feature="graduate"
                      permissions={['unknown.delete_graduatestudent']}
                      onClick={() => logAction('Delete Graduate')}
                    />
                    <CreateButton 
                      feature="graduate"
                      permissions={['unknown.add_graduatestudent']}
                      onClick={() => logAction('Create Graduate')}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">College Management Actions</h4>
                  <div className="flex flex-wrap gap-2">
                    <ViewButton 
                      feature="college"
                      onClick={() => logAction('View College')}
                    />
                    <EditButton 
                      feature="college"
                      permissions={['unknown.change_verificationcollege']}
                      onClick={() => logAction('Edit College')}
                    />
                    <ManageButton 
                      feature="college"
                      groups={['Main Registrar', 'Administrator']}
                      onClick={() => logAction('Manage College')}
                    />
                  </div>
                </div>
              </div>

              {/* Action Log */}
              <div className="mt-4 p-3 bg-gray-50 rounded">
                <h4 className="font-medium mb-2">Action Log</h4>
                {actionLog.length > 0 ? (
                  <div className="space-y-1">
                    {actionLog.map((log, index) => (
                      <p key={index} className="text-sm text-muted-foreground">{log}</p>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No actions performed yet</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Dynamic Tables Demo */}
        <TabsContent value="tables" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Dynamic Data Tables</CardTitle>
            </CardHeader>
            <CardContent>
              <GraduateTable graduates={sampleGraduates} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feature Access Demo */}
        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Feature Access Control</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-3">Graduate Management Access</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can View</span>
                      <Badge variant={graduateAccess.canView ? "default" : "destructive"}>
                        {graduateAccess.canView ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can Edit</span>
                      <Badge variant={graduateAccess.canEdit ? "default" : "destructive"}>
                        {graduateAccess.canEdit ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can Delete</span>
                      <Badge variant={graduateAccess.canDelete ? "default" : "destructive"}>
                        {graduateAccess.canDelete ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can Create</span>
                      <Badge variant={graduateAccess.canCreate ? "default" : "destructive"}>
                        {graduateAccess.canCreate ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Access Level</span>
                      <Badge variant="outline">{graduateAccess.accessLevel}</Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">College Management Access</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can View</span>
                      <Badge variant={collegeAccess.canView ? "default" : "destructive"}>
                        {collegeAccess.canView ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can Edit</span>
                      <Badge variant={collegeAccess.canEdit ? "default" : "destructive"}>
                        {collegeAccess.canEdit ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can Delete</span>
                      <Badge variant={collegeAccess.canDelete ? "default" : "destructive"}>
                        {collegeAccess.canDelete ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Can Create</span>
                      <Badge variant={collegeAccess.canCreate ? "default" : "destructive"}>
                        {collegeAccess.canCreate ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Access Level</span>
                      <Badge variant="outline">{collegeAccess.accessLevel}</Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Denied Reasons */}
              {(graduateAccess.deniedReason || collegeAccess.deniedReason) && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                  <h4 className="font-medium text-red-800 mb-2">Access Restrictions</h4>
                  {graduateAccess.deniedReason && (
                    <p className="text-sm text-red-700">Graduate: {graduateAccess.deniedReason}</p>
                  )}
                  {collegeAccess.deniedReason && (
                    <p className="text-sm text-red-700">College: {collegeAccess.deniedReason}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DynamicAccessDemo;
