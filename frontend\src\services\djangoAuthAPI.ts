import api from './api';

// Types for Django's default auth models
export interface DjangoUser {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  date_joined: string;
  last_login: string | null;
  groups: DjangoGroup[];
  user_permissions: DjangoPermission[];
}

export interface DjangoGroup {
  id: number;
  name: string;
  permissions: DjangoPermission[];
}

export interface DjangoPermission {
  id: number;
  name: string;
  content_type: number;
  codename: string;
}

export interface DjangoContentType {
  id: number;
  app_label: string;
  model: string;
}

// Django User API
const djangoUserAPI = {
  // Get all users
  getUsers: (params?: { search?: string; is_active?: boolean; is_staff?: boolean; page?: number }) => 
    api.get('/auth/users/', { params }),

  // Get single user
  getUser: (id: number) => 
    api.get(`/auth/users/${id}/`),

  // Create user
  createUser: (data: {
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    password: string;
    is_active?: boolean;
    is_staff?: boolean;
    is_superuser?: boolean;
  }) => api.post('/auth/users/', data),

  // Update user
  updateUser: (id: number, data: Partial<DjangoUser>) => 
    api.patch(`/auth/users/${id}/`, data),

  // Delete user
  deleteUser: (id: number) => 
    api.delete(`/auth/users/${id}/`),

  // Change password
  changePassword: (id: number, data: { old_password?: string; new_password: string }) =>
    api.post(`/auth/users/${id}/change_password/`, data),

  // Set user groups
  setUserGroups: (id: number, group_ids: number[]) =>
    api.post(`/auth/users/${id}/groups/`, { groups: group_ids }),

  // Set user permissions
  setUserPermissions: (id: number, permission_ids: number[]) =>
    api.post(`/auth/users/${id}/permissions/`, { permissions: permission_ids }),
};

// Django Group API
const djangoGroupAPI = {
  // Get all groups
  getGroups: (params?: { search?: string }) => 
    api.get('/auth/groups/', { params }),

  // Get single group
  getGroup: (id: number) => 
    api.get(`/auth/groups/${id}/`),

  // Create group
  createGroup: (data: { name: string; permissions?: number[] }) => 
    api.post('/auth/groups/', data),

  // Update group
  updateGroup: (id: number, data: { name?: string; permissions?: number[] }) => 
    api.patch(`/auth/groups/${id}/`, data),

  // Delete group
  deleteGroup: (id: number) => 
    api.delete(`/auth/groups/${id}/`),

  // Get group members
  getGroupMembers: (id: number) => 
    api.get(`/auth/groups/${id}/users/`),

  // Add users to group
  addUsersToGroup: (id: number, user_ids: number[]) =>
    api.post(`/auth/groups/${id}/users/`, { users: user_ids }),

  // Remove users from group
  removeUsersFromGroup: (id: number, user_ids: number[]) =>
    api.delete(`/auth/groups/${id}/users/`, { data: { users: user_ids } }),
};

// Django Permission API
const djangoPermissionAPI = {
  // Get all permissions
  getPermissions: (params?: { content_type?: number; search?: string }) => 
    api.get('/auth/permissions/', { params }),

  // Get permissions by content type
  getPermissionsByContentType: () => 
    api.get('/auth/permissions/by_content_type/'),

  // Get content types
  getContentTypes: () => 
    api.get('/auth/content_types/'),
};

// Django Auth Stats API (simple stats from Django models)
const djangoStatsAPI = {
  // Get basic auth statistics
  getAuthStats: () => api.get('/auth/stats/'),

  // Get user activity (from Django's session framework)
  getUserActivity: (params?: { days?: number }) => 
    api.get('/auth/user_activity/', { params }),
};

// Export all APIs
export {
  djangoUserAPI,
  djangoGroupAPI,
  djangoPermissionAPI,
  djangoStatsAPI,
};

// Default export
export default {
  users: djangoUserAPI,
  groups: djangoGroupAPI,
  permissions: djangoPermissionAPI,
  stats: djangoStatsAPI,
};
