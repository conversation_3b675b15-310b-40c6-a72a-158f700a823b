"""
Enhanced Authentication Views for robust JWT authentication and authorization
"""
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.utils import timezone
from django.conf import settings
import logging

from .serializers import UserDetailSerializer
from .jwt_serializers import CustomTokenObtainPairSerializer

logger = logging.getLogger(__name__)


class EnhancedTokenObtainPairView(TokenObtainPairView):
    """
    Enhanced JWT token obtain view with additional security features
    """
    serializer_class = CustomTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            
            if response.status_code == 200:
                # Log successful login
                username = request.data.get('username')
                user = User.objects.filter(username=username).first()
                if user:
                    user.last_login = timezone.now()
                    user.save(update_fields=['last_login'])
                    logger.info(f"Successful login for user: {username}")
                
                # Add additional security headers
                response['X-Content-Type-Options'] = 'nosniff'
                response['X-Frame-Options'] = 'DENY'
                response['X-XSS-Protection'] = '1; mode=block'
                
            return response
            
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return Response(
                {'error': 'Authentication failed'},
                status=status.HTTP_401_UNAUTHORIZED
            )


class EnhancedTokenRefreshView(TokenRefreshView):
    """
    Enhanced JWT token refresh view with additional validation
    """
    
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            
            if response.status_code == 200:
                # Add security headers
                response['X-Content-Type-Options'] = 'nosniff'
                response['X-Frame-Options'] = 'DENY'
                
                # Log token refresh
                logger.info("Token refreshed successfully")
                
            return response
            
        except TokenError as e:
            logger.warning(f"Token refresh failed: {str(e)}")
            return Response(
                {'error': 'Token refresh failed', 'detail': str(e)},
                status=status.HTTP_401_UNAUTHORIZED
            )


class TokenValidateView(APIView):
    """
    Validate JWT token and return user information
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            # If we reach here, the token is valid (due to IsAuthenticated)
            serializer = UserDetailSerializer(request.user)
            return Response({
                'valid': True,
                'user': serializer.data,
                'token_info': {
                    'user_id': request.user.id,
                    'username': request.user.username,
                    'is_staff': request.user.is_staff,
                    'is_superuser': request.user.is_superuser,
                }
            })
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            return Response(
                {'valid': False, 'error': str(e)},
                status=status.HTTP_401_UNAUTHORIZED
            )


class LogoutView(APIView):
    """
    Logout view that blacklists the refresh token
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
                
            logger.info(f"User {request.user.username} logged out successfully")
            return Response(
                {'message': 'Successfully logged out'},
                status=status.HTTP_200_OK
            )
        except TokenError as e:
            logger.warning(f"Logout error: {str(e)}")
            return Response(
                {'error': 'Invalid token'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return Response(
                {'error': 'Logout failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserPermissionsView(APIView):
    """
    Get comprehensive user permissions and roles
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        try:
            user = request.user
            
            # Get all permissions
            all_permissions = user.get_all_permissions()
            
            # Get user permissions
            user_permissions = user.user_permissions.all()
            
            # Get group permissions
            group_permissions = []
            groups = user.groups.all()
            
            for group in groups:
                group_permissions.extend(group.permissions.all())
            
            # Create detailed response
            permissions_data = {
                'user_id': user.id,
                'username': user.username,
                'is_superuser': user.is_superuser,
                'is_staff': user.is_staff,
                'is_active': user.is_active,
                'groups': [{'id': g.id, 'name': g.name} for g in groups],
                'permissions': {
                    'all_permissions': list(all_permissions),
                    'user_permissions': [
                        {
                            'id': p.id,
                            'name': p.name,
                            'codename': p.codename,
                            'content_type': p.content_type.app_label + '.' + p.content_type.model
                        } for p in user_permissions
                    ],
                    'group_permissions': [
                        {
                            'id': p.id,
                            'name': p.name,
                            'codename': p.codename,
                            'content_type': p.content_type.app_label + '.' + p.content_type.model
                        } for p in set(group_permissions)
                    ],
                    'permission_count': len(all_permissions)
                }
            }
            
            return Response(permissions_data)
            
        except Exception as e:
            logger.error(f"Error fetching user permissions: {str(e)}")
            return Response(
                {'error': 'Failed to fetch permissions'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def check_permission(request):
    """
    Check if user has specific permission
    Usage: GET /api/auth/check-permission/?permission=app.action_model
    """
    permission = request.GET.get('permission')
    if not permission:
        return Response(
            {'error': 'Permission parameter is required'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    has_permission = request.user.has_perm(permission)
    
    return Response({
        'user_id': request.user.id,
        'username': request.user.username,
        'permission': permission,
        'has_permission': has_permission,
        'is_superuser': request.user.is_superuser
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def auth_status(request):
    """
    Get current authentication status and user info
    """
    try:
        serializer = UserDetailSerializer(request.user)
        return Response({
            'authenticated': True,
            'user': serializer.data,
            'session_info': {
                'last_activity': timezone.now().isoformat(),
                'session_valid': True,
                'token_valid': True
            }
        })
    except Exception as e:
        logger.error(f"Auth status error: {str(e)}")
        return Response(
            {'authenticated': False, 'error': str(e)},
            status=status.HTTP_401_UNAUTHORIZED
        )
