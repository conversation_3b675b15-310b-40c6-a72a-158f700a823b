import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Pencil, Trash2, Search, RefreshCw, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Building } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface Department {
  id: number;
  name: string;
  code: string;
  college: number;
  college_name?: string;
}

interface College {
  id: number;
  name: string;
  code: string;
}

const DepartmentManagement = () => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState<Department | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    college: '',
  });
  const [formErrors, setFormErrors] = useState({
    name: '',
    code: '',
    college: '',
  });

  // Fetch departments and colleges on component mount
  useEffect(() => {
    fetchDepartments();
    fetchColleges();
  }, []);

  // Debug log to check departments data structure
  useEffect(() => {
    console.log('Departments state:', departments);
    console.log('Is Array?', Array.isArray(departments));
  }, [departments]);

  const fetchDepartments = async () => {
    setLoading(true);
    try {
      console.log('Attempting to fetch department data from all possible endpoints');

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try verification-departments endpoints
        { url: 'http://localhost:8000/api/verification-departments/', auth: true },
        { url: 'http://localhost:8000/api/verification-departments/public/', auth: false },
        // Try regular departments endpoints
        { url: 'http://localhost:8000/api/departments/', auth: true },
        { url: 'http://localhost:8000/api/departments/public/', auth: false },
      ];

      let departmentData = null;

      // Try each endpoint until one works
      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint.url}`);

          const headers: Record<string, string> = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            const token = localStorage.getItem('token');
            if (token) {
              headers['Authorization'] = `Bearer ${token}`;
            }
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`Successfully fetched data from ${endpoint.url}:`, data);
            departmentData = data;
            break; // Exit the loop if we got data successfully
          } else {
            console.log(`Endpoint ${endpoint.url} failed with status: ${response.status}`);
          }
        } catch (endpointError) {
          console.error(`Error fetching from ${endpoint.url}:`, endpointError);
        }
      }

      if (!departmentData) {
        throw new Error('All endpoints failed to return data');
      }

      // Store the raw response data and set departments
      setDepartments(departmentData);
      console.log('Departments set with real data, length:', departmentData.length);
    } catch (error: any) {
      console.error('Error fetching departments:', error);
      toast.error(`Failed to fetch departments: ${error.message}`);
      setDepartments([]); // Ensure departments is always an array
    } finally {
      setLoading(false);
    }
  };

  const fetchColleges = async () => {
    try {
      console.log('Attempting to fetch college data for dropdown');

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try verification-colleges endpoints
        { url: 'http://localhost:8000/api/verification-colleges/', auth: true },
        { url: 'http://localhost:8000/api/verification-colleges/public/', auth: false },
        // Try regular colleges endpoints
        { url: 'http://localhost:8000/api/colleges/', auth: true },
        { url: 'http://localhost:8000/api/colleges/public/', auth: false },
      ];

      let collegeData = null;

      // Try each endpoint until one works
      for (const endpoint of endpoints) {
        try {
          console.log(`Trying college endpoint: ${endpoint.url}`);

          const headers: Record<string, string> = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            const token = localStorage.getItem('token');
            if (token) {
              headers['Authorization'] = `Bearer ${token}`;
            }
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`Successfully fetched college data from ${endpoint.url}:`, data);
            collegeData = data;
            break; // Exit the loop if we got data successfully
          } else {
            console.log(`College endpoint ${endpoint.url} failed with status: ${response.status}`);
          }
        } catch (endpointError) {
          console.error(`Error fetching from ${endpoint.url}:`, endpointError);
        }
      }

      if (!collegeData) {
        throw new Error('All college endpoints failed to return data');
      }

      // Set colleges data
      setColleges(collegeData);
      console.log('Colleges set for dropdown, length:', collegeData.length);
    } catch (error: any) {
      console.error('Error fetching colleges for dropdown:', error);
      toast.error(`Failed to fetch colleges: ${error.message}`);
      setColleges([]); // Ensure colleges is always an array
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    // Auto-format code to uppercase
    let newValue = value;
    if (name === 'code') {
      newValue = value.toUpperCase();
    }

    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : newValue,
    });

    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleSelectChange = (value: string) => {
    setFormData({
      ...formData,
      college: value,
    });

    // Clear error when user selects a college
    if (formErrors.college) {
      setFormErrors({
        ...formErrors,
        college: ''
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    let valid = true;
    const newErrors = { name: '', code: '', college: '' };

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      valid = false;
    } else if (formData.name.length > 200) {
      newErrors.name = 'Name must be less than 200 characters';
      valid = false;
    }

    // Validate code
    if (!formData.code.trim()) {
      newErrors.code = 'Code is required';
      valid = false;
    } else if (formData.code.length > 20) {
      newErrors.code = 'Code must be less than 20 characters';
      valid = false;
    } else if (!/^[A-Za-z0-9]+$/.test(formData.code.trim())) {
      newErrors.code = 'Code must contain only letters and numbers';
      valid = false;
    } else if (formData.code.length < 2) {
      newErrors.code = 'Code must be at least 2 characters';
      valid = false;
    } else {
      // Check for duplicate code within the same college
      if (!currentDepartment && Array.isArray(departments)) {
        const existingDepartment = departments.find(
          (dept) =>
            dept.code.toUpperCase() === formData.code.toUpperCase() &&
            dept.college.toString() === formData.college
        );
        if (existingDepartment) {
          newErrors.code = 'This code is already in use for this college. Codes must be unique within a college.';
          valid = false;
        }
      }
      // When editing, check if the code is changed and if it's already in use
      else if (currentDepartment && Array.isArray(departments)) {
        if (currentDepartment.code.toUpperCase() !== formData.code.toUpperCase() ||
            currentDepartment.college.toString() !== formData.college) {
          const existingDepartment = departments.find(
            (dept) =>
              dept.id !== currentDepartment.id &&
              dept.code.toUpperCase() === formData.code.toUpperCase() &&
              dept.college.toString() === formData.college
          );
          if (existingDepartment) {
            newErrors.code = 'This code is already in use for this college. Codes must be unique within a college.';
            valid = false;
          }
        }
      }
    }

    // Validate college
    if (!formData.college) {
      newErrors.college = 'College is required';
      valid = false;
    }

    setFormErrors(newErrors);
    return valid;
  };

  // Helper function to determine the correct API endpoint
  const getApiEndpoint = async () => {
    const token = localStorage.getItem('token');
    if (!token) return null;

    // Try all possible endpoints to find which one works
    const endpoints = [
      'http://localhost:8000/api/verification-departments/',
      'http://localhost:8000/api/departments/'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          console.log(`Found working endpoint: ${endpoint}`);
          return endpoint;
        }
      } catch (error) {
        console.error(`Error testing endpoint ${endpoint}:`, error);
      }
    }

    return null;
  };

  const handleAddDepartment = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add a department');
        return;
      }

      // Determine the correct endpoint
      const endpoint = await getApiEndpoint();

      if (!endpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                newErrors[field as keyof typeof newErrors] = Array.isArray(errors) ? errors[0] : errors.toString();
                hasFieldErrors = true;
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Department added successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchDepartments(); // Refresh the list after adding
    } catch (error) {
      console.error('Error adding department:', error);
      toast.error(`Failed to add department: ${error.message}`);
    }
  };

  const handleEditDepartment = async () => {
    if (!currentDepartment) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit a department');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentDepartment.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                newErrors[field as keyof typeof newErrors] = Array.isArray(errors) ? errors[0] : errors.toString();
                hasFieldErrors = true;
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Department updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchDepartments(); // Refresh the list after updating
    } catch (error) {
      console.error('Error updating department:', error);
      toast.error(`Failed to update department: ${error.message}`);
    }
  };

  const handleDeleteDepartment = async () => {
    if (!currentDepartment) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete a department');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentDepartment.id}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `HTTP error! Status: ${response.status}`);
      }

      toast.success('Department deleted successfully');
      setIsDeleteDialogOpen(false);
      fetchDepartments(); // Refresh the list after deleting
    } catch (error) {
      console.error('Error deleting department:', error);
      toast.error(`Failed to delete department: ${error.message}`);
    }
  };

  const openEditDialog = (department: Department) => {
    setCurrentDepartment(department);
    setFormData({
      name: department.name,
      code: department.code,
      college: department.college.toString(),
    });
    setFormErrors({
      name: '',
      code: '',
      college: '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (department: Department) => {
    setCurrentDepartment(department);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      college: '',
    });
    setFormErrors({
      name: '',
      code: '',
      college: '',
    });
    setCurrentDepartment(null);
  };

  // Safely filter departments, ensuring departments is an array
  const filteredDepartments = Array.isArray(departments)
    ? departments.filter(
        (department) =>
          department && (
            (department.name && department.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (department.code && department.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (department.college_name && department.college_name.toLowerCase().includes(searchTerm.toLowerCase()))
          )
      )
    : [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDepartments.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredDepartments.length / itemsPerPage);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Get college name by ID
  const getCollegeName = (collegeId: number) => {
    const college = colleges.find(c => c.id === collegeId);
    return college ? college.name : 'Unknown College';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Department Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete departments for graduate verification
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Department
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-[95vw] sm:max-w-lg lg:max-w-xl mx-4 sm:mx-auto max-h-[90vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
                        <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">Add New Department</DialogTitle>
                        <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                          Enter the details for the new department
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>

                  <div className="p-4 sm:p-5 lg:p-6 space-y-4 lg:space-y-5">
                    {/* College Selection */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-[#1a73c0]" />
                        <Label htmlFor="college" className="text-sm font-semibold text-gray-800">College *</Label>
                      </div>
                      <Select
                        value={formData.college}
                        onValueChange={handleSelectChange}
                      >
                        <SelectTrigger className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.college
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}>
                          <SelectValue placeholder="Choose a college..." />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {colleges.map((college) => (
                            <SelectItem
                              key={college.id}
                              value={college.id.toString()}
                              className="py-3 px-4 hover:bg-blue-50 focus:bg-blue-50"
                            >
                              <div className="flex items-center space-x-2">
                                <Building className="h-4 w-4 text-[#1a73c0]" />
                                <span>{college.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formErrors.college && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.college}</p>
                        </div>
                      )}
                    </div>

                    {/* Department Name */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        <Label htmlFor="name" className="text-sm font-semibold text-gray-800">Department Name *</Label>
                      </div>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="e.g. Computer Science, Mathematics, Biology"
                        className={cn(
                          "h-10 text-sm transition-all duration-200",
                          formErrors.name
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.name && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.name}</p>
                        </div>
                      )}
                    </div>

                    {/* Department Code */}
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                        </svg>
                        <Label htmlFor="code" className="text-sm font-semibold text-gray-800">Department Code *</Label>
                      </div>
                      <Input
                        id="code"
                        name="code"
                        value={formData.code}
                        onChange={handleInputChange}
                        placeholder="e.g. CS, MATH, BIO"
                        className={cn(
                          "h-10 text-sm transition-all duration-200 font-mono",
                          formErrors.code
                            ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                            : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                        )}
                      />
                      {formErrors.code && (
                        <div className="flex items-center space-x-2 text-red-600">
                          <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">{formErrors.code}</p>
                        </div>
                      )}
                      <p className="text-xs text-gray-500 flex items-center space-x-1">
                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span>Use a short, unique code (2-10 characters)</span>
                      </p>
                    </div>
                  </div>

                  <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t">
                    <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
                      <DialogClose asChild>
                        <Button
                          variant="outline"
                          className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                        >
                          Cancel
                        </Button>
                      </DialogClose>
                      <Button
                        onClick={handleAddDepartment}
                        className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create Department
                      </Button>
                    </div>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search Departments
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search by college, name or code..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[30%] text-[#1a73c0] font-medium">College</TableHead>
                    <TableHead className="w-[30%] text-[#1a73c0] font-medium">Name</TableHead>
                    <TableHead className="w-[25%] text-[#1a73c0] font-medium">Code</TableHead>
                    <TableHead className="w-[15%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-12">
                      <div className="flex flex-col justify-center items-center space-y-3">
                        <div className="bg-blue-100 p-3 rounded-full">
                          <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                        </div>
                        <div className="text-[#1a73c0] font-medium">Loading departments...</div>
                        <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredDepartments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-12">
                      <div className="flex flex-col justify-center items-center space-y-3">
                        <div className="bg-gray-100 p-3 rounded-full">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="text-gray-700 font-medium">No departments found</div>
                        <div className="text-sm text-gray-500 max-w-sm text-center">
                          {searchTerm ?
                            'Try adjusting your search criteria to find what you\'re looking for.' :
                            'There are no departments available. Click the "Add Department" button to create one.'}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentItems.map((department) => (
                    <TableRow key={department.id} className="hover:bg-blue-50 transition-colors">
                      <TableCell>{department.college_name || getCollegeName(department.college)}</TableCell>
                      <TableCell className="font-medium text-[#1a73c0]">{department.name}</TableCell>
                      <TableCell>{department.code}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(department)}
                            title="Edit"
                            className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDeleteDialog(department)}
                            className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredDepartments.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredDepartments.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredDepartments.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-lg lg:max-w-xl mx-4 sm:mx-auto max-h-[90vh] lg:max-h-none overflow-y-auto lg:overflow-visible">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 sm:p-3 bg-[#1a73c0] rounded-lg shadow-sm flex-shrink-0">
                <Pencil className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg sm:text-xl text-[#1a73c0] font-semibold">Edit Department</DialogTitle>
                <DialogDescription className="mt-1 text-sm sm:text-base text-gray-600">
                  Update the department information
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="p-4 sm:p-5 lg:p-6 space-y-4 lg:space-y-5">
            {/* College Selection */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-[#1a73c0]" />
                <Label htmlFor="edit-college" className="text-sm font-semibold text-gray-800">College *</Label>
              </div>
              <Select
                value={formData.college}
                onValueChange={handleSelectChange}
              >
                <SelectTrigger className={cn(
                  "h-10 text-sm transition-all duration-200",
                  formErrors.college
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}>
                  <SelectValue placeholder="Choose a college..." />
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  {colleges.map((college) => (
                    <SelectItem
                      key={college.id}
                      value={college.id.toString()}
                      className="py-3 px-4 hover:bg-blue-50 focus:bg-blue-50"
                    >
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-[#1a73c0]" />
                        <span>{college.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formErrors.college && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.college}</p>
                </div>
              )}
            </div>

            {/* Department Name */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <Label htmlFor="edit-name" className="text-sm font-semibold text-gray-800">Department Name *</Label>
              </div>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="e.g. Computer Science, Mathematics, Biology"
                className={cn(
                  "h-10 text-sm transition-all duration-200",
                  formErrors.name
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}
              />
              {formErrors.name && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.name}</p>
                </div>
              )}
            </div>

            {/* Department Code */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-[#1a73c0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                </svg>
                <Label htmlFor="edit-code" className="text-sm font-semibold text-gray-800">Department Code *</Label>
              </div>
              <Input
                id="edit-code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                placeholder="e.g. CS, MATH, BIO"
                className={cn(
                  "h-10 text-sm transition-all duration-200 font-mono",
                  formErrors.code
                    ? 'border-red-500 focus-visible:ring-red-400 bg-red-50'
                    : 'border-blue-200 focus-visible:ring-blue-400 hover:border-blue-300 bg-white'
                )}
              />
              {formErrors.code && (
                <div className="flex items-center space-x-2 text-red-600">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-sm font-medium">{formErrors.code}</p>
                </div>
              )}
              <p className="text-xs text-gray-500 flex items-center space-x-1">
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span>Use a short, unique code (2-10 characters)</span>
              </p>
            </div>
          </div>

          <DialogFooter className="bg-gray-50 p-4 sm:p-5 lg:p-6 rounded-b-lg border-t">
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-end">
              <DialogClose asChild>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 h-10 text-sm font-medium"
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                onClick={handleEditDepartment}
                className="w-full sm:w-auto bg-[#1a73c0] hover:bg-blue-700 h-10 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Pencil className="h-4 w-4 mr-2" />
                Update Department
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-orange-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-red-500 rounded-lg shadow-sm">
                <Trash2 className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-red-600">Confirm Deletion</DialogTitle>
                <DialogDescription className="mt-1">
                  This action cannot be undone
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-4 px-1">
            <p className="text-gray-700">
              Are you sure you want to delete the department <span className="font-semibold text-gray-900">"{currentDepartment?.name}"</span>?
            </p>
            <p className="mt-2 text-sm text-gray-500">
              This will permanently remove the department from the system. This action cannot be reversed.
            </p>
          </div>
          <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
            <DialogClose asChild>
              <Button variant="outline" className="border-gray-300">Cancel</Button>
            </DialogClose>
            <Button variant="destructive" onClick={handleDeleteDepartment}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DepartmentManagement;
