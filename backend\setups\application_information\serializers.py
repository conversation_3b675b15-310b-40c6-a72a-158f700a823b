from rest_framework import serializers
from .models import ApplicationInformation
from setups.college.models import College
from setups.department.models import Department
from setups.program.models import Program
from setups.admission_type.models import AdmissionType
from setups.study_field.models import StudyField
from setups.study_program.models import StudyProgram

class ApplicationCollegeSerializer(serializers.ModelSerializer):
    class Meta:
        model = College
        fields = ['id', 'name']
        ref_name = 'ApplicationCollege'

class ApplicationDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['id', 'name']
        ref_name = 'ApplicationDepartment'

class ApplicationProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = Program
        fields = ['id', 'program_code', 'program_name', 'registration_fee']
        ref_name = 'ApplicationProgram'

class ApplicationAdmissionTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdmissionType
        fields = ['id', 'name']
        ref_name = 'ApplicationAdmissionType'

class ApplicationStudyFieldSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='field_of_study', read_only=True)

    class Meta:
        model = StudyField
        fields = ['id', 'name']
        ref_name = 'ApplicationStudyField'

class ApplicationStudyProgramSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='program_name', read_only=True)

    class Meta:
        model = StudyProgram
        fields = ['id', 'name']
        ref_name = 'ApplicationStudyProgram'

class ApplicationInformationSerializer(serializers.ModelSerializer):
    # For reading (GET requests) - use nested serializers
    college = ApplicationCollegeSerializer(read_only=True)
    department = ApplicationDepartmentSerializer(read_only=True)
    program = ApplicationProgramSerializer(read_only=True)
    admission_type = ApplicationAdmissionTypeSerializer(read_only=True)
    field_of_study = ApplicationStudyFieldSerializer(read_only=True)
    study_program = ApplicationStudyProgramSerializer(read_only=True)

    # For writing (POST/PUT requests) - use foreign key IDs
    college_id = serializers.IntegerField(write_only=True, required=False)
    department_id = serializers.IntegerField(write_only=True, required=False)
    program_id = serializers.IntegerField(write_only=True, required=False)
    admission_type_id = serializers.IntegerField(write_only=True, required=False)
    field_of_study_id = serializers.IntegerField(write_only=True, required=False)
    study_program_id = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = ApplicationInformation
        fields = ['id', 'admission_type', 'program', 'college', 'department', 'field_of_study', 'study_program',
                 'college_id', 'department_id', 'program_id', 'admission_type_id', 'field_of_study_id', 'study_program_id',
                 'spacial_case', 'duration', 'status', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def create(self, validated_data):
        # Extract the foreign key IDs
        college_id = validated_data.pop('college_id', None)
        department_id = validated_data.pop('department_id', None)
        program_id = validated_data.pop('program_id', None)
        admission_type_id = validated_data.pop('admission_type_id', None)
        field_of_study_id = validated_data.pop('field_of_study_id', None)
        study_program_id = validated_data.pop('study_program_id', None)

        # Get the actual objects
        if college_id:
            validated_data['college'] = College.objects.get(id=college_id)
        if department_id:
            validated_data['department'] = Department.objects.get(id=department_id)
        if program_id:
            validated_data['program'] = Program.objects.get(id=program_id)
        if admission_type_id:
            validated_data['admission_type'] = AdmissionType.objects.get(id=admission_type_id)
        if field_of_study_id:
            validated_data['field_of_study'] = StudyField.objects.get(id=field_of_study_id)
        if study_program_id:
            validated_data['study_program'] = StudyProgram.objects.get(id=study_program_id)

        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Extract the foreign key IDs
        college_id = validated_data.pop('college_id', None)
        department_id = validated_data.pop('department_id', None)
        program_id = validated_data.pop('program_id', None)
        admission_type_id = validated_data.pop('admission_type_id', None)
        field_of_study_id = validated_data.pop('field_of_study_id', None)
        study_program_id = validated_data.pop('study_program_id', None)

        # Get the actual objects
        if college_id:
            validated_data['college'] = College.objects.get(id=college_id)
        if department_id:
            validated_data['department'] = Department.objects.get(id=department_id)
        if program_id:
            validated_data['program'] = Program.objects.get(id=program_id)
        if admission_type_id:
            validated_data['admission_type'] = AdmissionType.objects.get(id=admission_type_id)
        if field_of_study_id:
            validated_data['field_of_study'] = StudyField.objects.get(id=field_of_study_id)
        if study_program_id:
            validated_data['study_program'] = StudyProgram.objects.get(id=study_program_id)

        return super().update(instance, validated_data)