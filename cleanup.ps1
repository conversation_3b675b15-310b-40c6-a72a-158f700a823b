# Project Cleanup Script
# This script removes unnecessary files and directories from the project

Write-Host "Starting project cleanup..." -ForegroundColor Green

# Remove Python cache files
Write-Host "Removing Python cache files..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Force -Name "__pycache__" | ForEach-Object {
    Remove-Item -Path $_ -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: $_" -ForegroundColor Gray
}

Get-ChildItem -Path . -Recurse -Force -Include "*.pyc", "*.pyo" | ForEach-Object {
    Remove-Item -Path $_ -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: $($_.Name)" -ForegroundColor Gray
}

# Remove Node.js dependencies from backend (if any)
if (Test-Path "backend/node_modules") {
    Write-Host "Removing Node.js modules from backend..." -ForegroundColor Yellow
    Remove-Item -Path "backend/node_modules" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: backend/node_modules" -ForegroundColor Gray
}

if (Test-Path "backend/package.json") {
    Write-Host "Removing Node.js package files from backend..." -ForegroundColor Yellow
    Remove-Item -Path "backend/package.json" -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "backend/package-lock.json" -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: backend/package.json and package-lock.json" -ForegroundColor Gray
}

# Remove build artifacts
Write-Host "Removing build artifacts..." -ForegroundColor Yellow
$buildDirs = @("frontend/dist", "frontend/build", "backend/dist", "backend/build")
foreach ($dir in $buildDirs) {
    if (Test-Path $dir) {
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "Removed: $dir" -ForegroundColor Gray
    }
}

# Remove development databases
Write-Host "Removing development databases..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Force -Include "*.sqlite3", "*.db" | ForEach-Object {
    Remove-Item -Path $_ -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: $($_.Name)" -ForegroundColor Gray
}

# Remove log files
Write-Host "Removing log files..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Force -Include "*.log" | ForEach-Object {
    Remove-Item -Path $_ -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: $($_.Name)" -ForegroundColor Gray
}

# Remove temporary files
Write-Host "Removing temporary files..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Force -Include "*.tmp", "*.temp" | ForEach-Object {
    Remove-Item -Path $_ -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: $($_.Name)" -ForegroundColor Gray
}

# Remove OS generated files
Write-Host "Removing OS generated files..." -ForegroundColor Yellow
Get-ChildItem -Path . -Recurse -Force -Include ".DS_Store", "Thumbs.db", "Desktop.ini" | ForEach-Object {
    Remove-Item -Path $_ -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: $($_.Name)" -ForegroundColor Gray
}

# Remove alternative package manager files
Write-Host "Removing alternative package manager files..." -ForegroundColor Yellow
if (Test-Path "frontend/bun.lockb") {
    Remove-Item -Path "frontend/bun.lockb" -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: frontend/bun.lockb" -ForegroundColor Gray
}

if (Test-Path "frontend/yarn.lock") {
    Remove-Item -Path "frontend/yarn.lock" -Force -ErrorAction SilentlyContinue
    Write-Host "Removed: frontend/yarn.lock" -ForegroundColor Gray
}

Write-Host "Project cleanup completed!" -ForegroundColor Green
Write-Host "Note: Virtual environments (venv/) are preserved. Remove manually if needed." -ForegroundColor Cyan
