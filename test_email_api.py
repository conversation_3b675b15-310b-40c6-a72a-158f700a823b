#!/usr/bin/env python3
"""
Simple test script to check the email notification API endpoints.
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/communication/emails"

def test_get_emails():
    """Test getting all emails."""
    print("Testing GET /api/communication/emails/")
    response = requests.get(f"{BASE_URL}/")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Found {len(data)} emails")
        return data
    else:
        print(f"Error: {response.text}")
        return []

def test_update_email(email_id, update_data):
    """Test updating an email."""
    print(f"\nTesting PATCH /api/communication/emails/{email_id}/")
    print(f"Update data: {json.dumps(update_data, indent=2)}")
    
    response = requests.patch(
        f"{BASE_URL}/{email_id}/",
        json=update_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        print("Update successful!")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return True
    else:
        print(f"Error: {response.text}")
        return False

def test_create_email(create_data):
    """Test creating an email."""
    print(f"\nTesting POST /api/communication/emails/")
    print(f"Create data: {json.dumps(create_data, indent=2)}")
    
    response = requests.post(
        f"{BASE_URL}/",
        json=create_data,
        headers={'Content-Type': 'application/json'}
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        print("Create successful!")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    else:
        print(f"Error: {response.text}")
        return None

if __name__ == "__main__":
    print("Email Notification API Test")
    print("=" * 40)
    
    # Test getting emails
    emails = test_get_emails()
    
    if emails:
        # Find a draft email to test update
        draft_emails = [email for email in emails if email['status'] == 'draft']
        
        if draft_emails:
            test_email = draft_emails[0]
            print(f"\nFound draft email to test: ID {test_email['id']}")
            
            # Test update
            update_data = {
                "subject": f"API Test Update - {test_email['subject']}",
                "content": "This email was updated via API test",
                "recipients": "<EMAIL>",
                "status": "draft"
            }
            
            test_update_email(test_email['id'], update_data)
        else:
            print("\nNo draft emails found, testing create instead")
            
            # Test create
            create_data = {
                "subject": "API Test Email",
                "content": "This is a test email created via API",
                "recipients": "<EMAIL>",
                "status": "draft"
            }
            
            test_create_email(create_data)
    
    print("\nTest completed!")
