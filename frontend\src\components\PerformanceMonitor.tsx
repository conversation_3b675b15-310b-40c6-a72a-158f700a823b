/**
 * Performance monitoring component for development and optimization
 */
import React, { useEffect, useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity, Clock, Zap, AlertTriangle } from 'lucide-react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  apiCalls: number;
  cacheHits: number;
  cacheMisses: number;
  fileValidations: number;
  securityScore: number;
  errors: string[];
  warnings: string[];
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  position = 'bottom-right',
  onMetricsUpdate
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    apiCalls: 0,
    cacheHits: 0,
    cacheMisses: 0,
    fileValidations: 0,
    securityScore: 100,
    errors: [],
    warnings: []
  });

  const [isVisible, setIsVisible] = useState(false);
  const renderStartTime = useRef<number>(0);
  const observerRef = useRef<PerformanceObserver | null>(null);

  useEffect(() => {
    if (!enabled) return;

    renderStartTime.current = performance.now();

    // Monitor performance entries
    if ('PerformanceObserver' in window) {
      observerRef.current = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'measure') {
            setMetrics(prev => ({
              ...prev,
              renderTime: entry.duration
            }));
          }
        });
      });

      observerRef.current.observe({ entryTypes: ['measure', 'navigation'] });
    }

    // Monitor memory usage
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // Convert to MB
        }));
      }
    };

    const memoryInterval = setInterval(updateMemoryUsage, 1000);

    // Monitor API calls
    const originalFetch = window.fetch;
    let apiCallCount = 0;

    window.fetch = async (...args) => {
      apiCallCount++;
      setMetrics(prev => ({ ...prev, apiCalls: apiCallCount }));
      
      const startTime = performance.now();
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        
        // Log slow API calls
        if (endTime - startTime > 1000) {
          setMetrics(prev => ({
            ...prev,
            warnings: [...prev.warnings, `Slow API call: ${args[0]} (${(endTime - startTime).toFixed(0)}ms)`]
          }));
        }
        
        return response;
      } catch (error) {
        setMetrics(prev => ({
          ...prev,
          errors: [...prev.errors, `API call failed: ${args[0]}`]
        }));
        throw error;
      }
    };

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      clearInterval(memoryInterval);
      window.fetch = originalFetch;
    };
  }, [enabled]);

  useEffect(() => {
    if (onMetricsUpdate) {
      onMetricsUpdate(metrics);
    }
  }, [metrics, onMetricsUpdate]);

  if (!enabled) return null;

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'bg-green-500';
    if (value <= thresholds.warning) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className={`fixed ${getPositionClasses()} z-50 max-w-sm`}>
      <Card className="bg-white/95 backdrop-blur-sm shadow-lg border">
        <CardHeader 
          className="pb-2 cursor-pointer"
          onClick={() => setIsVisible(!isVisible)}
        >
          <CardTitle className="text-sm flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Performance Monitor
            <Badge variant="outline" className="ml-auto">
              {isVisible ? 'Hide' : 'Show'}
            </Badge>
          </CardTitle>
        </CardHeader>
        
        {isVisible && (
          <CardContent className="pt-0 space-y-3">
            {/* Render Performance */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-3 w-3" />
                <span className="text-xs">Render Time</span>
              </div>
              <div className="flex items-center gap-1">
                <div 
                  className={`w-2 h-2 rounded-full ${getPerformanceColor(
                    metrics.renderTime, 
                    { good: 16, warning: 50 }
                  )}`}
                />
                <span className="text-xs font-mono">
                  {metrics.renderTime.toFixed(1)}ms
                </span>
              </div>
            </div>

            {/* Memory Usage */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="h-3 w-3" />
                <span className="text-xs">Memory</span>
              </div>
              <div className="flex items-center gap-1">
                <div 
                  className={`w-2 h-2 rounded-full ${getPerformanceColor(
                    metrics.memoryUsage, 
                    { good: 50, warning: 100 }
                  )}`}
                />
                <span className="text-xs font-mono">
                  {metrics.memoryUsage.toFixed(1)}MB
                </span>
              </div>
            </div>

            {/* API Calls */}
            <div className="flex items-center justify-between">
              <span className="text-xs">API Calls</span>
              <span className="text-xs font-mono">{metrics.apiCalls}</span>
            </div>

            {/* Cache Performance */}
            <div className="flex items-center justify-between">
              <span className="text-xs">Cache Hit Rate</span>
              <span className="text-xs font-mono">
                {metrics.cacheHits + metrics.cacheMisses > 0 
                  ? `${((metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100).toFixed(0)}%`
                  : 'N/A'
                }
              </span>
            </div>

            {/* File Validations */}
            <div className="flex items-center justify-between">
              <span className="text-xs">File Validations</span>
              <span className="text-xs font-mono">{metrics.fileValidations}</span>
            </div>

            {/* Security Score */}
            <div className="flex items-center justify-between">
              <span className="text-xs">Security Score</span>
              <div className="flex items-center gap-1">
                <div 
                  className={`w-2 h-2 rounded-full ${getPerformanceColor(
                    100 - metrics.securityScore, 
                    { good: 10, warning: 25 }
                  )}`}
                />
                <span className="text-xs font-mono">{metrics.securityScore}/100</span>
              </div>
            </div>

            {/* Errors and Warnings */}
            {(metrics.errors.length > 0 || metrics.warnings.length > 0) && (
              <div className="border-t pt-2">
                {metrics.errors.length > 0 && (
                  <div className="flex items-center gap-1 mb-1">
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                    <span className="text-xs text-red-600">
                      {metrics.errors.length} error(s)
                    </span>
                  </div>
                )}
                {metrics.warnings.length > 0 && (
                  <div className="flex items-center gap-1">
                    <AlertTriangle className="h-3 w-3 text-yellow-500" />
                    <span className="text-xs text-yellow-600">
                      {metrics.warnings.length} warning(s)
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* Clear Metrics Button */}
            <button
              onClick={() => setMetrics({
                renderTime: 0,
                memoryUsage: 0,
                apiCalls: 0,
                cacheHits: 0,
                cacheMisses: 0,
                fileValidations: 0,
                securityScore: 100,
                errors: [],
                warnings: []
              })}
              className="w-full text-xs py-1 px-2 bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            >
              Clear Metrics
            </button>
          </CardContent>
        )}
      </Card>
    </div>
  );
};

// Hook for updating performance metrics from components
export const usePerformanceMetrics = () => {
  const updateCacheMetrics = (hit: boolean) => {
    // This would be implemented to communicate with the PerformanceMonitor
    // For now, we'll use a simple event system
    window.dispatchEvent(new CustomEvent('performance-cache', { 
      detail: { hit } 
    }));
  };

  const updateFileValidation = (score: number) => {
    window.dispatchEvent(new CustomEvent('performance-file-validation', { 
      detail: { score } 
    }));
  };

  const reportError = (error: string) => {
    window.dispatchEvent(new CustomEvent('performance-error', { 
      detail: { error } 
    }));
  };

  const reportWarning = (warning: string) => {
    window.dispatchEvent(new CustomEvent('performance-warning', { 
      detail: { warning } 
    }));
  };

  return {
    updateCacheMetrics,
    updateFileValidation,
    reportError,
    reportWarning
  };
};
