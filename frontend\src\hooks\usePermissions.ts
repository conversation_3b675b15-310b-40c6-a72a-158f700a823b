import { useCallback, useMemo } from 'react';
import { useRBAC } from '@/contexts/SimpleRBACContext';

// Common role definitions
export const UserRoles = {
  SUPER_ADMIN: 'Super Admin',
  ADMINISTRATOR: 'Administrator',
  MAIN_REGISTRAR: 'Main Registrar',
  REGISTRAR_OFFICER: 'Registrar Officer',
  DEPARTMENT_HEAD: 'Department Head',
  VERIFICATION_CLERK: 'Verification Clerk',
  OFFICIAL_CLERK: 'Official Clerk',
  SERVICE_MANAGER: 'Service Manager',
} as const;

// Common permission definitions
export const UserPermissions = {
  // User management
  USER_CREATE: 'auth.add_user',
  USER_VIEW: 'auth.view_user',
  USER_EDIT: 'auth.change_user',
  USER_DELETE: 'auth.delete_user',
  
  // Role management
  ROLE_CREATE: 'auth.add_group',
  ROLE_VIEW: 'auth.view_group',
  ROLE_EDIT: 'auth.change_group',
  ROLE_DELETE: 'auth.delete_group',
  
  // Application management
  APPLICATION_VIEW: 'registration.view_applicantprogramselection',
  APPLICATION_EDIT: 'registration.change_applicantprogramselection',
  APPLICATION_DELETE: 'registration.delete_applicantprogramselection',
  APPLICATION_APPROVE: 'registration.approve_applicantprogramselection',
  
  // Graduate management
  GRADUATE_VIEW: 'GraduateVerification.view_graduate',
  GRADUATE_EDIT: 'GraduateVerification.change_graduate',
  GRADUATE_DELETE: 'GraduateVerification.delete_graduate',
  
  // Official management
  OFFICIAL_VIEW: 'official.view_official',
  OFFICIAL_EDIT: 'official.change_official',
  OFFICIAL_DELETE: 'official.delete_official',
  
  // Service management
  SERVICE_VIEW: 'services.view_servicerequest',
  SERVICE_EDIT: 'services.change_servicerequest',
  SERVICE_DELETE: 'services.delete_servicerequest',
  
  // System settings
  SETTINGS_VIEW: 'settings_manager.view_organizationsettings',
  SETTINGS_EDIT: 'settings_manager.change_organizationsettings',
} as const;

// Access level definitions
export const AccessLevels = {
  SUPERUSER: 'superuser',
  ADMIN: 'admin',
  STAFF: 'staff',
  USER: 'user',
} as const;

/**
 * Hook for checking user permissions
 */
export const usePermissions = () => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    checkPermissionAsync,
    user,
    isSuperuser
  } = useRBAC();

  // Dynamic permission checking - superusers have all permissions
  const dynamicHasPermission = useCallback((permission: string) => {
    if (isSuperuser) return true;
    return hasPermission(permission);
  }, [isSuperuser, hasPermission]);

  const dynamicHasAnyPermission = useCallback((permissions: string[]) => {
    if (isSuperuser) return true;
    return hasAnyPermission(permissions);
  }, [isSuperuser, hasAnyPermission]);

  const dynamicHasAllPermissions = useCallback((permissions: string[]) => {
    if (isSuperuser) return true;
    return hasAllPermissions(permissions);
  }, [isSuperuser, hasAllPermissions]);

  // Memoized permission checkers for common permissions with dynamic access
  const canManageUsers = useMemo(() =>
    dynamicHasAnyPermission([
      UserPermissions.USER_CREATE,
      UserPermissions.USER_EDIT,
      UserPermissions.USER_DELETE
    ]), [dynamicHasAnyPermission]
  );

  const canViewUsers = useMemo(() =>
    dynamicHasPermission(UserPermissions.USER_VIEW), [dynamicHasPermission]
  );

  const canManageRoles = useMemo(() =>
    dynamicHasAnyPermission([
      UserPermissions.ROLE_CREATE,
      UserPermissions.ROLE_EDIT,
      UserPermissions.ROLE_DELETE
    ]), [dynamicHasAnyPermission]
  );

  const canManageApplications = useMemo(() =>
    dynamicHasAnyPermission([
      UserPermissions.APPLICATION_VIEW,
      UserPermissions.APPLICATION_EDIT,
      UserPermissions.APPLICATION_DELETE,
      UserPermissions.APPLICATION_APPROVE
    ]), [dynamicHasAnyPermission]
  );

  const canManageGraduates = useMemo(() =>
    dynamicHasAnyPermission([
      UserPermissions.GRADUATE_VIEW,
      UserPermissions.GRADUATE_EDIT,
      UserPermissions.GRADUATE_DELETE
    ]), [dynamicHasAnyPermission]
  );

  const canManageOfficials = useMemo(() =>
    dynamicHasAnyPermission([
      UserPermissions.OFFICIAL_VIEW,
      UserPermissions.OFFICIAL_EDIT,
      UserPermissions.OFFICIAL_DELETE
    ]), [dynamicHasAnyPermission]
  );

  const canManageServices = useMemo(() =>
    dynamicHasAnyPermission([
      UserPermissions.SERVICE_VIEW,
      UserPermissions.SERVICE_EDIT,
      UserPermissions.SERVICE_DELETE
    ]), [dynamicHasAnyPermission]
  );

  const canManageSettings = useMemo(() =>
    dynamicHasAnyPermission([
      UserPermissions.SETTINGS_VIEW,
      UserPermissions.SETTINGS_EDIT
    ]), [dynamicHasAnyPermission]
  );

  // Async permission checker with caching
  const checkPermissionWithCache = useCallback(async (permission: string) => {
    // Superusers always have permission
    if (isSuperuser) return true;

    // First check local cache
    if (hasPermission(permission)) {
      return true;
    }

    // If not in cache, check with backend
    return await checkPermissionAsync(permission);
  }, [isSuperuser, hasPermission, checkPermissionAsync]);

  return {
    // Dynamic permission functions (superuser-aware)
    hasPermission: dynamicHasPermission,
    hasAnyPermission: dynamicHasAnyPermission,
    hasAllPermissions: dynamicHasAllPermissions,
    checkPermissionAsync: checkPermissionWithCache,
    
    // Common permission checks
    canManageUsers,
    canViewUsers,
    canManageRoles,
    canManageApplications,
    canManageGraduates,
    canManageOfficials,
    canManageServices,
    canManageSettings,
    
    // User info
    userPermissions: user?.permissions || [],
  };
};

/**
 * Hook for checking user roles with dynamic access control
 */
export const useRoles = () => {
  const {
    hasRole,
    hasAnyRole,
    hasAllRoles,
    checkRoleAsync,
    user,
    isSuperuser,
    isAdmin,
    isStaff,
    canAccessAdmin,
    isStaffWithGroups
  } = useRBAC();

  // Dynamic role checking - superusers have all roles
  const dynamicHasRole = useCallback((role: string) => {
    if (isSuperuser) return true;
    return hasRole(role);
  }, [isSuperuser, hasRole]);

  const dynamicHasAnyRole = useCallback((roles: string[]) => {
    if (isSuperuser) return true;
    return hasAnyRole(roles);
  }, [isSuperuser, hasAnyRole]);

  const dynamicHasAllRoles = useCallback((roles: string[]) => {
    if (isSuperuser) return true;
    return hasAllRoles(roles);
  }, [isSuperuser, hasAllRoles]);

  // Memoized role checkers for common roles with dynamic access
  const isSuperAdmin = useMemo(() =>
    dynamicHasRole(UserRoles.SUPER_ADMIN) || isSuperuser, [dynamicHasRole, isSuperuser]
  );

  const isAdministrator = useMemo(() =>
    dynamicHasRole(UserRoles.ADMINISTRATOR) || isAdmin, [dynamicHasRole, isAdmin]
  );

  const isMainRegistrar = useMemo(() =>
    dynamicHasRole(UserRoles.MAIN_REGISTRAR), [dynamicHasRole]
  );

  const isRegistrarOfficer = useMemo(() =>
    dynamicHasRole(UserRoles.REGISTRAR_OFFICER), [dynamicHasRole]
  );

  const isDepartmentHead = useMemo(() =>
    dynamicHasRole(UserRoles.DEPARTMENT_HEAD), [dynamicHasRole]
  );

  const isVerificationClerk = useMemo(() =>
    dynamicHasRole(UserRoles.VERIFICATION_CLERK), [dynamicHasRole]
  );

  const isOfficialClerk = useMemo(() =>
    dynamicHasRole(UserRoles.OFFICIAL_CLERK), [dynamicHasRole]
  );

  const isServiceManager = useMemo(() =>
    dynamicHasRole(UserRoles.SERVICE_MANAGER), [dynamicHasRole]
  );

  // Access level checks with dynamic control
  const hasAdminAccess = useMemo(() =>
    isSuperuser || isAdmin || canAccessAdmin, [isSuperuser, isAdmin, canAccessAdmin]
  );

  const hasStaffAccess = useMemo(() =>
    isSuperuser || (isStaff && isStaffWithGroups), [isSuperuser, isStaff, isStaffWithGroups]
  );

  const hasRegistrarAccess = useMemo(() =>
    dynamicHasAnyRole([
      UserRoles.MAIN_REGISTRAR,
      UserRoles.REGISTRAR_OFFICER,
      UserRoles.ADMINISTRATOR,
      UserRoles.SUPER_ADMIN
    ]), [dynamicHasAnyRole]
  );

  // Async role checker with caching
  const checkRoleWithCache = useCallback(async (role: string) => {
    // First check local cache
    if (hasRole(role)) {
      return true;
    }
    
    // If not in cache, check with backend
    return await checkRoleAsync(role);
  }, [hasRole, checkRoleAsync]);

  return {
    // Dynamic role functions (superuser-aware) - these override the basic ones
    hasRole: dynamicHasRole,
    hasAnyRole: dynamicHasAnyRole,
    hasAllRoles: dynamicHasAllRoles,
    checkRoleAsync: checkRoleWithCache,

    // Common role checks
    isSuperAdmin,
    isAdministrator,
    isMainRegistrar,
    isRegistrarOfficer,
    isDepartmentHead,
    isVerificationClerk,
    isOfficialClerk,
    isServiceManager,

    // Access level checks
    hasAdminAccess,
    hasStaffAccess,
    hasRegistrarAccess,

    // User info
    userRoles: user?.roles || [],
    accessLevel: isSuperuser ? AccessLevels.SUPERUSER :
                 isAdmin ? AccessLevels.ADMIN :
                 isStaff ? AccessLevels.STAFF :
                 AccessLevels.USER,
  };
};

/**
 * Combined hook for both permissions and roles
 * Renamed to avoid conflict with existing AuthContext
 */
export const useRBACAuth = () => {
  const rbac = useRBAC();
  const permissions = usePermissions();
  const roles = useRoles();

  return {
    ...rbac,
    ...permissions,
    ...roles,
  };
};

export default usePermissions;
