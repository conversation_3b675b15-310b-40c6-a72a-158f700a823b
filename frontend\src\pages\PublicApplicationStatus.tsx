import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2, Search, CheckCircle, AlertTriangle, Clock, FileCheck, XCircle, Plus, FileText, Award, User, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import api from '@/services/api';
import DocumentTitle from '@/components/DocumentTitle';
import DashboardLayout from '@/components/DashboardLayout';

// Define interfaces for our data types
interface ApplicationStatusResponse {
  found: boolean;
  gat_info?: {
    gat_number: string;
    gat_result: number;
    created_at: string;
    updated_at: string;
  };
  user_info?: {
    first_name: string;
    last_name: string;
    email: string;
  };
  application_info?: {
    application_number: string;
    transaction_id: string;
    sponsorship: string;
    created_at: string;
    updated_at: string;
    details: {
      program?: string;
      department?: string;
      college?: string;
      field_of_study?: string;
      study_program?: string;
      admission_type?: string;
    };
  };
  payment_info?: {
    payment_status: string;
    payment_method: string;
    amount_paid: string;
    created_at: string;
    updated_at: string;
  };
  status: {
    registrar: string;
    department: string;
    payment: string;
  };
  error?: string;
}

const PublicApplicationStatus = () => {
  const navigate = useNavigate();
  const [searchType, setSearchType] = useState<'gat' | 'application'>('gat');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<ApplicationStatusResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Set page title on component mount
  useEffect(() => {
    // This is a public page, no authentication check needed
    if (process.env.NODE_ENV === 'development') {
      console.debug('Public application status page loaded');
    }
  }, []);

  // Function to check application status
  const checkApplicationStatus = async (params: { gat_number?: string, application_number?: string }) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('Checking application status with params:', params);
    }
    try {
      const response = await api.get('/application-status/', { params });
      if (process.env.NODE_ENV === 'development') {
        console.debug('Application status response:', response.data);
      }
      return response.data;
    } catch (error: any) {
      console.error('Error checking application status:', error);
      if (process.env.NODE_ENV === 'development') {
        console.debug('Error response:', error.response?.data);
      }

      // If the API returns a specific error message, use it
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      } else if (error.response?.status === 404) {
        throw new Error('No application found with the provided information');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required to access this information');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to access this information');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error. Please try again later.');
      }

      // For any other errors
      throw new Error('An error occurred while searching. Please try again.');
    }
  };

  // Function to handle search
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a search term');
      return;
    }

    setLoading(true);
    setError(null);
    setSearchResult(null);

    try {
      // Construct the query parameters based on search type
      const params = searchType === 'gat'
        ? { gat_number: searchQuery }
        : { application_number: searchQuery };

      // Make the API call using our checkApplicationStatus function
      const result = await checkApplicationStatus(params);

      if (process.env.NODE_ENV === 'development') {
        console.debug('Search result:', result);
      }

      // Check if the result is valid
      if (!result) {
        throw new Error('Invalid response from server');
      }

      setSearchResult(result);

      if (!result.found) {
        setError('No application found with the provided information');
      }
    } catch (error: any) {
      console.error('Error searching for application:', error);

      // Use the error message if it's an Error object, otherwise try to extract from response
      const errorMessage = error instanceof Error
        ? error.message
        : error.response?.data?.error || 'An error occurred while searching. Please try again.';

      setError(errorMessage);
      toast.error('Search failed: ' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Function to render status badge
  const renderStatusBadge = (status: string) => {
    // Filter out "Complete" status and show "Approved" instead
    const normalizedStatus = status.toLowerCase() === 'completed' ? 'approved' : status.toLowerCase();

    switch (normalizedStatus) {
      case 'approved':
        return (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 px-3 py-1 text-sm font-medium">
              Approved
            </Badge>
          </div>
        );
      case 'rejected':
        return (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-2">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 px-3 py-1 text-sm font-medium">
              Rejected
            </Badge>
          </div>
        );
      case 'under review':
        return (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-2">
              <FileCheck className="h-6 w-6 text-blue-600" />
            </div>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 px-3 py-1 text-sm font-medium">
              Under Review
            </Badge>
          </div>
        );
      case 'pending':
      default:
        return (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mb-2">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 px-3 py-1 text-sm font-medium">
              Pending
            </Badge>
          </div>
        );
    }
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <DashboardLayout>
      <DocumentTitle pageTitle="University of Gondar - Application Portal" />
      <div className="w-full relative">
        <div
          className="fixed top-0 left-0 w-full h-full opacity-10 pointer-events-none"
          style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
        ></div>

        {/* Search Card */}
        <Card className="mt-8 mb-8 shadow-md mx-4 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] text-white relative">
            <div
              className="absolute top-0 left-0 w-full h-full opacity-20 pointer-events-none"
              style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
            ></div>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-xl">Application Verification</CardTitle>
                <CardDescription className="text-blue-100">Enter your GAT Number or Application Number to verify your application</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
                <div className="flex flex-wrap gap-6 mb-4 relative z-10">
                  <div
                    className="flex items-center space-x-2 p-2 hover:bg-blue-100 rounded-md cursor-pointer transition-colors"
                    onClick={() => setSearchType('gat')}
                  >
                    <input
                      type="radio"
                      id="gat"
                      name="searchType"
                      value="gat"
                      checked={searchType === 'gat'}
                      onChange={() => setSearchType('gat')}
                      className="h-5 w-5 text-blue-600 focus:ring-blue-500 cursor-pointer"
                    />
                    <Label
                      htmlFor="gat"
                      className="text-gray-700 font-medium cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        setSearchType('gat');
                      }}
                    >
                      GAT Number
                    </Label>
                  </div>
                  <div
                    className="flex items-center space-x-2 p-2 hover:bg-blue-100 rounded-md cursor-pointer transition-colors"
                    onClick={() => setSearchType('application')}
                  >
                    <input
                      type="radio"
                      id="application"
                      name="searchType"
                      value="application"
                      checked={searchType === 'application'}
                      onChange={() => setSearchType('application')}
                      className="h-5 w-5 text-blue-600 focus:ring-blue-500 cursor-pointer"
                    />
                    <Label
                      htmlFor="application"
                      className="text-gray-700 font-medium cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        setSearchType('application');
                      }}
                    >
                      Application Number
                    </Label>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 relative z-10">
                  <div className="flex-1">
                    <Label htmlFor="search-input" className="sr-only">
                      {searchType === 'gat' ? "Enter GAT Number" : "Enter Application Number"}
                    </Label>
                    <Input
                      id="search-input"
                      type="text"
                      placeholder={searchType === 'gat' ? "Enter GAT Number" : "Enter Application Number"}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleSearch();
                        }
                      }}
                      className="w-full h-12 px-4 border-blue-200 focus:border-blue-400 focus:ring-blue-400 relative z-10"
                      autoComplete="off"
                    />
                  </div>
                  <Button
                    onClick={handleSearch}
                    disabled={loading}
                    className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] hover:from-[#0e4a7d] hover:to-[#083057] text-white h-12 px-6 font-medium text-base shadow-lg hover:shadow-xl transition-all duration-300 relative z-10"
                    type="button"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Searching...
                      </>
                    ) : (
                      <>
                        <Search className="h-5 w-5 mr-2" />
                        Search
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <Card className="shadow-md mb-8 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-red-600 to-red-800 text-white relative">
              <div
                className="absolute top-0 left-0 w-full h-full opacity-20 pointer-events-none"
                style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
              ></div>
              <CardTitle className="text-xl">Error</CardTitle>
              <CardDescription className="text-red-100">There was a problem with your search</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="bg-red-100 p-3 rounded-full mr-4 flex-shrink-0">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-red-800 mb-2">Search Error</h3>
                    <p className="text-red-600">{error}</p>
                    <p className="text-gray-600 mt-2">Please check your input and try again.</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}



        {/* Search Results */}
        {searchResult && searchResult.found && (
          <Card className="shadow-md overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] text-white relative">
              <div
                className="absolute top-0 left-0 w-full h-full opacity-20 pointer-events-none"
                style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
              ></div>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl">Application Details</CardTitle>
                  <CardDescription className="text-blue-100">View your application information</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              {/* Status Summary */}
              <div className="space-y-6">
                <div className="overflow-hidden border border-blue-200 hover:border-blue-300 transition-all duration-300 shadow-md hover:shadow-lg rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 relative">
                  <div
                    className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none"
                    style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
                  ></div>
                  <div className="p-6 relative z-10">
                    <div className="flex items-center mb-4">
                      <div className="bg-[#1a73c0] p-2 rounded-md mr-3">
                        <FileCheck className="h-5 w-5 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-800">Verification Status</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="bg-gradient-to-b from-white to-blue-50 border border-blue-100 shadow-md p-6 rounded-lg text-center hover:shadow-lg transition-all duration-300">
                        <p className="text-sm text-blue-700 mb-3 uppercase tracking-wide font-semibold">Registrar Status</p>
                        <div className="flex justify-center">
                          {renderStatusBadge(searchResult.status.registrar)}
                        </div>
                      </div>
                      <div className="bg-gradient-to-b from-white to-blue-50 border border-blue-100 shadow-md p-6 rounded-lg text-center hover:shadow-lg transition-all duration-300">
                        <p className="text-sm text-blue-700 mb-3 uppercase tracking-wide font-semibold">Department Status</p>
                        <div className="flex justify-center">
                          {renderStatusBadge(searchResult.status.department)}
                        </div>
                      </div>
                      <div className="bg-gradient-to-b from-white to-blue-50 border border-blue-100 shadow-md p-6 rounded-lg text-center hover:shadow-lg transition-all duration-300">
                        <p className="text-sm text-blue-700 mb-3 uppercase tracking-wide font-semibold">Payment Status</p>
                        <div className="flex justify-center">
                          {renderStatusBadge(searchResult.status.payment)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Application Details */}
              {searchResult.gat_info && (
                <div className="mt-6">
                  <div className="overflow-hidden border border-blue-200 hover:border-blue-300 transition-all duration-300 shadow-md hover:shadow-lg rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 relative">
                    <div
                      className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none"
                      style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
                    ></div>
                    <div className="p-6 relative z-10">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#1a73c0] p-2 rounded-md mr-3">
                          <Award className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-800">GAT Information</h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">GAT Number:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.gat_info.gat_number}</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">GAT Result:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.gat_info.gat_result}</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Created:</span>
                          <span className="text-sm font-semibold text-blue-800">{formatDate(searchResult.gat_info.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* User Information */}
              {searchResult.user_info && (
                <div className="mt-6">
                  <div className="overflow-hidden border border-blue-200 hover:border-blue-300 transition-all duration-300 shadow-md hover:shadow-lg rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 relative">
                    <div
                      className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none"
                      style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
                    ></div>
                    <div className="p-6 relative z-10">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#1a73c0] p-2 rounded-md mr-3">
                          <User className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-800">Applicant Information</h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Name:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.user_info.first_name} {searchResult.user_info.last_name}</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Email:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.user_info.email}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Application Information */}
              {searchResult.application_info && (
                <div className="mt-6">
                  <div className="overflow-hidden border border-blue-200 hover:border-blue-300 transition-all duration-300 shadow-md hover:shadow-lg rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 relative">
                    <div
                      className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none"
                      style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
                    ></div>
                    <div className="p-6 relative z-10">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#1a73c0] p-2 rounded-md mr-3">
                          <FileText className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-800">Application Information</h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Application Number:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.application_info.application_number}</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Sponsorship:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.application_info.sponsorship}</span>
                        </div>
                        {searchResult.application_info.details.program && (
                          <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                            <span className="text-sm text-gray-600 font-medium min-w-32">Program:</span>
                            <span className="text-sm font-semibold text-blue-800">{searchResult.application_info.details.program}</span>
                          </div>
                        )}
                        {searchResult.application_info.details.department && (
                          <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                            <span className="text-sm text-gray-600 font-medium min-w-32">Department:</span>
                            <span className="text-sm font-semibold text-blue-800">{searchResult.application_info.details.department}</span>
                          </div>
                        )}
                        {searchResult.application_info.details.college && (
                          <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                            <span className="text-sm text-gray-600 font-medium min-w-32">College:</span>
                            <span className="text-sm font-semibold text-blue-800">{searchResult.application_info.details.college}</span>
                          </div>
                        )}
                        {searchResult.application_info.details.field_of_study && (
                          <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                            <span className="text-sm text-gray-600 font-medium min-w-32">Field of Study:</span>
                            <span className="text-sm font-semibold text-blue-800">{searchResult.application_info.details.field_of_study}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Payment Information */}
              {searchResult.payment_info && (
                <div className="mt-6">
                  <div className="overflow-hidden border border-blue-200 hover:border-blue-300 transition-all duration-300 shadow-md hover:shadow-lg rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 relative">
                    <div
                      className="absolute top-0 left-0 w-full h-full opacity-10 pointer-events-none"
                      style={{ backgroundImage: 'url("/images/pattern-bg.svg")', backgroundSize: 'cover' }}
                    ></div>
                    <div className="p-6 relative z-10">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#1a73c0] p-2 rounded-md mr-3">
                          <CreditCard className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-800">Payment Information</h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Payment Method:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.payment_info.payment_method}</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Amount Paid:</span>
                          <span className="text-sm font-semibold text-blue-800">{searchResult.payment_info.amount_paid}</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 hover:bg-blue-100 rounded-md transition-colors">
                          <span className="text-sm text-gray-600 font-medium min-w-32">Payment Date:</span>
                          <span className="text-sm font-semibold text-blue-800">{formatDate(searchResult.payment_info.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
};

export default PublicApplicationStatus;
