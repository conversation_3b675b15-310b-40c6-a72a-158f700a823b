/**
 * Django-Compatible Authentication & Authorization System
 * 
 * This module implements Django's authentication and authorization system
 * exactly as it works in Django, including:
 * - User authentication
 * - Permission checking (user.has_perm())
 * - Group-based permissions
 * - Superuser privileges
 * - Staff status checking
 * - Django's permission format (app_label.codename)
 */

import { User } from '@/types/auth';

/**
 * Django Permission Object (matches Django's Permission model)
 */
export interface DjangoPermission {
  id: number;
  name: string;
  content_type: number;
  codename: string;
  app_label?: string;
}

/**
 * Django Group Object (matches Django's Group model)
 */
export interface DjangoGroup {
  id: number;
  name: string;
  permissions: DjangoPermission[];
}

/**
 * Django User Object (matches Django's User model)
 */
export interface DjangoUser {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  date_joined: string;
  last_login: string | null;
  
  // Permissions and groups
  user_permissions: DjangoPermission[];
  groups: DjangoGroup[];
  
  // Computed permission list (all permissions from user + groups)
  all_permissions?: string[];
}

/**
 * Django-compatible authentication class
 * Implements Django's User.has_perm(), User.has_perms(), etc.
 */
export class DjangoAuthenticator {
  private user: DjangoUser | null;

  constructor(user: DjangoUser | null) {
    this.user = user;
  }

  /**
   * Check if user is authenticated (Django's user.is_authenticated)
   */
  isAuthenticated(): boolean {
    return this.user !== null && this.user.is_active;
  }

  /**
   * Check if user is anonymous (Django's user.is_anonymous)
   */
  isAnonymous(): boolean {
    return this.user === null;
  }

  /**
   * Check if user is staff (Django's user.is_staff)
   */
  isStaff(): boolean {
    return this.user?.is_staff === true;
  }

  /**
   * Check if user is superuser (Django's user.is_superuser)
   */
  isSuperuser(): boolean {
    return this.user?.is_superuser === true;
  }

  /**
   * Check if user is active (Django's user.is_active)
   */
  isActive(): boolean {
    return this.user?.is_active === true;
  }

  /**
   * Get all user permissions (Django's user.get_all_permissions())
   * Returns permissions from both user_permissions and group permissions
   */
  getAllPermissions(): Set<string> {
    if (!this.user) return new Set();

    // Superuser has all permissions
    if (this.user.is_superuser) {
      return new Set(['*']); // Special marker for all permissions
    }

    const permissions = new Set<string>();

    // Add user's direct permissions
    this.user.user_permissions?.forEach(perm => {
      const permString = perm.app_label ? `${perm.app_label}.${perm.codename}` : perm.codename;
      permissions.add(permString);
    });

    // Add permissions from groups
    this.user.groups?.forEach(group => {
      group.permissions?.forEach(perm => {
        const permString = perm.app_label ? `${perm.app_label}.${perm.codename}` : perm.codename;
        permissions.add(permString);
      });
    });

    return permissions;
  }

  /**
   * Check if user has specific permission (Django's user.has_perm())
   * 
   * @param permission Permission string in Django format (e.g., 'auth.add_user', 'change_user')
   * @returns boolean
   */
  hasPerm(permission: string): boolean {
    if (!this.user || !this.user.is_active) return false;

    // Superuser has all permissions
    if (this.user.is_superuser) return true;

    const allPermissions = this.getAllPermissions();
    
    // Check exact match
    if (allPermissions.has(permission)) return true;

    // If permission doesn't have app_label, check with common app labels
    if (!permission.includes('.')) {
      const commonApps = ['auth', 'admin', 'contenttypes', 'sessions'];
      for (const app of commonApps) {
        if (allPermissions.has(`${app}.${permission}`)) return true;
      }
    }

    return false;
  }

  /**
   * Check if user has multiple permissions (Django's user.has_perms())
   * 
   * @param permissions Array of permission strings
   * @param requireAll If true, user must have ALL permissions. If false, user needs ANY permission.
   * @returns boolean
   */
  hasPerms(permissions: string[], requireAll: boolean = true): boolean {
    if (!permissions || permissions.length === 0) return true;

    if (requireAll) {
      return permissions.every(perm => this.hasPerm(perm));
    } else {
      return permissions.some(perm => this.hasPerm(perm));
    }
  }

  /**
   * Check if user has permission for specific model (Django's user.has_module_perms())
   * 
   * @param appLabel Django app label (e.g., 'auth', 'admin')
   * @returns boolean
   */
  hasModulePerms(appLabel: string): boolean {
    if (!this.user || !this.user.is_active) return false;
    if (this.user.is_superuser) return true;

    const allPermissions = this.getAllPermissions();
    
    // Check if user has any permission for this app
    for (const perm of allPermissions) {
      if (perm.startsWith(`${appLabel}.`)) return true;
    }

    return false;
  }

  /**
   * Get user's groups (Django's user.groups.all())
   */
  getGroups(): DjangoGroup[] {
    return this.user?.groups || [];
  }

  /**
   * Check if user is in specific group
   */
  isInGroup(groupName: string): boolean {
    return this.getGroups().some(group => group.name === groupName);
  }

  /**
   * Check if user is in any of the specified groups
   */
  isInAnyGroup(groupNames: string[]): boolean {
    return groupNames.some(groupName => this.isInGroup(groupName));
  }

  /**
   * Get user's direct permissions (not including group permissions)
   */
  getUserPermissions(): Set<string> {
    if (!this.user) return new Set();

    const permissions = new Set<string>();
    this.user.user_permissions?.forEach(perm => {
      const permString = perm.app_label ? `${perm.app_label}.${perm.codename}` : perm.codename;
      permissions.add(permString);
    });

    return permissions;
  }

  /**
   * Get permissions from groups only
   */
  getGroupPermissions(): Set<string> {
    if (!this.user) return new Set();

    const permissions = new Set<string>();
    this.user.groups?.forEach(group => {
      group.permissions?.forEach(perm => {
        const permString = perm.app_label ? `${perm.app_label}.${perm.codename}` : perm.codename;
        permissions.add(permString);
      });
    });

    return permissions;
  }

  /**
   * Django-style string representation
   */
  toString(): string {
    return this.user ? this.user.username : 'AnonymousUser';
  }

  /**
   * Get user object (Django's request.user)
   */
  getUser(): DjangoUser | null {
    return this.user;
  }
}

/**
 * Convert frontend User object to Django-compatible format
 */
export const convertToDjangoUser = (user: User | null): DjangoUser | null => {
  if (!user) return null;

  // Convert permissions array to Django format
  const userPermissions: DjangoPermission[] = [];
  const groups: DjangoGroup[] = [];

  // Process permissions
  if (user.permissions && Array.isArray(user.permissions)) {
    user.permissions.forEach((perm, index) => {
      if (typeof perm === 'object' && perm !== null) {
        userPermissions.push({
          id: index,
          name: perm.name || perm.codename || 'Unknown Permission',
          content_type: perm.content_type || 0,
          codename: perm.codename || 'unknown',
          app_label: perm.app_label
        });
      } else if (typeof perm === 'string') {
        // Handle string permissions
        const parts = perm.split('.');
        userPermissions.push({
          id: index,
          name: perm,
          content_type: 0,
          codename: parts.length > 1 ? parts[1] : perm,
          app_label: parts.length > 1 ? parts[0] : undefined
        });
      }
    });
  }

  // Process groups (if available)
  if (user.role_names && Array.isArray(user.role_names)) {
    user.role_names.forEach((groupName, index) => {
      groups.push({
        id: index,
        name: groupName,
        permissions: [] // Group permissions would be loaded separately
      });
    });
  }

  return {
    id: user.id || 0,
    username: user.username || '',
    email: user.email || '',
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    is_active: user.is_active !== false,
    is_staff: user.is_staff === true,
    is_superuser: user.is_superuser === true,
    date_joined: user.date_joined || new Date().toISOString(),
    last_login: user.last_login || null,
    user_permissions: userPermissions,
    groups: groups
  };
};

/**
 * Create Django authenticator from frontend user
 */
export const createDjangoAuth = (user: User | null): DjangoAuthenticator => {
  const djangoUser = convertToDjangoUser(user);
  return new DjangoAuthenticator(djangoUser);
};

/**
 * Global Django-style authentication functions
 */
export const django = {
  /**
   * Check if user has permission (Django's user.has_perm())
   */
  hasPerm: (user: User | null, permission: string): boolean => {
    return createDjangoAuth(user).hasPerm(permission);
  },

  /**
   * Check if user has multiple permissions (Django's user.has_perms())
   */
  hasPerms: (user: User | null, permissions: string[], requireAll: boolean = true): boolean => {
    return createDjangoAuth(user).hasPerms(permissions, requireAll);
  },

  /**
   * Check if user is staff (Django's user.is_staff)
   */
  isStaff: (user: User | null): boolean => {
    return createDjangoAuth(user).isStaff();
  },

  /**
   * Check if user is superuser (Django's user.is_superuser)
   */
  isSuperuser: (user: User | null): boolean => {
    return createDjangoAuth(user).isSuperuser();
  },

  /**
   * Check if user is authenticated (Django's user.is_authenticated)
   */
  isAuthenticated: (user: User | null): boolean => {
    return createDjangoAuth(user).isAuthenticated();
  },

  /**
   * Check if user is in group
   */
  isInGroup: (user: User | null, groupName: string): boolean => {
    return createDjangoAuth(user).isInGroup(groupName);
  }
};
