import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  ToggleLeft,
  ToggleRight,
  Cog,
  Filter,
  RefreshC<PERSON>,
  Eye,
  EyeOff,

  FileText,
  Building,
  CheckCircle,
  XCircle,
  Settings,
  Pencil,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { serviceTypeAPI, documentTypeAPI } from '@/services/api';
import { cn } from '@/lib/utils';

interface DocumentType {
  id: string;
  name: string;
  is_active: boolean;
}

interface ServiceType {
  id: string;
  name: string;
  description?: string;
  fee: string;
  url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  document_types?: DocumentType[];
  document_types_count: number;
  active_document_types_count: number;
}

interface FormData {
  name: string;
  description: string;
  fee: string;
  url: string;
  is_active: boolean;
  document_type_ids: string[];
}

const ServiceTypeManagement: React.FC = () => {
  const [serviceTypes, setServiceTypes] = useState<ServiceType[]>([]);
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingServiceType, setEditingServiceType] = useState<ServiceType | null>(null);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    fee: '',
    url: '',
    is_active: true,
    document_type_ids: [],
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    description: '',
    fee: '',
    url: '',
    document_type_ids: '',
    general: '',
  });

  // Fetch document types for dropdown
  const fetchDocumentTypes = async () => {
    try {
      const response = await documentTypeAPI.getActiveDocumentTypes();
      setDocumentTypes(response.data);
    } catch (error) {
      console.error('Error fetching document types:', error);
    }
  };

  // Fetch service types
  const fetchServiceTypes = async () => {
    try {
      setLoading(true);
      const params: any = {};
      
      if (searchTerm) {
        params.search = searchTerm;
      }
      
      if (statusFilter !== 'all') {
        params.is_active = statusFilter === 'active';
      }



      const response = await serviceTypeAPI.searchServiceTypes(params);
      setServiceTypes(response.data);
    } catch (error) {
      console.error('Error fetching service types:', error);
      toast.error('Failed to fetch service types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocumentTypes();
  }, []);

  useEffect(() => {
    fetchServiceTypes();
  }, [searchTerm, statusFilter]);

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      fee: '',
      url: '',
      is_active: true,
      document_type_ids: [],
    });
    setFormErrors({
      name: '',
      description: '',
      fee: '',
      url: '',
      document_type_ids: '',
      general: '',
    });
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors = {
      name: '',
      description: '',
      fee: '',
      url: '',
      document_type_ids: '',
      general: '',
    };

    // Validate name
    if (!formData.name.trim()) {
      errors.name = 'Service type name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Service type name must be at least 2 characters long';
    } else if (formData.name.trim().length > 255) {
      errors.name = 'Service type name cannot exceed 255 characters';
    }

    // Check for duplicate names (case-insensitive)
    const existingServiceType = serviceTypes.find(
      st => st.name.toLowerCase() === formData.name.trim().toLowerCase() &&
           st.id !== editingServiceType?.id
    );
    if (existingServiceType) {
      errors.name = 'A service type with this name already exists';
    }

    // Validate fee
    if (!formData.fee.trim()) {
      errors.fee = 'Service fee is required';
    } else {
      const feeValue = parseFloat(formData.fee);
      if (isNaN(feeValue)) {
        errors.fee = 'Service fee must be a valid number';
      } else if (feeValue < 0) {
        errors.fee = 'Service fee cannot be negative';
      } else if (feeValue > 99999999.99) {
        errors.fee = 'Service fee is too large (maximum: 99,999,999.99 ETB)';
      } else if (feeValue.toString().split('.')[1]?.length > 2) {
        errors.fee = 'Service fee can have at most 2 decimal places';
      }
    }

    // Validate URL (optional)
    if (formData.url.trim()) {
      try {
        // Try to create a URL object to validate the URL
        new URL(formData.url.trim());
        if (formData.url.trim().length > 200) {
          errors.url = 'URL cannot exceed 200 characters';
        }
      } catch {
        // If URL constructor fails, check if it's a relative URL or localhost URL
        const urlPattern = /^(https?:\/\/)?(localhost|127\.0\.0\.1|[\da-z\.-]+\.([a-z\.]{2,6}))(:\d+)?(\/[^\s]*)?(\?[^\s]*)?(\#[^\s]*)?$/i;
        if (!urlPattern.test(formData.url.trim())) {
          errors.url = 'Please enter a valid URL (e.g., https://example.com or http://localhost:8080/path?query=value)';
        } else if (formData.url.trim().length > 200) {
          errors.url = 'URL cannot exceed 200 characters';
        }
      }
    }

    // Validate document types (optional but provide helpful feedback)
    if (formData.document_type_ids.length === 0) {
      // This is just a warning, not an error
      console.log('Note: No document types selected. Service will not require specific documents.');
    }

    setFormErrors(errors);
    return !errors.name && !errors.fee && !errors.url && !errors.document_type_ids && !errors.general;
  };

  // Handle create
  const handleCreate = async () => {
    if (!validateForm()) return;

    setSubmitting(true);
    try {
      const cleanData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        fee: parseFloat(formData.fee),
        url: formData.url.trim() || null,
        is_active: formData.is_active,
        document_type_ids: formData.document_type_ids,
      };

      console.log('Creating service type with data:', cleanData);
      console.log('Document type IDs being sent:', formData.document_type_ids);
      console.log('Document type IDs type:', typeof formData.document_type_ids);
      console.log('Document type IDs length:', formData.document_type_ids.length);
      if (formData.document_type_ids.length > 0) {
        console.log('First document type ID:', formData.document_type_ids[0]);
        console.log('First document type ID type:', typeof formData.document_type_ids[0]);
      }

      await serviceTypeAPI.createServiceType(cleanData);

      toast.success('Service type created successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchServiceTypes();
    } catch (error: any) {
      console.error('Error creating service type:', error);
      console.error('Error response data:', error.response?.data);

      if (error.response?.data) {
        const errorData = error.response.data;

        if (errorData.name) {
          setFormErrors(prev => ({ ...prev, name: Array.isArray(errorData.name) ? errorData.name[0] : errorData.name }));
        } else if (errorData.fee) {
          setFormErrors(prev => ({ ...prev, fee: Array.isArray(errorData.fee) ? errorData.fee[0] : errorData.fee }));
        } else if (errorData.document_type_ids) {
          setFormErrors(prev => ({ ...prev, document_type_ids: Array.isArray(errorData.document_type_ids) ? errorData.document_type_ids[0] : errorData.document_type_ids }));
        } else if (errorData.detail) {
          setFormErrors(prev => ({ ...prev, general: errorData.detail }));
        } else {
          // Show all errors if we can't parse them specifically
          const errorMessage = typeof errorData === 'string' ? errorData : JSON.stringify(errorData);
          setFormErrors(prev => ({ ...prev, general: errorMessage }));
          toast.error('Failed to create service type: ' + errorMessage);
        }
      } else {
        toast.error('Failed to create service type: Network error');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit
  const handleEdit = (serviceType: ServiceType) => {
    setEditingServiceType(serviceType);

    // Extract document type IDs from the service type
    const documentTypeIds = serviceType.document_types?.map(dt => dt.id) || [];

    setFormData({
      name: serviceType.name,
      description: serviceType.description || '',
      fee: serviceType.fee,
      url: serviceType.url || '',
      is_active: serviceType.is_active,
      document_type_ids: documentTypeIds,
    });

    // Reset form errors
    setFormErrors({
      name: '',
      description: '',
      fee: '',
      url: '',
      document_type_ids: '',
      general: '',
    });
    setIsEditDialogOpen(true);
  };

  // Handle update
  const handleUpdate = async () => {
    if (!validateForm() || !editingServiceType) return;

    setSubmitting(true);
    try {
      const cleanData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        fee: parseFloat(formData.fee),
        url: formData.url.trim() || null,
        is_active: formData.is_active,
        document_type_ids: formData.document_type_ids,
      };

      await serviceTypeAPI.updateServiceType(editingServiceType.id, cleanData);
      toast.success('Service type updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      setEditingServiceType(null);
      fetchServiceTypes();
    } catch (error: any) {
      console.error('Error updating service type:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (errorData.name) {
          setFormErrors(prev => ({ ...prev, name: errorData.name[0] }));
        } else if (errorData.fee) {
          setFormErrors(prev => ({ ...prev, fee: errorData.fee[0] }));
        } else if (errorData.document_type_ids) {
          setFormErrors(prev => ({ ...prev, document_type_ids: errorData.document_type_ids[0] }));
        } else if (errorData.detail) {
          setFormErrors(prev => ({ ...prev, general: errorData.detail }));
        } else {
          toast.error('Failed to update service type');
        }
      } else {
        toast.error('Failed to update service type');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async (serviceType: ServiceType) => {
    try {
      await serviceTypeAPI.deleteServiceType(serviceType.id);
      toast.success('Service type deleted successfully');
      fetchServiceTypes();
    } catch (error) {
      console.error('Error deleting service type:', error);
      toast.error('Failed to delete service type');
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (serviceType: ServiceType) => {
    try {
      await serviceTypeAPI.toggleServiceTypeStatus(serviceType.id);
      toast.success(`Service type ${serviceType.is_active ? 'deactivated' : 'activated'} successfully`);
      fetchServiceTypes();
    } catch (error) {
      console.error('Error toggling service type status:', error);
      toast.error('Failed to toggle service type status');
    }
  };



  // Filter service types based on search term and status
  const filteredServiceTypes = serviceTypes.filter((serviceType) => {
    const matchesSearch = serviceType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (serviceType.description && serviceType.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'active' && serviceType.is_active) ||
      (statusFilter === 'inactive' && !serviceType.is_active);

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredServiceTypes.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredServiceTypes.length / itemsPerPage);

  // Reset to first page when search term or status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Cog className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Service Type Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete service types with pricing and document requirements
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
                setIsAddDialogOpen(open);
                if (open) {
                  resetForm();
                }
              }}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Service Type
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                        <Plus className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <DialogTitle className="text-xl font-semibold text-gray-900">Add New Service Type</DialogTitle>
                        <DialogDescription className="text-gray-600 mt-1">
                          Create a new service type with pricing and document requirements
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>
                  <div className="p-6 space-y-8">
                    {formErrors.general && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center">
                          <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          <p className="text-sm text-red-700">{formErrors.general}</p>
                        </div>
                      </div>
                    )}

                    {/* Service Information Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Settings className="h-4 w-4 text-blue-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Service Information</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <Label htmlFor="add-name" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Service Name
                          </Label>
                          <Input
                            id="add-name"
                            name="name"
                            placeholder="Academic Verification, Document Processing..."
                            value={formData.name}
                            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                            className={cn(
                              "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                              formErrors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                            )}
                          />
                          {formErrors.name ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.name}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Enter a descriptive name for the service (2-255 characters)</p>
                          )}
                        </div>
                        <div className="space-y-3">
                          <Label htmlFor="add-fee" className="text-sm font-semibold text-gray-700 flex items-center">
                            <span className="text-red-500 mr-1">*</span>
                            Service Fee
                          </Label>
                          <div className="relative">
                            <Input
                              id="add-fee"
                              name="fee"
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="0.00"
                              value={formData.fee}
                              onChange={(e) => setFormData(prev => ({ ...prev, fee: e.target.value }))}
                              className={cn(
                                "h-11 pr-12 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                                formErrors.fee ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                              )}
                            />
                            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm font-medium">ETB</span>
                          </div>
                          {formErrors.fee ? (
                            <p className="text-sm text-red-600 flex items-center">
                              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              {formErrors.fee}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500">Enter the fee amount (maximum 2 decimal places)</p>
                          )}
                        </div>
                      </div>

                      {/* Description Field */}
                      <div className="space-y-3">
                        <Label htmlFor="add-description" className="text-sm font-semibold text-gray-700">
                          Service Description
                        </Label>
                        <Textarea
                          id="add-description"
                          name="description"
                          rows={4}
                          placeholder="Detailed description of the service, its purpose, and what it provides to users..."
                          value={formData.description}
                          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                          className={cn(
                            "border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200 resize-none",
                            formErrors.description ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                          )}
                        />
                        {formErrors.description ? (
                          <p className="text-sm text-red-600 flex items-center">
                            <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            {formErrors.description}
                          </p>
                        ) : (
                          <p className="text-xs text-gray-500">Optional: Provide a detailed description to help users understand this service</p>
                        )}
                      </div>

                      {/* URL Field */}
                      <div className="space-y-3">
                        <Label htmlFor="add-url" className="text-sm font-semibold text-gray-700">
                          Service URL
                        </Label>
                        <Input
                          id="add-url"
                          name="url"
                          type="url"
                          placeholder="http://localhost:8080/alumni-application?form=form1"
                          value={formData.url}
                          onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                          className={cn(
                            "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                            formErrors.url ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                          )}
                        />
                        {formErrors.url ? (
                          <p className="text-sm text-red-600 flex items-center">
                            <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            {formErrors.url}
                          </p>
                        ) : (
                          <p className="text-xs text-gray-500">
                            Optional: Specify form type - use ?form=form1 for complete application or ?form=form2 for simplified application
                          </p>
                        )}
                      </div>
                    </div>
                    {/* Document Types Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <FileText className="h-4 w-4 text-green-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Document Requirements</h3>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-sm font-semibold text-gray-700">
                              Required Document Types
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">
                              Optional: Select document types required for this service
                            </p>
                          </div>
                        {documentTypes && documentTypes.length > 0 && (
                          <div className="flex gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const activeIds = documentTypes.filter(dt => dt.is_active).map(dt => dt.id);
                                setFormData(prev => ({
                                  ...prev,
                                  document_type_ids: activeIds
                                }));
                              }}
                              className="h-7 px-2 text-xs"
                            >
                              Select All Active
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setFormData(prev => ({
                                  ...prev,
                                  document_type_ids: []
                                }));
                              }}
                              className="h-7 px-2 text-xs"
                            >
                              Clear All
                            </Button>
                          </div>
                        )}
                      </div>
                      <div className="max-h-48 overflow-y-auto border rounded-md p-3 bg-gray-50">
                        {documentTypes && documentTypes.length > 0 ? (
                          <div className="grid grid-cols-2 gap-3">
                            {documentTypes.map((docType) => (
                              <div key={docType.id} className="flex items-center space-x-2 p-2 bg-white rounded border">
                                <Checkbox
                                  id={`add-doc-${docType.id}`}
                                  checked={formData.document_type_ids.includes(docType.id)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setFormData(prev => ({
                                        ...prev,
                                        document_type_ids: [...prev.document_type_ids, docType.id]
                                      }));
                                    } else {
                                      setFormData(prev => ({
                                        ...prev,
                                        document_type_ids: prev.document_type_ids.filter(id => id !== docType.id)
                                      }));
                                    }
                                  }}
                                  className="data-[state=checked]:bg-[#1a73c0] data-[state=checked]:border-[#1a73c0]"
                                />
                                <div className="flex-1 min-w-0">
                                  <Label
                                    htmlFor={`add-doc-${docType.id}`}
                                    className="text-sm font-normal cursor-pointer block truncate"
                                    title={docType.name}
                                  >
                                    {docType.name}
                                  </Label>
                                  <Badge
                                    variant={docType.is_active ? "default" : "secondary"}
                                    className={cn(
                                      "text-xs mt-1",
                                      docType.is_active
                                        ? "bg-green-100 text-green-800"
                                        : "bg-gray-100 text-gray-600"
                                    )}
                                  >
                                    {docType.is_active ? "Active" : "Inactive"}
                                  </Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            <FileText className="h-8 w-8 mx-auto mb-2 opacity-30" />
                            <p className="text-sm">No document types available</p>
                            <p className="text-xs text-gray-400">Create document types first to associate with services</p>
                          </div>
                        )}
                      </div>
                      {formData.document_type_ids.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs text-gray-600 mb-2">
                            Selected: {formData.document_type_ids.length} document type{formData.document_type_ids.length !== 1 ? 's' : ''}
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {formData.document_type_ids.map((id) => {
                              const docType = documentTypes && documentTypes.find(dt => dt.id === id);
                              return docType ? (
                                <Badge
                                  key={id}
                                  variant="outline"
                                  className="text-xs bg-[#1a73c0] text-white border-[#1a73c0]"
                                >
                                  {docType.name}
                                </Badge>
                              ) : null;
                            })}
                          </div>
                        </div>
                      )}
                        {formErrors.document_type_ids && (
                          <p className="text-sm text-red-500">{formErrors.document_type_ids}</p>
                        )}
                      </div>
                    </div>

                    {/* Status Section */}
                    <div className="space-y-6">
                      <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <CheckCircle className="h-4 w-4 text-purple-600" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
                      </div>

                      <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <input
                              type="checkbox"
                              id="add-status"
                              checked={formData.is_active}
                              onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                              className="h-5 w-5 rounded border-gray-300 text-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                            />
                            <Label htmlFor="add-status" className="text-base font-semibold text-gray-700 cursor-pointer">
                              Service Type Status
                            </Label>
                          </div>
                          <span className={cn(
                            "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                            formData.is_active
                              ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                              : "bg-gray-100 text-gray-800 border border-gray-200"
                          )}>
                            {formData.is_active ? "✓ Active" : "○ Inactive"}
                          </span>
                        </div>

                        <div className={cn(
                          "p-4 rounded-lg border transition-all duration-200",
                          formData.is_active
                            ? "bg-green-50 border-green-200"
                            : "bg-gray-50 border-gray-200"
                        )}>
                          <div className="flex items-start space-x-3">
                            {formData.is_active ? (
                              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                            ) : (
                              <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                            )}
                            <div>
                              <p className={cn(
                                "text-sm font-medium mb-1",
                                formData.is_active ? "text-green-800" : "text-gray-700"
                              )}>
                                {formData.is_active ? "Service Type is Active" : "Service Type is Inactive"}
                              </p>
                              <p className="text-xs text-gray-600 leading-relaxed">
                                {formData.is_active
                                  ? "This service type will be available for selection and will appear in all relevant lists."
                                  : "This service type will be hidden and will not be available for selection."}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
                    <div className="flex justify-end space-x-3 w-full">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setIsAddDialogOpen(false);
                          resetForm();
                        }}
                        disabled={submitting}
                        className="px-6 py-2.5 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
                      >
                        Cancel
                      </Button>
                      <Button
                        type="button"
                        onClick={handleCreate}
                        disabled={submitting}
                        className="px-6 py-2.5 bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#134a7a] text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                      >
                        {submitting ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Creating Service Type...
                          </>
                        ) : (
                          <>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Service Type
                          </>
                        )}
                      </Button>
                    </div>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Button
                variant="outline"
                onClick={fetchServiceTypes}
                disabled={loading}
                className="border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0] hover:text-white"
              >
                <RefreshCw className={cn("h-4 w-4 mr-2", loading && "animate-spin")} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search & Filter
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search by name or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
                <div className="flex gap-3">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[150px] border-blue-200 focus:ring-blue-400 shadow-sm">
                      <SelectValue placeholder="Filter by Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            {(searchTerm || statusFilter !== 'all') && (
              <div className="p-3 border border-blue-100 rounded-md bg-white shadow-sm">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-sm font-medium text-[#1a73c0] flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                    Active Filters:
                  </span>

                  {searchTerm && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                      Search: {searchTerm}
                      <button
                        onClick={() => setSearchTerm('')}
                        className="ml-2 hover:text-blue-900 transition-colors"
                        aria-label="Remove search filter"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </span>
                  )}

                  {statusFilter !== 'all' && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                      Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                      <button
                        onClick={() => setStatusFilter('all')}
                        className="ml-2 hover:text-blue-900 transition-colors"
                        aria-label="Remove status filter"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </span>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('all');
                    }}
                    className="text-xs h-7 px-3 ml-auto border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Clear All
                  </Button>
                </div>
              </div>
            )}
          </div>

          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[20%] text-[#1a73c0] font-medium">Service Name</TableHead>
                    <TableHead className="w-[12%] text-[#1a73c0] font-medium">Fee</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">URL</TableHead>
                    <TableHead className="w-[20%] text-[#1a73c0] font-medium">Document Types</TableHead>
                    <TableHead className="w-[13%] text-[#1a73c0] font-medium">Status</TableHead>
                    <TableHead className="w-[20%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-blue-100 p-3 rounded-full">
                            <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                          </div>
                          <div className="text-[#1a73c0] font-medium">Loading service types...</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredServiceTypes.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <Cog className="h-8 w-8 text-gray-500" />
                          </div>
                          <div className="text-gray-700 font-medium">No service types found</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {searchTerm || statusFilter !== 'all'
                              ? 'Try adjusting your search criteria to find what you\'re looking for.'
                              : 'There are no service types available. Click the "Add Service Type" button to create one.'}
                          </div>
                          {(searchTerm || statusFilter !== 'all') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSearchTerm('');
                                setStatusFilter('all');
                              }}
                              className="mt-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                            >
                              Clear Filters
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                ) : (
                  currentItems.map((serviceType) => (
                    <TableRow key={serviceType.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium">{serviceType.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className="font-medium">{parseFloat(serviceType.fee).toFixed(2)} ETB</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {serviceType.url ? (
                          <a
                            href={serviceType.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                            title={serviceType.url}
                          >
                            <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            <span className="text-sm truncate max-w-[100px]">Link</span>
                          </a>
                        ) : (
                          <span className="text-gray-400 text-sm">No URL</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {serviceType.document_types_count} total
                          </Badge>
                          {serviceType.active_document_types_count !== serviceType.document_types_count && (
                            <Badge variant="secondary" className="text-xs">
                              {serviceType.active_document_types_count} active
                            </Badge>
                          )}
                        </div>
                        {serviceType.document_types && serviceType.document_types.length > 0 && (
                          <div className="mt-1 text-xs text-gray-500 truncate max-w-[200px]" title={serviceType.document_types.map(dt => dt.name).join(', ')}>
                            {serviceType.document_types.map(dt => dt.name).join(', ')}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={serviceType.is_active ? "default" : "secondary"}
                          className={cn(
                            serviceType.is_active
                              ? "bg-green-100 text-green-800 hover:bg-green-100"
                              : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                          )}
                        >
                          {serviceType.is_active ? (
                            <>
                              <Eye className="h-3 w-3 mr-1" />
                              Active
                            </>
                          ) : (
                            <>
                              <EyeOff className="h-3 w-3 mr-1" />
                              Inactive
                            </>
                          )}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(serviceType)}
                            className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-[#1a73c0]"
                            title="Edit service type"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStatus(serviceType)}
                            className={cn(
                              "h-8 w-8 p-0",
                              serviceType.is_active
                                ? "hover:bg-orange-50 hover:text-orange-600"
                                : "hover:bg-green-50 hover:text-green-600"
                            )}
                            title={serviceType.is_active ? "Deactivate service type" : "Activate service type"}
                          >
                            {serviceType.is_active ? (
                              <ToggleRight className="h-4 w-4" />
                            ) : (
                              <ToggleLeft className="h-4 w-4" />
                            )}
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                                title="Delete service type"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Service Type</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{serviceType.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(serviceType)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>

        <CardFooter>
          {filteredServiceTypes.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredServiceTypes.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredServiceTypes.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Dynamic page number buttons with ellipsis for large page counts */}
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === number
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show a condensed version with ellipsis
                    <>
                      {/* Always show first page */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === 1
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {currentPage > 3 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number =>
                          number > 1 &&
                          number < totalPages &&
                          (
                            number === currentPage - 1 ||
                            number === currentPage ||
                            number === currentPage + 1 ||
                            (currentPage <= 3 && number <= 4) ||
                            (currentPage >= totalPages - 2 && number >= totalPages - 3)
                          )
                        )
                        .map(number => (
                          <Button
                            key={number}
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={cn(
                              "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                              currentPage === number
                                ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                                : "bg-white text-gray-700 hover:bg-blue-50"
                            )}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {currentPage < totalPages - 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === totalPages
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        if (!open) {
          resetForm();
          setEditingServiceType(null);
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Pencil className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">Edit Service Type</DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Update the service type information, pricing, and document requirements
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6 space-y-8">
            {formErrors.general && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  <p className="text-sm text-red-700">{formErrors.general}</p>
                </div>
              </div>
            )}

            {/* Service Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Settings className="h-4 w-4 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Service Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="edit-name" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Service Name
                  </Label>
                  <Input
                    id="edit-name"
                    name="name"
                    placeholder="Academic Verification, Document Processing..."
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.name ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.name}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter a descriptive name for the service (2-255 characters)</p>
                  )}
                </div>
                <div className="space-y-3">
                  <Label htmlFor="edit-fee" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Service Fee
                  </Label>
                  <div className="relative">
                    <Input
                      id="edit-fee"
                      name="fee"
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                      value={formData.fee}
                      onChange={(e) => setFormData(prev => ({ ...prev, fee: e.target.value }))}
                      className={cn(
                        "h-11 pr-12 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.fee ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm font-medium">ETB</span>
                  </div>
                  {formErrors.fee ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {formErrors.fee}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter the fee amount (maximum 2 decimal places)</p>
                  )}
                </div>
              </div>

              {/* Description Field */}
              <div className="space-y-3">
                <Label htmlFor="edit-description" className="text-sm font-semibold text-gray-700">
                  Service Description
                </Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  rows={4}
                  placeholder="Detailed description of the service, its purpose, and what it provides to users..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className={cn(
                    "border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200 resize-none",
                    formErrors.description ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                  )}
                />
                {formErrors.description ? (
                  <p className="text-sm text-red-600 flex items-center">
                    <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {formErrors.description}
                  </p>
                ) : (
                  <p className="text-xs text-gray-500">Optional: Provide a detailed description to help users understand this service</p>
                )}
              </div>

              {/* URL Field */}
              <div className="space-y-3">
                <Label htmlFor="edit-url" className="text-sm font-semibold text-gray-700">
                  Service URL
                </Label>
                <Input
                  id="edit-url"
                  name="url"
                  type="url"
                  placeholder="http://localhost:8080/alumni-application?form=form1"
                  value={formData.url}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  className={cn(
                    "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                    formErrors.url ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                  )}
                />
                {formErrors.url ? (
                  <p className="text-sm text-red-600 flex items-center">
                    <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {formErrors.url}
                  </p>
                ) : (
                  <p className="text-xs text-gray-500">
                    Optional: Specify form type - use ?form=form1 for complete application or ?form=form2 for simplified application
                  </p>
                )}
              </div>
            </div>
            {/* Document Types Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FileText className="h-4 w-4 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Document Requirements</h3>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-semibold text-gray-700">
                      Required Document Types
                    </Label>
                    <p className="text-xs text-gray-500 mt-1">
                      Optional: Select document types required for this service
                    </p>
                  </div>
                {documentTypes && documentTypes.length > 0 && (
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const activeIds = documentTypes.filter(dt => dt.is_active).map(dt => dt.id);
                        setFormData(prev => ({
                          ...prev,
                          document_type_ids: activeIds
                        }));
                      }}
                      className="h-7 px-2 text-xs"
                    >
                      Select All Active
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          document_type_ids: []
                        }));
                      }}
                      className="h-7 px-2 text-xs"
                    >
                      Clear All
                    </Button>
                  </div>
                )}
              </div>
              <div className="max-h-48 overflow-y-auto border rounded-md p-3 bg-gray-50">
                {documentTypes && documentTypes.length > 0 ? (
                  <div className="grid grid-cols-2 gap-3">
                    {documentTypes.map((docType) => (
                      <div key={docType.id} className="flex items-center space-x-2 p-2 bg-white rounded border">
                        <Checkbox
                          id={`edit-doc-${docType.id}`}
                          checked={formData.document_type_ids.includes(docType.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setFormData(prev => ({
                                ...prev,
                                document_type_ids: [...prev.document_type_ids, docType.id]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                document_type_ids: prev.document_type_ids.filter(id => id !== docType.id)
                              }));
                            }
                          }}
                          className="data-[state=checked]:bg-[#1a73c0] data-[state=checked]:border-[#1a73c0]"
                        />
                        <div className="flex-1 min-w-0">
                          <Label
                            htmlFor={`edit-doc-${docType.id}`}
                            className="text-sm font-normal cursor-pointer block truncate"
                            title={docType.name}
                          >
                            {docType.name}
                          </Label>
                          <Badge
                            variant={docType.is_active ? "default" : "secondary"}
                            className={cn(
                              "text-xs mt-1",
                              docType.is_active
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-600"
                            )}
                          >
                            {docType.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-30" />
                    <p className="text-sm">No document types available</p>
                    <p className="text-xs text-gray-400">Create document types first to associate with services</p>
                  </div>
                )}
              </div>
              {formData.document_type_ids.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs text-gray-600 mb-2">
                    Selected: {formData.document_type_ids.length} document type{formData.document_type_ids.length !== 1 ? 's' : ''}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {formData.document_type_ids.map((id) => {
                      const docType = documentTypes && documentTypes.find(dt => dt.id === id);
                      return docType ? (
                        <Badge
                          key={id}
                          variant="outline"
                          className="text-xs bg-[#1a73c0] text-white border-[#1a73c0]"
                        >
                          {docType.name}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
                {formErrors.document_type_ids && (
                  <p className="text-sm text-red-500">{formErrors.document_type_ids}</p>
                )}
              </div>
            </div>

            {/* Status Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="edit-status"
                      checked={formData.is_active}
                      onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                      className="h-5 w-5 rounded border-gray-300 text-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                    />
                    <Label htmlFor="edit-status" className="text-base font-semibold text-gray-700 cursor-pointer">
                      Service Type Status
                    </Label>
                  </div>
                  <span className={cn(
                    "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                    formData.is_active
                      ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                      : "bg-gray-100 text-gray-800 border border-gray-200"
                  )}>
                    {formData.is_active ? "✓ Active" : "○ Inactive"}
                  </span>
                </div>

                <div className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  formData.is_active
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                )}>
                  <div className="flex items-start space-x-3">
                    {formData.is_active ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={cn(
                        "text-sm font-medium mb-1",
                        formData.is_active ? "text-green-800" : "text-gray-700"
                      )}>
                        {formData.is_active ? "Service Type is Active" : "Service Type is Inactive"}
                      </p>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {formData.is_active
                          ? "This service type will be available for selection and will appear in all relevant lists."
                          : "This service type will be hidden and will not be available for selection."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex justify-end space-x-3 w-full">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsEditDialogOpen(false);
                  resetForm();
                  setEditingServiceType(null);
                }}
                disabled={submitting}
                className="px-6 py-2.5 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleUpdate}
                disabled={submitting}
                className="px-6 py-2.5 bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#134a7a] text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                {submitting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Updating Service Type...
                  </>
                ) : (
                  <>
                    <Pencil className="h-4 w-4 mr-2" />
                    Update Service Type
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ServiceTypeManagement;
