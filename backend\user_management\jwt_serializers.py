from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
import logging

logger = logging.getLogger(__name__)

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    username_field = 'username'

    def validate(self, attrs):
        # Get the username and password from the request
        username = attrs.get('username')
        password = attrs.get('password')

        logger.info(f"Login attempt with username: {username}")

        if not username or not password:
            from rest_framework import serializers
            logger.warning("Missing username or password")
            raise serializers.ValidationError(
                'Must include "username" and "password".'
            )

        # First try to authenticate with username
        user = authenticate(request=self.context.get('request'),
                            username=username, password=password)

        # If that fails, try to find a user with this email and authenticate with their username
        if not user:
            logger.warning(f"Authentication failed with username: {username}")
            try:
                # Try to find a user with this email
                user_obj = User.objects.filter(email=username).first()
                if user_obj:
                    logger.info(f"Found user with email {username}, trying with username: {user_obj.username}")
                    # Try to authenticate with the username
                    user = authenticate(request=self.context.get('request'),
                                        username=user_obj.username, password=password)
            except Exception as e:
                logger.error(f"Error looking up user by email: {str(e)}")

        # If authentication still fails, raise an error
        if not user:
            from rest_framework import serializers
            logger.warning("Authentication failed completely")
            raise serializers.ValidationError(
                'No active account found with the given credentials'
            )

        # Check if the user is active
        if not user.is_active:
            from rest_framework import serializers
            logger.warning(f"User {user.username} is inactive")
            raise serializers.ValidationError(
                'User account is disabled.'
            )

        logger.info(f"Authentication successful for user: {user.username}")

        # Update last_login timestamp
        from django.utils import timezone
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])
        logger.info(f"Updated last_login timestamp for user: {user.username}")

        # If authentication is successful, proceed with token generation
        data = {}
        refresh = self.get_token(user)

        data['refresh'] = str(refresh)
        data['access'] = str(refresh.access_token)

        return data

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer
