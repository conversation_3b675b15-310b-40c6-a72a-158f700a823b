import axios from 'axios';

/**
 * Utility function to reset a database sequence for a specific table
 * This is useful when you encounter "duplicate key value violates unique constraint" errors
 * 
 * @param tableName The name of the table whose sequence needs to be reset
 * @param token The authentication token
 * @returns A promise that resolves when the sequence is reset
 */
export const resetDatabaseSequence = async (tableName: string, token: string): Promise<boolean> => {
  try {
    // Call a special endpoint to reset the sequence
    const response = await axios.post(
      'http://localhost:8000/api/admin/reset-sequence/',
      { table_name: tableName },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log(`Database sequence for ${tableName} reset successfully:`, response.data);
    return true;
  } catch (error) {
    console.error(`Failed to reset database sequence for ${tableName}:`, error);
    return false;
  }
};

/**
 * Utility function to check if a database sequence is out of sync
 * 
 * @param tableName The name of the table to check
 * @param token The authentication token
 * @returns A promise that resolves to true if the sequence is out of sync
 */
export const checkDatabaseSequence = async (tableName: string, token: string): Promise<boolean> => {
  try {
    // Call a special endpoint to check the sequence
    const response = await axios.get(
      `http://localhost:8000/api/admin/check-sequence/?table_name=${tableName}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log(`Database sequence check for ${tableName}:`, response.data);
    return response.data.is_out_of_sync || false;
  } catch (error) {
    console.error(`Failed to check database sequence for ${tableName}:`, error);
    return false;
  }
};

/**
 * Utility function to get the next available ID for a table
 * This is useful when you want to manually set an ID that won't conflict
 * 
 * @param tableName The name of the table
 * @param token The authentication token
 * @returns A promise that resolves to the next available ID
 */
export const getNextAvailableId = async (tableName: string, token: string): Promise<number> => {
  try {
    // Call a special endpoint to get the next available ID
    const response = await axios.get(
      `http://localhost:8000/api/admin/next-id/?table_name=${tableName}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log(`Next available ID for ${tableName}:`, response.data);
    return response.data.next_id || 1;
  } catch (error) {
    console.error(`Failed to get next available ID for ${tableName}:`, error);
    // Return a very high number as a fallback
    return 100000;
  }
};
