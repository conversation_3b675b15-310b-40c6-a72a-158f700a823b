/**
 * High-performance form validation hook with debouncing and memoization
 */
import { useState, useCallback, useMemo, useRef } from 'react';

// Custom debounce function to avoid external dependencies
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any, formData: any) => string | null;
  email?: boolean;
  phone?: boolean;
  year?: { min?: number; max?: number };
}

export interface ValidationRules {
  [fieldName: string]: ValidationRule;
}

export interface ValidationErrors {
  [fieldName: string]: string;
}

// Predefined validation patterns
const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^(\+251|0)?[79]\d{8}$/,
  year: /^\d{4}$/,
  name: /^[a-zA-Z\s'-]+$/,
  studentId: /^[a-zA-Z0-9/-]+$/
};

// Common validation functions
const validateRequired = (value: any): string | null => {
  if (value === null || value === undefined || value === '') {
    return 'This field is required';
  }
  if (typeof value === 'string' && value.trim() === '') {
    return 'This field is required';
  }
  return null;
};

const validateMinLength = (value: string, minLength: number): string | null => {
  if (value && value.length < minLength) {
    return `Must be at least ${minLength} characters`;
  }
  return null;
};

const validateMaxLength = (value: string, maxLength: number): string | null => {
  if (value && value.length > maxLength) {
    return `Must be no more than ${maxLength} characters`;
  }
  return null;
};

const validatePattern = (value: string, pattern: RegExp, message: string): string | null => {
  if (value && !pattern.test(value)) {
    return message;
  }
  return null;
};

const validateEmail = (value: string): string | null => {
  return validatePattern(value, VALIDATION_PATTERNS.email, 'Please enter a valid email address');
};

const validatePhone = (value: string): string | null => {
  return validatePattern(value, VALIDATION_PATTERNS.phone, 'Please enter a valid Ethiopian phone number');
};

const validateYear = (value: string | number, min?: number, max?: number): string | null => {
  const year = typeof value === 'string' ? parseInt(value) : value;
  
  if (isNaN(year)) {
    return 'Please enter a valid year';
  }
  
  if (min && year < min) {
    return `Year must be ${min} or later`;
  }
  
  if (max && year > max) {
    return `Year must be ${max} or earlier`;
  }
  
  return null;
};

// Sanitization functions
const sanitizeInput = (value: string): string => {
  if (typeof value !== 'string') return value;
  
  // Remove potentially dangerous characters
  return value
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
};

export const useFormValidation = (
  initialData: any = {},
  validationRules: ValidationRules = {},
  options: {
    debounceMs?: number;
    validateOnChange?: boolean;
    sanitizeInputs?: boolean;
  } = {}
) => {
  const {
    debounceMs = 300,
    validateOnChange = true,
    sanitizeInputs = true
  } = options;

  const [formData, setFormData] = useState(initialData);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const [isValidating, setIsValidating] = useState(false);

  // Refs for performance optimization
  const validationTimeoutRef = useRef<{ [key: string]: NodeJS.Timeout }>({});
  const lastValidationRef = useRef<{ [key: string]: any }>({});

  // Validate a single field
  const validateField = useCallback((fieldName: string, value: any, currentFormData: any): string | null => {
    const rule = validationRules[fieldName];
    if (!rule) return null;

    // Required validation
    if (rule.required) {
      const requiredError = validateRequired(value);
      if (requiredError) return requiredError;
    }

    // Skip other validations if field is empty and not required
    if (!rule.required && (value === '' || value === null || value === undefined)) {
      return null;
    }

    const stringValue = String(value);

    // Length validations
    if (rule.minLength) {
      const minLengthError = validateMinLength(stringValue, rule.minLength);
      if (minLengthError) return minLengthError;
    }

    if (rule.maxLength) {
      const maxLengthError = validateMaxLength(stringValue, rule.maxLength);
      if (maxLengthError) return maxLengthError;
    }

    // Pattern validation
    if (rule.pattern) {
      const patternError = validatePattern(stringValue, rule.pattern, 'Invalid format');
      if (patternError) return patternError;
    }

    // Email validation
    if (rule.email) {
      const emailError = validateEmail(stringValue);
      if (emailError) return emailError;
    }

    // Phone validation
    if (rule.phone) {
      const phoneError = validatePhone(stringValue);
      if (phoneError) return phoneError;
    }

    // Year validation
    if (rule.year) {
      const yearError = validateYear(value, rule.year.min, rule.year.max);
      if (yearError) return yearError;
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value, currentFormData);
      if (customError) return customError;
    }

    return null;
  }, [validationRules]);

  // Debounced validation function
  const debouncedValidateField = useCallback(
    debounce((fieldName: string, value: any, currentFormData: any) => {
      const error = validateField(fieldName, value, currentFormData);
      setErrors(prev => ({
        ...prev,
        [fieldName]: error || ''
      }));
      setIsValidating(false);
    }, debounceMs),
    [validateField, debounceMs]
  );

  // Validate all fields
  const validateAllFields = useCallback((data: any = formData): ValidationErrors => {
    const newErrors: ValidationErrors = {};
    
    Object.keys(validationRules).forEach(fieldName => {
      const error = validateField(fieldName, data[fieldName], data);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    return newErrors;
  }, [formData, validationRules, validateField]);

  // Update form data with validation
  const updateField = useCallback((fieldName: string, value: any) => {
    // Sanitize input if enabled
    const sanitizedValue = sanitizeInputs && typeof value === 'string' 
      ? sanitizeInput(value) 
      : value;

    // Update form data
    setFormData(prev => {
      const newData = { ...prev, [fieldName]: sanitizedValue };
      
      // Validate on change if enabled
      if (validateOnChange && validationRules[fieldName]) {
        // Clear previous timeout
        if (validationTimeoutRef.current[fieldName]) {
          clearTimeout(validationTimeoutRef.current[fieldName]);
        }

        // Only validate if value has changed
        if (lastValidationRef.current[fieldName] !== sanitizedValue) {
          setIsValidating(true);
          lastValidationRef.current[fieldName] = sanitizedValue;
          debouncedValidateField(fieldName, sanitizedValue, newData);
        }
      }
      
      return newData;
    });

    // Mark field as touched
    setTouched(prev => ({ ...prev, [fieldName]: true }));
  }, [sanitizeInputs, validateOnChange, validationRules, debouncedValidateField]);

  // Batch update multiple fields
  const updateFields = useCallback((updates: { [key: string]: any }) => {
    setFormData(prev => {
      const newData = { ...prev };
      
      Object.entries(updates).forEach(([fieldName, value]) => {
        const sanitizedValue = sanitizeInputs && typeof value === 'string' 
          ? sanitizeInput(value) 
          : value;
        newData[fieldName] = sanitizedValue;
      });

      return newData;
    });

    // Mark all updated fields as touched
    setTouched(prev => ({
      ...prev,
      ...Object.keys(updates).reduce((acc, key) => ({ ...acc, [key]: true }), {})
    }));
  }, [sanitizeInputs]);

  // Validate form and return validation result
  const validateForm = useCallback((): { isValid: boolean; errors: ValidationErrors } => {
    const newErrors = validateAllFields();
    setErrors(newErrors);
    
    // Mark all fields as touched
    const allFields = Object.keys(validationRules);
    setTouched(prev => ({
      ...prev,
      ...allFields.reduce((acc, key) => ({ ...acc, [key]: true }), {})
    }));

    return {
      isValid: Object.keys(newErrors).length === 0,
      errors: newErrors
    };
  }, [validateAllFields, validationRules]);

  // Reset form
  const resetForm = useCallback((newData: any = initialData) => {
    setFormData(newData);
    setErrors({});
    setTouched({});
    setIsValidating(false);
    
    // Clear validation timeouts
    Object.values(validationTimeoutRef.current).forEach(timeout => {
      clearTimeout(timeout);
    });
    validationTimeoutRef.current = {};
    lastValidationRef.current = {};
  }, [initialData]);

  // Clear errors for specific fields
  const clearErrors = useCallback((fieldNames?: string[]) => {
    if (fieldNames) {
      setErrors(prev => {
        const newErrors = { ...prev };
        fieldNames.forEach(fieldName => {
          delete newErrors[fieldName];
        });
        return newErrors;
      });
    } else {
      setErrors({});
    }
  }, []);

  // Memoized computed values
  const isValid = useMemo(() => {
    return Object.values(errors).every(error => !error);
  }, [errors]);

  const hasErrors = useMemo(() => {
    return Object.values(errors).some(error => error !== '');
  }, [errors]);

  const touchedFields = useMemo(() => {
    return Object.keys(touched).filter(key => touched[key]);
  }, [touched]);

  const isDirty = useMemo(() => {
    return JSON.stringify(formData) !== JSON.stringify(initialData);
  }, [formData, initialData]);

  return {
    // Form state
    formData,
    errors,
    touched,
    isValidating,
    
    // Computed values
    isValid,
    hasErrors,
    touchedFields,
    isDirty,
    
    // Actions
    updateField,
    updateFields,
    validateForm,
    validateField: (fieldName: string, value?: any) => {
      const valueToValidate = value !== undefined ? value : formData[fieldName];
      return validateField(fieldName, valueToValidate, formData);
    },
    resetForm,
    clearErrors,
    
    // Utilities
    setFormData,
    setErrors,
    setTouched
  };
};
