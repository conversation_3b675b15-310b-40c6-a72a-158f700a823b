from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.shortcuts import get_object_or_404

from .models import ServiceType
from .serializers import ServiceTypeSerializer, ServiceTypeListSerializer
from .filters import ServiceTypeFilter


class ServiceTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing service types.
    
    Provides CRUD operations for service types with filtering, searching,
    and pagination capabilities.
    """
    
    queryset = ServiceType.objects.all().prefetch_related('document_types')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceTypeFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'fee', 'created_at', 'updated_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return ServiceTypeListSerializer
        return ServiceTypeSerializer

    def get_queryset(self):
        """Get queryset with optional filtering."""
        queryset = super().get_queryset()
        
        # Additional filtering based on query parameters
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            if is_active.lower() in ['true', '1']:
                queryset = queryset.filter(is_active=True)
            elif is_active.lower() in ['false', '0']:
                queryset = queryset.filter(is_active=False)
        
        # Filter by document types
        document_type_ids = self.request.query_params.getlist('document_types')
        if document_type_ids:
            queryset = queryset.filter(document_types__id__in=document_type_ids).distinct()
        
        return queryset

    def list(self, request, *args, **kwargs):
        """List all service types with optional filtering."""
        queryset = self.filter_queryset(self.get_queryset())
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """Create a new service type."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        service_type = serializer.save()
        
        # Return detailed serializer for response
        response_serializer = ServiceTypeSerializer(service_type)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific service type."""
        instance = self.get_object()
        serializer = ServiceTypeSerializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update a service type."""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        service_type = serializer.save()

        # Return detailed serializer for response
        response_serializer = ServiceTypeSerializer(service_type)
        return Response(response_serializer.data)

    def destroy(self, request, *args, **kwargs):
        """Delete a service type."""
        instance = self.get_object()
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """Toggle the active status of a service type."""
        service_type = get_object_or_404(ServiceType, pk=pk)
        service_type.is_active = not service_type.is_active
        service_type.save(update_fields=['is_active', 'updated_at'])
        
        serializer = ServiceTypeSerializer(service_type)
        return Response({
            'message': f'Service type {"activated" if service_type.is_active else "deactivated"} successfully.',
            'service_type': serializer.data
        })

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get only active service types."""
        queryset = self.get_queryset().filter(is_active=True)
        queryset = self.filter_queryset(queryset)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search service types by name."""
        query = request.query_params.get('q', '').strip()
        if not query:
            return Response({'results': []})

        queryset = self.get_queryset().filter(
            Q(name__icontains=query) | Q(description__icontains=query)
        )
        
        # Apply additional filters
        queryset = self.filter_queryset(queryset)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response({'results': serializer.data})

    @action(detail=False, methods=['get'], permission_classes=[AllowAny])
    def public(self, request):
        """Get active service types for public access (no authentication required)."""
        queryset = self.get_queryset().filter(is_active=True)

        # Apply search if provided
        search_query = request.query_params.get('search', '').strip()
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) | Q(description__icontains=search_query)
            )

        # Order by name
        queryset = queryset.order_by('name')

        serializer = ServiceTypeListSerializer(queryset, many=True)
        return Response(serializer.data)
