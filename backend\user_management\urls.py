from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .csrf_views import get_csrf_token, CSRFTokenView, csrf_token_simple

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'users', views.UserViewSet, basename='user-management')
router.register(r'staff', views.StaffViewSet, basename='staff-management')
router.register(r'roles', views.GroupViewSet, basename='role-management')  # Keep for backward compatibility
router.register(r'permissions', views.PermissionViewSet, basename='permission-management')
router.register(r'content-types', views.ContentTypeViewSet, basename='content-type-management')

# New RBAC endpoints
router.register(r'profiles', views.UserProfileViewSet, basename='user-profile-management')
router.register(r'rbac-roles', views.RoleViewSet, basename='rbac-role-management')
router.register(r'permission-categories', views.PermissionCategoryViewSet, basename='permission-category-management')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),

    # RBAC endpoints
    path('rbac/', views.RBACUtilityView.as_view(), name='rbac-utility'),
    path('rbac-info/', views.RBACInfoView.as_view(), name='rbac-info'),
    path('permission-check/', views.PermissionCheckView.as_view(), name='permission-check'),
    path('role-validation/', views.RoleValidationView.as_view(), name='role-validation'),
    path('check-user/', views.CheckUserView.as_view(), name='check-user'),

    # CSRF token endpoints
    path('csrf/', get_csrf_token, name='csrf-token'),
    path('csrf-token/', CSRFTokenView.as_view(), name='csrf-token-view'),
    path('csrf-simple/', csrf_token_simple, name='csrf-token-simple'),
]
