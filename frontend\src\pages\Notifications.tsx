
import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Bell, CheckCheck, MailOpen, AlertCircle, Info, CheckCircle } from 'lucide-react';
import notificationService, { Notification } from '@/services/notificationService';

const Notifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');

  useEffect(() => {
    // Get notifications from the notification service
    const storedNotifications = notificationService.getNotifications();
    setNotifications(storedNotifications);
  }, []);

  const getFilteredNotifications = () => {
    switch (filter) {
      case 'unread':
        return notifications.filter(n => !n.read);
      case 'read':
        return notifications.filter(n => n.read);
      default:
        return notifications;
    }
  };

  const markAsRead = (id: string) => {
    notificationService.markAsRead(id);
    // Update the local state to reflect the change
    setNotifications(prev => prev.map(notification =>
      notification.id === id ? { ...notification, read: true } : notification
    ));
  };

  const markAllAsRead = () => {
    notificationService.markAllAsRead();
    // Update the local state to reflect the change
    setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-amber-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getNotificationClass = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-300 bg-green-50';
      case 'warning':
        return 'border-amber-300 bg-amber-50';
      case 'error':
        return 'border-red-300 bg-red-50';
      case 'info':
      default:
        return 'border-blue-300 bg-blue-50';
    }
  };

  const filteredNotifications = getFilteredNotifications();

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 space-y-4 md:space-y-0">
          <h1 className="text-2xl font-bold">Notifications</h1>

          <div className="flex items-center space-x-4">
            <div className="flex border rounded-lg overflow-hidden">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className={`${filter === 'all' ? 'bg-gondar text-white' : 'bg-white text-gray-700'} rounded-none border-r`}
                onClick={() => setFilter('all')}
              >
                All
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className={`${filter === 'unread' ? 'bg-gondar text-white' : 'bg-white text-gray-700'} rounded-none border-r`}
                onClick={() => setFilter('unread')}
              >
                Unread
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className={`${filter === 'read' ? 'bg-gondar text-white' : 'bg-white text-gray-700'} rounded-none`}
                onClick={() => setFilter('read')}
              >
                Read
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={markAllAsRead}
            >
              <CheckCheck className="h-4 w-4" />
              <span>Mark all as read</span>
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              <span>Your Notifications</span>
            </CardTitle>
            <CardDescription>
              {filter === 'all'
                ? 'All notifications'
                : filter === 'unread'
                  ? 'Unread notifications'
                  : 'Read notifications'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredNotifications.length > 0 ? (
              <div className="space-y-4">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`relative p-4 border rounded-lg ${notification.read ? 'border-gray-200 bg-white' : `${getNotificationClass(notification.type)} border-l-4`}`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-grow">
                        <h3 className={`font-medium ${notification.read ? 'text-gray-800' : 'text-gray-900'}`}>
                          {notification.title}
                        </h3>
                        <p className={`text-sm ${notification.read ? 'text-gray-500' : 'text-gray-700'}`}>
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(notification.date).toLocaleDateString()} at {new Date(notification.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </p>
                      </div>
                      {!notification.read && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="flex-shrink-0 text-gray-500 hover:text-gray-900"
                          onClick={() => markAsRead(notification.id)}
                        >
                          <MailOpen className="h-4 w-4" />
                          <span className="sr-only">Mark as read</span>
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                  <Bell className="h-6 w-6 text-gray-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">No notifications</h3>
                <p className="text-gray-600">
                  {filter === 'all'
                    ? "You don't have any notifications yet"
                    : filter === 'unread'
                      ? "You don't have any unread notifications"
                      : "You don't have any read notifications"}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Notifications;
