from rest_framework import serializers
from django.contrib.auth.models import User
from .models import ApplicantInformation, ApplicantGAT, ApplicantProgramSelection, ApplicantDocumentation
from setups.application_information.models import ApplicationInformation
from setups.college.models import College
from setups.department.models import Department
from setups.program.models import Program

class UserBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'date_joined', 'last_login']

class CollegeSerializer(serializers.ModelSerializer):
    class Meta:
        model = College
        fields = ['id', 'name']

class DepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['id', 'name']

class ProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = Program
        fields = ['id', 'program_name']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # For backward compatibility, map program_name to name
        representation['name'] = representation.pop('program_name')
        return representation

class ApplicationInfoSerializer(serializers.ModelSerializer):
    college = CollegeSerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    program = ProgramSerializer(read_only=True)
    field_of_study = serializers.SerializerMethodField()

    class Meta:
        model = ApplicationInformation
        fields = ['id', 'college', 'department', 'program', 'field_of_study', 'status']

    def get_field_of_study(self, obj):
        if obj.field_of_study:
            return {
                'id': obj.field_of_study.id,
                'name': obj.field_of_study.field_of_study
            }
        return None

class ApplicantInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = ApplicantInformation
        fields = ['id', 'grandfather_name', 'gender', 'dob', 'mobile', 'program_level',
                 'ug_university', 'ug_field_of_study', 'ug_CGPA',
                 'pg_university', 'pg_field_of_study', 'pg_CGPA']

class ApplicantGATSerializer(serializers.ModelSerializer):
    gat_no = serializers.CharField(source='GAT_No', read_only=True)

    class Meta:
        model = ApplicantGAT
        fields = ['id', 'GAT_No', 'GAT_Result', 'gat_no']

class ApplicantListSerializer(serializers.ModelSerializer):
    user = UserBasicSerializer(read_only=True)
    application_info = ApplicationInfoSerializer(read_only=True)
    applicant_info = serializers.SerializerMethodField()
    gat = ApplicantGATSerializer(read_only=True)
    year_name = serializers.SerializerMethodField()
    term_name = serializers.SerializerMethodField()

    def get_year_name(self, obj):
        return obj.year.year if obj.year else None

    def get_term_name(self, obj):
        return obj.term.name if obj.term else None

    class Meta:
        model = ApplicantProgramSelection
        fields = [
            'id', 'user', 'application_num', 'transaction_id',
            'sponsorship', 'application_info', 'applicant_info',
            'registrar_off_status', 'department_status', 'payment_status',
            'gat', 'year', 'year_name', 'term', 'term_name', 'created_at', 'updated_at'
        ]

    def get_applicant_info(self, obj):
        try:
            applicant_info = ApplicantInformation.objects.get(author=obj.user)
            return ApplicantInfoSerializer(applicant_info).data
        except ApplicantInformation.DoesNotExist:
            return None

class ApplicantDetailSerializer(serializers.ModelSerializer):
    user = UserBasicSerializer(read_only=True)
    application_info = ApplicationInfoSerializer(read_only=True)
    applicant_info = serializers.SerializerMethodField()
    gat = ApplicantGATSerializer(read_only=True)
    has_documents = serializers.SerializerMethodField()
    documents = serializers.SerializerMethodField()
    reg_approved_by = UserBasicSerializer(read_only=True)
    dep_approved_by = UserBasicSerializer(read_only=True)

    class Meta:
        model = ApplicantProgramSelection
        fields = [
            'id', 'user', 'application_num', 'transaction_id',
            'sponsorship', 'application_info', 'applicant_info',
            'registrar_off_status', 'department_status', 'payment_status',
            'gat', 'remark', 'has_documents', 'documents',
            'reg_approved_by', 'dep_approved_by', 'year',
            'created_at', 'updated_at'
        ]

    def get_applicant_info(self, obj):
        try:
            applicant_info = ApplicantInformation.objects.get(author=obj.user)
            return ApplicantInfoSerializer(applicant_info).data
        except ApplicantInformation.DoesNotExist:
            return None

    def get_has_documents(self, obj):
        try:
            docs = ApplicantDocumentation.objects.filter(user=obj.user).first()
            if docs:
                # Check if any document field has a value
                return any([
                    bool(docs.degree),
                    bool(docs.sponsorship),
                    bool(docs.student_copy),
                    bool(docs.recommendation),
                    bool(docs.publication),
                    bool(docs.conceptnote),
                    bool(docs.grade_12_certificate),
                    bool(docs.grade_9_12_transcript)
                ])
            return False
        except Exception as e:
            print(f"Error checking documents: {str(e)}")
            return False

    def get_documents(self, obj):
        try:
            docs = ApplicantDocumentation.objects.filter(user=obj.user).first()
            if docs:
                result = {}
                # Only include fields that actually have files
                if docs.degree:
                    result['degree'] = docs.degree.url
                if docs.sponsorship:
                    result['sponsorship'] = docs.sponsorship.url
                if docs.student_copy:
                    result['student_copy'] = docs.student_copy.url
                if docs.recommendation:
                    result['recommendation'] = docs.recommendation.url
                if docs.publication:
                    result['publication'] = docs.publication.url
                if docs.conceptnote:
                    result['conceptnote'] = docs.conceptnote.url
                if docs.grade_12_certificate:
                    result['grade_12_certificate'] = docs.grade_12_certificate.url
                if docs.grade_9_12_transcript:
                    result['grade_9_12_transcript'] = docs.grade_9_12_transcript.url

                return result if result else None
            return None
        except Exception as e:
            print(f"Error getting documents: {str(e)}")
            return None
