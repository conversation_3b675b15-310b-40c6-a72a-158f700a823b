# Generated by Django 5.2.1 on 2025-05-31 11:07

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Year',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('year', models.Char<PERSON>ield(help_text='Year', max_length=10, unique=True, verbose_name='Year')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Year',
                'verbose_name_plural': 'Years',
                'ordering': ['-year'],
            },
        ),
    ]
