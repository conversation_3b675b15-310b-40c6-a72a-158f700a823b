import React, { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { AlertTriangle, Lock, Eye, EyeOff } from 'lucide-react';

interface PermissionGateProps {
  children: ReactNode;
  // Permission requirements
  permissions?: string[];
  groups?: string[];
  requireAll?: boolean; // If true, user must have ALL permissions/groups. If false, ANY will do
  
  // User status requirements
  requireAuth?: boolean;
  requireActive?: boolean;
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  
  // Behavior options
  fallback?: ReactNode; // What to show when access is denied
  hideWhenDenied?: boolean; // If true, renders nothing when denied
  showPlaceholder?: boolean; // If true, shows a placeholder when denied
  placeholderText?: string;
  
  // Feature-level control
  feature?: string; // Feature identifier for logging/analytics
  logAccess?: boolean; // Whether to log access attempts
  
  // Custom validation
  customCheck?: (user: any) => boolean;
}

/**
 * PermissionGate Component
 * Dynamically controls access to UI components based on user permissions
 */
export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permissions = [],
  groups = [],
  requireAll = false,
  requireAuth = true,
  requireActive = true,
  requireStaff = false,
  requireSuperuser = false,
  fallback,
  hideWhenDenied = false,
  showPlaceholder = false,
  placeholderText = "Access restricted",
  feature,
  logAccess = false,
  customCheck
}) => {
  const { user, isAuthenticated, hasPermission, hasRole, hasAnyRole } = useAuth();

  // Check authentication
  if (requireAuth && !isAuthenticated) {
    if (logAccess) console.log(`🔒 PermissionGate: Authentication required for feature ${feature}`);
    return renderDenied('Authentication required');
  }

  // Check if user exists
  if (requireAuth && !user) {
    if (logAccess) console.log(`🔒 PermissionGate: User object missing for feature ${feature}`);
    return renderDenied('User not found');
  }

  // Check active status
  if (requireActive && user && !user.is_active) {
    if (logAccess) console.log(`🔒 PermissionGate: Inactive user for feature ${feature}`);
    return renderDenied('Account inactive');
  }

  // Check staff requirement
  if (requireStaff && user && !user.is_staff) {
    if (logAccess) console.log(`🔒 PermissionGate: Staff status required for feature ${feature}`);
    return renderDenied('Staff access required');
  }

  // Check superuser requirement
  if (requireSuperuser && user && !user.is_superuser) {
    if (logAccess) console.log(`🔒 PermissionGate: Superuser status required for feature ${feature}`);
    return renderDenied('Superuser access required');
  }

  // Superuser bypass (unless custom check is provided)
  if (user?.is_superuser && !customCheck) {
    if (logAccess) console.log(`✅ PermissionGate: Superuser access granted for feature ${feature}`);
    return <>{children}</>;
  }

  // Custom validation check
  if (customCheck && !customCheck(user)) {
    if (logAccess) console.log(`🔒 PermissionGate: Custom check failed for feature ${feature}`);
    return renderDenied('Custom validation failed');
  }

  // Check permissions
  if (permissions.length > 0) {
    const permissionCheck = requireAll 
      ? permissions.every(permission => hasPermission(permission))
      : permissions.some(permission => hasPermission(permission));
    
    if (!permissionCheck) {
      if (logAccess) {
        console.log(`🔒 PermissionGate: Permission check failed for feature ${feature}`, {
          required: permissions,
          requireAll,
          userPermissions: user?.permissions
        });
      }
      return renderDenied(`Missing permissions: ${permissions.join(', ')}`);
    }
  }

  // Check groups/roles
  if (groups.length > 0) {
    const groupCheck = requireAll
      ? groups.every(group => hasRole(group))
      : hasAnyRole(groups);
    
    if (!groupCheck) {
      if (logAccess) {
        console.log(`🔒 PermissionGate: Group check failed for feature ${feature}`, {
          required: groups,
          requireAll,
          userGroups: user?.role_names
        });
      }
      return renderDenied(`Missing groups: ${groups.join(', ')}`);
    }
  }

  // All checks passed
  if (logAccess) console.log(`✅ PermissionGate: Access granted for feature ${feature}`);
  return <>{children}</>;

  // Helper function to render denied state
  function renderDenied(reason: string) {
    if (hideWhenDenied) {
      return null;
    }

    if (fallback) {
      return <>{fallback}</>;
    }

    if (showPlaceholder) {
      return (
        <div className="flex items-center gap-2 p-3 bg-gray-50 border border-gray-200 rounded text-sm text-gray-600">
          <EyeOff className="h-4 w-4" />
          <span>{placeholderText}</span>
        </div>
      );
    }

    return null;
  }
};

// Convenience components for common use cases
export const AuthRequired: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate requireAuth fallback={fallback}>
    {children}
  </PermissionGate>
);

export const StaffOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate requireStaff fallback={fallback}>
    {children}
  </PermissionGate>
);

export const SuperuserOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate requireSuperuser fallback={fallback}>
    {children}
  </PermissionGate>
);

export const HasPermission: React.FC<{ 
  children: ReactNode; 
  permissions: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}> = ({ children, permissions, requireAll = false, fallback }) => (
  <PermissionGate permissions={permissions} requireAll={requireAll} fallback={fallback}>
    {children}
  </PermissionGate>
);

export const HasRole: React.FC<{ 
  children: ReactNode; 
  roles: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}> = ({ children, roles, requireAll = false, fallback }) => (
  <PermissionGate groups={roles} requireAll={requireAll} fallback={fallback}>
    {children}
  </PermissionGate>
);

export const FeatureFlag: React.FC<{ 
  children: ReactNode; 
  feature: string;
  permissions?: string[];
  groups?: string[];
  customCheck?: (user: any) => boolean;
  fallback?: ReactNode;
}> = ({ children, feature, permissions, groups, customCheck, fallback }) => (
  <PermissionGate 
    permissions={permissions}
    groups={groups}
    customCheck={customCheck}
    feature={feature}
    logAccess
    fallback={fallback}
  >
    {children}
  </PermissionGate>
);

export default PermissionGate;
