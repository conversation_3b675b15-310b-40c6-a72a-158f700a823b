import React, { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { createPermissionChecker } from '@/utils/permissionChecker';
import { AlertTriangle, Lock, Eye, EyeOff } from 'lucide-react';

interface PermissionGateProps {
  children: ReactNode;
  // Permission requirements (ONLY permissions, groups are deprecated)
  permissions?: string[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, ANY will do

  // User status requirements
  requireAuth?: boolean;
  requireActive?: boolean;
  requireStaff?: boolean;
  requireSuperuser?: boolean;

  // Behavior options
  fallback?: ReactNode; // What to show when access is denied
  hideWhenDenied?: boolean; // If true, renders nothing when denied
  showPlaceholder?: boolean; // If true, shows a placeholder when denied
  placeholderText?: string;

  // Feature-level control
  feature?: string; // Feature identifier for logging/analytics
  logAccess?: boolean; // Whether to log access attempts

  // Custom validation
  customCheck?: (user: any) => boolean;

  // DEPRECATED: Groups are no longer used for access control
  groups?: string[];
}

/**
 * PermissionGate Component
 * Dynamically controls access to UI components based on user permissions
 */
export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permissions = [],
  groups = [], // DEPRECATED: Kept for backward compatibility but ignored
  requireAll = false,
  requireAuth = true,
  requireActive = true,
  requireStaff = false,
  requireSuperuser = false,
  fallback,
  hideWhenDenied = false,
  showPlaceholder = false,
  placeholderText = "Access restricted",
  feature,
  logAccess = false,
  customCheck
}) => {
  const { user, isAuthenticated } = useAuth();

  // Use the new permission checker
  const checker = createPermissionChecker(user);

  // Use the new permission checker for all access control logic
  const accessResult = checker.checkAccess({
    permissions,
    requireAll,
    requireAuth,
    requireActive,
    requireStaff,
    requireSuperuser,
    customCheck
  });

  // Log access decision
  if (logAccess) {
    if (accessResult.hasAccess) {
      console.log(`✅ PermissionGate: Access granted for feature ${feature}`, {
        reason: accessResult.reason,
        matchedPermissions: accessResult.matchedPermissions
      });
    } else {
      console.log(`🔒 PermissionGate: Access denied for feature ${feature}`, {
        reason: accessResult.reason,
        requiredPermissions: permissions
      });
    }
  }

  // Grant or deny access
  if (accessResult.hasAccess) {
    return <>{children}</>;
  } else {
    return renderDenied(accessResult.reason || 'Access denied');
  }

  // Helper function to render denied state
  function renderDenied(reason: string) {
    if (hideWhenDenied) {
      return null;
    }

    if (fallback) {
      return <>{fallback}</>;
    }

    if (showPlaceholder) {
      return (
        <div className="flex items-center gap-2 p-3 bg-gray-50 border border-gray-200 rounded text-sm text-gray-600">
          <EyeOff className="h-4 w-4" />
          <span>{placeholderText}</span>
        </div>
      );
    }

    return null;
  }
};

// Convenience components for common use cases
export const AuthRequired: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate requireAuth fallback={fallback}>
    {children}
  </PermissionGate>
);

export const StaffOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate requireStaff fallback={fallback}>
    {children}
  </PermissionGate>
);

export const SuperuserOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate requireSuperuser fallback={fallback}>
    {children}
  </PermissionGate>
);

export const HasPermission: React.FC<{ 
  children: ReactNode; 
  permissions: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}> = ({ children, permissions, requireAll = false, fallback }) => (
  <PermissionGate permissions={permissions} requireAll={requireAll} fallback={fallback}>
    {children}
  </PermissionGate>
);

export const HasRole: React.FC<{ 
  children: ReactNode; 
  roles: string[];
  requireAll?: boolean;
  fallback?: ReactNode;
}> = ({ children, roles, requireAll = false, fallback }) => (
  <PermissionGate groups={roles} requireAll={requireAll} fallback={fallback}>
    {children}
  </PermissionGate>
);

export const FeatureFlag: React.FC<{ 
  children: ReactNode; 
  feature: string;
  permissions?: string[];
  groups?: string[];
  customCheck?: (user: any) => boolean;
  fallback?: ReactNode;
}> = ({ children, feature, permissions, groups, customCheck, fallback }) => (
  <PermissionGate 
    permissions={permissions}
    groups={groups}
    customCheck={customCheck}
    feature={feature}
    logAccess
    fallback={fallback}
  >
    {children}
  </PermissionGate>
);

export default PermissionGate;
