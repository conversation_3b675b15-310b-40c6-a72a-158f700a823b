# Generated by Django 5.2.3 on 2025-06-15 19:36

import django.core.validators
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alumni_applications', '0001_initial'),
        ('college', '0001_initial'),
        ('department', '0001_initial'),
        ('service_type', '0003_alter_servicetype_document_types'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='studentapplicationform',
            name='father_name',
            field=models.CharField(help_text="Father's name (letters only)", max_length=50, validators=[django.core.validators.RegexValidator(message='Father name must contain only letters, spaces, hyphens, apostrophes, and periods.', regex="^[a-zA-Z\\s\\-\\'\\.]+$")]),
        ),
        migrations.AlterField(
            model_name='studentapplicationform',
            name='first_name',
            field=models.Char<PERSON>ield(help_text='First name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='First name must contain only letters, spaces, hyphens, apostrophes, and periods.', regex="^[a-zA-Z\\s\\-\\'\\.]+$")]),
        ),
        migrations.AlterField(
            model_name='studentapplicationform',
            name='last_name',
            field=models.CharField(help_text='Last name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='Last name must contain only letters, spaces, hyphens, apostrophes, and periods.', regex="^[a-zA-Z\\s\\-\\'\\.]+$")]),
        ),
        migrations.AlterField(
            model_name='studentapplicationformsimplified',
            name='father_name',
            field=models.CharField(help_text="Father's name (letters only)", max_length=50, validators=[django.core.validators.RegexValidator(message='Father name must contain only letters, spaces, hyphens, apostrophes, and periods.', regex="^[a-zA-Z\\s\\-\\'\\.]+$")]),
        ),
        migrations.AlterField(
            model_name='studentapplicationformsimplified',
            name='first_name',
            field=models.CharField(help_text='First name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='First name must contain only letters, spaces, hyphens, apostrophes, and periods.', regex="^[a-zA-Z\\s\\-\\'\\.]+$")]),
        ),
        migrations.AlterField(
            model_name='studentapplicationformsimplified',
            name='last_name',
            field=models.CharField(help_text='Last name (letters only)', max_length=50, validators=[django.core.validators.RegexValidator(message='Last name must contain only letters, spaces, hyphens, apostrophes, and periods.', regex="^[a-zA-Z\\s\\-\\'\\.]+$")]),
        ),
        migrations.AddConstraint(
            model_name='studentapplicationform',
            constraint=models.UniqueConstraint(fields=('email',), name='unique_form1_email'),
        ),
        migrations.AddConstraint(
            model_name='studentapplicationformsimplified',
            constraint=models.UniqueConstraint(fields=('email',), name='unique_form2_email'),
        ),
    ]
