#!/usr/bin/env python
"""
Create test data for Alumni Applications system.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.service_type.models import ServiceType
from setups.college.models import College
from setups.department.models import Department
from setups.document_type.models import DocumentType


def create_test_data():
    """Create test data for the alumni applications system."""
    print("🏗️  Creating test data for Alumni Applications...")
    
    try:
        # Create test colleges
        print("  Creating test colleges...")
        college1, created = College.objects.get_or_create(
            name="College of Natural and Computational Sciences",
            defaults={'description': "Test college for natural sciences"}
        )
        if created:
            print(f"    ✅ Created college: {college1.name}")
        else:
            print(f"    ℹ️  College already exists: {college1.name}")
        
        college2, created = College.objects.get_or_create(
            name="College of Engineering and Technology",
            defaults={'description': "Test college for engineering"}
        )
        if created:
            print(f"    ✅ Created college: {college2.name}")
        else:
            print(f"    ℹ️  College already exists: {college2.name}")
        
        # Create test departments
        print("  Creating test departments...")
        dept1, created = Department.objects.get_or_create(
            name="Computer Science",
            college=college1,
            defaults={'description': "Computer Science Department"}
        )
        if created:
            print(f"    ✅ Created department: {dept1.name}")
        else:
            print(f"    ℹ️  Department already exists: {dept1.name}")
        
        dept2, created = Department.objects.get_or_create(
            name="Mathematics",
            college=college1,
            defaults={'description': "Mathematics Department"}
        )
        if created:
            print(f"    ✅ Created department: {dept2.name}")
        else:
            print(f"    ℹ️  Department already exists: {dept2.name}")
        
        dept3, created = Department.objects.get_or_create(
            name="Civil Engineering",
            college=college2,
            defaults={'description': "Civil Engineering Department"}
        )
        if created:
            print(f"    ✅ Created department: {dept3.name}")
        else:
            print(f"    ℹ️  Department already exists: {dept3.name}")
        
        # Create test document types
        print("  Creating test document types...")
        doc_types = [
            {"name": "Student ID Copy", "description": "Copy of student identification"},
            {"name": "Transcript Request Form", "description": "Official transcript request form"},
            {"name": "Degree Certificate Copy", "description": "Copy of degree certificate"},
            {"name": "Payment Receipt", "description": "Proof of payment"},
            {"name": "Authorization Letter", "description": "Letter of authorization for third party"},
        ]
        
        created_doc_types = []
        for doc_data in doc_types:
            doc_type, created = DocumentType.objects.get_or_create(
                name=doc_data["name"],
                defaults={'description': doc_data["description"]}
            )
            created_doc_types.append(doc_type)
            if created:
                print(f"    ✅ Created document type: {doc_type.name}")
            else:
                print(f"    ℹ️  Document type already exists: {doc_type.name}")
        
        # Create test service types
        print("  Creating test service types...")
        service_types = [
            {
                "name": "Official Transcript",
                "description": "Official academic transcript for external institutions",
                "fee": Decimal("150.00"),
                "document_types": [created_doc_types[0], created_doc_types[1], created_doc_types[3]]
            },
            {
                "name": "Student Copy Transcript",
                "description": "Student copy of academic transcript",
                "fee": Decimal("50.00"),
                "document_types": [created_doc_types[0], created_doc_types[3]]
            },
            {
                "name": "Original Degree Certificate",
                "description": "Original degree certificate replacement",
                "fee": Decimal("300.00"),
                "document_types": [created_doc_types[0], created_doc_types[2], created_doc_types[3], created_doc_types[4]]
            },
            {
                "name": "Temporary Certificate",
                "description": "Temporary graduation certificate",
                "fee": Decimal("100.00"),
                "document_types": [created_doc_types[0], created_doc_types[3]]
            },
        ]
        
        for service_data in service_types:
            service_type, created = ServiceType.objects.get_or_create(
                name=service_data["name"],
                defaults={
                    'description': service_data["description"],
                    'fee': service_data["fee"]
                }
            )

            if created:
                print(f"    ✅ Created service type: {service_type.name}")
            else:
                print(f"    ℹ️  Service type already exists: {service_type.name}")

            # Always update document types (in case they changed)
            service_type.document_types.set(service_data["document_types"])
            print(f"      Associated {len(service_data['document_types'])} document types")
        
        print("✅ Test data creation completed successfully!")
        
        # Print summary
        print("\n📊 Test Data Summary:")
        print(f"  Colleges: {College.objects.count()}")
        print(f"  Departments: {Department.objects.count()}")
        print(f"  Document Types: {DocumentType.objects.count()}")
        print(f"  Service Types: {ServiceType.objects.count()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test data creation failed: {e}")
        return False


def main():
    """Main function."""
    print("🚀 Alumni Applications Test Data Creator\n")
    
    success = create_test_data()
    
    if success:
        print("\n🎉 Test data created successfully!")
        print("You can now run the alumni applications tests.")
    else:
        print("\n❌ Test data creation failed.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
