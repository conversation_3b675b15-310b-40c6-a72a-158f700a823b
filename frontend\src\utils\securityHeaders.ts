/**
 * Security headers and CSRF protection utilities
 */

// CSRF Token management
class CSRFTokenManager {
  private static instance: CSRFTokenManager;
  private token: string | null = null;
  private tokenExpiry: number = 0;

  private constructor() {}

  static getInstance(): CSRFTokenManager {
    if (!CSRFTokenManager.instance) {
      CSRFTokenManager.instance = new CSRFTokenManager();
    }
    return CSRFTokenManager.instance;
  }

  async getToken(): Promise<string> {
    // Check if token is still valid
    if (this.token && Date.now() < this.tokenExpiry) {
      return this.token;
    }

    // Generate new token
    await this.refreshToken();
    return this.token!;
  }

  private async refreshToken(): Promise<void> {
    try {
      // In a real implementation, this would fetch from your backend
      // For now, we'll generate a client-side token
      const response = await fetch('/api/csrf-token', {
        method: 'GET',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        this.token = data.token;
        this.tokenExpiry = Date.now() + (data.expiry || 3600000); // 1 hour default
      } else {
        // Fallback: generate client-side token
        this.token = this.generateClientToken();
        this.tokenExpiry = Date.now() + 3600000; // 1 hour
      }
    } catch (error) {
      console.warn('Failed to fetch CSRF token from server, using client-side token');
      this.token = this.generateClientToken();
      this.tokenExpiry = Date.now() + 3600000; // 1 hour
    }
  }

  private generateClientToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  invalidateToken(): void {
    this.token = null;
    this.tokenExpiry = 0;
  }
}

// Security headers configuration
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
} as const;

// Content Security Policy
export const CSP_DIRECTIVES = {
  'default-src': "'self'",
  'script-src': "'self' 'unsafe-inline' 'unsafe-eval'", // Note: unsafe-* should be removed in production
  'style-src': "'self' 'unsafe-inline'",
  'img-src': "'self' data: blob:",
  'font-src': "'self'",
  'connect-src': "'self' http://localhost:8000 http://localhost:8080",
  'media-src': "'self'",
  'object-src': "'none'",
  'frame-src': "'none'",
  'base-uri': "'self'",
  'form-action': "'self'",
} as const;

// Generate CSP header value
export const generateCSPHeader = (): string => {
  return Object.entries(CSP_DIRECTIVES)
    .map(([directive, value]) => `${directive} ${value}`)
    .join('; ');
};

// Enhanced fetch wrapper with security headers
export const secureApiCall = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  const csrfManager = CSRFTokenManager.getInstance();
  const csrfToken = await csrfManager.getToken();

  const secureHeaders = {
    ...SECURITY_HEADERS,
    'X-CSRF-Token': csrfToken,
    'Content-Type': 'application/json',
    ...options.headers,
  };

  const secureOptions: RequestInit = {
    ...options,
    headers: secureHeaders,
    credentials: 'include', // Include cookies for session management
    mode: 'cors',
  };

  // Add request timestamp for replay attack prevention
  if (secureOptions.method && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(secureOptions.method)) {
    const body = secureOptions.body ? JSON.parse(secureOptions.body as string) : {};
    body._timestamp = Date.now();
    body._nonce = crypto.getRandomValues(new Uint32Array(1))[0].toString(16);
    secureOptions.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, secureOptions);

    // Handle CSRF token refresh
    if (response.status === 403 && response.headers.get('X-CSRF-Error')) {
      csrfManager.invalidateToken();
      // Retry with new token
      const newToken = await csrfManager.getToken();
      secureHeaders['X-CSRF-Token'] = newToken;
      return fetch(url, { ...secureOptions, headers: secureHeaders });
    }

    return response;
  } catch (error) {
    console.error('Secure API call failed:', error);
    throw error;
  }
};

// Input sanitization utilities
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return input;

  return input
    // Remove script tags
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // Remove javascript: protocol
    .replace(/javascript:/gi, '')
    // Remove event handlers
    .replace(/on\w+\s*=/gi, '')
    // Remove data: URLs (except images)
    .replace(/data:(?!image\/)/gi, '')
    // Encode HTML entities
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim();
};

// Validate and sanitize file names
export const sanitizeFileName = (fileName: string): string => {
  if (!fileName) return 'unnamed_file';

  return fileName
    // Remove path traversal attempts
    .replace(/\.\./g, '')
    .replace(/[\/\\]/g, '')
    // Remove potentially dangerous characters
    .replace(/[<>:"|?*]/g, '')
    // Remove control characters
    .replace(/[\x00-\x1f\x80-\x9f]/g, '')
    // Limit length
    .substring(0, 255)
    // Ensure it doesn't start with a dot
    .replace(/^\.+/, '')
    // Ensure it has an extension
    || 'unnamed_file';
};

// Rate limiting utility
export class RateLimiter {
  private requests: { [key: string]: number[] } = {};
  private maxRequests: number;
  private windowMs: number;

  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;

    // Initialize or clean up old requests
    if (!this.requests[identifier]) {
      this.requests[identifier] = [];
    }

    // Remove old requests outside the window
    this.requests[identifier] = this.requests[identifier].filter(
      timestamp => timestamp > windowStart
    );

    // Check if under limit
    if (this.requests[identifier].length < this.maxRequests) {
      this.requests[identifier].push(now);
      return true;
    }

    return false;
  }

  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const windowStart = now - this.windowMs;

    if (!this.requests[identifier]) {
      return this.maxRequests;
    }

    const recentRequests = this.requests[identifier].filter(
      timestamp => timestamp > windowStart
    );

    return Math.max(0, this.maxRequests - recentRequests.length);
  }

  getTimeUntilReset(identifier: string): number {
    if (!this.requests[identifier] || this.requests[identifier].length === 0) {
      return 0;
    }

    const oldestRequest = Math.min(...this.requests[identifier]);
    const resetTime = oldestRequest + this.windowMs;
    return Math.max(0, resetTime - Date.now());
  }
}

// Security event logger
export class SecurityLogger {
  private static instance: SecurityLogger;
  private events: Array<{
    timestamp: number;
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    metadata?: any;
  }> = [];

  private constructor() {}

  static getInstance(): SecurityLogger {
    if (!SecurityLogger.instance) {
      SecurityLogger.instance = new SecurityLogger();
    }
    return SecurityLogger.instance;
  }

  log(
    type: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    message: string,
    metadata?: any
  ): void {
    const event = {
      timestamp: Date.now(),
      type,
      severity,
      message,
      metadata
    };

    this.events.push(event);

    // Keep only last 1000 events
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      const logMethod = severity === 'critical' || severity === 'high' ? 'error' : 
                       severity === 'medium' ? 'warn' : 'log';
      console[logMethod](`[Security ${severity.toUpperCase()}] ${type}: ${message}`, metadata);
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production' && (severity === 'high' || severity === 'critical')) {
      this.sendToMonitoring(event);
    }
  }

  private async sendToMonitoring(event: any): Promise<void> {
    try {
      await fetch('/api/security-events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event),
        credentials: 'include'
      });
    } catch (error) {
      console.error('Failed to send security event to monitoring:', error);
    }
  }

  getEvents(severity?: 'low' | 'medium' | 'high' | 'critical'): typeof this.events {
    if (severity) {
      return this.events.filter(event => event.severity === severity);
    }
    return [...this.events];
  }

  clearEvents(): void {
    this.events = [];
  }
}

// Export singleton instances
export const csrfTokenManager = CSRFTokenManager.getInstance();
export const securityLogger = SecurityLogger.getInstance();
export const globalRateLimiter = new RateLimiter(100, 60000); // 100 requests per minute
