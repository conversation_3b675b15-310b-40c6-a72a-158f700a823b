from django.contrib import admin
from .models import ApplicantInformation
from .models import ApplicantGAT, ApplicantProgramSelection, ApplicantDocumentation, ApplicantPayment
# Register your models here.
admin.site.register(ApplicantInformation)
admin.site.register(ApplicantGAT)
@admin.register(ApplicantProgramSelection)
class ApplicantProgramSelectionAdmin(admin.ModelAdmin):
    exclude = ('id', 'created_at', 'updated_at')
    readonly_fields = ('transaction_id',)
    list_display = ('user', 'gat', 'application_num', 'transaction_id', 'sponsorship', 'year', 'term', 'created_at')
    search_fields = ('user__username', 'user__email', 'application_num', 'transaction_id')
    list_filter = ('year', 'term', 'sponsorship', 'registrar_off_status', 'department_status', 'payment_status')
admin.site.register(ApplicantDocumentation)
@admin.register(ApplicantPayment)
class ApplicantPaymentAdmin(admin.ModelAdmin):
    list_display = ('user', 'applicant_gat', 'payment_status', 'payment_method', 'amount_paid', 'created_at')
    list_filter = ('payment_status', 'payment_method')
    search_fields = ('user__username', 'user__email', 'applicant_gat__GAT_No')
    readonly_fields = ('created_at', 'updated_at')



