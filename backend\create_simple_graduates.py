#!/usr/bin/env python
"""
Simple script to create graduate students with proper relationships
"""
import os
import sys
import django
from decimal import Decimal

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from GraduateVerification.models import (
    GraduateStudent, VerificationCollege, VerificationDepartment, 
    VerificationProgram, VerificationFieldOfStudy, AdmissionClassification
)

def create_test_graduates():
    """Create test graduate students"""
    print("Creating test graduate students...")
    
    # Get or create basic data
    college, _ = VerificationCollege.objects.get_or_create(
        name="Test University",
        defaults={'code': 'TU'}
    )

    department, _ = VerificationDepartment.objects.get_or_create(
        name="Computer Science",
        college=college,
        defaults={'code': 'CS'}
    )

    program1, _ = VerificationProgram.objects.get_or_create(
        name="Bachelor of Science",
        defaults={'code': 'BSC'}
    )

    program2, _ = VerificationProgram.objects.get_or_create(
        name="Master of Science",
        defaults={'code': 'MSC'}
    )

    field_of_study, _ = VerificationFieldOfStudy.objects.get_or_create(
        name="Software Engineering",
        department=department,
        defaults={'code': 'SE', 'duration': 4}
    )
    
    admission_class, _ = AdmissionClassification.objects.get_or_create(
        name="Regular",
        defaults={'description': 'Regular admission'}
    )
    
    print(f"Created/found: College={college.name}, Department={department.name}")
    print(f"Programs: {program1.name}, {program2.name}")
    
    # Create graduates for multiple years
    years = [2022, 2023, 2024]
    programs = [program1, program2]
    genders = ['Male', 'Female']
    
    created_count = 0
    
    for year in years:
        for program in programs:
            for gender in genders:
                # Create 5 graduates per year/program/gender combination
                for i in range(5):
                    # Generate realistic GPA
                    base_gpa = 3.0 if program == program1 else 3.3  # Masters slightly higher
                    gpa_variation = (i * 0.1) + ((year - 2022) * 0.05)  # Slight improvement over time
                    final_gpa = min(4.0, max(2.5, base_gpa + gpa_variation))
                    
                    student_id = f"{program.code}/{year}/{gender[0]}{i+1:02d}"
                    
                    graduate, created = GraduateStudent.objects.get_or_create(
                        student_id=student_id,
                        defaults={
                            'first_name': f"Student{i+1}",
                            'last_name': f"{gender}{year}",
                            'year_of_entry': year - 4 if program == program1 else year - 2,
                            'year_of_graduation': year,
                            'gpa': Decimal(str(round(final_gpa, 2))),
                            'gender': gender,
                            'college': college,
                            'department': department,
                            'program': program,
                            'field_of_study': field_of_study,
                            'admission_classification': admission_class,
                        }
                    )
                    
                    if created:
                        created_count += 1
    
    print(f"Created {created_count} new graduate students")
    
    # Verify the data
    total_students = GraduateStudent.objects.count()
    print(f"Total graduate students in database: {total_students}")
    
    # Check by program
    for program in programs:
        count = GraduateStudent.objects.filter(program=program).count()
        avg_gpa = GraduateStudent.objects.filter(program=program).aggregate(
            avg_gpa=django.db.models.Avg('gpa')
        )['avg_gpa']
        print(f"Program {program.name}: {count} students, avg GPA: {avg_gpa:.2f}")
    
    # Check by year
    for year in years:
        count = GraduateStudent.objects.filter(year_of_graduation=year).count()
        avg_gpa = GraduateStudent.objects.filter(year_of_graduation=year).aggregate(
            avg_gpa=django.db.models.Avg('gpa')
        )['avg_gpa']
        print(f"Year {year}: {count} students, avg GPA: {avg_gpa:.2f}")

if __name__ == '__main__':
    import django.db.models
    create_test_graduates()
