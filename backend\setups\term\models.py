from django.db import models
import uuid

# Create your models here.
class Term(models.Model):
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique identifier for the term"
    )
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Name of the term"
    )
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Detailed description of the term (optional)"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="Date and time when the term was last updated"
    )

    class Meta:
        verbose_name = "Term"
        verbose_name_plural = "Terms"
        ordering = ['name']  # Default ordering

    def __str__(self):
        return f"{self.name}"

    def save(self, *args, **kwargs):
        """Override save method for any custom logic"""
        super().save(*args, **kwargs)