# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (for any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager lock files (keep only one)
bun.lockb
yarn.lock

# Build artifacts
dist/
build/
*.tgz
*.tar.gz

# Temporary files
*.tmp
*.temp
.cache/

# Development databases
*.sqlite3
*.db



# Logs
*.log
logs/

# Coverage reports
coverage/
.nyc_output/

# IDE and editor files
*.swp
*.swo
*~
.project
.classpath
.settings/

# OS generated files
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
