/**
 * Menu Configuration with Permission-Based Access Control
 * Defines all menu items for different dashboard types with their permission requirements
 */

import {
  Home, FileText, Clock, Bell, User, Users, BarChart3, Settings,
  GraduationCap, Building2, Package, Shield, Database, MessageSquare,
  Award, BookOpen, UserCheck, Globe, Calendar, HelpCircle, Info,
  ChevronRight, School, Cog, UserCircle, Building, Download, FileCheck, Upload
} from 'lucide-react';

export interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  permissionKey: string; // Key to check in menuPermissions.ts
  badge?: string;
  subPaths?: string[];
  description?: string;
  category?: string;
  isSingle?: boolean; // For single menu items without dropdown
}

export interface MenuCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  items: MenuItem[];
}

// ===== APPLICANT DASHBOARD MENUS =====
export const APPLICANT_MENU_ITEMS: MenuItem[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    path: '/dashboard',
    icon: Home,
    permissionKey: 'dashboard',
    description: 'Overview of your application status and activities'
  },
  {
    id: 'personal-information',
    name: 'Personal Information',
    path: '/personal-information',
    icon: UserCircle,
    permissionKey: 'personal-information',
    description: 'Manage your personal details and profile'
  },
  {
    id: 'application',
    name: 'Application',
    path: '/application',
    icon: FileText,
    permissionKey: 'application',
    subPaths: [
      '/application/personal-info',
      '/application/gat',
      '/application/program-selection',
      '/application/documentation',
      '/application/payment',
      '/application/status'
    ],
    description: 'Submit and manage your graduate applications'
  },
  {
    id: 'application-status',
    name: 'Application Status',
    path: '/status',
    icon: Clock,
    permissionKey: 'application-status',
    description: 'Track the progress of your applications'
  },
  {
    id: 'notifications',
    name: 'Notifications',
    path: '/notifications',
    icon: Bell,
    permissionKey: 'notifications',
    description: 'View important updates and messages'
  }
];

// ===== STAFF DASHBOARD MENUS =====
export const STAFF_MENU_CATEGORIES: MenuCategory[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: Home,
    items: [
      {
        id: 'graduation-dashboard',
        name: 'Graduation Dashboard',
        path: '/graduate-admin?tab=dashboard',
        icon: GraduationCap,
        permissionKey: 'graduation-dashboard',
        description: 'Overview of graduate student data and statistics'
      },
      {
        id: 'application-dashboard',
        name: 'Application Dashboard',
        path: '/graduate-admin?tab=application-dashboard',
        icon: FileText,
        permissionKey: 'application-dashboard',
        description: 'Monitor and manage student applications'
      },
      {
        id: 'service-fee-dashboard',
        name: 'Service Fee Dashboard',
        path: '/graduate-admin?tab=service-fee-dashboard',
        icon: Package,
        permissionKey: 'service-fee-dashboard',
        description: 'Monitor service fees and payments'
      }
    ]
  },
  {
    id: 'graduate-verification',
    name: 'Graduate Verification',
    icon: UserCheck,
    items: [
      {
        id: 'manage-graduates',
        name: 'Manage Graduates',
        path: '/graduate-admin?tab=graduate-management',
        icon: GraduationCap,
        permissionKey: 'manage-graduates',
        description: 'Manage graduate student records and verification'
      },
      {
        id: 'manage-colleges',
        name: 'Manage Colleges',
        path: '/graduate-admin?tab=college-management',
        icon: Building2,
        permissionKey: 'manage-colleges',
        description: 'Manage colleges and faculties'
      },
      {
        id: 'manage-departments',
        name: 'Manage Departments',
        path: '/graduate-admin?tab=department-management',
        icon: Building,
        permissionKey: 'manage-departments',
        description: 'Manage academic departments'
      },
      {
        id: 'graduate-fields-of-study',
        name: 'Graduate Fields of Study',
        path: '/graduate-admin?tab=graduate-fields-of-study',
        icon: BookOpen,
        permissionKey: 'graduate-fields-of-study',
        description: 'Manage graduate fields of study'
      },
      {
        id: 'manage-admission-classifications',
        name: 'Manage Admission Classifications',
        path: '/graduate-admin?tab=admission-classifications',
        icon: Award,
        permissionKey: 'manage-admission-classifications',
        description: 'Manage admission classification types'
      },
      {
        id: 'manage-programs',
        name: 'Manage Programs',
        path: '/graduate-admin?tab=program-management',
        icon: School,
        permissionKey: 'manage-programs',
        description: 'Manage academic programs'
      }
    ]
  },
  {
    id: 'application-portal',
    name: 'Application Portal',
    icon: FileText,
    items: [
      {
        id: 'manage-colleges-app',
        name: 'Manage Colleges',
        path: '/graduate-admin?tab=colleges',
        icon: Building2,
        permissionKey: 'manage-colleges-app',
        description: 'Manage colleges for applications'
      },
      {
        id: 'manage-departments-app',
        name: 'Manage Departments',
        path: '/graduate-admin?tab=departments',
        icon: Building,
        permissionKey: 'manage-departments-app',
        description: 'Manage departments for applications'
      },
      {
        id: 'manage-programs-app',
        name: 'Manage Programs',
        path: '/graduate-admin?tab=programs',
        icon: BookOpen,
        permissionKey: 'manage-programs-app',
        description: 'Manage programs for applications'
      },
      {
        id: 'manage-study-programs',
        name: 'Manage Study Programs',
        path: '/graduate-admin?tab=study-programs',
        icon: School,
        permissionKey: 'manage-study-programs',
        description: 'Manage study program configurations'
      },
      {
        id: 'manage-admission-types',
        name: 'Manage Admission Types',
        path: '/graduate-admin?tab=admission-types',
        icon: Award,
        permissionKey: 'manage-admission-types',
        description: 'Manage admission type categories'
      },
      {
        id: 'manage-registration-periods',
        name: 'Manage Registration Periods',
        path: '/graduate-admin?tab=registration-periods',
        icon: Calendar,
        permissionKey: 'manage-registration-periods',
        description: 'Manage application registration periods'
      },
      {
        id: 'manage-fields-of-study-app',
        name: 'Manage Fields of Study',
        path: '/graduate-admin?tab=fields-of-study',
        icon: BookOpen,
        permissionKey: 'manage-fields-of-study-app',
        description: 'Manage fields of study for applications'
      },
      {
        id: 'manage-years',
        name: 'Manage Years',
        path: '/graduate-admin?tab=years',
        icon: Calendar,
        permissionKey: 'manage-years',
        description: 'Manage academic years'
      },
      {
        id: 'manage-terms',
        name: 'Manage Terms',
        path: '/graduate-admin?tab=terms',
        icon: Calendar,
        permissionKey: 'manage-terms',
        description: 'Manage academic terms'
      },
      {
        id: 'application-information',
        name: 'Application Information',
        path: '/graduate-admin?tab=application-information',
        icon: Info,
        permissionKey: 'application-information',
        description: 'View application information and statistics'
      },
      {
        id: 'manage-applicants',
        name: 'Manage Applicants',
        path: '/graduate-admin?tab=applicants',
        icon: Users,
        permissionKey: 'manage-applicants',
        description: 'Manage applicant records'
      },
      {
        id: 'downloadable-content',
        name: 'Downloadable Content',
        path: '/graduate-admin?tab=downloadable-content',
        icon: Download,
        permissionKey: 'downloadable-content',
        description: 'Manage downloadable documents and forms'
      }
    ]
  },
  {
    id: 'services',
    name: 'Services',
    icon: Settings,
    items: [
      {
        id: 'services',
        name: 'Services',
        path: '/graduate-admin?tab=services',
        icon: Cog,
        permissionKey: 'services',
        description: 'Manage university services'
      },
      {
        id: 'service-requests',
        name: 'Service Requests',
        path: '/graduate-admin?tab=service-requests',
        icon: FileText,
        permissionKey: 'service-requests',
        description: 'Manage service requests from users'
      },
      {
        id: 'service-types',
        name: 'Service Types',
        path: '/graduate-admin?tab=service-types',
        icon: Package,
        permissionKey: 'service-types',
        description: 'Manage available service types'
      },
      {
        id: 'document-uploads',
        name: 'Document Uploads',
        path: '/graduate-admin?tab=document-uploads',
        icon: Upload,
        permissionKey: 'document-uploads',
        description: 'Manage document uploads for services'
      },
      {
        id: 'document-types',
        name: 'Document Types',
        path: '/graduate-admin?tab=document-types',
        icon: FileText,
        permissionKey: 'document-types',
        description: 'Manage document type categories'
      },
      {
        id: 'alumni-applications-service',
        name: 'Alumni Applications',
        path: '/graduate-admin?tab=alumni-applications',
        icon: School,
        permissionKey: 'alumni-applications-service',
        description: 'Process alumni service applications'
      }
    ]
  },
  {
    id: 'officials',
    name: 'Officials',
    icon: UserCheck,
    items: [
      {
        id: 'certificate-types',
        name: 'Certificate Types',
        path: '/graduate-admin?tab=certificate-types',
        icon: Award,
        permissionKey: 'certificate-types',
        description: 'Manage certificate type definitions'
      },
      {
        id: 'official-certificates',
        name: 'Official Certificates',
        path: '/graduate-admin?tab=official-certificates',
        icon: FileCheck,
        permissionKey: 'official-certificates',
        description: 'Manage official certificate issuance'
      }
    ]
  },
  // Single menu items (not in dropdowns)
  {
    id: 'settings-single',
    name: 'Settings',
    icon: Settings,
    items: [
      {
        id: 'general-settings',
        name: 'Settings',
        path: '/graduate-admin?tab=settings',
        icon: Settings,
        permissionKey: 'general-settings',
        description: 'Configure general system settings',
        isSingle: true
      }
    ]
  },
  {
    id: 'authentication-single',
    name: 'Authentication & Authorization',
    icon: Shield,
    items: [
      {
        id: 'authentication-authorization',
        name: 'Authentication & Authorization',
        path: '/graduate-admin?tab=authentication',
        icon: Shield,
        permissionKey: 'authentication-authorization',
        description: 'Manage authentication and authorization settings',
        isSingle: true
      }
    ]
  }
];

// ===== ADMIN DASHBOARD MENUS (Superuser Only) =====
export const ADMIN_MENU_CATEGORIES: MenuCategory[] = [
  {
    id: 'system-administration',
    name: 'System Administration',
    icon: Settings,
    items: [
      {
        id: 'system-settings',
        name: 'System Settings',
        path: '/system-settings',
        icon: Settings,
        permissionKey: 'system-settings',
        badge: 'Admin',
        description: 'Configure system-wide settings and preferences'
      },
      {
        id: 'authentication-management',
        name: 'Authentication Management',
        path: '/graduate-admin?tab=authentication-management',
        icon: Shield,
        permissionKey: 'authentication-management',
        badge: 'Admin',
        description: 'Manage user authentication and security'
      },
      {
        id: 'user-management',
        name: 'User Management',
        path: '/graduate-admin?tab=user-management',
        icon: UserCheck,
        permissionKey: 'user-management',
        badge: 'Admin',
        description: 'Manage user accounts and permissions'
      },
      {
        id: 'permission-management',
        name: 'Permission Management',
        path: '/graduate-admin?tab=permission-management',
        icon: Shield,
        permissionKey: 'permission-management',
        badge: 'Admin',
        description: 'Manage user permissions and roles'
      }
    ]
  },
  {
    id: 'communication',
    name: 'Communication',
    icon: MessageSquare,
    items: [
      {
        id: 'announcements',
        name: 'Announcements',
        path: '/graduate-admin?tab=announcements',
        icon: MessageSquare,
        permissionKey: 'announcements',
        description: 'Create and manage public announcements'
      },
      {
        id: 'official-management',
        name: 'Official Communications',
        path: '/graduate-admin?tab=official-management',
        icon: Globe,
        permissionKey: 'official-management',
        description: 'Manage official correspondence'
      }
    ]
  },
  {
    id: 'data-management',
    name: 'Data Management',
    icon: Database,
    items: [
      {
        id: 'database-management',
        name: 'Database Management',
        path: '/graduate-admin?tab=database-management',
        icon: Database,
        permissionKey: 'database-management',
        badge: 'Admin',
        description: 'Database administration and maintenance'
      }
    ]
  },
  {
    id: 'analytics',
    name: 'Analytics & Reports',
    icon: BarChart3,
    items: [
      {
        id: 'reports',
        name: 'Reports',
        path: '/graduate-admin?tab=reports',
        icon: BarChart3,
        permissionKey: 'reports',
        description: 'Generate and view system reports'
      },
      {
        id: 'analytics',
        name: 'Analytics',
        path: '/graduate-admin?tab=analytics',
        icon: BarChart3,
        permissionKey: 'analytics',
        description: 'View system analytics and insights'
      }
    ]
  }
];

// ===== UTILITY FUNCTIONS =====

/**
 * Get all menu items as a flat array
 */
export const getAllMenuItems = (): MenuItem[] => {
  const staffItems = STAFF_MENU_CATEGORIES.flatMap(category => category.items);
  const adminItems = ADMIN_MENU_CATEGORIES.flatMap(category => category.items);
  return [...APPLICANT_MENU_ITEMS, ...staffItems, ...adminItems];
};

/**
 * Find menu item by ID
 */
export const findMenuItemById = (id: string): MenuItem | undefined => {
  return getAllMenuItems().find(item => item.id === id);
};

/**
 * Find menu item by path
 */
export const findMenuItemByPath = (path: string): MenuItem | undefined => {
  return getAllMenuItems().find(item => 
    item.path === path || 
    (item.subPaths && item.subPaths.some(subPath => path.startsWith(subPath)))
  );
};

/**
 * Get menu categories for staff dashboard
 */
export const getStaffMenuCategories = (): MenuCategory[] => {
  return STAFF_MENU_CATEGORIES;
};

/**
 * Get menu categories for admin dashboard
 */
export const getAdminMenuCategories = (): MenuCategory[] => {
  return ADMIN_MENU_CATEGORIES;
};
