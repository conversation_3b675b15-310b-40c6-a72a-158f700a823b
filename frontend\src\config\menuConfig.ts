/**
 * Menu Configuration with Permission-Based Access Control
 * Defines all menu items for different dashboard types with their permission requirements
 */

import {
  Home, FileText, Clock, Bell, User, Users, BarChart3, Settings,
  GraduationCap, Building2, Package, Shield, Database, MessageSquare,
  Award, BookOpen, UserCheck, Globe, Calendar, HelpCircle, Info,
  ChevronRight, School, Cog, UserCircle, Building
} from 'lucide-react';

export interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  permissionKey: string; // Key to check in menuPermissions.ts
  badge?: string;
  subPaths?: string[];
  description?: string;
  category?: string;
}

export interface MenuCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  items: MenuItem[];
}

// ===== APPLICANT DASHBOARD MENUS =====
export const APPLICANT_MENU_ITEMS: MenuItem[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    path: '/dashboard',
    icon: Home,
    permissionKey: 'dashboard',
    description: 'Overview of your application status and activities'
  },
  {
    id: 'personal-information',
    name: 'Personal Information',
    path: '/personal-information',
    icon: UserCircle,
    permissionKey: 'personal-information',
    description: 'Manage your personal details and profile'
  },
  {
    id: 'application',
    name: 'Application',
    path: '/application',
    icon: FileText,
    permissionKey: 'application',
    subPaths: [
      '/application/personal-info',
      '/application/gat',
      '/application/program-selection',
      '/application/documentation',
      '/application/payment',
      '/application/status'
    ],
    description: 'Submit and manage your graduate applications'
  },
  {
    id: 'application-status',
    name: 'Application Status',
    path: '/status',
    icon: Clock,
    permissionKey: 'application-status',
    description: 'Track the progress of your applications'
  },
  {
    id: 'notifications',
    name: 'Notifications',
    path: '/notifications',
    icon: Bell,
    permissionKey: 'notifications',
    description: 'View important updates and messages'
  }
];

// ===== STAFF DASHBOARD MENUS =====
export const STAFF_MENU_CATEGORIES: MenuCategory[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: BarChart3,
    items: [
      {
        id: 'graduate-dashboard',
        name: 'Graduate Dashboard',
        path: '/graduate-admin?tab=dashboard',
        icon: GraduationCap,
        permissionKey: 'graduate-dashboard',
        description: 'Overview of graduate student data and statistics'
      },
      {
        id: 'application-dashboard',
        name: 'Application Dashboard',
        path: '/graduate-admin?tab=application-dashboard',
        icon: FileText,
        permissionKey: 'application-dashboard',
        description: 'Monitor and manage student applications'
      }
    ]
  },
  {
    id: 'student-management',
    name: 'Student Management',
    icon: Users,
    items: [
      {
        id: 'graduate-management',
        name: 'Graduate Management',
        path: '/graduate-admin?tab=graduate-management',
        icon: GraduationCap,
        permissionKey: 'graduate-management',
        description: 'Manage graduate student records and verification'
      },
      {
        id: 'alumni-applications',
        name: 'Alumni Applications',
        path: '/graduate-admin?tab=alumni-applications',
        icon: School,
        permissionKey: 'alumni-applications',
        description: 'Process alumni service applications'
      }
    ]
  },
  {
    id: 'academic-management',
    name: 'Academic Management',
    icon: BookOpen,
    items: [
      {
        id: 'program-management',
        name: 'Program Management',
        path: '/graduate-admin?tab=program-management',
        icon: BookOpen,
        permissionKey: 'program-management',
        description: 'Manage academic programs and study plans'
      },
      {
        id: 'department-management',
        name: 'Department Management',
        path: '/graduate-admin?tab=department-management',
        icon: Building,
        permissionKey: 'department-management',
        description: 'Manage academic departments'
      },
      {
        id: 'college-management',
        name: 'College Management',
        path: '/graduate-admin?tab=college-management',
        icon: Building2,
        permissionKey: 'college-management',
        description: 'Manage colleges and faculties'
      }
    ]
  },
  {
    id: 'service-management',
    name: 'Service Management',
    icon: Package,
    items: [
      {
        id: 'service-management',
        name: 'Services',
        path: '/graduate-admin?tab=service-management',
        icon: Package,
        permissionKey: 'service-management',
        description: 'Manage available services and offerings'
      },
      {
        id: 'certificate-management',
        name: 'Certificate Types',
        path: '/graduate-admin?tab=certificate-management',
        icon: Award,
        permissionKey: 'certificate-management',
        description: 'Manage certificate types and templates'
      },
      {
        id: 'document-management',
        name: 'Document Management',
        path: '/graduate-admin?tab=document-management',
        icon: FileText,
        permissionKey: 'document-management',
        description: 'Manage downloadable documents and forms'
      }
    ]
  },
  {
    id: 'communication',
    name: 'Communication',
    icon: MessageSquare,
    items: [
      {
        id: 'announcements',
        name: 'Announcements',
        path: '/graduate-admin?tab=announcements',
        icon: MessageSquare,
        permissionKey: 'announcements',
        description: 'Create and manage public announcements'
      },
      {
        id: 'official-management',
        name: 'Official Communications',
        path: '/graduate-admin?tab=official-management',
        icon: Globe,
        permissionKey: 'official-management',
        description: 'Manage official correspondence'
      }
    ]
  }
];

// ===== ADMIN DASHBOARD MENUS (Superuser Only) =====
export const ADMIN_MENU_CATEGORIES: MenuCategory[] = [
  {
    id: 'system-administration',
    name: 'System Administration',
    icon: Settings,
    items: [
      {
        id: 'system-settings',
        name: 'System Settings',
        path: '/system-settings',
        icon: Settings,
        permissionKey: 'system-settings',
        badge: 'Admin',
        description: 'Configure system-wide settings and preferences'
      },
      {
        id: 'authentication-management',
        name: 'Authentication Management',
        path: '/graduate-admin?tab=authentication-management',
        icon: Shield,
        permissionKey: 'authentication-management',
        badge: 'Admin',
        description: 'Manage user authentication and security'
      },
      {
        id: 'user-management',
        name: 'User Management',
        path: '/graduate-admin?tab=user-management',
        icon: UserCheck,
        permissionKey: 'user-management',
        badge: 'Admin',
        description: 'Manage user accounts and permissions'
      }
    ]
  },
  {
    id: 'data-management',
    name: 'Data Management',
    icon: Database,
    items: [
      {
        id: 'database-management',
        name: 'Database Management',
        path: '/graduate-admin?tab=database-management',
        icon: Database,
        permissionKey: 'database-management',
        badge: 'Admin',
        description: 'Database administration and maintenance'
      }
    ]
  },
  {
    id: 'analytics',
    name: 'Analytics & Reports',
    icon: BarChart3,
    items: [
      {
        id: 'reports',
        name: 'Reports',
        path: '/graduate-admin?tab=reports',
        icon: BarChart3,
        permissionKey: 'reports',
        description: 'Generate and view system reports'
      },
      {
        id: 'analytics',
        name: 'Analytics',
        path: '/graduate-admin?tab=analytics',
        icon: BarChart3,
        permissionKey: 'analytics',
        description: 'View system analytics and insights'
      }
    ]
  }
];

// ===== UTILITY FUNCTIONS =====

/**
 * Get all menu items as a flat array
 */
export const getAllMenuItems = (): MenuItem[] => {
  const staffItems = STAFF_MENU_CATEGORIES.flatMap(category => category.items);
  const adminItems = ADMIN_MENU_CATEGORIES.flatMap(category => category.items);
  return [...APPLICANT_MENU_ITEMS, ...staffItems, ...adminItems];
};

/**
 * Find menu item by ID
 */
export const findMenuItemById = (id: string): MenuItem | undefined => {
  return getAllMenuItems().find(item => item.id === id);
};

/**
 * Find menu item by path
 */
export const findMenuItemByPath = (path: string): MenuItem | undefined => {
  return getAllMenuItems().find(item => 
    item.path === path || 
    (item.subPaths && item.subPaths.some(subPath => path.startsWith(subPath)))
  );
};

/**
 * Get menu categories for staff dashboard
 */
export const getStaffMenuCategories = (): MenuCategory[] => {
  return STAFF_MENU_CATEGORIES;
};

/**
 * Get menu categories for admin dashboard
 */
export const getAdminMenuCategories = (): MenuCategory[] => {
  return ADMIN_MENU_CATEGORIES;
};
