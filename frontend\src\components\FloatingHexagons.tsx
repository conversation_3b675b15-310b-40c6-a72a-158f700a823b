import React from 'react';

const FloatingHexagons: React.FC = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Large floating hexagon - top left */}
      <div className="absolute -top-10 -left-10 w-40 h-40 animate-float opacity-10">
        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
        </svg>
      </div>
      
      {/* Medium floating hexagon - top right */}
      <div className="absolute top-1/4 right-10 w-32 h-32 animate-float-reverse opacity-15" style={{ animationDelay: '1s' }}>
        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
        </svg>
      </div>
      
      {/* Small floating hexagon - bottom left */}
      <div className="absolute bottom-20 left-1/4 w-24 h-24 animate-float opacity-10" style={{ animationDelay: '2s' }}>
        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
        </svg>
      </div>
      
      {/* Small floating hexagon - bottom right */}
      <div className="absolute bottom-10 right-1/4 w-20 h-20 animate-float-reverse opacity-15" style={{ animationDelay: '3s' }}>
        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
        </svg>
      </div>
      
      {/* Medium floating hexagon - center */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-28 h-28 animate-float opacity-10" style={{ animationDelay: '1.5s' }}>
        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
        </svg>
      </div>
    </div>
  );
};

export default FloatingHexagons;
