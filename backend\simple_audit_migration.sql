-- Simple SQL script to add audit trail fields to GraduateStudent table
-- Execute this directly in your database

-- Add created_by_id column
ALTER TABLE "GraduateVerification_graduatestudent" 
ADD COLUMN "created_by_id" integer NULL;

-- Add updated_by_id column
ALTER TABLE "GraduateVerification_graduatestudent" 
ADD COLUMN "updated_by_id" integer NULL;

-- Add foreign key constraints
ALTER TABLE "GraduateVerification_graduatestudent" 
ADD CONSTRAINT "GraduateVerification_graduatestudent_created_by_id_fkey" 
FOREIGN KEY ("created_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE "GraduateVerification_graduatestudent" 
ADD CONSTRAINT "GraduateVerification_graduatestudent_updated_by_id_fkey" 
FOREIGN KEY ("updated_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;

-- Create indexes for better performance
CREATE INDEX "GraduateVerification_graduatestudent_created_by_id_idx" 
ON "GraduateVerification_graduatestudent" ("created_by_id");

CREATE INDEX "GraduateVerification_graduatestudent_updated_by_id_idx" 
ON "GraduateVerification_graduatestudent" ("updated_by_id");

-- Update migration table to mark this migration as applied
INSERT INTO "django_migrations" ("app", "name", "applied") 
VALUES ('GraduateVerification', '0003_add_audit_trail_fields', NOW());

-- Verify the changes
SELECT 'Audit trail migration completed successfully!' as status;
