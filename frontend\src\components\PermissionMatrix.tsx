import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Shield, 
  Users, 
  Search,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Lock,
  Key
} from 'lucide-react';
import { MENU_PERMISSIONS } from '@/utils/menuPermissions';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Permission Matrix Component
 * Shows all menu items and their required permissions/groups
 */
export const PermissionMatrix: React.FC = () => {
  const { user, hasPermission, hasAnyRole } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterByAccess, setFilterByAccess] = useState<'all' | 'accessible' | 'denied'>('all');

  // Get all menu permissions
  const menuItems = Object.entries(MENU_PERMISSIONS);

  // Filter menu items based on search and access filter
  const filteredItems = menuItems.filter(([menuKey, permission]) => {
    // Search filter
    const matchesSearch = menuKey.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (permission.permissions && permission.permissions.some(p => 
                           p.toLowerCase().includes(searchTerm.toLowerCase()))) ||
                         (permission.groups && permission.groups.some(g => 
                           g.toLowerCase().includes(searchTerm.toLowerCase())));

    if (!matchesSearch) return false;

    // Access filter
    if (filterByAccess === 'all') return true;

    const hasAccess = checkUserAccess(user, permission);
    if (filterByAccess === 'accessible') return hasAccess;
    if (filterByAccess === 'denied') return !hasAccess;

    return true;
  });

  // Check if user has access to a specific menu item
  const checkUserAccess = (user: any, permission: any) => {
    if (!user) return false;
    if (user.is_superuser) return true;

    // Check public access
    if (permission.public) return true;

    // Check superuser requirement
    if (permission.requireSuperuser && !user.is_superuser) return false;

    // Check staff requirement
    if (permission.requireStaff && !user.is_staff) return false;

    // Check permissions
    if (permission.permissions && permission.permissions.length > 0) {
      const hasRequiredPermission = permission.permissions.some((perm: string) => 
        hasPermission(perm)
      );
      if (!hasRequiredPermission) return false;
    }

    // Check groups
    if (permission.groups && permission.groups.length > 0) {
      const hasRequiredGroup = hasAnyRole(permission.groups);
      if (!hasRequiredGroup) return false;
    }

    // If no specific permissions or groups defined and user is staff, deny by default
    if ((!permission.permissions || permission.permissions.length === 0) && 
        (!permission.groups || permission.groups.length === 0) &&
        !permission.allowStaffWithoutGroups &&
        permission.requireStaff &&
        !user.is_superuser) {
      return false;
    }

    return true;
  };

  const getAccessIcon = (hasAccess: boolean) => {
    return hasAccess ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getAccessBadge = (hasAccess: boolean) => {
    return (
      <Badge variant={hasAccess ? "default" : "destructive"} className="text-xs">
        {hasAccess ? "Accessible" : "Denied"}
      </Badge>
    );
  };

  const formatMenuName = (menuKey: string) => {
    return menuKey
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permission Matrix - Strict Access Control
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-yellow-800">Strict Mode Active</h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    Non-superusers can only access pages, menus, and actions explicitly permitted 
                    through groups or permissions. All access is denied by default.
                  </p>
                </div>
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search menus, permissions, or groups..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={filterByAccess === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterByAccess('all')}
                >
                  All ({menuItems.length})
                </Button>
                <Button
                  variant={filterByAccess === 'accessible' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterByAccess('accessible')}
                >
                  Accessible
                </Button>
                <Button
                  variant={filterByAccess === 'denied' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterByAccess('denied')}
                >
                  Denied
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Menu Access Requirements ({filteredItems.length} items)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredItems.map(([menuKey, permission]) => {
              const hasAccess = checkUserAccess(user, permission);
              
              return (
                <div key={menuKey} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      {getAccessIcon(hasAccess)}
                      <div>
                        <h3 className="font-semibold">{formatMenuName(menuKey)}</h3>
                        <p className="text-sm text-muted-foreground">Menu Key: {menuKey}</p>
                      </div>
                    </div>
                    {getAccessBadge(hasAccess)}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Requirements */}
                    <div>
                      <h4 className="text-sm font-medium mb-2">Requirements</h4>
                      <div className="space-y-1">
                        {permission.requireSuperuser && (
                          <Badge variant="destructive" className="text-xs">
                            Superuser Required
                          </Badge>
                        )}
                        {permission.requireStaff && (
                          <Badge variant="secondary" className="text-xs">
                            Staff Required
                          </Badge>
                        )}
                        {permission.public && (
                          <Badge variant="outline" className="text-xs">
                            Public Access
                          </Badge>
                        )}
                        {permission.allowStaffWithoutGroups && (
                          <Badge variant="outline" className="text-xs">
                            Staff Without Groups OK
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Required Permissions */}
                    <div>
                      <h4 className="text-sm font-medium mb-2">Required Permissions</h4>
                      <div className="space-y-1">
                        {permission.permissions && permission.permissions.length > 0 ? (
                          permission.permissions.map((perm, index) => (
                            <div key={index} className="flex items-center gap-1">
                              {hasPermission(perm) ? (
                                <CheckCircle className="h-3 w-3 text-green-500" />
                              ) : (
                                <XCircle className="h-3 w-3 text-red-500" />
                              )}
                              <span className="text-xs font-mono">{perm}</span>
                            </div>
                          ))
                        ) : (
                          <span className="text-xs text-muted-foreground">No specific permissions</span>
                        )}
                      </div>
                    </div>

                    {/* Required Groups */}
                    <div>
                      <h4 className="text-sm font-medium mb-2">Required Groups</h4>
                      <div className="space-y-1">
                        {permission.groups && permission.groups.length > 0 ? (
                          permission.groups.map((group, index) => (
                            <div key={index} className="flex items-center gap-1">
                              {hasAnyRole([group]) ? (
                                <CheckCircle className="h-3 w-3 text-green-500" />
                              ) : (
                                <XCircle className="h-3 w-3 text-red-500" />
                              )}
                              <span className="text-xs">{group}</span>
                            </div>
                          ))
                        ) : (
                          <span className="text-xs text-muted-foreground">No specific groups</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Access Explanation */}
                  {!hasAccess && !user?.is_superuser && (
                    <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-800">
                        <Lock className="h-4 w-4 inline mr-1" />
                        <strong>Access Denied:</strong> You need {
                          permission.permissions && permission.permissions.length > 0 
                            ? `one of the required permissions (${permission.permissions.join(', ')})` 
                            : ''
                        }{
                          permission.permissions && permission.permissions.length > 0 && 
                          permission.groups && permission.groups.length > 0 ? ' OR ' : ''
                        }{
                          permission.groups && permission.groups.length > 0 
                            ? `membership in one of the required groups (${permission.groups.join(', ')})` 
                            : ''
                        }.
                      </p>
                    </div>
                  )}
                </div>
              );
            })}

            {filteredItems.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No menu items match your search criteria.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Access Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {filteredItems.filter(([key, perm]) => checkUserAccess(user, perm)).length}
              </div>
              <div className="text-sm text-muted-foreground">Accessible</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {filteredItems.filter(([key, perm]) => !checkUserAccess(user, perm)).length}
              </div>
              <div className="text-sm text-muted-foreground">Denied</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {filteredItems.length}
              </div>
              <div className="text-sm text-muted-foreground">Total</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PermissionMatrix;
