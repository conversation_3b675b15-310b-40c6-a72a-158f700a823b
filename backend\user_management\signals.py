from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from django.contrib.auth.models import User, Group
from django.core.cache import cache
from .middleware import clear_user_cache, clear_all_user_cache
from .models import Role, UserProfile
import logging

logger = logging.getLogger(__name__)


@receiver(m2m_changed, sender=User.groups.through)
def clear_user_roles_cache(sender, instance, action, pk_set, **kwargs):
    """Clear user cache when groups are added/removed"""
    if action in ['post_add', 'post_remove', 'post_clear']:
        clear_user_cache(instance.id)
        logger.info(f"Cleared cache for user {instance.username} due to group changes")


@receiver(post_save, sender=Role)
@receiver(post_delete, sender=Role)
def clear_role_cache(sender, instance, **kwargs):
    """Clear cache when roles are modified"""
    # Clear cache for all users in this role's group
    if hasattr(instance, 'group') and instance.group:
        user_ids = instance.group.user_set.values_list('id', flat=True)
        for user_id in user_ids:
            clear_user_cache(user_id)
        logger.info(f"Cleared cache for users in role {instance.group.name}")


@receiver(m2m_changed, sender=Group.permissions.through)
def clear_group_permissions_cache(sender, instance, action, pk_set, **kwargs):
    """Clear user cache when group permissions are modified"""
    if action in ['post_add', 'post_remove', 'post_clear']:
        # Clear cache for all users in this group
        user_ids = instance.user_set.values_list('id', flat=True)
        for user_id in user_ids:
            clear_user_cache(user_id)
        logger.info(f"Cleared cache for users in group {instance.name} due to permission changes")


@receiver(post_save, sender=UserProfile)
def clear_user_profile_cache(sender, instance, **kwargs):
    """Clear user cache when profile is updated"""
    clear_user_cache(instance.user.id)
    logger.info(f"Cleared cache for user {instance.user.username} due to profile update")


@receiver(post_save, sender=User)
def handle_user_save(sender, instance, created, **kwargs):
    """Handle user save events"""
    if not created:  # Only for updates, not new users
        clear_user_cache(instance.id)
        logger.info(f"Cleared cache for user {instance.username} due to user update")


# Signal to clear cache when user is activated/deactivated
@receiver(post_save, sender=User)
def handle_user_status_change(sender, instance, **kwargs):
    """Clear cache when user status changes"""
    if hasattr(instance, '_state') and instance._state.adding is False:
        # This is an update, check if is_active changed
        try:
            old_instance = User.objects.get(pk=instance.pk)
            if old_instance.is_active != instance.is_active:
                clear_user_cache(instance.id)
                logger.info(f"Cleared cache for user {instance.username} due to status change")
        except User.DoesNotExist:
            pass
