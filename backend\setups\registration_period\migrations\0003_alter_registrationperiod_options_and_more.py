# Generated by Django 5.2.1 on 2025-06-06 07:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('program', '0001_initial'),
        ('registration_period', '0002_registrationperiod_year'),
        ('year', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='registrationperiod',
            options={'ordering': ['-open_date'], 'verbose_name': 'Registration Period', 'verbose_name_plural': 'Registration Periods'},
        ),
        migrations.AlterField(
            model_name='registrationperiod',
            name='is_active',
            field=models.BooleanField(default=False, help_text='Only one active period per program allowed'),
        ),
        migrations.AlterField(
            model_name='registrationperiod',
            name='open_date',
            field=models.DateTimeField(help_text='Must be in the future'),
        ),
        migrations.AlterUniqueTogether(
            name='registrationperiod',
            unique_together={('program', 'year')},
        ),
    ]
