import React from 'react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { PermissionGate } from './PermissionGate';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { Lock, Eye, Edit, Trash2, Plus, Settings } from 'lucide-react';

interface DynamicButtonProps {
  // Button properties
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  disabled?: boolean;
  
  // Access control
  feature?: string;
  action?: 'view' | 'edit' | 'delete' | 'create' | 'manage';
  permissions?: string[];
  groups?: string[];
  requireAll?: boolean;
  customCheck?: (user: any) => boolean;
  
  // Behavior when access denied
  hideWhenDenied?: boolean;
  showTooltipWhenDenied?: boolean;
  disableWhenDenied?: boolean;
  deniedTooltip?: string;
}

/**
 * Dynamic Button Component
 * Automatically controls button visibility and functionality based on permissions
 */
export const DynamicButton: React.FC<DynamicButtonProps> = ({
  children,
  onClick,
  variant = 'default',
  size = 'default',
  className,
  disabled = false,
  feature,
  action,
  permissions,
  groups,
  requireAll = false,
  customCheck,
  hideWhenDenied = false,
  showTooltipWhenDenied = true,
  disableWhenDenied = true,
  deniedTooltip
}) => {
  const featureAccess = useFeatureAccess(feature || 'default', {
    permissions,
    groups,
    requireAll,
    customCheck
  });

  // Determine if user has access to this specific action
  const hasAccess = (() => {
    if (!action) return true; // No specific action required
    
    switch (action) {
      case 'view':
        return featureAccess.canView;
      case 'edit':
        return featureAccess.canEdit;
      case 'delete':
        return featureAccess.canDelete;
      case 'create':
        return featureAccess.canCreate;
      case 'manage':
        return featureAccess.canManage;
      default:
        return true;
    }
  })();

  // If access denied and should hide, return nothing
  if (!hasAccess && hideWhenDenied) {
    return null;
  }

  // Determine button state
  const isDisabled = disabled || (!hasAccess && disableWhenDenied);
  const tooltipText = !hasAccess 
    ? (deniedTooltip || featureAccess.deniedReason || 'Access denied')
    : undefined;

  const button = (
    <Button
      onClick={hasAccess ? onClick : undefined}
      variant={variant}
      size={size}
      className={className}
      disabled={isDisabled}
    >
      {children}
    </Button>
  );

  // Show tooltip when access denied
  if (!hasAccess && showTooltipWhenDenied && tooltipText) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {button}
          </TooltipTrigger>
          <TooltipContent>
            <div className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              <span>{tooltipText}</span>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return button;
};

// Convenience components for common actions
export const ViewButton: React.FC<Omit<DynamicButtonProps, 'action'>> = (props) => (
  <DynamicButton {...props} action="view" variant="outline">
    <Eye className="h-4 w-4 mr-2" />
    {props.children || 'View'}
  </DynamicButton>
);

export const EditButton: React.FC<Omit<DynamicButtonProps, 'action'>> = (props) => (
  <DynamicButton {...props} action="edit" variant="outline">
    <Edit className="h-4 w-4 mr-2" />
    {props.children || 'Edit'}
  </DynamicButton>
);

export const DeleteButton: React.FC<Omit<DynamicButtonProps, 'action'>> = (props) => (
  <DynamicButton {...props} action="delete" variant="destructive">
    <Trash2 className="h-4 w-4 mr-2" />
    {props.children || 'Delete'}
  </DynamicButton>
);

export const CreateButton: React.FC<Omit<DynamicButtonProps, 'action'>> = (props) => (
  <DynamicButton {...props} action="create" variant="default">
    <Plus className="h-4 w-4 mr-2" />
    {props.children || 'Create'}
  </DynamicButton>
);

export const ManageButton: React.FC<Omit<DynamicButtonProps, 'action'>> = (props) => (
  <DynamicButton {...props} action="manage" variant="outline">
    <Settings className="h-4 w-4 mr-2" />
    {props.children || 'Manage'}
  </DynamicButton>
);

export default DynamicButton;
