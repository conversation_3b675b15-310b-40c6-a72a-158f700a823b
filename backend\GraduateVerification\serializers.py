from rest_framework import serializers
from .models import GraduateStudent, VerificationCollege, VerificationDepartment, VerificationFieldOfStudy, VerificationProgram, AdmissionClassification

# Organization model is not defined in this app
'''
class OrganizationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = '__all__'
'''

class VerificationCollegeSerializer(serializers.ModelSerializer):
    class Meta:
        model = VerificationCollege
        fields = '__all__'
        ref_name = 'VerificationCollege'

class VerificationDepartmentSerializer(serializers.ModelSerializer):
    college_name = serializers.ReadOnlyField(source='college.name')

    class Meta:
        model = VerificationDepartment
        fields = '__all__'
        ref_name = 'VerificationDepartment'

class VerificationFieldOfStudySerializer(serializers.ModelSerializer):
    department_name = serializers.ReadOnlyField(source='department.name')
    college_name = serializers.ReadOnlyField(source='department.college.name')

    class Meta:
        model = VerificationFieldOfStudy
        fields = '__all__'
        ref_name = 'VerificationFieldOfStudy'

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Format duration as "X years" if it exists
        if representation.get('duration'):
            representation['duration_display'] = f"{representation['duration']} years"
        else:
            representation['duration_display'] = None
        return representation

class VerificationProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = VerificationProgram
        fields = '__all__'
        ref_name = 'VerificationProgram'

    def validate_name(self, value):
        # Check if the name already exists in the database
        request = self.context.get('request')
        if request and request.method == 'POST':
            # For new programs, check if name exists
            if VerificationProgram.objects.filter(name__iexact=value).exists():
                raise serializers.ValidationError(f"The program name '{value}' is already in use. Program names must be unique.")
        elif request and request.method in ['PUT', 'PATCH']:
            # For updates, check if name exists for other programs
            instance_id = self.instance.id if self.instance else None
            if instance_id and VerificationProgram.objects.exclude(id=instance_id).filter(name__iexact=value).exists():
                raise serializers.ValidationError(f"The program name '{value}' is already in use. Program names must be unique.")
        return value

    def validate_code(self, value):
        # Convert code to uppercase
        value = value.upper()

        # Check if the code already exists in the database
        request = self.context.get('request')
        if request and request.method == 'POST':
            # For new programs, check if code exists
            if VerificationProgram.objects.filter(code__iexact=value).exists():
                raise serializers.ValidationError(f"The program code '{value}' is already in use. Program codes must be unique.")
        elif request and request.method in ['PUT', 'PATCH']:
            # For updates, check if code exists for other programs
            instance_id = self.instance.id if self.instance else None
            if instance_id and VerificationProgram.objects.exclude(id=instance_id).filter(code__iexact=value).exists():
                raise serializers.ValidationError(f"The program code '{value}' is already in use. Program codes must be unique.")
        return value

class VerificationAdmissionClassificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdmissionClassification
        fields = '__all__'
        ref_name = 'VerificationAdmissionClassification'

class GraduateVerificationSerializer(serializers.ModelSerializer):
    college_name = serializers.ReadOnlyField(source='college.name')
    department_name = serializers.ReadOnlyField(source='department.name')
    field_of_study_name = serializers.ReadOnlyField(source='field_of_study.name')
    program_name = serializers.ReadOnlyField(source='program.name')
    admission_classification_name = serializers.ReadOnlyField(source='admission_classification.name')
    full_name = serializers.SerializerMethodField()

    # Audit trail fields
    created_by_name = serializers.SerializerMethodField()
    updated_by_name = serializers.SerializerMethodField()
    deleted_by_name = serializers.SerializerMethodField()

    class Meta:
        model = GraduateStudent
        fields = '__all__'
        read_only_fields = ['created_by', 'updated_by', 'created_at', 'updated_at']
        ref_name = 'GraduateVerification'

    def get_full_name(self, obj):
        return obj.get_full_name()

    def get_created_by_name(self, obj):
        try:
            if hasattr(obj, 'created_by') and obj.created_by:
                return f"{obj.created_by.first_name} {obj.created_by.last_name}".strip() or obj.created_by.username
        except (AttributeError, Exception):
            pass
        return None

    def get_updated_by_name(self, obj):
        try:
            if hasattr(obj, 'updated_by') and obj.updated_by:
                return f"{obj.updated_by.first_name} {obj.updated_by.last_name}".strip() or obj.updated_by.username
        except (AttributeError, Exception):
            pass
        return None

    def get_deleted_by_name(self, obj):
        """Get the name of the user who soft deleted this record"""
        try:
            if hasattr(obj, 'deleted_by') and obj.deleted_by:
                return f"{obj.deleted_by.first_name} {obj.deleted_by.last_name}".strip() or obj.deleted_by.username
        except (AttributeError, Exception):
            pass
        return None

class GraduateVerificationDetailSerializer(GraduateVerificationSerializer):
    college = VerificationCollegeSerializer(read_only=True)
    department = VerificationDepartmentSerializer(read_only=True)
    field_of_study = VerificationFieldOfStudySerializer(read_only=True)
    program = VerificationProgramSerializer(read_only=True)
    admission_classification = VerificationAdmissionClassificationSerializer(read_only=True)

    class Meta:
        model = GraduateStudent
        fields = '__all__'
        ref_name = 'GraduateVerificationDetail'
