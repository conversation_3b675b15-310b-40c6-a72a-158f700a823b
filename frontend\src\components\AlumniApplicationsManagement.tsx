import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Search,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  FileText,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  Plus,
  Upload,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  BarChart3,
  TrendingUp,
  PieChart,
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { alumniApplicationsAPI, AlumniApplication, AlumniApplicationMini, ApplicationListParams, ApplicationStatistics } from '@/services/alumniApplicationsAPI';
import AlumniApplicationDetails from './AlumniApplicationDetails';
import AlumniApplicationForm from './AlumniApplicationForm';


const AlumniApplicationsManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState('statistics');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [paymentFilter, setPaymentFilter] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState<AlumniApplication | AlumniApplicationMini | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingApplication, setEditingApplication] = useState<AlumniApplication | AlumniApplicationMini | null>(null);
  const [statistics, setStatistics] = useState<ApplicationStatistics | null>(null);
  const [statisticsLoading, setStatisticsLoading] = useState(true);

  // Utility functions for safe calculations
  const safeNumber = (value: number | undefined | null): number => {
    return typeof value === 'number' && !isNaN(value) && isFinite(value) && value >= 0 ? value : 0;
  };

  const safePercentage = (numerator: number | undefined | null, denominator: number | undefined | null): number => {
    const safeNum = safeNumber(numerator);
    const safeDenom = safeNumber(denominator);
    if (safeDenom === 0) return 0;
    const result = (safeNum / safeDenom) * 100;
    return Math.round(Math.min(Math.max(result, 0), 100)); // Clamp between 0-100
  };

  const formatCurrency = (amount: number): string => {
    const safeAmount = safeNumber(amount);
    return `${safeAmount.toLocaleString()} ETB`;
  };

  const validateStatistics = (stats: ApplicationStatistics | null): boolean => {
    if (!stats) return false;

    // Check if all required properties exist and are valid numbers
    const requiredNumbers = [
      stats.total_requests,
      stats.form1_count,
      stats.form2_count,
      stats.recent_applications
    ];

    const statusNumbers = stats.by_status ? [
      stats.by_status.pending,
      stats.by_status.processing,
      stats.by_status.completed,
      stats.by_status.rejected
    ] : [];

    const paymentNumbers = stats.by_payment_status ? [
      stats.by_payment_status.paid,
      stats.by_payment_status.unpaid,
      stats.by_payment_status.pending
    ] : [];

    const allNumbers = [...requiredNumbers, ...statusNumbers, ...paymentNumbers];

    return allNumbers.every(num => typeof num === 'number' && !isNaN(num) && isFinite(num) && num >= 0);
  };

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const pageSize = itemsPerPage;

  // Inline editing state
  const [editingStatus, setEditingStatus] = useState<{[key: string]: boolean}>({});
  const [editingPayment, setEditingPayment] = useState<{[key: string]: boolean}>({});

  // Delete confirmation state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [applicationToDelete, setApplicationToDelete] = useState<{id: string, name: string} | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const queryClient = useQueryClient();

  // Handle URL parameters for form selection
  useEffect(() => {
    const formParam = searchParams.get('form');
    if (formParam === 'complete') {
      setActiveTab('form1');
      setShowForm(true);
      setEditingApplication(null);
    } else if (formParam === 'simplified') {
      setActiveTab('form2');
      setShowForm(true);
      setEditingApplication(null);
    }
  }, [searchParams]);

  // Fetch statistics with comprehensive validation
  const fetchStatistics = async () => {
    try {
      setStatisticsLoading(true);
      const response = await alumniApplicationsAPI.getStatistics();

      // Validate response structure
      if (!response || !response.data) {
        throw new Error('Invalid response structure from statistics API');
      }

      const statsData = response.data;

      // Log the received data for debugging
      console.log('Received statistics data:', statsData);

      // Always sanitize the data to ensure safe display
      const safeStats: ApplicationStatistics = {
        total_requests: safeNumber(statsData.total_requests),
        form1_count: safeNumber(statsData.form1_count),
        form2_count: safeNumber(statsData.form2_count),
        recent_applications: safeNumber(statsData.recent_applications),
        by_status: {
          pending: safeNumber(statsData.by_status?.pending),
          processing: safeNumber(statsData.by_status?.processing),
          completed: safeNumber(statsData.by_status?.completed),
          rejected: safeNumber(statsData.by_status?.rejected)
        },
        by_payment_status: {
          paid: safeNumber(statsData.by_payment_status?.paid),
          unpaid: safeNumber(statsData.by_payment_status?.unpaid),
          pending: safeNumber(statsData.by_payment_status?.pending)
        },
        time_based: statsData.time_based ? {
          today: safeNumber(statsData.time_based.today),
          three_days: safeNumber(statsData.time_based.three_days),
          one_week: safeNumber(statsData.time_based.one_week),
          one_month: safeNumber(statsData.time_based.one_month)
        } : {
          today: 0,
          three_days: 0,
          one_week: 0,
          one_month: 0
        },
        revenue: statsData.revenue ? {
          paid_revenue: safeNumber(statsData.revenue.paid_revenue),
          pending_revenue: safeNumber(statsData.revenue.pending_revenue),
          unpaid_revenue: safeNumber(statsData.revenue.unpaid_revenue),
          total_potential_revenue: safeNumber(statsData.revenue.total_potential_revenue)
        } : {
          paid_revenue: 0,
          pending_revenue: 0,
          unpaid_revenue: 0,
          total_potential_revenue: 0
        }
      };

      console.log('Processed safe statistics:', safeStats);
      setStatistics(safeStats);
    } catch (error) {
      console.error('Error fetching statistics:', error);

      // Set fallback statistics to prevent UI crashes
      const fallbackStats: ApplicationStatistics = {
        total_requests: 0,
        form1_count: 0,
        form2_count: 0,
        recent_applications: 0,
        by_status: {
          pending: 0,
          processing: 0,
          completed: 0,
          rejected: 0
        },
        by_payment_status: {
          paid: 0,
          unpaid: 0,
          pending: 0
        },
        time_based: {
          today: 0,
          three_days: 0,
          one_week: 0,
          one_month: 0
        },
        revenue: {
          paid_revenue: 0,
          pending_revenue: 0,
          unpaid_revenue: 0,
          total_potential_revenue: 0
        }
      };
      setStatistics(fallbackStats);
    } finally {
      setStatisticsLoading(false);
    }
  };

  // Load statistics on component mount
  React.useEffect(() => {
    fetchStatistics();
  }, []);

  // Query parameters
  const getQueryParams = (): ApplicationListParams => ({
    page: currentPage,
    page_size: pageSize,
    search: searchTerm || undefined,
    application_status: statusFilter && statusFilter !== 'all' ? statusFilter : undefined,
    payment_status: paymentFilter && paymentFilter !== 'all' ? paymentFilter : undefined,
    ordering: '-created_at'
  });

  // Fetch Form1 applications
  const { 
    data: form1Data, 
    isLoading: form1Loading, 
    error: form1Error,
    refetch: refetchForm1 
  } = useQuery({
    queryKey: ['alumni-applications-form1', getQueryParams()],
    queryFn: () => alumniApplicationsAPI.getApplications(getQueryParams()),
    enabled: activeTab === 'form1'
  });

  // Fetch Form2 applications
  const { 
    data: form2Data, 
    isLoading: form2Loading, 
    error: form2Error,
    refetch: refetchForm2 
  } = useQuery({
    queryKey: ['alumni-applications-form2', getQueryParams()],
    queryFn: () => alumniApplicationsAPI.getMiniApplications(getQueryParams()),
    enabled: activeTab === 'form2'
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => {
      if (activeTab === 'form1') {
        return alumniApplicationsAPI.deleteApplication(id);
      } else {
        return alumniApplicationsAPI.deleteMiniApplication(id);
      }
    },
    onSuccess: () => {
      toast.success('Application deleted successfully');
      queryClient.invalidateQueries({ queryKey: [`alumni-applications-${activeTab}`] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete application');
    }
  });

  // Status update mutation
  const statusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: any }) => {
      if (activeTab === 'form1') {
        return alumniApplicationsAPI.updateApplicationStatus(id, status);
      } else {
        return alumniApplicationsAPI.updateMiniApplicationStatus(id, status);
      }
    },
    onSuccess: () => {
      toast.success('Status updated successfully');
      queryClient.invalidateQueries({ queryKey: [`alumni-applications-${activeTab}`] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update status');
    }
  });

  const currentData = activeTab === 'form1' ? form1Data : form2Data;
  const isLoading = activeTab === 'form1' ? form1Loading : form2Loading;
  const error = activeTab === 'form1' ? form1Error : form2Error;

  // Try different response structures with better error handling
  let applications = [];
  let totalCount = 0;

  try {
    if (currentData) {
      // Handle different API response structures
      if (Array.isArray(currentData)) {
        applications = currentData;
        totalCount = currentData.length;
      } else if (currentData.data && Array.isArray(currentData.data.results)) {
        applications = currentData.data.results;
        totalCount = currentData.data.count || currentData.data.results.length;
      } else if (currentData.results && Array.isArray(currentData.results)) {
        applications = currentData.results;
        totalCount = currentData.count || currentData.results.length;
      } else if (currentData.data && Array.isArray(currentData.data)) {
        applications = currentData.data;
        totalCount = currentData.data.length;
      } else {
        console.warn('Unexpected API response structure:', currentData);
        applications = [];
        totalCount = 0;
      }
    }
  } catch (error) {
    console.error('Error processing applications data:', error);
    applications = [];
    totalCount = 0;
  }

  const totalPages = Math.ceil(totalCount / pageSize);

  // Debug logging (can be removed in production)
  console.log('Alumni Applications Debug:', {
    activeTab,
    currentData: currentData ? 'Data loaded' : 'No data',
    applicationsCount: applications.length,
    isLoading,
    error: error ? 'Error present' : 'No error'
  });

  // Applications loaded successfully
  if (applications.length > 0) {
    console.log(`Successfully loaded ${applications.length} applications`);
  }

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    if (activeTab === 'form1') {
      refetchForm1();
    } else {
      refetchForm2();
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    if (activeTab === 'form1') {
      refetchForm1();
    } else {
      refetchForm2();
    }
  };

  // Handle view details
  const handleViewDetails = (application: AlumniApplication | AlumniApplicationMini) => {
    setSelectedApplication(application);
    setShowDetails(true);
  };

  // Handle edit - fetch full application details
  const handleEdit = async (application: AlumniApplication | AlumniApplicationMini) => {
    try {
      // Fetch full application details for editing
      let fullApplication;
      if (activeTab === 'form1') {
        fullApplication = await alumniApplicationsAPI.getApplication(application.id);
      } else {
        fullApplication = await alumniApplicationsAPI.getMiniApplication(application.id);
      }

      setEditingApplication(fullApplication.data);
      setShowForm(true);
    } catch (error) {
      console.error('Error fetching application details for editing:', error);
      toast.error('Failed to load application details for editing');
    }
  };



  // Handle delete - open confirmation dialog
  const handleDelete = (application: AlumniApplication | AlumniApplicationMini) => {
    const applicantName = `${application.first_name} ${application.father_name} ${application.last_name}`;
    setApplicationToDelete({
      id: application.id,
      name: applicantName
    });
    setDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!applicationToDelete) return;

    setIsDeleting(true);
    try {
      await deleteMutation.mutateAsync(applicationToDelete.id);
      setDeleteDialogOpen(false);
      setApplicationToDelete(null);
    } catch (error) {
      // Error is handled by the mutation's onError
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle status update
  const handleStatusUpdate = (id: string, field: string, value: string) => {
    statusMutation.mutate({
      id,
      status: { [field]: value }
    });
  };

  // Handle inline status editing
  const handleInlineStatusUpdate = async (id: string, field: 'application_status' | 'payment_status', value: string) => {
    try {
      console.log('Updating status:', { id, field, value });

      // Determine which API to use based on current tab
      const isForm1 = activeTab === 'form1';

      // Create the update data
      const updateData = { [field]: value };

      console.log('Sending update data:', updateData);

      // Call the appropriate API
      if (isForm1) {
        await alumniApplicationsAPI.updateApplicationStatus(id, updateData);
      } else {
        await alumniApplicationsAPI.updateMiniApplicationStatus(id, updateData);
      }

      // Refresh both the list data and statistics
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['alumni-applications'] }),
        queryClient.invalidateQueries({ queryKey: ['alumni-applications-form1'] }),
        queryClient.invalidateQueries({ queryKey: ['alumni-applications-form2'] }),
        fetchStatistics() // Refresh statistics immediately
      ]);

      // Exit editing mode
      if (field === 'application_status') {
        setEditingStatus(prev => ({ ...prev, [id]: false }));
      } else {
        setEditingPayment(prev => ({ ...prev, [id]: false }));
      }

      // Show success message
      toast.success(`${field === 'application_status' ? 'Application' : 'Payment'} status updated successfully`);
    } catch (error: any) {
      console.error('Error updating status:', error);
      console.error('Error response:', error.response);
      console.error('Error response data:', error.response?.data);

      // Show error message
      const errorMessage = error.response?.data?.detail ||
                          error.response?.data?.message ||
                          error.response?.data?.error ||
                          (error.response?.data && typeof error.response.data === 'object'
                            ? JSON.stringify(error.response.data)
                            : error.response?.data) ||
                          `Failed to update ${field === 'application_status' ? 'application' : 'payment'} status`;
      toast.error(errorMessage);

      // Keep editing mode active on error
    }
  };

  // Toggle editing mode
  const toggleStatusEditing = (id: string, field: 'application_status' | 'payment_status') => {
    // Check if we're trying to edit application status and payment is not completed
    if (field === 'application_status') {
      // Find the application to check payment status
      const currentApplications = activeTab === 'form1' ? form1Data?.data?.results : form2Data?.data?.results;
      const application = currentApplications?.find((app: any) => app.id === id);

      if (application && application.payment_status !== 'Completed') {
        // Don't allow editing if payment is not completed
        return;
      }

      setEditingStatus(prev => ({ ...prev, [id]: !prev[id] }));
    } else {
      setEditingPayment(prev => ({ ...prev, [id]: !prev[id] }));
    }
  };

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Get completion status icon
  const getCompletionIcon = (status: any) => {
    if (!status) {
      return <XCircle className="h-4 w-4 text-gray-400" />;
    }
    if (status.is_complete) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else if (status.completion_percentage >= 50) {
      return <Clock className="h-4 w-4 text-yellow-500" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  // Inline Status Editor Component
  const InlineStatusEditor = ({
    applicationId,
    currentValue,
    field,
    isEditing,
    onToggleEdit,
    onUpdate,
    paymentStatus
  }: {
    applicationId: string;
    currentValue: string;
    field: 'application_status' | 'payment_status';
    isEditing: boolean;
    onToggleEdit: () => void;
    onUpdate: (value: string) => void;
    paymentStatus?: string;
  }) => {
    const statusOptions = field === 'application_status'
      ? ['Pending', 'On Review', 'Processing', 'Complete']
      : ['Pending', 'Initiated', 'Processing', 'Completed', 'Failed', 'Refunded'];

    // Check if application status editing should be disabled
    const isApplicationStatusDisabled = field === 'application_status' && paymentStatus !== 'Completed';

    const getStatusColor = (status: string) => {
      if (field === 'application_status') {
        switch (status) {
          case 'Complete':
            return 'bg-green-100 text-green-800 border-green-200';
          case 'Processing':
            return 'bg-blue-100 text-blue-800 border-blue-200';
          case 'On Review':
            return 'bg-yellow-100 text-yellow-800 border-yellow-200';
          case 'Pending':
            return 'bg-gray-100 text-gray-800 border-gray-200';
          default:
            return 'bg-gray-100 text-gray-800 border-gray-200';
        }
      } else {
        switch (status) {
          case 'Completed':
            return 'bg-green-100 text-green-800 border-green-200';
          case 'Failed':
            return 'bg-red-100 text-red-800 border-red-200';
          case 'Processing':
            return 'bg-blue-100 text-blue-800 border-blue-200';
          case 'Initiated':
            return 'bg-yellow-100 text-yellow-800 border-yellow-200';
          case 'Refunded':
            return 'bg-purple-100 text-purple-800 border-purple-200';
          case 'Pending':
            return 'bg-gray-100 text-gray-800 border-gray-200';
          default:
            return 'bg-gray-100 text-gray-800 border-gray-200';
        }
      }
    };

    const getStatusDotColor = (status: string) => {
      if (field === 'application_status') {
        switch (status) {
          case 'Complete':
            return 'bg-green-600';
          case 'Processing':
            return 'bg-blue-600';
          case 'On Review':
            return 'bg-yellow-600';
          case 'Pending':
            return 'bg-gray-600';
          default:
            return 'bg-gray-600';
        }
      } else {
        switch (status) {
          case 'Completed':
            return 'bg-green-600';
          case 'Failed':
            return 'bg-red-600';
          case 'Processing':
            return 'bg-blue-600';
          case 'Initiated':
            return 'bg-yellow-600';
          case 'Refunded':
            return 'bg-purple-600';
          case 'Pending':
            return 'bg-gray-600';
          default:
            return 'bg-gray-600';
        }
      }
    };

    if (isEditing) {
      return (
        <div className="min-w-[140px]">
          <Select
            value={currentValue}
            onValueChange={(value) => {
              onUpdate(value);
            }}
          >
            <SelectTrigger className="h-8 text-xs border-blue-300 focus:border-blue-500">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option} value={option} className="text-xs">
                  <div className="flex items-center">
                    <span className={`w-2 h-2 rounded-full mr-2 ${getStatusDotColor(option)}`}></span>
                    {option}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    }

    return (
      <button
        onClick={isApplicationStatusDisabled ? undefined : onToggleEdit}
        className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border shadow-sm transition-all duration-200 ${
          isApplicationStatusDisabled
            ? 'cursor-not-allowed opacity-60 bg-gray-100 text-gray-500 border-gray-200'
            : `cursor-pointer hover:shadow-md ${getStatusColor(currentValue)}`
        }`}
        title={isApplicationStatusDisabled ? "Application status can only be edited after payment is completed" : "Click to edit status"}
      >
        <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
          isApplicationStatusDisabled ? 'bg-gray-400' : getStatusDotColor(currentValue)
        }`}></span>
        {currentValue}
        {isApplicationStatusDisabled && (
          <span className="ml-1 text-xs">🔒</span>
        )}
      </button>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Alumni Applications Management</CardTitle>
                <CardDescription className="mt-1">
                  Manage alumni application requests for transcripts, certificates, and other services
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                className="border-blue-200 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 shadow-sm">
              <TabsTrigger
                value="statistics"
                className="flex items-center gap-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200"
              >
                <BarChart3 className="h-4 w-4" />
                Statistics Dashboard
              </TabsTrigger>
              <TabsTrigger
                value="form1"
                className="flex items-center gap-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200"
              >
                <FileText className="h-4 w-4" />
                Complete Applications
                {form1Data?.data?.count > 0 && (
                  <span className="ml-1 px-2 py-0.5 text-xs font-medium bg-white/20 rounded-full">
                    {form1Data.data.count}
                  </span>
                )}
              </TabsTrigger>
              <TabsTrigger
                value="form2"
                className="flex items-center gap-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200"
              >
                <FileText className="h-4 w-4" />
                Simplified Applications
                {form2Data?.data?.count > 0 && (
                  <span className="ml-1 px-2 py-0.5 text-xs font-medium bg-white/20 rounded-full">
                    {form2Data.data.count}
                  </span>
                )}
              </TabsTrigger>
            </TabsList>

            {/* Error Display */}
            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  <div>
                    <h4 className="text-red-800 font-medium">Error loading applications</h4>
                    <p className="text-red-600 text-sm mt-1">
                      {error?.message || 'Failed to load applications. Please try again.'}
                    </p>
                  </div>
                </div>
              </div>
            )}



            <TabsContent value="statistics" className="space-y-6">
              {/* Statistics Dashboard */}
              <div className="space-y-6">
                {/* Basic Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {statisticsLoading ? (
                    // Loading skeleton
                    [...Array(4)].map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-4">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                        </CardContent>
                      </Card>
                    ))
                  ) : statistics && validateStatistics(statistics) ? (
                    <>
                      {/* Total Requests */}
                      <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-blue-600">Total Requests</p>
                              <p className="text-2xl font-bold text-blue-900">{safeNumber(statistics?.total_requests)}</p>
                            </div>
                            <FileText className="h-8 w-8 text-blue-500" />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Pending */}
                      <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-yellow-600">Pending</p>
                              <p className="text-2xl font-bold text-yellow-900">{safeNumber(statistics?.by_status?.pending)}</p>
                            </div>
                            <Clock className="h-8 w-8 text-yellow-500" />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Processing */}
                      <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-orange-600">Processing</p>
                              <p className="text-2xl font-bold text-orange-900">{safeNumber(statistics?.by_status?.processing)}</p>
                            </div>
                            <RefreshCw className="h-8 w-8 text-orange-500" />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Completed */}
                      <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-green-600">Completed</p>
                              <p className="text-2xl font-bold text-green-900">{safeNumber(statistics?.by_status?.completed)}</p>
                            </div>
                            <CheckCircle className="h-8 w-8 text-green-500" />
                          </div>
                        </CardContent>
                      </Card>
                    </>
                  ) : (
                    // Error state - show empty cards
                    [...Array(4)].map((_, i) => (
                      <Card key={i} className="border-gray-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-gray-500">--</p>
                              <p className="text-2xl font-bold text-gray-400">0</p>
                            </div>
                            <XCircle className="h-8 w-8 text-gray-400" />
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>

                {/* Enhanced Financial Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {statisticsLoading ? (
                    // Loading skeleton
                    [...Array(4)].map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-6">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                          <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                        </CardContent>
                      </Card>
                    ))
                  ) : statistics && validateStatistics(statistics) ? (
                    <>
                      {/* Total Revenue */}
                      <Card className="border-emerald-200 bg-gradient-to-br from-emerald-50 to-emerald-100 shadow-lg">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-emerald-600">Total Revenue</p>
                              <p className="text-3xl font-bold text-emerald-900">
                                {formatCurrency(safeNumber(statistics?.revenue?.paid_revenue))}
                              </p>
                              <p className="text-xs text-emerald-600 mt-1">
                                {safeNumber(statistics?.by_payment_status?.paid)} paid applications
                              </p>
                            </div>
                            <div className="h-10 w-10 bg-emerald-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-sm">ETB</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Pending Revenue */}
                      <Card className="border-amber-200 bg-gradient-to-br from-amber-50 to-amber-100 shadow-lg">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-amber-600">Pending Revenue</p>
                              <p className="text-3xl font-bold text-amber-900">
                                {formatCurrency(safeNumber(statistics?.revenue?.pending_revenue))}
                              </p>
                              <p className="text-xs text-amber-600 mt-1">
                                {safeNumber(statistics?.by_payment_status?.pending)} pending applications
                              </p>
                            </div>
                            <Clock className="h-10 w-10 text-amber-500" />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Completion Rate */}
                      <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-blue-600">Completion Rate</p>
                              <p className="text-3xl font-bold text-blue-900">
                                {safePercentage(statistics?.by_status?.completed, statistics?.total_requests)}%
                              </p>
                              <p className="text-xs text-blue-600 mt-1">
                                {safeNumber(statistics?.by_status?.completed)} of {safeNumber(statistics?.total_requests)} completed
                              </p>
                            </div>
                            <TrendingUp className="h-10 w-10 text-blue-500" />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Recent Applications */}
                      <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-purple-600">Recent (30 days)</p>
                              <p className="text-3xl font-bold text-purple-900">
                                {safeNumber(statistics?.recent_applications)}
                              </p>
                              <p className="text-xs text-purple-600 mt-1">
                                New applications this month
                              </p>
                            </div>
                            <FileText className="h-10 w-10 text-purple-500" />
                          </div>
                        </CardContent>
                      </Card>
                    </>
                  ) : (
                    // Error state
                    [...Array(4)].map((_, i) => (
                      <Card key={i} className="border-gray-200">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-gray-500">--</p>
                              <p className="text-3xl font-bold text-gray-400">0</p>
                              <p className="text-xs text-gray-400 mt-1">No data available</p>
                            </div>
                            <XCircle className="h-10 w-10 text-gray-400" />
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>

                {/* Charts Section */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Application Status Chart */}
                  <Card className="shadow-lg">
                    <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                      <CardTitle className="text-lg text-[#1a73c0] flex items-center">
                        <PieChart className="h-5 w-5 mr-2" />
                        Application Status Distribution
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6">
                      {statisticsLoading ? (
                        <div className="animate-pulse space-y-4">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-32 bg-gray-200 rounded"></div>
                        </div>
                      ) : statistics && validateStatistics(statistics) ? (
                        <div className="space-y-4">
                          {/* Status Bars */}
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Pending</span>
                              <span className="text-sm text-gray-600">{safeNumber(statistics?.by_status?.pending)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-yellow-500 h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${safePercentage(statistics?.by_status?.pending, statistics?.total_requests)}%`
                                }}
                              ></div>
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Processing</span>
                              <span className="text-sm text-gray-600">{safeNumber(statistics?.by_status?.processing)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-orange-500 h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${safePercentage(statistics?.by_status?.processing, statistics?.total_requests)}%`
                                }}
                              ></div>
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Completed</span>
                              <span className="text-sm text-gray-600">{safeNumber(statistics?.by_status?.completed)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-green-500 h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${safePercentage(statistics?.by_status?.completed, statistics?.total_requests)}%`
                                }}
                              ></div>
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Rejected</span>
                              <span className="text-sm text-gray-600">{safeNumber(statistics?.by_status?.rejected)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-red-500 h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${safePercentage(statistics?.by_status?.rejected, statistics?.total_requests)}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <XCircle className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                          <p>No data available</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Payment Status Chart */}
                  <Card className="shadow-lg">
                    <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
                      <CardTitle className="text-lg text-emerald-700 flex items-center">
                        <div className="h-5 w-5 mr-2 bg-emerald-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-xs">ETB</span>
                        </div>
                        Payment Status Distribution
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6">
                      {statisticsLoading ? (
                        <div className="animate-pulse space-y-4">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div className="h-32 bg-gray-200 rounded"></div>
                        </div>
                      ) : statistics && validateStatistics(statistics) ? (
                        <div className="space-y-4">
                          {/* Payment Bars */}
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Paid</span>
                              <span className="text-sm text-gray-600">{safeNumber(statistics?.by_payment_status?.paid)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-green-500 h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${safePercentage(statistics?.by_payment_status?.paid, statistics?.total_requests)}%`
                                }}
                              ></div>
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Unpaid</span>
                              <span className="text-sm text-gray-600">{safeNumber(statistics?.by_payment_status?.unpaid)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-red-500 h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${safePercentage(statistics?.by_payment_status?.unpaid, statistics?.total_requests)}%`
                                }}
                              ></div>
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-700">Pending Payment</span>
                              <span className="text-sm text-gray-600">{safeNumber(statistics?.by_payment_status?.pending)}</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-yellow-500 h-2 rounded-full transition-all duration-500"
                                style={{
                                  width: `${safePercentage(statistics?.by_payment_status?.pending, statistics?.total_requests)}%`
                                }}
                              ></div>
                            </div>
                          </div>


                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <XCircle className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                          <p>No data available</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Time-based Alumni Applications */}
                <Card className="shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 border-b">
                    <CardTitle className="text-lg text-indigo-700 flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2" />
                      Recent Alumni Applications
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {/* Today */}
                      <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-blue-600">Today</p>
                              <p className="text-2xl font-bold text-blue-900">
                                {statistics?.time_based?.today || 0}
                              </p>
                              <p className="text-xs text-blue-600 mt-1">
                                applications submitted
                              </p>
                            </div>
                            <div className="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-xs">1D</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 3 Days */}
                      <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100 shadow-lg">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-green-600">3 Days</p>
                              <p className="text-2xl font-bold text-green-900">
                                {statistics?.time_based?.three_days || 0}
                              </p>
                              <p className="text-xs text-green-600 mt-1">
                                applications submitted
                              </p>
                            </div>
                            <div className="h-8 w-8 bg-green-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-xs">3D</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 1 Week */}
                      <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 shadow-lg">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-purple-600">1 Week</p>
                              <p className="text-2xl font-bold text-purple-900">
                                {statistics?.time_based?.one_week || 0}
                              </p>
                              <p className="text-xs text-purple-600 mt-1">
                                applications submitted
                              </p>
                            </div>
                            <div className="h-8 w-8 bg-purple-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-xs">1W</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 1 Month */}
                      <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-orange-600">1 Month</p>
                              <p className="text-2xl font-bold text-orange-900">
                                {statistics?.time_based?.one_month || 0}
                              </p>
                              <p className="text-xs text-orange-600 mt-1">
                                applications submitted
                              </p>
                            </div>
                            <div className="h-8 w-8 bg-orange-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-xs">1M</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="form1" className="space-y-4">
              {/* Search and Filters */}
              <div className="mb-6 space-y-4">
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
                  <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                    <Search className="h-4 w-4 mr-2" />
                    Search & Filter Complete Applications
                  </h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                      <Input
                        id="search-applications"
                        name="search"
                        placeholder="Search applications..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                      />
                    </div>
                    <div className="flex gap-3">
                      <Select value={statusFilter} onValueChange={setStatusFilter} name="status-filter">
                        <SelectTrigger className="w-[150px] border-blue-200 focus:ring-blue-400 shadow-sm" id="status-filter">
                          <SelectValue placeholder="Filter by Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="Pending">Pending</SelectItem>
                          <SelectItem value="On Review">On Review</SelectItem>
                          <SelectItem value="Processing">Processing</SelectItem>
                          <SelectItem value="Complete">Complete</SelectItem>
                        </SelectContent>
                      </Select>

                      <Select value={paymentFilter} onValueChange={setPaymentFilter} name="payment-filter">
                        <SelectTrigger className="w-[150px] border-blue-200 focus:ring-blue-400 shadow-sm" id="payment-filter">
                          <SelectValue placeholder="Filter by Payment" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Payments</SelectItem>
                          <SelectItem value="Pending">Pending</SelectItem>
                          <SelectItem value="Initiated">Initiated</SelectItem>
                          <SelectItem value="Processing">Processing</SelectItem>
                          <SelectItem value="Completed">Completed</SelectItem>
                          <SelectItem value="Failed">Failed</SelectItem>
                          <SelectItem value="Refunded">Refunded</SelectItem>
                        </SelectContent>
                      </Select>

                      <Button
                        onClick={() => setShowForm(true)}
                        className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        New Application
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Form1 Applications Table */}
              <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                      <TableRow>
                        <TableHead className="text-[#1a73c0] font-medium">Transaction ID</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Applicant</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Service Type</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Status</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Payment</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Documents</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Created</TableHead>
                        <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8">
                            <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                            Loading applications...
                          </TableCell>
                        </TableRow>
                      ) : applications.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                            No applications found
                          </TableCell>
                        </TableRow>
                      ) : (
                        applications.map((app: AlumniApplication) => (
                          <TableRow key={app.id} className="hover:bg-blue-50 transition-colors">
                            <TableCell className="font-mono text-sm">{app.transaction_id}</TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium text-[#1a73c0]">{app.full_name}</div>
                                <div className="text-sm text-muted-foreground">{app.email}</div>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium">{app.service_type_name || app.service_type}</TableCell>
                            <TableCell>
                              <InlineStatusEditor
                                applicationId={app.id}
                                currentValue={app.application_status}
                                field="application_status"
                                isEditing={editingStatus[app.id] || false}
                                onToggleEdit={() => toggleStatusEditing(app.id, 'application_status')}
                                onUpdate={(value) => handleInlineStatusUpdate(app.id, 'application_status', value)}
                                paymentStatus={app.payment_status}
                              />
                            </TableCell>
                            <TableCell>
                              <InlineStatusEditor
                                applicationId={app.id}
                                currentValue={app.payment_status}
                                field="payment_status"
                                isEditing={editingPayment[app.id] || false}
                                onToggleEdit={() => toggleStatusEditing(app.id, 'payment_status')}
                                onUpdate={(value) => handleInlineStatusUpdate(app.id, 'payment_status', value)}
                                paymentStatus={app.payment_status}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {getCompletionIcon(app.document_completion_status)}
                                <span className="text-sm font-medium">
                                  {app.document_completion_status?.uploaded_count || 0}/{app.document_completion_status?.required_count || 0}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-sm text-gray-600">
                              {new Date(app.created_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleViewDetails(app)}
                                  title="View Details"
                                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEdit(app)}
                                  title="Edit Application"
                                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>

                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDelete(app)}
                                  className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                                  title="Delete Application"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="form2" className="space-y-4">
              {/* Search and Filters */}
              <div className="mb-6 space-y-4">
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
                  <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                    <Search className="h-4 w-4 mr-2" />
                    Search & Filter Simplified Applications
                  </h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                      <Input
                        id="search-applications-form2"
                        name="search"
                        placeholder="Search applications..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                      />
                    </div>
                    <div className="flex gap-3">
                      <Select value={statusFilter} onValueChange={setStatusFilter} name="status-filter-form2">
                        <SelectTrigger className="w-[150px] border-blue-200 focus:ring-blue-400 shadow-sm" id="status-filter-form2">
                          <SelectValue placeholder="Filter by Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="Pending">Pending</SelectItem>
                          <SelectItem value="On Review">On Review</SelectItem>
                          <SelectItem value="Processing">Processing</SelectItem>
                          <SelectItem value="Complete">Complete</SelectItem>
                        </SelectContent>
                      </Select>

                      <Select value={paymentFilter} onValueChange={setPaymentFilter} name="payment-filter-form2">
                        <SelectTrigger className="w-[150px] border-blue-200 focus:ring-blue-400 shadow-sm" id="payment-filter-form2">
                          <SelectValue placeholder="Filter by Payment" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Payments</SelectItem>
                          <SelectItem value="Pending">Pending</SelectItem>
                          <SelectItem value="Initiated">Initiated</SelectItem>
                          <SelectItem value="Processing">Processing</SelectItem>
                          <SelectItem value="Completed">Completed</SelectItem>
                          <SelectItem value="Failed">Failed</SelectItem>
                          <SelectItem value="Refunded">Refunded</SelectItem>
                        </SelectContent>
                      </Select>

                      <Button
                        onClick={() => setShowForm(true)}
                        className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        New Application
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Form2 Applications Table */}
              <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                      <TableRow>
                        <TableHead className="text-[#1a73c0] font-medium">Transaction ID</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Applicant</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Service Type</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Status</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Payment</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Documents</TableHead>
                        <TableHead className="text-[#1a73c0] font-medium">Created</TableHead>
                        <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8">
                            <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                            Loading applications...
                          </TableCell>
                        </TableRow>
                      ) : applications.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                            No applications found
                          </TableCell>
                        </TableRow>
                      ) : (
                        applications.map((app: AlumniApplicationMini) => (
                          <TableRow key={app.id} className="hover:bg-blue-50 transition-colors">
                            <TableCell className="font-mono text-sm">{app.transaction_id}</TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium text-[#1a73c0]">{app.full_name}</div>
                                <div className="text-sm text-muted-foreground">{app.email}</div>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium">{app.service_type_name || app.service_type}</TableCell>
                            <TableCell>
                              <InlineStatusEditor
                                applicationId={app.id}
                                currentValue={app.application_status}
                                field="application_status"
                                isEditing={editingStatus[app.id] || false}
                                onToggleEdit={() => toggleStatusEditing(app.id, 'application_status')}
                                onUpdate={(value) => handleInlineStatusUpdate(app.id, 'application_status', value)}
                                paymentStatus={app.payment_status}
                              />
                            </TableCell>
                            <TableCell>
                              <InlineStatusEditor
                                applicationId={app.id}
                                currentValue={app.payment_status}
                                field="payment_status"
                                isEditing={editingPayment[app.id] || false}
                                onToggleEdit={() => toggleStatusEditing(app.id, 'payment_status')}
                                onUpdate={(value) => handleInlineStatusUpdate(app.id, 'payment_status', value)}
                                paymentStatus={app.payment_status}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {getCompletionIcon(app.document_completion_status)}
                                <span className="text-sm font-medium">
                                  {app.document_completion_status?.uploaded_count || 0}/{app.document_completion_status?.required_count || 0}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-sm text-gray-600">
                              {new Date(app.created_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleViewDetails(app)}
                                  title="View Details"
                                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEdit(app)}
                                  title="Edit Application"
                                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>

                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDelete(app)}
                                  className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                                  title="Delete Application"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>

            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter>
          {totalCount > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">
                  entries per page | Showing {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} applications
                </span>
              </div>
              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Dynamic page number buttons with ellipsis for large page counts */}
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === number
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show a condensed version with ellipsis
                    <>
                      {/* Always show first page */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === 1
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {currentPage > 3 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number =>
                          number > 1 &&
                          number < totalPages &&
                          (
                            number === currentPage - 1 ||
                            number === currentPage ||
                            number === currentPage + 1 ||
                            (currentPage <= 3 && number <= 4) ||
                            (currentPage >= totalPages - 2 && number >= totalPages - 3)
                          )
                        )
                        .map(number => (
                          <Button
                            key={number}
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={cn(
                              "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                              currentPage === number
                                ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                                : "bg-white text-gray-700 hover:bg-blue-50"
                            )}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {currentPage < totalPages - 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === totalPages
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Details Modal */}
      {showDetails && selectedApplication && (
        <AlumniApplicationDetails
          application={selectedApplication}
          isOpen={showDetails}
          onClose={() => {
            setShowDetails(false);
            setSelectedApplication(null);
          }}
          onStatusUpdate={handleStatusUpdate}
        />
      )}

      {/* Form Modal */}
      {showForm && (
        <AlumniApplicationForm
          application={editingApplication}
          isOpen={showForm}
          onClose={() => {
            setShowForm(false);
            setEditingApplication(null);
          }}
          onSuccess={() => {
            setShowForm(false);
            setEditingApplication(null);
            handleRefresh();
          }}
          formType={activeTab as 'form1' | 'form2'}
        />
      )}

      {/* Attractive Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className="max-w-md">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-t-lg border-b border-red-100">
            <AlertDialogHeader className="space-y-0">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="p-3 bg-gradient-to-r from-red-500 to-red-600 rounded-xl shadow-lg">
                    <AlertTriangle className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <AlertDialogTitle className="text-xl font-bold text-red-600 mb-2">
                    Confirm Deletion
                  </AlertDialogTitle>
                  <AlertDialogDescription className="text-red-700 text-sm leading-relaxed">
                    This action cannot be undone and will permanently remove the application from the system.
                  </AlertDialogDescription>
                </div>
              </div>
            </AlertDialogHeader>
          </div>

          {/* Content */}
          <div className="p-6 space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                </div>
                <div className="flex-1">
                  <p className="text-amber-800 font-medium text-sm mb-1">
                    You are about to delete:
                  </p>
                  <p className="text-amber-900 font-bold text-base">
                    {applicationToDelete?.name}
                  </p>
                  <p className="text-amber-700 text-xs mt-1">
                    Application ID: {applicationToDelete?.id}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <Trash2 className="h-5 w-5 text-red-500 mt-0.5" />
                </div>
                <div className="flex-1">
                  <h4 className="text-red-800 font-semibold text-sm mb-1">
                    What will be deleted:
                  </h4>
                  <ul className="text-red-700 text-xs space-y-1">
                    <li>• All application information and documents</li>
                    <li>• Payment and transaction records</li>
                    <li>• Application history and status updates</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Footer with action buttons */}
          <AlertDialogFooter className="bg-gray-50 px-6 py-4 rounded-b-lg border-t border-gray-200">
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full">
              <AlertDialogCancel
                disabled={isDeleting}
                className="flex-1 border-gray-300 hover:bg-gray-100 hover:border-gray-400 transition-colors"
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                disabled={isDeleting}
                className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
              >
                {isDeleting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Deleting...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Trash2 className="h-4 w-4" />
                    <span>Delete Application</span>
                  </div>
                )}
              </AlertDialogAction>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
};

export default AlumniApplicationsManagement;
