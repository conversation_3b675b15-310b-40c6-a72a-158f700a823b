from django.core.management.base import BaseCommand
from django.db.models import Avg, Count, F
from GraduateVerification.models import GraduateStudent, VerificationCollege, VerificationDepartment
from decimal import Decimal

class Command(BaseCommand):
    help = 'Calculate and display average GPA statistics at system, college, and department levels'

    def add_arguments(self, parser):
        parser.add_argument('--top', type=int, default=None, help='Show only top N colleges and departments')
        parser.add_argument('--min-graduates', type=int, default=5, 
                           help='Minimum number of graduates required for a college/department to be included')

    def handle(self, *args, **options):
        top_n = options['top']
        min_graduates = options['min_graduates']
        
        # Calculate system-wide average GPA
        total_graduates = GraduateStudent.objects.count()
        system_avg_gpa = GraduateStudent.objects.aggregate(avg_gpa=Avg('gpa'))['avg_gpa']
        
        self.stdout.write(self.style.SUCCESS(f"===== GPA STATISTICS ====="))
        self.stdout.write(self.style.SUCCESS(f"Total Graduates: {total_graduates}"))
        self.stdout.write(self.style.SUCCESS(f"System-wide Average GPA: {system_avg_gpa:.2f}"))
        self.stdout.write("\n")
        
        # Calculate college-wide average GPA
        college_stats = GraduateStudent.objects.values(
            'college__name'
        ).annotate(
            avg_gpa=Avg('gpa'),
            count=Count('id')
        ).filter(
            count__gte=min_graduates
        ).order_by('-avg_gpa')
        
        if top_n:
            college_stats = college_stats[:top_n]
        
        self.stdout.write(self.style.SUCCESS(f"===== COLLEGE-WIDE AVERAGE GPA ====="))
        self.stdout.write(f"{'College':<50} {'Average GPA':<15} {'# Graduates':<15}")
        self.stdout.write("-" * 80)
        
        for stat in college_stats:
            self.stdout.write(
                f"{stat['college__name']:<50} {stat['avg_gpa']:.2f}{'':<10} {stat['count']:<15}"
            )
        
        self.stdout.write("\n")
        
        # Calculate department-wide average GPA
        department_stats = GraduateStudent.objects.values(
            'department__name', 
            'college__name'
        ).annotate(
            avg_gpa=Avg('gpa'),
            count=Count('id')
        ).filter(
            count__gte=min_graduates
        ).order_by('-avg_gpa')
        
        if top_n:
            department_stats = department_stats[:top_n]
        
        self.stdout.write(self.style.SUCCESS(f"===== DEPARTMENT-WIDE AVERAGE GPA ====="))
        self.stdout.write(f"{'Department':<40} {'College':<30} {'Average GPA':<15} {'# Graduates':<15}")
        self.stdout.write("-" * 100)
        
        for stat in department_stats:
            self.stdout.write(
                f"{stat['department__name']:<40} {stat['college__name']:<30} {stat['avg_gpa']:.2f}{'':<10} {stat['count']:<15}"
            )
            
        # Calculate GPA distribution
        gpa_ranges = [
            {'min': Decimal('2.0'), 'max': Decimal('2.5'), 'label': '2.0-2.5'},
            {'min': Decimal('2.5'), 'max': Decimal('3.0'), 'label': '2.5-3.0'},
            {'min': Decimal('3.0'), 'max': Decimal('3.5'), 'label': '3.0-3.5'},
            {'min': Decimal('3.5'), 'max': Decimal('4.01'), 'label': '3.5-4.0'},
        ]
        
        self.stdout.write("\n")
        self.stdout.write(self.style.SUCCESS(f"===== GPA DISTRIBUTION ====="))
        self.stdout.write(f"{'GPA Range':<15} {'Count':<10} {'Percentage':<15}")
        self.stdout.write("-" * 40)
        
        for range_info in gpa_ranges:
            count = GraduateStudent.objects.filter(
                gpa__gte=range_info['min'],
                gpa__lt=range_info['max']
            ).count()
            
            percentage = (count / total_graduates) * 100 if total_graduates > 0 else 0
            
            self.stdout.write(
                f"{range_info['label']:<15} {count:<10} {percentage:.2f}%"
            )
