import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Download, 
  FileImage, 
  FileText, 
  Table,
  Share2,
  Printer
} from "lucide-react"
import { toast } from "sonner"
import html2canvas from "html2canvas"
import jsPDF from "jspdf"

interface ChartExportProps {
  chartRef: React.RefObject<HTMLDivElement>
  data: any[]
  filename?: string
  title?: string
  className?: string
}

export function ChartExport({
  chartRef,
  data,
  filename = "chart",
  title = "Chart",
  className
}: ChartExportProps) {
  const [isExporting, setIsExporting] = React.useState(false)

  const exportToPNG = async () => {
    if (!chartRef.current) return

    setIsExporting(true)
    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true
      })

      const link = document.createElement('a')
      link.download = `${filename}.png`
      link.href = canvas.toDataURL('image/png')
      link.click()

      toast.success("Chart exported as PNG")
    } catch (error) {
      console.error('Error exporting PNG:', error)
      toast.error("Failed to export PNG")
    } finally {
      setIsExporting(false)
    }
  }

  const exportToPDF = async () => {
    if (!chartRef.current) return

    setIsExporting(true)
    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true
      })

      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      })

      const imgWidth = 280
      const pageHeight = 210
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 10

      // Add title
      pdf.setFontSize(16)
      pdf.text(title, 15, position)
      position += 15

      // Add chart
      pdf.addImage(imgData, 'PNG', 15, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 15, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      pdf.save(`${filename}.pdf`)
      toast.success("Chart exported as PDF")
    } catch (error) {
      console.error('Error exporting PDF:', error)
      toast.error("Failed to export PDF")
    } finally {
      setIsExporting(false)
    }
  }

  const exportToCSV = () => {
    if (!data || data.length === 0) {
      toast.error("No data to export")
      return
    }

    try {
      // Get all unique keys from the data
      const keys = Array.from(new Set(data.flatMap(Object.keys)))
      
      // Create CSV content
      const csvContent = [
        keys.join(','), // Header
        ...data.map(row => 
          keys.map(key => {
            const value = row[key]
            // Handle values that might contain commas or quotes
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`
            }
            return value ?? ''
          }).join(',')
        )
      ].join('\n')

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `${filename}.csv`
      link.click()

      toast.success("Data exported as CSV")
    } catch (error) {
      console.error('Error exporting CSV:', error)
      toast.error("Failed to export CSV")
    }
  }

  const printChart = () => {
    if (!chartRef.current) return

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const chartHtml = chartRef.current.outerHTML
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px;
              background: white;
            }
            h1 { 
              color: #333; 
              margin-bottom: 20px;
              text-align: center;
            }
            .chart-container {
              display: flex;
              justify-content: center;
              align-items: center;
            }
            @media print {
              body { margin: 0; }
              .chart-container { 
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <h1>${title}</h1>
          <div class="chart-container">
            ${chartHtml}
          </div>
        </body>
      </html>
    `)
    
    printWindow.document.close()
    printWindow.focus()
    
    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 250)
  }

  const shareChart = async () => {
    if (!chartRef.current) return

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true
      })

      canvas.toBlob(async (blob) => {
        if (!blob) return

        if (navigator.share && navigator.canShare) {
          const file = new File([blob], `${filename}.png`, { type: 'image/png' })
          
          if (navigator.canShare({ files: [file] })) {
            await navigator.share({
              title: title,
              text: `Check out this ${title.toLowerCase()}`,
              files: [file]
            })
            return
          }
        }

        // Fallback: copy to clipboard
        if (navigator.clipboard && window.ClipboardItem) {
          await navigator.clipboard.write([
            new ClipboardItem({ 'image/png': blob })
          ])
          toast.success("Chart copied to clipboard")
        } else {
          toast.error("Sharing not supported on this device")
        }
      }, 'image/png')
    } catch (error) {
      console.error('Error sharing chart:', error)
      toast.error("Failed to share chart")
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          disabled={isExporting}
          className={className}
        >
          <Download className="h-4 w-4 mr-2" />
          {isExporting ? "Exporting..." : "Export"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Export Options</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={exportToPNG}>
          <FileImage className="h-4 w-4 mr-2" />
          Export as PNG
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={exportToPDF}>
          <FileText className="h-4 w-4 mr-2" />
          Export as PDF
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={exportToCSV}>
          <Table className="h-4 w-4 mr-2" />
          Export Data (CSV)
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={printChart}>
          <Printer className="h-4 w-4 mr-2" />
          Print Chart
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={shareChart}>
          <Share2 className="h-4 w-4 mr-2" />
          Share Chart
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
