import axios from 'axios';

export interface Department {
  id: number;
  name: string;
  college: number;
  college_name?: string;
  code?: string;
  description?: string;
  status?: boolean;
  created_at?: string;
  updated_at?: string;
}

const API_BASE_URL = 'http://localhost:8000/api/';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

export const departmentAPI = {
  getDepartments: async (params?: { college?: number }) => {
    const response = await api.get('departments/', { params });
    return response.data;
  },

  getDepartmentById: async (id: number) => {
    const response = await api.get(`departments/${id}/`);
    return response.data;
  },

  createDepartment: async (data: Partial<Department>) => {
    const response = await api.post('verification/departments/', data);
    return response.data;
  },

  updateDepartment: async (id: number, data: Partial<Department>) => {
    const response = await api.put(`verification/departments/${id}/`, data);
    return response.data;
  },

  deleteDepartment: async (id: number) => {
    const response = await api.delete(`verification/departments/${id}/`);
    return response.data;
  }
};

// React Query hook for compatibility with existing code
export const useGetDepartmentsQuery = (params?: { college?: number }) => {
  return { data: [] }; // Placeholder until we implement React Query
};
