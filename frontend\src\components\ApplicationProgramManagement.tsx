import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Plus, Pencil, Trash2, Search, Filter, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, FileText, CheckCircle, XCircle, Clock, Users, RotateCcw, User, Building, MessageSquare, GraduationCap, Calendar, CreditCard, BookOpen, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import axios from 'axios';

interface Program {
  id: number;
  program_code: string;
  program_name: string;
  registration_fee: number | string | null;
}

const ApplicationProgramManagement = () => {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentProgram, setCurrentProgram] = useState<Program | null>(null);
  const [formData, setFormData] = useState({
    program_code: '',
    program_name: '',
    registration_fee: '',
  });
  const [formErrors, setFormErrors] = useState({
    program_code: '',
    program_name: '',
    registration_fee: '',
  });

  // Fetch programs on component mount
  useEffect(() => {
    fetchPrograms();
  }, []);

  const fetchPrograms = async () => {
    setLoading(true);
    try {
      // Try to fetch from the application program endpoint
      const response = await axios.get('http://localhost:8000/api/programs/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.data) {
        console.log('Programs data:', response.data);

        // Ensure all program data is properly formatted
        const formattedPrograms = response.data.map(program => ({
          ...program,
          // Ensure registration_fee is properly handled
          registration_fee: program.registration_fee !== undefined ? program.registration_fee : null
        }));

        setPrograms(formattedPrograms);
      } else {
        console.warn('No program data returned from API');
        setPrograms([]);
      }
    } catch (error) {
      console.error('Error fetching programs:', error);

      // Try the public endpoint as fallback
      try {
        const publicResponse = await axios.get('http://localhost:8000/api/programs/public/');
        if (publicResponse.data) {
          console.log('Programs data from public endpoint:', publicResponse.data);

          // Ensure all program data is properly formatted
          const formattedPrograms = publicResponse.data.map(program => ({
            ...program,
            // Ensure registration_fee is properly handled
            registration_fee: program.registration_fee !== undefined ? program.registration_fee : null
          }));

          setPrograms(formattedPrograms);
        } else {
          console.warn('No program data returned from public API');
          setPrograms([]);
        }
      } catch (publicError) {
        console.error('Error fetching programs from public endpoint:', publicError);
        toast.error('Failed to load programs');
        setPrograms([]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // For registration_fee, only allow numbers and decimal point
    if (name === 'registration_fee' && value !== '') {
      const regex = /^[0-9]*\.?[0-9]*$/;
      if (!regex.test(value)) return;
    }

    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user types
    setFormErrors({
      ...formErrors,
      [name]: '',
    });
  };

  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formData.program_code.trim()) {
      newErrors.program_code = 'Program code is required';
      valid = false;
    }

    if (!formData.program_name.trim()) {
      newErrors.program_name = 'Program name is required';
      valid = false;
    }

    // Registration fee is optional, but if provided, must be a valid number
    if (formData.registration_fee && isNaN(parseFloat(formData.registration_fee))) {
      newErrors.registration_fee = 'Registration fee must be a valid number';
      valid = false;
    }

    setFormErrors(newErrors);
    return valid;
  };

  const handleAddProgram = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add a program');
        return;
      }

      // Prepare the data for submission
      const programData = {
        program_code: formData.program_code,
        program_name: formData.program_name,
        registration_fee: formData.registration_fee ? parseFloat(formData.registration_fee) : null
      };

      // Show loading toast
      const loadingToast = toast.loading('Adding program...');

      // Make the request with the current token
      const response = await axios.post('http://localhost:8000/api/programs/', programData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      toast.success('Program added successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchPrograms(); // Refresh the list after adding
    } catch (error) {
      console.error('Error adding program:', error);

      // Handle validation errors from the server
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        const newErrors = { ...formErrors };
        let hasFieldErrors = false;

        // Check for field-specific errors
        if (errorData.program_code) {
          newErrors.program_code = Array.isArray(errorData.program_code)
            ? errorData.program_code[0]
            : errorData.program_code;
          hasFieldErrors = true;
        }

        if (errorData.program_name) {
          newErrors.program_name = Array.isArray(errorData.program_name)
            ? errorData.program_name[0]
            : errorData.program_name;
          hasFieldErrors = true;
        }

        if (errorData.registration_fee) {
          newErrors.registration_fee = Array.isArray(errorData.registration_fee)
            ? errorData.registration_fee[0]
            : errorData.registration_fee;
          hasFieldErrors = true;
        }

        if (hasFieldErrors) {
          setFormErrors(newErrors);
          toast.error('Please fix the errors in the form');
        } else {
          toast.error(`Failed to add program: ${error.response.data.detail || 'Unknown error'}`);
        }
      } else {
        toast.error(`Failed to add program: ${error.message}`);
      }
    }
  };

  const handleEditProgram = async () => {
    if (!currentProgram) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit a program');
        return;
      }

      // Prepare the data for submission
      const programData = {
        program_code: formData.program_code,
        program_name: formData.program_name,
        registration_fee: formData.registration_fee ? parseFloat(formData.registration_fee) : null
      };

      // Show loading toast
      const loadingToast = toast.loading('Updating program...');

      // Make the request with the current token
      const response = await axios.put(`http://localhost:8000/api/programs/${currentProgram.id}/`, programData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      toast.success('Program updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchPrograms(); // Refresh the list after editing
    } catch (error) {
      console.error('Error updating program:', error);

      // Handle validation errors from the server
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        const newErrors = { ...formErrors };
        let hasFieldErrors = false;

        // Check for field-specific errors
        if (errorData.program_code) {
          newErrors.program_code = Array.isArray(errorData.program_code)
            ? errorData.program_code[0]
            : errorData.program_code;
          hasFieldErrors = true;
        }

        if (errorData.program_name) {
          newErrors.program_name = Array.isArray(errorData.program_name)
            ? errorData.program_name[0]
            : errorData.program_name;
          hasFieldErrors = true;
        }

        if (errorData.registration_fee) {
          newErrors.registration_fee = Array.isArray(errorData.registration_fee)
            ? errorData.registration_fee[0]
            : errorData.registration_fee;
          hasFieldErrors = true;
        }

        if (hasFieldErrors) {
          setFormErrors(newErrors);
          toast.error('Please fix the errors in the form');
        } else {
          toast.error(`Failed to update program: ${error.response.data.detail || 'Unknown error'}`);
        }
      } else {
        toast.error(`Failed to update program: ${error.message}`);
      }
    }
  };

  const handleDeleteProgram = async () => {
    if (!currentProgram) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete a program');
        return;
      }

      // Show loading toast
      const loadingToast = toast.loading('Deleting program...');

      // Make the request with the current token
      const response = await axios.delete(`http://localhost:8000/api/programs/delete/${currentProgram.id}/`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      toast.success('Program deleted successfully');
      setIsDeleteDialogOpen(false);
      fetchPrograms(); // Refresh the list after deleting
    } catch (error) {
      console.error('Error deleting program:', error);

      // Handle validation errors from the server
      if (error.response && error.response.data) {
        if (error.response.data.detail) {
          toast.error(`Failed to delete program: ${error.response.data.detail}`);
        } else {
          toast.error(`Failed to delete program: ${JSON.stringify(error.response.data)}`);
        }
      } else {
        toast.error(`Failed to delete program: ${error.message}`);
      }
    }
  };

  const openEditDialog = (program: Program) => {
    setCurrentProgram(program);
    setFormData({
      program_code: program.program_code,
      program_name: program.program_name,
      registration_fee: program.registration_fee !== null ? program.registration_fee.toString() : '',
    });
    setFormErrors({
      program_code: '',
      program_name: '',
      registration_fee: '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (program: Program) => {
    setCurrentProgram(program);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      program_code: '',
      program_name: '',
      registration_fee: '',
    });
    setFormErrors({
      program_code: '',
      program_name: '',
      registration_fee: '',
    });
    setCurrentProgram(null);
  };

  // Format currency for display
  const formatCurrency = (amount: number | string | null) => {
    if (amount === null || amount === undefined) return 'N/A';

    // Convert to number if it's a string
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    // Check if it's a valid number after conversion
    if (isNaN(numericAmount)) return 'N/A';

    return `${numericAmount.toFixed(2)} ETB`;
  };

  // Filter programs based on search term
  const filteredPrograms = programs.filter(program =>
    program.program_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    program.program_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredPrograms.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredPrograms.length / itemsPerPage);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <BookOpen className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Application Program Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete programs for the application portal
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Program
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-t-lg border-b">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                        <Plus className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <DialogTitle className="text-lg text-[#1a73c0]">Add New Program</DialogTitle>
                        <DialogDescription className="mt-1">
                          Enter the details for the new program
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="program_code" className="text-sm font-medium text-gray-700">Program Code</Label>
                      <Input
                        id="program_code"
                        name="program_code"
                        value={formData.program_code}
                        onChange={handleInputChange}
                        placeholder="UG, PG, ..."
                        className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.program_code ? 'border-red-500 focus-visible:ring-red-400' : '')}
                      />
                      {formErrors.program_code && (
                        <p className="text-sm text-red-500">{formErrors.program_code}</p>
                      )}
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="program_name" className="text-sm font-medium text-gray-700">Program Name</Label>
                      <Input
                        id="program_name"
                        name="program_name"
                        value={formData.program_name}
                        onChange={handleInputChange}
                        placeholder="Undergraduate, Postgraduate, ..."
                        className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.program_name ? 'border-red-500 focus-visible:ring-red-400' : '')}
                      />
                      {formErrors.program_name && (
                        <p className="text-sm text-red-500">{formErrors.program_name}</p>
                      )}
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="registration_fee" className="text-sm font-medium text-gray-700">Registration Fee (ETB)</Label>
                      <Input
                        id="registration_fee"
                        name="registration_fee"
                        value={formData.registration_fee}
                        onChange={handleInputChange}
                        placeholder="Enter registration fee (optional)"
                        className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.registration_fee ? 'border-red-500 focus-visible:ring-red-400' : '')}
                      />
                      {formErrors.registration_fee && (
                        <p className="text-sm text-red-500">{formErrors.registration_fee}</p>
                      )}
                    </div>
                  </div>
                  <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
                    <DialogClose asChild>
                      <Button variant="outline" className="border-gray-300">Cancel</Button>
                    </DialogClose>
                    <Button onClick={handleAddProgram} className="bg-[#1a73c0] hover:bg-blue-700">Save</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search Programs
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    placeholder="Search by program code or name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[20%] text-[#1a73c0] font-medium">Program Code</TableHead>
                    <TableHead className="w-[45%] text-[#1a73c0] font-medium">Program Name</TableHead>
                    <TableHead className="w-[20%] text-[#1a73c0] font-medium">Registration Fee</TableHead>
                    <TableHead className="w-[15%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-blue-100 p-3 rounded-full">
                            <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                          </div>
                          <div className="text-[#1a73c0] font-medium">Loading programs...</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredPrograms.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div className="text-gray-700 font-medium">No programs found</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {searchTerm ?
                              'Try adjusting your search criteria to find what you\'re looking for.' :
                              'There are no programs available. Click the "Add Program" button to create one.'}
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentItems.map((program) => (
                      <TableRow key={program.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{program.program_code}</TableCell>
                        <TableCell>{program.program_name}</TableCell>
                        <TableCell>{formatCurrency(program.registration_fee)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(program)}
                              title="Edit"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDeleteDialog(program)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredPrograms.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredPrograms.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredPrograms.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Pencil className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-[#1a73c0]">Edit Program</DialogTitle>
                <DialogDescription className="mt-1">
                  Update the program information
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-program_code" className="text-sm font-medium text-gray-700">Program Code</Label>
              <Input
                id="edit-program_code"
                name="program_code"
                value={formData.program_code}
                onChange={handleInputChange}
                placeholder="UG, PG, MSc, PhD - Undergraduate, Post Graduate, Master of Science, Doctor of Philosophy"
                className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.program_code ? 'border-red-500 focus-visible:ring-red-400' : '')}
              />
              {formErrors.program_code && (
                <p className="text-sm text-red-500">{formErrors.program_code}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-program_name" className="text-sm font-medium text-gray-700">Program Name</Label>
              <Input
                id="edit-program_name"
                name="program_name"
                value={formData.program_name}
                onChange={handleInputChange}
                placeholder="Computer Science, Business Admin, Medicine - Bachelor of Computer Science, Master of Business Administration, Doctor of Medicine"
                className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.program_name ? 'border-red-500 focus-visible:ring-red-400' : '')}
              />
              {formErrors.program_name && (
                <p className="text-sm text-red-500">{formErrors.program_name}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-registration_fee" className="text-sm font-medium text-gray-700">Registration Fee (ETB)</Label>
              <Input
                id="edit-registration_fee"
                name="registration_fee"
                value={formData.registration_fee}
                onChange={handleInputChange}
                placeholder="Enter registration fee (optional)"
                className={cn("border-blue-200 focus-visible:ring-blue-400", formErrors.registration_fee ? 'border-red-500 focus-visible:ring-red-400' : '')}
              />
              {formErrors.registration_fee && (
                <p className="text-sm text-red-500">{formErrors.registration_fee}</p>
              )}
            </div>
          </div>
          <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
            <DialogClose asChild>
              <Button variant="outline" className="border-gray-300">Cancel</Button>
            </DialogClose>
            <Button onClick={handleEditProgram} className="bg-[#1a73c0] hover:bg-blue-700">Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-orange-50 p-4 rounded-t-lg border-b">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-red-500 rounded-lg shadow-sm">
                <Trash2 className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-lg text-red-600">Confirm Deletion</DialogTitle>
                <DialogDescription className="mt-1">
                  This action cannot be undone
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="py-4 px-1">
            <p className="text-gray-700">
              Are you sure you want to delete the program <span className="font-semibold text-gray-900">"{currentProgram?.program_name}"</span> (<span className="font-semibold text-gray-900">{currentProgram?.program_code}</span>)?
            </p>
            <p className="mt-2 text-sm text-gray-500">
              This will permanently remove the program from the system. This action cannot be reversed.
            </p>
          </div>
          <DialogFooter className="bg-gray-50 p-4 rounded-b-lg border-t">
            <DialogClose asChild>
              <Button variant="outline" className="border-gray-300">Cancel</Button>
            </DialogClose>
            <Button variant="destructive" onClick={handleDeleteProgram}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ApplicationProgramManagement;
