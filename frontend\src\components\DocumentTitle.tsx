import { useEffect, useState } from 'react';
import { settingsAPI } from '@/services/api';

interface DocumentTitleProps {
  pageTitle?: string;
}

const DocumentTitle: React.FC<DocumentTitleProps> = ({ pageTitle }) => {
  const [settings, setSettings] = useState<{
    systemName: string;
    organizationName: string;
  }>({
    systemName: '',
    organizationName: ''
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          setSettings({
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || ''
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  useEffect(() => {
    // Only update if settings have been loaded
    if (!settings.systemName || !settings.organizationName) {
      return;
    }

    // Set the document title based on the page title and system name
    if (pageTitle) {
      document.title = `${pageTitle} | ${settings.systemName} - ${settings.organizationName}`;
    } else {
      document.title = `${settings.systemName} - ${settings.organizationName}`;
    }

    // Update meta tags
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', `${settings.systemName} - ${settings.organizationName}`);
    }

    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute('content', `${settings.systemName} - ${settings.organizationName}`);
    }

    const ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription) {
      ogDescription.setAttribute('content', `${settings.systemName} - ${settings.organizationName}`);
    }
  }, [pageTitle, settings.systemName, settings.organizationName]);

  // This component doesn't render anything
  return null;
};

export default DocumentTitle;
