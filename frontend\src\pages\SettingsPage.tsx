import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import OrganizationSettingsForm from '@/components/settings/OrganizationSettingsForm';
import QuickLinksManager from '@/components/settings/QuickLinksManager';
import SocialMediaLinksManager from '@/components/settings/SocialMediaLinksManager';
import SMTPSettingsForm from '@/components/settings/SMTPSettingsForm';
import { SettingsProvider } from '@/contexts/SettingsContext';
import { Building, Link2, Share2, Settings, Mail } from 'lucide-react';

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('organization');

  return (
    <SettingsProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
            <div className="flex justify-between items-center">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                  <Settings className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl text-[#1a73c0]">Settings</CardTitle>
                  <CardDescription className="mt-1">
                    Manage organization settings, quick links, social media profiles, and SMTP configuration
                  </CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-6">
            <TabsTrigger
              value="organization"
              className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
            >
              <Building className="h-4 w-4" />
              <span>Organization</span>
            </TabsTrigger>
            <TabsTrigger
              value="quick-links"
              className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
            >
              <Link2 className="h-4 w-4" />
              <span>Quick Links</span>
            </TabsTrigger>
            <TabsTrigger
              value="social-media"
              className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
            >
              <Share2 className="h-4 w-4" />
              <span>Social Media</span>
            </TabsTrigger>
            <TabsTrigger
              value="smtp"
              className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
            >
              <Mail className="h-4 w-4" />
              <span>SMTP</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="organization" className="space-y-4">
            <OrganizationSettingsForm />
          </TabsContent>
          
          <TabsContent value="quick-links" className="space-y-4">
            <QuickLinksManager />
          </TabsContent>
          
          <TabsContent value="social-media" className="space-y-4">
            <SocialMediaLinksManager />
          </TabsContent>

          <TabsContent value="smtp" className="space-y-4">
            <SMTPSettingsForm />
          </TabsContent>
        </Tabs>
          </CardContent>
        </Card>
      </div>
    </SettingsProvider>
  );
};

export default SettingsPage;
