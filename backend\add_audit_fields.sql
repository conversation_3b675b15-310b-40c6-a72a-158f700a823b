-- Add audit trail fields to GraduateStudent table
-- Run this SQL script to add the missing columns

-- Check if columns already exist (optional check)
-- SELECT column_name FROM information_schema.columns
-- WHERE table_name = 'GraduateVerification_graduatestudent'
-- AND column_name IN ('created_by_id', 'updated_by_id');

-- Add created_by column (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'GraduateVerification_graduatestudent'
        AND column_name = 'created_by_id'
    ) THEN
        ALTER TABLE "GraduateVerification_graduatestudent"
        ADD COLUMN "created_by_id" integer NULL;
    END IF;
END $$;

-- Add updated_by column (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'GraduateVerification_graduatestudent'
        AND column_name = 'updated_by_id'
    ) THEN
        ALTER TABLE "GraduateVerification_graduatestudent"
        ADD COLUMN "updated_by_id" integer NULL;
    END IF;
END $$;

-- Add foreign key constraints (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'GraduateVerification_graduatestudent_created_by_id_fkey'
    ) THEN
        ALTER TABLE "GraduateVerification_graduatestudent"
        ADD CONSTRAINT "GraduateVerification_graduatestudent_created_by_id_fkey"
        FOREIGN KEY ("created_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'GraduateVerification_graduatestudent_updated_by_id_fkey'
    ) THEN
        ALTER TABLE "GraduateVerification_graduatestudent"
        ADD CONSTRAINT "GraduateVerification_graduatestudent_updated_by_id_fkey"
        FOREIGN KEY ("updated_by_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED;
    END IF;
END $$;

-- Create indexes for better performance (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'GraduateVerification_graduatestudent_created_by_id_idx'
    ) THEN
        CREATE INDEX "GraduateVerification_graduatestudent_created_by_id_idx"
        ON "GraduateVerification_graduatestudent" ("created_by_id");
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'GraduateVerification_graduatestudent_updated_by_id_idx'
    ) THEN
        CREATE INDEX "GraduateVerification_graduatestudent_updated_by_id_idx"
        ON "GraduateVerification_graduatestudent" ("updated_by_id");
    END IF;
END $$;

-- Update migration table to mark this migration as applied (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM "django_migrations"
        WHERE app = 'GraduateVerification' AND name = '0003_add_audit_trail_fields'
    ) THEN
        INSERT INTO "django_migrations" ("app", "name", "applied")
        VALUES ('GraduateVerification', '0003_add_audit_trail_fields', NOW());
    END IF;
END $$;

-- Verify the changes
SELECT 'Audit trail migration completed successfully!' as status;
