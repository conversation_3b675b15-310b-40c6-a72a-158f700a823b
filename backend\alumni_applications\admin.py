from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django import forms
from .models import AlumniApplication, AlumniApplicationMini, ApplicationDocument


class ApplicationDocumentForm(forms.ModelForm):
    """Custom form for ApplicationDocument to explicitly control fields."""

    class Meta:
        model = ApplicationDocument
        fields = ['document_type_name', 'file']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add any custom field configurations here if needed


class ApplicationDocumentInline(admin.TabularInline):
    """Inline admin for application documents."""
    model = ApplicationDocument
    form = ApplicationDocumentForm
    extra = 0

    # Explicitly define all fields to prevent auto-generation
    fields = ['document_type_name', 'file']
    readonly_fields = ['original_filename', 'formatted_file_size', 'mime_type', 'upload_timestamp']

    def get_fields(self, request, obj=None):
        """Explicitly define the fields to avoid any cached field references."""
        _ = request, obj  # Unused parameters
        if obj:  # Editing existing object
            return ['document_type_name', 'file', 'original_filename', 'formatted_file_size', 'mime_type', 'upload_timestamp']
        else:  # Adding new object
            return ['document_type_name', 'file']

    def get_readonly_fields(self, request, obj=None):
        """Make certain fields readonly only when object exists."""
        _ = request  # Unused parameter
        if obj:  # Editing existing object
            return self.readonly_fields
        else:  # Adding new object
            return ['formatted_file_size', 'upload_timestamp']

    def formatted_file_size(self, obj):
        """Display formatted file size."""
        if obj and obj.file_size:
            return obj.formatted_file_size
        return '0 B'
    formatted_file_size.short_description = 'File Size'


@admin.register(AlumniApplication)
class AlumniApplicationAdmin(admin.ModelAdmin):
    """Admin interface for AlumniApplication (Form1)."""

    list_display = [
        'transaction_id', 'full_name', 'email', 'service_type',
        'application_status', 'payment_status', 'document_completion_display', 'college_name', 'created_at'
    ]
    list_filter = [
        'application_status', 'payment_status', 'student_status',
        'admission_type', 'degree_type', 'is_uog_destination',
        'service_type', 'college', 'created_at'
    ]
    search_fields = [
        'first_name', 'last_name', 'father_name', 'email',
        'phone_number', 'student_id', 'transaction_id'
    ]
    readonly_fields = [
        'id', 'transaction_id', 'full_name', 'college_name',
        'department_name', 'required_document_types_display', 'document_completion_display',
        'created_at', 'updated_at'
    ]

    fieldsets = [
        ('Personal Information', {
            'fields': [
                'first_name', 'father_name', 'last_name', 'student_id',
                'phone_number', 'email'
            ]
        }),
        ('Academic Information', {
            'fields': [
                'admission_type', 'degree_type', 'student_status',
                'is_other_college', 'college', 'other_college_name',
                'department', 'other_department_name'
            ]
        }),
        ('Academic Status Details', {
            'fields': [
                'current_year', 'year_of_leaving_ethiopian', 'year_of_leaving_gregorian',
                'year_of_graduation_ethiopian', 'year_of_graduation_gregorian'
            ],
            'classes': ['collapse']
        }),
        ('Service & Payment', {
            'fields': [
                'service_type', 'application_status', 'payment_status', 'transaction_id'
            ]
        }),
        ('Document Requirements', {
            'fields': [
                'required_document_types_display', 'document_completion_display'
            ],
            'classes': ['collapse']
        }),
        ('Destination Logic', {
            'fields': [
                'is_uog_destination', 'uog_college', 'uog_department',
                'order_type', 'institution_name', 'country',
                'institution_address', 'mailing_agent'
            ],
            'classes': ['collapse']
        }),
        ('Audit Trail', {
            'fields': [
                'created_at', 'updated_at', 'created_by', 'updated_by'
            ],
            'classes': ['collapse']
        }),
    ]

    inlines = [ApplicationDocumentInline]

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'service_type', 'college', 'department', 'uog_college', 'uog_department'
        )

    def save_model(self, request, obj, form, change):
        """Set created_by/updated_by when saving."""
        if not change:  # Creating new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def required_document_types_display(self, obj):
        """Display required document types for the service type."""
        if obj.service_type:
            required_types = obj.required_document_types_list
            if required_types:
                return format_html('<br>'.join([f'• {doc_type}' for doc_type in required_types]))
            return 'No documents required'
        return 'No service type selected'
    required_document_types_display.short_description = 'Required Document Types'

    def document_completion_display(self, obj):
        """Display document completion status."""
        status = obj.get_document_completion_status()
        percentage = status['completion_percentage']

        if status['is_complete']:
            color = 'green'
            icon = '✅'
        elif percentage >= 50:
            color = 'orange'
            icon = '⚠️'
        else:
            color = 'red'
            icon = '❌'

        missing_text = ''
        if status['missing_types']:
            missing_text = f"<br><small>Missing: {', '.join(status['missing_types'])}</small>"

        return format_html(
            f'<span style="color: {color};">{icon} {status["uploaded_count"]}/{status["required_count"]} ({percentage:.0f}%)</span>{missing_text}'
        )
    document_completion_display.short_description = 'Document Status'


@admin.register(AlumniApplicationMini)
class AlumniApplicationMiniAdmin(admin.ModelAdmin):
    """Admin interface for AlumniApplicationMini (Form2)."""

    list_display = [
        'transaction_id', 'full_name', 'email', 'service_type',
        'application_status', 'payment_status', 'document_completion_display', 'college_name', 'created_at'
    ]
    list_filter = [
        'application_status', 'payment_status', 'student_status',
        'admission_type', 'degree_type', 'service_type', 'college', 'created_at'
    ]
    search_fields = [
        'first_name', 'last_name', 'father_name', 'email',
        'phone_number', 'student_id', 'transaction_id'
    ]
    readonly_fields = [
        'id', 'transaction_id', 'full_name', 'college_name',
        'department_name', 'required_document_types_display', 'document_completion_display',
        'created_at', 'updated_at'
    ]

    fieldsets = [
        ('Personal Information', {
            'fields': [
                'first_name', 'father_name', 'last_name', 'student_id',
                'phone_number', 'email'
            ]
        }),
        ('Academic Information', {
            'fields': [
                'admission_type', 'degree_type', 'student_status',
                'is_other_college', 'college', 'other_college_name',
                'department', 'other_department_name'
            ]
        }),
        ('Academic Status Details', {
            'fields': [
                'current_year', 'year_of_leaving_ethiopian', 'year_of_leaving_gregorian',
                'year_of_graduation_ethiopian', 'year_of_graduation_gregorian'
            ],
            'classes': ['collapse']
        }),
        ('Service & Payment', {
            'fields': [
                'service_type', 'application_status', 'payment_status', 'transaction_id'
            ]
        }),
        ('Document Requirements', {
            'fields': [
                'required_document_types_display', 'document_completion_display'
            ],
            'classes': ['collapse']
        }),
        ('Audit Trail', {
            'fields': [
                'created_at', 'updated_at', 'created_by', 'updated_by'
            ],
            'classes': ['collapse']
        }),
    ]

    inlines = [ApplicationDocumentInline]

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'service_type', 'college', 'department'
        )

    def save_model(self, request, obj, form, change):
        """Set created_by/updated_by when saving."""
        if not change:  # Creating new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def required_document_types_display(self, obj):
        """Display required document types for the service type."""
        if obj.service_type:
            required_types = obj.required_document_types_list
            if required_types:
                return format_html('<br>'.join([f'• {doc_type}' for doc_type in required_types]))
            return 'No documents required'
        return 'No service type selected'
    required_document_types_display.short_description = 'Required Document Types'

    def document_completion_display(self, obj):
        """Display document completion status."""
        status = obj.get_document_completion_status()
        percentage = status['completion_percentage']

        if status['is_complete']:
            color = 'green'
            icon = '✅'
        elif percentage >= 50:
            color = 'orange'
            icon = '⚠️'
        else:
            color = 'red'
            icon = '❌'

        missing_text = ''
        if status['missing_types']:
            missing_text = f"<br><small>Missing: {', '.join(status['missing_types'])}</small>"

        return format_html(
            f'<span style="color: {color};">{icon} {status["uploaded_count"]}/{status["required_count"]} ({percentage:.0f}%)</span>{missing_text}'
        )
    document_completion_display.short_description = 'Document Status'


@admin.register(ApplicationDocument)
class ApplicationDocumentAdmin(admin.ModelAdmin):
    """Admin interface for ApplicationDocument."""

    list_display = [
        'get_applicant_name', 'document_type_name', 'original_filename',
        'formatted_file_size', 'mime_type', 'upload_timestamp'
    ]
    list_filter = [
        'document_type_name', 'mime_type', 'upload_timestamp'
    ]
    search_fields = [
        'original_filename', 'document_type_name',
        'application_form1__first_name', 'application_form1__last_name',
        'application_form2__first_name', 'application_form2__last_name'
    ]
    readonly_fields = [
        'id', 'original_filename', 'file_size', 'formatted_file_size',
        'mime_type', 'upload_timestamp', 'get_application_link'
    ]

    fieldsets = [
        ('Document Information', {
            'fields': [
                'document_type_name', 'file', 'original_filename',
                'formatted_file_size', 'mime_type'
            ]
        }),
        ('Application Link', {
            'fields': ['application_form1', 'application_form2', 'get_application_link']
        }),
        ('Audit Trail', {
            'fields': ['upload_timestamp', 'uploaded_by'],
            'classes': ['collapse']
        }),
    ]

    def get_applicant_name(self, obj):
        """Get the applicant name from either form."""
        application = obj.application_form1 or obj.application_form2
        return application.full_name if application else 'N/A'
    get_applicant_name.short_description = 'Applicant Name'

    def get_application_link(self, obj):
        """Create a link to the related application."""
        if obj.application_form1:
            url = reverse('admin:alumni_applications_alumniapplication_change',
                         args=[obj.application_form1.pk])
            return format_html('<a href="{}">View Form1 Application</a>', url)
        elif obj.application_form2:
            url = reverse('admin:alumni_applications_alumniapplicationmini_change',
                         args=[obj.application_form2.pk])
            return format_html('<a href="{}">View Form2 Application</a>', url)
        return 'No application linked'
    get_application_link.short_description = 'Application Link'

    def save_model(self, request, obj, form, change):
        """Set uploaded_by when saving."""
        if not change:  # Creating new object
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)
