import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { applicantAPI, Applicant, ApplicantListParams, ApplicantStatusUpdate } from '@/services/applicantAPI';
import { collegeAPI } from '@/services/collegeAPI';
import { departmentAPI } from '@/services/departmentAPI';
import { programAPI } from '@/services/programAPI';
import { yearAPI, termAPI } from '@/services/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import {
  Search,
  RefreshCw,
  Filter,
  ChevronLeft,
  ChevronRight,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  RotateCcw,
  User,
  Building,
  MessageSquare,
  GraduationCap,
  Calendar,
  CreditCard,
  UserCircle,
  Mail,
  Phone,
  Bookmark,
  Award,
  BarChart,
  Briefcase,
  ClipboardCheck,
  Stamp,
  Files,
  School,
  BookOpen,
  Download,
  FileSpreadsheet
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Define interfaces
interface College {
  id: number;
  name: string;
  description?: string;
}

interface Department {
  id: number;
  name: string;
  college: number;
  description?: string;
}

interface Program {
  id: number;
  program_name: string;
  program_code?: string;
  registration_fee?: string | number;
}

interface Year {
  uuid: string;
  year: string;
  description?: string;
}

interface Term {
  id: string;
  name: string;
  description?: string;
}

const ApplicantManagement: React.FC = () => {
  // State for filters
  const [filters, setFilters] = useState<ApplicantListParams>({
    page: 1,
    page_size: 10,
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedApplicant, setSelectedApplicant] = useState<Applicant | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [statusUpdate, setStatusUpdate] = useState({
    registrar_off_status: '',
    department_status: '',
    payment_status: '',
    remark: '',
  });

  // Get query client
  const queryClient = useQueryClient();

  // Fetch applicants with filters
  const { data: allApplicants, isLoading, error, refetch } = useQuery({
    queryKey: ['applicants', filters],
    queryFn: async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('Authentication token not found. Please log in again.');
        }
        return await applicantAPI.getApplicants(filters);
      } catch (error) {
        console.error('Error fetching applicants:', error);
        throw error;
      }
    },
    placeholderData: (previousData) => previousData,
    retry: 1
  });

  // Client-side pagination since the API returns all records
  const applicants = React.useMemo(() => {
    if (!allApplicants || !Array.isArray(allApplicants)) return [];

    // Calculate start and end indices for the current page
    const startIndex = ((filters.page || 1) - 1) * (filters.page_size || 10);
    const endIndex = startIndex + (filters.page_size || 10);

    // Return the sliced array for the current page
    return allApplicants.slice(startIndex, endIndex);
  }, [allApplicants, filters.page, filters.page_size]);

  // Fetch colleges
  const { data: colleges } = useQuery({
    queryKey: ['colleges'],
    queryFn: () => collegeAPI.getColleges(),
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  // Fetch all departments for initial load
  const { data: allDepartments } = useQuery({
    queryKey: ['all-departments'],
    queryFn: () => departmentAPI.getDepartments(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Filter departments based on selected college
  const departments = React.useMemo(() => {
    if (!allDepartments) return [];
    if (!filters.college_id) return allDepartments;
    return allDepartments.filter((dept: Department) => dept.college === filters.college_id);
  }, [allDepartments, filters.college_id]);

  // Fetch all programs for initial load
  const { data: allPrograms, isLoading: isLoadingPrograms } = useQuery({
    queryKey: ['all-programs'],
    queryFn: async () => {
      try {
        const programs = await programAPI.getPrograms();
        return programs;
      } catch (error) {
        console.error('Error fetching programs:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch years
  const { data: years } = useQuery({
    queryKey: ['years'],
    queryFn: async () => {
      try {
        const response = await yearAPI.getAllYears();
        return response.data;
      } catch (error) {
        console.error('Error fetching years:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch terms
  const { data: terms } = useQuery({
    queryKey: ['terms'],
    queryFn: async () => {
      try {
        const response = await termAPI.getTerms();
        return response.data;
      } catch (error) {
        console.error('Error fetching terms:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update applicant status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => applicantAPI.updateApplicantStatus(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['applicants'] });
    }
  });

  // Extract mutation state for easier access
  const isUpdatingStatus = updateStatusMutation.isPending;

  // Handle search input change
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchTerm) {
        setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
      } else {
        setFilters(prev => {
          const newFilters = { ...prev, page: 1 };
          delete newFilters.search;
          return newFilters;
        });
      }
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm]);

  // Handle filter changes
  const handleFilterChange = (key: string, value: string | number | null) => {
    setFilters(prev => {
      const newFilters = { ...prev, page: 1 } as ApplicantListParams;

      if (value === null || value === '' || value === 'all') {
        // Remove the filter
        if (key === 'college_id') delete newFilters.college_id;
        if (key === 'department_id') delete newFilters.department_id;
        if (key === 'program_id') delete newFilters.program_id;
        if (key === 'registrar_off_status') delete newFilters.registrar_off_status;
        if (key === 'department_status') delete newFilters.department_status;
        if (key === 'payment_status') delete newFilters.payment_status;
        if (key === 'sponsorship') delete newFilters.sponsorship;
        if (key === 'search') delete newFilters.search;
        if (key === 'year') delete newFilters.year;
        if (key === 'term') delete newFilters.term;
      } else {
        // Set the filter with appropriate type conversion
        if (key === 'program_id') {
          newFilters.program_id = typeof value === 'string' ? parseInt(value) : value as number;
        } else if (key === 'college_id') {
          newFilters.college_id = typeof value === 'string' ? parseInt(value) : value as number;
        } else if (key === 'department_id') {
          newFilters.department_id = typeof value === 'string' ? parseInt(value) : value as number;
        } else if (key === 'registrar_off_status') {
          newFilters.registrar_off_status = value as string;
        } else if (key === 'department_status') {
          newFilters.department_status = value as string;
        } else if (key === 'payment_status') {
          newFilters.payment_status = value as string;
        } else if (key === 'sponsorship') {
          newFilters.sponsorship = value as string;
        } else if (key === 'search') {
          newFilters.search = value as string;
        } else if (key === 'year') {
          newFilters.year = value as string;
        } else if (key === 'term') {
          newFilters.term = value as string;
        }
      }

      // Reset dependent filters
      if (key === 'college_id') {
        delete newFilters.department_id;
      }

      return newFilters;
    });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    setFilters(prev => ({ ...prev, page: 1, page_size: newSize }));
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({ page: 1, page_size: filters.page_size || 10 });
    setSearchTerm('');
  };

  // Calculate total pages based on the total number of applicants
  const totalPages = React.useMemo(() => {
    if (!allApplicants || !Array.isArray(allApplicants)) return 1;

    // Calculate total pages based on the total number of applicants and page size
    return Math.ceil(allApplicants.length / (filters.page_size || 10));
  }, [allApplicants, filters.page_size]);

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!selectedApplicant) return;

    try {
      await updateStatusMutation.mutateAsync({
        id: selectedApplicant.id,
        data: statusUpdate
      });

      toast.success('Applicant status updated successfully');
      setIsStatusDialogOpen(false);
    } catch (error) {
      console.error('Failed to update status:', error);
      toast.error('Failed to update applicant status');
    }
  };

  // State for document data
  const [documentData, setDocumentData] = useState<any>(null);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(false);

  // Open applicant detail dialog
  const openDetailDialog = async (applicant: Applicant) => {
    console.log('Opening detail dialog for applicant:', applicant);
    console.log('Applicant user ID:', applicant.user.id);
    console.log('Applicant remark:', applicant.remark);

    setSelectedApplicant(applicant);
    setIsDetailDialogOpen(true);

    // Fetch documents directly
    try {
      setIsLoadingDocuments(true);
      console.log('Fetching documents for user ID:', applicant.user.id);
      const docs = await applicantAPI.getApplicantDocuments(applicant.user.id);
      console.log('Fetched documents directly:', docs);
      setDocumentData(docs);
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setIsLoadingDocuments(false);
    }
  };

  // Open status update dialog
  const openStatusDialog = (applicant: Applicant) => {
    setSelectedApplicant(applicant);
    setStatusUpdate({
      registrar_off_status: applicant.registrar_off_status,
      department_status: applicant.department_status,
      payment_status: applicant.payment_status,
      remark: '',
    });
    setIsStatusDialogOpen(true);
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      // Original statuses for registrar and department
      case 'Approved':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Approved</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Rejected</Badge>;
      case 'Pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending</Badge>;

      // Detailed payment statuses
      case 'Initiated':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">Initiated</Badge>;
      case 'Processing':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">Processing</Badge>;
      case 'Completed':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Completed</Badge>;
      case 'Failed':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Failed</Badge>;
      case 'Verified':
        return <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">Verified</Badge>;
      case 'Expired':
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">Expired</Badge>;
      case 'Refunded':
        return <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">Refunded</Badge>;
      case 'Cancelled':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">Cancelled</Badge>;

      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{status}</Badge>;
    }
  };



  // Get program name from different data structures
  const getProgramName = (program: any) => {
    if (!program) return 'N/A';

    if (typeof program === 'object') {
      return program?.program_name || program.name || 'N/A';
    } else if (typeof program === 'number' && allPrograms) {
      const foundProgram = allPrograms.find(p => p.id === program);
      return foundProgram?.program_name || 'N/A';
    }

    return 'N/A';
  };

  // Get department name from different data structures
  const getDepartmentName = (department: any) => {
    if (!department) return 'N/A';

    if (typeof department === 'object') {
      return department?.name || 'N/A';
    } else if (typeof department === 'number' && allDepartments) {
      const foundDepartment = allDepartments.find((d: Department) => d.id === department);
      return foundDepartment?.name || 'N/A';
    }

    return 'N/A';
  };

  // Export to CSV function
  const exportToCSV = () => {
    if (!allApplicants || allApplicants.length === 0) {
      toast.error('No data to export');
      return;
    }

    const headers = [
      'Application Number',
      'GAT Number',
      'Transaction ID',
      'First Name',
      'Last Name',
      'Email',
      'Grandfather Name',
      'Gender',
      'Mobile',
      'Program Level',
      'Program',
      'College',
      'Department',
      'Academic Year',
      'Term',
      'Sponsorship',
      'Registrar Status',
      'Department Status',
      'Payment Status',
      'Created Date'
    ];

    const csvData = allApplicants.map((applicant) => [
      applicant.application_num || '',
      applicant.gat?.gat_no || '',
      applicant.transaction_id || '',
      applicant.user?.first_name || '',
      applicant.user?.last_name || '',
      applicant.user?.email || '',
      applicant.applicant_info?.grandfather_name || '',
      applicant.applicant_info?.gender || '',
      applicant.applicant_info?.mobile || '',
      applicant.applicant_info?.program_level || '',
      getProgramName(applicant.application_info?.program),
      applicant.application_info?.college?.name || '',
      getDepartmentName(applicant.application_info?.department),
      applicant.year_name || '',
      applicant.term_name || '',
      applicant.sponsorship || '',
      applicant.registrar_off_status || '',
      applicant.department_status || '',
      applicant.payment_status || '',
      formatDate(applicant.created_at)
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `applicants_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('CSV export completed successfully');
  };

  // Export to PDF function
  const exportToPDF = () => {
    if (!allApplicants || allApplicants.length === 0) {
      toast.error('No data to export');
      return;
    }

    // Create a new window for PDF generation
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      toast.error('Please allow popups to export PDF');
      return;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Applicants Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
          h1 { color: #1a73c0; text-align: center; margin-bottom: 30px; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f8f9fa; font-weight: bold; color: #1a73c0; }
          tr:nth-child(even) { background-color: #f9f9f9; }
          .status-approved { color: #22c55e; font-weight: bold; }
          .status-rejected { color: #ef4444; font-weight: bold; }
          .status-pending { color: #f59e0b; font-weight: bold; }
          .header-info { margin-bottom: 20px; }
          .export-date { text-align: right; color: #666; margin-bottom: 10px; }
          @media print {
            body { margin: 0; }
            @page { size: landscape; margin: 0.5in; }
          }
        </style>
      </head>
      <body>
        <div class="export-date">Generated on: ${new Date().toLocaleString()}</div>
        <h1>Applicants Report</h1>
        <div class="header-info">
          <strong>Total Applicants:</strong> ${allApplicants.length}
        </div>
        <table>
          <thead>
            <tr>
              <th>Application #</th>
              <th>GAT #</th>
              <th>Name</th>
              <th>Email</th>
              <th>Gender</th>
              <th>Mobile</th>
              <th>Program</th>
              <th>Year</th>
              <th>Term</th>
              <th>Sponsorship</th>
              <th>Registrar</th>
              <th>Department</th>
              <th>Payment</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            ${allApplicants.map(applicant => `
              <tr>
                <td>${applicant.application_num || ''}</td>
                <td>${applicant.gat?.gat_no || ''}</td>
                <td>${applicant.user?.first_name || ''} ${applicant.user?.last_name || ''}</td>
                <td>${applicant.user?.email || ''}</td>
                <td>${applicant.applicant_info?.gender || ''}</td>
                <td>${applicant.applicant_info?.mobile || ''}</td>
                <td>${getProgramName(applicant.application_info?.program)}</td>
                <td>${applicant.year_name || ''}</td>
                <td>${applicant.term_name || ''}</td>
                <td>${applicant.sponsorship || ''}</td>
                <td class="status-${applicant.registrar_off_status?.toLowerCase()}">${applicant.registrar_off_status || ''}</td>
                <td class="status-${applicant.department_status?.toLowerCase()}">${applicant.department_status || ''}</td>
                <td class="status-${applicant.payment_status?.toLowerCase()}">${applicant.payment_status || ''}</td>
                <td>${formatDate(applicant.created_at)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
      toast.success('PDF export initiated');
    }, 500);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Applicant Management</CardTitle>
                <CardDescription className="mt-1">
                  View and manage applicants in the application portal
                </CardDescription>
              </div>
            </div>

          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="mb-6 space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
                <Search className="h-4 w-4 mr-2" />
                Search & Filter
              </h3>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                  <Input
                    id="search-applicants"
                    name="search-applicants"
                    placeholder="Search applicants by name, email, ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                  />
                </div>
              </div>
            </div>

            <div className="p-5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                <div className="flex items-center">
                  <div className="bg-[#1a73c0] p-1.5 rounded-md shadow-sm mr-3">
                    <Filter className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-base font-medium text-[#1a73c0]">Filter Options</h3>
                    <p className="text-xs text-gray-500 mt-0.5">Refine your search with these filters</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 mt-2 md:mt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportToCSV}
                    className="text-green-600 border-green-200 hover:bg-green-50 transition-all"
                    disabled={!allApplicants || allApplicants.length === 0}
                  >
                    <FileSpreadsheet className="h-3.5 w-3.5 mr-1.5" />
                    Export CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportToPDF}
                    className="text-red-600 border-red-200 hover:bg-red-50 transition-all"
                    disabled={!allApplicants || allApplicants.length === 0}
                  >
                    <Download className="h-3.5 w-3.5 mr-1.5" />
                    Export PDF
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetFilters}
                    className="text-blue-600 border-blue-200 hover:bg-blue-50 transition-all"
                  >
                    <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
                    Reset Filters
                  </Button>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg border border-blue-200 shadow-sm">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                  <div className="space-y-3 group">
                    <Label htmlFor="college" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <Building className="h-4 w-4 text-blue-500" />
                      </div>
                      College
                    </Label>
                    <Select
                      name="college"
                      value={filters.college_id?.toString() || ''}
                      onValueChange={(value) => handleFilterChange('college_id', value ? parseInt(value) : null)}
                    >
                      <SelectTrigger
                        id="college"
                        className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                      >
                        <SelectValue placeholder="All Colleges" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Colleges</SelectItem>
                        {colleges?.map((college: College) => (
                          <SelectItem key={college.id} value={college.id.toString()}>
                            {college.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3 group">
                    <Label htmlFor="department" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <Building className="h-4 w-4 text-blue-500" />
                      </div>
                      Department
                    </Label>
                    <Select
                      name="department"
                      value={filters.department_id?.toString() || ''}
                      onValueChange={(value) => handleFilterChange('department_id', value ? parseInt(value) : null)}
                      disabled={!filters.college_id}
                    >
                      <SelectTrigger
                        id="department"
                        className={cn(
                          "border-blue-200 focus:ring-blue-400 shadow-sm transition-all rounded-md h-9 text-sm",
                          !filters.college_id ? "opacity-70 cursor-not-allowed" : "hover:border-blue-300"
                        )}
                      >
                        <SelectValue placeholder="All Departments" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Departments</SelectItem>
                        {departments?.map((department: Department) => (
                          <SelectItem key={department.id} value={department.id.toString()}>
                            {department.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                  </div>

                  <div className="space-y-3 group">
                    <Label htmlFor="program" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <GraduationCap className="h-4 w-4 text-blue-500" />
                      </div>
                      Program
                    </Label>
                    <Select
                      name="program"
                      value={filters.program_id?.toString() || ''}
                      onValueChange={(value) => handleFilterChange('program_id', value ? parseInt(value) : null)}
                    >
                      <SelectTrigger
                        id="program"
                        className={cn(
                          "border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm",
                          isLoadingPrograms ? "animate-pulse" : ""
                        )}
                      >
                        <SelectValue placeholder={isLoadingPrograms ? "Loading programs..." : "All Programs"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Programs</SelectItem>
                        {allPrograms && allPrograms.length > 0 ? (
                          allPrograms.map((program: Program) => (
                            <SelectItem key={program.id} value={program.id.toString()}>
                              {program.program_name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled>No programs found</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3 group">
                    <Label htmlFor="registrar_status" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <CheckCircle className="h-4 w-4 text-blue-500" />
                      </div>
                      Registrar Status
                    </Label>
                    <Select
                      name="registrar_status"
                      value={filters.registrar_off_status || ''}
                      onValueChange={(value) => handleFilterChange('registrar_off_status', value)}
                    >
                      <SelectTrigger
                        id="registrar_status"
                        className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                      >
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="Approved" className="text-green-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                            Approved
                          </div>
                        </SelectItem>
                        <SelectItem value="Rejected" className="text-red-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                            Rejected
                          </div>
                        </SelectItem>
                        <SelectItem value="Pending" className="text-yellow-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                            Pending
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3 group">
                    <Label htmlFor="department_status" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <Building className="h-4 w-4 text-blue-500" />
                      </div>
                      Department Status
                    </Label>
                    <Select
                      name="department_status"
                      value={filters.department_status || ''}
                      onValueChange={(value) => handleFilterChange('department_status', value)}
                    >
                      <SelectTrigger
                        id="department_status"
                        className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                      >
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="Approved" className="text-green-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                            Approved
                          </div>
                        </SelectItem>
                        <SelectItem value="Rejected" className="text-red-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                            Rejected
                          </div>
                        </SelectItem>
                        <SelectItem value="Pending" className="text-yellow-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                            Pending
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3 group">
                    <Label htmlFor="payment_status" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <CreditCard className="h-4 w-4 text-blue-500" />
                      </div>
                      Payment Status
                    </Label>
                    <Select
                      name="payment_status"
                      value={filters.payment_status || ''}
                      onValueChange={(value) => handleFilterChange('payment_status', value)}
                    >
                      <SelectTrigger
                        id="payment_status"
                        className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                      >
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="Pending" className="text-yellow-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                            Pending
                          </div>
                        </SelectItem>
                        <SelectItem value="Initiated" className="text-blue-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                            Initiated
                          </div>
                        </SelectItem>
                        <SelectItem value="Processing" className="text-purple-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                            Processing
                          </div>
                        </SelectItem>
                        <SelectItem value="Completed" className="text-green-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                            Completed
                          </div>
                        </SelectItem>
                        <SelectItem value="Failed" className="text-red-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                            Failed
                          </div>
                        </SelectItem>
                        <SelectItem value="Verified" className="text-emerald-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-emerald-500 mr-2"></div>
                            Verified
                          </div>
                        </SelectItem>
                        <SelectItem value="Expired" className="text-orange-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-orange-500 mr-2"></div>
                            Expired
                          </div>
                        </SelectItem>
                        <SelectItem value="Refunded" className="text-indigo-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-indigo-500 mr-2"></div>
                            Refunded
                          </div>
                        </SelectItem>
                        <SelectItem value="Cancelled" className="text-gray-700 font-medium">
                          <div className="flex items-center">
                            <div className="w-2 h-2 rounded-full bg-gray-500 mr-2"></div>
                            Cancelled
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3 group">
                    <Label htmlFor="year" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <Calendar className="h-4 w-4 text-blue-500" />
                      </div>
                      Academic Year
                    </Label>
                    <Select
                      name="year"
                      value={filters.year || ''}
                      onValueChange={(value) => handleFilterChange('year', value)}
                    >
                      <SelectTrigger
                        id="year"
                        className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                      >
                        <SelectValue placeholder="All Years" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Years</SelectItem>
                        {years?.map((year: Year) => (
                          <SelectItem key={year.uuid} value={year.uuid}>
                            {year.year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3 group">
                    <Label htmlFor="term" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                      <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                        <BookOpen className="h-4 w-4 text-blue-500" />
                      </div>
                      Term
                    </Label>
                    <Select
                      name="term"
                      value={filters.term || ''}
                      onValueChange={(value) => handleFilterChange('term', value)}
                    >
                      <SelectTrigger
                        id="term"
                        className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                      >
                        <SelectValue placeholder="All Terms" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Terms</SelectItem>
                        {terms?.map((term: Term) => (
                          <SelectItem key={term.id} value={term.id}>
                            {term.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Active Filters Summary */}
                {Object.keys(filters).filter(key =>
                  key !== 'page' &&
                  key !== 'page_size' &&
                  filters[key as keyof ApplicantListParams] !== undefined &&
                  filters[key as keyof ApplicantListParams] !== null &&
                  filters[key as keyof ApplicantListParams] !== ''
                ).length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-4 pt-3 border-t border-blue-100">
                    {filters.college_id && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <Building className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">College:</span>
                        <span className="font-medium mr-1">{colleges?.find((c: College) => c.id === filters.college_id)?.name || 'Unknown'}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('college_id', null)}
                        />
                      </div>
                    )}

                    {filters.department_id && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <Building className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Department:</span>
                        <span className="font-medium mr-1">{departments?.find((d: Department) => d.id === filters.department_id)?.name || 'Unknown'}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('department_id', null)}
                        />
                      </div>
                    )}

                    {filters.program_id && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <GraduationCap className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Program:</span>
                        <span className="font-medium mr-1">{allPrograms?.find((p: Program) => p.id === filters.program_id)?.program_name || 'Unknown'}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('program_id', null)}
                        />
                      </div>
                    )}

                    {filters.registrar_off_status && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <CheckCircle className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Registrar:</span>
                        <span className="font-medium mr-1">{filters.registrar_off_status}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('registrar_off_status', null)}
                        />
                      </div>
                    )}

                    {filters.department_status && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <Building className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Dept Status:</span>
                        <span className="font-medium mr-1">{filters.department_status}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('department_status', null)}
                        />
                      </div>
                    )}

                    {filters.payment_status && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <CreditCard className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Payment:</span>
                        <span className="font-medium mr-1">{filters.payment_status}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('payment_status', null)}
                        />
                      </div>
                    )}

                    {filters.search && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <Search className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Search:</span>
                        <span className="font-medium mr-1">"{filters.search}"</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => {
                            setSearchTerm('');
                            handleFilterChange('search', null);
                          }}
                        />
                      </div>
                    )}

                    {filters.year && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <Calendar className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Year:</span>
                        <span className="font-medium mr-1">{years?.find((y: Year) => y.uuid === filters.year)?.year || 'Unknown'}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('year', null)}
                        />
                      </div>
                    )}

                    {filters.term && (
                      <div className="flex items-center bg-blue-50 px-2 py-1 rounded-full text-xs text-blue-700 border border-blue-200">
                        <BookOpen className="h-3 w-3 mr-1 text-blue-500" />
                        <span className="mr-1">Term:</span>
                        <span className="font-medium mr-1">{terms?.find((t: Term) => t.id === filters.term)?.name || 'Unknown'}</span>
                        <XCircle
                          className="h-3 w-3 ml-1 text-gray-400 hover:text-red-500 cursor-pointer"
                          onClick={() => handleFilterChange('term', null)}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[5%] text-[#1a73c0] font-medium">Actions</TableHead>
                    <TableHead className="w-[5%] text-[#1a73c0] font-medium">Registrar</TableHead>
                    <TableHead className="w-[5%] text-[#1a73c0] font-medium">Department</TableHead>
                    <TableHead className="w-[5%] text-[#1a73c0] font-medium">Payment</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">Application</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">GAT No</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">Transaction ID</TableHead>
                    <TableHead className="w-[13%] text-[#1a73c0] font-medium">Applicant & Grandfather</TableHead>
                    <TableHead className="w-[6%] text-[#1a73c0] font-medium">Gender</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">Program Level</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">Mobile</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">Program</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">Year</TableHead>
                    <TableHead className="w-[7%] text-[#1a73c0] font-medium">Term</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={14} className="text-center py-8">
                        <div className="flex justify-center items-center">
                          <RefreshCw className="h-6 w-6 animate-spin mr-2 text-blue-500" />
                          <span className="text-blue-600">Loading applicants...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={14} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center">
                          <XCircle className="h-8 w-8 text-red-500 mb-2" />
                          <span className="text-red-500 font-medium mb-2">Error loading applicants</span>
                          <p className="text-gray-500 mb-3 text-sm">There was a problem fetching the data. Please try again.</p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => refetch()}
                            className="flex items-center gap-1"
                          >
                            <RefreshCw className="h-4 w-4 mr-1" />
                            Retry
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : !allApplicants || !Array.isArray(allApplicants) || allApplicants.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={14} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center">
                          <Search className="h-8 w-8 text-gray-400 mb-2" />
                          <span className="font-medium mb-2">No applicants found</span>
                          <p className="text-gray-500 mb-3 text-sm">Try adjusting your filters to find what you're looking for.</p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={resetFilters}
                            className="flex items-center gap-1"
                          >
                            <RotateCcw className="h-4 w-4 mr-1.5" />
                            Reset Filters
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    applicants.map((applicant) => (
                      <TableRow key={applicant.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDetailDialog(applicant)}
                              title="View Details"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openStatusDialog(applicant)}
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                              title="Update Status"
                            >
                              <Clock className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(applicant.registrar_off_status)}</TableCell>
                        <TableCell>{getStatusBadge(applicant.department_status)}</TableCell>
                        <TableCell>{getStatusBadge(applicant.payment_status)}</TableCell>
                        <TableCell className="font-medium text-[#1a73c0]">{applicant.application_num}</TableCell>
                        <TableCell>{applicant.gat?.gat_no || 'N/A'}</TableCell>
                        <TableCell className="text-gray-700">{applicant.transaction_id}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium">{applicant.user.first_name} {applicant.user.last_name}</span>
                            <span className="text-gray-500 text-xs">Grandfather: {applicant.applicant_info?.grandfather_name || 'N/A'}</span>
                            <span className="text-gray-500 text-xs">{applicant.user.email}</span>
                          </div>
                        </TableCell>
                        <TableCell>{applicant.applicant_info?.gender || 'N/A'}</TableCell>
                        <TableCell>{applicant.applicant_info?.program_level || 'N/A'}</TableCell>
                        <TableCell>{applicant.applicant_info?.mobile || 'N/A'}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{getProgramName(applicant.application_info?.program)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-sm">{applicant.year_name || 'N/A'}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-sm">{applicant.term_name || 'N/A'}</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {allApplicants && allApplicants.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={filters.page_size}
                  onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {((filters.page || 1) - 1) * (filters.page_size || 10) + 1} - {Math.min(((filters.page || 1) - 1) * (filters.page_size || 10) + applicants.length, ((filters.page || 1)) * (filters.page_size || 10))}
                  </span> of <span className="font-medium text-[#1a73c0]">{allApplicants?.length || 0}</span> applicants
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-lg overflow-hidden shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={!filters.page || filters.page <= 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border border-blue-200",
                      !filters.page || filters.page <= 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronLeft className="h-4 w-4 -ml-0.5" />
                    <ChevronLeft className="h-4 w-4 -ml-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(Math.max(1, (filters.page || 1) - 1))}
                    disabled={!filters.page || filters.page <= 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-t border-b border-blue-200",
                      !filters.page || filters.page <= 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Dynamic page number buttons with ellipsis for large page counts */}
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-t border-b border-blue-200",
                          (filters.page || 1) === number
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show a condensed version with ellipsis
                    <>
                      {/* Always show first page */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-t border-b border-blue-200",
                          (filters.page || 1) === 1
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {(filters.page || 1) > 3 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-t border-b border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number =>
                          number > 1 &&
                          number < totalPages &&
                          (
                            number === (filters.page || 1) - 1 ||
                            number === (filters.page || 1) ||
                            number === (filters.page || 1) + 1 ||
                            ((filters.page || 1) <= 3 && number <= 4) ||
                            ((filters.page || 1) >= totalPages - 2 && number >= totalPages - 3)
                          )
                        )
                        .map(number => (
                          <Button
                            key={number}
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={cn(
                              "h-9 w-9 p-0 rounded-none border-t border-b border-blue-200",
                              (filters.page || 1) === number
                                ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                                : "bg-white text-gray-700 hover:bg-blue-50"
                            )}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {(filters.page || 1) < totalPages - 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-t border-b border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-t border-b border-blue-200",
                            (filters.page || 1) === totalPages
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange((filters.page || 1) + 1)}
                    disabled={(filters.page || 1) >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-t border-b border-blue-200",
                      (filters.page || 1) >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={(filters.page || 1) >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border border-blue-200",
                      (filters.page || 1) >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                    <ChevronRight className="h-4 w-4 -ml-2" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Applicant Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 -mx-6 px-6 py-4 rounded-t-lg border-b border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl text-blue-700 flex items-center">
                  <UserCircle className="h-6 w-6 mr-2 text-blue-500" />
                  Applicant Details
                </DialogTitle>
                <DialogDescription className="text-blue-600">
                  Application #{selectedApplicant?.application_num} - {selectedApplicant?.user.first_name} {selectedApplicant?.user.last_name}
                </DialogDescription>
              </div>
              {selectedApplicant && (
                <div className="flex items-center space-x-2">
                  <Badge variant={selectedApplicant.registrar_off_status === 'Approved' ? 'success' :
                              selectedApplicant.registrar_off_status === 'Rejected' ? 'destructive' : 'outline'}>
                    Registrar: {selectedApplicant.registrar_off_status}
                  </Badge>
                  <Badge variant={selectedApplicant.department_status === 'Approved' ? 'success' :
                              selectedApplicant.department_status === 'Rejected' ? 'destructive' : 'outline'}>
                    Department: {selectedApplicant.department_status}
                  </Badge>
                </div>
              )}
            </div>
          </DialogHeader>

          {selectedApplicant && (
            <div className="grid gap-6 py-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-blue-700 flex items-center border-b pb-2">
                    <User className="h-4 w-4 mr-2" />
                    Personal Information
                  </h3>
                  <div className="bg-white rounded-lg border border-blue-100 shadow-sm overflow-hidden">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 flex items-center space-x-3 border-b border-blue-100">
                      <div className="bg-blue-100 rounded-full p-3">
                        <UserCircle className="h-8 w-8 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-lg text-blue-700">{selectedApplicant.user.first_name} {selectedApplicant.user.last_name}</div>
                        <div className="text-sm text-blue-600">Grandfather: {selectedApplicant.applicant_info?.grandfather_name || 'N/A'}</div>
                      </div>
                    </div>
                    <div className="p-4 grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <div className="text-xs text-gray-500 flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          Email
                        </div>
                        <div className="font-medium text-sm">{selectedApplicant.user.email}</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-xs text-gray-500 flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          Mobile
                        </div>
                        <div className="font-medium text-sm">{selectedApplicant.applicant_info?.mobile || 'N/A'}</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-xs text-gray-500 flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          Gender
                        </div>
                        <div className="font-medium text-sm">{selectedApplicant.applicant_info?.gender || 'N/A'}</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-xs text-gray-500 flex items-center">
                          <GraduationCap className="h-3 w-3 mr-1" />
                          Program Level
                        </div>
                        <div className="font-medium text-sm">{selectedApplicant.applicant_info?.program_level || 'N/A'}</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-xs text-gray-500 flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Year
                        </div>
                        <div className="font-medium text-sm">{selectedApplicant.year || 'N/A'}</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-xs text-gray-500 flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          Applied On
                        </div>
                        <div className="font-medium text-sm">{formatDate(selectedApplicant.created_at)}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-blue-700 flex items-center border-b pb-2">
                    <FileText className="h-4 w-4 mr-2" />
                    Application Information
                  </h3>
                  <div className="bg-white rounded-lg border border-blue-100 shadow-sm overflow-hidden">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 flex items-center space-x-3 border-b border-blue-100">
                      <div className="bg-blue-100 rounded-full p-3">
                        <Bookmark className="h-8 w-8 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-lg text-blue-700">Application #{selectedApplicant.application_num}</div>
                        <div className="text-sm text-blue-600">Transaction ID: {selectedApplicant.transaction_id}</div>
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500 flex items-center">
                            <Award className="h-3 w-3 mr-1" />
                            GAT Number
                          </div>
                          <div className="font-medium text-sm">{selectedApplicant.gat?.gat_no || 'N/A'}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500 flex items-center">
                            <BarChart className="h-3 w-3 mr-1" />
                            GAT Result
                          </div>
                          <div className="font-medium text-sm">{selectedApplicant.gat?.GAT_Result || 'N/A'}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500 flex items-center">
                            <Building className="h-3 w-3 mr-1" />
                            College
                          </div>
                          <div className="font-medium text-sm">{selectedApplicant.application_info?.college?.name || 'N/A'}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500 flex items-center">
                            <Briefcase className="h-3 w-3 mr-1" />
                            Department
                          </div>
                          <div className="font-medium text-sm">{getDepartmentName(selectedApplicant.application_info?.department)}</div>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-3 rounded-md mb-4">
                        <div className="text-xs text-blue-700 font-medium mb-2 flex items-center">
                          <GraduationCap className="h-3 w-3 mr-1" />
                          Program Details
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500">Program</div>
                            <div className="font-medium text-sm">{getProgramName(selectedApplicant.application_info?.program)}</div>
                          </div>
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500">Field of Study</div>
                            <div className="font-medium text-sm">{selectedApplicant.application_info?.field_of_study?.name || 'N/A'}</div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between bg-gray-50 p-3 rounded-md">
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500 flex items-center">
                            <Briefcase className="h-3 w-3 mr-1" />
                            Sponsorship
                          </div>
                          <div className="font-medium text-sm">{selectedApplicant.sponsorship || 'N/A'}</div>
                        </div>
                        <Badge variant="outline" className="bg-white">
                          {selectedApplicant.year} Academic Year
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-blue-700 flex items-center border-b pb-2">
                    <GraduationCap className="h-4 w-4 mr-2" />
                    Educational Information
                  </h3>
                  <div className="bg-white rounded-lg border border-blue-100 shadow-sm overflow-hidden">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 border-b border-blue-100">
                      <div className="font-medium text-blue-700 flex items-center">
                        <School className="h-4 w-4 mr-2" />
                        Academic Background
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="mb-4">
                        <div className="text-xs font-medium text-blue-700 mb-2 flex items-center">
                          <BookOpen className="h-3 w-3 mr-1" />
                          Undergraduate Education
                        </div>
                        <div className="bg-gray-50 p-3 rounded-md">
                          <div className="grid grid-cols-1 gap-3">
                            <div className="flex justify-between items-center">
                              <div className="space-y-1">
                                <div className="text-xs text-gray-500">University</div>
                                <div className="font-medium text-sm">{selectedApplicant.applicant_info?.ug_university || 'N/A'}</div>
                              </div>
                              {selectedApplicant.applicant_info?.ug_CGPA && (
                                <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                                  CGPA: {selectedApplicant.applicant_info?.ug_CGPA}
                                </Badge>
                              )}
                            </div>
                            <div>
                              <div className="text-xs text-gray-500">Field of Study</div>
                              <div className="font-medium text-sm">{selectedApplicant.applicant_info?.ug_field_of_study || 'N/A'}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {(selectedApplicant.applicant_info?.pg_university || selectedApplicant.applicant_info?.pg_field_of_study || selectedApplicant.applicant_info?.pg_CGPA) && (
                        <div>
                          <div className="text-xs font-medium text-blue-700 mb-2 flex items-center">
                            <BookOpen className="h-3 w-3 mr-1" />
                            Postgraduate Education
                          </div>
                          <div className="bg-gray-50 p-3 rounded-md">
                            <div className="grid grid-cols-1 gap-3">
                              <div className="flex justify-between items-center">
                                <div className="space-y-1">
                                  <div className="text-xs text-gray-500">University</div>
                                  <div className="font-medium text-sm">{selectedApplicant.applicant_info?.pg_university || 'N/A'}</div>
                                </div>
                                {selectedApplicant.applicant_info?.pg_CGPA && (
                                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                                    CGPA: {selectedApplicant.applicant_info?.pg_CGPA}
                                  </Badge>
                                )}
                              </div>
                              <div>
                                <div className="text-xs text-gray-500">Field of Study</div>
                                <div className="font-medium text-sm">{selectedApplicant.applicant_info?.pg_field_of_study || 'N/A'}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-blue-700 flex items-center border-b pb-2">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Status Information
                  </h3>
                  <div className="bg-white rounded-lg border border-blue-100 shadow-sm overflow-hidden">
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 border-b border-blue-100">
                      <div className="font-medium text-blue-700 flex items-center">
                        <ClipboardCheck className="h-4 w-4 mr-2" />
                        Application Status
                      </div>
                    </div>

                    <div className="p-4 space-y-4">
                      <div className={`p-3 rounded-md ${selectedApplicant.registrar_off_status === 'Approved' ? 'bg-green-50 border border-green-100' :
                                      selectedApplicant.registrar_off_status === 'Rejected' ? 'bg-red-50 border border-red-100' :
                                      'bg-gray-50 border border-gray-100'}`}>
                        <div className="flex justify-between items-center mb-2">
                          <div className="text-sm font-medium flex items-center">
                            <Stamp className="h-4 w-4 mr-2 text-blue-600" />
                            Registrar Status
                          </div>
                          {getStatusBadge(selectedApplicant.registrar_off_status)}
                        </div>
                        {selectedApplicant.reg_approved_by && (
                          <div className="flex items-center text-xs text-gray-600 mt-2 bg-white p-2 rounded border">
                            <User className="h-3 w-3 mr-1 text-blue-500" />
                            <span>Processed by: <span className="font-medium">{selectedApplicant.reg_approved_by.first_name} {selectedApplicant.reg_approved_by.last_name}</span></span>
                          </div>
                        )}
                      </div>

                      <div className={`p-3 rounded-md ${selectedApplicant.department_status === 'Approved' ? 'bg-green-50 border border-green-100' :
                                      selectedApplicant.department_status === 'Rejected' ? 'bg-red-50 border border-red-100' :
                                      'bg-gray-50 border border-gray-100'}`}>
                        <div className="flex justify-between items-center mb-2">
                          <div className="text-sm font-medium flex items-center">
                            <Building className="h-4 w-4 mr-2 text-blue-600" />
                            Department Status
                          </div>
                          {getStatusBadge(selectedApplicant.department_status)}
                        </div>
                        {selectedApplicant.dep_approved_by && (
                          <div className="flex items-center text-xs text-gray-600 mt-2 bg-white p-2 rounded border">
                            <User className="h-3 w-3 mr-1 text-blue-500" />
                            <span>Processed by: <span className="font-medium">{selectedApplicant.dep_approved_by.first_name} {selectedApplicant.dep_approved_by.last_name}</span></span>
                          </div>
                        )}
                      </div>

                      <div className={`p-3 rounded-md ${selectedApplicant.payment_status === 'Approved' ? 'bg-green-50 border border-green-100' :
                                      selectedApplicant.payment_status === 'Rejected' ? 'bg-red-50 border border-red-100' :
                                      'bg-gray-50 border border-gray-100'}`}>
                        <div className="flex justify-between items-center">
                          <div className="text-sm font-medium flex items-center">
                            <CreditCard className="h-4 w-4 mr-2 text-blue-600" />
                            Payment Status
                          </div>
                          {getStatusBadge(selectedApplicant.payment_status)}
                        </div>
                      </div>

                      <div className="p-3 bg-amber-50 rounded-md border border-amber-100">
                        <div className="text-sm font-medium flex items-center mb-2 text-amber-800">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Remarks
                        </div>
                        <div className="text-sm bg-white p-3 rounded border border-amber-100 text-gray-700">
                          {selectedApplicant.remark ? (
                            selectedApplicant.remark
                          ) : (
                            <span className="text-gray-400 italic">No remarks available</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-sm font-medium text-blue-700 flex items-center border-b pb-2">
                  <FileText className="h-4 w-4 mr-2" />
                  Uploaded Documents
                </h3>
                <div className="bg-white rounded-lg border border-blue-100 shadow-sm overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 border-b border-blue-100">
                    <div className="font-medium text-blue-700 flex items-center">
                      <Files className="h-4 w-4 mr-2" />
                      Supporting Documentation
                    </div>
                  </div>

                  <div className="p-4">
                    {isLoadingDocuments ? (
                      <div className="text-center py-8">
                        <div className="mx-auto w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-4 animate-pulse">
                          <FileText className="h-8 w-8 text-blue-400" />
                        </div>
                        <h3 className="text-lg font-medium text-blue-700 mb-2">Loading Documents...</h3>
                        <p className="text-gray-500 max-w-md mx-auto">
                          Please wait while we fetch the documents for this applicant.
                        </p>
                      </div>
                    ) : documentData && Object.keys(documentData).length > 0 && Object.values(documentData).some(val => val) ? (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {documentData.degree && (
                          <a
                            href={documentData.degree}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Degree Certificate</div>
                          </a>
                        )}
                        {documentData.sponsorship && (
                          <a
                            href={documentData.sponsorship}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Sponsorship Letter</div>
                          </a>
                        )}
                        {documentData.student_copy && (
                          <a
                            href={documentData.student_copy}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Student Copy</div>
                          </a>
                        )}
                        {documentData.recommendation && (
                          <a
                            href={documentData.recommendation}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Recommendation</div>
                          </a>
                        )}
                        {documentData.publication && (
                          <a
                            href={documentData.publication}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Publication</div>
                          </a>
                        )}
                        {documentData.conceptnote && (
                          <a
                            href={documentData.conceptnote}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Concept Note</div>
                          </a>
                        )}
                        {documentData.grade_12_certificate && (
                          <a
                            href={documentData.grade_12_certificate}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Grade 12 Certificate</div>
                          </a>
                        )}
                        {documentData.grade_9_12_transcript && (
                          <a
                            href={documentData.grade_9_12_transcript}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group relative p-4 bg-white border border-blue-100 rounded-md hover:border-blue-300 hover:shadow-md transition-all flex flex-col items-center"
                          >
                            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md opacity-0 group-hover:opacity-100 transition-opacity">
                              View
                            </div>
                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-3">
                              <FileText className="h-6 w-6 text-blue-500" />
                            </div>
                            <div className="text-sm font-medium text-center text-gray-700">Grade 9-12 Transcript</div>
                          </a>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                          <FileText className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-700 mb-2">No Documents Available</h3>
                        <p className="text-gray-500 max-w-md mx-auto">
                          This applicant has not uploaded any supporting documents yet.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
              Close
            </Button>
            <Button onClick={() => {
              setIsDetailDialogOpen(false);
              openStatusDialog(selectedApplicant!);
            }}>
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Status Update Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Applicant Status</DialogTitle>
            <DialogDescription>
              Change the status for {selectedApplicant?.user.first_name} {selectedApplicant?.user.last_name}
            </DialogDescription>
          </DialogHeader>

          {selectedApplicant && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="registrar_status">Registrar</Label>
                  <Select
                    name="update_registrar_status"
                    value={statusUpdate.registrar_off_status}
                    onValueChange={(value) => setStatusUpdate(prev => ({ ...prev, registrar_off_status: value }))}
                  >
                    <SelectTrigger id="registrar_status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Approved">Approved</SelectItem>
                      <SelectItem value="Rejected">Rejected</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="department_status">Department</Label>
                  <Select
                    name="update_department_status"
                    value={statusUpdate.department_status}
                    onValueChange={(value) => setStatusUpdate(prev => ({ ...prev, department_status: value }))}
                  >
                    <SelectTrigger id="department_status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Approved">Approved</SelectItem>
                      <SelectItem value="Rejected">Rejected</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="payment_status">Payment</Label>
                  <Select
                    name="update_payment_status"
                    value={statusUpdate.payment_status}
                    onValueChange={(value) => setStatusUpdate(prev => ({ ...prev, payment_status: value }))}
                  >
                    <SelectTrigger id="payment_status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Initiated">Initiated</SelectItem>
                      <SelectItem value="Processing">Processing</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="Failed">Failed</SelectItem>
                      <SelectItem value="Verified">Verified</SelectItem>
                      <SelectItem value="Expired">Expired</SelectItem>
                      <SelectItem value="Refunded">Refunded</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="remark">Remark</Label>
                  <Textarea
                    id="remark"
                    name="remark"
                    placeholder="Add a remark about this status change"
                    value={statusUpdate.remark}
                    onChange={(e) => setStatusUpdate(prev => ({ ...prev, remark: e.target.value }))}
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleStatusUpdate}
              disabled={isUpdatingStatus}
            >
              {isUpdatingStatus ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Status'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ApplicantManagement;