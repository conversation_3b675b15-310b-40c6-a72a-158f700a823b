

import { Link } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  GraduationCap, BookOpen, Building, UserPlus, LogIn, Loader2
} from 'lucide-react';
import FloatingHexagons from '@/components/FloatingHexagons';
import DocumentTitle from '@/components/DocumentTitle';
import { settingsAPI } from '@/services/api';

// Define Program interface
interface Program {
  id: number;
  program_code: string;
  program_name: string;
  program_type?: string;
  registration_fee?: string | number;
}

const Index = () => {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const userData = localStorage.getItem('user');
  const user = userData ? JSON.parse(userData) : null;

  // State for programs
  const [programs, setPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for organization settings
  const [settings, setSettings] = useState<{
    systemName: string;
    organizationName: string;
  }>({
    systemName: '',
    organizationName: ''
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Fetch organization settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          setSettings({
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || ''
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Fetch programs from API
  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Fetching programs from API...');
        const response = await fetch('http://localhost:8000/api/programs/public/');

        if (!response.ok) {
          throw new Error(`API returned status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Programs data from API:', data);

        // Add program_type based on program_code if not present
        const processedData = data.map((program: Program) => {
          if (!program.program_type && program.program_code) {
            const code = program.program_code.toLowerCase();
            if (code.includes('undergraduate')) {
              return { ...program, program_type: 'undergraduate' };
            } else if (code.includes('postgraduate')) {
              return { ...program, program_type: 'postgraduate' };
            }
          }
          return program;
        });

        setPrograms(processedData);
      } catch (err) {
        console.error('Error fetching programs:', err);
        setError('Failed to load programs');
      } finally {
        setLoading(false);
      }
    };

    fetchPrograms();
  }, []);

  // Custom pattern background for University of Gondar themed hero section
  const heroPatternImage = '/images/university-pattern.svg'; // Custom SVG pattern with educational elements

  return (
    <Layout>
      <DocumentTitle pageTitle="Home" />
      {/* Hero Section with Animated Patterned Background */}
      <section className="relative py-8 md:py-12 h-[350px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          {isSettingsLoading ? (
            <div className="h-10 w-64 bg-white/20 animate-pulse rounded mx-auto"></div>
          ) : (
            <h1 className="text-2xl md:text-3xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
              {settings.organizationName} {settings.systemName}
            </h1>
          )}
          <p className="mt-4 text-base md:text-lg text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Your gateway to academic excellence at Ethiopia's premier institution. Apply for undergraduate and postgraduate programs through our streamlined digital platform.
          </p>
          <div className="mt-6 flex flex-col sm:flex-row gap-4 animate-slide-in relative z-10" style={{animationDelay: '0.2s'}}>
            {!isAuthenticated && (
              <>
                <Link to="/register">
                  <Button className="bg-white text-[#1a73c0] hover:bg-gray-100 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Register
                  </Button>
                </Link>
                <Link to="/login">
                  <Button className="bg-[#1a73c0] text-white hover:bg-[#1a73c0]/80 border-none font-medium shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                    <LogIn className="mr-2 h-4 w-4" />
                    Login
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gondar mb-4">Our Programs</h2>
            <div className="h-1 w-20 bg-gondar-accent mx-auto mb-6 rounded-full"></div>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">
              Discover the diverse academic opportunities available at {isSettingsLoading ? (
                <span className="inline-block h-5 w-32 bg-gray-200 animate-pulse rounded align-middle"></span>
              ) : (
                settings.organizationName
              )}
            </p>
          </div>

          {/* Loading state */}
          {loading && (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gondar" />
              <span className="ml-2 text-gondar">Loading programs...</span>
            </div>
          )}

          {/* Error state */}
          {!loading && error && (
            <div className="text-center py-12">
              <p className="text-red-500">{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
            </div>
          )}

          {/* Programs grid */}
          {!loading && !error && (
            <div className="flex flex-wrap justify-center items-center gap-8 px-4 max-w-7xl mx-auto">
              {programs.length > 0 ? (
                programs.map((program) => {
                  // Determine icon based on program type
                  let icon: React.ReactNode;
                  if (program.program_type === 'undergraduate' || program.program_code?.toLowerCase().includes('undergraduate')) {
                    icon = <GraduationCap size={40} className="text-gondar" />;
                  } else if (program.program_type === 'postgraduate' || program.program_code?.toLowerCase().includes('postgraduate')) {
                    icon = <BookOpen size={40} className="text-gondar" />;
                  } else {
                    icon = <Building size={40} className="text-gondar" />;
                  }

                  return (
                    <Card
                      key={program.id}
                      className="overflow-hidden border border-gray-100 shadow-md hover:shadow-xl transition-all duration-300 hover:border-gondar-light/30 group w-72 sm:w-80 flex justify-center items-center"
                    >
                      <CardContent className="p-6 flex flex-col items-center justify-center text-center h-full w-full">
                        <div className="rounded-full bg-gondar-light/10 p-4 mb-4 group-hover:bg-gondar-light/20 transition-colors mx-auto">
                          {icon}
                        </div>
                        <h3 className="text-xl font-bold mb-3 text-gondar text-center w-full">{program.program_code}</h3>
                        <p className="text-gray-600 text-sm text-center w-full">
                          {program.program_name}
                        </p>
                        {program.registration_fee && (
                          <p className="mt-2 text-gondar font-medium text-center w-full">
                            Registration Fee: {typeof program.registration_fee === 'string' ?
                              parseFloat(program.registration_fee).toFixed(2) :
                              program.registration_fee.toFixed(2)} ETB
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  );
                })
              ) : (
                // Fallback if no programs found
                <div className="col-span-4 text-center py-12">
                  <p className="text-gray-500">No programs available at the moment.</p>
                </div>
              )}
            </div>
          )}

        </div>
      </section>

      {/* Application Process */}
      <section className="py-20 bg-[#EFEFEF] relative overflow-hidden">
        {/* No background pattern - clean #EFEFEF background */}

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gondar mb-4">Application Process</h2>
            <div className="h-1 w-20 bg-gondar-accent mx-auto mb-6 rounded-full"></div>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">
              Follow these simple steps to complete your application to {isSettingsLoading ? (
                <span className="inline-block h-5 w-32 bg-gray-200 animate-pulse rounded align-middle"></span>
              ) : (
                settings.organizationName
              )}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 relative">
            {/* Connection line for desktop */}
            <div className="hidden lg:block absolute top-1/4 left-0 right-0 h-0.5 bg-gradient-to-r from-gondar-light/30 via-gondar/50 to-gondar-light/30 z-0"></div>

            {[
              {
                number: "01",
                title: "Create an Account",
                description: "Register on our portal with your email and create a secure password.",
                icon: <UserPlus className="h-8 w-8" />
              },
              {
                number: "02",
                title: "Fill Application Form",
                description: "Complete the multi-step application form with your personal and academic information.",
                icon: <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              },
              {
                number: "03",
                title: "Upload Documents",
                description: "Submit all required documents including transcripts and identification.",
                icon: <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              },
              {
                number: "04",
                title: "Track Application",
                description: "Monitor your application status and receive updates through your dashboard.",
                icon: <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              }
            ].map((step, index) => (
              <div key={index} className="relative z-10">
                <div className="bg-white rounded-xl shadow-lg p-6 h-full border border-gray-100 hover:border-gondar-light/50 transition-all hover:shadow-xl group hover:translate-y-[-5px] duration-300">
                  {/* Top accent bar with gradient */}
                  <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-gondar to-gondar-accent rounded-t-xl"></div>

                  {/* Circle with number */}
                  <div className="flex justify-center mb-6">
                    <div className="relative">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-gondar to-gondar-accent flex items-center justify-center shadow-lg transform group-hover:rotate-12 transition-all duration-300">
                        <span className="text-xl font-bold text-white">{step.number}</span>
                      </div>
                      {/* Arrow for desktop */}
                      {index < 3 && (
                        <div className="hidden lg:block absolute -right-10 top-1/2 transform -translate-y-1/2">
                          <svg className="h-6 w-6 text-gondar animate-pulse" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="flex justify-center mb-4 text-gondar bg-gondar-light/10 p-3 rounded-full w-16 h-16 mx-auto group-hover:bg-gondar-light/20 transition-colors">
                    {step.icon}
                  </div>

                  <h3 className="text-xl font-bold mb-3 text-gondar text-center">{step.title}</h3>
                  <p className="text-gray-600 text-center">{step.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16">
            <Link to={isAuthenticated ? "/application/gat" : "/register"}>
              <Button size="lg" className="bg-gondar hover:bg-gondar-dark shadow-md hover:shadow-lg transition-all px-8 py-6 text-lg">
                Start Your Application
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* FAQs Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gondar mb-4">Frequently Asked Questions</h2>
            <div className="h-1 w-20 bg-gondar-accent mx-auto mb-6 rounded-full"></div>
            <p className="text-gray-600 max-w-2xl mx-auto">Find answers to common questions about our application process</p>
          </div>

          <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
            {[
              {
                question: "When is the application deadline?",
                answer: "The application deadlines vary by program. Please refer to the specific program details for accurate deadlines."
              },
              {
                question: "What documents do I need to submit?",
                answer: "Required documents typically include academic transcripts, standardized test scores, identification, and program-specific requirements."
              },
              {
                question: "Can international students apply?",
                answer: isSettingsLoading ?
                  "Yes, we welcome applications from international students for various programs." :
                  `Yes, ${settings.organizationName} welcomes applications from international students for various programs.`
              },
              {
                question: "How can I check my application status?",
                answer: "You can track your application status through your dashboard after logging into the application portal."
              },
              {
                question: "Is there an application fee?",
                answer: "Yes, most programs require an application fee payable through the TeleBirr payment system."
              }
            ].map((faq, index) => {
              // State for accordion open/closed
              const [isOpen, setIsOpen] = useState(index === 0); // First one open by default

              return (
                <div key={index} className={`border-b border-gray-200 ${index === 0 ? 'border-t' : ''}`}>
                  <button
                    onClick={() => setIsOpen(!isOpen)}
                    className="flex justify-between items-center w-full py-5 px-6 text-left focus:outline-none focus:ring-2 focus:ring-gondar focus:ring-opacity-50 transition-all hover:bg-gray-50"
                    aria-expanded={isOpen}
                  >
                    <h3 className="text-lg font-medium text-gondar">{faq.question}</h3>
                    <span className={`ml-6 flex-shrink-0 text-gondar transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}>
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </span>
                  </button>
                  <div
                    className={`transition-all duration-300 overflow-hidden ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}
                  >
                    <div className="py-4 px-6 bg-gray-50">
                      <p className="text-gray-600">{faq.answer}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section with Divi-inspired Hexagonal Pattern */}
      <section className="py-20 relative overflow-hidden">
        {/* Hexagonal Pattern Background */}
        <div className="absolute inset-0 bg-cover bg-center animate-pattern-shift" style={{
          backgroundImage: 'url("/images/hexagon-pattern.svg")',
        }}></div>

        {/* Floating Hexagons for Divi-inspired animation effect */}
        <FloatingHexagons />

        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 shadow-xl border border-white/20 relative overflow-hidden">
            {/* Hexagonal accent shapes */}
            <div className="absolute -top-10 -right-10 w-40 h-40 opacity-10 rotate-12">
              <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
              </svg>
            </div>
            <div className="absolute -bottom-10 -left-10 w-32 h-32 opacity-10 -rotate-12">
              <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
              </svg>
            </div>

            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 text-center drop-shadow-md">Ready to Begin Your Journey?</h2>
            <p className="text-white/90 text-lg mb-8 text-center max-w-2xl mx-auto drop-shadow-sm">
              Take the first step towards your academic future at {isSettingsLoading ? (
                <span className="inline-block h-5 w-32 bg-white/30 animate-pulse rounded align-middle"></span>
              ) : (
                settings.organizationName
              )}. Our application process is simple and straightforward.
            </p>
            <div className="flex flex-col sm:flex-row justify-center items-center gap-6">
              <Link to={isAuthenticated ? "/dashboard" : "/register"}>
                <Button size="lg" className="bg-white text-[#1a73c0] hover:bg-gray-100 font-medium px-8 py-6 text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 relative overflow-hidden group">
                  <span className="relative z-10">{isAuthenticated ? "Go to Dashboard" : "Apply Now"}</span>
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" className="h-full w-full">
                      <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="#1a73c0" />
                    </svg>
                  </div>
                </Button>
              </Link>
              <Link to="/programs">
                <Button size="lg" className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-[#1a73c0] font-medium px-8 py-6 text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 relative overflow-hidden group">
                  <span className="relative z-10">Explore Programs</span>
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" className="h-full w-full">
                      <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="white" />
                    </svg>
                  </div>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* BackToTop button is already included in the Layout component */}
    </Layout>
  );
};

export default Index;
