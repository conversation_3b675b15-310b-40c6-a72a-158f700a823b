#!/usr/bin/env python
"""
Test script for SMTP API functionality
"""
import os
import sys
import django
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from settings_manager.smtp_models import SMTPSettings
from settings_manager.smtp_serializers import SMTPSettingsSerializer
from django.test import RequestFactory
from django.contrib.auth.models import User
from settings_manager.smtp_views import smtp_settings_view, test_smtp_connection

def test_smtp_model():
    """Test SMTP model functionality"""
    print("=== Testing SMTP Model ===")
    
    # Test loading settings
    settings = SMTPSettings.load()
    print(f"✓ SMTP settings loaded: {settings}")
    print(f"  Host: {settings.host}")
    print(f"  Port: {settings.port}")
    print(f"  Username: {settings.username}")
    print(f"  From Email: {settings.from_email}")
    print(f"  Use TLS: {settings.use_tls}")
    print(f"  Use SSL: {settings.use_ssl}")
    print(f"  Timeout: {settings.timeout}")
    
    # Test serializer
    serializer = SMTPSettingsSerializer(settings)
    print(f"✓ Serializer data: {serializer.data}")
    
    return settings

def test_smtp_api():
    """Test SMTP API endpoints"""
    print("\n=== Testing SMTP API ===")
    
    # Create a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={'is_staff': True, 'is_superuser': True}
    )
    
    factory = RequestFactory()
    
    # Test GET request
    print("Testing GET /api/settings/smtp/")
    request = factory.get('/api/settings/smtp/')
    request.user = user
    
    try:
        response = smtp_settings_view(request)
        print(f"✓ GET response status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"  Response data: {response.data}")
    except Exception as e:
        print(f"✗ GET request failed: {e}")
    
    # Test POST request
    print("\nTesting POST /api/settings/smtp/")
    test_data = {
        'host': 'smtp.gmail.com',
        'port': 587,
        'username': '<EMAIL>',
        'password': 'testpassword',
        'from_email': '<EMAIL>',
        'use_tls': True,
        'use_ssl': False,
        'timeout': 60
    }
    
    request = factory.post(
        '/api/settings/smtp/',
        data=json.dumps(test_data),
        content_type='application/json'
    )
    request.user = user
    
    try:
        response = smtp_settings_view(request)
        print(f"✓ POST response status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"  Response data: {response.data}")
    except Exception as e:
        print(f"✗ POST request failed: {e}")

def test_smtp_test_email():
    """Test SMTP test email functionality"""
    print("\n=== Testing SMTP Test Email ===")
    
    # Create a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={'is_staff': True, 'is_superuser': True}
    )
    
    factory = RequestFactory()
    
    # Test email sending
    test_data = {
        'recipient': '<EMAIL>'
    }
    
    request = factory.post(
        '/api/settings/smtp/test/',
        data=json.dumps(test_data),
        content_type='application/json'
    )
    request.user = user
    
    try:
        response = test_smtp_connection(request)
        print(f"✓ Test email response status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"  Response data: {response.data}")
    except Exception as e:
        print(f"✗ Test email failed: {e}")

if __name__ == '__main__':
    print("Starting SMTP API Tests...")
    
    try:
        # Test model
        settings = test_smtp_model()
        
        # Test API
        test_smtp_api()
        
        # Test email functionality
        test_smtp_test_email()
        
        print("\n=== Test Summary ===")
        print("✓ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
