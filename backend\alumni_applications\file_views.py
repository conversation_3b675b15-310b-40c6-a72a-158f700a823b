"""
Secure file serving views for alumni applications.
"""
import os
import mimetypes
import logging
from django.http import HttpR<PERSON>ponse, Http404, HttpResponseForbidden
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.cache import never_cache
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.permissions import AllowAny
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from .models import ApplicationDocument
import hashlib

logger = logging.getLogger(__name__)

class SecureFileServeView(View):
    """Secure file serving with access controls and logging."""
    
    def get(self, request, document_id):
        """Serve file securely with proper headers and access controls."""
        try:
            # Get document
            document = get_object_or_404(ApplicationDocument, id=document_id)
            
            # Check file exists
            if not document.file or not os.path.exists(document.file.path):
                logger.warning(f"File not found for document {document_id}")
                raise Http404("File not found")
            
            # Log file access
            self._log_file_access(request, document)
            
            # Check access permissions
            if not self._check_access_permissions(request, document):
                logger.warning(f"Unauthorized file access attempt for document {document_id} by {request.user}")
                return HttpResponseForbidden("Access denied")
            
            # Serve file securely
            return self._serve_file_securely(document)
            
        except Exception as e:
            logger.error(f"Error serving file for document {document_id}: {str(e)}")
            raise Http404("File not available")
    
    def _check_access_permissions(self, request, document):
        """Check if user has permission to access the file."""
        # For public alumni applications, allow access to the file owner or staff
        if request.user.is_authenticated:
            # Staff can access all files
            if request.user.is_staff:
                return True
            
            # File uploader can access their own files
            if document.uploaded_by == request.user:
                return True
        
        # For public access, check if the application is from the same session/IP
        # This is a simplified check - you might want to implement token-based access
        return True  # Allow public access for now, but log it
    
    def _serve_file_securely(self, document):
        """Serve file with security headers and content validation."""
        file_path = document.file.path
        
        # Detect MIME type
        mime_type, _ = mimetypes.guess_type(file_path)
        if not mime_type:
            mime_type = 'application/octet-stream'
        
        # Read file content
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
        except IOError as e:
            logger.error(f"Error reading file {file_path}: {str(e)}")
            raise Http404("File not available")
        
        # Create response with security headers
        response = HttpResponse(file_content, content_type=mime_type)
        
        # Set security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Cache-Control'] = 'private, no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        
        # Set content disposition for download
        safe_filename = document.original_filename.replace('"', '\\"')
        response['Content-Disposition'] = f'inline; filename="{safe_filename}"'
        response['Content-Length'] = len(file_content)
        
        # Add file integrity header (optional)
        file_hash = hashlib.sha256(file_content).hexdigest()
        response['X-File-Hash'] = file_hash
        
        return response
    
    def _log_file_access(self, request, document):
        """Log file access for audit purposes."""
        client_ip = self._get_client_ip(request)
        user_info = str(request.user) if request.user.is_authenticated else 'Anonymous'
        
        logger.info(f"File access: Document {document.id} ({document.original_filename}) "
                   f"accessed by {user_info} from {client_ip}")
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@api_view(['GET'])
@permission_classes([AllowAny])
@never_cache
def serve_document_file(request, document_id):
    """API endpoint for serving document files securely."""
    try:
        # Get document
        document = get_object_or_404(ApplicationDocument, id=document_id)
        
        # Check file exists
        if not document.file or not os.path.exists(document.file.path):
            logger.warning(f"File not found for document {document_id}")
            return Response({'error': 'File not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Log access attempt
        client_ip = get_client_ip(request)
        user_info = str(request.user) if request.user.is_authenticated else 'Anonymous'
        logger.info(f"API file access: Document {document_id} requested by {user_info} from {client_ip}")
        
        # Return file metadata for frontend to handle
        return Response({
            'id': document.id,
            'filename': document.original_filename,
            'size': document.file_size,
            'mime_type': document.mime_type,
            'download_url': f'/api/alumni-applications/documents/{document_id}/download/',
            'upload_timestamp': document.upload_timestamp
        })
        
    except Exception as e:
        logger.error(f"Error in serve_document_file for document {document_id}: {str(e)}")
        return Response({'error': 'File not available'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([AllowAny])
@never_cache
def download_document_file(request, document_id):
    """Download document file with security checks."""
    try:
        # Use the secure file serve view
        view = SecureFileServeView()
        return view.get(request, document_id)
        
    except Http404:
        return Response({'error': 'File not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Error downloading document {document_id}: {str(e)}")
        return Response({'error': 'Download failed'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def get_client_ip(request):
    """Get client IP address."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@api_view(['POST'])
@permission_classes([AllowAny])
def validate_file_upload(request):
    """Validate file before upload (pre-upload validation)."""
    try:
        from .security import FileSecurityValidator
        
        if 'file' not in request.FILES:
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)
        
        uploaded_file = request.FILES['file']
        
        # Perform security validation
        validator = FileSecurityValidator()
        validation_result = validator.validate_file(uploaded_file)
        
        # Log validation attempt
        client_ip = get_client_ip(request)
        logger.info(f"File validation request from {client_ip}: {uploaded_file.name}")
        
        if validation_result['is_valid']:
            return Response({
                'valid': True,
                'message': 'File passed security validation',
                'file_info': validation_result['file_info'],
                'warnings': validation_result.get('warnings', [])
            })
        else:
            logger.warning(f"File validation failed from {client_ip}: {validation_result['errors']}")
            return Response({
                'valid': False,
                'errors': validation_result['errors'],
                'warnings': validation_result.get('warnings', [])
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error in file validation: {str(e)}")
        return Response({
            'valid': False,
            'errors': ['Validation service temporarily unavailable']
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def file_upload_security_info(request):
    """Provide file upload security information to frontend."""
    return Response({
        'max_file_size': 10 * 1024 * 1024,  # 10MB
        'allowed_extensions': ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],
        'allowed_mime_types': [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ],
        'security_features': [
            'File signature validation',
            'MIME type verification',
            'Malware scanning',
            'Size limits',
            'Rate limiting',
            'Secure file storage'
        ],
        'rate_limits': {
            'uploads_per_minute': getattr(settings, 'MAX_FILE_UPLOADS_PER_MINUTE', 10),
            'uploads_per_hour': getattr(settings, 'MAX_FILE_UPLOADS_PER_HOUR', 50),
            'size_per_minute_mb': getattr(settings, 'MAX_FILE_SIZE_PER_MINUTE', 50 * 1024 * 1024) // (1024 * 1024)
        }
    })
