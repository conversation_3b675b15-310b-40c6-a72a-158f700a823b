import React, { Suspense, lazy, ComponentType } from 'react';
import { Card, CardContent } from '@/components/ui/card';

// Loading placeholder component
const ChartLoading = () => (
  <div className="flex items-center justify-center h-full w-full">
    <div className="flex flex-col items-center space-y-2">
      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-[#1a73c0]"></div>
      <p className="text-sm text-gray-500">Loading chart...</p>
    </div>
  </div>
);

interface LazyChartProps {
  component: ComponentType<any>;
  props: any;
  height?: string;
}

// LazyChart component that loads charts on demand
const Lazy<PERSON>hart: React.FC<LazyChartProps> = ({ component: Component, props, height = 'h-80' }) => {
  return (
    <div className={height}>
      <Suspense fallback={<ChartLoading />}>
        <Component {...props} />
      </Suspense>
    </div>
  );
};

export default LazyChart;
