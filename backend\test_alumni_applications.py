#!/usr/bin/env python
"""
Test script for Alumni Applications system.
This script tests the models, API endpoints, and validation logic.
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

import json
from django.test import TestCase
from django.core.exceptions import ValidationError
from alumni_applications.models import AlumniApplication, AlumniApplicationMini, ApplicationDocument
from setups.service_type.models import ServiceType
from setups.college.models import College
from setups.department.models import Department
from setups.document_type.models import DocumentType


def test_models():
    """Test the model validation and creation."""
    print("🧪 Testing Alumni Application Models...")
    
    try:
        # Get or create test data
        service_type = ServiceType.objects.first()
        college = College.objects.first()
        department = Department.objects.first()
        
        if not service_type:
            print("❌ No ServiceType found. Please create test data first.")
            return False
        
        if not college:
            print("❌ No College found. Please create test data first.")
            return False
        
        if not department:
            print("❌ No Department found. Please create test data first.")
            return False
        
        # Test Form1 creation
        print("  Testing Form1 (Complete) creation...")
        import time
        timestamp = str(int(time.time()))
        form1_data = {
            'first_name': '<PERSON>',
            'father_name': 'Michael',
            'last_name': 'Doe',
            'phone_number': '+251912345678',
            'email': f'john.doe.{timestamp}@example.com',
            'admission_type': 'Regular',
            'degree_type': 'Degree',
            'is_other_college': False,
            'college': college,
            'department': department,
            'student_status': 'Graduated',
            'year_of_graduation_ethiopian': '2015',
            'service_type': service_type,
            'is_uog_destination': True,
            'uog_college': college,
            'uog_department': department,
        }
        
        form1 = AlumniApplication(**form1_data)
        form1.full_clean()  # This will run validation
        form1.save()
        print(f"  ✅ Form1 created successfully: {form1.transaction_id}")

        # Test Form2 creation
        print("  Testing Form2 (Simplified) creation...")
        form2_data = {
            'first_name': 'Jane',
            'father_name': 'Robert',
            'last_name': 'Smith',
            'phone_number': '+251987654321',
            'email': f'jane.smith.{timestamp}@example.com',
            'admission_type': 'Evening',
            'degree_type': 'Master\'s',
            'is_other_college': True,
            'other_college_name': 'External University',
            'other_department_name': 'Computer Science',
            'student_status': 'Active',
            'current_year': '2nd year',
            'service_type': service_type,
        }

        form2 = AlumniApplicationMini(**form2_data)
        form2.full_clean()  # This will run validation
        form2.save()
        print(f"  ✅ Form2 created successfully: {form2.transaction_id}")
        
        # Test validation errors
        print("  Testing validation errors...")
        
        # Test missing required fields
        try:
            invalid_form = AlumniApplication(
                first_name='Test',
                # Missing required fields
            )
            invalid_form.full_clean()
            print("  ❌ Validation should have failed")
            return False
        except ValidationError:
            print("  ✅ Validation correctly caught missing fields")

        # Test email uniqueness
        try:
            duplicate_form = AlumniApplication(**form1_data)
            duplicate_form.full_clean()
            print("  ❌ Email uniqueness validation should have failed")
            return False
        except ValidationError as e:
            if 'email' in str(e):
                print("  ✅ Email uniqueness validation working")
            else:
                print(f"  ❌ Unexpected validation error: {e}")
                return False
        
        print("✅ Model tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False


def test_serializers():
    """Test the serializers and validation."""
    print("\n📝 Testing Serializers...")

    try:
        from alumni_applications.serializers import (
            AlumniApplicationSerializer,
            AlumniApplicationMiniSerializer
        )

        service_type = ServiceType.objects.first()
        college = College.objects.first()
        department = Department.objects.first()

        # Test Form1 serializer
        print("  Testing Form1 serializer...")
        import time
        timestamp = str(int(time.time()))
        form1_data = {
            'first_name': 'Serializer',
            'father_name': 'Test',
            'last_name': 'User',
            'phone_number': '+251911111111',
            'email': f'serializer.test.{timestamp}@example.com',
            'admission_type': 'Regular',
            'degree_type': 'Degree',
            'is_other_college': False,
            'college': college.id,
            'department': department.id,
            'student_status': 'Graduated',
            'year_of_graduation_ethiopian': '2020',
            'service_type': service_type.id,
            'is_uog_destination': True,
            'uog_college': college.id,
            'uog_department': department.id,
        }

        serializer = AlumniApplicationSerializer(data=form1_data)
        if serializer.is_valid():
            instance = serializer.save()
            print(f"  ✅ Form1 serializer validation passed: {instance.transaction_id}")
        else:
            print(f"  ❌ Form1 serializer validation failed: {serializer.errors}")
            return False

        # Test Form2 serializer
        print("  Testing Form2 serializer...")
        form2_data = {
            'first_name': 'Sarah',
            'father_name': 'Michael',
            'last_name': 'Johnson',
            'phone_number': '+251922222222',
            'email': f'serializer.test2.{timestamp}@example.com',
            'admission_type': 'Evening',
            'degree_type': 'Diploma',
            'is_other_college': True,
            'other_college_name': 'Serializer Test College',
            'other_department_name': 'Serializer Test Department',
            'student_status': 'Active',
            'current_year': '3rd year',
            'service_type': service_type.id,
        }

        serializer = AlumniApplicationMiniSerializer(data=form2_data)
        if serializer.is_valid():
            instance = serializer.save()
            print(f"  ✅ Form2 serializer validation passed: {instance.transaction_id}")
        else:
            print(f"  ❌ Form2 serializer validation failed: {serializer.errors}")
            return False

        print("✅ Serializer tests passed!")
        return True

    except Exception as e:
        print(f"❌ Serializer test failed: {e}")
        return False


def test_business_logic():
    """Test specific business logic and edge cases."""
    print("\n🧠 Testing Business Logic...")
    
    try:
        service_type = ServiceType.objects.first()
        college = College.objects.first()
        department = Department.objects.first()
        
        # Test conditional validation for student status
        print("  Testing student status conditional validation...")
        
        # Active student should require current_year
        try:
            form = AlumniApplication(
                first_name='Test',
                father_name='Test',
                last_name='User',
                phone_number='+251933333333',
                email='<EMAIL>',
                admission_type='Regular',
                degree_type='Degree',
                college=college,
                department=department,
                student_status='Active',
                # Missing current_year
                service_type=service_type,
                is_uog_destination=True,
                uog_college=college,
                uog_department=department,
            )
            form.full_clean()
            print("  ❌ Should have failed for missing current_year")
            return False
        except ValidationError as e:
            if 'current_year' in str(e):
                print("  ✅ Active student validation working")
            else:
                print(f"  ❌ Unexpected validation error: {e}")
                return False
        
        # Test destination logic validation
        print("  Testing destination logic validation...")
        
        # UoG destination should require uog_college and uog_department
        try:
            form = AlumniApplication(
                first_name='Test',
                father_name='Test',
                last_name='User',
                phone_number='+251944444444',
                email='<EMAIL>',
                admission_type='Regular',
                degree_type='Degree',
                college=college,
                department=department,
                student_status='Graduated',
                year_of_graduation_ethiopian='2020',
                service_type=service_type,
                is_uog_destination=True,
                # Missing uog_college and uog_department
            )
            form.full_clean()
            print("  ❌ Should have failed for missing UoG destination fields")
            return False
        except ValidationError as e:
            if 'uog_college' in str(e) or 'uog_department' in str(e):
                print("  ✅ UoG destination validation working")
            else:
                print(f"  ❌ Unexpected validation error: {e}")
                return False
        
        # External destination should require external fields
        try:
            form = AlumniApplication(
                first_name='Test',
                father_name='Test',
                last_name='User',
                phone_number='+251955555555',
                email='<EMAIL>',
                admission_type='Regular',
                degree_type='Degree',
                college=college,
                department=department,
                student_status='Graduated',
                year_of_graduation_ethiopian='2020',
                service_type=service_type,
                is_uog_destination=False,
                # Missing external destination fields
            )
            form.full_clean()
            print("  ❌ Should have failed for missing external destination fields")
            return False
        except ValidationError as e:
            if any(field in str(e) for field in ['order_type', 'institution_name', 'country']):
                print("  ✅ External destination validation working")
            else:
                print(f"  ❌ Unexpected validation error: {e}")
                return False
        
        print("✅ Business logic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Business logic test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Alumni Applications System Tests\n")
    
    tests = [
        test_models,
        test_serializers,
        test_business_logic,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Alumni Applications system is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
