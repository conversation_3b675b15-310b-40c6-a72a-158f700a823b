from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from .models import OrganizationSetting, QuickLink, SocialMediaLink
from .serializers import (
    OrganizationSettingSerializer,
    QuickLinkSerializer,
    SocialMediaLinkSerializer
)


class OrganizationSettingViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing organization settings.
    Only one instance of organization settings should exist.
    """
    queryset = OrganizationSetting.objects.all()
    serializer_class = OrganizationSettingSerializer
    permission_classes = [permissions.AllowAny]

    def list(self, request, *args, **kwargs):
        """Return the single organization settings instance or create default."""
        instance = OrganizationSetting.get_settings()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create organization settings if none exist, otherwise update the existing one.
        """
        if OrganizationSetting.objects.exists():
            instance = OrganizationSetting.objects.first()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        else:
            return super().create(request, *args, **kwargs)

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def public(self, request):
        """
        Public endpoint to get organization settings without authentication.
        """
        instance = OrganizationSetting.get_settings()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class QuickLinkViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing quick links.
    """
    queryset = QuickLink.objects.all()
    serializer_class = QuickLinkSerializer
    permission_classes = [permissions.AllowAny]

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def public(self, request):
        """
        Public endpoint to get active quick links without authentication.
        """
        queryset = QuickLink.objects.filter(is_active=True).order_by('order', 'name')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def reorder(self, request):
        """
        Reorder quick links based on provided order data.
        Expected format: [{"id": 1, "order": 0}, {"id": 2, "order": 1}, ...]
        """
        order_data = request.data
        if not isinstance(order_data, list):
            return Response(
                {"error": "Expected a list of objects with id and order fields"},
                status=status.HTTP_400_BAD_REQUEST
            )

        for item in order_data:
            if 'id' not in item or 'order' not in item:
                return Response(
                    {"error": "Each item must have id and order fields"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                link = QuickLink.objects.get(id=item['id'])
                link.order = item['order']
                link.save(update_fields=['order'])
            except QuickLink.DoesNotExist:
                pass  # Skip non-existent links

        return Response({"status": "Links reordered successfully"})


class SocialMediaLinkViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing social media links.
    """
    queryset = SocialMediaLink.objects.all()
    serializer_class = SocialMediaLinkSerializer
    permission_classes = [permissions.AllowAny]

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def public(self, request):
        """
        Public endpoint to get active social media links without authentication.
        """
        queryset = SocialMediaLink.objects.filter(is_active=True).order_by('order', 'platform')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def reorder(self, request):
        """
        Reorder social media links based on provided order data.
        Expected format: [{"id": 1, "order": 0}, {"id": 2, "order": 1}, ...]
        """
        order_data = request.data
        if not isinstance(order_data, list):
            return Response(
                {"error": "Expected a list of objects with id and order fields"},
                status=status.HTTP_400_BAD_REQUEST
            )

        for item in order_data:
            if 'id' not in item or 'order' not in item:
                return Response(
                    {"error": "Each item must have id and order fields"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                link = SocialMediaLink.objects.get(id=item['id'])
                link.order = item['order']
                link.save(update_fields=['order'])
            except SocialMediaLink.DoesNotExist:
                pass  # Skip non-existent links

        return Response({"status": "Links reordered successfully"})
