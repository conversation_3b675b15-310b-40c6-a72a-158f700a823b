import React, { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface SimpleAuthRouteProps {
  children: ReactNode;
}

/**
 * Simple authenticated route component
 * Only checks if user is logged in - no complex role checking
 */
const SimpleAuthRoute: React.FC<SimpleAuthRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Export with the old names for backward compatibility
export const UserRoute = SimpleAuthRoute;
export const RestrictedRoute = SimpleAuthRoute;

export default SimpleAuthRoute;
