import axios from 'axios';
import { API_BASE_URL } from '../config';

export interface Downloadable {
  id: string;
  title: string;
  file: string;
  file_url: string;
  file_size: number | null;
  file_name: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DownloadableCreate {
  title: string;
  file: File;
  is_active?: boolean;
}

export interface DownloadableUpdate {
  title?: string;
  file?: File;
  is_active?: boolean;
}

export interface DownloadableListResponse {
  count: number;
  results: Downloadable[];
}

class DownloadableAPI {
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  private getCacheKey(url: string): string {
    return `downloadables_${url}`;
  }

  private isValidCache(timestamp: number): boolean {
    return Date.now() - timestamp < this.CACHE_DURATION;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private getCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && this.isValidCache(cached.timestamp)) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  async getDownloadables(): Promise<DownloadableListResponse> {
    const cacheKey = this.getCacheKey('list');
    const cached = this.getCache(cacheKey);

    if (cached) {
      return cached;
    }

    const response = await axios.get(`${API_BASE_URL}/downloadables/`);
    this.setCache(cacheKey, response.data);
    return response.data;
  }

  async getDownloadable(id: string): Promise<Downloadable> {
    const response = await axios.get(`${API_BASE_URL}/downloadables/${id}/`, {
      headers: this.getAuthHeaders(),
    });
    return response.data;
  }

  async createDownloadable(data: DownloadableCreate): Promise<Downloadable> {
    const formData = new FormData();
    formData.append('title', data.title);
    formData.append('file', data.file);
    if (data.is_active !== undefined) {
      formData.append('is_active', data.is_active.toString());
    }

    const response = await axios.post(`${API_BASE_URL}/downloadables/`, formData, {
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async updateDownloadable(id: string, data: DownloadableUpdate): Promise<Downloadable> {
    const formData = new FormData();
    
    if (data.title !== undefined) {
      formData.append('title', data.title);
    }
    if (data.file !== undefined) {
      formData.append('file', data.file);
    }
    if (data.is_active !== undefined) {
      formData.append('is_active', data.is_active.toString());
    }

    const response = await axios.patch(`${API_BASE_URL}/downloadables/${id}/`, formData, {
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteDownloadable(id: string): Promise<void> {
    await axios.delete(`${API_BASE_URL}/downloadables/${id}/`, {
      headers: this.getAuthHeaders(),
    });
  }

  async downloadFile(id: string): Promise<void> {
    const response = await axios.get(`${API_BASE_URL}/downloadables/${id}/download/`, {
      headers: this.getAuthHeaders(),
      responseType: 'blob',
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    
    // Get filename from Content-Disposition header or use default
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'download';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }
    
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }

  async viewFile(id: string): Promise<void> {
    const response = await axios.get(`${API_BASE_URL}/downloadables/${id}/view/`, {
      responseType: 'blob',
    });

    // Create blob URL with proper MIME type
    const blob = new Blob([response.data], { type: response.headers['content-type'] });
    const url = window.URL.createObjectURL(blob);

    // Get filename from Content-Disposition header
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'file';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    // Open in new tab/window for viewing
    const newWindow = window.open(url, '_blank');
    if (newWindow) {
      newWindow.document.title = filename;
      // Clean up the URL after a delay to allow the browser to load the content
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
      }, 1000);
    } else {
      // Fallback: if popup blocked, download the file
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    }
  }

  // Helper method to determine if a file can be viewed inline
  isViewableFile(filename: string): boolean {
    if (!filename) return false;

    const extension = filename.toLowerCase().split('.').pop();
    const viewableExtensions = [
      // Images
      'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg',
      // Documents
      'pdf', 'txt', 'html', 'htm', 'xml', 'json', 'csv',
      // Code files
      'js', 'css', 'py', 'java', 'cpp', 'c', 'php', 'rb', 'go', 'rs'
    ];

    return viewableExtensions.includes(extension || '');
  }

  // Clear cache (useful for admin operations)
  clearCache(): void {
    this.cache.clear();
  }

  formatFileSize(bytes: number | null): string {
    if (!bytes) return 'Unknown size';

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';

    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}

export const downloadableAPI = new DownloadableAPI();
export default downloadableAPI;
