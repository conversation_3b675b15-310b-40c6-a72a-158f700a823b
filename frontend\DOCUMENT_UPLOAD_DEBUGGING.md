# 🔍 Document Upload/Replace Debugging - Enhanced

## 🐛 **Issue Reported**
**Problem**: In `/graduate-admin?tab=alumni-applications`, users cannot update or replace documents in the Service Information section.

## 🔧 **Debugging Enhancements Applied**

### **1. File Input Debugging ✅**
```tsx
const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
  console.log('File input changed:', e.target.files);
  if (e.target.files && e.target.files[0]) {
    console.log('Selected file:', e.target.files[0]);
    onFileUpload(e.target.files[0]);
    // Reset the input so the same file can be selected again
    e.target.value = '';
  }
};
```

### **2. Upload Button Click Debugging ✅**
```tsx
onClick={(e) => {
  e.preventDefault();
  e.stopPropagation();
  console.log('Upload/Replace button clicked for:', docType.name);
  console.log('File input ref:', fileInputRef.current);
  console.log('Existing document:', existingDocument);
  fileInputRef.current?.click();
}}
```

### **3. Document Upload Handler Debugging ✅**
```tsx
const handleDocumentUpload = (documentType: string, file: File) => {
  console.log('handleDocumentUpload called:', { documentType, file });
  console.log('File details:', {
    name: file.name,
    size: file.size,
    type: file.type
  });
  
  // Validate file
  if (!isValidFileType(file)) {
    console.log('File type validation failed');
    toast.error('Invalid file type. Allowed: PDF, JPG, PNG, DOC, DOCX');
    return;
  }

  if (!isValidFileSize(file)) {
    console.log('File size validation failed');
    toast.error('File size exceeds 10MB limit');
    return;
  }

  console.log('File validation passed, adding to upload queue');
  // ... rest of function
};
```

### **4. Document State Management Debugging ✅**
```tsx
// Remove existing document of same type and add new one
const updatedDocuments = documentsToUpload.filter(doc => doc.documentType !== documentType);
console.log('Current documents to upload:', documentsToUpload);
console.log('Updated documents after filter:', updatedDocuments);

const newDocument = {
  id: Date.now().toString(),
  file,
  documentType,
  status: 'pending' as const
};
console.log('New document created:', newDocument);

const newDocumentsArray = [...updatedDocuments, newDocument];
console.log('Final documents array:', newDocumentsArray);

setDocumentsToUpload(newDocumentsArray);
```

### **5. State Change Monitoring ✅**
```tsx
// Debug documentsToUpload changes
useEffect(() => {
  console.log('documentsToUpload state changed:', documentsToUpload);
}, [documentsToUpload]);
```

## 🧪 **Testing Instructions**

### **Step 1: Open Browser Developer Tools**
1. **Press F12** or right-click → "Inspect"
2. **Go to Console tab**
3. **Clear console** (Ctrl+L or click clear button)

### **Step 2: Navigate to Alumni Applications**
1. **Go to**: `/graduate-admin?tab=alumni-applications`
2. **Click "Edit"** on any application with uploaded documents
3. **Navigate to "Service Information"** tab

### **Step 3: Test Document Replace**
1. **Click "Replace"** button on any existing document
2. **Check Console** for these logs:
   ```
   Upload/Replace button clicked for: [Document Type Name]
   File input ref: <input element>
   Existing document: { id: "...", original_filename: "...", ... }
   ```

### **Step 4: Select File**
1. **Choose a file** from the file picker
2. **Check Console** for these logs:
   ```
   File input changed: FileList { 0: File, length: 1 }
   Selected file: File { name: "...", size: ..., type: "..." }
   handleDocumentUpload called: { documentType: "...", file: File }
   File details: { name: "...", size: ..., type: "..." }
   ```

### **Step 5: Validate File Processing**
**If file is valid**:
```
File validation passed, adding to upload queue
Current documents to upload: [...]
Updated documents after filter: [...]
New document created: { id: "...", file: File, documentType: "...", status: "pending" }
Final documents array: [...]
documentsToUpload state changed: [...]
```

**If file is invalid**:
```
File type validation failed
// OR
File size validation failed
```

## 🔍 **Potential Issues to Identify**

### **Issue 1: File Input Not Triggering**
**Symptoms**: No "File input changed" log when selecting file
**Possible Causes**:
- File input ref is null
- Event handler not attached
- Browser blocking file picker

**Debug Steps**:
1. Check if `fileInputRef.current` is not null
2. Verify file input element exists in DOM
3. Try clicking directly on hidden file input

### **Issue 2: File Validation Failing**
**Symptoms**: "File type validation failed" or "File size validation failed"
**Possible Causes**:
- Unsupported file type
- File size exceeds 10MB
- MIME type detection issues

**Debug Steps**:
1. Check file.type in console
2. Check file.size in console
3. Verify file is PDF, JPG, PNG, DOC, or DOCX

### **Issue 3: State Not Updating**
**Symptoms**: No "documentsToUpload state changed" log
**Possible Causes**:
- State setter not called
- React state update batching
- Component re-rendering issues

**Debug Steps**:
1. Check if setDocumentsToUpload is called
2. Verify newDocumentsArray is correct
3. Check for React strict mode double rendering

### **Issue 4: Form Submission Interference**
**Symptoms**: Form submits when clicking replace button
**Possible Causes**:
- Missing `type="button"` attribute
- Event prevention not working
- Button inside form triggering submit

**Debug Steps**:
1. Check if "Application updated successfully" appears
2. Verify preventDefault() and stopPropagation() are called
3. Check button type attribute

## 📊 **Expected Console Output Flow**

### **Successful Document Replace**
```
1. Upload/Replace button clicked for: Academic Transcript
2. File input ref: <input type="file" class="hidden" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
3. Existing document: { id: "123", original_filename: "transcript.pdf", ... }
4. File input changed: FileList { 0: File, length: 1 }
5. Selected file: File { name: "new_transcript.pdf", size: 2048576, type: "application/pdf" }
6. handleDocumentUpload called: { documentType: "Academic Transcript", file: File }
7. File details: { name: "new_transcript.pdf", size: 2048576, type: "application/pdf" }
8. File validation passed, adding to upload queue
9. Current documents to upload: []
10. Updated documents after filter: []
11. New document created: { id: "1640995200000", file: File, documentType: "Academic Transcript", status: "pending" }
12. Final documents array: [{ id: "1640995200000", file: File, documentType: "Academic Transcript", status: "pending" }]
13. documentsToUpload state changed: [{ id: "1640995200000", file: File, documentType: "Academic Transcript", status: "pending" }]
```

### **Failed File Validation**
```
1. Upload/Replace button clicked for: Academic Transcript
2. File input ref: <input type="file" class="hidden" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
3. File input changed: FileList { 0: File, length: 1 }
4. Selected file: File { name: "document.txt", size: 1024, type: "text/plain" }
5. handleDocumentUpload called: { documentType: "Academic Transcript", file: File }
6. File details: { name: "document.txt", size: 1024, type: "text/plain" }
7. File type validation failed
```

## ✅ **Success Criteria**

After testing, you should see:
- ✅ **Button click logs** when clicking Replace/Upload
- ✅ **File selection logs** when choosing files
- ✅ **File validation logs** for each selected file
- ✅ **State update logs** when documents are added
- ✅ **"Ready" status** appears for pending uploads
- ✅ **No form submission** when clicking document buttons

## 🚨 **Common Problems & Solutions**

### **Problem 1: No Console Logs**
**Solution**: Ensure browser console is open and not filtered

### **Problem 2: File Input Not Working**
**Solution**: Check if file input element exists and has proper attributes

### **Problem 3: File Validation Always Fails**
**Solution**: Check file types and sizes, verify validation functions

### **Problem 4: State Not Updating**
**Solution**: Check React DevTools for state changes, verify setDocumentsToUpload calls

## 🎯 **Next Steps**

Based on the console output, we can identify the exact point where the document upload/replace process is failing and provide a targeted fix. Please run the test and share the console output! 🔍
