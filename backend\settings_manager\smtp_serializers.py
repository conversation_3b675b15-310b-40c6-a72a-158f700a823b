from rest_framework import serializers
from .smtp_models import SMTPSettings


class SMTPSettingsSerializer(serializers.ModelSerializer):
    """Serializer for SMTP Settings"""

    class Meta:
        model = SMTPSettings
        fields = [
            'provider',
            'host',
            'port',
            'username',
            'password',
            'use_tls',
            'use_ssl',
            'from_email',
            'timeout',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_port(self, value):
        """Validate SMTP port"""
        if value < 1 or value > 65535:
            raise serializers.ValidationError("Port must be between 1 and 65535")
        return value

    def validate_timeout(self, value):
        """Validate SMTP timeout"""
        if value < 1:
            raise serializers.ValidationError("Timeout must be at least 1 second")
        return value

    def validate(self, data):
        """Validate SMTP settings combination"""
        if data.get('use_tls') and data.get('use_ssl'):
            raise serializers.ValidationError("Cannot use both TLS and SSL simultaneously")

        return data


class SMTPTestSerializer(serializers.Serializer):
    """Serializer for SMTP test request"""

    recipient = serializers.EmailField()
