import api from './api';
import axios from 'axios';

// Get the API base URL for public endpoints
const getPublicAPIBaseURL = () => {
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;

  if (hostname === 'localhost') {
    return 'http://localhost:8000/api';
  } else {
    const fallbackPort = '8000';
    return `${protocol}//${hostname}:${fallbackPort}/api`;
  }
};

const PUBLIC_API_BASE_URL = getPublicAPIBaseURL();

// CSRF Token utility function
const getCSRFToken = (): string | null => {
  // Try to get CSRF token from meta tag first
  const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (metaToken) {
    return metaToken;
  }

  // Try to get from cookie
  const cookieValue = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrftoken='))
    ?.split('=')[1];

  if (cookieValue) {
    return cookieValue;
  }

  // Try to get from Django's default cookie name
  const djangoCsrfToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrfmiddlewaretoken='))
    ?.split('=')[1];

  return djangoCsrfToken || null;
};

// Create axios instance for public API with CSRF token support
const createPublicAPIInstance = () => {
  const instance = axios.create({
    baseURL: PUBLIC_API_BASE_URL,
    withCredentials: true, // Important for CSRF cookies
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add request interceptor to include CSRF token only for non-GET requests
  instance.interceptors.request.use(
    (config) => {
      // Only add CSRF token for non-safe methods (POST, PUT, PATCH, DELETE)
      if (config.method && !['get', 'head', 'options'].includes(config.method.toLowerCase())) {
        const csrfToken = getCSRFToken();
        if (csrfToken) {
          config.headers['X-CSRFToken'] = csrfToken;
          config.headers['X-CSRF-Token'] = csrfToken; // Alternative header name
        }
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Add response interceptor to handle CSRF errors
  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.response?.status === 403 && error.response?.data?.error?.includes('CSRF')) {
        // CSRF token might be invalid, try to get a new one
        console.warn('CSRF token error, attempting to refresh...');

        // If the response includes a new CSRF token, use it
        if (error.response.data.csrf_token) {
          // Store the new token in a meta tag for future use
          let metaTag = document.querySelector('meta[name="csrf-token"]');
          if (!metaTag) {
            metaTag = document.createElement('meta');
            metaTag.setAttribute('name', 'csrf-token');
            document.head.appendChild(metaTag);
          }
          metaTag.setAttribute('content', error.response.data.csrf_token);

          // Retry the original request with the new token
          const originalRequest = error.config;
          originalRequest.headers['X-CSRFToken'] = error.response.data.csrf_token;
          originalRequest.headers['X-CSRF-Token'] = error.response.data.csrf_token;

          return instance(originalRequest);
        }
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

const publicAPI = createPublicAPIInstance();

// Types for Alumni Applications
export interface AlumniApplication {
  id: string;
  first_name: string;
  father_name: string;
  last_name: string;
  student_id?: string;
  phone_number: string;
  email: string;
  admission_type: string;
  degree_type: string;
  college: string;
  department: string;
  is_other_college: boolean;
  other_college_name?: string;
  other_department_name?: string;
  student_status: string;
  current_year?: string;
  year_of_leaving_ethiopian?: string;
  year_of_leaving_gregorian?: string;
  year_of_graduation_ethiopian?: string;
  year_of_graduation_gregorian?: string;
  service_type: string;
  is_uog_destination: boolean;
  uog_college?: string;
  uog_department?: string;
  order_type?: string;
  institution_name?: string;
  country?: string;
  institution_address?: string;
  mailing_agent?: string;
  application_status: string;
  payment_status: string;
  transaction_id: string;
  created_at: string;
  updated_at: string;
  full_name: string;
  college_name: string;
  department_name: string;
  required_document_types: DocumentType[];
  required_document_types_list: string[];
  missing_document_types: DocumentType[];
  document_completion_status: DocumentCompletionStatus;
  documents: ApplicationDocument[];
}

export interface AlumniApplicationMini {
  id: string;
  first_name: string;
  father_name: string;
  last_name: string;
  student_id?: string;
  phone_number: string;
  email: string;
  admission_type: string;
  degree_type: string;
  college: string;
  department: string;
  is_other_college: boolean;
  other_college_name?: string;
  other_department_name?: string;
  student_status: string;
  current_year?: string;
  year_of_leaving_ethiopian?: string;
  year_of_leaving_gregorian?: string;
  year_of_graduation_ethiopian?: string;
  year_of_graduation_gregorian?: string;
  service_type: string;
  application_status: string;
  payment_status: string;
  transaction_id: string;
  created_at: string;
  updated_at: string;
  full_name: string;
  college_name: string;
  department_name: string;
  required_document_types: DocumentType[];
  required_document_types_list: string[];
  missing_document_types: DocumentType[];
  document_completion_status: DocumentCompletionStatus;
  documents: ApplicationDocument[];
}

export interface ApplicationDocument {
  id: string;
  document_type_name: string;
  file: string;
  original_filename: string;
  file_size: number;
  formatted_file_size: string;
  mime_type: string;
  upload_timestamp: string;
  uploaded_by?: string;
}

export interface DocumentType {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
}

export interface ServiceType {
  id: string | number;
  name: string;
  fee: string;
  description: string;
  required_document_types: DocumentType[];
  required_document_types_count: number;
}

export interface College {
  id: string | number;
  name: string;
  description: string;
}

export interface Department {
  id: string | number;
  name: string;
  college: string | number;
  college_name: string;
  description: string;
}

export interface DocumentCompletionStatus {
  required_count: number;
  uploaded_count: number;
  missing_count: number;
  missing_types: string[];
  is_complete: boolean;
  completion_percentage: number;
}

export interface ApplicationListParams {
  page?: number;
  page_size?: number;
  search?: string;
  application_status?: string;
  payment_status?: string;
  student_status?: string;
  service_type?: string;
  college?: string;
  department?: string;
  ordering?: string;
}

export interface ApplicationStatusUpdate {
  application_status?: string;
  payment_status?: string;
}

export interface ApplicationStatistics {
  total_requests: number;
  form1_count: number;
  form2_count: number;
  by_status: {
    pending: number;
    processing: number;
    completed: number;
    rejected: number;
  };
  by_payment_status: {
    paid: number;
    unpaid: number;
    pending: number;
  };
  recent_applications: number;
  time_based?: {
    today: number;
    three_days: number;
    one_week: number;
    one_month: number;
  };
  revenue?: {
    paid_revenue: number;
    pending_revenue: number;
    unpaid_revenue: number;
    total_potential_revenue: number;
  };
}

// API Functions
export const alumniApplicationsAPI = {
  // Alumni Applications (Form1)
  getApplications: (params?: ApplicationListParams) => 
    api.get('/applications/form1/', { params }),
  
  getApplication: (id: string) => 
    api.get(`/applications/form1/${id}/`),
  
  createApplication: (data: Partial<AlumniApplication>) =>
    api.post('/applications/form1/', data, {
      validateStatus: (status) => status >= 200 && status < 300 // Only 2xx is success
    }),
  
  updateApplication: (id: string, data: Partial<AlumniApplication>) => 
    api.put(`/applications/form1/${id}/`, data),
  
  updateApplicationStatus: (id: string, data: ApplicationStatusUpdate) => 
    api.patch(`/applications/form1/${id}/update_status/`, data),
  
  deleteApplication: (id: string) => 
    api.delete(`/applications/form1/${id}/`),

  // Alumni Applications Mini (Form2)
  getMiniApplications: (params?: ApplicationListParams) => 
    api.get('/applications/form2/', { params }),
  
  getMiniApplication: (id: string) => 
    api.get(`/applications/form2/${id}/`),
  
  createMiniApplication: (data: Partial<AlumniApplicationMini>) =>
    api.post('/applications/form2/', data, {
      validateStatus: (status) => status >= 200 && status < 300 // Only 2xx is success
    }),
  
  updateMiniApplication: (id: string, data: Partial<AlumniApplicationMini>) => 
    api.put(`/applications/form2/${id}/`, data),
  
  updateMiniApplicationStatus: (id: string, data: ApplicationStatusUpdate) => 
    api.patch(`/applications/form2/${id}/update_status/`, data),
  
  deleteMiniApplication: (id: string) => 
    api.delete(`/applications/form2/${id}/`),

  // Documents
  getDocuments: (params?: any) =>
    api.get('/documents/', { params }),

  uploadDocument: (data: FormData) =>
    api.post('/documents/', data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  // Application-specific document upload
  uploadDocumentToApplication: (applicationId: string, data: FormData) =>
    api.post(`/applications/form1/${applicationId}/upload_document/`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  uploadDocumentToMiniApplication: (applicationId: string, data: FormData) =>
    api.post(`/applications/form2/${applicationId}/upload_document/`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  
  deleteDocument: (id: string) => 
    api.delete(`/documents/${id}/`),

  // Lookups
  getServiceTypes: () => 
    api.get('/lookups/service-types/'),
  
  getServiceTypeRequiredDocuments: (serviceTypeId: string) => 
    api.get(`/lookups/service-types/${serviceTypeId}/required_documents/`),
  
  getColleges: () => 
    api.get('/lookups/colleges/'),
  
  getDepartments: (collegeId?: string) => 
    api.get('/lookups/departments/', { 
      params: collegeId ? { college: collegeId } : {} 
    }),
  
  getDocumentTypes: (serviceTypeId?: string) =>
    api.get('/lookups/document-types/', {
      params: serviceTypeId ? { service_type: serviceTypeId } : {}
    }),

  // Statistics
  getStatistics: () =>
    api.get('/applications/statistics/'),
};

// Public API Functions (no authentication required, but with CSRF protection)
export const publicAlumniApplicationsAPI = {
  // Public Alumni Applications (Form1) - Create only
  createApplication: (data: Partial<AlumniApplication>) =>
    publicAPI.post('/applications/form1/', data, {
      validateStatus: (status) => status >= 200 && status < 300
    }),

  // Public Alumni Applications Mini (Form2) - Create only
  createMiniApplication: (data: Partial<AlumniApplicationMini>) =>
    publicAPI.post('/applications/form2/', data, {
      validateStatus: (status) => status >= 200 && status < 300
    }),

  // Public document upload
  uploadDocumentToApplication: (applicationId: string, data: FormData) =>
    publicAPI.post(`/applications/form1/${applicationId}/upload_document/`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  uploadDocumentToMiniApplication: (applicationId: string, data: FormData) =>
    publicAPI.post(`/applications/form2/${applicationId}/upload_document/`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  // Public lookups
  getServiceTypes: () =>
    publicAPI.get('/lookups/service-types/'),

  getServiceTypeRequiredDocuments: (serviceTypeId: string) =>
    publicAPI.get(`/lookups/service-types/${serviceTypeId}/required_documents/`),

  getColleges: () =>
    publicAPI.get('/lookups/colleges/'),

  getDepartments: (collegeId?: string) =>
    publicAPI.get('/lookups/departments/', {
      params: collegeId ? { college: collegeId } : {}
    }),

  getDocumentTypes: (serviceTypeId?: string) =>
    publicAPI.get('/lookups/document-types/', {
      params: serviceTypeId ? { service_type: serviceTypeId } : {}
    }),


};

export default alumniApplicationsAPI;
