# Generated by Django 5.2.1 on 2025-05-31 12:42

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OrganizationSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('system_name', models.Char<PERSON>ield(help_text='Name of the system displayed in the title and headers.', max_length=255, verbose_name='System Name')),
                ('organization', models.CharField(help_text='Full legal name of the organization.', max_length=255, verbose_name='Organization Name')),
                ('copyright', models.<PERSON>r<PERSON><PERSON>(help_text='Example: © 2025 University of Gondar. All rights reserved.', max_length=255, verbose_name='Copyright Text')),
                ('contact_info', models.TextField(help_text='General contact information or description.', verbose_name='Contact Information')),
                ('contact_number', models.Char<PERSON><PERSON>(help_text='Phone number in international format. Example: +251912345678.', max_length=20, validators=[django.core.validators.RegexValidator('^\\+?\\d{9,15}$', message='Enter a valid phone number.')], verbose_name='Contact Number')),
                ('support_email', models.EmailField(help_text='Main email address for support and inquiries.', max_length=254, verbose_name='Support Email')),
                ('address', models.TextField(help_text='Physical address of the organization (street, city, country).', verbose_name='Physical Address')),
                ('po_box', models.CharField(blank=True, help_text='Postal box number if available.', max_length=50, null=True, verbose_name='P.O. Box')),
                ('footer_logo', models.ImageField(blank=True, help_text='Logo displayed in the footer area.', null=True, upload_to='logos/footer/', verbose_name='Footer Logo')),
                ('header_logo', models.ImageField(blank=True, help_text='Logo displayed in the header area.', null=True, upload_to='logos/header/', verbose_name='Header Logo')),
                ('favicon', models.ImageField(blank=True, help_text='Small icon displayed in browser tabs (recommended size: 32x32px).', null=True, upload_to='logos/favicon/', verbose_name='Favicon')),
                ('primary_color', models.CharField(default='#1a73c0', help_text='Primary brand color in hex format (e.g., #1a73c0).', max_length=7, validators=[django.core.validators.RegexValidator('^#([A-Fa-f0-9]{6})$', message='Enter a valid hex color code (e.g., #1a73c0).')], verbose_name='Primary Color')),
                ('secondary_color', models.CharField(default='#f5f5f5', help_text='Secondary brand color in hex format (e.g., #f5f5f5).', max_length=7, validators=[django.core.validators.RegexValidator('^#([A-Fa-f0-9]{6})$', message='Enter a valid hex color code (e.g., #f5f5f5).')], verbose_name='Secondary Color')),
                ('account_inactivity_period', models.IntegerField(choices=[(0, 'Disabled'), (30, '30 days'), (60, '60 days'), (90, '90 days'), (180, '6 months'), (365, '1 year')], default=0, help_text="Automatically deactivate user accounts after this period of inactivity. Set to 'Disabled' to turn off this feature.", verbose_name='Account Inactivity Period')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
            ],
            options={
                'verbose_name': 'Organization Setting',
                'verbose_name_plural': 'Organization Settings',
            },
        ),
        migrations.CreateModel(
            name='QuickLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Display name for the quick link.', max_length=255, verbose_name='Link Name')),
                ('url', models.URLField(help_text='Full URL to link to (must start with http:// or https://).', verbose_name='Link URL')),
                ('description', models.CharField(blank=True, help_text='Brief description of the link (optional).', max_length=255, null=True, verbose_name='Description')),
                ('is_external', models.BooleanField(default=True, help_text='If checked, link will open in a new tab.', verbose_name='External Link')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order of appearance. Lower numbers appear first.', verbose_name='Order')),
                ('is_active', models.BooleanField(default=True, help_text='If unchecked, this link will not be displayed.', verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
            ],
            options={
                'verbose_name': 'Quick Link',
                'verbose_name_plural': 'Quick Links',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SocialMediaLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('facebook', 'Facebook'), ('twitter', 'Twitter'), ('linkedin', 'LinkedIn'), ('youtube', 'YouTube'), ('telegram', 'Telegram'), ('instagram', 'Instagram'), ('tiktok', 'TikTok'), ('other', 'Other')], help_text='Select the social media platform.', max_length=50, verbose_name='Platform')),
                ('url', models.URLField(help_text="Full URL to the organization's profile/page.", verbose_name='Profile URL')),
                ('icon', models.ImageField(blank=True, help_text='Custom icon for the platform. Optional.', null=True, upload_to='social_icons/', verbose_name='Platform Icon')),
                ('display_name', models.CharField(blank=True, help_text="Custom display name (e.g., 'Follow us on Facebook').", max_length=100, null=True, verbose_name='Display Name')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order of appearance. Lower numbers appear first.', verbose_name='Order')),
                ('is_active', models.BooleanField(default=True, help_text='If unchecked, this link will not be displayed.', verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
            ],
            options={
                'verbose_name': 'Social Media Link',
                'verbose_name_plural': 'Social Media Links',
                'ordering': ['order', 'platform'],
                'constraints': [models.UniqueConstraint(fields=('platform',), name='unique_social_platform')],
            },
        ),
    ]
