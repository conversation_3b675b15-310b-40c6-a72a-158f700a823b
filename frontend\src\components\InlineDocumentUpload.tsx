import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  FileText, 
  X, 
  CheckCircle, 
  AlertCircle,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';

interface DocumentUpload {
  id: string;
  file: File;
  documentType: string;
  status: 'pending' | 'uploading' | 'uploaded' | 'error';
}

interface InlineDocumentUploadProps {
  requiredDocumentTypes: Array<{
    id: string;
    name: string;
    description?: string;
  }>;
  onDocumentsChange: (documents: DocumentUpload[]) => void;
  disabled?: boolean;
}

const InlineDocumentUpload: React.FC<InlineDocumentUploadProps> = ({
  requiredDocumentTypes,
  onDocumentsChange,
  disabled = false
}) => {
  const [documents, setDocuments] = useState<DocumentUpload[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleFileSelect = (file: File) => {
    if (!isValidFileType(file)) {
      toast.error('Invalid file type. Allowed: PDF, JPG, PNG, DOC, DOCX');
      return;
    }

    if (!isValidFileSize(file)) {
      toast.error('File size exceeds 10MB limit');
      return;
    }

    const newDocument: DocumentUpload = {
      id: Date.now().toString(),
      file,
      documentType: '',
      status: 'pending'
    };

    const updatedDocuments = [...documents, newDocument];
    setDocuments(updatedDocuments);
    onDocumentsChange(updatedDocuments);
  };

  const handleDocumentTypeChange = (documentId: string, documentType: string) => {
    const updatedDocuments = documents.map(doc =>
      doc.id === documentId ? { ...doc, documentType } : doc
    );
    setDocuments(updatedDocuments);
    onDocumentsChange(updatedDocuments);
  };

  const handleRemoveDocument = (documentId: string) => {
    const updatedDocuments = documents.filter(doc => doc.id !== documentId);
    setDocuments(updatedDocuments);
    onDocumentsChange(updatedDocuments);
  };

  const isValidFileType = (file: File) => {
    const allowedTypes = [
      'application/pdf', 
      'image/jpeg', 
      'image/jpg', 
      'image/png', 
      'application/msword', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    return allowedTypes.includes(file.type);
  };

  const isValidFileSize = (file: File) => {
    return file.size <= 10 * 1024 * 1024; // 10MB
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getAvailableDocumentTypes = () => {
    const usedTypes = documents.map(doc => doc.documentType).filter(Boolean);
    return requiredDocumentTypes.filter(type => !usedTypes.includes(type.name));
  };

  const getDocumentStatus = (docTypeName: string) => {
    const uploadedDoc = documents.find(doc => doc.documentType === docTypeName);
    return uploadedDoc ? 'uploaded' : 'required';
  };

  return (
    <div className="space-y-4">
      {/* Document Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
          disabled 
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
            : dragActive 
              ? 'border-primary bg-primary/5' 
              : 'border-gray-300 hover:border-gray-400 cursor-pointer'
        }`}
        onDragEnter={disabled ? undefined : handleDrag}
        onDragLeave={disabled ? undefined : handleDrag}
        onDragOver={disabled ? undefined : handleDrag}
        onDrop={disabled ? undefined : handleDrop}
        onClick={disabled ? undefined : () => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
          onChange={handleFileInputChange}
          disabled={disabled}
        />
        
        <div className="space-y-2">
          <Upload className={`h-8 w-8 mx-auto ${disabled ? 'text-gray-300' : 'text-gray-400'}`} />
          <div className={`font-medium ${disabled ? 'text-gray-400' : ''}`}>
            {disabled ? 'Select a service type first' : 'Drop files here or click to browse'}
          </div>
          <div className={`text-sm ${disabled ? 'text-gray-300' : 'text-muted-foreground'}`}>
            Supports: PDF, JPG, PNG, DOC, DOCX (max 10MB)
          </div>
          {!disabled && (
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Document
            </Button>
          )}
        </div>
      </div>

      {/* Uploaded Documents */}
      {documents.length > 0 && (
        <div className="space-y-2">
          <Label className="font-medium">Documents to Upload</Label>
          <div className="space-y-2">
            {documents.map((doc) => (
              <div key={doc.id} className="flex items-center gap-3 p-3 border rounded bg-muted/50">
                <FileText className="h-4 w-4 text-blue-500 flex-shrink-0" />
                
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">{doc.file.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {formatFileSize(doc.file.size)}
                  </div>
                </div>

                <div className="flex-shrink-0 w-48">
                  <Select 
                    value={doc.documentType} 
                    onValueChange={(value) => handleDocumentTypeChange(doc.id, value)}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {requiredDocumentTypes.map((docType) => (
                        <SelectItem 
                          key={docType.id} 
                          value={docType.name}
                          disabled={documents.some(d => d.documentType === docType.name && d.id !== doc.id)}
                        >
                          {docType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  {doc.documentType ? (
                    <Badge variant="success" className="text-xs">Ready</Badge>
                  ) : (
                    <Badge variant="secondary" className="text-xs">Select Type</Badge>
                  )}
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveDocument(doc.id)}
                    className="text-destructive hover:text-destructive h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Document Requirements Status */}
      {requiredDocumentTypes.length > 0 && (
        <div className="space-y-2">
          <Label className="font-medium">Document Requirements</Label>
          <div className="grid grid-cols-1 gap-2">
            {requiredDocumentTypes.map((docType) => {
              const status = getDocumentStatus(docType.name);
              return (
                <div key={docType.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <span className="text-sm font-medium">{docType.name}</span>
                    {docType.description && (
                      <div className="text-xs text-muted-foreground">{docType.description}</div>
                    )}
                  </div>
                  <Badge variant={status === 'uploaded' ? 'success' : 'secondary'}>
                    {status === 'uploaded' ? 'Ready' : 'Required'}
                  </Badge>
                </div>
              );
            })}
          </div>
          
          <div className="text-sm text-muted-foreground">
            {documents.filter(doc => doc.documentType).length} of {requiredDocumentTypes.length} documents ready for upload
          </div>
        </div>
      )}
    </div>
  );
};

export default InlineDocumentUpload;
