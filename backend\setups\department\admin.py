from django.contrib import admin
from django.db import connection
from django.contrib import messages
from setups.department.models import Department
import re

# Custom admin action to reset sequence
def reset_sequence(modeladmin, request, queryset):
    """Reset the auto-increment sequence for the Department table"""
    # This action should be performed outside of a transaction
    # We'll use a flag in the session to indicate that the sequence should be reset
    # after the current transaction is complete
    request.session['reset_department_sequence'] = True
    # No message - the sequence will be reset silently

reset_sequence.short_description = "Reset auto-increment sequence"

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'college', 'description', 'created_at', 'updated_at']
    search_fields = ['name', 'college__name', 'description']
    list_filter = ['college', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    ordering = ['name']
    list_per_page = 20
    list_max_show_all = 100
    list_display_links = ['name']
    autocomplete_fields = ['college']
    readonly_fields = ['created_at', 'updated_at']
    actions = [reset_sequence]
    fieldsets = [
        ('Department Information', {
            'fields': ['name', 'college', 'description']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]

    def save_model(self, request, obj, form, change):
        """Override save_model to handle potential sequence issues"""
        try:
            # For new objects, try to set a safe ID to avoid conflicts
            if not change and not obj.id:  # Only for new objects without an ID
                # Find the highest ID and add a buffer
                max_id = Department.objects.all().order_by('-id').first()
                if max_id:
                    # Add a buffer to avoid conflicts with existing IDs
                    obj.id = max_id.id + 100
                    # No message - the ID assignment will be silent

            # Call the parent save_model method
            super().save_model(request, obj, form, change)

            # Schedule a sequence reset after the save is complete
            request.session['reset_department_sequence'] = True

        except Exception as e:
            error_message = str(e)
            if 'duplicate key value violates unique constraint' in error_message:
                # Extract the conflicting ID
                match = re.search(r'Key \(id\)=\((\d+)\) already exists', error_message)
                if match:
                    conflict_id = int(match.group(1))
                    # Try again with an even higher ID
                    obj.id = conflict_id + 200  # Add a larger buffer
                    super().save_model(request, obj, form, change)
                    # Just a simple message that the issue was fixed
                    messages.success(request, "Department added successfully.")
                    # Schedule a sequence reset
                    request.session['reset_department_sequence'] = True
                else:
                    # If we can't extract the ID, just raise the error
                    raise
            else:
                # Re-raise the exception if it's not a sequence issue
                raise