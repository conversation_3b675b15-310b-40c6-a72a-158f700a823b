import React, { useState } from 'react';
import { Award, Send, Inbox, BarChart3 } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import OfficialSentManagement from './OfficialSentManagement';
import OfficialReceivedManagement from './OfficialReceivedManagement';
import OfficialStatistics from './OfficialStatistics';

const OfficialManagement = () => {
  const [activeTab, setActiveTab] = useState('sent');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Award className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Official Certificate Management</CardTitle>
                <CardDescription className="mt-1">
                  Manage sent and received official certificates with comprehensive tracking
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger 
                value="sent" 
                className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
              >
                <Send className="h-4 w-4" />
                <span>Sent Certificates</span>
              </TabsTrigger>
              <TabsTrigger 
                value="received" 
                className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
              >
                <Inbox className="h-4 w-4" />
                <span>Received Certificates</span>
              </TabsTrigger>
              <TabsTrigger 
                value="statistics" 
                className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
              >
                <BarChart3 className="h-4 w-4" />
                <span>Statistics</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="sent" className="space-y-4">
              <OfficialSentManagement />
            </TabsContent>

            <TabsContent value="received" className="space-y-4">
              <OfficialReceivedManagement />
            </TabsContent>

            <TabsContent value="statistics" className="space-y-4">
              <OfficialStatistics />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default OfficialManagement;
