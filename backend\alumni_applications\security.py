"""
Security utilities for file upload validation and protection.
"""
import os
import hashlib
import tempfile
import logging
from typing import Dict, List, Tuple, Optional
from django.core.exceptions import ValidationError
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile
import subprocess
import re

# Optional import for python-magic
try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    magic = None

logger = logging.getLogger(__name__)

# Log the availability of optional dependencies
if MAGIC_AVAILABLE:
    logger.info("python-magic is available - advanced file type detection enabled")
else:
    logger.warning("python-magic is not available - using basic file type detection")

class FileSecurityValidator:
    """Comprehensive file security validation."""
    
    # File type configurations
    ALLOWED_EXTENSIONS = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
    ALLOWED_MIME_TYPES = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    
    # File signatures (magic numbers) for validation
    FILE_SIGNATURES = {
        'pdf': [b'%PDF'],
        'jpg': [b'\xff\xd8\xff\xe0', b'\xff\xd8\xff\xe1', b'\xff\xd8\xff\xe2', 
                b'\xff\xd8\xff\xe3', b'\xff\xd8\xff\xe8'],
        'jpeg': [b'\xff\xd8\xff\xe0', b'\xff\xd8\xff\xe1', b'\xff\xd8\xff\xe2', 
                 b'\xff\xd8\xff\xe3', b'\xff\xd8\xff\xe8'],
        'png': [b'\x89PNG\r\n\x1a\n'],
        'doc': [b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'],
        'docx': [b'PK\x03\x04']  # ZIP-based format
    }
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MIN_FILE_SIZE = 1  # 1 byte minimum
    
    def __init__(self):
        self.errors = []
    
    def validate_file(self, uploaded_file: UploadedFile) -> Dict[str, any]:
        """
        Comprehensive file validation.
        Returns validation result with security checks.
        """
        self.errors = []
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {},
            'security_checks': {}
        }
        
        try:
            # 1. Basic file validation
            self._validate_file_basics(uploaded_file, result)
            
            # 2. File name security validation
            self._validate_filename_security(uploaded_file.name, result)
            
            # 3. File size validation
            self._validate_file_size(uploaded_file, result)
            
            # 4. MIME type validation
            self._validate_mime_type(uploaded_file, result)
            
            # 5. File extension validation
            self._validate_file_extension(uploaded_file.name, result)
            
            # 6. File signature validation (magic number)
            self._validate_file_signature(uploaded_file, result)
            
            # 7. Content security validation
            self._validate_file_content(uploaded_file, result)
            
            # 8. Generate file hash for integrity
            result['file_info']['hash'] = self._generate_file_hash(uploaded_file)
            
            # Set overall validation result
            result['is_valid'] = len(result['errors']) == 0
            
        except Exception as e:
            logger.error(f"File validation error: {str(e)}")
            result['is_valid'] = False
            result['errors'].append(f"Validation failed: {str(e)}")
        
        return result
    
    def _validate_file_basics(self, uploaded_file: UploadedFile, result: Dict):
        """Basic file validation."""
        if not uploaded_file:
            result['errors'].append("No file provided")
            return
        
        if not uploaded_file.name:
            result['errors'].append("File name is missing")
            return
        
        result['file_info']['original_name'] = uploaded_file.name
        result['file_info']['size'] = uploaded_file.size
        result['file_info']['content_type'] = uploaded_file.content_type
    
    def _validate_filename_security(self, filename: str, result: Dict):
        """Validate filename for security issues."""
        if not filename:
            result['errors'].append("Filename is empty")
            return
        
        # Check for directory traversal attempts
        if '..' in filename or '/' in filename or '\\' in filename:
            result['errors'].append("Filename contains invalid characters (directory traversal attempt)")
        
        # Check for null bytes
        if '\x00' in filename:
            result['errors'].append("Filename contains null bytes")
        
        # Check for excessively long filename
        if len(filename) > 255:
            result['errors'].append("Filename is too long (max 255 characters)")
        
        # Check for dangerous characters
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*']
        if any(char in filename for char in dangerous_chars):
            result['warnings'].append("Filename contains potentially dangerous characters")
        
        # Generate safe filename
        safe_filename = re.sub(r'[^a-zA-Z0-9._-]', '_', filename)
        result['file_info']['safe_filename'] = safe_filename
        
        result['security_checks']['filename_validation'] = 'passed'
    
    def _validate_file_size(self, uploaded_file: UploadedFile, result: Dict):
        """Validate file size."""
        if uploaded_file.size > self.MAX_FILE_SIZE:
            result['errors'].append(f"File size ({uploaded_file.size} bytes) exceeds maximum allowed size ({self.MAX_FILE_SIZE} bytes)")
        
        if uploaded_file.size < self.MIN_FILE_SIZE:
            result['errors'].append("File is empty or corrupted")
        
        result['security_checks']['size_validation'] = 'passed'
    
    def _validate_mime_type(self, uploaded_file: UploadedFile, result: Dict):
        """Validate MIME type."""
        if uploaded_file.content_type not in self.ALLOWED_MIME_TYPES:
            result['errors'].append(f"MIME type '{uploaded_file.content_type}' is not allowed")
        
        result['security_checks']['mime_validation'] = 'passed'
    
    def _validate_file_extension(self, filename: str, result: Dict):
        """Validate file extension."""
        file_ext = os.path.splitext(filename.lower())[1]
        
        if file_ext not in self.ALLOWED_EXTENSIONS:
            result['errors'].append(f"File extension '{file_ext}' is not allowed")
        
        result['file_info']['extension'] = file_ext
        result['security_checks']['extension_validation'] = 'passed'
    
    def _validate_file_signature(self, uploaded_file: UploadedFile, result: Dict):
        """Validate file signature (magic number)."""
        try:
            # Read first 8 bytes for signature check
            uploaded_file.seek(0)
            file_header = uploaded_file.read(8)
            uploaded_file.seek(0)  # Reset file pointer
            
            file_ext = os.path.splitext(uploaded_file.name.lower())[1][1:]  # Remove dot
            
            if file_ext in self.FILE_SIGNATURES:
                valid_signatures = self.FILE_SIGNATURES[file_ext]
                signature_match = any(file_header.startswith(sig) for sig in valid_signatures)
                
                if not signature_match:
                    result['errors'].append(f"File signature does not match extension '{file_ext}' (possible file type spoofing)")
                else:
                    result['security_checks']['signature_validation'] = 'passed'
            else:
                result['warnings'].append(f"No signature validation available for extension '{file_ext}'")
        
        except Exception as e:
            logger.error(f"Signature validation error: {str(e)}")
            result['warnings'].append("Could not validate file signature")
    
    def _validate_file_content(self, uploaded_file: UploadedFile, result: Dict):
        """Validate file content for security issues."""
        try:
            # Read file content for analysis
            uploaded_file.seek(0)
            file_content = uploaded_file.read(1024)  # Read first 1KB
            uploaded_file.seek(0)  # Reset file pointer

            # Detect actual MIME type using magic (if available)
            if MAGIC_AVAILABLE and magic:
                try:
                    detected_mime = magic.from_buffer(file_content, mime=True)
                    result['file_info']['detected_mime'] = detected_mime

                    # Check if detected MIME matches declared MIME
                    if detected_mime != uploaded_file.content_type:
                        result['warnings'].append(f"Detected MIME type ({detected_mime}) differs from declared type ({uploaded_file.content_type})")
                except Exception:
                    result['warnings'].append("Could not detect MIME type using magic")
            else:
                result['warnings'].append("Advanced MIME detection not available (python-magic not installed)")

            # Check for embedded executables or scripts
            suspicious_patterns = [
                b'<script',
                b'javascript:',
                b'vbscript:',
                b'onload=',
                b'onerror=',
                b'<?php',
                b'<%',
                b'MZ',  # PE executable header
                b'\x7fELF'  # ELF executable header
            ]

            for pattern in suspicious_patterns:
                if pattern in file_content.lower():
                    result['errors'].append("File contains suspicious content (possible malware or script injection)")
                    break

            result['security_checks']['content_validation'] = 'passed'

        except Exception as e:
            logger.error(f"Content validation error: {str(e)}")
            result['warnings'].append("Could not validate file content")
    
    def _generate_file_hash(self, uploaded_file: UploadedFile) -> str:
        """Generate SHA-256 hash of the file."""
        try:
            uploaded_file.seek(0)
            hash_sha256 = hashlib.sha256()
            
            for chunk in iter(lambda: uploaded_file.read(4096), b""):
                hash_sha256.update(chunk)
            
            uploaded_file.seek(0)  # Reset file pointer
            return hash_sha256.hexdigest()
        
        except Exception as e:
            logger.error(f"Hash generation error: {str(e)}")
            return ""

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage."""
    # Remove path components
    filename = os.path.basename(filename)
    
    # Replace dangerous characters
    filename = re.sub(r'[^a-zA-Z0-9._-]', '_', filename)
    
    # Limit length
    if len(filename) > 100:
        name, ext = os.path.splitext(filename)
        filename = name[:95] + ext
    
    return filename

def generate_secure_filename(original_filename: str) -> str:
    """Generate a secure, unique filename."""
    import uuid
    from django.utils import timezone
    
    # Get file extension
    _, ext = os.path.splitext(original_filename)
    
    # Generate unique filename
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    unique_id = str(uuid.uuid4())[:8]
    
    return f"{timestamp}_{unique_id}{ext.lower()}"
