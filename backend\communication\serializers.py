from rest_framework import serializers
from .models import Announcement, EmailNotification, SMSNotification
from django.contrib.auth.models import User


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model (used in nested serializers)."""
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'full_name']

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username


class AnnouncementSerializer(serializers.ModelSerializer):
    """Serializer for Announcement model."""
    author_details = UserSerializer(source='author', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)

    class Meta:
        model = Announcement
        fields = [
            'id', 'title', 'content', 'author', 'author_details',
            'priority', 'target_audience', 'is_active', 'start_date',
            'end_date', 'created_at', 'updated_at', 'is_expired'
        ]
        read_only_fields = ['author', 'created_at', 'updated_at']

    def validate_end_date(self, value):
        """Validate end_date field."""
        # For now, skip complex date validation to avoid timezone issues
        # The model's clean method or database constraints can handle this
        return value

    def validate(self, data):
        """Validate the entire announcement data."""
        # Ensure required fields are present
        if not data.get('title'):
            raise serializers.ValidationError({'title': 'Title is required.'})
        if not data.get('content'):
            raise serializers.ValidationError({'content': 'Content is required.'})

        # Validate priority choices
        valid_priorities = ['low', 'medium', 'high']
        if data.get('priority') and data['priority'] not in valid_priorities:
            raise serializers.ValidationError({
                'priority': f'Priority must be one of: {", ".join(valid_priorities)}'
            })

        # Validate target_audience choices
        valid_audiences = ['all', 'students', 'staff', 'applicants']
        if data.get('target_audience') and data['target_audience'] not in valid_audiences:
            raise serializers.ValidationError({
                'target_audience': f'Target audience must be one of: {", ".join(valid_audiences)}'
            })

        # Skip complex date validation for now to avoid timezone issues
        # The frontend and model validation can handle basic date logic

        return data


class EmailNotificationSerializer(serializers.ModelSerializer):
    """Serializer for EmailNotification model."""
    sender_details = UserSerializer(source='sender', read_only=True)

    class Meta:
        model = EmailNotification
        fields = [
            'id', 'subject', 'content', 'sender', 'sender_details',
            'recipients', 'status', 'scheduled_time', 'sent_time',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'sent_time']
        extra_kwargs = {
            'sender': {'required': False}  # Sender is set automatically in perform_create
        }

    def validate_scheduled_time(self, value):
        """Validate scheduled time format and logic."""
        if value and isinstance(value, str):
            # Handle datetime-local format from frontend
            try:
                from django.utils.dateparse import parse_datetime
                from django.utils import timezone

                # Try to parse the datetime string
                parsed_datetime = parse_datetime(value)
                if not parsed_datetime:
                    # Try alternative format
                    import datetime
                    parsed_datetime = datetime.datetime.fromisoformat(value.replace('T', ' '))

                # Make it timezone aware if it isn't
                if parsed_datetime and timezone.is_naive(parsed_datetime):
                    parsed_datetime = timezone.make_aware(parsed_datetime)

                return parsed_datetime
            except (ValueError, TypeError) as e:
                raise serializers.ValidationError(f"Invalid datetime format: {e}")

        return value

    def validate(self, data):
        """Validate the entire email notification data."""
        # If status is scheduled, scheduled_time should be provided
        if data.get('status') == 'scheduled' and not data.get('scheduled_time'):
            raise serializers.ValidationError({
                'scheduled_time': 'Scheduled time is required when status is scheduled.'
            })

        # Validate recipients format
        recipients = data.get('recipients', '')
        if recipients:
            # Basic email validation for recipients
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            emails = [email.strip() for email in recipients.split(',') if email.strip()]

            for email in emails:
                if not re.match(email_pattern, email):
                    raise serializers.ValidationError({
                        'recipients': f'Invalid email format: {email}'
                    })

        return data


class SMSNotificationSerializer(serializers.ModelSerializer):
    """Serializer for SMSNotification model."""
    sender_details = UserSerializer(source='sender', read_only=True)
    message_length = serializers.SerializerMethodField()

    class Meta:
        model = SMSNotification
        fields = [
            'id', 'message', 'sender', 'sender_details', 'recipients',
            'status', 'scheduled_time', 'sent_time', 'created_at',
            'updated_at', 'message_length'
        ]
        read_only_fields = ['created_at', 'updated_at', 'sent_time']
        extra_kwargs = {
            'sender': {'required': False}  # Sender is set automatically in perform_create
        }

    def get_message_length(self, obj):
        """Calculate the length of the SMS message."""
        return len(obj.message)

    def validate_message(self, value):
        """Validate that the message is not too long."""
        if len(value) > 160:
            raise serializers.ValidationError(
                "Standard SMS messages should not exceed 160 characters. "
                "Your message is {0} characters long.".format(len(value))
            )
        return value
