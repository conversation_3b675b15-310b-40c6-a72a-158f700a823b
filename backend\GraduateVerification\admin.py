from django.contrib import admin
from .models import (
    VerificationCollege,
    VerificationDepartment,
    VerificationFieldOfStudy,
    VerificationProgram,
    AdmissionClassification,
    GraduateStudent,
)

# Organization Admin class is commented out since there's no Organization model
'''
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'phone', 'city', 'created_at')
    search_fields = ('name', 'email', 'phone', 'city')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'motto', 'logo')
        }),
        ('Contact Information', {
            'fields': ('website', 'email', 'phone')
        }),
        ('Address', {
            'fields': ('street', 'city', 'county', 'po_box')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
'''

# College Admin
class CollegeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code')
    search_fields = ('name', 'code')
    ordering = ('name',)

# Department Admin
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'college')
    list_filter = ('college',)
    search_fields = ('name', 'code')
    ordering = ('college', 'name')

# Field of Study Admin
class FieldOfStudyAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'duration', 'department', 'get_college')
    list_filter = ('department', 'department__college')
    search_fields = ('name', 'code', 'department__name')
    ordering = ('department', 'name')

    def get_college(self, obj):
        return obj.department.college
    get_college.short_description = 'College'
    get_college.admin_order_field = 'department__college'

# Program Admin
class ProgramAdmin(admin.ModelAdmin):
    list_display = ('name', 'code')
    search_fields = ('name', 'code')
    ordering = ('name',)

# Admission Classification Admin
class AdmissionClassificationAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name',)
    ordering = ('name',)

# Graduate Verification Admin
class GraduateVerificationAdmin(admin.ModelAdmin):
    list_display = ('student_id', 'get_full_name', 'gender', 'year_of_entry', 'year_of_graduation', 'gpa', 'college', 'department', 'field_of_study', 'program', 'is_deleted', 'created_by', 'updated_by', 'deleted_by')
    list_filter = ('gender', 'year_of_entry', 'year_of_graduation', 'college', 'department', 'field_of_study', 'program', 'admission_classification', 'is_deleted', 'created_by', 'updated_by', 'deleted_by')
    search_fields = ('student_id', 'first_name', 'middle_name', 'last_name')
    ordering = ('-year_of_graduation', 'last_name', 'first_name')
    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at', 'deleted_by', 'deleted_at')

    def get_queryset(self, request):
        """Show all records including soft-deleted ones in admin"""
        return self.model.objects.all_with_deleted()

    fieldsets = (
        ('Personal Information', {
            'fields': ('student_id', 'first_name', 'middle_name', 'last_name', 'gender')
        }),
        ('Academic Information', {
            'fields': ('year_of_entry', 'year_of_graduation', 'gpa', 'college', 'department', 'field_of_study', 'program', 'admission_classification')
        }),
        ('Audit Trail', {
            'fields': ('created_by', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_full_name(self, obj):
        return obj.get_full_name()
    get_full_name.short_description = 'Full Name'

    def save_model(self, request, obj, form, change):
        """Set audit trail fields when saving through admin"""
        try:
            if not change:  # Creating new object
                obj.created_by = request.user
                obj.updated_by = request.user
            else:  # Updating existing object
                obj.updated_by = request.user
        except Exception:
            # Ignore if audit trail fields don't exist yet
            pass
        super().save_model(request, obj, form, change)

# Register models
admin.site.register(VerificationCollege, CollegeAdmin)
admin.site.register(VerificationDepartment, DepartmentAdmin)
admin.site.register(VerificationFieldOfStudy, FieldOfStudyAdmin)
admin.site.register(VerificationProgram, ProgramAdmin)
admin.site.register(AdmissionClassification, AdmissionClassificationAdmin)
admin.site.register(GraduateStudent, GraduateVerificationAdmin)
