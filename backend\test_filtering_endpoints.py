#!/usr/bin/env python3
"""
Test script for the new filtering endpoints
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8001/api"

def test_available_years():
    """Test the available years endpoint"""
    print("Testing available years endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/graduate-verifications/available-years/")
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Available years: {data['years']}")
            print(f"Count: {data['count']}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Exception: {e}")
        return False

def test_available_colleges():
    """Test the available colleges endpoint"""
    print("\nTesting available colleges endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/graduate-verifications/available-colleges/")
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Available colleges:")
            for college in data['colleges']:
                print(f"  - ID: {college['id']}, Name: {college['name']}")
            print(f"Count: {data['count']}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Exception: {e}")
        return False

def test_graduate_data():
    """Test getting graduate data for filtering"""
    print("\nTesting graduate data endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/graduate-verifications/")
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Total graduates: {data.get('count', 'N/A')}")
            if 'results' in data and len(data['results']) > 0:
                sample = data['results'][0]
                print(f"Sample graduate: {sample.get('full_name', 'N/A')} - {sample.get('year_of_graduation', 'N/A')}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Exception: {e}")
        return False

if __name__ == "__main__":
    print("Testing Graduate Dashboard Filtering Endpoints")
    print("=" * 50)
    
    # Test all endpoints
    years_ok = test_available_years()
    colleges_ok = test_available_colleges()
    graduates_ok = test_graduate_data()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Available Years: {'✓' if years_ok else '✗'}")
    print(f"Available Colleges: {'✓' if colleges_ok else '✗'}")
    print(f"Graduate Data: {'✓' if graduates_ok else '✗'}")
    
    if all([years_ok, colleges_ok, graduates_ok]):
        print("\n🎉 All tests passed! The filtering endpoints are working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the server logs.")
