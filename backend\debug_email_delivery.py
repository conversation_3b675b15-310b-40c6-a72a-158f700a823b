#!/usr/bin/env python
"""
Comprehensive email delivery debugging script.
This script will help identify why emails are marked as "sent" but not received.
"""

import os
import sys
import django
import smtplib
import socket
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON>art

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from settings_manager.smtp_models import SMTPSettings
from alumni_applications.email_service import AlumniApplicationEmailService
from django.core.mail import send_mail, EmailMultiAlternatives
from django.conf import settings
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def check_smtp_settings():
    """Check current SMTP settings in database."""
    print("🔍 Checking SMTP Settings...")
    print("=" * 50)
    
    try:
        smtp_settings = SMTPSettings.load()
        print(f"Provider: {smtp_settings.provider}")
        print(f"Host: {smtp_settings.host}")
        print(f"Port: {smtp_settings.port}")
        print(f"Username: {smtp_settings.username}")
        print(f"From Email: {smtp_settings.from_email}")
        print(f"Use TLS: {smtp_settings.use_tls}")
        print(f"Use SSL: {smtp_settings.use_ssl}")
        print(f"Timeout: {smtp_settings.timeout}")
        print(f"Password Set: {'Yes' if smtp_settings.password else 'No'}")
        print(f"Password Length: {len(smtp_settings.password) if smtp_settings.password else 0}")
        
        return smtp_settings
        
    except Exception as e:
        print(f"❌ Error loading SMTP settings: {e}")
        return None


def test_smtp_connection(smtp_settings):
    """Test direct SMTP connection."""
    print("\n🔌 Testing SMTP Connection...")
    print("=" * 50)
    
    try:
        # Test basic connectivity
        print(f"Testing connection to {smtp_settings.host}:{smtp_settings.port}")
        
        if smtp_settings.use_ssl:
            server = smtplib.SMTP_SSL(smtp_settings.host, smtp_settings.port, timeout=smtp_settings.timeout)
        else:
            server = smtplib.SMTP(smtp_settings.host, smtp_settings.port, timeout=smtp_settings.timeout)
            if smtp_settings.use_tls:
                server.starttls()
        
        print("✅ Connection established")
        
        # Test authentication
        if smtp_settings.username and smtp_settings.password:
            print(f"Testing authentication for {smtp_settings.username}")
            server.login(smtp_settings.username, smtp_settings.password)
            print("✅ Authentication successful")
        else:
            print("⚠️  No authentication credentials provided")
        
        server.quit()
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Authentication failed: {e}")
        print("💡 Check username/password or enable app passwords for Gmail")
        return False
    except smtplib.SMTPConnectError as e:
        print(f"❌ Connection failed: {e}")
        print("💡 Check host and port settings")
        return False
    except smtplib.SMTPServerDisconnected as e:
        print(f"❌ Server disconnected: {e}")
        return False
    except socket.timeout as e:
        print(f"❌ Connection timeout: {e}")
        print("💡 Check network connectivity and firewall settings")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_django_email_backend():
    """Test Django's email backend configuration."""
    print("\n📧 Testing Django Email Backend...")
    print("=" * 50)
    
    try:
        # Apply SMTP settings to Django
        smtp_settings = SMTPSettings.load()
        smtp_settings.apply_to_settings()
        
        print(f"Django EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
        print(f"Django EMAIL_HOST: {settings.EMAIL_HOST}")
        print(f"Django EMAIL_PORT: {settings.EMAIL_PORT}")
        print(f"Django EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
        print(f"Django EMAIL_USE_TLS: {settings.EMAIL_USE_TLS}")
        print(f"Django EMAIL_USE_SSL: {settings.EMAIL_USE_SSL}")
        print(f"Django DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error configuring Django email backend: {e}")
        return False


def send_test_email_direct(smtp_settings, recipient_email):
    """Send test email using direct SMTP connection."""
    print(f"\n📤 Sending Test Email (Direct SMTP) to {recipient_email}...")
    print("=" * 50)
    
    try:
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = "Test Email - Direct SMTP"
        msg['From'] = smtp_settings.from_email
        msg['To'] = recipient_email
        
        # Create text content
        text_content = f"""
Test Email - Direct SMTP Connection

This email was sent directly using SMTP to test email delivery.

SMTP Configuration:
- Host: {smtp_settings.host}
- Port: {smtp_settings.port}
- From: {smtp_settings.from_email}
- TLS: {smtp_settings.use_tls}
- SSL: {smtp_settings.use_ssl}

If you receive this email, the SMTP configuration is working correctly.
"""
        
        text_part = MIMEText(text_content, 'plain')
        msg.attach(text_part)
        
        # Connect and send
        if smtp_settings.use_ssl:
            server = smtplib.SMTP_SSL(smtp_settings.host, smtp_settings.port, timeout=smtp_settings.timeout)
        else:
            server = smtplib.SMTP(smtp_settings.host, smtp_settings.port, timeout=smtp_settings.timeout)
            if smtp_settings.use_tls:
                server.starttls()
        
        if smtp_settings.username and smtp_settings.password:
            server.login(smtp_settings.username, smtp_settings.password)
        
        # Send email
        server.send_message(msg)
        server.quit()
        
        print("✅ Direct SMTP email sent successfully")
        return True
        
    except Exception as e:
        print(f"❌ Direct SMTP email failed: {e}")
        return False


def send_test_email_django(recipient_email):
    """Send test email using Django's email backend."""
    print(f"\n📤 Sending Test Email (Django Backend) to {recipient_email}...")
    print("=" * 50)
    
    try:
        # Apply SMTP settings
        smtp_settings = SMTPSettings.load()
        smtp_settings.apply_to_settings()
        
        subject = "Test Email - Django Backend"
        message = f"""
Test Email - Django Email Backend

This email was sent using Django's email backend to test email delivery.

Django Configuration:
- Backend: {settings.EMAIL_BACKEND}
- Host: {settings.EMAIL_HOST}
- Port: {settings.EMAIL_PORT}
- From: {settings.DEFAULT_FROM_EMAIL}

If you receive this email, Django's email configuration is working correctly.
"""
        from_email = settings.DEFAULT_FROM_EMAIL
        
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[recipient_email],
            fail_silently=False
        )
        
        print("✅ Django email sent successfully")
        return True
        
    except Exception as e:
        print(f"❌ Django email failed: {e}")
        return False


def send_test_email_service(recipient_email):
    """Send test email using the AlumniApplicationEmailService."""
    print(f"\n📤 Sending Test Email (Email Service) to {recipient_email}...")
    print("=" * 50)
    
    try:
        success = AlumniApplicationEmailService.send_test_email(
            recipient_email=recipient_email,
            test_message="This is a test email from the Alumni Application Email Service."
        )
        
        if success:
            print("✅ Email service test successful")
            return True
        else:
            print("❌ Email service test failed")
            return False
            
    except Exception as e:
        print(f"❌ Email service test error: {e}")
        return False


def check_common_issues():
    """Check for common email delivery issues."""
    print("\n🔍 Checking Common Email Issues...")
    print("=" * 50)
    
    issues_found = []
    
    try:
        smtp_settings = SMTPSettings.load()
        
        # Check if using Gmail without app password
        if smtp_settings.host == 'smtp.gmail.com':
            if not smtp_settings.password or len(smtp_settings.password) < 16:
                issues_found.append("Gmail requires App Passwords (16 characters). Regular passwords won't work.")
        
        # Check if using Office 365 with incorrect settings
        if smtp_settings.host == 'smtp.office365.com':
            if smtp_settings.port != 587:
                issues_found.append("Office 365 typically uses port 587 with TLS.")
        
        # Check for missing from_email
        if not smtp_settings.from_email:
            issues_found.append("From email address is not set.")
        
        # Check for TLS/SSL conflict
        if smtp_settings.use_tls and smtp_settings.use_ssl:
            issues_found.append("Both TLS and SSL are enabled. Use only one.")
        
        # Check for common port issues
        if smtp_settings.use_ssl and smtp_settings.port == 587:
            issues_found.append("SSL typically uses port 465, not 587. Use TLS for port 587.")
        
        if issues_found:
            print("⚠️  Potential Issues Found:")
            for i, issue in enumerate(issues_found, 1):
                print(f"  {i}. {issue}")
        else:
            print("✅ No obvious configuration issues found")
            
    except Exception as e:
        print(f"❌ Error checking issues: {e}")
    
    return issues_found


def main():
    """Main debugging function."""
    print("🐛 Email Delivery Debugging Tool")
    print("=" * 60)
    
    # Get recipient email
    recipient_email = input("Enter recipient email address for testing: ").strip()
    if not recipient_email:
        recipient_email = "<EMAIL>"
    
    print(f"\nTesting email delivery to: {recipient_email}")
    print("=" * 60)
    
    # Step 1: Check SMTP settings
    smtp_settings = check_smtp_settings()
    if not smtp_settings:
        print("\n❌ Cannot proceed without SMTP settings")
        return
    
    # Step 2: Check common issues
    issues = check_common_issues()
    
    # Step 3: Test SMTP connection
    connection_ok = test_smtp_connection(smtp_settings)
    
    # Step 4: Test Django backend
    django_ok = test_django_email_backend()
    
    # Step 5: Send test emails
    if connection_ok:
        direct_ok = send_test_email_direct(smtp_settings, recipient_email)
        django_email_ok = send_test_email_django(recipient_email)
        service_ok = send_test_email_service(recipient_email)
    else:
        direct_ok = django_email_ok = service_ok = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEBUGGING SUMMARY")
    print("=" * 60)
    print(f"SMTP Settings: {'✅ OK' if smtp_settings else '❌ FAIL'}")
    print(f"SMTP Connection: {'✅ OK' if connection_ok else '❌ FAIL'}")
    print(f"Django Backend: {'✅ OK' if django_ok else '❌ FAIL'}")
    print(f"Direct SMTP Email: {'✅ OK' if direct_ok else '❌ FAIL'}")
    print(f"Django Email: {'✅ OK' if django_email_ok else '❌ FAIL'}")
    print(f"Email Service: {'✅ OK' if service_ok else '❌ FAIL'}")
    print(f"Issues Found: {len(issues)}")
    
    if all([smtp_settings, connection_ok, direct_ok, django_email_ok, service_ok]):
        print("\n🎉 All tests passed! Email delivery should be working.")
        print("If applicants still don't receive emails, check:")
        print("  - Spam/junk folders")
        print("  - Email provider blocking")
        print("  - Recipient email address validity")
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")
        
        if issues:
            print("\n💡 Recommended fixes:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")


if __name__ == "__main__":
    main()
