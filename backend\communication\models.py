from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

class Announcement(models.Model):
    """
    Model for system-wide announcements that can be displayed to users.
    """
    PRIORITY_CHOICES = [
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
    ]

    TARGET_CHOICES = [
        ('all', _('All Users')),
        ('students', _('Students')),
        ('staff', _('Staff')),
        ('applicants', _('Applicants')),
    ]

    title = models.CharField(
        max_length=255,
        verbose_name=_("Title"),
        help_text=_("Title of the announcement")
    )
    content = models.TextField(
        verbose_name=_("Content"),
        help_text=_("Content of the announcement")
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='announcements',
        verbose_name=_("Author")
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name=_("Priority")
    )
    target_audience = models.CharField(
        max_length=20,
        choices=TARGET_CHOICES,
        default='all',
        verbose_name=_("Target Audience")
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active"),
        help_text=_("Whether this announcement is currently active")
    )
    start_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_("Start Date"),
        help_text=_("When this announcement should start being displayed")
    )
    end_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("End Date"),
        help_text=_("When this announcement should stop being displayed (optional)")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Announcement")
        verbose_name_plural = _("Announcements")

    def __str__(self):
        return self.title

    def clean(self):
        """Validate the announcement data."""
        super().clean()

        # Validate that end_date is after start_date
        if self.end_date and self.start_date:
            if self.end_date <= self.start_date:
                from django.core.exceptions import ValidationError
                raise ValidationError({
                    'end_date': 'End date must be after start date.'
                })

    @property
    def is_expired(self):
        """Check if the announcement has expired."""
        if self.end_date:
            return timezone.now() > self.end_date
        return False


class EmailNotification(models.Model):
    """
    Model for email notifications sent to users.
    """
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('scheduled', _('Scheduled')),
        ('sent', _('Sent')),
        ('failed', _('Failed')),
    ]

    subject = models.CharField(
        max_length=255,
        verbose_name=_("Subject"),
        help_text=_("Email subject line")
    )
    content = models.TextField(
        verbose_name=_("Content"),
        help_text=_("Email content (HTML supported)")
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_emails',
        verbose_name=_("Sender")
    )
    recipients = models.TextField(
        verbose_name=_("Recipients"),
        help_text=_("Comma-separated list of email addresses or recipient groups")
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_("Status")
    )
    scheduled_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Scheduled Time"),
        help_text=_("When this email should be sent (if scheduled)")
    )
    sent_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Sent Time"),
        help_text=_("When this email was actually sent")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Email Notification")
        verbose_name_plural = _("Email Notifications")

    def __str__(self):
        return self.subject


class SMSNotification(models.Model):
    """
    Model for SMS notifications sent to users.
    """
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('scheduled', _('Scheduled')),
        ('sent', _('Sent')),
        ('failed', _('Failed')),
    ]

    message = models.TextField(
        verbose_name=_("Message"),
        help_text=_("SMS message content (160 characters max for standard SMS)")
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_sms',
        verbose_name=_("Sender")
    )
    recipients = models.TextField(
        verbose_name=_("Recipients"),
        help_text=_("Comma-separated list of phone numbers or recipient groups")
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        verbose_name=_("Status")
    )
    scheduled_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Scheduled Time"),
        help_text=_("When this SMS should be sent (if scheduled)")
    )
    sent_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Sent Time"),
        help_text=_("When this SMS was actually sent")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("SMS Notification")
        verbose_name_plural = _("SMS Notifications")

    def __str__(self):
        return f"SMS: {self.message[:30]}..."
