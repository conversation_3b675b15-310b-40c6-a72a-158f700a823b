import React from 'react';
import {
  Area<PERSON>hart,
  Area,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface GPADistributionByGenderChartProps {
  data: any[];
  currentYear: number;
  colors: {
    gender: string[];
  };
}

const GPADistributionByGenderChart: React.FC<GPADistributionByGenderChartProps> = ({
  data,
  currentYear,
  colors
}) => {
  // Transform data for better visualization
  const transformedData = data.length > 0 ? [
    ...data[0].data.map(item => {
      // Convert range strings to numeric values for better trend visualization
      const rangeValue = (() => {
        switch(item.range) {
          case '2.0-2.5': return 2.25;
          case '2.5-3.0': return 2.75;
          case '3.0-3.5': return 3.25;
          case '3.5-4.0': return 3.75;
          default: return 3.0;
        }
      })();

      return {
        range: item.range,
        rangeValue: rangeValue,
        Male: item.count,
        Female: data[1]?.data.find(f => f.range === item.range)?.count || 0
      };
    })
  ] : [];

  return (
    <Card>
      <CardHeader>
        <CardTitle>GPA Distribution by Gender ({currentYear})</CardTitle>
        <CardDescription>Revenue Trend visualization for gender performance comparison</CardDescription>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={transformedData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="rangeValue"
              type="number"
              domain={[2, 4]}
              ticks={[2.25, 2.75, 3.25, 3.75]}
              tickFormatter={(value) => {
                switch(value) {
                  case 2.25: return '2.0-2.5';
                  case 2.75: return '2.5-3.0';
                  case 3.25: return '3.0-3.5';
                  case 3.75: return '3.5-4.0';
                  default: return value.toString();
                }
              }}
            />
            <YAxis />
            <Tooltip
              wrapperStyle={{ maxWidth: '250px' }}
              formatter={(value: any, name: string) => [`${value} Graduates`, name]}
              labelFormatter={(label) => {
                if (typeof label === 'number') {
                  switch(label) {
                    case 2.25: return 'GPA Range: 2.0-2.5';
                    case 2.75: return 'GPA Range: 2.5-3.0';
                    case 3.25: return 'GPA Range: 3.0-3.5';
                    case 3.75: return 'GPA Range: 3.5-4.0';
                    default: return `GPA: ${label}`;
                  }
                }
                return label;
              }}
            />
            <Legend />
            <Area
              type="monotone"
              dataKey="Male"
              name="Male"
              stroke={colors.gender[0]}
              fill={colors.gender[0]}
              fillOpacity={0.3}
              strokeWidth={3}
              isAnimationActive={true}
              animationBegin={0}
              animationDuration={1500}
              animationEasing="ease-out"
              dot={{ r: 6, strokeWidth: 2, fill: '#fff', stroke: colors.gender[0] }}
              activeDot={{ r: 8, strokeWidth: 0, fill: colors.gender[0] }}
            />
            <Area
              type="monotone"
              dataKey="Female"
              name="Female"
              stroke={colors.gender[1]}
              fill={colors.gender[1]}
              fillOpacity={0.3}
              strokeWidth={3}
              isAnimationActive={true}
              animationBegin={300}
              animationDuration={1500}
              animationEasing="ease-out"
              dot={{ r: 6, strokeWidth: 2, fill: '#fff', stroke: colors.gender[1] }}
              activeDot={{ r: 8, strokeWidth: 0, fill: colors.gender[1] }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default GPADistributionByGenderChart;
