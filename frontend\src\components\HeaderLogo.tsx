import React, { useState, useEffect } from 'react';
import { Layers } from 'lucide-react';

interface HeaderLogoProps {
  logoUrl: string | null;
  organizationName: string;
  className?: string;
}

const HeaderLogo: React.FC<HeaderLogoProps> = ({ logoUrl, organizationName, className = '' }) => {
  const [processedUrl, setProcessedUrl] = useState<string | null>(null);
  const [error, setError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    // Reset states when URL changes
    setError(false);
    setImageLoaded(false);
    setIsProcessing(true);

    if (!logoUrl) {
      setProcessedUrl(null);
      setIsProcessing(false);
      return;
    }

    // Process the URL
    let url = logoUrl;

    // If the URL is relative (doesn't start with http), prepend the backend media URL
    if (url && !url.startsWith('http') && !url.startsWith('data:')) {
      const backendMediaUrl = import.meta.env.VITE_BACKEND_MEDIA_URL || 'http://localhost:8000';

      // Ensure the logo URL starts with a slash
      const logoPath = url.startsWith('/') ? url : `/${url}`;
      url = `${backendMediaUrl}${logoPath}`;
    }

    console.log('Setting processed URL:', url);

    // Pre-load the image to avoid flash
    const img = new Image();
    img.onload = () => {
      setProcessedUrl(url);
      setImageLoaded(true);
      setIsProcessing(false);
      console.log('Image pre-loaded successfully:', url);
    };
    img.onerror = () => {
      console.error('Failed to pre-load image:', url);
      setError(true);
      setIsProcessing(false);
    };
    img.src = url;
  }, [logoUrl]);

  // Handle image load success
  const handleImageLoad = () => {
    console.log('Image loaded successfully in DOM:', processedUrl);
    setImageLoaded(true);
  };

  // Handle image load error
  const handleImageError = () => {
    console.error('Failed to load image in DOM:', processedUrl);
    setError(true);
  };

  // Always render both the icon and image, but control visibility with CSS
  return (
    <div className={`relative h-9 w-9 flex-shrink-0 ${className}`}>
      {/* Default icon - shown while loading or on error */}
      <div
        className={`absolute inset-0 h-9 w-9 rounded-md bg-white/10 border border-white/20 flex items-center justify-center shadow-sm transition-opacity duration-300`}
        style={{
          opacity: isProcessing || !processedUrl || error ? 1 : 0,
          pointerEvents: isProcessing || !processedUrl || error ? 'auto' : 'none'
        }}
        title={organizationName || 'Organization Logo'}
      >
        {/* Enhanced fallback - show organization initials or icon */}
        {organizationName && organizationName.length > 0 ? (
          <span className="text-white font-bold text-sm">
            {organizationName.split(' ').map(word => word.charAt(0)).join('').substring(0, 2).toUpperCase()}
          </span>
        ) : (
          <Layers className="h-5 w-5 text-white" />
        )}
      </div>

      {/* Image - only shown when loaded successfully */}
      {processedUrl && !error && (
        <div
          className={`absolute inset-0 h-9 w-9 flex items-center justify-center transition-opacity duration-300`}
          style={{
            opacity: !isProcessing && imageLoaded ? 1 : 0,
            pointerEvents: !isProcessing && imageLoaded ? 'auto' : 'none'
          }}
        >
          <img
            src={processedUrl}
            alt={organizationName}
            className="h-full w-full max-h-9 max-w-9 object-contain p-0.5"
            style={{ background: 'rgba(255,255,255,0.05)' }}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
      )}
    </div>
  );
};

export default HeaderLogo;
