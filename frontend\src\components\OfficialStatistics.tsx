import React, { useState, useEffect } from 'react';
import { BarChart3, Send, Inbox, Users, Award, TrendingUp, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { toast } from 'sonner';
import { officialAPI } from '@/services/officialAPI';
import type { OfficialStatistics as OfficialStatsType } from '@/services/officialAPI';

const OfficialStatistics = () => {
  const [statistics, setStatistics] = useState<OfficialStatsType | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const response = await officialAPI.getStatistics();
      setStatistics(response.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
      toast.error('Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  };



  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col justify-center items-center space-y-4">
              <div className="bg-blue-100 p-4 rounded-full">
                <RefreshCw className="h-12 w-12 text-[#1a73c0] animate-spin" />
              </div>
              <div className="text-[#1a73c0] font-medium text-lg">Loading Statistics...</div>
              <div className="text-sm text-gray-500 text-center max-w-md">
                Please wait while we gather the latest certificate statistics from the database.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col justify-center items-center space-y-4">
              <div className="bg-red-100 p-4 rounded-full">
                <BarChart3 className="h-12 w-12 text-red-500" />
              </div>
              <div className="text-red-700 font-medium text-lg">Failed to Load Statistics</div>
              <div className="text-sm text-gray-500 text-center max-w-md">
                Unable to retrieve certificate statistics. Please try refreshing the page.
              </div>
              <Button onClick={fetchStatistics} className="bg-[#1a73c0] hover:bg-blue-700">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="border-blue-200 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Sent</CardTitle>
            <Send className="h-4 w-4 text-[#1a73c0]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#1a73c0]">{statistics.sent.total}</div>
            <p className="text-xs text-gray-500 mt-1">Certificates sent to institutions</p>
          </CardContent>
        </Card>

        <Card className="border-green-200 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Received</CardTitle>
            <Inbox className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{statistics.received.total}</div>
            <p className="text-xs text-gray-500 mt-1">Certificates received from institutions</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Certificates</CardTitle>
            <Award className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {statistics.sent.total + statistics.received.total}
            </div>
            <p className="text-xs text-gray-500 mt-1">Combined sent and received</p>
          </CardContent>
        </Card>


      </div>

      {/* Detailed Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sent Certificates Statistics */}
        <Card>
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg">
                <Send className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg text-[#1a73c0]">Sent Certificates</CardTitle>
                <CardDescription>Breakdown by certificate type and gender</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              {/* By Certificate Type */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3">By Certificate Type</h4>
                <div className="space-y-2">
                  {statistics.sent.by_certificate_type.length > 0 ? (
                    statistics.sent.by_certificate_type.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                        <span className="text-sm font-medium text-gray-700">
                          {item.certificate_type__name || 'Unknown Type'}
                        </span>
                        <span className="text-sm font-bold text-[#1a73c0]">{item.count}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500 italic">No data available</p>
                  )}
                </div>
              </div>

              {/* By Gender */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3">By Gender</h4>
                <div className="space-y-2">
                  {statistics.sent.by_gender.length > 0 ? (
                    statistics.sent.by_gender.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-blue-50 rounded-lg border border-blue-100">
                        <span className="text-sm font-medium text-gray-700">
                          {item.gender === 'M' ? 'Male' : item.gender === 'F' ? 'Female' : 'Unknown'}
                        </span>
                        <span className="text-sm font-bold text-[#1a73c0]">{item.count}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500 italic">No data available</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Received Certificates Statistics */}
        <Card>
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-600 rounded-lg">
                <Inbox className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg text-green-600">Received Certificates</CardTitle>
                <CardDescription>Breakdown by certificate type and gender</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              {/* By Certificate Type */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3">By Certificate Type</h4>
                <div className="space-y-2">
                  {statistics.received.by_certificate_type.length > 0 ? (
                    statistics.received.by_certificate_type.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-100">
                        <span className="text-sm font-medium text-gray-700">
                          {item.certificate_type__name || 'Unknown Type'}
                        </span>
                        <span className="text-sm font-bold text-green-600">{item.count}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500 italic">No data available</p>
                  )}
                </div>
              </div>

              {/* By Gender */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3">By Gender</h4>
                <div className="space-y-2">
                  {statistics.received.by_gender.length > 0 ? (
                    statistics.received.by_gender.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-100">
                        <span className="text-sm font-medium text-gray-700">
                          {item.gender === 'M' ? 'Male' : item.gender === 'F' ? 'Female' : 'Unknown'}
                        </span>
                        <span className="text-sm font-bold text-green-600">{item.count}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500 italic">No data available</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>


    </div>
  );
};

export default OfficialStatistics;
