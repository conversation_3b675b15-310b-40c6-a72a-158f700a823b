/**
 * Enhanced Logout Button Component
 * Provides secure logout functionality with proper token cleanup
 */
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { authAPI } from '../services/authAPI';
import { toast } from 'sonner';

interface LogoutButtonProps {
  className?: string;
  children?: React.ReactNode;
  showConfirmation?: boolean;
  redirectTo?: string;
  onLogoutStart?: () => void;
  onLogoutComplete?: () => void;
  onLogoutError?: (error: any) => void;
}

interface LogoutConfirmationProps {
  onConfirm: () => void;
  onCancel: () => void;
  isLoading: boolean;
}

const LogoutConfirmation: React.FC<LogoutConfirmationProps> = ({
  onConfirm,
  onCancel,
  isLoading
}) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Confirm Logout
      </h3>
      <p className="text-sm text-gray-500 mb-6">
        Are you sure you want to log out? You will need to sign in again to access your account.
      </p>
      <div className="flex space-x-3">
        <button
          onClick={onCancel}
          disabled={isLoading}
          className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          Cancel
        </button>
        <button
          onClick={onConfirm}
          disabled={isLoading}
          className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </div>
          ) : (
            'Logout'
          )}
        </button>
      </div>
    </div>
  </div>
);

export const LogoutButton: React.FC<LogoutButtonProps> = ({
  className = '',
  children = 'Logout',
  showConfirmation = false,
  redirectTo = '/login',
  onLogoutStart,
  onLogoutComplete,
  onLogoutError,
}) => {
  const { logout, user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleLogout = async () => {
    setIsLoading(true);
    
    try {
      if (onLogoutStart) onLogoutStart();

      // Get refresh token for blacklisting
      const refreshToken = localStorage.getItem('refresh_token');

      // Call backend logout to blacklist refresh token
      if (refreshToken) {
        try {
          await authAPI.logout(refreshToken);
          console.log('Refresh token blacklisted successfully');
        } catch (error) {
          console.warn('Failed to blacklist refresh token:', error);
          // Continue with logout even if blacklisting fails
        }
      }

      // Clear local authentication state
      await logout();

      // Show success message
      toast.success('Logged out successfully', {
        description: 'You have been securely logged out'
      });

      if (onLogoutComplete) onLogoutComplete();

      // Redirect to login page
      navigate(redirectTo, { replace: true });

    } catch (error) {
      console.error('Logout error:', error);
      
      if (onLogoutError) onLogoutError(error);
      
      toast.error('Logout failed', {
        description: 'There was an error logging you out. Please try again.'
      });
    } finally {
      setIsLoading(false);
      setShowConfirmDialog(false);
    }
  };

  const handleClick = () => {
    if (showConfirmation) {
      setShowConfirmDialog(true);
    } else {
      handleLogout();
    }
  };

  const handleConfirm = () => {
    handleLogout();
  };

  const handleCancel = () => {
    setShowConfirmDialog(false);
  };

  return (
    <>
      <button
        onClick={handleClick}
        disabled={isLoading}
        className={`${className} ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        title={`Logout ${user?.username || 'user'}`}
      >
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            <span>Logging out...</span>
          </div>
        ) : (
          children
        )}
      </button>

      {showConfirmDialog && (
        <LogoutConfirmation
          onConfirm={handleConfirm}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
      )}
    </>
  );
};

/**
 * Logout menu item for dropdown menus
 */
export const LogoutMenuItem: React.FC<{
  onClick?: () => void;
  className?: string;
}> = ({ onClick, className = '' }) => {
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      
      if (refreshToken) {
        await authAPI.logout(refreshToken);
      }
      
      await logout();
      
      toast.success('Logged out successfully');
      navigate('/login', { replace: true });
      
      if (onClick) onClick();
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Logout failed');
    }
  };

  return (
    <button
      onClick={handleLogout}
      className={`w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 ${className}`}
    >
      <div className="flex items-center">
        <svg
          className="mr-3 h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
          />
        </svg>
        Logout
      </div>
    </button>
  );
};

/**
 * Hook for logout functionality
 */
export const useLogout = () => {
  const { logout } = useAuth();
  const navigate = useNavigate();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const performLogout = async (options?: {
    redirectTo?: string;
    showToast?: boolean;
    blacklistToken?: boolean;
  }) => {
    const {
      redirectTo = '/login',
      showToast = true,
      blacklistToken = true
    } = options || {};

    setIsLoggingOut(true);

    try {
      // Blacklist refresh token if requested
      if (blacklistToken) {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          try {
            await authAPI.logout(refreshToken);
          } catch (error) {
            console.warn('Failed to blacklist refresh token:', error);
          }
        }
      }

      // Clear local state
      await logout();

      if (showToast) {
        toast.success('Logged out successfully');
      }

      // Redirect
      navigate(redirectTo, { replace: true });

    } catch (error) {
      console.error('Logout error:', error);
      
      if (showToast) {
        toast.error('Logout failed');
      }
      
      throw error;
    } finally {
      setIsLoggingOut(false);
    }
  };

  return {
    logout: performLogout,
    isLoggingOut
  };
};

export default LogoutButton;
