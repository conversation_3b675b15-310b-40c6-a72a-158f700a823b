# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=*

# Database Configuration
DB_ENGINE=django.db.backends.postgresql
DB_NAME=ApplicationPortal
DB_USER=postgres
DB_PASSWORD=your-db-password
DB_HOST=localhost
DB_PORT=5432

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:8080,http://127.0.0.1:8080
CORS_ALLOW_ALL_ORIGINS=True

# JWT Configuration
JWT_ACCESS_TOKEN_LIFETIME_HOURS=12
JWT_REFRESH_TOKEN_LIFETIME_DAYS=7

# Payment Gateway Configuration
PAYMENT_BASE_URL=https://your-payment-gateway-url
FABRIC_APP_ID=your-fabric-app-id
APP_SECRET=your-app-secret
MERCHANT_APP_ID=your-merchant-app-id
MERCHANT_CODE=your-merchant-code
PRIVATE_KEY=your-private-key

# Media and Static Files
MEDIA_URL=/media/
STATIC_URL=/static/

# Time Zone
TIME_ZONE=Africa/Addis_Ababa

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_USE_SSL=False
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>

# Admin Configuration
ADMIN_SITE_TITLE=Online Application Portal Admin
ADMIN_SITE_HEADER=Admin Panel
ADMIN_SITE_BRAND=Online Application Portal

# Security Configuration
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
SECURE_SSL_REDIRECT=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/django.log

# File Upload Configuration
FILE_UPLOAD_MAX_MEMORY_SIZE=2621440
DATA_UPLOAD_MAX_MEMORY_SIZE=2621440
FILE_UPLOAD_PERMISSIONS=0o644

# API Server Configuration
API_SERVER_HOST=localhost
API_SERVER_PORT=8080
