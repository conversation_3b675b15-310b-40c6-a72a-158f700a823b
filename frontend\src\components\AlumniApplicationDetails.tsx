import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Mail, 
  Phone, 
  School, 
  Building, 
  FileText, 
  Calendar,
  MapPin,
  CreditCard,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  X
} from 'lucide-react';
import { AlumniApplication, AlumniApplicationMini } from '@/services/alumniApplicationsAPI';

interface AlumniApplicationDetailsProps {
  application: AlumniApplication | AlumniApplicationMini;
  isOpen: boolean;
  onClose: () => void;
  onStatusUpdate: (id: string, field: string, value: string) => void;
}

const AlumniApplicationDetails: React.FC<AlumniApplicationDetailsProps> = ({
  application,
  isOpen,
  onClose,
  onStatusUpdate
}) => {
  const [applicationStatus, setApplicationStatus] = useState(application.application_status);
  const [paymentStatus, setPaymentStatus] = useState(application.payment_status);

  const handleStatusUpdate = (field: string, value: string) => {
    // Prevent application status update if payment is not completed
    if (field === 'application_status' && paymentStatus !== 'Completed') {
      return;
    }

    onStatusUpdate(application.id, field, value);
    if (field === 'application_status') {
      setApplicationStatus(value);
    } else if (field === 'payment_status') {
      setPaymentStatus(value);
    }
  };

  const getStatusBadge = (status: string, type: 'application' | 'payment') => {
    const variants: Record<string, string> = {
      'Pending': 'secondary',
      'Under Review': 'default',
      'Approved': 'success',
      'Rejected': 'destructive',
      'Completed': 'success',
      'Unpaid': 'destructive',
      'Paid': 'success',
      'Pending Payment': 'secondary',
      'Refunded': 'outline'
    };
    return variants[status] || 'default';
  };

  const getCompletionIcon = (status: any) => {
    if (status.is_complete) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (status.completion_percentage >= 50) {
      return <Clock className="h-5 w-5 text-yellow-500" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const isForm1 = 'is_uog_destination' in application;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Application Details - {application.transaction_id}
          </DialogTitle>
          <DialogClose />
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Management */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Application Status</label>
                  <Select
                    value={applicationStatus}
                    onValueChange={(value) => handleStatusUpdate('application_status', value)}
                    disabled={paymentStatus !== 'Completed'}
                  >
                    <SelectTrigger className={paymentStatus !== 'Completed' ? 'opacity-60 cursor-not-allowed' : ''}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="On Review">On Review</SelectItem>
                      <SelectItem value="Processing">Processing</SelectItem>
                      <SelectItem value="Complete">Complete</SelectItem>
                    </SelectContent>
                  </Select>
                  {paymentStatus !== 'Completed' && (
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      🔒 Application status can only be edited after payment is completed
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Payment Status</label>
                  <Select 
                    value={paymentStatus} 
                    onValueChange={(value) => handleStatusUpdate('payment_status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Unpaid">Unpaid</SelectItem>
                      <SelectItem value="Paid">Paid</SelectItem>
                      <SelectItem value="Pending Payment">Pending Payment</SelectItem>
                      <SelectItem value="Refunded">Refunded</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="h-5 w-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Full Name:</span>
                    <span>{application.full_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Email:</span>
                    <span>{application.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Phone:</span>
                    <span>{application.phone_number}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Student ID:</span>
                    <span>{application.student_id}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <School className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Student Status:</span>
                    <Badge variant="outline">{application.student_status}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Applied:</span>
                    <span>{new Date(application.created_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Academic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <School className="h-5 w-5" />
                Academic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">College:</span>
                    <span>{application.college_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <School className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Department:</span>
                    <span>{application.department_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Admission Type:</span>
                    <span>{application.admission_type}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Degree Type:</span>
                    <span>{application.degree_type}</span>
                  </div>
                  {application.current_year && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Current Year:</span>
                      <span>{application.current_year}</span>
                    </div>
                  )}
                  {application.year_of_graduation_ethiopian && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Graduation Year:</span>
                      <span>{application.year_of_graduation_ethiopian}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Service Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Service Type:</span>
                    <span>{application.service_type}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Application Status:</span>
                    <Badge variant={getStatusBadge(application.application_status, 'application') as any}>
                      {application.application_status}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Transaction ID:</span>
                    <span className="font-mono text-sm">{application.transaction_id}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Payment Status:</span>
                    <Badge variant={getStatusBadge(application.payment_status, 'payment') as any}>
                      {application.payment_status}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Document Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                {getCompletionIcon(application.document_completion_status)}
                <div>
                  <div className="font-medium">
                    {application.document_completion_status.uploaded_count} of {application.document_completion_status.required_count} documents uploaded
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {application.document_completion_status.completion_percentage.toFixed(0)}% complete
                  </div>
                </div>
              </div>
              
              {application.document_completion_status.missing_count > 0 && (
                <div className="space-y-2">
                  <div className="font-medium text-destructive">Missing Documents:</div>
                  <div className="flex flex-wrap gap-2">
                    {application.document_completion_status.missing_types.map((docType, index) => (
                      <Badge key={index} variant="destructive">
                        {docType}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {application.documents && application.documents.length > 0 && (
                <div className="space-y-2">
                  <div className="font-medium">Uploaded Documents:</div>
                  <div className="space-y-2">
                    {application.documents.map((doc, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{doc.document_type_name}</div>
                            <div className="text-sm text-muted-foreground">
                              {doc.original_filename} ({doc.formatted_file_size})
                            </div>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Destination Information (Form1 only) */}
          {isForm1 && (application as AlumniApplication).is_uog_destination !== undefined && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Destination Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Destination Type:</span>
                  <Badge variant="outline">
                    {(application as AlumniApplication).is_uog_destination ? 'UoG Internal' : 'External Institution'}
                  </Badge>
                </div>
                
                {(application as AlumniApplication).is_uog_destination ? (
                  <div className="space-y-2">
                    <div className="font-medium">UoG Destination:</div>
                    <div className="text-sm">
                      College: {(application as AlumniApplication).uog_college}<br />
                      Department: {(application as AlumniApplication).uog_department}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="font-medium">External Institution:</div>
                    <div className="text-sm">
                      Institution: {(application as AlumniApplication).institution_name}<br />
                      Country: {(application as AlumniApplication).country}<br />
                      Address: {(application as AlumniApplication).institution_address}<br />
                      Mailing Agent: {(application as AlumniApplication).mailing_agent}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AlumniApplicationDetails;
