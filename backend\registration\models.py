from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.exceptions import ValidationError
from setups.application_information.models import ApplicationInformation
from django.utils.translation import gettext_lazy as _
from django.core.validators import MaxValueValidator, MinValueValidator
from django.core.validators import FileExtensionValidator
import uuid
import os
from datetime import datetime
from django.conf import settings
import json
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

# Set this to True to use UUID fields, False to use integer fields
USE_UUID_FIELDS = True

def validate_file_size(value):
    """Validate that the file size is less than 5MB."""
    filesize = value.size
    if filesize > 2 * 1024 * 1024:  # 2MB
        raise ValidationError("The maximum file size that can be uploaded is 5MB")
    return value

def current_year():
    """Return the current year."""
    from datetime import datetime
    return datetime.now().year

# A utility function for generating application numbers
def generate_application_num():
    """
    Generate a unique application number in the format UOG followed by a 6-digit number.
    The number is based on the count of existing ApplicantProgramSelection records plus 1.
    """
    from django.db import connection

    # Get the current maximum application number from the database
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT MAX(CAST(SUBSTRING(application_num, 4) AS INTEGER))
            FROM registration_applicantprogramselection
            WHERE application_num LIKE 'UOG%'
        """)
        result = cursor.fetchone()[0]

    # If no records exist or the query returns None, start with 1
    next_num = (result or 0) + 1

    # Format: UOG followed by a 6-digit number
    return f"UOG{str(next_num).zfill(6)}"

def generate_transaction_id():
    return f"TXN{uuid.uuid4().hex[:12].upper()}"

def generate_unique_filename(instance, filename, document_type):
    """
    Generate a unique filename for uploaded documents.
    Format: {username}_{document_type}_{timestamp}.{extension}
    """
    # Get the file extension
    ext = filename.split('.')[-1]

    # Generate timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Get username (or user ID if username is not available)
    username = instance.user.username if hasattr(instance, 'user') and instance.user else 'unknown'

    # Create a unique filename
    new_filename = f"{username}_{document_type}_{timestamp}.{ext}"

    # Return the full path
    return os.path.join(f'uploads/{document_type}/', new_filename)

# Create specific upload_to functions for each document type
def degree_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'degree')

def sponsorship_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'sponsorship')

def student_copy_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'student_copy')

def recommendation_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'recommendation')

def publication_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'publication')

def conceptnote_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'conceptnote')

def grade_12_certificate_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'grade_12_certificate')

def grade_9_12_transcript_upload_path(instance, filename):
    return generate_unique_filename(instance, filename, 'grade_9_12_transcript')

class ApplicantInformation(models.Model):
    """
    Model to store applicant's personal and educational information.
    Each user can have only one ApplicantInformation record.
    Educational requirements vary based on the program level:
    - BSC/BA: No educational details required
    - MSC/MBA: Undergraduate details required
    - PHD: Both undergraduate and postgraduate details required
    """
    GENDER_CHOICES = [
        ('Male', 'Male'),
        ('Female', 'Female'),
    ]

    PROGRAM_LEVEL_CHOICES = [
        ('BSC/BA', 'Bachelor of Science/Arts'),
        ('MSC/MBA', 'Master of Science/Business Administration'),
        ('PHD', 'Doctor of Philosophy'),
    ]

    # Basic information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    author = models.OneToOneField(User, on_delete=models.CASCADE, related_name='applicant_info')
    grandfather_name = models.CharField(max_length=100)
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES)
    dob = models.DateField(verbose_name="Date of Birth")
    mobile = models.CharField(max_length=15)

    # Program level the applicant is applying for
    program_level = models.CharField(
        max_length=20,
        choices=PROGRAM_LEVEL_CHOICES,
        default='MSC/MBA',
        verbose_name="Program Level"
    )

    # Undergraduate details (required for MSC/MBA and PHD)
    ug_university = models.CharField(
        max_length=255,
        verbose_name="Undergraduate University",
        blank=True,
        null=True,
        help_text="Required for MSC/MBA and PHD applications"
    )
    ug_field_of_study = models.CharField(
        max_length=255,
        verbose_name="Undergraduate Field of Study",
        blank=True,
        null=True,
        help_text="Required for MSC/MBA and PHD applications"
    )
    ug_CGPA = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        validators=[MinValueValidator(2.0), MaxValueValidator(4.0)],
        verbose_name="Undergraduate CGPA",
        blank=True,
        null=True,
        help_text="Required for MSC/MBA and PHD applications",
        db_column='ug_cgpa'
    )

    # Postgraduate details (required only for PHD)
    pg_university = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Postgraduate University",
        help_text="Required for PHD applications"
    )
    pg_field_of_study = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Postgraduate Field of Study",
        help_text="Required for PHD applications"
    )
    pg_CGPA = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(2.0), MaxValueValidator(4.0)],
        verbose_name="Postgraduate CGPA",
        help_text="Required for PHD applications",
        db_column='pg_cgpa'
    )

    # Property methods to access the database column names directly
    @property
    def ug_cgpa(self):
        return self.ug_CGPA

    @property
    def pg_cgpa(self):
        return self.pg_CGPA

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        name = f"{self.author.first_name} {self.author.last_name}"
        if self.ug_university and self.ug_field_of_study:
            cgpa = f", CGPA: {self.ug_CGPA}" if self.ug_CGPA else ""
            return f"{name} - {self.ug_university} - {self.ug_field_of_study}{cgpa}"
        return name

    def clean(self):
        """
        Validate that the required educational fields are provided based on program level.
        """
        if self.program_level in ['MSC/MBA', 'PHD']:
            # Undergraduate details are required for MSC/MBA and PHD
            required_fields = {
                'ug_university': 'Undergraduate university is required for MSC/MBA and PHD applications',
                'ug_field_of_study': 'Undergraduate field of study is required for MSC/MBA and PHD applications',
                'ug_CGPA': 'Undergraduate CGPA is required for MSC/MBA and PHD applications'
            }

            errors = {}
            for field, message in required_fields.items():
                value = getattr(self, field)
                if value is None or (isinstance(value, str) and not value.strip()):
                    errors[field] = message

            if errors:
                raise ValidationError(errors)

        if self.program_level == 'PHD':
            # Postgraduate details are required for PHD
            if not self.pg_university or not self.pg_field_of_study or self.pg_CGPA is None:
                raise ValidationError({
                    'pg_university': 'Postgraduate university is required for PHD applications',
                    'pg_field_of_study': 'Postgraduate field of study is required for PHD applications',
                    'pg_CGPA': 'Postgraduate CGPA is required for PHD applications'
                })

class ApplicantGAT(models.Model):
    """
    Model to store Graduate Admission Test (GAT) information for applicants.
    Each user can have multiple GAT records, but each GAT number must be unique.
    Each GAT record is associated with exactly one program selection.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='gat_records')
    GAT_No = models.CharField(max_length=20, unique=True, verbose_name="GAT Number", db_column='gat_no')
    GAT_Result = models.PositiveIntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="GAT Result",
        db_column='gat_result'
    )
    # We'll add a OneToOneField from ApplicantProgramSelection to this model
    # instead of having a ForeignKey here

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - GAT: {self.GAT_No} - Score: {self.GAT_Result}"

    # Property methods to access the database column names directly
    @property
    def gat_no(self):
        return self.GAT_No

    @property
    def gat_result(self):
        return self.GAT_Result


class ApplicantProgramSelection(models.Model):
    """
    Model to store the program selection information for an applicant.
    Each user can have multiple program selections (multiple applications).
    Each GAT record can be associated with exactly one program selection.
    Each program selection is associated with exactly one GAT record.
    """
    # Status choices
    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]

    # Payment status choices (detailed)
    PAYMENT_STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Initiated', 'Initiated'),
        ('Processing', 'Processing'),
        ('Completed', 'Completed'),
        ('Failed', 'Failed'),
        ('Verified', 'Verified'),
        ('Expired', 'Expired'),
        ('Refunded', 'Refunded'),
        ('Cancelled', 'Cancelled'),
    ]
    SPONSERSHIP_CHOICES = [
        ('Ministry of Education', 'Ministry of Education'),
        ('Other Government Office', 'Other Government Office'),
        ('University of Gondar', 'University of Gondar'),
        ('Private Office ','Private Office' ),
        ('Self', 'Self'),
    ]

    # Basic information

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='program_selections')
    # Use OneToOneField for one-to-one relationship (one GAT to one program selection)
    gat = models.OneToOneField(ApplicantGAT, on_delete=models.CASCADE, related_name='program_selection', null=True, blank=True)
    application_num = models.CharField(max_length=20, unique=True, default=generate_application_num, editable=False)
    transaction_id = models.CharField(
        max_length=20,
        unique=True,
        default=generate_transaction_id,
        editable=False  # Optional: makes it read-only in admin
    )
    sponsorship = models.CharField(max_length=50, choices=SPONSERSHIP_CHOICES, null=False, blank=False)
    #application_info = models.ForeignKey('application_information.ApplicationInformation', on_delete=models.CASCADE)
    application_info = models.ForeignKey(ApplicationInformation, on_delete=models.CASCADE, null=True, blank=True)
    registrar_off_status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Pending')
    reg_approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        related_name='registrar_approvals',
        null=True,
        blank=True
    )
    department_status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Pending')
    dep_approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        related_name='department_approvals',
        null=True,
        blank=True
    )

    # Additional information
    remark = models.TextField(null=True, blank=True)
    payment_status = models.CharField(max_length=50, choices=PAYMENT_STATUS_CHOICES, default='Pending')
    year = models.ForeignKey('year.Year', on_delete=models.CASCADE, related_name='applicant_program_selections')
    term = models.ForeignKey('term.Term', on_delete=models.CASCADE, related_name='applicant_program_selections')
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class ApplicantDocumentation(models.Model):
    """
    Model to store the documentation uploaded by an applicant.
    Each user can have only one ApplicantDocumentation, and
    each document can be owned by only one user.
    """

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='documents')

    # Document uploads
    degree = models.FileField(
        upload_to=degree_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']),
            validate_file_size
        ],
        null=True,
        blank=True
    )
    sponsorship = models.FileField(
        upload_to=sponsorship_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf']),
            validate_file_size
        ],
        null=True,
        blank=True
    )
    student_copy = models.FileField(
        upload_to=student_copy_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']),
            validate_file_size
        ],
        null=True,
        blank=True
    )
    recommendation = models.FileField(
        upload_to=recommendation_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf']),
            validate_file_size
        ],
        null=True,
        blank=True
    )

    publication = models.FileField(
        upload_to=publication_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf']),
            validate_file_size
        ],
        null=True,
        blank=True
    )
    conceptnote = models.FileField(
        upload_to=conceptnote_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf']),
            validate_file_size
        ],
        null=True,
        blank=True
    )
    grade_12_certificate = models.FileField(
        upload_to=grade_12_certificate_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']),
            validate_file_size
        ],
        null=True,
        blank=True
    )
    grade_9_12_transcript = models.FileField(
        upload_to=grade_9_12_transcript_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png']),
            validate_file_size
        ],
        null=True,
        blank=True
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class ApplicantPayment(models.Model):
    """
    Model to store payment information for an applicant.
    Each user can have multiple payment records.
    Each payment is associated with exactly one GAT ID.
    One ApplicantGAT can have one ApplicantPayment.
    """
    # Payment status choices
    PAYMENT_STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Initiated', 'Initiated'),
        ('Processing', 'Processing'),
        ('Completed', 'Completed'),
        ('Failed', 'Failed'),
        ('Verified', 'Verified'),
        ('Expired', 'Expired'),
        ('Refunded', 'Refunded'),
        ('Cancelled', 'Cancelled'),
    ]

    # Payment method choices
    PAYMENT_METHOD_CHOICES = [
        ('TeleBirr', 'TeleBirr'),
        ('CBE Birr', 'Bank Transfer'),
    ]

    # Basic information
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payments')
    payment_status = models.CharField(max_length=50, choices=PAYMENT_STATUS_CHOICES, default='Pending')
    payment_method = models.CharField(max_length=50, choices=PAYMENT_METHOD_CHOICES)

    # One-to-one relationship with ApplicantGAT
    applicant_gat = models.OneToOneField(
        'ApplicantGAT',
        on_delete=models.CASCADE,
        related_name='payment',  # allows reverse access: gat.payment
        null=True,
        blank=True
    )

    applicant_program_selection = models.OneToOneField(
        'ApplicantProgramSelection',
        on_delete=models.CASCADE,
        related_name='payment',  # allows reverse access: selection.payment
        null=True,
        blank=True
    )
    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0, message='Payment amount cannot be negative')]
    )
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    def clean(self):
        """
        Validate that either applicant_gat or applicant_program_selection is provided.
        """
        if not self.applicant_gat and not self.applicant_program_selection:
            raise ValidationError({
                'applicant_gat': 'Either GAT or Program Selection must be provided',
                'applicant_program_selection': 'Either GAT or Program Selection must be provided'
            })