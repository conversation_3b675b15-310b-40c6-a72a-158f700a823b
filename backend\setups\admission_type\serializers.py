from rest_framework import serializers
from .models import AdmissionType

class AdmissionTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = AdmissionType
        fields = ['id', 'name', 'description', 'created_at', 'updated_at']
        ref_name = 'AdmissionType'

    def validate_name(self, value):
        # Check if the name already exists in the database
        instance = getattr(self, 'instance', None)

        # If this is an update (instance exists) and the name hasn't changed, it's valid
        if instance and instance.name == value:
            return value

        # Otherwise, check if the name is already in use
        if AdmissionType.objects.filter(name=value).exists():
            raise serializers.ValidationError(f"The admission type name '{value}' is already in use.")

        return value