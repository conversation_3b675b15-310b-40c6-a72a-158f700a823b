# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api
VITE_API_TIMEOUT=30000

# Development Server Configuration
VITE_DEV_SERVER_HOST=::
VITE_DEV_SERVER_PORT=8080
VITE_HMR_HOST=localhost
VITE_HMR_PORT=8080

# Payment Gateway Configuration
VITE_PAYMENT_BASE_URL=http://your-payment-server:port/API_SERVER
VITE_MERCHANT_APP_ID=your-merchant-app-id

# Legacy API Configuration (for backward compatibility)
VITE_LEGACY_BASE_URL=http://localhost:8084/OnlineApplicationPortal/backend/API_SERVER
VITE_LEGACY_MERCHANT_APP_ID=your-legacy-merchant-app-id

# Application Configuration
VITE_APP_VERSION=1.0.0
VITE_DEFAULT_PAGINATION_LIMIT=10
VITE_DEFAULT_THEME=light

# Feature Flags
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_NOTIFICATIONS=true

# Environment
NODE_ENV=development

# Content Security Policy
VITE_CSP_IMG_SRC=http://localhost:8000
VITE_CSP_CONNECT_SRC=http://localhost:8000 ws://localhost:8080 wss://localhost:8080

# Backend Media URL
VITE_BACKEND_MEDIA_URL=http://localhost:8000

# Dynamic API Configuration
VITE_FALLBACK_API_PORT=8000
VITE_SPECIAL_IP_ADDRESS=**************
