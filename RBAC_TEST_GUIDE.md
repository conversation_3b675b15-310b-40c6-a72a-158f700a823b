# 🔐 RBAC Testing Guide for Alumni Applications System

This guide provides comprehensive instructions for testing Role-Based Access Control (RBAC) functionality, including the newly added alumni applications statistics and revenue calculations.

## 📋 Test Overview

### What We're Testing
- ✅ **Alumni Applications RBAC** - Create, read, update, delete permissions
- ✅ **Statistics Dashboard Access** - Role-based statistics viewing
- ✅ **Revenue Calculations** - Proper fee summation and access control
- ✅ **Time-based Statistics** - Today, 3 days, 1 week, 1 month data
- ✅ **User Management** - Admin-only user operations
- ✅ **File Access Control** - Document viewing permissions
- ✅ **API Authentication** - JWT token validation
- ✅ **Method-based Permissions** - GET, POST, PUT, DELETE access
- ✅ **Cross-role Scenarios** - Role hierarchy validation

### User Roles Tested
1. **Super Admin** - Full system access
2. **Administrator** - Administrative functions
3. **Main Registrar** - Registration management
4. **Registrar Officer** - Limited registration access
5. **Department Head** - Department-specific access
6. **Staff** - General staff access
7. **Anonymous** - Public access only

## 🚀 Quick Start

### Prerequisites
```bash
# 1. Backend server running
cd backend
python manage.py runserver

# 2. Frontend server running (optional)
cd frontend
npm run dev
```

### Run All Tests
```bash
# Comprehensive test suite
python run_rbac_tests.py

# Backend-only tests
cd backend
python test_rbac_comprehensive.py

# Alumni statistics specific tests
cd backend
python test_alumni_statistics_rbac.py
```

## 🔧 Detailed Test Instructions

### 1. Backend RBAC Tests

#### Run Comprehensive Backend Tests
```bash
cd backend
python test_rbac_comprehensive.py
```

**Expected Output:**
```
🚀 Starting Comprehensive RBAC Testing...
🔧 Setting up test users...
  ✅ Created user: test_super_admin (super_admin)
  ✅ Created user: test_admin (admin)
  ✅ Created user: test_staff (staff)

📋 Testing Alumni Applications RBAC...
  ✅ GET /api/applications/form1/ (anonymous): 200
  ✅ POST /api/applications/form1/ (anonymous): 201
  ✅ GET /api/applications/statistics/ (admin): 200
  ❌ GET /api/applications/statistics/ (anonymous): 401

📊 RBAC TEST REPORT
Total Tests: 45
Passed: 43 ✅
Failed: 2 ❌
Success Rate: 95.6%
```

#### Test Alumni Statistics Specifically
```bash
cd backend
python test_alumni_statistics_rbac.py
```

**Expected Output:**
```
🚀 Starting Alumni Statistics RBAC Tests...
🔧 Setting up test data...
  ✅ Created service type: Official Transcript (150.00 ETB)
  ✅ Created service type: Temporary Certificate (100.00 ETB)

📊 Expected Revenue Calculations:
  • Paid Revenue: 300 ETB (2 completed applications)
  • Pending Revenue: 200 ETB (2 pending applications)
  • Total Potential: 600 ETB

🔐 Testing Statistics Endpoint Access...
  ✅ Anonymous access: 401 (expected 401)
  ✅ Admin access: 200
  ✅ Staff access: 200

💰 Revenue Validation:
    Paid: 300.0 ETB (expected 300.0) ✅
    Pending: 200.0 ETB (expected 200.0) ✅
    Total: 600.0 ETB (expected 600.0) ✅
```

### 2. Frontend RBAC Tests

#### Browser Console Testing
```javascript
// Open browser console on http://localhost:8080
// Load the test script
const script = document.createElement('script');
script.src = '/test_rbac_frontend.js';
document.head.appendChild(script);

// Run tests
const tester = new FrontendRBACTester();
tester.runAllTests();
```

#### Manual Frontend Testing

1. **Navigate to Graduate Admin**
   ```
   http://localhost:8080/graduate-admin?tab=alumni-applications
   ```

2. **Test Different User Roles**
   - Login as different users
   - Check statistics tab visibility
   - Verify revenue cards display
   - Test edit/delete permissions

3. **Expected Behavior by Role**
   - **Anonymous**: Redirected to login
   - **Staff**: Can view statistics, cannot edit
   - **Admin**: Full access to all features
   - **Super Admin**: Complete system access

### 3. Integration Tests

#### API Endpoint Testing
```bash
# Test public endpoints (should work without auth)
curl -X GET http://localhost:8000/api/applications/form1/
curl -X POST http://localhost:8000/api/applications/form1/ \
  -H "Content-Type: application/json" \
  -d '{"first_name":"Test","father_name":"User","last_name":"RBAC"}'

# Test protected endpoints (should require auth)
curl -X GET http://localhost:8000/api/applications/statistics/
# Expected: 401 Unauthorized

# Test with authentication
curl -X GET http://localhost:8000/api/applications/statistics/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
# Expected: 200 OK with statistics data
```

#### Revenue Calculation Testing
```bash
# Get statistics with authentication
curl -X GET http://localhost:8000/api/applications/statistics/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  | jq '.revenue'

# Expected response:
{
  "paid_revenue": 300.0,
  "pending_revenue": 200.0,
  "unpaid_revenue": 100.0,
  "total_potential_revenue": 600.0
}
```

## 🎯 Key Test Scenarios

### Scenario 1: Public Access
- **Test**: Anonymous user accessing alumni application forms
- **Expected**: Can view and create applications
- **Verify**: No authentication required for form1/form2 endpoints

### Scenario 2: Statistics Access
- **Test**: Different roles accessing statistics dashboard
- **Expected**: Authenticated users can view, anonymous cannot
- **Verify**: 401 for anonymous, 200 for authenticated users

### Scenario 3: Revenue Calculations
- **Test**: Revenue data accuracy in statistics
- **Expected**: Correct summation of service fees by payment status
- **Verify**: Manual calculation matches API response

### Scenario 4: Admin Functions
- **Test**: User management access by role
- **Expected**: Only admin roles can access user management
- **Verify**: 403 for non-admin roles, 200 for admin roles

### Scenario 5: File Access
- **Test**: Document file access permissions
- **Expected**: Users can only access their own files or staff can access all
- **Verify**: Proper file access control

## 📊 Test Results Interpretation

### Success Indicators
- ✅ **Public endpoints accessible** without authentication
- ✅ **Protected endpoints require** authentication
- ✅ **Role-based access** properly enforced
- ✅ **Revenue calculations** are accurate
- ✅ **Time-based statistics** are present
- ✅ **JWT tokens** properly validated

### Common Issues
- ❌ **CORS errors**: Check CORS configuration
- ❌ **401 Unauthorized**: Verify JWT token format
- ❌ **403 Forbidden**: Check user role assignments
- ❌ **Revenue mismatch**: Verify service type fees
- ❌ **Missing statistics**: Check database data

## 🔍 Troubleshooting

### Backend Issues
```bash
# Check Django logs
python manage.py runserver --verbosity=2

# Verify database data
python manage.py shell
>>> from alumni_applications.models import AlumniApplication
>>> AlumniApplication.objects.count()

# Check user roles
>>> from django.contrib.auth.models import User
>>> user = User.objects.get(username='test_admin')
>>> user.groups.all()
```

### Frontend Issues
```javascript
// Check localStorage for auth data
console.log(localStorage.getItem('token'));
console.log(localStorage.getItem('user'));

// Check API calls in Network tab
// Verify Authorization headers are present
```

### Database Issues
```sql
-- Check service types and fees
SELECT name, fee FROM service_type_servicetype;

-- Check application payment statuses
SELECT payment_status, COUNT(*) FROM alumni_applications_alumniapplication 
GROUP BY payment_status;

-- Check user roles
SELECT u.username, g.name FROM auth_user u 
JOIN auth_user_groups ug ON u.id = ug.user_id 
JOIN auth_group g ON ug.group_id = g.id;
```

## 📄 Test Reports

After running tests, check these files for detailed results:
- `rbac_test_report.json` - Backend test results
- `comprehensive_rbac_report.json` - Full test suite results
- Browser console - Frontend test results

## ✅ Validation Checklist

- [ ] All public endpoints accessible without auth
- [ ] All protected endpoints require authentication
- [ ] Revenue calculations sum actual service fees
- [ ] Time-based statistics show correct counts
- [ ] Role hierarchy properly enforced
- [ ] JWT tokens validated correctly
- [ ] CORS headers present
- [ ] CSRF protection active
- [ ] File access properly controlled
- [ ] Method-based permissions working

## 🎉 Success Criteria

**RBAC implementation is successful when:**
1. **95%+ test pass rate** across all test suites
2. **Revenue calculations accurate** to actual service fees
3. **No unauthorized access** to protected resources
4. **Proper role hierarchy** enforcement
5. **All new features** (statistics, revenue) properly protected

---

**📞 Need Help?**
- Check server logs for detailed error messages
- Verify database has test data
- Ensure all required environment variables are set
- Confirm JWT secret keys match between frontend/backend
