import random
import datetime
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
from GraduateVerification.models import (
    GraduateStudent,
    VerificationCollege,
    VerificationDepartment,
    VerificationFieldOfStudy,
    VerificationProgram,
    AdmissionClassification
)

# Amharic names for realistic data
FIRST_NAMES_MALE = [
    "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>lug<PERSON>", "<PERSON>ache<PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>sfaye", "Alemayeh<PERSON>", "<PERSON><PERSON>han<PERSON>", "Der<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON>izaw", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mekonnen", "Negash", "<PERSON>shome", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON>ew<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>me<PERSON>", "Endalkachew", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"
]

FIRST_NAMES_FEMALE = [
    "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>igist", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>be<PERSON>",
    "<PERSON>irt<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>rte", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Adanech"
]

LAST_NAMES = [
    "Abebe", "Bekele", "Tadesse", "Girma", "Kebede", "Mulugeta", "Getachew", "Haile",
    "Dawit", "Solomon", "Yohannes", "Tesfaye", "Alemayehu", "Berhanu", "Dereje", "Fikru",
    "Gizaw", "Hailu", "Jemal", "Kassa", "Lemma", "Mekonnen", "Negash", "Teshome", "Worku",
    "Yilma", "Zewdu", "Alemu", "Birhanu", "Demeke", "Endalkachew", "Fasil", "Gebre", "Habtamu",
    "Tsegaye", "Wondimu", "Assefa", "Bogale", "Chala", "Degefa", "Eshetu", "Fantahun", "Gashaw"
]

MIDDLE_NAMES = [
    "Abebe", "Bekele", "Tadesse", "Girma", "Kebede", "Mulugeta", "Getachew", "Haile",
    "Dawit", "Solomon", "Yohannes", "Tesfaye", "Alemayehu", "Berhanu", "Dereje", "Fikru",
    "Gizaw", "Hailu", "Jemal", "Kassa", "Lemma", "Mekonnen", "Negash", "Teshome", "Worku"
]

class Command(BaseCommand):
    help = 'Seeds the database with graduate verification data'

    def add_arguments(self, parser):
        parser.add_argument('count', type=int, help='Number of graduate records to create')

    def handle(self, *args, **options):
        count = options['count']
        
        # Check if we have the required related models
        colleges = list(VerificationCollege.objects.all())
        if not colleges:
            self.stdout.write(self.style.ERROR('No colleges found. Please create colleges first.'))
            return
            
        departments = list(VerificationDepartment.objects.all())
        if not departments:
            self.stdout.write(self.style.ERROR('No departments found. Please create departments first.'))
            return
            
        fields_of_study = list(VerificationFieldOfStudy.objects.all())
        if not fields_of_study:
            self.stdout.write(self.style.ERROR('No fields of study found. Please create fields of study first.'))
            return
            
        programs = list(VerificationProgram.objects.all())
        if not programs:
            self.stdout.write(self.style.ERROR('No programs found. Please create programs first.'))
            return
            
        admission_classifications = list(AdmissionClassification.objects.all())
        if not admission_classifications:
            self.stdout.write(self.style.ERROR('No admission classifications found. Please create admission classifications first.'))
            return
        
        # Create graduate records
        created_count = 0
        skipped_count = 0
        current_year = timezone.now().year
        
        self.stdout.write(f"Creating {count} graduate records...")
        
        for i in range(count):
            # Generate a unique student ID
            student_id = f"UOG/{random.randint(2010, current_year)}/{random.randint(1000, 9999)}"
            
            # Skip if student ID already exists
            if GraduateStudent.objects.filter(student_id=student_id).exists():
                skipped_count += 1
                continue
                
            # Randomly select gender and appropriate name
            gender = random.choice(['Male', 'Female'])
            if gender == 'Male':
                first_name = random.choice(FIRST_NAMES_MALE)
            else:
                first_name = random.choice(FIRST_NAMES_FEMALE)
                
            last_name = random.choice(LAST_NAMES)
            
            # 70% chance of having a middle name
            if random.random() < 0.7:
                middle_name = random.choice(MIDDLE_NAMES)
            else:
                middle_name = None
                
            # Random graduation year (weighted towards recent years)
            weights = [0.05, 0.05, 0.1, 0.1, 0.15, 0.2, 0.35]  # More recent years have higher weights
            year_options = list(range(current_year - 6, current_year + 1))
            year_of_graduation = random.choices(year_options, weights=weights)[0]
            
            # Random GPA (weighted towards higher GPAs)
            gpa_base = random.uniform(2.0, 4.0)
            # Skew towards higher GPAs
            if gpa_base < 2.5:
                gpa = Decimal(str(round(random.uniform(2.0, 2.5), 2)))
            elif gpa_base < 3.0:
                gpa = Decimal(str(round(random.uniform(2.5, 3.0), 2)))
            elif gpa_base < 3.5:
                gpa = Decimal(str(round(random.uniform(3.0, 3.5), 2)))
            else:
                gpa = Decimal(str(round(random.uniform(3.5, 4.0), 2)))
                
            # Randomly select college
            college = random.choice(colleges)
            
            # Filter departments by college
            college_departments = [d for d in departments if d.college_id == college.id]
            if not college_departments:
                # If no departments for this college, skip
                skipped_count += 1
                continue
                
            department = random.choice(college_departments)
            
            # Filter fields of study by department
            department_fields = [f for f in fields_of_study if f.department_id == department.id]
            if not department_fields:
                # If no fields for this department, skip
                skipped_count += 1
                continue
                
            field_of_study = random.choice(department_fields)
            
            # Randomly select program and admission classification
            program = random.choice(programs)
            admission_classification = random.choice(admission_classifications)
            
            # Create the graduate record
            try:
                GraduateStudent.objects.create(
                    student_id=student_id,
                    first_name=first_name,
                    middle_name=middle_name,
                    last_name=last_name,
                    year_of_graduation=year_of_graduation,
                    gpa=gpa,
                    gender=gender,
                    college=college,
                    department=department,
                    field_of_study=field_of_study,
                    program=program,
                    admission_classification=admission_classification
                )
                created_count += 1
                
                # Show progress every 100 records
                if created_count % 100 == 0:
                    self.stdout.write(f"Created {created_count} records so far...")
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating graduate record: {e}"))
                skipped_count += 1
        
        self.stdout.write(self.style.SUCCESS(f"Successfully created {created_count} graduate records"))
        if skipped_count > 0:
            self.stdout.write(self.style.WARNING(f"Skipped {skipped_count} records due to errors or duplicates"))
