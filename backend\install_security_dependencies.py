#!/usr/bin/env python3
"""
Install security dependencies for the alumni applications file upload security.
"""
import subprocess
import sys
import os

def install_package(package_name):
    """Install a Python package using pip."""
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ Successfully installed {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False

def check_package_installed(package_name):
    """Check if a package is already installed."""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """Main installation function."""
    print("🔒 Alumni Applications Security Dependencies Installer")
    print("=" * 60)
    
    # List of required packages
    security_packages = [
        "python-magic==0.4.27",
    ]
    
    # Optional packages (for enhanced security)
    optional_packages = [
        "python-magic-bin==0.4.14",  # Windows binary for python-magic
    ]
    
    print("\n📦 Installing required security packages...")
    
    success_count = 0
    total_packages = len(security_packages)
    
    for package in security_packages:
        package_name = package.split("==")[0]
        
        # Check if already installed
        if check_package_installed(package_name.replace("-", "_")):
            print(f"✅ {package_name} is already installed")
            success_count += 1
            continue
        
        # Install the package
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 Installation Summary:")
    print(f"   Successfully installed: {success_count}/{total_packages} packages")
    
    if success_count == total_packages:
        print("✅ All required security packages installed successfully!")
    else:
        print("⚠️  Some packages failed to install. Security features may be limited.")
    
    # Try to install optional packages
    print("\n🔧 Installing optional packages for enhanced security...")
    
    for package in optional_packages:
        package_name = package.split("==")[0]
        
        if check_package_installed(package_name.replace("-", "_")):
            print(f"✅ {package_name} is already installed")
            continue
        
        print(f"Installing optional package: {package_name}")
        if install_package(package):
            print(f"✅ Optional package {package_name} installed")
        else:
            print(f"⚠️  Optional package {package_name} failed to install (this is okay)")
    
    print("\n🛡️  Security Features Status:")
    
    # Check python-magic
    try:
        import magic
        print("✅ Advanced file type detection: ENABLED")
    except ImportError:
        print("⚠️  Advanced file type detection: DISABLED (python-magic not available)")
    
    # Check cache framework
    try:
        from django.core.cache import cache
        print("✅ Rate limiting with cache: ENABLED")
    except ImportError:
        print("⚠️  Rate limiting with cache: DISABLED (Django cache not available)")
    
    print("\n📋 Next Steps:")
    print("1. Restart your Django development server")
    print("2. Test file uploads in the alumni application forms")
    print("3. Check the Django logs for security validation messages")
    print("4. Monitor rate limiting and security features")
    
    print("\n🔍 Troubleshooting:")
    print("If python-magic still doesn't work:")
    print("- On Windows: Install python-magic-bin")
    print("- On Linux: Install libmagic1 (apt-get install libmagic1)")
    print("- On macOS: Install libmagic (brew install libmagic)")
    
    print("\n✨ Security implementation is ready!")

if __name__ == "__main__":
    main()
