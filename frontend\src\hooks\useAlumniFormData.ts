/**
 * Custom hook for managing alumni form data with caching and performance optimizations
 */
import { useState, useEffect, useCallback, useMemo } from 'react';
import { publicAlumniApplicationsAPI, ServiceType, College, Department, DocumentType } from '@/services/alumniApplicationsAPI';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

interface FormDataCache {
  serviceTypes: CacheEntry<ServiceType[]> | null;
  colleges: CacheEntry<College[]> | null;
  departments: { [collegeId: string]: CacheEntry<Department[]> };
  requiredDocuments: { [serviceTypeId: string]: CacheEntry<DocumentType[]> };
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 50; // Maximum number of cached entries per type

class FormDataCacheManager {
  private cache: FormDataCache = {
    serviceTypes: null,
    colleges: null,
    departments: {},
    requiredDocuments: {}
  };

  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() > entry.timestamp + entry.expiry;
  }

  private createEntry<T>(data: T, expiry: number = CACHE_DURATION): CacheEntry<T> {
    return {
      data,
      timestamp: Date.now(),
      expiry
    };
  }

  private cleanupCache<T>(cache: { [key: string]: CacheEntry<T> }) {
    const keys = Object.keys(cache);
    if (keys.length > MAX_CACHE_SIZE) {
      // Remove oldest entries
      const sortedKeys = keys.sort((a, b) => cache[a].timestamp - cache[b].timestamp);
      const keysToRemove = sortedKeys.slice(0, keys.length - MAX_CACHE_SIZE);
      keysToRemove.forEach(key => delete cache[key]);
    }
  }

  getServiceTypes(): ServiceType[] | null {
    if (!this.cache.serviceTypes || this.isExpired(this.cache.serviceTypes)) {
      return null;
    }
    return this.cache.serviceTypes.data;
  }

  setServiceTypes(data: ServiceType[]): void {
    this.cache.serviceTypes = this.createEntry(data);
  }

  getColleges(): College[] | null {
    if (!this.cache.colleges || this.isExpired(this.cache.colleges)) {
      return null;
    }
    return this.cache.colleges.data;
  }

  setColleges(data: College[]): void {
    this.cache.colleges = this.createEntry(data);
  }

  getDepartments(collegeId: string): Department[] | null {
    const entry = this.cache.departments[collegeId];
    if (!entry || this.isExpired(entry)) {
      return null;
    }
    return entry.data;
  }

  setDepartments(collegeId: string, data: Department[]): void {
    this.cache.departments[collegeId] = this.createEntry(data);
    this.cleanupCache(this.cache.departments);
  }

  getRequiredDocuments(serviceTypeId: string): DocumentType[] | null {
    const entry = this.cache.requiredDocuments[serviceTypeId];
    if (!entry || this.isExpired(entry)) {
      return null;
    }
    return entry.data;
  }

  setRequiredDocuments(serviceTypeId: string, data: DocumentType[]): void {
    this.cache.requiredDocuments[serviceTypeId] = this.createEntry(data);
    this.cleanupCache(this.cache.requiredDocuments);
  }

  clearCache(): void {
    this.cache = {
      serviceTypes: null,
      colleges: null,
      departments: {},
      requiredDocuments: {}
    };
  }
}

const cacheManager = new FormDataCacheManager();

export const useAlumniFormData = () => {
  const [serviceTypes, setServiceTypes] = useState<ServiceType[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [departments, setDepartments] = useState<{ [collegeId: string]: Department[] }>({});
  const [requiredDocuments, setRequiredDocuments] = useState<{ [serviceTypeId: string]: DocumentType[] }>({});
  const [loading, setLoading] = useState({
    serviceTypes: false,
    colleges: false,
    departments: {} as { [collegeId: string]: boolean },
    requiredDocuments: {} as { [serviceTypeId: string]: boolean }
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Load service types with caching
  const loadServiceTypes = useCallback(async () => {
    const cached = cacheManager.getServiceTypes();
    if (cached) {
      setServiceTypes(cached);
      return cached;
    }

    setLoading(prev => ({ ...prev, serviceTypes: true }));
    try {
      const response = await publicAlumniApplicationsAPI.getServiceTypes();
      const data = response.data || [];
      cacheManager.setServiceTypes(data);
      setServiceTypes(data);
      setErrors(prev => ({ ...prev, serviceTypes: '' }));
      return data;
    } catch (error) {
      console.error('Error loading service types:', error);
      setErrors(prev => ({ ...prev, serviceTypes: 'Failed to load service types' }));
      return [];
    } finally {
      setLoading(prev => ({ ...prev, serviceTypes: false }));
    }
  }, []);

  // Load colleges with caching
  const loadColleges = useCallback(async () => {
    const cached = cacheManager.getColleges();
    if (cached) {
      setColleges(cached);
      return cached;
    }

    setLoading(prev => ({ ...prev, colleges: true }));
    try {
      const response = await publicAlumniApplicationsAPI.getColleges();
      const data = response.data || [];
      cacheManager.setColleges(data);
      setColleges(data);
      setErrors(prev => ({ ...prev, colleges: '' }));
      return data;
    } catch (error) {
      console.error('Error loading colleges:', error);
      setErrors(prev => ({ ...prev, colleges: 'Failed to load colleges' }));
      return [];
    } finally {
      setLoading(prev => ({ ...prev, colleges: false }));
    }
  }, []);

  // Load departments with caching
  const loadDepartments = useCallback(async (collegeId: string) => {
    if (!collegeId) return [];

    const cached = cacheManager.getDepartments(collegeId);
    if (cached) {
      setDepartments(prev => ({ ...prev, [collegeId]: cached }));
      return cached;
    }

    setLoading(prev => ({ 
      ...prev, 
      departments: { ...prev.departments, [collegeId]: true }
    }));

    try {
      const response = await publicAlumniApplicationsAPI.getDepartments(collegeId);
      const data = response.data || [];
      cacheManager.setDepartments(collegeId, data);
      setDepartments(prev => ({ ...prev, [collegeId]: data }));
      setErrors(prev => ({ ...prev, [`departments_${collegeId}`]: '' }));
      return data;
    } catch (error) {
      console.error('Error loading departments:', error);
      setErrors(prev => ({ ...prev, [`departments_${collegeId}`]: 'Failed to load departments' }));
      return [];
    } finally {
      setLoading(prev => ({ 
        ...prev, 
        departments: { ...prev.departments, [collegeId]: false }
      }));
    }
  }, []);

  // Load required documents with caching
  const loadRequiredDocuments = useCallback(async (serviceTypeId: string) => {
    if (!serviceTypeId) return [];

    const cached = cacheManager.getRequiredDocuments(serviceTypeId);
    if (cached) {
      setRequiredDocuments(prev => ({ ...prev, [serviceTypeId]: cached }));
      return cached;
    }

    setLoading(prev => ({ 
      ...prev, 
      requiredDocuments: { ...prev.requiredDocuments, [serviceTypeId]: true }
    }));

    try {
      const response = await publicAlumniApplicationsAPI.getServiceTypeRequiredDocuments(serviceTypeId);
      const data = response.data.required_document_types || [];
      cacheManager.setRequiredDocuments(serviceTypeId, data);
      setRequiredDocuments(prev => ({ ...prev, [serviceTypeId]: data }));
      setErrors(prev => ({ ...prev, [`requiredDocuments_${serviceTypeId}`]: '' }));
      return data;
    } catch (error) {
      console.error('Error loading required documents:', error);
      setErrors(prev => ({ ...prev, [`requiredDocuments_${serviceTypeId}`]: 'Failed to load required documents' }));
      return [];
    } finally {
      setLoading(prev => ({ 
        ...prev, 
        requiredDocuments: { ...prev.requiredDocuments, [serviceTypeId]: false }
      }));
    }
  }, []);

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      await Promise.allSettled([
        loadServiceTypes(),
        loadColleges()
      ]);
    };

    initializeData();
  }, [loadServiceTypes, loadColleges]);

  // Memoized values
  const isLoading = useMemo(() => {
    return loading.serviceTypes || 
           loading.colleges || 
           Object.values(loading.departments).some(Boolean) ||
           Object.values(loading.requiredDocuments).some(Boolean);
  }, [loading]);

  const hasErrors = useMemo(() => {
    return Object.values(errors).some(error => error !== '');
  }, [errors]);

  // Clear cache function
  const clearCache = useCallback(() => {
    cacheManager.clearCache();
  }, []);

  return {
    // Data
    serviceTypes,
    colleges,
    departments,
    requiredDocuments,
    
    // Loading states
    loading,
    isLoading,
    
    // Error states
    errors,
    hasErrors,
    
    // Functions
    loadServiceTypes,
    loadColleges,
    loadDepartments,
    loadRequiredDocuments,
    clearCache
  };
};
