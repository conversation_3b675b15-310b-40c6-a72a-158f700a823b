/**
 * Comprehensive authentication and authorization types
 */

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  date_joined: string;
  last_login?: string;
  groups: Group[];
  user_permissions: Permission[];
  profile?: UserProfile;
}

export interface Group {
  id: number;
  name: string;
  permissions: Permission[];
  user_count?: number;
  description?: string;
}

export interface Permission {
  id: number;
  name: string;
  content_type: number;
  codename: string;
  content_type_name?: string;
  app_label?: string;
  model?: string;
}

export interface UserProfile {
  id: number;
  user: number;
  department?: string;
  phone?: string;
  employee_id?: string;
  full_name?: string;
  avatar?: string;
  bio?: string;
  address?: string;
  date_of_birth?: string;
  hire_date?: string;
  position?: string;
  manager?: number;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  is_active?: boolean;
  is_staff?: boolean;
  is_superuser?: boolean;
  groups?: number[];
  user_permissions?: number[];
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
  is_staff?: boolean;
  is_superuser?: boolean;
  groups?: number[];
  user_permissions?: number[];
}

export interface CreateGroupRequest {
  name: string;
  permissions?: number[];
  description?: string;
}

export interface UpdateGroupRequest {
  name?: string;
  permissions?: number[];
  description?: string;
}

export interface UserFilters {
  search?: string;
  is_active?: boolean;
  is_staff?: boolean;
  is_superuser?: boolean;
  groups?: number[];
  date_joined_after?: string;
  date_joined_before?: string;
  last_login_after?: string;
  last_login_before?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

export interface GroupFilters {
  search?: string;
  permissions?: number[];
  ordering?: string;
  page?: number;
  page_size?: number;
}

export interface PermissionFilters {
  search?: string;
  content_type?: number;
  app_label?: string;
  model?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

export interface AuthStats {
  total_users: number;
  active_users: number;
  staff_users: number;
  superusers: number;
  total_groups: number;
  total_permissions: number;
  recent_logins: number;
  inactive_users: number;
}

export interface UserActivity {
  id: number;
  user: number;
  action: string;
  timestamp: string;
  ip_address?: string;
  user_agent?: string;
  details?: Record<string, any>;
}

export interface SecurityEvent {
  id: number;
  event_type: 'login_success' | 'login_failed' | 'logout' | 'password_change' | 'permission_change' | 'account_locked';
  user?: number;
  timestamp: string;
  ip_address?: string;
  user_agent?: string;
  details?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface RoleHierarchy {
  id: number;
  name: string;
  level: number;
  parent?: number;
  children: RoleHierarchy[];
  permissions: Permission[];
  users_count: number;
}

export interface AccessControlRule {
  id: number;
  name: string;
  resource: string;
  action: string;
  conditions: Record<string, any>;
  effect: 'allow' | 'deny';
  priority: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Enums for better type safety
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}

export enum PermissionLevel {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  ADMIN = 'admin'
}

export enum GroupType {
  ROLE = 'role',
  DEPARTMENT = 'department',
  PROJECT = 'project',
  CUSTOM = 'custom'
}

export default {
  User,
  Group,
  Permission,
  UserProfile,
  CreateUserRequest,
  UpdateUserRequest,
  CreateGroupRequest,
  UpdateGroupRequest,
  UserFilters,
  GroupFilters,
  PermissionFilters,
  PaginatedResponse,
  AuthStats,
  UserActivity,
  SecurityEvent,
  RoleHierarchy,
  AccessControlRule,
  UserStatus,
  PermissionLevel,
  GroupType,
};
