import React, { useState, useEffect, lazy, Suspense } from 'react';
import usePerformanceMonitor from '@/hooks/usePerformanceMonitor';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { graduateVerificationAPI } from '@/services/api';
import { toast } from 'sonner';

// Import optimized components
import StatsCards from './dashboard/StatsCards';
const GraduatesByYearChart = lazy(() => import('./dashboard/GraduatesByYearChart'));
const GPADistributionChart = lazy(() => import('./dashboard/GPADistributionChart'));
const GPADistributionByGenderChart = lazy(() => import('./dashboard/GPADistributionByGenderChart'));

// Color definitions for charts
const COLORS = {
  blue: ['#0088FE', '#005bb7', '#003f7d', '#002347'],
  green: ['#00C49F', '#00967a', '#006854', '#003c30'],
  orange: ['#FFBB28', '#d99c00', '#a37600', '#6d4f00'],
  red: ['#FF8042', '#e05b1d', '#b34615', '#86340f'],
  purple: ['#8884D8', '#6762b0', '#464188', '#272560'],
  gender: ['#0088FE', '#FF8042'],
  verification: ['#00C49F', '#FF8042'],
  admissions: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'],
  programs: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'],
};

// Loading placeholder component
const ChartLoading = () => (
  <Card>
    <CardContent className="flex items-center justify-center py-8 h-80">
      <div className="flex flex-col items-center space-y-2">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-[#1a73c0]"></div>
        <p className="text-sm text-gray-500">Loading chart...</p>
      </div>
    </CardContent>
  </Card>
);

const OptimizedGraduateDashboard = () => {
  // Monitor performance
  const [performanceMonitoringEnabled] = useState(true);
  const [loading, setLoading] = useState(true);

  // Use performance monitor if enabled
  if (performanceMonitoringEnabled) {
    usePerformanceMonitor('OptimizedGraduateDashboard', loading);
  }
  // State for loading is defined above
  const [refreshing, setRefreshing] = useState(false);

  // Function to clear cache and refresh data
  const refreshData = async () => {
    setRefreshing(true);
    try {
      // Clear the API cache
      await graduateVerificationAPI.clearCache();
      toast.success('Cache cleared, refreshing data...');

      // Reload the page data
      fetchDashboardData();
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('Failed to refresh data');
      setRefreshing(false);
    }
  };

  // State for stats cards
  const [totalGraduates, setTotalGraduates] = useState(0);
  const [graduatesThisYear, setGraduatesThisYear] = useState(0);
  const [averageGPA, setAverageGPA] = useState(0);
  const [currentYear, setCurrentYear] = useState<number>(new Date().getFullYear());
  const [genderData, setGenderData] = useState<{name: string, value: number}[]>([]);
  const [genderGpaData, setGenderGpaData] = useState<{gender: string, avg_gpa: number, count: number}[]>([]);

  // State for charts
  const [yearData, setYearData] = useState<{year: string, count: number}[]>([]);
  const [gpaDistributionData, setGpaDistributionData] = useState<{range: string, count: number, percentage: number}[]>([]);
  const [genderGpaDistributionData, setGenderGpaDistributionData] = useState<{gender: string, data: {range: string, count: number, gender: string}[]}[]>([]);

  // State for other charts (loaded on demand)
  const [chartsLoaded, setChartsLoaded] = useState({
    yearChart: false,
    gpaDistribution: false,
    gpaDistributionByGender: false
  });

  // Define fetchDashboardData function outside useEffect for reuse
  const fetchDashboardData = async () => {
      setLoading(true);

      try {
        // Fetch essential data first for quick initial render
        const [graduatesResponse, genderReport, gpaStats] = await Promise.all([
          graduateVerificationAPI.getAllGraduates(),
          graduateVerificationAPI.getReportByGender(),
          graduateVerificationAPI.getGPAStats()
        ]);

        // Process essential data
        const graduates = graduatesResponse.data;
        setTotalGraduates(graduates.length);

        // Calculate graduates this year
        const currentYear = new Date().getFullYear();
        const thisYearGraduates = graduates.filter(g => g.year_of_graduation === currentYear);
        setGraduatesThisYear(thisYearGraduates.length);

        // Format gender data
        const genderChartData = genderReport.data.labels.map((gender, index) => ({
          name: gender,
          value: genderReport.data.data[index]
        }));
        setGenderData(genderChartData);

        // Process GPA stats data
        if (gpaStats.data) {
          // Set system-wide average GPA
          setAverageGPA(gpaStats.data.system_avg_gpa);

          // Set gender-based GPA data
          setGenderGpaData(gpaStats.data.gender_gpa);

          // Set GPA distribution data
          setGpaDistributionData(gpaStats.data.gpa_distribution);

          // Set gender GPA distribution data for current year
          if (gpaStats.data.gender_gpa_distribution) {
            setGenderGpaDistributionData(gpaStats.data.gender_gpa_distribution);
            setCurrentYear(gpaStats.data.current_year);
          }
        }

        // Now we can show the initial dashboard
        setLoading(false);

        // Fetch additional data for charts in the background
        const yearReport = await graduateVerificationAPI.getReportByYear();

        // Format year data
        const yearChartData = yearReport.data.labels.map((year, index) => ({
          year,
          count: yearReport.data.data[index]
        }));
        setYearData(yearChartData);
        setChartsLoaded(prev => ({ ...prev, yearChart: true }));

        // Mark other charts as loaded
        setChartsLoaded(prev => ({
          ...prev,
          gpaDistribution: true,
          gpaDistributionByGender: true
        }));

        // If this was a refresh, update the refreshing state
        setRefreshing(false);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Failed to load dashboard data');
        setLoading(false);
        setRefreshing(false);
      }
    };

  // Call fetchDashboardData on component mount
  useEffect(() => {
    fetchDashboardData();
  }, []);

  return (
    <div className="space-y-6">
      {/* Refresh button */}
      <div className="flex justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={refreshData}
          disabled={loading || refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </div>
      {loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="flex flex-col items-center space-y-2">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-[#1a73c0]"></div>
              <p className="text-sm text-gray-500">Loading dashboard data...</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Stats Cards - Always render first for better UX */}
          <StatsCards
            totalGraduates={totalGraduates}
            graduatesThisYear={graduatesThisYear}
            averageGPA={averageGPA}
            currentYear={currentYear}
            genderData={genderData}
            genderGpaData={genderGpaData}
            colors={COLORS}
          />

          {/* Charts - Row 1: Graduates by Year */}
          {chartsLoaded.yearChart ? (
            <Suspense fallback={<ChartLoading />}>
              <GraduatesByYearChart data={yearData} />
            </Suspense>
          ) : (
            <ChartLoading />
          )}

          {/* Charts - Row 2: GPA Distribution Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* GPA Distribution by Gender */}
            {chartsLoaded.gpaDistributionByGender ? (
              <Suspense fallback={<ChartLoading />}>
                <GPADistributionByGenderChart
                  data={genderGpaDistributionData}
                  currentYear={currentYear}
                  colors={COLORS}
                />
              </Suspense>
            ) : (
              <ChartLoading />
            )}

            {/* GPA Distribution */}
            {chartsLoaded.gpaDistribution ? (
              <Suspense fallback={<ChartLoading />}>
                <GPADistributionChart
                  data={gpaDistributionData}
                  currentYear={currentYear}
                />
              </Suspense>
            ) : (
              <ChartLoading />
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default OptimizedGraduateDashboard;
