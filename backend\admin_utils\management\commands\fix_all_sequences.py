from django.core.management.base import BaseCommand
from django.db import connection
from django.apps import apps
from django.db.models import Max


class Command(BaseCommand):
    help = 'Fix all auto-increment sequences in the database'

    def add_arguments(self, parser):
        parser.add_argument('--dry-run', action='store_true', help='Show SQL without executing')

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Get all models
        all_models = apps.get_models()
        
        for model in all_models:
            # Skip models without an auto-incrementing primary key
            if not model._meta.pk.auto_created:
                continue
                
            # Get the table name
            table_name = model._meta.db_table
            
            # Get the primary key field name
            pk_field = model._meta.pk.name
            
            # Check if the table has a sequence
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT pg_get_serial_sequence('{table_name}', '{pk_field}');")
                sequence_name = cursor.fetchone()[0]
                
                if not sequence_name:
                    self.stdout.write(self.style.WARNING(f"No sequence found for {model.__name__}.{pk_field}"))
                    continue
            
            # Get the maximum ID
            max_id = model.objects.aggregate(max_id=Max(pk_field))['max_id'] or 0
            
            # Prepare the SQL to reset the sequence
            sql = f"SELECT setval('{sequence_name}', {max_id + 1}, false);"
            
            if dry_run:
                self.stdout.write(self.style.SUCCESS(f"SQL (not executed): {sql}"))
                self.stdout.write(self.style.SUCCESS(f"Model: {model.__name__}, Current max ID: {max_id}, Next ID will be: {max_id + 1}"))
            else:
                # Execute the SQL
                with connection.cursor() as cursor:
                    cursor.execute(sql)
                    result = cursor.fetchone()[0]
                
                self.stdout.write(self.style.SUCCESS(f"Fixed sequence for {model.__name__}"))
                self.stdout.write(self.style.SUCCESS(f"Current max ID: {max_id}, Next ID will be: {result}"))
                
        self.stdout.write(self.style.SUCCESS("All sequences have been fixed!"))
