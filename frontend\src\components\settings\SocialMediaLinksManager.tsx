import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { settingsAPI } from '@/services/api';
import { SocialMediaLink, ReorderItem } from '@/services/settingsAPI';
import { Loader2, Plus, Save, Trash2, Upload, RefreshCw, ExternalLink } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const SocialMediaLinksManager: React.FC = () => {
  const { socialMediaLinks, refreshSettings } = useSettings();
  const [links, setLinks] = useState<SocialMediaLink[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLink, setEditingLink] = useState<SocialMediaLink | null>(null);
  const [iconFile, setIconFile] = useState<File | null>(null);
  const [iconPreview, setIconPreview] = useState<string | null>(null);
  const [formData, setFormData] = useState<SocialMediaLink>({
    platform: '',
    url: '',
    display_name: '',
    order: 0,
    is_active: true,
  });

  // Platform options
  const platformOptions = [
    { value: 'facebook', label: 'Facebook' },
    { value: 'twitter', label: 'Twitter' },
    { value: 'linkedin', label: 'LinkedIn' },
    { value: 'youtube', label: 'YouTube' },
    { value: 'telegram', label: 'Telegram' },
    { value: 'instagram', label: 'Instagram' },
    { value: 'tiktok', label: 'TikTok' },
    { value: 'other', label: 'Other' },
  ];

  // Initialize links from context
  useEffect(() => {
    if (socialMediaLinks) {
      setLinks([...socialMediaLinks].sort((a, b) => a.order - b.order));
    }
  }, [socialMediaLinks]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);

      setIconFile(file);
      setIconPreview(previewUrl);
    }
  };

  // Open dialog for creating a new link
  const handleAddLink = () => {
    setEditingLink(null);
    setFormData({
      platform: '',
      url: '',
      display_name: '',
      order: links.length,
      is_active: true,
    });
    setIconFile(null);
    setIconPreview(null);
    setIsDialogOpen(true);
  };

  // Open dialog for editing an existing link
  const handleEditLink = (link: SocialMediaLink) => {
    setEditingLink(link);
    setFormData({
      ...link,
    });
    setIconFile(null);
    setIconPreview(link.icon_url || null);
    setIsDialogOpen(true);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Prepare data for submission
      const dataToSubmit: SocialMediaLink = {
        ...formData,
      };

      // Add icon if it exists
      if (iconFile) {
        dataToSubmit.icon = iconFile;
      }

      if (editingLink) {
        // Update existing link
        await settingsAPI.updateSocialMediaLink(editingLink.id!, dataToSubmit);
        toast.success('Social media link updated successfully');
      } else {
        // Create new link
        await settingsAPI.createSocialMediaLink(dataToSubmit);
        toast.success('Social media link created successfully');
      }

      // Refresh settings and close dialog
      await refreshSettings();
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error saving social media link:', error);
      toast.error('Failed to save social media link');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle link deletion
  const handleDeleteLink = async (id: number) => {
    if (!confirm('Are you sure you want to delete this link?')) {
      return;
    }

    setIsLoading(true);

    try {
      await settingsAPI.deleteSocialMediaLink(id);
      toast.success('Social media link deleted successfully');
      await refreshSettings();
    } catch (error) {
      console.error('Error deleting social media link:', error);
      toast.error('Failed to delete social media link');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle moving a link up or down
  const handleMoveLink = async (id: number, direction: 'up' | 'down') => {
    const currentIndex = links.findIndex(link => link.id === id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === links.length - 1)
    ) {
      return; // Can't move further in this direction
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const newLinks = [...links];
    const [movedItem] = newLinks.splice(currentIndex, 1);
    newLinks.splice(newIndex, 0, movedItem);

    // Update local state immediately for better UX
    setLinks(newLinks);

    // Prepare data for API
    const reorderData: ReorderItem[] = newLinks.map((link, index) => ({
      id: link.id!,
      order: index,
    }));

    try {
      await settingsAPI.reorderSocialMediaLinks(reorderData);
      await refreshSettings();
    } catch (error) {
      console.error('Error reordering social media links:', error);
      toast.error('Failed to reorder social media links');
      // Revert to original order on error
      setLinks([...socialMediaLinks].sort((a, b) => a.order - b.order));
    }
  };

  // Get platform label from value
  const getPlatformLabel = (value: string) => {
    const platform = platformOptions.find(p => p.value === value);
    return platform ? platform.label : value;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Social Media Links</CardTitle>
          <CardDescription>
            Manage social media links displayed in the footer
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => refreshSettings()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleAddLink} disabled={isLoading}>
            <Plus className="h-4 w-4 mr-2" />
            Add Link
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {links.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No social media links found. Click "Add Link" to create one.
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Platform</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Display Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead style={{ width: '120px' }}>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {links.map((link, index) => (
                  <TableRow key={link.id?.toString() || index.toString()}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        {link.icon_url && (
                          <img
                            src={link.icon_url}
                            alt={link.platform}
                            className="w-5 h-5 object-contain"
                          />
                        )}
                        <span>{link.platform_display || getPlatformLabel(link.platform)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="max-w-[200px] truncate">
                      <a
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline flex items-center"
                      >
                        {link.url}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </TableCell>
                    <TableCell>{link.display_name || '-'}</TableCell>
                    <TableCell>
                      {link.is_active ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Inactive
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveLink(link.id!, 'up')}
                          disabled={index === 0}
                          className="h-8 w-8 p-0"
                        >
                          <span className="sr-only">Move Up</span>
                          ↑
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMoveLink(link.id!, 'down')}
                          disabled={index === links.length - 1}
                          className="h-8 w-8 p-0"
                        >
                          <span className="sr-only">Move Down</span>
                          ↓
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditLink(link)}
                          className="h-8 w-8 p-0"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-4 w-4 text-blue-600"
                          >
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3Z" />
                          </svg>
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteLink(link.id!)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4 text-red-600" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Social Media Link Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[550px]">
            <DialogHeader>
              <DialogTitle>
                {editingLink ? 'Edit Social Media Link' : 'Add Social Media Link'}
              </DialogTitle>
              <DialogDescription>
                {editingLink
                  ? 'Update the details of this social media link'
                  : 'Create a new social media link for the footer'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right text-sm font-medium">
                    Platform
                  </div>
                  <div className="col-span-3">
                    <Select
                      value={formData.platform}
                      onValueChange={(value) => handleSelectChange('platform', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a platform" />
                      </SelectTrigger>
                      <SelectContent>
                        {platformOptions.map((platform) => (
                          <SelectItem key={platform.value} value={platform.value}>
                            {platform.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {!formData.platform && (
                      <p className="text-red-500 text-xs mt-1">Platform is required</p>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right text-sm font-medium">
                    URL
                  </div>
                  <Input
                    id="url"
                    name="url"
                    type="url"
                    value={formData.url}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="https://facebook.com/youruniversity"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right text-sm font-medium">
                    Display Name
                  </div>
                  <Input
                    id="display_name"
                    name="display_name"
                    value={formData.display_name || ''}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="Follow us on Facebook"
                  />
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <div className="text-right text-sm font-medium pt-2">
                    Custom Icon
                  </div>
                  <div className="col-span-3">
                    <div className="flex flex-col items-center p-4 border rounded-md">
                      {iconPreview ? (
                        <div className="mb-2 p-2 bg-gray-50 rounded-md">
                          <img
                            src={iconPreview}
                            alt="Icon Preview"
                            className="max-h-16 object-contain"
                          />
                        </div>
                      ) : (
                        <div className="mb-2 p-4 bg-gray-50 rounded-md text-center text-gray-400">
                          No icon uploaded
                        </div>
                      )}
                      <div className="w-full">
                        <label
                          htmlFor="icon_upload"
                          className="w-full cursor-pointer"
                        >
                          <div className="flex items-center justify-center p-2 bg-gray-100 hover:bg-gray-200 rounded-md">
                            <Upload className="h-4 w-4 mr-2" />
                            <span>Upload Icon</span>
                          </div>
                          <Input
                            id="icon_upload"
                            type="file"
                            accept="image/*"
                            onChange={handleFileChange}
                            className="hidden"
                          />
                        </label>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Optional. If not provided, a default icon will be used.
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right text-sm font-medium">
                    Active
                  </div>
                  <div className="flex items-center space-x-2 col-span-3">
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) =>
                        handleSwitchChange('is_active', checked)
                      }
                    />
                    <span className="text-sm cursor-pointer">
                      {formData.is_active
                        ? 'Link is visible'
                        : 'Link is hidden'}
                    </span>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={isLoading || !formData.platform}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default SocialMediaLinksManager;
