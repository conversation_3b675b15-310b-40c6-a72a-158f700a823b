
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 150 50% 20%;
    --primary-foreground: 210 40% 98%;

    --secondary: 142 43% 55%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 150 50% 20%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 150 50% 20%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 142 43% 55%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 150 50% 20%;
    --primary-foreground: 210 40% 98%;

    --secondary: 142 43% 55%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 150 50% 20%;

    --sidebar-background: 150 50% 10%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 142 43% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 150 20% 15%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 150 20% 15%;
    --sidebar-ring: 142 43% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Scrollbar styling for Chrome, Safari and Opera */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Scrollbar styling for Firefox */
  html {
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
  }

  body {
    @apply bg-background text-foreground font-sans min-h-screen flex flex-col;
    margin: 0;
    padding: 0;
    /* Only show scrollbar when needed */
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* Ensure the root element takes up the full height */
  #root {
    @apply min-h-screen flex flex-col;
  }

  /* Apply custom scrollbar to html and body */
  html, body {
    &::-webkit-scrollbar {
      width: 12px;
      height: 12px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
      border: 3px solid #f1f1f1;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }

  /* Style scrollbars for elements with overflow */
  main, aside, nav, article, footer {
    /* Allow scrollbars to be visible when needed */
    overflow: auto;
  }

  /* Prevent horizontal scroll on header */
  header {
    overflow-x: hidden;
  }

  /* Prevent scroll bars in navigation menu */
  nav[role="navigation"],
  .navigation-menu,
  [data-radix-navigation-menu-root],
  [data-radix-navigation-menu-list] {
    overflow: hidden !important;
    overflow-x: hidden !important;
  }

  /* Only show scrollbars on divs when they have overflow content */
  div {
    overflow: visible;
    &.overflow-auto, &.overflow-scroll, &.overflow-x-auto, &.overflow-y-auto {
      overflow: auto;
    }
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }

  .multi-step-form-container {
    @apply max-w-4xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden;
  }

  .form-step {
    @apply p-6 animate-fade-in;
  }

  .form-step-header {
    @apply mb-6 text-center;
  }

  .form-step-title {
    @apply text-2xl font-bold text-gondar;
  }

  .form-step-description {
    @apply text-gray-600;
  }

  .form-group {
    @apply mb-4;
  }

  .form-input {
    @apply w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gondar;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-red-500 text-sm mt-1;
  }

  .form-button-container {
    @apply flex justify-between mt-6;
  }

  .form-button {
    @apply py-2 px-4 font-medium rounded-md transition-colors;
  }

  .form-button-prev {
    @apply bg-gray-100 text-gray-800 hover:bg-gray-200;
  }

  .form-button-next {
    @apply bg-gondar text-white hover:bg-gondar-dark;
  }

  .form-progress {
    @apply h-2 bg-gray-200 rounded-full overflow-hidden mb-8;
  }

  .form-progress-bar {
    @apply h-full bg-gondar transition-all duration-300 ease-in-out;
  }

  /* Flow Chart Styles */
  .flow-chart-container {
    @apply flex flex-col space-y-2 max-w-md mx-auto;
  }

  .flow-step {
    @apply flex flex-col items-center;
  }

  .flow-box {
    @apply flex items-center p-3 rounded-lg border-l-4 w-full relative shadow-sm;
  }

  .flow-number {
    @apply flex-shrink-0 bg-[#1a73c0] text-white w-7 h-7 rounded-full flex items-center justify-center font-bold text-sm mr-3;
  }

  .flow-content {
    @apply flex-grow;
  }

  .flow-title {
    @apply font-medium text-gray-800 text-sm;
  }

  .flow-description {
    @apply text-gray-600 text-xs;
  }

  .flow-icon {
    @apply flex-shrink-0 ml-2;
  }

  .flow-arrow {
    @apply h-6 flex items-center justify-center my-1 text-[#1a73c0]/70;
  }

  /* Remove extra space after pagination */
  .pagination-container {
    @apply mb-0;
  }

  /* Ensure card footers don't have extra space */
  .card-footer {
    @apply mb-0 border-t;
  }

  /* Remove extra space in tabs content */
  [data-radix-tabs-content] {
    @apply mt-0 !important;
  }

  /* Admin tabs specific styling */
  .admin-tabs [data-radix-tabs-content] {
    @apply mt-0 mb-0 overflow-visible !important;
  }

  /* Remove bottom margin from components in admin tabs */
  .admin-tabs [data-radix-tabs-content] > div {
    @apply mb-0 !important;
  }

  /* Remove bottom margin from cards in admin tabs */
  .admin-tabs [data-radix-tabs-content] .card {
    @apply mb-0 !important;
  }

  /* Remove extra padding from the bottom of the page */
  body, html, #root, main {
    @apply min-h-screen pb-0;
  }
}

/* Utility classes for scrollbar customization */
@layer utilities {
  /* Class to hide scrollbars if needed */
  .scrollbar-hidden {
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Class for custom scrollbar styling */
  .scrollbar-custom {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
  }

  /* Custom Animation Classes */
  .animate-fadeIn {
    animation: fade-in 0.5s ease-out;
  }

  .animate-slideInFromTop {
    animation: slide-in-from-top 0.5s ease-out;
  }

  .animate-slideInFromRight {
    animation: slide-in-from-right 0.5s ease-out;
  }

  .animate-scaleIn {
    animation: scale-in 0.5s ease-out;
  }

  /* Highlight effect for program details section */
  @keyframes highlight-pulse {
    0% { box-shadow: 0 0 0 0 rgba(26, 115, 192, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(26, 115, 192, 0); }
    100% { box-shadow: 0 0 0 0 rgba(26, 115, 192, 0); }
  }

  .highlight-section {
    animation: highlight-pulse 1.5s ease-out;
    transition: all 0.3s ease;
    background-color: #e6f0fa !important;
    border-color: #1a73c0 !important;
  }

  /* Animation with delay classes */
  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-400 {
    animation-delay: 400ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }

  /* Shimmer effect for loading states */
  .shimmer {
    background: linear-gradient(90deg,
      rgba(255,255,255,0) 0%,
      rgba(255,255,255,0.5) 50%,
      rgba(255,255,255,0) 100%);
    background-size: 200% 100%;
    animation: shimmer 2s linear infinite;
  }

  /* Animation for back-to-top button */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes slideIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-slide-in {
    animation: slideIn 0.5s ease-in-out 0.2s both;
  }

  .back-to-top__button {
    animation: shimmer 2s infinite ease-in-out;
  }

  /* Animation for pattern background */
  @keyframes patternShift {
    0% { background-position: 0 0; }
    100% { background-position: 100px 100px; }
  }

  .animate-pattern-shift {
    animation: patternShift 20s linear infinite;
  }
}
