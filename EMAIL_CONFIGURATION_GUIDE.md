# 📧 Email Configuration Guide - Alumni Applications System

This guide provides comprehensive instructions for configuring and troubleshooting email notifications in the Alumni Applications System.

## 🎯 Current Status

### ✅ What's Working
- **Email Service**: Fully implemented and functional
- **SMTP Configuration**: Properly configured with Office365
- **Email Templates**: Both HTML and text templates working
- **Email Logging**: Failed emails are tracked in communication system
- **Error Handling**: Robust error handling and logging

### ❌ Common Issues
- **Test Email Addresses**: Reserved domains (example.com) are rejected by SMTP
- **Invalid Email Addresses**: Non-existent emails cause SMTP errors
- **Spam Filtering**: Emails may end up in spam folders
- **SMTP Authentication**: Incorrect credentials cause sending failures

## 🔧 Email System Architecture

### Email Flow
```
Application Submitted → perform_create() → send_application_confirmation() → SMTP Server → Recipient
                                      ↓
                               Email Notification Logged
```

### Components
1. **AlumniApplicationEmailService** - Main email service class
2. **SMTP Settings** - Database-stored SMTP configuration
3. **Email Templates** - HTML and text email templates
4. **Email Notifications** - Communication system logging

## ⚙️ SMTP Configuration

### 1. Access Django Admin
```
http://localhost:8000/admin/settings_manager/smtpsettings/
```

### 2. Configure SMTP Settings

#### Office365/Outlook Configuration
```
Host: smtp.office365.com
Port: 587
Username: <EMAIL>
Password: your-app-password
Use TLS: Yes
Use SSL: No
From Email: <EMAIL>
```

#### Gmail Configuration
```
Host: smtp.gmail.com
Port: 587
Username: <EMAIL>
Password: your-app-password (not regular password)
Use TLS: Yes
Use SSL: No
From Email: <EMAIL>
```

#### Custom SMTP Configuration
```
Host: your-smtp-server.com
Port: 587 (or 465 for SSL)
Username: your-username
Password: your-password
Use TLS: Yes (for port 587)
Use SSL: Yes (for port 465)
From Email: <EMAIL>
```

### 3. App Passwords (Required for Gmail/Office365)

#### Gmail App Password
1. Enable 2-Factor Authentication
2. Go to Google Account Settings
3. Security → App passwords
4. Generate app password for "Mail"
5. Use this password in SMTP settings

#### Office365 App Password
1. Go to Microsoft Account Security
2. Enable 2-Factor Authentication
3. Create app password for email
4. Use this password in SMTP settings

## 🧪 Testing Email Configuration

### 1. Run Email Diagnosis
```bash
cd backend
python test_email_simple.py
```

### 2. Test with Real Email
```bash
cd backend
python test_real_email.py <EMAIL>
```

### 3. Test via API Endpoint
```bash
curl -X POST http://localhost:8000/api/email-test/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## 🔍 Troubleshooting Common Issues

### Issue 1: "Recipient address reserved by RFC 2606"
**Cause**: Using test email addresses like `<EMAIL>`
**Solution**: Use real email addresses for testing

### Issue 2: "Authentication failed"
**Cause**: Incorrect SMTP credentials
**Solutions**:
- Verify username and password
- Use app passwords for Gmail/Office365
- Check if 2FA is enabled

### Issue 3: "Connection refused"
**Cause**: Incorrect SMTP host or port
**Solutions**:
- Verify SMTP server settings
- Check firewall/network restrictions
- Try different ports (587, 465, 25)

### Issue 4: "Emails not received"
**Possible Causes**:
- Emails in spam folder
- Email provider blocking
- Incorrect recipient address

**Solutions**:
- Check spam/junk folders
- Whitelist sender domain
- Test with different email providers

### Issue 5: "SSL/TLS errors"
**Cause**: Incorrect encryption settings
**Solutions**:
- Use TLS for port 587
- Use SSL for port 465
- Check certificate validity

## 📝 Email Notification Logging

### Check Email Logs
```
http://localhost:8000/admin/communication/emailnotification/
```

### Email Status Types
- **sent**: Email sent successfully
- **failed**: Email sending failed
- **draft**: Email prepared but not sent

### Failed Email Investigation
1. Check error message in notification content
2. Verify recipient email address
3. Check SMTP configuration
4. Test with different email provider

## 🎨 Email Templates

### Template Locations
```
backend/alumni_applications/templates/alumni_applications/
├── confirmation_email.html    # HTML email template
└── confirmation_email.txt     # Plain text email template
```

### Template Variables
```python
{
    'applicant_name': 'Full Name',
    'transaction_id': 'ABC123',
    'service_type': 'Official Transcript',
    'service_fee': '150.00',
    'application_type': 'Complete Application',
    'submission_date': 'January 15, 2024 at 10:30 AM',
    'support_email': '<EMAIL>',
    'portal_url': 'http://localhost:8080'
}
```

### Customizing Templates
1. Edit HTML template for rich formatting
2. Edit text template for plain text fallback
3. Test template rendering with diagnosis script
4. Verify all variables are properly displayed

## 🚀 Production Deployment

### 1. Environment Variables
```bash
# Set in production environment
EMAIL_HOST=smtp.yourdomain.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-secure-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=<EMAIL>
```

### 2. Security Considerations
- Use app passwords, not regular passwords
- Enable TLS/SSL encryption
- Restrict SMTP access to application servers
- Monitor email sending rates
- Implement rate limiting

### 3. Monitoring
- Set up email delivery monitoring
- Track bounce rates and failures
- Monitor SMTP server logs
- Set up alerts for email failures

## 📊 Email Analytics

### Check Email Statistics
```python
from communication.models import EmailNotification

# Total emails sent
total_sent = EmailNotification.objects.filter(status='sent').count()

# Failed emails
failed_emails = EmailNotification.objects.filter(status='failed').count()

# Recent emails (last 24 hours)
from django.utils import timezone
from datetime import timedelta

recent_emails = EmailNotification.objects.filter(
    sent_time__gte=timezone.now() - timedelta(hours=24)
).count()
```

## 🔧 Quick Fixes

### Fix 1: Reset Email Configuration
```python
# In Django shell
from settings_manager.smtp_models import SMTPSettings
smtp = SMTPSettings.load()
smtp.apply_to_settings()
```

### Fix 2: Clear Failed Email Logs
```python
# In Django shell
from communication.models import EmailNotification
EmailNotification.objects.filter(status='failed').delete()
```

### Fix 3: Test Email Service
```python
# In Django shell
from alumni_applications.email_service import AlumniApplicationEmailService
result = AlumniApplicationEmailService.send_test_email('<EMAIL>')
print(f"Email test result: {result}")
```

## 📞 Support

### Common Commands
```bash
# Check email backend
python manage.py shell -c "from django.conf import settings; print(settings.EMAIL_BACKEND)"

# Test SMTP connection
python manage.py shell -c "from django.core.mail import send_mail; send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])"

# Check email logs
python manage.py shell -c "from communication.models import EmailNotification; print(EmailNotification.objects.count())"
```

### Log Files to Check
- Django application logs
- SMTP server logs
- Email provider logs
- Network/firewall logs

### Contact Information
- **Technical Support**: Check Django admin email notification logs
- **SMTP Issues**: Contact your email provider support
- **Template Issues**: Check template syntax and variables

---

## ✅ Email System Verification Checklist

- [ ] SMTP settings configured in Django admin
- [ ] App passwords created for Gmail/Office365
- [ ] Email templates exist and render correctly
- [ ] Test emails sent successfully to real addresses
- [ ] Email notifications logged in communication system
- [ ] Failed emails properly tracked and logged
- [ ] Production environment variables configured
- [ ] Email delivery monitoring set up

**The email system is fully functional and ready for production use with proper SMTP configuration.**
