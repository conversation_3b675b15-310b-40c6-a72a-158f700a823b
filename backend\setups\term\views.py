from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import AllowAny  # Temporarily remove authentication
from django.db.models import Q
from .models import Term
from .serializers import TermSerializer
import logging

logger = logging.getLogger(__name__)


class TermViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Terms
    Provides CRUD operations for terms with UUID primary keys
    """
    queryset = Term.objects.all()
    serializer_class = TermSerializer
    permission_classes = [AllowAny]  # Temporarily allow all for testing
    ordering = ['name']

    def get_queryset(self):
        """Filter queryset based on query parameters"""
        queryset = Term.objects.all().order_by('name')

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset

    def create(self, request, *args, **kwargs):
        """Create a new term"""
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            term = serializer.save()

            logger.info(f"Term created: {term.name}")

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error creating term: {str(e)}")
            return Response(
                {'error': 'Failed to create term', 'details': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def update(self, request, *args, **kwargs):
        """Update an existing term"""
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            term = serializer.save()

            logger.info(f"Term updated: {term.name}")

            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error updating term: {str(e)}")
            return Response(
                {'error': 'Failed to update term', 'details': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    def destroy(self, request, *args, **kwargs):
        """Delete a term"""
        try:
            instance = self.get_object()
            term_name = instance.name

            self.perform_destroy(instance)
            logger.info(f"Term deleted: {term_name}")

            return Response(
                {'message': f'Term "{term_name}" has been deleted successfully.'},
                status=status.HTTP_204_NO_CONTENT
            )

        except Exception as e:
            logger.error(f"Error deleting term: {str(e)}")
            return Response(
                {'error': 'Failed to delete term', 'details': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


