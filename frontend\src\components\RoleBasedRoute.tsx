import React, { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useRBAC } from '@/contexts/SimpleRBACContext';
import { useRoles, usePermissions, UserRoles } from '@/hooks/usePermissions';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Shield, Lock } from 'lucide-react';
import { logAuthState, logRouteAccess } from '@/utils/authDebug';

interface RoleBasedRouteProps {
  children: ReactNode;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  requireAll?: boolean; // If true, user must have ALL roles/permissions. If false, ANY will suffice
  fallbackPath?: string;
  showAccessDenied?: boolean;
  validateWithBackend?: boolean;
}

/**
 * Enhanced route protection component with comprehensive RBAC validation
 */
export const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAll = false,
  fallbackPath = '/',
  showAccessDenied = true,
  validateWithBackend = true,
}) => {
  const { 
    isLoading, 
    isAuthenticated, 
    user, 
    isSuperuser,
    validateAccess 
  } = useRBAC();
  const { hasRole, hasAnyRole, hasAllRoles } = useRoles();
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();
  const [isValidating, setIsValidating] = useState(validateWithBackend);
  const [hasValidAccess, setHasValidAccess] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const validateUserAccess = async () => {
      if (!isAuthenticated || !user) {
        setHasValidAccess(false);
        setIsValidating(false);
        return;
      }

      // Superusers always have access
      if (isSuperuser) {
        setHasValidAccess(true);
        setIsValidating(false);
        return;
      }

      try {
        // Backend validation if requested
        if (validateWithBackend) {
          const isValid = await validateAccess();
          if (!isValid) {
            console.error('Backend access validation failed');
            setHasValidAccess(false);
            setIsValidating(false);
            return;
          }
        }

        // Check role requirements
        let hasRequiredRoles = true;
        if (requiredRoles.length > 0) {
          hasRequiredRoles = requireAll 
            ? hasAllRoles(requiredRoles)
            : hasAnyRole(requiredRoles);
        }

        // Check permission requirements
        let hasRequiredPermissions = true;
        if (requiredPermissions.length > 0) {
          hasRequiredPermissions = requireAll
            ? hasAllPermissions(requiredPermissions)
            : hasAnyPermission(requiredPermissions);
        }

        const hasAccess = hasRequiredRoles && hasRequiredPermissions;
        setHasValidAccess(hasAccess);

        // Log security events
        if (!hasAccess) {
          console.warn('Access denied:', {
            user: user.username,
            path: location.pathname,
            requiredRoles,
            requiredPermissions,
            userRoles: user.roles,
            hasRequiredRoles,
            hasRequiredPermissions,
          });
        }

      } catch (error) {
        console.error('Access validation error:', error);
        setHasValidAccess(false);
      } finally {
        setIsValidating(false);
      }
    };

    if (!isLoading) {
      validateUserAccess();
    }
  }, [
    isAuthenticated, 
    user, 
    isSuperuser, 
    isLoading,
    requiredRoles,
    requiredPermissions,
    requireAll,
    validateWithBackend,
    location.pathname,
    validateAccess,
    hasAllRoles,
    hasAnyRole,
    hasAllPermissions,
    hasAnyPermission
  ]);

  // Loading state
  if (isLoading || isValidating) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Validating access...</p>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Access denied
  if (!hasValidAccess) {
    if (showAccessDenied) {
      return (
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive" className="max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="ml-2">
              <div className="space-y-2">
                <p className="font-semibold">Access Denied</p>
                <p className="text-sm">
                  You don't have the required permissions to access this page.
                </p>
                {requiredRoles.length > 0 && (
                  <p className="text-xs">
                    Required roles: {requiredRoles.join(', ')}
                  </p>
                )}
                {requiredPermissions.length > 0 && (
                  <p className="text-xs">
                    Required permissions: {requiredPermissions.join(', ')}
                  </p>
                )}
              </div>
            </AlertDescription>
          </Alert>
        </div>
      );
    }
    
    return <Navigate to={fallbackPath} replace />;
  }

  return <>{children}</>;
};

/**
 * Staff-only route protection
 */
export const StaffRoute: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => {
  const { isStaffWithGroups, isSuperuser } = useRBAC();

  if (!isSuperuser && !isStaffWithGroups) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive" className="max-w-md mx-auto">
          <Shield className="h-4 w-4" />
          <AlertDescription className="ml-2">
            <div className="space-y-2">
              <p className="font-semibold">Staff Access Required</p>
              <p className="text-sm">
                This page requires staff-level access with proper role assignments.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
};

/**
 * Enhanced staff route with dynamic access control
 * Superusers always have access, staff users need proper roles
 */
export const StaffLevelRoute: React.FC<{
  children: ReactNode;
  requiredRoles?: string[];
  fallback?: ReactNode;
}> = ({
  children,
  requiredRoles = [
    UserRoles.ADMINISTRATOR,
    UserRoles.MAIN_REGISTRAR,
    UserRoles.REGISTRAR_OFFICER,
    UserRoles.DEPARTMENT_HEAD,
    UserRoles.VERIFICATION_CLERK,
    UserRoles.OFFICIAL_CLERK,
    UserRoles.SERVICE_MANAGER
  ],
  fallback
}) => {
  const { isSuperuser, isStaffWithGroups, isLoading, isAuthenticated } = useRBAC();
  const { hasAnyRole } = useRoles();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Superusers always have access
  if (isSuperuser) {
    return <>{children}</>;
  }

  // Staff users need proper role assignments
  if (isStaffWithGroups && hasAnyRole(requiredRoles)) {
    return <>{children}</>;
  }

  // Access denied
  if (fallback) {
    return <>{fallback}</>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Alert variant="destructive" className="max-w-md mx-auto">
        <Shield className="h-4 w-4" />
        <AlertDescription className="ml-2">
          <div className="space-y-2">
            <p className="font-semibold">Staff Access Required</p>
            <p className="text-sm">
              This page requires staff-level access with proper role assignments.
            </p>
            <p className="text-xs text-muted-foreground">
              Required roles: {requiredRoles.join(', ')}
            </p>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

/**
 * Admin-only route protection
 */
export const AdminRoute: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => {
  return (
    <RoleBasedRoute
      requiredRoles={[UserRoles.ADMINISTRATOR, UserRoles.SUPER_ADMIN]}
      requireAll={false}
      showAccessDenied={!fallback}
      fallbackPath="/"
    >
      {children}
    </RoleBasedRoute>
  );
};

/**
 * Superuser-only route protection
 */
export const SuperuserRoute: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({ 
  children, 
  fallback 
}) => {
  const { isSuperuser } = useRBAC();

  if (!isSuperuser) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive" className="max-w-md mx-auto">
          <Lock className="h-4 w-4" />
          <AlertDescription className="ml-2">
            <div className="space-y-2">
              <p className="font-semibold">Superuser Access Required</p>
              <p className="text-sm">
                This page requires superuser privileges.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
};

/**
 * Flexible admin route that adapts to user type
 * Superusers get full access, staff users need proper roles
 */
export const FlexibleAdminRoute: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isSuperuser, isStaffWithGroups, isLoading, isAuthenticated, user } = useRBAC();

  // Debug logging (only once when component mounts)
  React.useEffect(() => {
    if (!isLoading && user) {
      logAuthState('FlexibleAdminRoute Check', user, {
        isSuperuser,
        isStaffWithGroups,
        isAuthenticated
      });
    }
  }, []); // Empty dependency array to run only once

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log('❌ Route Access: /graduate-admin - DENIED (Not authenticated)');
    return <Navigate to="/login" replace />;
  }

  // Superusers always have access
  if (isSuperuser) {
    console.log('✅ Route Access: /graduate-admin - GRANTED (Superuser access)');
    return <>{children}</>;
  }

  // Staff users with groups have access
  if (isStaffWithGroups) {
    console.log('✅ Route Access: /graduate-admin - GRANTED (Staff with groups access)');
    return <>{children}</>;
  }

  // For debugging - allow access for staff users even without groups
  if (user?.is_staff) {
    console.log('⚠️ Route Access: /graduate-admin - GRANTED (Staff access - debugging mode)');
    return <>{children}</>;
  }

  // Access denied for everyone else
  console.log('❌ Route Access: /graduate-admin - DENIED (Insufficient permissions)');
  return (
    <div className="container mx-auto px-4 py-8">
      <Alert variant="destructive" className="max-w-md mx-auto">
        <Shield className="h-4 w-4" />
        <AlertDescription className="ml-2">
          <div className="space-y-2">
            <p className="font-semibold">Admin Access Required</p>
            <p className="text-sm">
              This page requires administrative access. Please contact your system administrator.
            </p>
            <div className="text-xs text-muted-foreground mt-2">
              <p>Debug Info:</p>
              <p>• Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
              <p>• Superuser: {isSuperuser ? 'Yes' : 'No'}</p>
              <p>• Staff: {user?.is_staff ? 'Yes' : 'No'}</p>
              <p>• Staff with Groups: {isStaffWithGroups ? 'Yes' : 'No'}</p>
              <p>• User Roles: {user?.roles?.join(', ') || 'None'}</p>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default RoleBasedRoute;
