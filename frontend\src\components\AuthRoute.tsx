import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { useRedirectLoopPrevention } from '@/utils/redirectLoopPrevention';

interface AuthRouteProps {
  children: React.ReactNode;
}

/**
 * AuthRoute component redirects authenticated users away from auth pages (login, register, etc.)
 * If a user is already logged in, they will be redirected to the appropriate dashboard
 * Includes loop prevention logic
 */
const AuthRoute: React.FC<AuthRouteProps> = ({ children }) => {
  const location = useLocation();
  const { canRedirect } = useRedirectLoopPrevention();
  const userData = localStorage.getItem('user');
  const user = userData ? JSON.parse(userData) : null;
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const isStaff = user && (user.is_staff || user.is_superuser);

  if (isAuthenticated && user) {
    const targetPath = isStaff ? '/graduate-admin' : '/dashboard';

    // Check if redirect is allowed (prevents loops)
    if (!canRedirect(location.pathname, targetPath)) {
      console.warn(`AuthRoute: Redirect loop prevented from ${location.pathname} to ${targetPath}`);
      return <>{children}</>;
    }

    // Redirect to appropriate dashboard based on user role
    if (isStaff) {
      console.log('AuthRoute: Redirecting staff user to /graduate-admin');
      return <Navigate to="/graduate-admin" replace />;
    } else {
      console.log('AuthRoute: Redirecting regular user to /dashboard');
      return <Navigate to="/dashboard" replace />;
    }
  }

  return <>{children}</>;
};

export default AuthRoute;
