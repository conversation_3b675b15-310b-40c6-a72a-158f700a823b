from django.db import models
from django.utils.translation import gettext_lazy as _

class Service(models.Model):
    """
    Model for university services that can be requested by users.
    """
    service_name = models.CharField(
        max_length=255,
        verbose_name=_("Service Name"),
        help_text=_("Name of the service offered")
    )
    description = models.TextField(
        verbose_name=_("Description"),
        help_text=_("Detailed description of the service")
    )
    service_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name=_("Service Fee"),
        help_text=_("Fee charged for this service (in ETB)")
    )
    icon_name = models.CharField(
        max_length=50,
        verbose_name=_("Icon Name"),
        help_text=_("Name of the Lucide icon to display (e.g., 'FileText', 'GraduationCap')"),
        default="FileText"
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this service is currently available")
    )
    order = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Display Order"),
        help_text=_("Order in which to display this service (lower numbers first)")
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At")
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Updated At")
    )

    class Meta:
        verbose_name = _("Service")
        verbose_name_plural = _("Services")
        ordering = ['order', 'service_name']

    def __str__(self):
        return self.service_name
