<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="600" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a73c0;stop-opacity:1"/>
      <stop offset="50%" style="stop-color:#1a4d2e;stop-opacity:0.9"/>
      <stop offset="100%" style="stop-color:#1a73c0;stop-opacity:1"/>
    </linearGradient>
    
    <!-- Hexagon pattern definition -->
    <pattern id="hexagonPattern" width="100" height="100" patternUnits="userSpaceOnUse" patternTransform="scale(0.7)">
      <!-- Base hexagons -->
      <path d="M50 0 L93.3 25 L93.3 75 L50 100 L6.7 75 L6.7 25 Z" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
      <path d="M150 0 L193.3 25 L193.3 75 L150 100 L106.7 75 L106.7 25 Z" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
      <path d="M50 100 L93.3 125 L93.3 175 L50 200 L6.7 175 L6.7 125 Z" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
      <path d="M150 100 L193.3 125 L193.3 175 L150 200 L106.7 175 L106.7 125 Z" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
      <path d="M100 50 L143.3 75 L143.3 125 L100 150 L56.7 125 L56.7 75 Z" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
      <path d="M0 50 L43.3 75 L43.3 125 L0 150 L-43.3 125 L-43.3 75 Z" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
      <path d="M200 50 L243.3 75 L243.3 125 L200 150 L156.7 125 L156.7 75 Z" fill="none" stroke="white" stroke-width="1" stroke-opacity="0.2"/>
    </pattern>
    
    <!-- Highlighted hexagons pattern -->
    <pattern id="highlightedHexagons" width="300" height="300" patternUnits="userSpaceOnUse" patternTransform="scale(0.8)">
      <path d="M150 0 L193.3 25 L193.3 75 L150 100 L106.7 75 L106.7 25 Z" fill="white" fill-opacity="0.05"/>
      <path d="M50 100 L93.3 125 L93.3 175 L50 200 L6.7 175 L6.7 125 Z" fill="white" fill-opacity="0.05"/>
      <path d="M250 100 L293.3 125 L293.3 175 L250 200 L206.7 175 L206.7 125 Z" fill="white" fill-opacity="0.05"/>
      <path d="M150 200 L193.3 225 L193.3 275 L150 300 L106.7 275 L106.7 225 Z" fill="white" fill-opacity="0.05"/>
    </pattern>
    
    <!-- Small hexagons pattern -->
    <pattern id="smallHexagons" width="50" height="50" patternUnits="userSpaceOnUse" patternTransform="scale(0.5)">
      <path d="M25 0 L46.7 12.5 L46.7 37.5 L25 50 L3.3 37.5 L3.3 12.5 Z" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.15"/>
      <path d="M75 0 L96.7 12.5 L96.7 37.5 L75 50 L53.3 37.5 L53.3 12.5 Z" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.15"/>
      <path d="M25 50 L46.7 62.5 L46.7 87.5 L25 100 L3.3 87.5 L3.3 62.5 Z" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.15"/>
      <path d="M75 50 L96.7 62.5 L96.7 87.5 L75 100 L53.3 87.5 L53.3 62.5 Z" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.15"/>
      <path d="M50 25 L71.7 37.5 L71.7 62.5 L50 75 L28.3 62.5 L28.3 37.5 Z" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.15"/>
      <path d="M0 25 L21.7 37.5 L21.7 62.5 L0 75 L-21.7 62.5 L-21.7 37.5 Z" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.15"/>
      <path d="M100 25 L121.7 37.5 L121.7 62.5 L100 75 L78.3 62.5 L78.3 37.5 Z" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.15"/>
    </pattern>
    
    <!-- Dots pattern -->
    <pattern id="dotsPattern" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="white" fill-opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Main background -->
  <rect width="1200" height="600" fill="url(#blueGradient)"/>
  
  <!-- Hexagon patterns -->
  <rect width="1200" height="600" fill="url(#hexagonPattern)"/>
  <rect width="1200" height="600" fill="url(#highlightedHexagons)"/>
  <rect width="1200" height="600" fill="url(#smallHexagons)"/>
  <rect width="1200" height="600" fill="url(#dotsPattern)"/>
  
  <!-- Large featured hexagons -->
  <g opacity="0.15" fill="white">
    <path d="M200 100 L275 143.3 L275 230 L200 273.3 L125 230 L125 143.3 Z"/>
    <path d="M1000 300 L1075 343.3 L1075 430 L1000 473.3 L925 430 L925 343.3 Z"/>
    <path d="M400 400 L475 443.3 L475 530 L400 573.3 L325 530 L325 443.3 Z"/>
    <path d="M800 150 L875 193.3 L875 280 L800 323.3 L725 280 L725 193.3 Z"/>
  </g>
  
  <!-- Animated hexagons (these would be animated via CSS) -->
  <g opacity="0.2" fill="white">
    <path d="M600 50 L650 80 L650 140 L600 170 L550 140 L550 80 Z" class="floating-hex"/>
    <path d="M300 250 L350 280 L350 340 L300 370 L250 340 L250 280 Z" class="floating-hex"/>
    <path d="M900 450 L950 480 L950 540 L900 570 L850 540 L850 480 Z" class="floating-hex"/>
  </g>
  
  <!-- Subtle wave pattern at bottom -->
  <path d="M0 550 Q 300 500, 600 550 T 1200 550 V 600 H 0 Z" fill="white" fill-opacity="0.05"/>
</svg>
