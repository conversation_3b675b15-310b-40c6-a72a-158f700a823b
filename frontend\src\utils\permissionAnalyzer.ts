import { User } from '@/types/auth';

/**
 * Permission Analyzer - Analyzes actual Django permissions and suggests mappings
 */

export interface PermissionAnalysis {
  totalPermissions: number;
  permissionsByApp: Record<string, string[]>;
  suggestedMappings: Record<string, string[]>;
  commonPatterns: string[];
}

/**
 * Analyze user permissions and generate mapping suggestions
 */
export const analyzeUserPermissions = (user: User | null): PermissionAnalysis => {
  if (!user || !user.permissions) {
    return {
      totalPermissions: 0,
      permissionsByApp: {},
      suggestedMappings: {},
      commonPatterns: []
    };
  }

  const permissionsByApp: Record<string, string[]> = {};
  const allPermissions: string[] = [];

  // Process permissions
  user.permissions.forEach((perm) => {
    if (typeof perm === 'object' && perm !== null) {
      const appLabel = perm.app_label || 'unknown';
      const codename = perm.codename || perm.name || 'unknown';
      const fullPermission = `${appLabel}.${codename}`;
      
      if (!permissionsByApp[appLabel]) {
        permissionsByApp[appLabel] = [];
      }
      permissionsByApp[appLabel].push(codename);
      allPermissions.push(fullPermission);
    } else if (typeof perm === 'string') {
      allPermissions.push(perm);
    }
  });

  // Generate suggested mappings based on actual permissions
  const suggestedMappings = generateMappingSuggestions(permissionsByApp, allPermissions);
  
  // Identify common patterns
  const commonPatterns = identifyCommonPatterns(allPermissions);

  return {
    totalPermissions: user.permissions.length,
    permissionsByApp,
    suggestedMappings,
    commonPatterns
  };
};

/**
 * Generate permission mapping suggestions for menu items
 */
const generateMappingSuggestions = (
  permissionsByApp: Record<string, string[]>, 
  allPermissions: string[]
): Record<string, string[]> => {
  const suggestions: Record<string, string[]> = {};

  // Define menu items and their potential permission patterns
  const menuPermissionPatterns = {
    // Dashboard menus
    'graduation-dashboard': [
      'view_graduatestudent', 'view_graduate', 'view_verification', 'view_student',
      'auth.view_user', 'auth.view_group'
    ],
    'application-dashboard': [
      'view_application', 'view_applicant', 'view_admission', 'view_applicationform',
      'auth.view_user', 'auth.view_group'
    ],
    'service-fee-dashboard': [
      'view_service', 'view_servicetype', 'view_fee', 'view_payment', 'view_servicefee',
      'auth.view_user', 'auth.change_user'
    ],

    // Graduate verification
    'manage-graduates': [
      'view_graduatestudent', 'change_graduatestudent', 'add_graduatestudent',
      'view_graduate', 'change_graduate', 'view_student', 'change_student'
    ],
    'manage-colleges': [
      'view_college', 'change_college', 'add_college', 'delete_college',
      'view_verificationcollege', 'change_verificationcollege'
    ],
    'manage-departments': [
      'view_department', 'change_department', 'add_department', 'delete_department',
      'view_verificationdepartment', 'change_verificationdepartment'
    ],
    'graduate-fields-of-study': [
      'view_fieldofstudy', 'change_fieldofstudy', 'add_fieldofstudy',
      'view_verificationfieldofstudy', 'change_verificationfieldofstudy'
    ],

    // Application portal
    'manage-colleges-app': [
      'view_college', 'change_college', 'view_applicationcollege', 'change_applicationcollege'
    ],
    'manage-departments-app': [
      'view_department', 'change_department', 'view_applicationdepartment', 'change_applicationdepartment'
    ],
    'manage-programs-app': [
      'view_program', 'change_program', 'view_applicationprogram', 'change_applicationprogram'
    ],
    'manage-applicants': [
      'view_applicant', 'change_applicant', 'add_applicant', 'delete_applicant',
      'view_application', 'change_application'
    ],

    // Services
    'service-types': [
      'view_servicetype', 'change_servicetype', 'add_servicetype', 'delete_servicetype'
    ],
    'document-types': [
      'view_documenttype', 'change_documenttype', 'add_documenttype', 'delete_documenttype'
    ],
    'alumni-applications-service': [
      'view_alumniservice', 'change_alumniservice', 'view_service', 'change_service',
      'view_alumniapplication', 'change_alumniapplication'
    ],

    // Officials
    'certificate-types': [
      'view_certificatetype', 'change_certificatetype', 'add_certificatetype'
    ],
    'official-certificates': [
      'view_certificate', 'change_certificate', 'add_certificate', 'view_officialcertificate'
    ],
    'announcements': [
      'view_announcement', 'change_announcement', 'add_announcement', 'delete_announcement'
    ],

    // Administrative
    'user-management': [
      'view_user', 'change_user', 'add_user', 'delete_user',
      'auth.view_user', 'auth.change_user', 'auth.add_user', 'auth.delete_user'
    ],
    'general-settings': [
      'view_settings', 'change_settings', 'view_configuration', 'change_configuration'
    ]
  };

  // Find matching permissions for each menu item
  Object.entries(menuPermissionPatterns).forEach(([menuKey, patterns]) => {
    const matchedPermissions: string[] = [];

    patterns.forEach(pattern => {
      // Check if permission exists in any format
      const found = allPermissions.find(perm => 
        perm === pattern || 
        perm.endsWith(`.${pattern}`) ||
        perm.includes(pattern)
      );
      
      if (found && !matchedPermissions.includes(found)) {
        matchedPermissions.push(found);
      }
    });

    // If no specific permissions found, use basic auth permissions for staff
    if (matchedPermissions.length === 0) {
      const basicPerms = allPermissions.filter(perm => 
        perm.includes('auth.view_user') || 
        perm.includes('auth.view_group') ||
        perm.includes('view_user') ||
        perm.includes('view_group')
      );
      matchedPermissions.push(...basicPerms.slice(0, 2)); // Take first 2
    }

    if (matchedPermissions.length > 0) {
      suggestions[menuKey] = matchedPermissions;
    }
  });

  return suggestions;
};

/**
 * Identify common permission patterns
 */
const identifyCommonPatterns = (permissions: string[]): string[] => {
  const patterns: string[] = [];

  // Check for common Django patterns
  if (permissions.some(p => p.includes('auth.'))) {
    patterns.push('Django Auth permissions detected');
  }
  
  if (permissions.some(p => p.includes('view_'))) {
    patterns.push('View permissions available');
  }
  
  if (permissions.some(p => p.includes('change_'))) {
    patterns.push('Change permissions available');
  }
  
  if (permissions.some(p => p.includes('add_'))) {
    patterns.push('Add permissions available');
  }
  
  if (permissions.some(p => p.includes('delete_'))) {
    patterns.push('Delete permissions available');
  }

  // Check for specific app patterns
  const apps = new Set(permissions.map(p => p.split('.')[0]));
  if (apps.has('auth')) patterns.push('Authentication app permissions');
  if (apps.has('contenttypes')) patterns.push('Content types permissions');
  if (apps.has('admin')) patterns.push('Admin permissions');

  return patterns;
};

/**
 * Generate TypeScript code for permission mappings
 */
export const generatePermissionMappingCode = (analysis: PermissionAnalysis): string => {
  let code = '// Generated permission mappings based on actual Django permissions\n\n';
  
  code += 'export const MENU_PERMISSIONS: Record<string, PermissionRequirement> = {\n';
  
  Object.entries(analysis.suggestedMappings).forEach(([menuKey, permissions]) => {
    code += `  '${menuKey}': {\n`;
    code += `    permissions: [\n`;
    permissions.forEach(perm => {
      code += `      '${perm}',\n`;
    });
    code += `    ],\n`;
    code += `    requireStaff: true,\n`;
    code += `    description: '${menuKey.replace(/-/g, ' ')} management'\n`;
    code += `  },\n\n`;
  });
  
  code += '};\n';
  
  return code;
};

/**
 * Log detailed permission analysis to console
 */
export const logPermissionAnalysis = (user: User | null): void => {
  const analysis = analyzeUserPermissions(user);
  
  console.log('🔍 PERMISSION ANALYSIS REPORT');
  console.log('============================');
  console.log(`📊 Total Permissions: ${analysis.totalPermissions}`);
  console.log(`📱 Apps: ${Object.keys(analysis.permissionsByApp).length}`);
  console.log('');
  
  console.log('📋 Permissions by App:');
  Object.entries(analysis.permissionsByApp).forEach(([app, perms]) => {
    console.log(`  🔹 ${app}: ${perms.length} permissions`);
    perms.forEach(perm => console.log(`    - ${perm}`));
  });
  console.log('');
  
  console.log('🎯 Suggested Menu Mappings:');
  Object.entries(analysis.suggestedMappings).forEach(([menu, perms]) => {
    console.log(`  📋 ${menu}:`);
    perms.forEach(perm => console.log(`    ✅ ${perm}`));
  });
  console.log('');
  
  console.log('🔍 Common Patterns:');
  analysis.commonPatterns.forEach(pattern => {
    console.log(`  ✨ ${pattern}`);
  });
  console.log('');
  
  console.log('💻 Generated TypeScript Code:');
  console.log(generatePermissionMappingCode(analysis));
};
