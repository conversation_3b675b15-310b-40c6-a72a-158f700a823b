from django.db.utils import IntegrityError
import re
import logging

# Set up logging
logger = logging.getLogger(__name__)


class SequenceFixerMiddleware:
    """
    Middleware to log database sequence issues for later fixing.
    Instead of trying to fix sequences during request processing (which can cause transaction issues),
    this middleware logs the issues so they can be fixed later.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        """
        Process exceptions and log sequence issues if detected.
        """
        if isinstance(exception, IntegrityError):
            error_message = str(exception)

            # Check if this is a duplicate key error
            if 'duplicate key value violates unique constraint' in error_message:
                # Extract the table name and ID from the error message
                table_match = re.search(r'Key \(id\)=\((\d+)\) already exists', error_message)
                constraint_match = re.search(r'violates unique constraint "([^"]+)"', error_message)

                if table_match and constraint_match:
                    id_value = int(table_match.group(1))
                    constraint_name = constraint_match.group(1)

                    # Extract table name from constraint name (usually in format table_name_pkey)
                    table_name = constraint_name.replace('_pkey', '')

                    # Log the issue for later fixing, but only to the debug log
                    logger.debug(
                        f"Sequence issue detected for table {table_name}. "
                        f"ID {id_value} already exists."
                    )

        # Return None to let Django continue with the normal exception handling
        return None
