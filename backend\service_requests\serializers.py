from rest_framework import serializers
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import ServiceRequest, DocumentUpload
from setups.service_type.models import ServiceType
from setups.admission_type.models import AdmissionType
from setups.study_program.models import Study<PERSON>rogram
from setups.college.models import College
from setups.department.models import Department
from setups.certificate_type.models import CertificateType


class DocumentUploadSerializer(serializers.ModelSerializer):
    """Serializer for DocumentUpload model."""
    
    document_type_name = serializers.CharField(source='document_type.name', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)
    
    class Meta:
        model = DocumentUpload
        fields = [
            'id', 'document_type', 'document_type_name', 'file', 'original_filename',
            'file_size', 'content_type', 'is_verified', 'verification_notes',
            'created_at', 'updated_at', 'uploaded_by', 'uploaded_by_name',
            'verified_by', 'verified_by_name'
        ]
        read_only_fields = [
            'id', 'original_filename', 'file_size', 'content_type',
            'created_at', 'updated_at', 'uploaded_by', 'verified_by'
        ]


class ServiceRequestListSerializer(serializers.ModelSerializer):
    """Optimized serializer for list views."""
    
    service_type_name = serializers.CharField(source='service_type.name', read_only=True)
    admission_type_name = serializers.CharField(source='admission_type.name', read_only=True)
    degree_name = serializers.CharField(source='degree.program_name', read_only=True)
    college_display = serializers.SerializerMethodField()
    department_display = serializers.SerializerMethodField()
    full_name = serializers.ReadOnlyField()
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = ServiceRequest
        fields = [
            'id', 'full_name', 'email', 'mobile', 'service_type_name',
            'admission_type_name', 'degree_name', 'college_display',
            'department_display', 'status', 'created_at', 'created_by_name'
        ]
    
    def get_college_display(self, obj):
        return obj.college_name
    
    def get_department_display(self, obj):
        return obj.department_name


class ServiceRequestDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for full CRUD operations."""
    
    # Read-only nested fields
    service_type_name = serializers.CharField(source='service_type.name', read_only=True)
    admission_type_name = serializers.CharField(source='admission_type.name', read_only=True)
    degree_name = serializers.CharField(source='degree.program_name', read_only=True)
    college_name_display = serializers.CharField(source='college.name', read_only=True)
    department_name_display = serializers.CharField(source='department.name', read_only=True)
    mailing_college_name = serializers.CharField(source='mailing_college.name', read_only=True)
    mailing_department_name = serializers.CharField(source='mailing_department.name', read_only=True)
    
    # Computed fields
    full_name = serializers.ReadOnlyField()
    college_display = serializers.SerializerMethodField()
    department_display = serializers.SerializerMethodField()
    requires_mailing_address = serializers.ReadOnlyField()
    requires_graduation_year = serializers.ReadOnlyField()
    requires_student_status = serializers.ReadOnlyField()
    
    # Audit fields
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    # Document uploads
    document_uploads = DocumentUploadSerializer(many=True, read_only=True)
    
    class Meta:
        model = ServiceRequest
        fields = [
            # Core fields
            'id', 'first_name', 'middle_name', 'last_name', 'full_name',
            'email', 'mobile',
            
            # Foreign key fields
            'service_type', 'service_type_name', 'admission_type', 'admission_type_name',
            'degree', 'degree_name',
            
            # College fields
            'college', 'college_name_display', 'college_other', 'is_college_other', 'college_display',
            
            # Department fields
            'department', 'department_name_display', 'department_other', 'is_department_other', 'department_display',
            
            # Student status fields
            'student_status', 'year_ec', 'year_gc', 'year_type',
            
            # Graduation fields
            'graduation_year_ec', 'graduation_year_gc',
            
            # Mailing address fields
            'mailing_destination', 'mailing_college', 'mailing_college_name',
            'mailing_department', 'mailing_department_name', 'institute_name',
            'institute_country', 'institute_address', 'mailing_agent', 'mailing_agent_other',
            
            # Status and computed fields
            'status', 'requires_mailing_address', 'requires_graduation_year', 'requires_student_status',
            
            # Audit fields
            'created_at', 'updated_at', 'created_by', 'created_by_name',
            'updated_by', 'updated_by_name',
            
            # Document uploads
            'document_uploads'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'updated_by'
        ]
    
    def get_college_display(self, obj):
        return obj.college_name
    
    def get_department_display(self, obj):
        return obj.department_name
    
    def validate(self, data):
        """Custom validation for the entire object."""
        # Create a temporary instance for validation
        instance = ServiceRequest(**data)
        
        # If updating, preserve the existing instance's pk
        if self.instance:
            instance.pk = self.instance.pk
        
        try:
            instance.clean()
        except DjangoValidationError as e:
            raise serializers.ValidationError(e.message_dict)
        
        return data
    
    def validate_email(self, value):
        """Validate email format and uniqueness for active requests."""
        if value:
            # Check for existing active requests with same email
            existing = ServiceRequest.objects.filter(
                email__iexact=value,
                is_deleted=False
            ).exclude(pk=self.instance.pk if self.instance else None)
            
            # Allow multiple requests but warn about duplicates
            if existing.exists():
                # This is just a warning, not an error
                pass
        
        return value
    
    def validate_mobile(self, value):
        """Validate mobile number format."""
        if value:
            # Additional mobile validation can be added here
            pass
        return value
    
    def validate_service_type(self, value):
        """Validate service type is active."""
        if value and not value.is_active:
            raise serializers.ValidationError("Selected service type is not active.")
        return value
    
    def validate_college(self, value):
        """Validate college selection based on is_college_other flag."""
        # This validation will be handled in the main validate method
        return value
    
    def validate_department(self, value):
        """Validate department selection based on is_department_other flag."""
        # This validation will be handled in the main validate method
        return value


class ServiceRequestCreateSerializer(ServiceRequestDetailSerializer):
    """Specialized serializer for creating service requests."""

    class Meta(ServiceRequestDetailSerializer.Meta):
        # Exclude some fields that shouldn't be set during creation
        exclude_fields = ['created_by', 'updated_by', 'document_uploads']
        fields = [
            # Core fields
            'id', 'first_name', 'middle_name', 'last_name', 'full_name',
            'email', 'mobile',

            # Foreign key fields
            'service_type', 'service_type_name', 'admission_type', 'admission_type_name',
            'degree', 'degree_name',

            # College fields
            'college', 'college_name_display', 'college_other', 'is_college_other', 'college_display',

            # Department fields
            'department', 'department_name_display', 'department_other', 'is_department_other', 'department_display',

            # Student status fields
            'student_status', 'year_ec', 'year_gc', 'year_type',

            # Graduation fields
            'graduation_year_ec', 'graduation_year_gc',

            # Mailing address fields
            'mailing_destination', 'mailing_college', 'mailing_college_name',
            'mailing_department', 'mailing_department_name', 'institute_name',
            'institute_country', 'institute_address', 'mailing_agent', 'mailing_agent_other',

            # Status and computed fields
            'status', 'requires_mailing_address', 'requires_graduation_year', 'requires_student_status',

            # Audit fields (read-only)
            'created_at', 'updated_at'
        ]


# PublicServiceRequestSerializer - REMOVED


class ServiceRequestUpdateSerializer(ServiceRequestDetailSerializer):
    """Specialized serializer for updating service requests."""
    
    class Meta(ServiceRequestDetailSerializer.Meta):
        # Make some fields read-only during updates
        read_only_fields = ServiceRequestDetailSerializer.Meta.read_only_fields + [
            'service_type',  # Prevent changing service type after creation
            'email',  # Prevent changing email after creation
        ]


class ServiceRequestStatusSerializer(serializers.ModelSerializer):
    """Serializer for status updates only."""
    
    class Meta:
        model = ServiceRequest
        fields = ['id', 'status', 'updated_at', 'updated_by']
        read_only_fields = ['id', 'updated_at', 'updated_by']
    
    def validate_status(self, value):
        """Validate status transitions."""
        if self.instance:
            current_status = self.instance.status
            
            # Define allowed status transitions
            allowed_transitions = {
                'pending': ['processing', 'rejected'],
                'processing': ['completed', 'rejected'],
                'completed': [],  # No transitions from completed
                'rejected': ['pending'],  # Allow resubmission
            }
            
            if value not in allowed_transitions.get(current_status, []):
                raise serializers.ValidationError(
                    f"Cannot change status from '{current_status}' to '{value}'"
                )
        
        return value


# Lookup serializers for dropdown population
class ServiceTypeLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for service type lookups."""
    
    class Meta:
        model = ServiceType
        fields = ['id', 'name', 'fee']


class AdmissionTypeLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for admission type lookups."""
    
    class Meta:
        model = AdmissionType
        fields = ['id', 'name']


class StudyProgramLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for study program lookups."""
    
    class Meta:
        model = StudyProgram
        fields = ['id', 'program_code', 'program_name']


class CollegeLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for college lookups."""
    
    class Meta:
        model = College
        fields = ['id', 'name']


class DepartmentLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for department lookups."""
    
    college_name = serializers.CharField(source='college.name', read_only=True)
    
    class Meta:
        model = Department
        fields = ['id', 'name', 'college', 'college_name']


class CertificateTypeLookupSerializer(serializers.ModelSerializer):
    """Minimal serializer for certificate type lookups."""
    
    class Meta:
        model = CertificateType
        fields = ['uuid', 'name', 'is_active']
