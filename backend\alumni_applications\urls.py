from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    AlumniApplicationViewSet,
    AlumniApplicationMiniViewSet,
    ApplicationDocumentViewSet,
    ServiceTypeLookupViewSet,
    CollegeLookupViewSet,
    DepartmentLookupViewSet,
    DocumentTypeLookupViewSet,
    ApplicationStatisticsView,
    EmailTestView
)
from .file_views import (
    serve_document_file,
    download_document_file,
    validate_file_upload,
    file_upload_security_info,
    SecureFileServeView
)

# Create router and register viewsets
router = DefaultRouter()

# Main application viewsets
router.register(r'applications/form1', AlumniApplicationViewSet, basename='application-form1')
router.register(r'applications/form2', AlumniApplicationMiniViewSet, basename='application-form2')
router.register(r'documents', ApplicationDocumentViewSet, basename='application-document')

# Lookup viewsets for dropdown population
router.register(r'lookups/service-types', ServiceTypeLookupViewSet, basename='lookup-service-type')
router.register(r'lookups/colleges', CollegeLookupViewSet, basename='lookup-college')
router.register(r'lookups/departments', DepartmentLookupViewSet, basename='lookup-department')
router.register(r'lookups/document-types', DocumentTypeLookupViewSet, basename='lookup-document-type')

urlpatterns = [
    path('', include(router.urls)),
    path('applications/statistics/', ApplicationStatisticsView.as_view(), name='application-statistics'),
    path('email/test/', EmailTestView.as_view(), name='email-test'),

    # Secure file serving endpoints
    path('documents/<uuid:document_id>/info/', serve_document_file, name='document-info'),
    path('documents/<uuid:document_id>/download/', download_document_file, name='document-download'),
    path('documents/<uuid:document_id>/view/', SecureFileServeView.as_view(), name='document-view'),

    # File upload security endpoints
    path('files/validate/', validate_file_upload, name='file-validate'),
    path('files/security-info/', file_upload_security_info, name='file-security-info'),
]

# URL patterns will be:
# /api/applications/form1/ - StudentApplicationForm CRUD
# /api/applications/form1/{id}/ - Individual Form1 application
# /api/applications/form1/{id}/update_status/ - Update application status
# /api/applications/form1/{id}/documents/ - Get application documents
# /api/applications/form1/{id}/upload_document/ - Upload document for application
#
# /api/applications/form2/ - StudentApplicationFormSimplified CRUD
# /api/applications/form2/{id}/ - Individual Form2 application
# /api/applications/form2/{id}/update_status/ - Update application status
# /api/applications/form2/{id}/documents/ - Get application documents
# /api/applications/form2/{id}/upload_document/ - Upload document for application
#
# /api/documents/ - ApplicationDocument CRUD
# /api/documents/{id}/ - Individual document
#
# /api/lookups/service-types/ - Service type dropdown data
# /api/lookups/colleges/ - College dropdown data
# /api/lookups/departments/ - Department dropdown data (can filter by ?college=uuid)
# /api/lookups/document-types/ - Document type dropdown data (can filter by ?service_type=uuid)
