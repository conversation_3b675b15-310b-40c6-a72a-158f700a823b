import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { settingsAPI } from '@/services/api';
import { OrganizationSetting, QuickLink, SocialMediaLink } from '@/services/settingsAPI';
import { toast } from 'sonner';
import settingsCacheService from '@/services/settingsCacheService';

interface SettingsContextType {
  organizationSettings: OrganizationSetting | null;
  quickLinks: QuickLink[];
  socialMediaLinks: SocialMediaLink[];
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
}

const defaultSettings: SettingsContextType = {
  organizationSettings: null,
  quickLinks: [],
  socialMediaLinks: [],
  loading: true,
  error: null,
  refreshSettings: async () => {},
};

const SettingsContext = createContext<SettingsContextType>(defaultSettings);

export const useSettings = () => useContext(SettingsContext);

interface SettingsProviderProps {
  children: ReactNode;
}

// Helper function to get initial state from localStorage
const getInitialState = <T,>(key: string, defaultValue: T): T => {
  try {
    const storedValue = localStorage.getItem(key);
    if (storedValue) {
      const parsedValue = JSON.parse(storedValue);
      // Check if the stored value has a timestamp and it's not expired (5 minutes)
      if (parsedValue.timestamp && (Date.now() - parsedValue.timestamp < 5 * 60 * 1000)) {
        return parsedValue.data;
      }
    }
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error);
  }
  return defaultValue;
};

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  // Initialize state from localStorage if available
  const [organizationSettings, setOrganizationSettings] = useState<OrganizationSetting | null>(
    getInitialState('organizationSettings', null)
  );
  const [quickLinks, setQuickLinks] = useState<QuickLink[]>(
    getInitialState('quickLinks', [])
  );
  const [socialMediaLinks, setSocialMediaLinks] = useState<SocialMediaLink[]>(
    getInitialState('socialMediaLinks', [])
  );
  const [loading, setLoading] = useState(!organizationSettings);
  const [error, setError] = useState<string | null>(null);

  // Helper function to save state to localStorage
  const saveToLocalStorage = <T,>(key: string, data: T) => {
    try {
      const item = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.error(`Error saving ${key} to localStorage:`, error);
    }
  };

  const fetchSettings = async () => {
    // Only set loading to true if we don't have cached data
    if (!organizationSettings) {
      setLoading(true);
    }
    setError(null);

    try {
      // Fetch organization settings
      const orgSettingsResponse = await settingsAPI.getPublicOrganizationSettings();
      setOrganizationSettings(orgSettingsResponse.data);
      saveToLocalStorage('organizationSettings', orgSettingsResponse.data);

      // Fetch quick links
      const quickLinksResponse = await settingsAPI.getPublicQuickLinks();
      setQuickLinks(quickLinksResponse.data);
      saveToLocalStorage('quickLinks', quickLinksResponse.data);

      // Fetch social media links
      const socialMediaResponse = await settingsAPI.getPublicSocialMediaLinks();
      setSocialMediaLinks(socialMediaResponse.data);
      saveToLocalStorage('socialMediaLinks', socialMediaResponse.data);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings. Please try again later.');

      // Only show toast if we don't have cached data
      if (!organizationSettings) {
        toast.error('Failed to load application settings');
      }
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch on mount
  useEffect(() => {
    fetchSettings();
  }, []);

  const refreshSettings = async () => {
    // Clear localStorage cache
    localStorage.removeItem('organizationSettings');
    localStorage.removeItem('quickLinks');
    localStorage.removeItem('socialMediaLinks');

    // Clear in-memory cache
    settingsCacheService.clearCache();

    // Fetch fresh data
    await fetchSettings();
  };

  const value = {
    organizationSettings,
    quickLinks,
    socialMediaLinks,
    loading,
    error,
    refreshSettings,
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsContext;
