from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from GraduateVerification.models import GraduateStudent


class Command(BaseCommand):
    help = 'Fix any soft-deleted records that have NULL deleted_at or deleted_by fields'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting soft delete fields fix...'))
        
        # Get all deleted records
        deleted_records = GraduateStudent.objects.all_with_deleted().filter(is_deleted=True)
        total_deleted = deleted_records.count()
        
        self.stdout.write(f'Found {total_deleted} deleted records')
        
        # Find records with NULL deleted_at
        null_deleted_at = deleted_records.filter(deleted_at__isnull=True)
        null_deleted_at_count = null_deleted_at.count()
        
        # Find records with NULL deleted_by
        null_deleted_by = deleted_records.filter(deleted_by__isnull=True)
        null_deleted_by_count = null_deleted_by.count()
        
        self.stdout.write(f'Records with NULL deleted_at: {null_deleted_at_count}')
        self.stdout.write(f'Records with NULL deleted_by: {null_deleted_by_count}')
        
        if null_deleted_at_count == 0 and null_deleted_by_count == 0:
            self.stdout.write(self.style.SUCCESS('✅ All soft delete fields are properly set!'))
            return
        
        # Get a default user for deleted_by (first superuser or first user)
        try:
            default_user = User.objects.filter(is_superuser=True).first()
            if not default_user:
                default_user = User.objects.first()
            
            if not default_user:
                self.stdout.write(self.style.ERROR('❌ No users found in database'))
                return
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error getting default user: {e}'))
            return
        
        # Fix NULL deleted_at fields
        if null_deleted_at_count > 0:
            self.stdout.write(f'Fixing {null_deleted_at_count} records with NULL deleted_at...')
            current_time = timezone.now()
            
            for record in null_deleted_at:
                record.deleted_at = current_time
                record.save()
                self.stdout.write(f'  Fixed deleted_at for record ID: {record.id}')
        
        # Fix NULL deleted_by fields
        if null_deleted_by_count > 0:
            self.stdout.write(f'Fixing {null_deleted_by_count} records with NULL deleted_by...')
            
            for record in null_deleted_by:
                record.deleted_by = default_user
                record.save()
                self.stdout.write(f'  Fixed deleted_by for record ID: {record.id} (set to user: {default_user.username})')
        
        self.stdout.write(self.style.SUCCESS('✅ Soft delete fields fix completed!'))
        
        # Verify the fix
        self.stdout.write('\n=== Verification ===')
        remaining_null_deleted_at = GraduateStudent.objects.all_with_deleted().filter(
            is_deleted=True, deleted_at__isnull=True
        ).count()
        remaining_null_deleted_by = GraduateStudent.objects.all_with_deleted().filter(
            is_deleted=True, deleted_by__isnull=True
        ).count()
        
        self.stdout.write(f'Remaining NULL deleted_at: {remaining_null_deleted_at}')
        self.stdout.write(f'Remaining NULL deleted_by: {remaining_null_deleted_by}')
        
        if remaining_null_deleted_at == 0 and remaining_null_deleted_by == 0:
            self.stdout.write(self.style.SUCCESS('✅ All soft delete fields are now properly set!'))
        else:
            self.stdout.write(self.style.WARNING('⚠️ Some NULL fields remain'))
