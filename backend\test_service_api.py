#!/usr/bin/env python
import os
import django
import requests
import json

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from setups.certificate_type.models import CertificateType

User = get_user_model()

def get_jwt_token():
    """Get JWT token for API testing."""
    try:
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        return access_token
    except Exception as e:
        print(f"Error getting JWT token: {e}")
        return None

def test_service_type_creation():
    """Test service type creation via API."""
    print("Testing Service Type API...")
    
    # Check certificate types first
    cert_types = CertificateType.objects.all()
    print(f"Certificate types available: {cert_types.count()}")
    for ct in cert_types:
        print(f"  - {ct.name} (UUID: {ct.uuid}, Active: {ct.is_active})")
    
    # Get JWT token
    token = get_jwt_token()
    if not token:
        print("❌ Failed to get JWT token")
        return
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Test 1: Create service type without document types
    print("\n1. Testing service type creation without document types...")
    test_data = {
        'name': 'Test Service Without Docs',
        'fee': 25.50,
        'is_active': True,
        'document_type_ids': []
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/api/service-types/',
            headers=headers,
            data=json.dumps(test_data)
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 201:
            print("✅ Service type created successfully without document types")
        else:
            print(f"❌ Failed to create service type: {response.text}")
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Create service type with document types (if available)
    if cert_types.exists():
        print("\n2. Testing service type creation with document types...")
        first_cert_uuid = str(cert_types.first().uuid)
        test_data_with_docs = {
            'name': 'Test Service With Docs',
            'fee': 35.00,
            'is_active': True,
            'document_type_ids': [first_cert_uuid]
        }
        
        try:
            response = requests.post(
                'http://localhost:8000/api/service-types/',
                headers=headers,
                data=json.dumps(test_data_with_docs)
            )
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 201:
                print("✅ Service type created successfully with document types")
            else:
                print(f"❌ Failed to create service type with docs: {response.text}")
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == '__main__':
    test_service_type_creation()
