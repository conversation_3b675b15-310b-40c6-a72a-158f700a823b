import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Calendar,
  Users,
  TrendingUp,
  Clock,
  User,
  GraduationCap,
  Building,
  BookOpen,
  RefreshCw,
  Eye,
  ChevronRight,
  UserPlus,
  Edit3,
  Sparkles,
  Activity,
  BarChart3,
  Timer,
  Star,
  Award,
  Upload,
  Trash2,
  Archive,
  TrendingDown,
  AlertTriangle
} from 'lucide-react';
import { graduateVerificationAPI } from '@/services/api';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import DeletedGraduateStatistics from './DeletedGraduateStatistics';

interface Graduate {
  id: number;
  student_id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  full_name: string;
  college_name?: string;
  department_name?: string;
  field_of_study_name?: string;
  program_name?: string;
  created_by_name?: string;
  updated_by_name?: string;
  created_at?: string;
  updated_at?: string;
  year_of_graduation: number;
  gpa: number | string;
  gender: 'Male' | 'Female';
}

interface RecentGraduatesData {
  period: string;
  period_name: string;
  count: number;
  counts: {
    today: number;
    last_3_days: number;
    last_7_days: number;
    last_15_days: number;
    last_30_days: number;
  };
  graduates: Graduate[];
}

const RecentGraduatesDashboard: React.FC = () => {
  const [data, setData] = useState<RecentGraduatesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('7');
  const [showDetails, setShowDetails] = useState(false);

  const periods = [
    { value: '1', label: 'Today', icon: Calendar, color: 'from-emerald-500 to-teal-600', bgColor: 'bg-emerald-50', textColor: 'text-emerald-700' },
    { value: '3', label: '3 Days', icon: Clock, color: 'from-blue-500 to-cyan-600', bgColor: 'bg-blue-50', textColor: 'text-blue-700' },
    { value: '7', label: '7 Days', icon: TrendingUp, color: 'from-purple-500 to-indigo-600', bgColor: 'bg-purple-50', textColor: 'text-purple-700' },
    { value: '15', label: '15 Days', icon: BarChart3, color: 'from-orange-500 to-red-600', bgColor: 'bg-orange-50', textColor: 'text-orange-700' },
    { value: '30', label: '1 Month', icon: Award, color: 'from-pink-500 to-rose-600', bgColor: 'bg-pink-50', textColor: 'text-pink-700' },
  ];

  const fetchRecentGraduates = async (period: string) => {
    try {
      setLoading(true);
      const response = await graduateVerificationAPI.getRecentGraduates(period);
      setData(response.data);
    } catch (error: any) {
      console.error('Error fetching recent graduates:', error);
      toast.error('Failed to fetch recent graduates data');

      // Set empty data to show empty state
      setData({
        period: period,
        period_name: 'Error',
        count: 0,
        counts: {
          today: 0,
          last_3_days: 0,
          last_7_days: 0,
          last_15_days: 0,
          last_30_days: 0,
        },
        graduates: []
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecentGraduates(selectedPeriod);
  }, [selectedPeriod]);

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    setShowDetails(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getGenderBadge = (gender: string) => {
    return gender === 'Male'
      ? <Badge className="bg-gradient-to-r from-blue-500 to-cyan-600 text-white border-0 shadow-md hover:shadow-lg transition-shadow duration-200">
          <User className="h-3 w-3 mr-1" />
          Male
        </Badge>
      : <Badge className="bg-gradient-to-r from-pink-500 to-rose-600 text-white border-0 shadow-md hover:shadow-lg transition-shadow duration-200">
          <User className="h-3 w-3 mr-1" />
          Female
        </Badge>;
  };

  if (loading) {
    return (
      <div className="mb-6 border-0 shadow-2xl bg-gradient-to-br from-white via-blue-50 to-indigo-100 rounded-2xl p-8">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
              <RefreshCw className="h-8 w-8 animate-spin text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full flex items-center justify-center">
              <Sparkles className="h-3 w-3 text-white" />
            </div>
          </div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-800 mb-1">Loading Dashboard</h3>
            <p className="text-blue-600 font-medium">Fetching recent graduates data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6 space-y-6">
      {/* Enhanced Statistics Dashboard with Tabs */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-500 rounded-lg">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <CardTitle className="text-xl text-blue-700">Graduate Statistics Dashboard</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <Tabs defaultValue="recent" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger
                value="recent"
                className="flex items-center space-x-2 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
              >
                <UserPlus className="h-4 w-4" />
                <span>Recent Additions</span>
              </TabsTrigger>
              <TabsTrigger
                value="deleted"
                className="flex items-center space-x-2 data-[state=active]:bg-red-500 data-[state=active]:text-white"
              >
                <Trash2 className="h-4 w-4" />
                <span>Deleted Records</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="recent" className="space-y-6">
              {/* Period Selection */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
            {periods.map((period) => {
              const Icon = period.icon;
              const count = data?.counts ?
                (period.value === '1' ? data.counts.today :
                 period.value === '3' ? data.counts.last_3_days :
                 period.value === '7' ? data.counts.last_7_days :
                 period.value === '15' ? data.counts.last_15_days :
                 data.counts.last_30_days) : 0;

              const isSelected = selectedPeriod === period.value;

              return (
                <div
                  key={period.value}
                  onClick={() => handlePeriodChange(period.value)}
                  className={cn(
                    "relative group cursor-pointer transition-all duration-300 transform hover:scale-105",
                    isSelected ? "scale-105" : "hover:scale-102"
                  )}
                >
                  <div className={cn(
                    "relative overflow-hidden rounded-2xl p-6 border-2 transition-all duration-300",
                    isSelected
                      ? `bg-gradient-to-br ${period.color} text-white border-transparent shadow-2xl`
                      : `${period.bgColor} border-gray-200 hover:border-gray-300 shadow-lg hover:shadow-xl`
                  )}>
                    {/* Background Pattern for Selected */}
                    {isSelected && (
                      <div className="absolute inset-0 opacity-20">
                        <div className="absolute inset-0" style={{
                          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                          backgroundSize: '20px 20px'
                        }}></div>
                      </div>
                    )}

                    {/* Glow Effect for Selected */}
                    {isSelected && (
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
                    )}

                    <div className="relative z-10 flex flex-col items-center space-y-3">
                      <div className={cn(
                        "p-3 rounded-xl transition-all duration-300",
                        isSelected
                          ? "bg-white/20 backdrop-blur-sm"
                          : "bg-white shadow-md group-hover:shadow-lg"
                      )}>
                        <Icon className={cn(
                          "h-6 w-6 transition-all duration-300",
                          isSelected ? "text-white" : period.textColor
                        )} />
                      </div>
                      <div className="text-center">
                        <div className={cn(
                          "text-sm font-semibold transition-all duration-300",
                          isSelected ? "text-white" : "text-gray-700"
                        )}>
                          {period.label}
                        </div>
                        <div className={cn(
                          "text-2xl font-bold transition-all duration-300",
                          isSelected ? "text-white" : period.textColor
                        )}>
                          {count}
                        </div>
                        <div className={cn(
                          "text-xs transition-all duration-300",
                          isSelected ? "text-white/80" : "text-gray-500"
                        )}>
                          graduates
                        </div>
                      </div>
                    </div>

                    {/* Selection Indicator */}
                    {isSelected && (
                      <div className="absolute top-2 right-2">
                        <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Current Period Summary */}
          {data && (
            <div className="relative overflow-hidden bg-gradient-to-r from-white via-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-100 p-6 shadow-lg">

              <div className="relative z-10 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-white">{data.count}</span>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 flex items-center">
                      {data.count} graduates added in {data.period_name.toLowerCase()}
                      <Timer className="h-5 w-5 ml-2 text-blue-500" />
                    </h3>
                    <p className="text-sm text-gray-600 mt-1 flex items-center">
                      <Sparkles className="h-4 w-4 mr-1 text-purple-500" />
                      Click "View Details" to see the complete list
                    </p>
                  </div>
                </div>
                <Button
                  onClick={() => setShowDetails(!showDetails)}
                  className={cn(
                    "relative overflow-hidden transition-all duration-300 shadow-lg hover:shadow-xl",
                    showDetails
                      ? "bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white"
                      : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white"
                  )}
                >
                  <div className="absolute inset-0 bg-white/20 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10 flex items-center">
                    <Eye className="h-4 w-4 mr-2" />
                    {showDetails ? 'Hide Details' : 'View Details'}
                    <ChevronRight className={cn(
                      "h-4 w-4 ml-2 transition-transform duration-300",
                      showDetails && "rotate-90"
                    )} />
                  </div>
                </Button>
              </div>
            </div>
          )}

      {/* Detailed List */}
      {showDetails && data && data.graduates.length > 0 && (
        <div className="border-0 shadow-2xl bg-gradient-to-br from-white to-gray-50 overflow-hidden rounded-2xl">
          <div className="relative bg-gradient-to-r from-slate-700 via-gray-800 to-slate-900 text-white p-6 rounded-t-2xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '30px 30px'
              }}></div>
            </div>

            <div className="relative z-10 flex items-center space-x-3">
              <div className="p-2 bg-white/20 backdrop-blur-sm rounded-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">
                  Graduates Added in {data.period_name}
                </h3>
                <p className="text-gray-200 mt-1">
                  Detailed list with creator and updater information
                </p>
              </div>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Student
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Academic Info
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    GPA
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created By
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Updated By
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date Added
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.graduates.map((graduate, index) => (
                  <tr key={graduate.id} className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center">
                            <GraduationCap className="h-5 w-5 text-white" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {graduate.full_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {graduate.student_id}
                          </div>
                          <div className="text-xs text-gray-400">
                            {graduate.gender}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="font-medium">{graduate.college_name}</div>
                        <div className="text-gray-500">{graduate.department_name}</div>
                        <div className="text-xs text-gray-400">{graduate.program_name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          parseFloat(graduate.gpa.toString()) >= 3.5
                            ? 'bg-green-100 text-green-800'
                            : parseFloat(graduate.gpa.toString()) >= 3.0
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {graduate.gpa}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {graduate.created_by_name ? (
                          <div className="flex items-center">
                            <UserPlus className="h-4 w-4 text-green-500 mr-1" />
                            <span>{graduate.created_by_name}</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Upload className="h-4 w-4 text-gray-400 mr-1" />
                            <span className="text-gray-500 italic">CSV Import</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {graduate.updated_by_name ? (
                          <div className="flex items-center">
                            <Edit3 className="h-4 w-4 text-blue-500 mr-1" />
                            <span>{graduate.updated_by_name}</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Upload className="h-4 w-4 text-gray-400 mr-1" />
                            <span className="text-gray-500 italic">CSV Import</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>
                        {graduate.created_at && new Date(graduate.created_at).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-400">
                        {graduate.created_at && new Date(graduate.created_at).toLocaleTimeString()}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Empty State */}
      {showDetails && data && data.graduates.length === 0 && (
        <div className="border-0 shadow-2xl bg-gradient-to-br from-white via-gray-50 to-blue-50 overflow-hidden rounded-2xl p-12 text-center relative">

          <div className="relative z-10 flex flex-col items-center space-y-6">
            <div className="relative">
              <div className="p-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full shadow-lg">
                <Users className="h-12 w-12 text-gray-400" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-white">0</span>
              </div>
            </div>

            <div className="text-center space-y-3">
              <h3 className="text-2xl font-bold text-gray-800 flex items-center justify-center">
                No graduates found
                <Clock className="h-6 w-6 ml-2 text-blue-500" />
              </h3>
              <p className="text-gray-600 text-lg max-w-md">
                No graduates were added in {data.period_name.toLowerCase()}.
                Try selecting a different time period or check back later.
              </p>
            </div>

            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Sparkles className="h-4 w-4 text-purple-400" />
              <span>New graduates will appear here when they are added to the system</span>
            </div>
          </div>
        </div>
      )}
            </TabsContent>

            <TabsContent value="deleted" className="space-y-6">
              <DeletedGraduateStatistics />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default RecentGraduatesDashboard;
