import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import {
  Home,
  Menu,
  X,
  LogOut,
  Settings,
  User,
  ChevronDown,
  ChevronRight,
  BarChart3,
  FileText,
  Users,
  Package
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<number | null>(null);

  // Check if user is staff
  useEffect(() => {
    if (user && !user.is_staff) {
      toast.error('You do not have permission to access this page');
      navigate('/');
    }
  }, [user, navigate]);

  const menuItems = [
    {
      title: 'Dashboard',
      icon: <Home className="h-5 w-5" />,
      path: '/dashboard',
      active: location.pathname === '/dashboard'
    },
    {
      title: 'Analytics',
      icon: <BarChart3 className="h-5 w-5" />,
      path: '/graduate-admin?tab=dashboard',
      active: location.pathname === '/graduate-admin' && (location.search === '' || location.search === '?tab=dashboard')
    },
    {
      title: 'User Management',
      icon: <Users className="h-5 w-5" />,
      submenu: true,
      submenuOpen: activeSubmenu === 2,
      toggleSubmenu: () => setActiveSubmenu(activeSubmenu === 2 ? null : 2),
      items: [
        {
          title: 'Users',
          path: '/graduate-admin?tab=users',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=users'
        },
        {
          title: 'Roles',
          path: '/graduate-admin?tab=roles',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=roles'
        },
        {
          title: 'Permissions',
          path: '/graduate-admin?tab=permissions',
          active: location.pathname === '/graduate-admin' && location.search === '?tab=permissions'
        }
      ]
    },
    {
      title: 'Services',
      icon: <Package className="h-5 w-5" />,
      path: '/admin/services',
      active: location.pathname === '/admin/services'
    },
    {
      title: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      path: '/graduate-admin?tab=settings',
      active: location.pathname === '/graduate-admin' && location.search === '?tab=settings'
    }
  ];

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout failed:', error);
      toast.error('Logout failed. Please try again.');
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full",
          "md:relative md:z-0"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Sidebar Header */}
          <div className="flex items-center justify-between h-16 px-4 border-b bg-[#1a73c0] text-white">
            <Link to="/dashboard" className="flex items-center space-x-2">
              <span className="text-xl font-bold">Admin Panel</span>
            </Link>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-1 rounded-md hover:bg-blue-700 md:hidden"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* User Profile */}
          <div className="p-4 border-b">
            <div className="flex items-center space-x-3">
              <Avatar>
                <AvatarFallback className="bg-[#1a73c0] text-white">
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{user?.username || 'User'}</p>
                <p className="text-xs text-gray-500">{user?.is_staff ? 'Staff' : 'User'}</p>
              </div>
            </div>
          </div>

          {/* Sidebar Content */}
          <div className="flex-1 overflow-y-auto py-4 px-3">
            <nav className="space-y-1">
              {menuItems.map((item, index) => (
                <div key={index}>
                  {item.submenu ? (
                    <div className="space-y-1">
                      <button
                        onClick={item.toggleSubmenu}
                        className={cn(
                          "flex items-center w-full px-3 py-2 text-sm font-medium rounded-md group transition-colors",
                          item.active
                            ? "bg-[#1a73c0]/10 text-[#1a73c0] border-l-4 border-[#1a73c0] font-medium"
                            : "text-gray-700 hover:bg-gray-100"
                        )}
                      >
                        <span className="mr-3">{item.icon}</span>
                        <span className="flex-1 text-left">{item.title}</span>
                        <span>
                          {item.submenuOpen ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </span>
                      </button>

                      {item.submenuOpen && (
                        <div className="pl-10 space-y-1 mt-1">
                          {item.items?.map((subItem, subIndex) => (
                            <Link
                              key={subIndex}
                              to={subItem.path}
                              className={cn(
                                "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                                subItem.active
                                  ? "bg-[#1a73c0]/10 text-[#1a73c0]"
                                  : "text-gray-700 hover:bg-gray-100"
                              )}
                            >
                              {subItem.title}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                        item.active
                          ? "bg-[#1a73c0]/10 text-[#1a73c0] border-l-4 border-[#1a73c0] font-medium"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      <span className="mr-3">{item.icon}</span>
                      <span>{item.title}</span>
                    </Link>
                  )}
                </div>
              ))}
            </nav>
          </div>

          {/* Sidebar Footer */}
          <div className="p-4 border-t">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white shadow-sm z-10">
          <div className="flex items-center justify-between h-16 px-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-1 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 md:hidden"
            >
              <Menu className="h-6 w-6" />
            </button>

            <div className="flex items-center space-x-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-[#1a73c0] text-white">
                        {user?.username?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => navigate('/profile')}>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/settings')}>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Logout</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
