import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, Upload, X, CheckCircle, AlertCircle, User, Mail, Phone, GraduationCap, Building, MapPin, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

import {
  ServiceRequestFormData,
  ServiceRequestFormErrors,
  ServiceTypeLookup,
  AdmissionTypeLookup,
  StudyProgramLookup,
  CollegeLookup,
  DepartmentLookup,
  CertificateTypeLookup,
  STUDENT_STATUS_OPTIONS,
  YEAR_TYPE_OPTIONS,
  MAILING_DESTINATION_OPTIONS,
  MAILING_AGENT_OPTIONS
} from '@/types/serviceRequest';

import {
  serviceRequestAPI,
  lookupAPI,
  serviceRequestUtils,
  documentUploadAPI
} from '@/services/serviceRequestAPI';

interface ServiceRequestFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editData?: any;
  mode?: 'create' | 'edit';
}

const ServiceRequestForm: React.FC<ServiceRequestFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  editData,
  mode = 'create'
}) => {
  // Form state
  const [formData, setFormData] = useState<ServiceRequestFormData>({
    first_name: '',
    middle_name: '',
    last_name: '',
    email: '',
    mobile: '',
    service_type: '',
    admission_type: '',
    degree: '',
    college: '',
    college_other: '',
    is_college_other: false,
    department: '',
    department_other: '',
    is_department_other: false,
    student_status: '',
    year_ec: '',
    year_gc: '',
    year_type: '',
    graduation_year_ec: '',
    graduation_year_gc: '',
    mailing_destination: '',
    mailing_college: '',
    mailing_department: '',
    institute_name: '',
    institute_country: '',
    institute_address: '',
    mailing_agent: '',
    mailing_agent_other: ''
  });

  const [formErrors, setFormErrors] = useState<ServiceRequestFormErrors>({});
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  // Lookup data
  const [serviceTypes, setServiceTypes] = useState<ServiceTypeLookup[]>([]);
  const [admissionTypes, setAdmissionTypes] = useState<AdmissionTypeLookup[]>([]);
  const [studyPrograms, setStudyPrograms] = useState<StudyProgramLookup[]>([]);
  const [colleges, setColleges] = useState<CollegeLookup[]>([]);
  const [departments, setDepartments] = useState<DepartmentLookup[]>([]);
  const [filteredDepartments, setFilteredDepartments] = useState<DepartmentLookup[]>([]);
  const [requiredDocuments, setRequiredDocuments] = useState<CertificateTypeLookup[]>([]);

  // Document upload state
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  // Computed properties
  const selectedServiceType = serviceTypes.find(st => st.id === formData.service_type);
  const requiresMailingAddress = selectedServiceType ? serviceRequestUtils.requiresMailingAddress(selectedServiceType.name) : false;
  const requiresGraduationYear = selectedServiceType ? serviceRequestUtils.requiresGraduationYear(selectedServiceType.name) : false;
  const requiresStudentStatus = selectedServiceType ? serviceRequestUtils.requiresStudentStatus(selectedServiceType.name) : false;

  // Load lookup data on component mount
  useEffect(() => {
    const loadLookupData = async () => {
      try {
        const [serviceTypesData, admissionTypesData, studyProgramsData, collegesData, departmentsData] = await Promise.all([
          lookupAPI.getServiceTypes(),
          lookupAPI.getAdmissionTypes(),
          lookupAPI.getStudyPrograms(),
          lookupAPI.getColleges(),
          lookupAPI.getDepartments()
        ]);

        setServiceTypes(serviceTypesData);
        setAdmissionTypes(admissionTypesData);
        setStudyPrograms(studyProgramsData);
        setColleges(collegesData);
        setDepartments(departmentsData);
      } catch (error) {
        console.error('Error loading lookup data:', error);
        toast.error('Failed to load form data');
      }
    };

    if (isOpen) {
      loadLookupData();
    }
  }, [isOpen]);

  // Load edit data
  useEffect(() => {
    if (editData && mode === 'edit') {
      setFormData({
        first_name: editData.first_name || '',
        middle_name: editData.middle_name || '',
        last_name: editData.last_name || '',
        email: editData.email || '',
        mobile: editData.mobile || '',
        service_type: editData.service_type || '',
        admission_type: editData.admission_type?.toString() || '',
        degree: editData.degree?.toString() || '',
        college: editData.college?.toString() || '',
        college_other: editData.college_other || '',
        is_college_other: editData.is_college_other || false,
        department: editData.department?.toString() || '',
        department_other: editData.department_other || '',
        is_department_other: editData.is_department_other || false,
        student_status: editData.student_status || '',
        year_ec: editData.year_ec?.toString() || '',
        year_gc: editData.year_gc?.toString() || '',
        year_type: editData.year_type || '',
        graduation_year_ec: editData.graduation_year_ec?.toString() || '',
        graduation_year_gc: editData.graduation_year_gc?.toString() || '',
        mailing_destination: editData.mailing_destination || '',
        mailing_college: editData.mailing_college?.toString() || '',
        mailing_department: editData.mailing_department?.toString() || '',
        institute_name: editData.institute_name || '',
        institute_country: editData.institute_country || '',
        institute_address: editData.institute_address || '',
        mailing_agent: editData.mailing_agent || '',
        mailing_agent_other: editData.mailing_agent_other || ''
      });
    }
  }, [editData, mode]);

  // Filter departments based on selected college
  useEffect(() => {
    if (formData.college && !formData.is_college_other) {
      const filtered = departments.filter(dept => dept.college.toString() === formData.college);
      setFilteredDepartments(filtered);
    } else {
      setFilteredDepartments(departments);
    }
  }, [formData.college, formData.is_college_other, departments]);

  // Load required documents when service type changes
  useEffect(() => {
    const loadRequiredDocuments = async () => {
      if (formData.service_type && selectedServiceType) {
        try {
          // For now, we'll get all certificate types since we don't have the service request ID yet
          const certificateTypes = await lookupAPI.getCertificateTypes();
          setRequiredDocuments(certificateTypes);
        } catch (error) {
          console.error('Error loading required documents:', error);
        }
      }
    };

    loadRequiredDocuments();
  }, [formData.service_type, selectedServiceType]);

  const handleInputChange = (field: keyof ServiceRequestFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear related errors
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }

    // Handle special cases
    if (field === 'is_college_other') {
      setFormData(prev => ({
        ...prev,
        college: value ? '' : prev.college,
        college_other: value ? prev.college_other : ''
      }));
    }

    if (field === 'is_department_other') {
      setFormData(prev => ({
        ...prev,
        department: value ? '' : prev.department,
        department_other: value ? prev.department_other : ''
      }));
    }

    if (field === 'college' && !formData.is_college_other) {
      setFormData(prev => ({
        ...prev,
        department: '' // Reset department when college changes
      }));
    }
  };

  const validateForm = (): boolean => {
    const serviceTypeName = selectedServiceType?.name || '';
    const errors = serviceRequestUtils.validateForm(formData, serviceTypeName);
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please fix the form errors before submitting');
      return;
    }

    setLoading(true);
    try {
      // Prepare data for submission
      const submitData = {
        ...formData,
        mobile: serviceRequestUtils.formatPhoneNumber(formData.mobile),
        // Convert string numbers to integers
        admission_type: parseInt(formData.admission_type),
        degree: parseInt(formData.degree),
        college: formData.is_college_other ? undefined : (formData.college ? parseInt(formData.college) : undefined),
        department: formData.is_department_other ? undefined : (formData.department ? parseInt(formData.department) : undefined),
        mailing_college: formData.mailing_college ? parseInt(formData.mailing_college) : undefined,
        mailing_department: formData.mailing_department ? parseInt(formData.mailing_department) : undefined,
        year_ec: formData.year_ec ? parseInt(formData.year_ec) : undefined,
        year_gc: formData.year_gc ? parseInt(formData.year_gc) : undefined,
        graduation_year_ec: formData.graduation_year_ec ? parseInt(formData.graduation_year_ec) : undefined,
        graduation_year_gc: formData.graduation_year_gc ? parseInt(formData.graduation_year_gc) : undefined,
      };

      let result;
      if (mode === 'edit' && editData?.id) {
        result = await serviceRequestAPI.update(editData.id, submitData);
        toast.success('Service request updated successfully');
      } else {
        result = await serviceRequestAPI.create(submitData);
        toast.success('Service request created successfully');
      }

      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error submitting form:', error);
      if (error.response?.data) {
        const serverErrors = error.response.data;
        setFormErrors(serverErrors);
        toast.error('Please fix the form errors');
      } else {
        toast.error('Failed to submit service request');
      }
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      first_name: '',
      middle_name: '',
      last_name: '',
      email: '',
      mobile: '',
      service_type: '',
      admission_type: '',
      degree: '',
      college: '',
      college_other: '',
      is_college_other: false,
      department: '',
      department_other: '',
      is_department_other: false,
      student_status: '',
      year_ec: '',
      year_gc: '',
      year_type: '',
      graduation_year_ec: '',
      graduation_year_gc: '',
      mailing_destination: '',
      mailing_college: '',
      mailing_department: '',
      institute_name: '',
      institute_country: '',
      institute_address: '',
      mailing_agent: '',
      mailing_agent_other: ''
    });
    setFormErrors({});
    setCurrentStep(1);
    setUploadedFiles([]);
    setUploadProgress({});
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-6">
      {[1, 2, 3, 4].map((step) => (
        <div key={step} className="flex items-center">
          <div
            className={cn(
              "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
              step <= currentStep
                ? "bg-[#1a73c0] text-white"
                : "bg-gray-200 text-gray-500"
            )}
          >
            {step}
          </div>
          {step < 4 && (
            <div
              className={cn(
                "w-12 h-1 mx-2",
                step < currentStep ? "bg-[#1a73c0]" : "bg-gray-200"
              )}
            />
          )}
        </div>
      ))}
    </div>
  );

  const renderPersonalInformation = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-4">
        <User className="h-5 w-5 text-[#1a73c0]" />
        <h3 className="text-lg font-medium text-[#1a73c0]">Personal Information</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="first_name" className="text-sm font-medium text-gray-700">
            First Name *
          </Label>
          <Input
            id="first_name"
            value={formData.first_name}
            onChange={(e) => handleInputChange('first_name', e.target.value)}
            placeholder="Enter your first name"
            className={cn(
              "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
              formErrors.first_name && "border-red-500"
            )}
          />
          {formErrors.first_name && (
            <p className="text-sm text-red-500">{formErrors.first_name}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="middle_name" className="text-sm font-medium text-gray-700">
            Middle Name
          </Label>
          <Input
            id="middle_name"
            value={formData.middle_name}
            onChange={(e) => handleInputChange('middle_name', e.target.value)}
            placeholder="Enter your middle name (optional)"
            className="border-blue-200 focus:ring-blue-400 focus:border-blue-400"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="last_name" className="text-sm font-medium text-gray-700">
            Last Name *
          </Label>
          <Input
            id="last_name"
            value={formData.last_name}
            onChange={(e) => handleInputChange('last_name', e.target.value)}
            placeholder="Enter your last name"
            className={cn(
              "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
              formErrors.last_name && "border-red-500"
            )}
          />
          {formErrors.last_name && (
            <p className="text-sm text-red-500">{formErrors.last_name}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium text-gray-700">
            Email Address *
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter your email address"
              className={cn(
                "pl-9 border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.email && "border-red-500"
              )}
            />
          </div>
          {formErrors.email && (
            <p className="text-sm text-red-500">{formErrors.email}</p>
          )}
        </div>

        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="mobile" className="text-sm font-medium text-gray-700">
            Mobile Number *
          </Label>
          <div className="relative">
            <Phone className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              id="mobile"
              value={formData.mobile}
              onChange={(e) => handleInputChange('mobile', e.target.value)}
              placeholder="+251911123456"
              className={cn(
                "pl-9 border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.mobile && "border-red-500"
              )}
            />
          </div>
          {formErrors.mobile && (
            <p className="text-sm text-red-500">{formErrors.mobile}</p>
          )}
        </div>
      </div>
    </div>
  );

  const renderServiceInformation = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-4">
        <GraduationCap className="h-5 w-5 text-[#1a73c0]" />
        <h3 className="text-lg font-medium text-[#1a73c0]">Service & Academic Information</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="service_type" className="text-sm font-medium text-gray-700">
            Service Type *
          </Label>
          <Select value={formData.service_type} onValueChange={(value) => handleInputChange('service_type', value)}>
            <SelectTrigger className={cn(
              "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
              formErrors.service_type && "border-red-500"
            )}>
              <SelectValue placeholder="Select service type" />
            </SelectTrigger>
            <SelectContent>
              {serviceTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.name} - ${type.fee}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {formErrors.service_type && (
            <p className="text-sm text-red-500">{formErrors.service_type}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="admission_type" className="text-sm font-medium text-gray-700">
            Admission Type *
          </Label>
          <Select value={formData.admission_type} onValueChange={(value) => handleInputChange('admission_type', value)}>
            <SelectTrigger className={cn(
              "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
              formErrors.admission_type && "border-red-500"
            )}>
              <SelectValue placeholder="Select admission type" />
            </SelectTrigger>
            <SelectContent>
              {admissionTypes.map((type) => (
                <SelectItem key={type.id} value={type.id.toString()}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {formErrors.admission_type && (
            <p className="text-sm text-red-500">{formErrors.admission_type}</p>
          )}
        </div>

        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="degree" className="text-sm font-medium text-gray-700">
            Degree/Study Program *
          </Label>
          <Select value={formData.degree} onValueChange={(value) => handleInputChange('degree', value)}>
            <SelectTrigger className={cn(
              "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
              formErrors.degree && "border-red-500"
            )}>
              <SelectValue placeholder="Select your degree/study program" />
            </SelectTrigger>
            <SelectContent>
              {studyPrograms.map((program) => (
                <SelectItem key={program.id} value={program.id.toString()}>
                  {program.program_code} - {program.program_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {formErrors.degree && (
            <p className="text-sm text-red-500">{formErrors.degree}</p>
          )}
        </div>

        {/* College Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">College *</Label>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_college_other"
                checked={formData.is_college_other}
                onCheckedChange={(checked) => handleInputChange('is_college_other', checked as boolean)}
              />
              <Label htmlFor="is_college_other" className="text-sm text-gray-600">
                My college is not listed (Other)
              </Label>
            </div>

            {formData.is_college_other ? (
              <Input
                value={formData.college_other}
                onChange={(e) => handleInputChange('college_other', e.target.value)}
                placeholder="Enter your college name"
                className={cn(
                  "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                  formErrors.college_other && "border-red-500"
                )}
              />
            ) : (
              <Select value={formData.college} onValueChange={(value) => handleInputChange('college', value)}>
                <SelectTrigger className={cn(
                  "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                  formErrors.college && "border-red-500"
                )}>
                  <SelectValue placeholder="Select your college" />
                </SelectTrigger>
                <SelectContent>
                  {colleges.map((college) => (
                    <SelectItem key={college.id} value={college.id.toString()}>
                      {college.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {(formErrors.college || formErrors.college_other) && (
              <p className="text-sm text-red-500">{formErrors.college || formErrors.college_other}</p>
            )}
          </div>
        </div>

        {/* Department Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">Department *</Label>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_department_other"
                checked={formData.is_department_other}
                onCheckedChange={(checked) => handleInputChange('is_department_other', checked as boolean)}
              />
              <Label htmlFor="is_department_other" className="text-sm text-gray-600">
                My department is not listed (Other)
              </Label>
            </div>

            {formData.is_department_other ? (
              <Input
                value={formData.department_other}
                onChange={(e) => handleInputChange('department_other', e.target.value)}
                placeholder="Enter your department name"
                className={cn(
                  "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                  formErrors.department_other && "border-red-500"
                )}
              />
            ) : (
              <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                <SelectTrigger className={cn(
                  "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                  formErrors.department && "border-red-500"
                )}>
                  <SelectValue placeholder="Select your department" />
                </SelectTrigger>
                <SelectContent>
                  {filteredDepartments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id.toString()}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {(formErrors.department || formErrors.department_other) && (
              <p className="text-sm text-red-500">{formErrors.department || formErrors.department_other}</p>
            )}
          </div>
        </div>
      </div>

      {/* Service Type Information */}
      {selectedServiceType && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-[#1a73c0] mb-2">Service Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Service Fee:</span> ${selectedServiceType.fee}
            </div>
            <div>
              <span className="font-medium">Requires Mailing:</span> {requiresMailingAddress ? 'Yes' : 'No'}
            </div>
            <div>
              <span className="font-medium">Requires Graduation Year:</span> {requiresGraduationYear ? 'Yes' : 'No'}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderStudentStatus = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-4">
        <Calendar className="h-5 w-5 text-[#1a73c0]" />
        <h3 className="text-lg font-medium text-[#1a73c0]">
          {requiresGraduationYear ? 'Graduation Information' : 'Student Status Information'}
        </h3>
      </div>

      {requiresGraduationYear ? (
        // Graduation Year Fields (for Original Degree service)
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="graduation_year_ec" className="text-sm font-medium text-gray-700">
              Graduation Year (Ethiopian Calendar)
            </Label>
            <Input
              id="graduation_year_ec"
              type="number"
              value={formData.graduation_year_ec}
              onChange={(e) => handleInputChange('graduation_year_ec', e.target.value)}
              placeholder="e.g., 2016"
              className={cn(
                "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.graduation_year_ec && "border-red-500"
              )}
            />
            {formErrors.graduation_year_ec && (
              <p className="text-sm text-red-500">{formErrors.graduation_year_ec}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="graduation_year_gc" className="text-sm font-medium text-gray-700">
              Graduation Year (Gregorian Calendar)
            </Label>
            <Input
              id="graduation_year_gc"
              type="number"
              value={formData.graduation_year_gc}
              onChange={(e) => handleInputChange('graduation_year_gc', e.target.value)}
              placeholder="e.g., 2024"
              className={cn(
                "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.graduation_year_gc && "border-red-500"
              )}
            />
            {formErrors.graduation_year_gc && (
              <p className="text-sm text-red-500">{formErrors.graduation_year_gc}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <p className="text-sm text-gray-600">
              <AlertCircle className="inline h-4 w-4 mr-1" />
              Please provide at least one graduation year (Ethiopian or Gregorian calendar).
            </p>
          </div>
        </div>
      ) : requiresStudentStatus ? (
        // Student Status Fields (for other services)
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="student_status" className="text-sm font-medium text-gray-700">
              Student Status *
            </Label>
            <Select value={formData.student_status} onValueChange={(value) => handleInputChange('student_status', value)}>
              <SelectTrigger className={cn(
                "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.student_status && "border-red-500"
              )}>
                <SelectValue placeholder="Select your student status" />
              </SelectTrigger>
              <SelectContent>
                {STUDENT_STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formErrors.student_status && (
              <p className="text-sm text-red-500">{formErrors.student_status}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="year_type" className="text-sm font-medium text-gray-700">
              Year Type *
            </Label>
            <Select value={formData.year_type} onValueChange={(value) => handleInputChange('year_type', value)}>
              <SelectTrigger className={cn(
                "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.year_type && "border-red-500"
              )}>
                <SelectValue placeholder="Select year type" />
              </SelectTrigger>
              <SelectContent>
                {YEAR_TYPE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formErrors.year_type && (
              <p className="text-sm text-red-500">{formErrors.year_type}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="year_ec" className="text-sm font-medium text-gray-700">
              Year (Ethiopian Calendar)
            </Label>
            <Input
              id="year_ec"
              type="number"
              value={formData.year_ec}
              onChange={(e) => handleInputChange('year_ec', e.target.value)}
              placeholder="e.g., 2017"
              className={cn(
                "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.year_ec && "border-red-500"
              )}
            />
            {formErrors.year_ec && (
              <p className="text-sm text-red-500">{formErrors.year_ec}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="year_gc" className="text-sm font-medium text-gray-700">
              Year (Gregorian Calendar)
            </Label>
            <Input
              id="year_gc"
              type="number"
              value={formData.year_gc}
              onChange={(e) => handleInputChange('year_gc', e.target.value)}
              placeholder="e.g., 2025"
              className={cn(
                "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.year_gc && "border-red-500"
              )}
            />
            {formErrors.year_gc && (
              <p className="text-sm text-red-500">{formErrors.year_gc}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <p className="text-sm text-gray-600">
              <AlertCircle className="inline h-4 w-4 mr-1" />
              Please provide at least one year (Ethiopian or Gregorian calendar).
            </p>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No additional information required for this service type.</p>
        </div>
      )}
    </div>
  );

  const renderMailingAddress = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-4">
        <MapPin className="h-5 w-5 text-[#1a73c0]" />
        <h3 className="text-lg font-medium text-[#1a73c0]">Mailing Address Information</h3>
      </div>

      {requiresMailingAddress ? (
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="mailing_destination" className="text-sm font-medium text-gray-700">
              Mailing Destination *
            </Label>
            <Select value={formData.mailing_destination} onValueChange={(value) => handleInputChange('mailing_destination', value)}>
              <SelectTrigger className={cn(
                "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                formErrors.mailing_destination && "border-red-500"
              )}>
                <SelectValue placeholder="Select mailing destination" />
              </SelectTrigger>
              <SelectContent>
                {MAILING_DESTINATION_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formErrors.mailing_destination && (
              <p className="text-sm text-red-500">{formErrors.mailing_destination}</p>
            )}
          </div>

          {formData.mailing_destination === 'uog' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="space-y-2">
                <Label htmlFor="mailing_college" className="text-sm font-medium text-gray-700">
                  Delivery College *
                </Label>
                <Select value={formData.mailing_college} onValueChange={(value) => handleInputChange('mailing_college', value)}>
                  <SelectTrigger className={cn(
                    "border-blue-200 focus:ring-blue-400 focus:border-blue-400",
                    formErrors.mailing_college && "border-red-500"
                  )}>
                    <SelectValue placeholder="Select delivery college" />
                  </SelectTrigger>
                  <SelectContent>
                    {colleges.map((college) => (
                      <SelectItem key={college.id} value={college.id.toString()}>
                        {college.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formErrors.mailing_college && (
                  <p className="text-sm text-red-500">{formErrors.mailing_college}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="mailing_department" className="text-sm font-medium text-gray-700">
                  Delivery Department
                </Label>
                <Select value={formData.mailing_department} onValueChange={(value) => handleInputChange('mailing_department', value)}>
                  <SelectTrigger className="border-blue-200 focus:ring-blue-400 focus:border-blue-400">
                    <SelectValue placeholder="Select delivery department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.filter(dept => dept.college.toString() === formData.mailing_college).map((dept) => (
                      <SelectItem key={dept.id} value={dept.id.toString()}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {formData.mailing_destination === 'external' && (
            <div className="space-y-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="institute_name" className="text-sm font-medium text-gray-700">
                    Institution Name *
                  </Label>
                  <Input
                    id="institute_name"
                    value={formData.institute_name}
                    onChange={(e) => handleInputChange('institute_name', e.target.value)}
                    placeholder="Enter institution name"
                    className={cn(
                      "border-orange-200 focus:ring-orange-400 focus:border-orange-400",
                      formErrors.institute_name && "border-red-500"
                    )}
                  />
                  {formErrors.institute_name && (
                    <p className="text-sm text-red-500">{formErrors.institute_name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="institute_country" className="text-sm font-medium text-gray-700">
                    Country *
                  </Label>
                  <Input
                    id="institute_country"
                    value={formData.institute_country}
                    onChange={(e) => handleInputChange('institute_country', e.target.value)}
                    placeholder="Enter country"
                    className={cn(
                      "border-orange-200 focus:ring-orange-400 focus:border-orange-400",
                      formErrors.institute_country && "border-red-500"
                    )}
                  />
                  {formErrors.institute_country && (
                    <p className="text-sm text-red-500">{formErrors.institute_country}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="institute_address" className="text-sm font-medium text-gray-700">
                  Complete Address *
                </Label>
                <Textarea
                  id="institute_address"
                  value={formData.institute_address}
                  onChange={(e) => handleInputChange('institute_address', e.target.value)}
                  placeholder="Enter complete mailing address"
                  rows={3}
                  className={cn(
                    "border-orange-200 focus:ring-orange-400 focus:border-orange-400",
                    formErrors.institute_address && "border-red-500"
                  )}
                />
                {formErrors.institute_address && (
                  <p className="text-sm text-red-500">{formErrors.institute_address}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="mailing_agent" className="text-sm font-medium text-gray-700">
                    Mailing Agent
                  </Label>
                  <Select value={formData.mailing_agent} onValueChange={(value) => handleInputChange('mailing_agent', value)}>
                    <SelectTrigger className="border-orange-200 focus:ring-orange-400 focus:border-orange-400">
                      <SelectValue placeholder="Select mailing agent" />
                    </SelectTrigger>
                    <SelectContent>
                      {MAILING_AGENT_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {formData.mailing_agent === 'other' && (
                  <div className="space-y-2">
                    <Label htmlFor="mailing_agent_other" className="text-sm font-medium text-gray-700">
                      Specify Mailing Agent *
                    </Label>
                    <Input
                      id="mailing_agent_other"
                      value={formData.mailing_agent_other}
                      onChange={(e) => handleInputChange('mailing_agent_other', e.target.value)}
                      placeholder="Specify mailing agent"
                      className={cn(
                        "border-orange-200 focus:ring-orange-400 focus:border-orange-400",
                        formErrors.mailing_agent_other && "border-red-500"
                      )}
                    />
                    {formErrors.mailing_agent_other && (
                      <p className="text-sm text-red-500">{formErrors.mailing_agent_other}</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No mailing address required for this service type.</p>
        </div>
      )}
    </div>
  );

  const renderDocumentUpload = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-4">
        <FileText className="h-5 w-5 text-[#1a73c0]" />
        <h3 className="text-lg font-medium text-[#1a73c0]">Required Documents</h3>
      </div>

      {requiredDocuments.length > 0 ? (
        <div className="space-y-4">
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">Required Documents for {selectedServiceType?.name}</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-yellow-700">
              {requiredDocuments.map((doc) => (
                <li key={doc.uuid}>{doc.name}</li>
              ))}
            </ul>
          </div>

          <div className="text-sm text-gray-600">
            <p className="mb-2">
              <AlertCircle className="inline h-4 w-4 mr-1" />
              Documents can be uploaded after the service request is created.
            </p>
            <p>Supported formats: PDF, JPG, PNG (Max size: 5MB per file)</p>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No specific documents required for this service type.</p>
        </div>
      )}
    </div>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderPersonalInformation();
      case 2:
        return renderServiceInformation();
      case 3:
        return renderStudentStatus();
      case 4:
        return renderMailingAddress();
      default:
        return renderPersonalInformation();
    }
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1:
        return formData.first_name && formData.last_name && formData.email && formData.mobile;
      case 2:
        return formData.service_type && formData.admission_type && formData.degree &&
               ((formData.is_college_other && formData.college_other) || (!formData.is_college_other && formData.college)) &&
               ((formData.is_department_other && formData.department_other) || (!formData.is_department_other && formData.department));
      case 3:
        if (requiresGraduationYear) {
          return formData.graduation_year_ec || formData.graduation_year_gc;
        } else if (requiresStudentStatus) {
          return formData.student_status && formData.year_type && (formData.year_ec || formData.year_gc);
        }
        return true;
      case 4:
        if (requiresMailingAddress) {
          if (formData.mailing_destination === 'uog') {
            return formData.mailing_college;
          } else if (formData.mailing_destination === 'external') {
            return formData.institute_name && formData.institute_country && formData.institute_address;
          }
          return formData.mailing_destination;
        }
        return true;
      default:
        return true;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">
                {mode === 'edit' ? 'Edit Service Request' : 'Create New Service Request'}
              </DialogTitle>
              <DialogDescription className="text-gray-600 leading-relaxed">
                {mode === 'edit'
                  ? 'Update the service request information below.'
                  : 'Fill out the form below to submit a new service request. All required fields are marked with an asterisk (*).'}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="p-6">
          {/* Step Indicator */}
          {renderStepIndicator()}

          {/* Step Content */}
          <div className="min-h-[400px]">
            {renderStepContent()}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <div className="flex space-x-3">
              {currentStep > 1 && (
                <Button
                  variant="outline"
                  onClick={prevStep}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Previous
                </Button>
              )}
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleClose}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>

              {currentStep < 4 ? (
                <Button
                  onClick={nextStep}
                  disabled={!canProceedToNextStep()}
                  className="bg-[#1a73c0] hover:bg-blue-700 text-white transition-all duration-200"
                >
                  Next
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={loading || !canProceedToNextStep()}
                  className="bg-[#1a73c0] hover:bg-blue-700 text-white transition-all duration-200"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {mode === 'edit' ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {mode === 'edit' ? 'Update Request' : 'Submit Request'}
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ServiceRequestForm;
