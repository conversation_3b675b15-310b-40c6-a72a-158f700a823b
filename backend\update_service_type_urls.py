#!/usr/bin/env python3
"""
Script to update ServiceType URLs based on service names.
This script sets appropriate URLs for each service type to determine which form to use.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.service_type.models import ServiceType

def update_service_type_urls():
    """Update ServiceType URLs based on service names."""
    
    # Define URL mappings based on service types
    url_mappings = {
        # Form1 (Complete Application) - for official transcripts
        'form1_services': [
            'Official Transcript',
            'offical transcrpit',
            'offical transcrpit 2',
        ],
        # Form2 (Simplified Application) - for other services
        'form2_services': [
            'Original Degree Certificate',
            'Student Copy Transcript', 
            'Temporary Certificate',
            'hermon',
        ]
    }
    
    # Base URLs
    base_url = 'http://localhost:8080/alumni-application'
    form1_url = f'{base_url}?form=form1'
    form2_url = f'{base_url}?form=form2'
    
    updated_count = 0
    
    print("🔄 Updating ServiceType URLs...")
    print("=" * 50)
    
    # Update Form1 services
    for service_name in url_mappings['form1_services']:
        try:
            service = ServiceType.objects.get(name=service_name)
            old_url = service.url
            service.url = form1_url
            service.save()
            updated_count += 1
            
            print(f"✅ Updated '{service_name}':")
            print(f"   Old URL: {old_url or 'None'}")
            print(f"   New URL: {form1_url}")
            print()
            
        except ServiceType.DoesNotExist:
            print(f"⚠️  Service '{service_name}' not found - skipping")
            print()
    
    # Update Form2 services
    for service_name in url_mappings['form2_services']:
        try:
            service = ServiceType.objects.get(name=service_name)
            old_url = service.url
            service.url = form2_url
            service.save()
            updated_count += 1
            
            print(f"✅ Updated '{service_name}':")
            print(f"   Old URL: {old_url or 'None'}")
            print(f"   New URL: {form2_url}")
            print()
            
        except ServiceType.DoesNotExist:
            print(f"⚠️  Service '{service_name}' not found - skipping")
            print()
    
    # Check for any services without URLs
    services_without_urls = ServiceType.objects.filter(url__isnull=True, is_active=True)
    if services_without_urls.exists():
        print("⚠️  Services without URLs (will default to Form2):")
        for service in services_without_urls:
            print(f"   - {service.name}")
            # Optionally set default URL
            service.url = form2_url
            service.save()
            updated_count += 1
            print(f"     → Set default URL: {form2_url}")
        print()
    
    print("=" * 50)
    print(f"🎉 Update complete! Updated {updated_count} service types.")
    
    # Display final summary
    print("\n📊 Final URL Summary:")
    print("-" * 30)
    
    form1_services = ServiceType.objects.filter(url__contains='form=form1', is_active=True)
    form2_services = ServiceType.objects.filter(url__contains='form=form2', is_active=True)
    other_services = ServiceType.objects.filter(is_active=True).exclude(
        url__contains='form=form1'
    ).exclude(url__contains='form=form2')
    
    print(f"Form1 (Complete Application): {form1_services.count()} services")
    for service in form1_services:
        print(f"  ✅ {service.name}")
    
    print(f"\nForm2 (Simplified Application): {form2_services.count()} services")
    for service in form2_services:
        print(f"  ✅ {service.name}")
    
    if other_services.exists():
        print(f"\nOther URLs: {other_services.count()} services")
        for service in other_services:
            print(f"  ⚠️  {service.name}: {service.url or 'No URL'}")

def main():
    """Main function."""
    print("🚀 ServiceType URL Update Script")
    print("This script will update ServiceType URLs to specify which form to use.")
    print()
    
    # Show current state
    total_services = ServiceType.objects.filter(is_active=True).count()
    services_with_urls = ServiceType.objects.filter(is_active=True, url__isnull=False).exclude(url='').count()
    
    print(f"📊 Current State:")
    print(f"   Total active services: {total_services}")
    print(f"   Services with URLs: {services_with_urls}")
    print(f"   Services without URLs: {total_services - services_with_urls}")
    print()
    
    # Confirm before proceeding
    response = input("Do you want to proceed with updating URLs? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Update cancelled.")
        return
    
    # Run the update
    update_service_type_urls()

if __name__ == '__main__':
    main()
