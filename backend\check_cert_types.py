#!/usr/bin/env python
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.certificate_type.models import CertificateType

print('Certificate Types in database:')
cert_types = CertificateType.objects.all()
for ct in cert_types:
    print(f'- {ct.name} (ID: {ct.id}, Active: {ct.is_active})')

print(f'\nTotal: {cert_types.count()}')
print(f'Active: {CertificateType.objects.filter(is_active=True).count()}')

# Also check if we can create a simple service type
from setups.service_type.models import ServiceType
from decimal import Decimal

print('\nTesting service type creation...')
try:
    # Try to create a simple service type without document types
    test_service = ServiceType.objects.create(
        name='Test API Service',
        fee=Decimal('10.00'),
        is_active=True
    )
    print(f'✅ Successfully created test service: {test_service.name}')
    
    # Clean up
    test_service.delete()
    print('✅ Test service cleaned up')
    
except Exception as e:
    print(f'❌ Error creating test service: {e}')
