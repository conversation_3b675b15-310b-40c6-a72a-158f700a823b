import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import {
  Download,
  Plus,
  Edit,
  Trash2,
  FileText,
  Upload,
  Eye,
  Calendar,
  HardDrive,
  Search,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { downloadableAPI, Downloadable, DownloadableCreate, DownloadableUpdate } from '@/services/downloadableAPI';
import { cn } from '@/lib/utils';

const DownloadableManagement = () => {
  const [downloadables, setDownloadables] = useState<Downloadable[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingDownloadable, setEditingDownloadable] = useState<Downloadable | null>(null);
  const [formData, setFormData] = useState<DownloadableCreate>({
    title: '',
    file: null as any,
    is_active: true
  });

  useEffect(() => {
    fetchDownloadables();
  }, []);

  const fetchDownloadables = async () => {
    try {
      setLoading(true);
      const response = await downloadableAPI.getDownloadables();
      setDownloadables(response.results);
    } catch (error) {
      console.error('Error fetching downloadables:', error);
      toast.error('Failed to fetch downloadable files');
    } finally {
      setLoading(false);
    }
  };

  const validateFileSize = (file: File): boolean => {
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      toast.error('File size cannot exceed 2MB');
      return false;
    }
    return true;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file && !validateFileSize(file)) {
      // Clear the file input if validation fails
      e.target.value = '';
      return;
    }
    setFormData({ ...formData, file });
  };

  const handleCreate = async () => {
    if (!formData.title || !formData.file) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (!validateFileSize(formData.file)) {
      return;
    }

    try {
      await downloadableAPI.createDownloadable(formData);
      downloadableAPI.clearCache(); // Clear cache after creating
      toast.success('File uploaded successfully');
      setIsAddDialogOpen(false);
      setFormData({ title: '', file: null as any, is_active: true });
      fetchDownloadables();
    } catch (error: any) {
      console.error('Error creating downloadable:', error);
      if (error.response?.data?.file?.[0]) {
        toast.error(error.response.data.file[0]);
      } else {
        toast.error('Failed to upload file');
      }
    }
  };

  const handleUpdate = async () => {
    if (!editingDownloadable || !formData.title) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.file && !validateFileSize(formData.file)) {
      return;
    }

    try {
      const updateData: DownloadableUpdate = {
        title: formData.title,
        is_active: formData.is_active
      };

      if (formData.file) {
        updateData.file = formData.file;
      }

      await downloadableAPI.updateDownloadable(editingDownloadable.id, updateData);
      downloadableAPI.clearCache(); // Clear cache after updating
      toast.success('File updated successfully');
      setIsEditDialogOpen(false);
      setEditingDownloadable(null);
      setFormData({ title: '', file: null as any, is_active: true });
      fetchDownloadables();
    } catch (error: any) {
      console.error('Error updating downloadable:', error);
      if (error.response?.data?.file?.[0]) {
        toast.error(error.response.data.file[0]);
      } else {
        toast.error('Failed to update file');
      }
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this file?')) {
      return;
    }

    try {
      await downloadableAPI.deleteDownloadable(id);
      downloadableAPI.clearCache(); // Clear cache after deleting
      toast.success('File deleted successfully');
      fetchDownloadables();
    } catch (error) {
      console.error('Error deleting downloadable:', error);
      toast.error('Failed to delete file');
    }
  };

  const handleDownload = async (id: string) => {
    try {
      await downloadableAPI.downloadFile(id);
      toast.success('File downloaded successfully');
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Failed to download file');
    }
  };

  const handleView = async (id: string) => {
    try {
      await downloadableAPI.viewFile(id);
      toast.success('Opening file');
    } catch (error) {
      console.error('Error viewing file:', error);
      toast.error('Failed to open file');
    }
  };

  const openEditDialog = (downloadable: Downloadable) => {
    setEditingDownloadable(downloadable);
    setFormData({
      title: downloadable.title,
      file: null as any,
      is_active: downloadable.is_active
    });
    setIsEditDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({ title: '', file: null as any, is_active: true });
    setEditingDownloadable(null);
  };

  // Filter downloadables based on search and status
  const filteredDownloadables = downloadables.filter(downloadable => {
    const matchesSearch = downloadable.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (downloadable.file_name && downloadable.file_name.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'all' ||
                         (statusFilter === 'active' && downloadable.is_active) ||
                         (statusFilter === 'inactive' && !downloadable.is_active);

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDownloadables.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredDownloadables.length / itemsPerPage);

  // Reset to first page when search term or status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Download className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Downloadable Files</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete downloadable files for users
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm" onClick={resetForm}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add File
                  </Button>
                </DialogTrigger>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search files by title or filename..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-blue-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40 border-blue-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active Only</SelectItem>
                  <SelectItem value="inactive">Inactive Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters Display */}
          {(searchTerm || statusFilter !== 'all') && (
            <div className="mb-4">
              <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="text-sm font-medium text-[#1a73c0]">Active Filters:</span>

                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Search: {searchTerm}
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove search filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                {statusFilter !== 'all' && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                    <button
                      onClick={() => setStatusFilter('all')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove status filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                  }}
                  className="text-xs h-7 px-3 ml-auto border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Clear All
                </Button>
              </div>
            </div>
          )}

          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[25%] text-[#1a73c0] font-medium">Title</TableHead>
                    <TableHead className="w-[20%] text-[#1a73c0] font-medium">File</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">Size</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">Status</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">Created</TableHead>
                    <TableHead className="w-[10%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-blue-100 p-3 rounded-full">
                            <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                          </div>
                          <div className="text-[#1a73c0] font-medium">Loading downloadable files...</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredDownloadables.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <Download className="h-8 w-8 text-gray-500" />
                          </div>
                          <div className="text-gray-700 font-medium">No downloadable files found</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {searchTerm ?
                              'Try adjusting your search criteria to find what you\'re looking for.' :
                              'There are no downloadable files available. Click the "Add File" button to create one.'}
                          </div>
                          {(searchTerm || statusFilter !== 'all') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSearchTerm('');
                                setStatusFilter('all');
                              }}
                              className="mt-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                            >
                              Clear Filters
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentItems.map((downloadable) => (
                      <TableRow key={downloadable.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{downloadable.title}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-gray-500" />
                            <span className="text-sm text-gray-600">
                              {downloadable.file_name || 'Unknown'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <HardDrive className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">
                              {downloadableAPI.formatFileSize(downloadable.file_size)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {downloadable.is_active ? (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200 shadow-sm">
                                <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-green-600"></span>
                                Active
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200 shadow-sm">
                                <span className="w-1.5 h-1.5 rounded-full mr-1.5 bg-red-600"></span>
                                Inactive
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1 text-blue-500" />
                            {new Date(downloadable.created_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            })}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {downloadableAPI.isViewableFile(downloadable.file_name || '') && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleView(downloadable.id)}
                                className="h-8 w-8 p-0 border-green-500 text-green-600 hover:bg-green-500 hover:text-white hover:border-green-500 transition-all duration-200"
                                title="View file"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDownload(downloadable.id)}
                              className="h-8 w-8 p-0 border-blue-500 text-blue-600 hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-all duration-200"
                              title="Download file"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(downloadable)}
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                              title="Edit file"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(downloadable.id)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete file"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {filteredDownloadables.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredDownloadables.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredDownloadables.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Add File Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Add New Downloadable File</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Upload a new file for users to download. Provide a clear title and ensure the file is under 2MB.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="p-6 space-y-8">
            {/* File Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-4 w-4 text-[#1a73c0]" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">File Information</h3>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="title" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    File Title
                  </Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Enter file title..."
                    className="h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500">Enter a descriptive title for the downloadable file</p>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="file" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    File Upload (Max 2MB)
                  </Label>
                  <Input
                    id="file"
                    type="file"
                    onChange={handleFileChange}
                    accept="*/*"
                    className="h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500">Maximum file size: 2MB. All file types are supported.</p>
                </div>
              </div>
            </div>

            {/* Status Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                      className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                    />
                    <Label htmlFor="is_active" className="text-base font-semibold text-gray-700">
                      File Status
                    </Label>
                  </div>
                  <span className={cn(
                    "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                    formData.is_active
                      ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                      : "bg-gray-100 text-gray-800 border border-gray-200"
                  )}>
                    {formData.is_active ? "✓ Active" : "○ Inactive"}
                  </span>
                </div>

                <div className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  formData.is_active
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                )}>
                  <div className="flex items-start space-x-3">
                    {formData.is_active ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={cn(
                        "text-sm font-medium mb-1",
                        formData.is_active ? "text-green-800" : "text-gray-700"
                      )}>
                        {formData.is_active ? "File is Active" : "File is Inactive"}
                      </p>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {formData.is_active
                          ? "This file will be available for users to download."
                          : "This file will be hidden and unavailable for download."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="text-xs text-gray-500">
                <span className="text-red-500">*</span> Required fields
              </div>
              <div className="flex space-x-3">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                    onClick={resetForm}
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={handleCreate}
                  className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload File
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Edit className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Edit Downloadable File</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Update the file information. Make sure to review all fields before saving changes.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="p-6 space-y-8">
            {/* File Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-4 w-4 text-[#1a73c0]" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">File Information</h3>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="edit-title" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    File Title
                  </Label>
                  <Input
                    id="edit-title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Enter file title..."
                    className="h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500">Enter a descriptive title for the downloadable file</p>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="edit-file" className="text-sm font-semibold text-gray-700">
                    Replace File (optional, Max 2MB)
                  </Label>
                  <Input
                    id="edit-file"
                    type="file"
                    onChange={handleFileChange}
                    accept="*/*"
                    className="h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500">Maximum file size: 2MB. Leave empty to keep the current file.</p>
                </div>
              </div>
            </div>

            {/* Status Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-purple-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Status Settings</h3>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Switch
                      id="edit-is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                      className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                    />
                    <Label htmlFor="edit-is_active" className="text-base font-semibold text-gray-700">
                      File Status
                    </Label>
                  </div>
                  <span className={cn(
                    "text-sm font-medium px-3 py-1.5 rounded-full transition-all duration-200",
                    formData.is_active
                      ? "bg-green-100 text-green-800 border border-green-200 shadow-sm"
                      : "bg-gray-100 text-gray-800 border border-gray-200"
                  )}>
                    {formData.is_active ? "✓ Active" : "○ Inactive"}
                  </span>
                </div>

                <div className={cn(
                  "p-4 rounded-lg border transition-all duration-200",
                  formData.is_active
                    ? "bg-green-50 border-green-200"
                    : "bg-gray-50 border-gray-200"
                )}>
                  <div className="flex items-start space-x-3">
                    {formData.is_active ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p className={cn(
                        "text-sm font-medium mb-1",
                        formData.is_active ? "text-green-800" : "text-gray-700"
                      )}>
                        {formData.is_active ? "File is Active" : "File is Inactive"}
                      </p>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {formData.is_active
                          ? "This file will be available for users to download."
                          : "This file will be hidden and unavailable for download."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="text-xs text-gray-500">
                <span className="text-red-500">*</span> Required fields
              </div>
              <div className="flex space-x-3">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                    onClick={resetForm}
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={handleUpdate}
                  className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 px-6 shadow-sm"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Update File
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DownloadableManagement;
