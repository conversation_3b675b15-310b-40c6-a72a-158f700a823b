from rest_framework import serializers
from .models import CertificateType

class CertificateTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CertificateType
        fields = ['uuid', 'name', 'description', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['uuid', 'created_at', 'updated_at']

    def validate_name(self, value):
        """Validate certificate type name."""
        if not value or not value.strip():
            raise serializers.ValidationError("Certificate type name is required.")
        
        # Clean the name
        value = value.strip()
        
        if len(value) < 2:
            raise serializers.ValidationError("Certificate type name must be at least 2 characters long.")
        
        # Check for duplicate names (case-insensitive)
        existing = CertificateType.objects.filter(
            name__iexact=value
        ).exclude(pk=self.instance.pk if self.instance else None)
        
        if existing.exists():
            raise serializers.ValidationError("A certificate type with this name already exists.")
        
        return value

    def validate(self, data):
        """Perform object-level validation."""
        # Additional validation can be added here if needed
        return data
