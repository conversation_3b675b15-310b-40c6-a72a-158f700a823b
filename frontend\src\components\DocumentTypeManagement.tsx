import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  ToggleLeft,
  ToggleRight,
  FileText,
  Filter,
  RefreshCw,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  Settings,
  Pencil,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { documentTypeAPI } from '@/services/api';
import { cn } from '@/lib/utils';

interface DocumentType {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FormData {
  name: string;
  description: string;
  is_active: boolean;
}

const DocumentTypeManagement: React.FC = () => {
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingDocumentType, setEditingDocumentType] = useState<DocumentType | null>(null);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    is_active: true,
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    description: '',
    general: '',
  });

  // Fetch document types
  const fetchDocumentTypes = async () => {
    try {
      setLoading(true);
      const params: any = {};

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.is_active = statusFilter === 'active';
      }

      // Use searchDocumentTypes if we have search/filter params, otherwise getAllDocumentTypes
      const response = Object.keys(params).length > 0
        ? await documentTypeAPI.searchDocumentTypes(params)
        : await documentTypeAPI.getAllDocumentTypes();

      setDocumentTypes(response.data);
    } catch (error) {
      console.error('Error fetching document types:', error);
      toast.error('Failed to fetch document types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocumentTypes();
  }, [searchTerm, statusFilter]);

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      is_active: true,
    });
    setFormErrors({
      name: '',
      description: '',
      general: '',
    });
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors = {
      name: '',
      description: '',
      general: '',
    };

    // Validate name
    if (!formData.name.trim()) {
      errors.name = 'Document type name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Document type name must be at least 2 characters long';
    } else if (formData.name.trim().length > 100) {
      errors.name = 'Document type name cannot exceed 100 characters';
    }

    // Check for duplicate names (case-insensitive)
    const existingDocumentType = documentTypes.find(
      dt => dt.name.toLowerCase() === formData.name.trim().toLowerCase() &&
           dt.id !== editingDocumentType?.id
    );
    if (existingDocumentType) {
      errors.name = 'A document type with this name already exists';
    }

    setFormErrors(errors);
    return !errors.name && !errors.description && !errors.general;
  };

  // Handle create
  const handleCreate = async () => {
    if (!validateForm()) return;

    setSubmitting(true);
    try {
      const cleanData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        is_active: formData.is_active,
      };

      await documentTypeAPI.createDocumentType(cleanData);

      toast.success('Document type created successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchDocumentTypes();
    } catch (error: any) {
      console.error('Error creating document type:', error);
      if (error.response?.data) {
        const errorData = error.response.data;

        if (errorData.name) {
          setFormErrors(prev => ({ ...prev, name: Array.isArray(errorData.name) ? errorData.name[0] : errorData.name }));
        } else if (errorData.detail) {
          setFormErrors(prev => ({ ...prev, general: errorData.detail }));
        } else {
          const errorMessage = typeof errorData === 'string' ? errorData : JSON.stringify(errorData);
          setFormErrors(prev => ({ ...prev, general: errorMessage }));
          toast.error('Failed to create document type: ' + errorMessage);
        }
      } else {
        toast.error('Failed to create document type: Network error');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit
  const handleEdit = (documentType: DocumentType) => {
    setEditingDocumentType(documentType);

    setFormData({
      name: documentType.name,
      description: documentType.description || '',
      is_active: documentType.is_active,
    });

    // Reset form errors
    setFormErrors({
      name: '',
      description: '',
      general: '',
    });
    setIsEditDialogOpen(true);
  };

  // Handle update
  const handleUpdate = async () => {
    if (!validateForm() || !editingDocumentType) return;

    setSubmitting(true);
    try {
      const cleanData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        is_active: formData.is_active,
      };

      await documentTypeAPI.updateDocumentType(editingDocumentType.id, cleanData);
      toast.success('Document type updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      setEditingDocumentType(null);
      fetchDocumentTypes();
    } catch (error: any) {
      console.error('Error updating document type:', error);
      if (error.response?.data) {
        const errorData = error.response.data;
        if (errorData.name) {
          setFormErrors(prev => ({ ...prev, name: errorData.name[0] }));
        } else if (errorData.detail) {
          setFormErrors(prev => ({ ...prev, general: errorData.detail }));
        } else {
          toast.error('Failed to update document type');
        }
      } else {
        toast.error('Failed to update document type');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async (documentType: DocumentType) => {
    try {
      await documentTypeAPI.deleteDocumentType(documentType.id);
      toast.success('Document type deleted successfully');
      fetchDocumentTypes();
    } catch (error) {
      console.error('Error deleting document type:', error);
      toast.error('Failed to delete document type');
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (documentType: DocumentType) => {
    try {
      await documentTypeAPI.updateDocumentType(documentType.id, {
        ...documentType,
        is_active: !documentType.is_active
      });
      toast.success(`Document type ${documentType.is_active ? 'deactivated' : 'activated'} successfully`);
      fetchDocumentTypes();
    } catch (error) {
      console.error('Error toggling document type status:', error);
      toast.error('Failed to toggle document type status');
    }
  };

  // Filter document types based on search term and status
  const filteredDocumentTypes = documentTypes.filter((documentType) => {
    const matchesSearch = documentType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (documentType.description && documentType.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'active' && documentType.is_active) ||
      (statusFilter === 'inactive' && !documentType.is_active);

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDocumentTypes.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredDocumentTypes.length / itemsPerPage);

  // Reset to first page when search term or status filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Document Type Management</CardTitle>
                <CardDescription className="mt-1">
                  Create, view, update, and delete document types required for various services
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
                setIsAddDialogOpen(open);
                if (open) {
                  resetForm();
                }
              }}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Document Type
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                        <Plus className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <DialogTitle className="text-xl font-semibold text-gray-900">Add New Document Type</DialogTitle>
                        <DialogDescription className="text-gray-600 mt-1">
                          Create a new document type for service requirements
                        </DialogDescription>
                      </div>
                    </div>
                  </DialogHeader>
                  <div className="p-6 space-y-6">
                    {formErrors.general && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center">
                          <XCircle className="h-5 w-5 text-red-500 mr-2" />
                          <p className="text-sm text-red-700">{formErrors.general}</p>
                        </div>
                      </div>
                    )}

                    <div className="space-y-4">
                      <div className="space-y-3">
                        <Label htmlFor="add-name" className="text-sm font-semibold text-gray-700 flex items-center">
                          <span className="text-red-500 mr-1">*</span>
                          Document Type Name
                        </Label>
                        <Input
                          id="add-name"
                          name="name"
                          placeholder="Birth Certificate, Passport, ID Card..."
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          className={cn(
                            "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                            formErrors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                          )}
                        />
                        {formErrors.name && (
                          <p className="text-sm text-red-600 flex items-center">
                            <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            {formErrors.name}
                          </p>
                        )}
                      </div>

                      <div className="space-y-3">
                        <Label htmlFor="add-description" className="text-sm font-semibold text-gray-700">
                          Description
                        </Label>
                        <Textarea
                          id="add-description"
                          name="description"
                          placeholder="Describe the document type and its purpose..."
                          value={formData.description}
                          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                          className="min-h-[100px] border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                        />
                      </div>

                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-gray-700">
                          Status
                        </Label>
                        <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border">
                          <Switch
                            id="add-status"
                            checked={formData.is_active}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                            className="data-[state=checked]:bg-[#1a73c0]"
                          />
                          <div className="flex-1">
                            <Label htmlFor="add-status" className="text-sm font-medium cursor-pointer">
                              {formData.is_active ? 'Active' : 'Inactive'}
                            </Label>
                            <p className="text-xs text-gray-500 mt-1">
                              {formData.is_active
                                ? 'Document type is available for use in services'
                                : 'Document type is hidden and not available for use'
                              }
                            </p>
                          </div>
                          <div className="flex items-center">
                            {formData.is_active ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : (
                              <XCircle className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <DialogFooter className="px-6 py-4 bg-gray-50 border-t">
                    <Button
                      variant="outline"
                      onClick={() => setIsAddDialogOpen(false)}
                      className="border-gray-300 text-gray-700 hover:bg-gray-100"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleCreate}
                      disabled={submitting}
                      className="bg-[#1a73c0] hover:bg-blue-700 text-white"
                    >
                      {submitting ? 'Creating...' : 'Create Document Type'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search document types..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0]"
                />
              </div>
            </div>
            <div className="flex gap-3">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[140px] h-10 border-gray-300">
                  <Filter className="h-4 w-4 mr-2 text-gray-500" />
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                onClick={fetchDocumentTypes}
                className="h-10 px-4 border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Loading State */}
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0]"></div>
            </div>
          ) : (
            <>
              {/* Document Types Table */}
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-semibold text-gray-900">Name</TableHead>
                      <TableHead className="font-semibold text-gray-900">Description</TableHead>
                      <TableHead className="font-semibold text-gray-900">Status</TableHead>
                      <TableHead className="font-semibold text-gray-900">Created</TableHead>
                      <TableHead className="font-semibold text-gray-900 text-center">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentItems.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                          {searchTerm || statusFilter !== 'all' ? 'No document types found matching your criteria.' : 'No document types available.'}
                        </TableCell>
                      </TableRow>
                    ) : (
                      currentItems.map((documentType) => (
                        <TableRow key={documentType.id} className="hover:bg-gray-50">
                          <TableCell className="font-medium">{documentType.name}</TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate" title={documentType.description}>
                              {documentType.description || 'No description'}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={documentType.is_active ? "default" : "secondary"}
                              className={cn(
                                "font-medium",
                                documentType.is_active
                                  ? "bg-green-100 text-green-800 hover:bg-green-200"
                                  : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                              )}
                            >
                              {documentType.is_active ? (
                                <>
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Active
                                </>
                              ) : (
                                <>
                                  <XCircle className="h-3 w-3 mr-1" />
                                  Inactive
                                </>
                              )}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-gray-600">
                            {new Date(documentType.created_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center justify-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEdit(documentType)}
                                className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-[#1a73c0]"
                                title="Edit document type"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleToggleStatus(documentType)}
                                className={cn(
                                  "h-8 w-8 p-0",
                                  documentType.is_active
                                    ? "hover:bg-orange-50 hover:text-orange-600"
                                    : "hover:bg-green-50 hover:text-green-600"
                                )}
                                title={documentType.is_active ? "Deactivate document type" : "Activate document type"}
                              >
                                {documentType.is_active ? (
                                  <ToggleRight className="h-4 w-4" />
                                ) : (
                                  <ToggleLeft className="h-4 w-4" />
                                )}
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                                    title="Delete document type"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Delete Document Type</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to delete "{documentType.name}"? This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDelete(documentType)}
                                      className="bg-red-600 hover:bg-red-700"
                                    >
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </>
          )}
        </CardContent>

        <CardFooter>
          {filteredDocumentTypes.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">
                  of {filteredDocumentTypes.length} document types
                </span>
              </div>

              <div className="flex items-center bg-white rounded-md border border-blue-200 shadow-sm overflow-hidden">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="h-9 px-3 rounded-none border-r border-blue-200 bg-white text-gray-700 hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="First Page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="h-9 px-3 rounded-none border-r border-blue-200 bg-white text-gray-700 hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Previous Page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Dynamic page number buttons with ellipsis for large page counts */}
                {totalPages <= 7 ? (
                  // If we have 7 or fewer pages, show all page numbers
                  Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                    <Button
                      key={number}
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePageChange(number)}
                      className={cn(
                        "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                        currentPage === number
                          ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                          : "bg-white text-gray-700 hover:bg-blue-50"
                      )}
                    >
                      {number}
                    </Button>
                  ))
                ) : (
                  <>
                    {/* Always show first page */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePageChange(1)}
                      className={cn(
                        "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                        currentPage === 1
                          ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                          : "bg-white text-gray-700 hover:bg-blue-50"
                      )}
                    >
                      1
                    </Button>

                    {/* Show ellipsis if not showing first few pages */}
                    {currentPage > 3 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled
                        className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                      >
                        ...
                      </Button>
                    )}

                    {/* Show current page and adjacent pages */}
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(number =>
                        number > 1 &&
                        number < totalPages &&
                        (
                          number === currentPage - 1 ||
                          number === currentPage ||
                          number === currentPage + 1 ||
                          (currentPage <= 3 && number <= 4) ||
                          (currentPage >= totalPages - 2 && number >= totalPages - 3)
                        )
                      )
                      .map(number => (
                        <Button
                          key={number}
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(number)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === number
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {number}
                        </Button>
                      ))
                    }

                    {/* Show ellipsis if not showing last few pages */}
                    {currentPage < totalPages - 2 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled
                        className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                      >
                        ...
                      </Button>
                    )}

                    {/* Always show last page */}
                    {totalPages > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(totalPages)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === totalPages
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {totalPages}
                      </Button>
                    )}
                  </>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="h-9 px-3 rounded-none border-r border-blue-200 bg-white text-gray-700 hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Next Page"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="h-9 px-3 rounded-none bg-white text-gray-700 hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Last Page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="text-sm text-[#1a73c0] font-medium bg-white px-3 py-2 rounded-md border border-blue-200 shadow-sm">
                Page {currentPage} of {totalPages}
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        if (!open) {
          setEditingDocumentType(null);
          resetForm();
        }
      }}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Edit className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">Edit Document Type</DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  Update the document type information
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6 space-y-6">
            {formErrors.general && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  <p className="text-sm text-red-700">{formErrors.general}</p>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div className="space-y-3">
                <Label htmlFor="edit-name" className="text-sm font-semibold text-gray-700 flex items-center">
                  <span className="text-red-500 mr-1">*</span>
                  Document Type Name
                </Label>
                <Input
                  id="edit-name"
                  name="name"
                  placeholder="Birth Certificate, Passport, ID Card..."
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={cn(
                    "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                    formErrors.name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                  )}
                />
                {formErrors.name && (
                  <p className="text-sm text-red-600 flex items-center">
                    <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {formErrors.name}
                  </p>
                )}
              </div>

              <div className="space-y-3">
                <Label htmlFor="edit-description" className="text-sm font-semibold text-gray-700">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  placeholder="Describe the document type and its purpose..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="min-h-[100px] border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200"
                />
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700">
                  Status
                </Label>
                <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border">
                  <Switch
                    id="edit-status"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                    className="data-[state=checked]:bg-[#1a73c0]"
                  />
                  <div className="flex-1">
                    <Label htmlFor="edit-status" className="text-sm font-medium cursor-pointer">
                      {formData.is_active ? 'Active' : 'Inactive'}
                    </Label>
                    <p className="text-xs text-gray-500 mt-1">
                      {formData.is_active
                        ? 'Document type is available for use in services'
                        : 'Document type is hidden and not available for use'
                      }
                    </p>
                  </div>
                  <div className="flex items-center">
                    {formData.is_active ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="px-6 py-4 bg-gray-50 border-t">
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="border-gray-300 text-gray-700 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdate}
              disabled={submitting}
              className="bg-[#1a73c0] hover:bg-blue-700 text-white"
            >
              {submitting ? 'Updating...' : 'Update Document Type'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DocumentTypeManagement;
