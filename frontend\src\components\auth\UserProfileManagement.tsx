import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  User, 
  Users, 
  Edit, 
  Save,
  Upload,
  Camera,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Briefcase,
  Shield,
  Settings,
  Eye,
  EyeOff,
  RefreshCw,
  Search,
  Filter,
  Download,
  FileText,
  Lock,
  Unlock
} from 'lucide-react';
import { toast } from 'sonner';
import { userAPI, profileAPI } from '@/services/authAPI';
import { User as UserType, UserProfile } from '@/types/auth';

const UserProfileManagement: React.FC = () => {
  const [users, setUsers] = useState<UserType[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const [showProfileDialog, setShowProfileDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  // Profile form state
  const [profileForm, setProfileForm] = useState<Partial<UserProfile>>({});

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getUsers();
      setUsers(response.data.results);
    } catch (error) {
      toast.error('Failed to load users');
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserProfile = async (userId: number) => {
    try {
      setProfileLoading(true);
      const response = await profileAPI.getProfile(userId);
      setUserProfile(response.data);
      setProfileForm(response.data);
    } catch (error) {
      // Profile might not exist, create empty form
      const emptyProfile: Partial<UserProfile> = {
        user: userId,
        department: '',
        phone: '',
        employee_id: '',
        bio: '',
        address: '',
        position: '',
      };
      setUserProfile(null);
      setProfileForm(emptyProfile);
      console.log('Profile not found, will create new one');
    } finally {
      setProfileLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!selectedUser) return;

    try {
      if (userProfile) {
        // Update existing profile
        await profileAPI.updateProfile(selectedUser.id, profileForm);
        toast.success('Profile updated successfully');
      } else {
        // Create new profile - this would need a create endpoint
        toast.info('Profile creation not implemented yet');
      }
      
      setEditMode(false);
      loadUserProfile(selectedUser.id);
    } catch (error) {
      toast.error('Failed to save profile');
      console.error('Error saving profile:', error);
    }
  };

  const handleAvatarUpload = async () => {
    if (!selectedUser || !avatarFile) return;

    try {
      await profileAPI.uploadAvatar(selectedUser.id, avatarFile);
      toast.success('Avatar uploaded successfully');
      setAvatarFile(null);
      loadUserProfile(selectedUser.id);
    } catch (error) {
      toast.error('Failed to upload avatar');
      console.error('Error uploading avatar:', error);
    }
  };

  const openProfileDialog = async (user: UserType) => {
    setSelectedUser(user);
    setShowProfileDialog(true);
    setEditMode(false);
    await loadUserProfile(user.id);
  };

  const getUserInitials = (user: UserType) => {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  const getDepartments = () => {
    const departments = users
      .map(user => user.profile?.department)
      .filter(Boolean)
      .filter((dept, index, arr) => arr.indexOf(dept) === index);
    return departments as string[];
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = !filterDepartment || user.profile?.department === filterDepartment;
    
    return matchesSearch && matchesDepartment;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">User Profile Management</h1>
          <p className="text-muted-foreground">
            Manage detailed user profiles and personal information
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadUsers}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Profiles
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search Users</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name, username, or email..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="department-filter">Filter by Department</Label>
              <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                <SelectTrigger>
                  <SelectValue placeholder="All departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All departments</SelectItem>
                  {getDepartments().map(dept => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setFilterDepartment('');
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>User Profiles ({filteredUsers.length})</span>
          </CardTitle>
          <CardDescription>
            Click on a user to view and edit their detailed profile
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading users...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="text-muted-foreground">
                        <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No users found</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id} className="cursor-pointer hover:bg-muted/50">
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.profile?.avatar} />
                            <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {user.first_name} {user.last_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              @{user.username}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.profile?.department || (
                          <span className="text-muted-foreground">Not set</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {user.profile?.position || (
                          <span className="text-muted-foreground">Not set</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1 text-sm">
                            <Mail className="h-3 w-3" />
                            <span>{user.email}</span>
                          </div>
                          {user.profile?.phone && (
                            <div className="flex items-center space-x-1 text-sm">
                              <Phone className="h-3 w-3" />
                              <span>{user.profile.phone}</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.is_superuser ? (
                          <Badge variant="default">Superuser</Badge>
                        ) : user.is_staff ? (
                          <Badge variant="secondary">Staff</Badge>
                        ) : (
                          <Badge variant="outline">User</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openProfileDialog(user)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Profile
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* User Profile Dialog */}
      <Dialog open={showProfileDialog} onOpenChange={setShowProfileDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>User Profile - {selectedUser?.username}</span>
            </DialogTitle>
            <DialogDescription>
              View and edit detailed user profile information
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-6">
              {/* Profile Header */}
              <div className="flex items-start space-x-6 p-6 bg-muted/50 rounded-lg">
                <div className="flex flex-col items-center space-y-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={userProfile?.avatar} />
                    <AvatarFallback className="text-lg">
                      {getUserInitials(selectedUser)}
                    </AvatarFallback>
                  </Avatar>

                  {editMode && (
                    <div className="space-y-2">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => setAvatarFile(e.target.files?.[0] || null)}
                        className="text-xs"
                      />
                      {avatarFile && (
                        <Button size="sm" onClick={handleAvatarUpload}>
                          <Upload className="h-3 w-3 mr-1" />
                          Upload
                        </Button>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex-1 space-y-2">
                  <h3 className="text-2xl font-bold">
                    {selectedUser.first_name} {selectedUser.last_name}
                  </h3>
                  <p className="text-muted-foreground">@{selectedUser.username}</p>

                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Mail className="h-4 w-4" />
                      <span>{selectedUser.email}</span>
                    </div>
                    {userProfile?.phone && (
                      <div className="flex items-center space-x-1">
                        <Phone className="h-4 w-4" />
                        <span>{userProfile.phone}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {selectedUser.is_superuser && <Badge variant="default">Superuser</Badge>}
                    {selectedUser.is_staff && <Badge variant="secondary">Staff</Badge>}
                    <Badge variant={selectedUser.is_active ? "outline" : "destructive"}>
                      {selectedUser.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>

                <div className="flex space-x-2">
                  {editMode ? (
                    <>
                      <Button onClick={handleSaveProfile}>
                        <Save className="h-4 w-4 mr-2" />
                        Save
                      </Button>
                      <Button variant="outline" onClick={() => setEditMode(false)}>
                        Cancel
                      </Button>
                    </>
                  ) : (
                    <Button onClick={() => setEditMode(true)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  )}
                </div>
              </div>

              {/* Profile Details */}
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-5 w-5" />
                      <span>Personal Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="employee-id">Employee ID</Label>
                      <Input
                        id="employee-id"
                        value={profileForm.employee_id || ''}
                        onChange={(e) => setProfileForm({ ...profileForm, employee_id: e.target.value })}
                        disabled={!editMode}
                      />
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={profileForm.phone || ''}
                        onChange={(e) => setProfileForm({ ...profileForm, phone: e.target.value })}
                        disabled={!editMode}
                      />
                    </div>

                    <div>
                      <Label htmlFor="department">Department</Label>
                      <Input
                        id="department"
                        value={profileForm.department || ''}
                        onChange={(e) => setProfileForm({ ...profileForm, department: e.target.value })}
                        disabled={!editMode}
                      />
                    </div>

                    <div>
                      <Label htmlFor="position">Position</Label>
                      <Input
                        id="position"
                        value={profileForm.position || ''}
                        onChange={(e) => setProfileForm({ ...profileForm, position: e.target.value })}
                        disabled={!editMode}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Settings className="h-5 w-5" />
                      <span>Account Details</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Date Joined</Label>
                      <Input value={formatDate(selectedUser.date_joined)} disabled />
                    </div>

                    <div>
                      <Label>Last Login</Label>
                      <Input value={formatDate(selectedUser.last_login)} disabled />
                    </div>

                    <div>
                      <Label>Groups</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {selectedUser.groups.map((group) => (
                          <Badge key={group.id} variant="outline" className="text-xs">
                            {group.name}
                          </Badge>
                        ))}
                        {selectedUser.groups.length === 0 && (
                          <span className="text-muted-foreground text-sm">No groups</span>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label>Permissions</Label>
                      <div className="text-sm text-muted-foreground">
                        {selectedUser.user_permissions.length} direct permissions
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Bio Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Bio</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={profileForm.bio || ''}
                    onChange={(e) => setProfileForm({ ...profileForm, bio: e.target.value })}
                    disabled={!editMode}
                    rows={4}
                    placeholder="Tell us about yourself..."
                  />
                </CardContent>
              </Card>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowProfileDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserProfileManagement;
