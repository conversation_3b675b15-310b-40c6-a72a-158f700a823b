# Environment Variables Setup Guide

This document explains how to configure environment variables for both the backend (Django) and frontend (React/Vite) applications.

## Backend Environment Variables (.env)

Create a `.env` file in the `backend/` directory with the following variables:

### Django Configuration
- `SECRET_KEY`: Django secret key for cryptographic signing
- `DEBUG`: Enable/disable debug mode (True/False)
- `ALLOWED_HOSTS`: Comma-separated list of allowed hosts (* for all)

### Database Configuration
- `DB_ENGINE`: Database engine (default: django.db.backends.postgresql)
- `DB_NAME`: Database name
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `DB_HOST`: Database host
- `DB_PORT`: Database port

### CORS Configuration
- `CORS_ALLOWED_ORIGINS`: Comma-separated list of allowed origins
- `CORS_ALLOW_ALL_ORIGINS`: Allow all origins (True/False)

### JWT Configuration
- `JWT_ACCESS_TOKEN_LIFETIME_HOURS`: Access token lifetime in hours
- `JWT_REFRESH_TOKEN_LIFETIME_DAYS`: Refresh token lifetime in days

### Payment Gateway Configuration
- `PAYMENT_BASE_URL`: Payment gateway base URL
- `FABRIC_APP_ID`: Fabric application ID
- `APP_SECRET`: Application secret
- `MERCHANT_APP_ID`: Merchant application ID
- `MERCHANT_CODE`: Merchant code
- `PRIVATE_KEY`: Private key for payment gateway

### Email Configuration
- `EMAIL_BACKEND`: Email backend (default: django.core.mail.backends.console.EmailBackend)
- `EMAIL_HOST`: SMTP server host (e.g., smtp.gmail.com)
- `EMAIL_PORT`: SMTP server port (e.g., 587 for TLS, 465 for SSL)
- `EMAIL_USE_TLS`: Use TLS encryption (True/False)
- `EMAIL_USE_SSL`: Use SSL encryption (True/False)
- `EMAIL_HOST_USER`: SMTP username/email
- `EMAIL_HOST_PASSWORD`: SMTP password or app password
- `DEFAULT_FROM_EMAIL`: Default sender email address
- `SERVER_EMAIL`: Server error email address

### Security Configuration
- `SESSION_COOKIE_SECURE`: Secure session cookies (True/False)
- `CSRF_COOKIE_SECURE`: Secure CSRF cookies (True/False)
- `SECURE_SSL_REDIRECT`: Redirect HTTP to HTTPS (True/False)
- `SECURE_BROWSER_XSS_FILTER`: Enable XSS filter (True/False)
- `SECURE_CONTENT_TYPE_NOSNIFF`: Prevent MIME sniffing (True/False)

### File Upload Configuration
- `FILE_UPLOAD_MAX_MEMORY_SIZE`: Max file upload size in memory (bytes)
- `DATA_UPLOAD_MAX_MEMORY_SIZE`: Max data upload size (bytes)
- `FILE_UPLOAD_PERMISSIONS`: File upload permissions (octal)

### Logging Configuration
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `LOG_FILE_PATH`: Log file path (e.g., logs/django.log)

### Other Configuration
- `MEDIA_URL`: Media files URL path
- `STATIC_URL`: Static files URL path
- `TIME_ZONE`: Application timezone
- `ADMIN_SITE_TITLE`: Admin panel title
- `ADMIN_SITE_HEADER`: Admin panel header
- `ADMIN_SITE_BRAND`: Admin panel brand name

## Frontend Environment Variables (.env)

Create a `.env` file in the `frontend/` directory with the following variables:

### API Configuration
- `VITE_API_BASE_URL`: Backend API base URL
- `VITE_API_TIMEOUT`: API request timeout in milliseconds

### Development Server Configuration
- `VITE_DEV_SERVER_HOST`: Development server host
- `VITE_DEV_SERVER_PORT`: Development server port
- `VITE_HMR_HOST`: Hot module replacement host
- `VITE_HMR_PORT`: Hot module replacement port

### Payment Gateway Configuration
- `VITE_PAYMENT_BASE_URL`: Payment gateway base URL
- `VITE_MERCHANT_APP_ID`: Merchant application ID

### Legacy Configuration
- `VITE_LEGACY_BASE_URL`: Legacy API base URL
- `VITE_LEGACY_MERCHANT_APP_ID`: Legacy merchant application ID

### Application Configuration
- `VITE_APP_VERSION`: Application version
- `VITE_DEFAULT_PAGINATION_LIMIT`: Default pagination limit
- `VITE_DEFAULT_THEME`: Default theme (light/dark)

### Feature Flags
- `VITE_ENABLE_DARK_MODE`: Enable dark mode (true/false)
- `VITE_ENABLE_NOTIFICATIONS`: Enable notifications (true/false)

### Content Security Policy
- `VITE_CSP_IMG_SRC`: Image source for CSP headers
- `VITE_CSP_CONNECT_SRC`: Connection sources for CSP headers

### Backend Integration
- `VITE_BACKEND_MEDIA_URL`: Backend media server URL
- `VITE_FALLBACK_API_PORT`: Fallback API port for dynamic URL generation
- `VITE_SPECIAL_IP_ADDRESS`: Special IP address for custom routing

## Setup Instructions

1. Copy the example files:
   ```bash
   # Backend
   cp backend/.env.example backend/.env

   # Frontend
   cp frontend/.env.example frontend/.env
   ```

2. Edit the `.env` files with your specific configuration values.

3. Restart your development servers to apply the changes.

## Security Notes

- Never commit `.env` files to version control
- Use strong, unique values for SECRET_KEY and passwords
- In production, use environment-specific values
- Consider using a secrets management service for sensitive data

## Email Setup Examples

### Gmail SMTP Configuration
```env
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_USE_SSL=False
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

### Outlook/Hotmail SMTP Configuration
```env
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp-mail.outlook.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_USE_SSL=False
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

### Development (Console) Configuration
```env
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
DEFAULT_FROM_EMAIL=<EMAIL>
```

## Production Considerations

### Security Settings for Production
```env
DEBUG=False
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
SECURE_SSL_REDIRECT=True
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True
```

### Database for Production
```env
DB_ENGINE=django.db.backends.postgresql
DB_NAME=production_db_name
DB_USER=production_user
DB_PASSWORD=strong_production_password
DB_HOST=your-db-host.com
DB_PORT=5432
```

## Dependencies

Make sure you have the required packages installed:

### Backend
- `python-dotenv`: For loading environment variables

### Frontend
- Vite automatically loads environment variables prefixed with `VITE_`

## Troubleshooting

If environment variables are not being loaded:

1. Check that the `.env` file is in the correct directory
2. Ensure variable names are correct (case-sensitive)
3. Restart your development server
4. For frontend variables, ensure they start with `VITE_`
5. Check for syntax errors in the `.env` file (no spaces around =)
