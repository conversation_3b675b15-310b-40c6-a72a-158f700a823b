import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  User, 
  Users, 
  Key,
  RefreshCw,
  TestTube
} from 'lucide-react';
import { useRBAC } from '@/contexts/RBACContext';
import { useRoles, usePermissions, UserRoles, UserPermissions } from '@/hooks/usePermissions';
import { toast } from 'sonner';

const RBACTest: React.FC = () => {
  const rbac = useRBAC();
  const roles = useRoles();
  const permissions = usePermissions();
  const [testPermission, setTestPermission] = useState('');
  const [testRole, setTestRole] = useState('');
  const [testResults, setTestResults] = useState<any[]>([]);

  const runPermissionTest = async () => {
    if (!testPermission) {
      toast.error('Please enter a permission to test');
      return;
    }

    try {
      const localResult = permissions.hasPermission(testPermission);
      const backendResult = await permissions.checkPermissionAsync(testPermission);
      
      const result = {
        type: 'permission',
        test: testPermission,
        localResult,
        backendResult,
        match: localResult === backendResult,
        timestamp: new Date().toISOString(),
      };
      
      setTestResults(prev => [result, ...prev.slice(0, 9)]);
      
      if (result.match) {
        toast.success('Permission test passed');
      } else {
        toast.error('Permission test failed - local and backend results differ');
      }
    } catch (error) {
      toast.error('Permission test failed');
      console.error('Permission test error:', error);
    }
  };

  const runRoleTest = async () => {
    if (!testRole) {
      toast.error('Please enter a role to test');
      return;
    }

    try {
      const localResult = roles.hasRole(testRole);
      const backendResult = await roles.checkRoleAsync(testRole);
      
      const result = {
        type: 'role',
        test: testRole,
        localResult,
        backendResult,
        match: localResult === backendResult,
        timestamp: new Date().toISOString(),
      };
      
      setTestResults(prev => [result, ...prev.slice(0, 9)]);
      
      if (result.match) {
        toast.success('Role test passed');
      } else {
        toast.error('Role test failed - local and backend results differ');
      }
    } catch (error) {
      toast.error('Role test failed');
      console.error('Role test error:', error);
    }
  };

  const runComprehensiveTest = async () => {
    const tests = [
      // Common permission tests
      { type: 'permission', test: UserPermissions.USER_VIEW },
      { type: 'permission', test: UserPermissions.APPLICATION_VIEW },
      { type: 'permission', test: UserPermissions.GRADUATE_VIEW },
      
      // Common role tests
      { type: 'role', test: UserRoles.ADMINISTRATOR },
      { type: 'role', test: UserRoles.MAIN_REGISTRAR },
      { type: 'role', test: UserRoles.DEPARTMENT_HEAD },
    ];

    setTestResults([]);
    
    for (const test of tests) {
      try {
        let localResult, backendResult;
        
        if (test.type === 'permission') {
          localResult = permissions.hasPermission(test.test);
          backendResult = await permissions.checkPermissionAsync(test.test);
        } else {
          localResult = roles.hasRole(test.test);
          backendResult = await roles.checkRoleAsync(test.test);
        }
        
        const result = {
          type: test.type,
          test: test.test,
          localResult,
          backendResult,
          match: localResult === backendResult,
          timestamp: new Date().toISOString(),
        };
        
        setTestResults(prev => [result, ...prev]);
      } catch (error) {
        console.error(`Test failed for ${test.type}: ${test.test}`, error);
      }
    }
    
    toast.success('Comprehensive RBAC test completed');
  };

  const validateAccess = async () => {
    try {
      const isValid = await rbac.validateAccess();
      if (isValid) {
        toast.success('Access validation passed');
      } else {
        toast.error('Access validation failed');
      }
    } catch (error) {
      toast.error('Access validation error');
      console.error('Access validation error:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* User Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Current User RBAC Status</span>
          </CardTitle>
          <CardDescription>
            Your current authentication and authorization status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong>Username:</strong> {rbac.user?.username || 'Not authenticated'}</p>
              <p><strong>Email:</strong> {rbac.user?.email || 'N/A'}</p>
              <p><strong>Department:</strong> {rbac.user?.department || 'N/A'}</p>
              <div className="flex items-center space-x-2">
                <strong>Status:</strong>
                <div className="flex space-x-1">
                  {rbac.isSuperuser && <Badge variant="destructive">Superuser</Badge>}
                  {rbac.isAdmin && <Badge variant="secondary">Admin</Badge>}
                  {rbac.isStaff && <Badge variant="outline">Staff</Badge>}
                  {rbac.canAccessAdmin && <Badge variant="default">Admin Access</Badge>}
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <p><strong>Roles:</strong> {rbac.user?.roles?.join(', ') || 'None'}</p>
              <p><strong>Permissions:</strong> {rbac.user?.permissions?.length || 0}</p>
              <p><strong>Staff with Groups:</strong> {rbac.isStaffWithGroups ? 'Yes' : 'No'}</p>
              <Button onClick={validateAccess} size="sm" className="mt-2">
                <Shield className="h-4 w-4 mr-2" />
                Validate Access
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* RBAC Testing Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TestTube className="h-5 w-5" />
            <span>RBAC Testing Interface</span>
          </CardTitle>
          <CardDescription>
            Test role and permission checking functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="manual" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="manual">Manual Testing</TabsTrigger>
              <TabsTrigger value="auto">Automated Tests</TabsTrigger>
            </TabsList>

            <TabsContent value="manual" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Test Permission</label>
                  <div className="flex space-x-2">
                    <Input
                      placeholder="e.g., auth.view_user"
                      value={testPermission}
                      onChange={(e) => setTestPermission(e.target.value)}
                    />
                    <Button onClick={runPermissionTest} size="sm">
                      <Key className="h-4 w-4 mr-2" />
                      Test
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Test Role</label>
                  <div className="flex space-x-2">
                    <Input
                      placeholder="e.g., Administrator"
                      value={testRole}
                      onChange={(e) => setTestRole(e.target.value)}
                    />
                    <Button onClick={runRoleTest} size="sm">
                      <Users className="h-4 w-4 mr-2" />
                      Test
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="auto" className="space-y-4">
              <div className="flex justify-center">
                <Button onClick={runComprehensiveTest} size="lg">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Run Comprehensive Test
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>
              Recent RBAC test results showing local vs backend validation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <Alert key={index} variant={result.match ? "default" : "destructive"}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center space-x-2">
                      {result.match ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <div>
                        <p className="font-medium">
                          {result.type.charAt(0).toUpperCase() + result.type.slice(1)}: {result.test}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Local: {result.localResult ? 'Allow' : 'Deny'} | 
                          Backend: {result.backendResult ? 'Allow' : 'Deny'}
                        </p>
                      </div>
                    </div>
                    <Badge variant={result.match ? "default" : "destructive"}>
                      {result.match ? 'PASS' : 'FAIL'}
                    </Badge>
                  </div>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Access Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Access Tests</CardTitle>
          <CardDescription>
            Test common access patterns and role combinations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <div className="text-center p-2 border rounded">
              <p className="text-sm font-medium">Can Manage Users</p>
              <Badge variant={permissions.canManageUsers ? "default" : "secondary"}>
                {permissions.canManageUsers ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="text-center p-2 border rounded">
              <p className="text-sm font-medium">Can Manage Apps</p>
              <Badge variant={permissions.canManageApplications ? "default" : "secondary"}>
                {permissions.canManageApplications ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="text-center p-2 border rounded">
              <p className="text-sm font-medium">Has Admin Access</p>
              <Badge variant={roles.hasAdminAccess ? "default" : "secondary"}>
                {roles.hasAdminAccess ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="text-center p-2 border rounded">
              <p className="text-sm font-medium">Has Staff Access</p>
              <Badge variant={roles.hasStaffAccess ? "default" : "secondary"}>
                {roles.hasStaffAccess ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RBACTest;
