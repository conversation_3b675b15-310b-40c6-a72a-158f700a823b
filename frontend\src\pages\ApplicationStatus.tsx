
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Loader2, Award, FileText, CheckCircle, AlertTriangle, Clock, ChevronRight, Plus, School, BookOpen, CreditCard, FileCheck, Edit, Trash, Trash2, User, Calendar, ArrowRight, MoreVertical, XCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { applicationAPI } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Define interfaces for our data types
interface GATRecord {
  id: number;
  GAT_No: string;
  GAT_Result: number;
  created_at: string;
  updated_at: string;
  user: number;
}

interface ProgramSelection {
  id: number;
  application_num: string;
  sponsorship: string;
  application_info: any;
  created_at: string;
  updated_at: string;
  user: number;
  gat: number;
  year?: string;
  year_name?: string;
  term?: string;
  term_name?: string;
}

interface Documentation {
  id: number;
  user: number;
  created_at: string;
  updated_at: string;
  passport_photo?: string;
  undergraduate_degree?: string;
  undergraduate_transcript?: string;
  masters_degree?: string;
  masters_transcript?: string;
  letter_of_sponsorship?: string;
  letter_of_recommendation?: string;
  research_proposal?: string;
  other_document?: string;
}

const ApplicationStatus = () => {
  const { applicationId } = useParams();
  const navigate = useNavigate();
  const [gatRecords, setGatRecords] = useState<GATRecord[]>([]);
  const [programSelections, setProgramSelections] = useState<ProgramSelection[]>([]);
  const [documentations, setDocumentations] = useState<Documentation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<{ id: number, type: 'gat' | 'program' } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchAllApplicationData = async () => {
      try {
        setLoading(true);

        // Fetch all GAT records for the current user
        const gatResponse = await applicationAPI.getCurrentGAT();
        if (process.env.NODE_ENV === 'development') {
          console.debug('GAT records:', gatResponse.data);
        }
        setGatRecords(gatResponse.data);

        // Fetch all program selections for the current user
        const programResponse = await applicationAPI.getCurrentProgramSelection();
        if (process.env.NODE_ENV === 'development') {
          console.debug('Program selections:', programResponse.data);
        }
        setProgramSelections(programResponse.data);

        // Fetch all documentation records for the current user
        try {
          const documentationResponse = await applicationAPI.getCurrentDocumentation();
          if (process.env.NODE_ENV === 'development') {
            console.debug('Documentation records:', documentationResponse.data);
          }
          setDocumentations(Array.isArray(documentationResponse.data) ? documentationResponse.data : []);
        } catch (docError) {
          if (process.env.NODE_ENV === 'development') {
            console.debug('Error fetching documentation data:', docError);
          }
          // Don't fail the whole process if documentation fetch fails
          setDocumentations([]);
        }

      } catch (error) {
        console.error('Error fetching application data:', error);
        setError('Failed to load your applications. Please try again later.');
        toast.error('Failed to load your applications');
      } finally {
        setLoading(false);
      }
    };

    fetchAllApplicationData();
  }, []);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <Card className="mb-8 shadow-md">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
              <CardTitle className="text-xl text-gray-800">My Applications</CardTitle>
              <CardDescription>Loading your applications...</CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <div className="flex items-center justify-center h-64">
                <div className="animate-pulse space-y-4 w-full max-w-3xl">
                  <div className="h-12 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-24 bg-gray-200 rounded"></div>
                  <div className="h-24 bg-gray-200 rounded"></div>
                  <div className="h-24 bg-gray-200 rounded"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <Card className="mb-8 shadow-md">
            <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50 border-b border-red-100">
              <CardTitle className="text-xl text-gray-800">Error</CardTitle>
              <CardDescription>There was a problem loading your applications</CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <div className="flex items-center justify-center h-64">
                <div className="flex flex-col items-center">
                  <XCircle className="h-12 w-12 text-red-500 mb-6" />
                  <p className="text-red-600 text-lg font-medium mb-4">{error}</p>
                  <p className="text-gray-600 mb-8">Please try again or contact support.</p>
                  <Button
                    onClick={() => window.location.reload()}
                    className="bg-[#1a73c0] hover:bg-[#0e4a7d]"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Function to get application status based on program selection
  const getApplicationStatus = (programSelection: ProgramSelection | undefined) => {
    if (!programSelection) return 'pending';

    // In a real application, you would check various fields to determine status
    // For now, we'll just return 'submitted' for any program selection that exists
    return 'submitted';
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Find program selection for a GAT record
  const findProgramSelection = (gatId: number) => {
    return programSelections.find(ps => ps.gat === gatId);
  };

  // Find documentation for a GAT record
  const findDocumentation = (gatId: number) => {
    // First find the program selection for this GAT
    const programSelection = findProgramSelection(gatId);
    if (!programSelection) return null;

    // Then find documentation that matches the user ID
    // Since documentation is linked to the user, not directly to GAT or program selection
    // We check if any documentation exists for this user
    return documentations.length > 0 ? documentations[0] : null;
  };

  // Start a new application
  const startNewApplication = () => {
    // Skip the new application page and go directly to the GAT page with new=true
    navigate('/application/gat?new=true');
  };

  // Continue an existing application
  const continueApplication = (gatId: number) => {
    navigate(`/application/gat?id=${gatId}`);
  };

  // View application details
  const viewApplicationDetails = (programSelectionId: number) => {
    navigate(`/application/details/${programSelectionId}`);
  };

  // Edit GAT record
  const editGatRecord = (gatId: number) => {
    navigate(`/application/gat?id=${gatId}&edit=true`);
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (id: number, type: 'gat' | 'program') => {
    setRecordToDelete({ id, type });
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setRecordToDelete(null);
  };

  // Delete record
  const deleteRecord = async () => {
    if (!recordToDelete) return;

    setIsDeleting(true);

    try {
      if (recordToDelete.type === 'gat') {
        // Check if this GAT record has associated program selections
        const associatedProgram = programSelections.find(ps => ps.gat === recordToDelete.id);

        if (associatedProgram) {
          // Delete the program selection first
          await applicationAPI.deleteProgramSelection(associatedProgram.id);
        }

        // Then delete the GAT record
        await applicationAPI.deleteGAT(recordToDelete.id);

        // Update the state
        setGatRecords(gatRecords.filter(gat => gat.id !== recordToDelete.id));
        setProgramSelections(programSelections.filter(ps => ps.gat !== recordToDelete.id));

        toast.success('Application deleted successfully');
      } else if (recordToDelete.type === 'program') {
        // Delete just the program selection
        await applicationAPI.deleteProgramSelection(recordToDelete.id);

        // Update the state
        setProgramSelections(programSelections.filter(ps => ps.id !== recordToDelete.id));

        toast.success('Program selection deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting record:', error);
      toast.error('Failed to delete record. Please try again.');
    } finally {
      setIsDeleting(false);
      closeDeleteDialog();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <header className="bg-[#1a73c0] text-white shadow-md">
        <div className="container mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <h1 className="text-xl font-bold">University of Gondar</h1>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/10 rounded-md transition-colors"
              onClick={() => navigate('/dashboard')}
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto p-4 md:p-6 pt-8">
        {/* Header Card */}
        <Card className="mb-8 shadow-md">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-xl text-gray-800">My Applications</CardTitle>
                <CardDescription>View and manage your applications</CardDescription>
              </div>
              <Button
                onClick={startNewApplication}
                className="bg-white hover:bg-white/90 text-[#1a73c0] font-medium px-5 py-2 h-auto text-base shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20 rounded-md"
              >
                <Plus className="h-5 w-5 mr-2 text-[#1a73c0]" />
                Start New Application
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {gatRecords.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">No Applications Found</h3>
                <p className="text-gray-500 mb-6">You haven't started any applications yet.</p>
                <Button
                  onClick={startNewApplication}
                  className="bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium px-5 py-2 h-auto text-base shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Start Your First Application
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {gatRecords.map((gat) => {
                  const programSelection = findProgramSelection(gat.id);
                  const documentation = findDocumentation(gat.id);
                  const applicationStatus = getApplicationStatus(programSelection);
                  // Application is complete if documentation exists
                  const isComplete = !!documentation;

                  return (
                    <Card key={gat.id} className="overflow-hidden border border-gray-200 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow">
                      <div className="flex flex-col md:flex-row">
                        <div className="p-5 flex-grow">
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="flex items-center gap-2 mb-2">
                                <Award className="h-5 w-5 text-[#1a73c0]" />
                                <h3 className="font-medium text-gray-900">
                                  GAT Number: {gat.GAT_No}
                                </h3>
                              </div>
                            </div>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => editGatRecord(gat.id)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => openDeleteDialog(gat.id, 'gat')}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2 mt-4">
                            <div className="flex items-center gap-2">
                              <Award className="h-4 w-4 text-gray-500" />
                              <span className="text-sm text-gray-500">GAT Score:</span>
                              <span className="text-sm font-medium">{gat.GAT_Result}</span>
                            </div>

                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-gray-500" />
                              <span className="text-sm text-gray-500">Created:</span>
                              <span className="text-sm font-medium">{formatDate(gat.created_at)}</span>
                            </div>

                            {programSelection && (
                              <>
                                <div className="flex items-center gap-2">
                                  <FileText className="h-4 w-4 text-gray-500" />
                                  <span className="text-sm text-gray-500">Application #:</span>
                                  <span className="text-sm font-medium">{programSelection.application_num}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                  <User className="h-4 w-4 text-gray-500" />
                                  <span className="text-sm text-gray-500">Sponsorship:</span>
                                  <span className="text-sm font-medium">{programSelection.sponsorship}</span>
                                </div>

                                {programSelection.year_name && (
                                  <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm text-gray-500">Academic Year:</span>
                                    <span className="text-sm font-medium">{programSelection.year_name}</span>
                                  </div>
                                )}

                                {programSelection.term_name && (
                                  <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm text-gray-500">Term:</span>
                                    <span className="text-sm font-medium">{programSelection.term_name}</span>
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>

                        <div className="bg-gray-50 p-5 flex flex-col justify-center items-center md:w-64 border-t md:border-t-0 md:border-l border-gray-200">
                          {isComplete ? (
                            <Button
                              onClick={() => viewApplicationDetails(programSelection!.id)}
                              className="w-full bg-[#1a73c0] hover:bg-[#0e4a7d] text-white mb-2"
                            >
                              View Details
                              <ArrowRight className="h-4 w-4 ml-2" />
                            </Button>
                          ) : (
                            <Button
                              onClick={() => continueApplication(gat.id)}
                              className="w-full bg-[#1a73c0] hover:bg-[#0e4a7d] text-white mb-2"
                            >
                              Continue Application
                              <ArrowRight className="h-4 w-4 ml-2" />
                            </Button>
                          )}

                          <div className="text-xs text-gray-500 mt-2 text-center">
                            Last updated: {formatDate(gat.updated_at)}
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Confirm Deletion
              </AlertDialogTitle>
              <AlertDialogDescription>
                {recordToDelete?.type === 'gat' ? (
                  <>
                    Are you sure you want to delete this application? This action cannot be undone.
                    {programSelections.some(ps => ps.gat === recordToDelete.id) && (
                      <p className="mt-2 text-red-500 font-medium">
                        Warning: This will also delete all associated program selections and documents.
                      </p>
                    )}
                  </>
                ) : (
                  <>
                    Are you sure you want to delete this program selection? This action cannot be undone.
                  </>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={(e) => {
                  e.preventDefault();
                  deleteRecord();
                }}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default ApplicationStatus;
