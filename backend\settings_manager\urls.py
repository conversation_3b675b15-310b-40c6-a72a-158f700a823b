from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import OrganizationSettingViewSet, QuickLinkViewSet, SocialMediaLinkViewSet
from .smtp_views import smtp_settings_view, test_smtp_connection, smtp_debug_info, smtp_provider_configs, apply_provider_config

router = DefaultRouter()
router.register(r'organization', OrganizationSettingViewSet)
router.register(r'quick-links', QuickLinkViewSet)
router.register(r'social-media', SocialMediaLinkViewSet)

urlpatterns = [
    path('', include(router.urls)),
    # SMTP Settings URLs
    path('smtp/', smtp_settings_view, name='smtp_settings'),
    path('smtp/test/', test_smtp_connection, name='test_smtp_connection'),
    path('smtp/debug/', smtp_debug_info, name='smtp_debug_info'),
    path('smtp/providers/', smtp_provider_configs, name='smtp_provider_configs'),
    path('smtp/apply-provider/', apply_provider_config, name='apply_provider_config'),
]
