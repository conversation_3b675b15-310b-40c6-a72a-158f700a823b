"""
Security decorators for robust API endpoint protection
"""
from functools import wraps
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from rest_framework.decorators import permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from rest_framework import status
import logging

logger = logging.getLogger(__name__)


def require_permissions(*permissions):
    """
    Decorator to require specific permissions for a view
    Usage: @require_permissions('app.action_model', 'app.another_action')
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse(
                    {'error': 'Authentication required'}, 
                    status=401
                )
            
            # Superusers bypass permission checks
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # Check if user has all required permissions
            missing_permissions = []
            for permission in permissions:
                if not request.user.has_perm(permission):
                    missing_permissions.append(permission)
            
            if missing_permissions:
                logger.warning(
                    f"User {request.user.username} denied access. "
                    f"Missing permissions: {missing_permissions}"
                )
                return JsonResponse({
                    'error': 'Insufficient permissions',
                    'missing_permissions': missing_permissions
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_roles(*roles):
    """
    Decorator to require specific roles (groups) for a view
    Usage: @require_roles('Admin', 'Manager')
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse(
                    {'error': 'Authentication required'}, 
                    status=401
                )
            
            # Superusers bypass role checks
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # Check if user has any of the required roles
            user_roles = set(request.user.groups.values_list('name', flat=True))
            required_roles = set(roles)
            
            if not user_roles.intersection(required_roles):
                logger.warning(
                    f"User {request.user.username} denied access. "
                    f"Required roles: {required_roles}, User roles: {user_roles}"
                )
                return JsonResponse({
                    'error': 'Insufficient role privileges',
                    'required_roles': list(required_roles),
                    'user_roles': list(user_roles)
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def require_staff(view_func):
    """
    Decorator to require staff status
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse(
                {'error': 'Authentication required'}, 
                status=401
            )
        
        if not (request.user.is_staff or request.user.is_superuser):
            logger.warning(
                f"User {request.user.username} denied staff access"
            )
            return JsonResponse(
                {'error': 'Staff privileges required'}, 
                status=403
            )
        
        return view_func(request, *args, **kwargs)
    return wrapper


def require_superuser(view_func):
    """
    Decorator to require superuser status
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse(
                {'error': 'Authentication required'}, 
                status=401
            )
        
        if not request.user.is_superuser:
            logger.warning(
                f"User {request.user.username} denied superuser access"
            )
            return JsonResponse(
                {'error': 'Superuser privileges required'}, 
                status=403
            )
        
        return view_func(request, *args, **kwargs)
    return wrapper


def api_require_permissions(*permissions):
    """
    DRF-compatible decorator for API views requiring specific permissions
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            if not request.user.is_authenticated:
                return Response(
                    {'error': 'Authentication required'}, 
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Superusers bypass permission checks
            if request.user.is_superuser:
                return view_func(self, request, *args, **kwargs)
            
            # Check permissions
            missing_permissions = []
            for permission in permissions:
                if not request.user.has_perm(permission):
                    missing_permissions.append(permission)
            
            if missing_permissions:
                logger.warning(
                    f"API access denied for user {request.user.username}. "
                    f"Missing permissions: {missing_permissions}"
                )
                return Response({
                    'error': 'Insufficient permissions',
                    'missing_permissions': missing_permissions,
                    'required_permissions': list(permissions)
                }, status=status.HTTP_403_FORBIDDEN)
            
            return view_func(self, request, *args, **kwargs)
        return wrapper
    return decorator


def api_require_roles(*roles):
    """
    DRF-compatible decorator for API views requiring specific roles
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            if not request.user.is_authenticated:
                return Response(
                    {'error': 'Authentication required'}, 
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Superusers bypass role checks
            if request.user.is_superuser:
                return view_func(self, request, *args, **kwargs)
            
            # Check roles
            user_roles = set(request.user.groups.values_list('name', flat=True))
            required_roles = set(roles)
            
            if not user_roles.intersection(required_roles):
                logger.warning(
                    f"API access denied for user {request.user.username}. "
                    f"Required roles: {required_roles}, User roles: {user_roles}"
                )
                return Response({
                    'error': 'Insufficient role privileges',
                    'required_roles': list(required_roles),
                    'user_roles': list(user_roles)
                }, status=status.HTTP_403_FORBIDDEN)
            
            return view_func(self, request, *args, **kwargs)
        return wrapper
    return decorator


def validate_request_data(*required_fields):
    """
    Decorator to validate required fields in request data
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            missing_fields = []
            
            for field in required_fields:
                if field not in request.data or request.data[field] is None:
                    missing_fields.append(field)
            
            if missing_fields:
                return Response({
                    'error': 'Missing required fields',
                    'missing_fields': missing_fields,
                    'required_fields': list(required_fields)
                }, status=status.HTTP_400_BAD_REQUEST)
            
            return view_func(self, request, *args, **kwargs)
        return wrapper
    return decorator


def log_api_access(view_func):
    """
    Decorator to log API access attempts
    """
    @wraps(view_func)
    def wrapper(self, request, *args, **kwargs):
        user_info = f"User: {request.user.username}" if request.user.is_authenticated else "Anonymous"
        endpoint = f"{request.method} {request.path}"
        
        logger.info(f"API Access - {endpoint} - {user_info}")
        
        try:
            response = view_func(self, request, *args, **kwargs)
            logger.info(f"API Success - {endpoint} - {user_info} - Status: {response.status_code}")
            return response
        except Exception as e:
            logger.error(f"API Error - {endpoint} - {user_info} - Error: {str(e)}")
            raise
    
    return wrapper
