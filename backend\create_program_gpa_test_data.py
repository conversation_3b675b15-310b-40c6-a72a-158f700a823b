#!/usr/bin/env python
"""
Script to create test data for Program GPA Trends functionality
"""
import os
import sys
import django
from datetime import datetime
import random

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from GraduateVerification.models import GraduateStudent
from registration.models import College, Department, Program, AdmissionClassification
from study_program.models import StudyProgram
from GraduateVerification.models import VerificationFieldOfStudy

def create_program_gpa_test_data():
    """Create test data for program GPA trends"""
    
    print("Creating test data for Program GPA Trends...")
    
    # Get existing data
    colleges = list(College.objects.all())
    departments = list(Department.objects.all())
    programs = list(Program.objects.all())
    study_programs = list(StudyProgram.objects.all())
    fields_of_study = list(VerificationFieldOfStudy.objects.all())
    admission_classifications = list(AdmissionClassification.objects.all())
    
    print(f"Found {len(colleges)} colleges, {len(departments)} departments, {len(programs)} programs")
    print(f"Found {len(study_programs)} study programs, {len(fields_of_study)} fields of study")
    
    if not all([colleges, departments, programs, study_programs, admission_classifications]):
        print("ERROR: Missing required reference data. Please ensure all reference tables have data.")
        return
    
    # Create graduates for multiple years (2022, 2023, 2024, 2025)
    years = [2022, 2023, 2024, 2025]
    genders = ['Male', 'Female']
    
    # Sample names for variety
    male_names = [
        ('John', 'Smith'), ('Michael', 'Johnson'), ('David', 'Williams'), ('James', 'Brown'),
        ('Robert', 'Jones'), ('William', 'Garcia'), ('Richard', 'Miller'), ('Joseph', 'Davis'),
        ('Thomas', 'Rodriguez'), ('Christopher', 'Martinez'), ('Charles', 'Hernandez'),
        ('Daniel', 'Lopez'), ('Matthew', 'Gonzalez'), ('Anthony', 'Wilson'), ('Mark', 'Anderson')
    ]
    
    female_names = [
        ('Mary', 'Smith'), ('Patricia', 'Johnson'), ('Jennifer', 'Williams'), ('Linda', 'Brown'),
        ('Elizabeth', 'Jones'), ('Barbara', 'Garcia'), ('Susan', 'Miller'), ('Jessica', 'Davis'),
        ('Sarah', 'Rodriguez'), ('Karen', 'Martinez'), ('Nancy', 'Hernandez'), ('Lisa', 'Lopez'),
        ('Betty', 'Gonzalez'), ('Helen', 'Wilson'), ('Sandra', 'Anderson'), ('Donna', 'Thomas')
    ]
    
    created_count = 0
    
    for year in years:
        print(f"Creating graduates for year {year}...")
        
        # Create 15-25 graduates per year for each study program
        for study_program in study_programs:
            graduates_for_program = random.randint(15, 25)
            
            for i in range(graduates_for_program):
                # Random gender
                gender = random.choice(genders)
                
                # Random name based on gender
                if gender == 'Male':
                    first_name, last_name = random.choice(male_names)
                else:
                    first_name, last_name = random.choice(female_names)
                
                # Add some variation to names
                if random.random() < 0.3:  # 30% chance to modify name
                    first_name = f"{first_name}{random.randint(1, 99)}"
                
                # Random college and department (but try to make it realistic)
                college = random.choice(colleges)
                dept_options = [d for d in departments if d.college == college]
                department = random.choice(dept_options) if dept_options else random.choice(departments)
                
                # Random program and field of study
                program = random.choice(programs)
                field_of_study = random.choice(fields_of_study)
                admission_classification = random.choice(admission_classifications)
                
                # Generate realistic GPA based on program and year
                # Higher level programs (PhD) tend to have higher GPAs
                base_gpa = 3.0
                if 'Doctor' in study_program.program_name or 'PhD' in study_program.program_name:
                    base_gpa = 3.5
                elif 'Master' in study_program.program_name:
                    base_gpa = 3.2
                elif 'Bachelor' in study_program.program_name:
                    base_gpa = 3.0
                
                # Add some year-based trend (slight improvement over time)
                year_bonus = (year - 2022) * 0.05
                
                # Add random variation
                gpa_variation = random.uniform(-0.3, 0.4)
                final_gpa = min(4.0, max(2.0, base_gpa + year_bonus + gpa_variation))
                
                # Create graduate student
                graduate = GraduateStudent.objects.create(
                    first_name=first_name,
                    last_name=last_name,
                    email=f"{first_name.lower()}.{last_name.lower()}{random.randint(1, 999)}@example.com",
                    phone_number=f"+251{random.randint(100000000, 999999999)}",
                    gender=gender,
                    date_of_birth=f"{random.randint(1990, 2000)}-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
                    college=college,
                    department=department,
                    program=program,
                    study_program=study_program,
                    field_of_study=field_of_study,
                    admission_classification=admission_classification,
                    year_of_graduation=year,
                    gpa=round(final_gpa, 2),
                    graduation_date=f"{year}-{random.randint(6, 8):02d}-{random.randint(1, 28):02d}",
                    student_id=f"GSU/{year}/{random.randint(1000, 9999)}",
                    verification_status='verified',
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                created_count += 1
                
                if created_count % 50 == 0:
                    print(f"Created {created_count} graduates...")
    
    print(f"\nSuccessfully created {created_count} graduate students!")
    print(f"Data spans {len(years)} years: {min(years)} - {max(years)}")
    print(f"Each study program has 15-25 graduates per year")
    print(f"Total study programs: {len(study_programs)}")
    
    # Verify the data
    print("\nVerifying created data:")
    for study_program in study_programs:
        count = GraduateStudent.objects.filter(study_program=study_program).count()
        avg_gpa = GraduateStudent.objects.filter(study_program=study_program).aggregate(
            avg_gpa=django.db.models.Avg('gpa')
        )['avg_gpa']
        print(f"  {study_program.program_name}: {count} graduates, avg GPA: {avg_gpa:.2f}")
    
    print("\nProgram GPA Trends should now be functional!")

if __name__ == '__main__':
    create_program_gpa_test_data()
