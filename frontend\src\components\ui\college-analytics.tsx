import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './card';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { ChartExport } from './chart-export';
import { TrendingUp, Award, Users } from 'lucide-react';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

interface CollegeAnalyticsProps {
  collegePerformanceData: any[];
  isLoading?: boolean;
}

export const CollegeAnalytics: React.FC<CollegeAnalyticsProps> = ({
  collegePerformanceData,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card className="border-0 shadow-xl">
          <CardHeader>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-100 rounded animate-pulse"></div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-xl">
          <CardHeader>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-80 bg-gray-100 rounded animate-pulse"></div>
          </CardContent>
        </Card>
      </div>
    );
  }



  // Prepare data for pie chart
  const pieChartData = collegePerformanceData.slice(0, 6).map((college, index) => ({
    name: college.name,
    value: college.count,
    percentage: college.percentage,
    color: COLORS[index % COLORS.length]
  }));



  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{data.name}</p>
          <p className="text-blue-600">
            <span className="font-medium">Graduates:</span> {data.value.toLocaleString()}
          </p>
          <p className="text-green-600">
            <span className="font-medium">Percentage:</span> {data.percentage}%
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="mb-8">
      {/* Pie Chart */}
      <Card className="border-0 shadow-xl bg-white hover:shadow-2xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
                <div className="h-2 w-2 bg-green-600 rounded-full mr-3"></div>
                Graduate Distribution by College
              </CardTitle>
              <p className="text-gray-600 mt-1">Percentage distribution across colleges</p>
            </div>
            <div className="flex items-center space-x-2">
              <ChartExport 
                chartId="college-pie-chart"
                filename="graduate-distribution-by-college"
                data={pieChartData}
              />
              <div className="bg-green-600 p-2 rounded-lg">
                <Users className="h-5 w-5 text-white" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="h-96 p-6">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieChartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percentage }) => `${percentage}%`}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
              >
                {pieChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<PieTooltip />} />
            </PieChart>
          </ResponsiveContainer>
          
          {/* Legend */}
          <div className="mt-4 grid grid-cols-2 gap-2">
            {pieChartData.map((entry, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: entry.color }}
                ></div>
                <span className="text-xs text-gray-600 truncate">
                  {entry.name.length > 15 ? entry.name.substring(0, 15) + '...' : entry.name}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CollegeAnalytics;
