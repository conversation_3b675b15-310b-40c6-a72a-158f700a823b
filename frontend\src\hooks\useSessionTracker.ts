import { useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../config';

/**
 * Hook to track user activity and refresh the token when needed
 * This helps prevent session expiration during active use
 */
const useSessionTracker = () => {
  useEffect(() => {
    // Set initial last activity timestamp if not exists
    if (!localStorage.getItem('lastActivity')) {
      localStorage.setItem('lastActivity', Date.now().toString());
    }

    // Function to refresh the token
    const refreshToken = async () => {
      const refreshTokenValue = localStorage.getItem('refresh_token');
      if (!refreshTokenValue) return;

      try {
        const response = await axios.post(`${API_BASE_URL}/token/refresh/`, {
          refresh: refreshTokenValue
        });

        if (response.data.access) {
          localStorage.setItem('token', response.data.access);
          if (response.data.refresh) {
            localStorage.setItem('refresh_token', response.data.refresh);
          }
          localStorage.setItem('lastActivity', Date.now().toString());
          console.log('Token refreshed by session tracker');
        }
      } catch (error) {
        console.error('Session tracker token refresh failed:', error);
      }
    };

    // Function to check if token needs refresh
    const checkSession = () => {
      const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
      if (!isAuthenticated) return;

      const lastActivity = localStorage.getItem('lastActivity');
      if (!lastActivity) return;

      const now = Date.now();
      const lastActivityTime = parseInt(lastActivity, 10);
      const timeSinceLastActivity = now - lastActivityTime;

      // Refresh token if last activity was more than 30 minutes ago
      // but less than the refresh token lifetime (to ensure the refresh token is still valid)
      if (timeSinceLastActivity > 30 * 60 * 1000 && timeSinceLastActivity < 6 * 24 * 60 * 60 * 1000) {
        refreshToken();
      }
    };

    // Function to update last activity timestamp
    const updateActivity = () => {
      const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
      if (isAuthenticated) {
        localStorage.setItem('lastActivity', Date.now().toString());
      }
    };

    // Check session every 5 minutes
    const sessionInterval = setInterval(checkSession, 5 * 60 * 1000);

    // Update activity on user interactions
    const events = ['mousedown', 'keydown', 'scroll', 'touchstart'];
    events.forEach(event => {
      window.addEventListener(event, updateActivity);
    });

    // Clean up
    return () => {
      clearInterval(sessionInterval);
      events.forEach(event => {
        window.removeEventListener(event, updateActivity);
      });
    };
  }, []);
};

export default useSessionTracker;
