from django.core.management.base import BaseCommand
from GraduateVerification.models import GraduateStudent
from django.utils import timezone

class Command(BaseCommand):
    help = 'Test soft delete functionality'

    def handle(self, *args, **options):
        self.stdout.write('Testing soft delete functionality...')
        
        # Check if soft delete fields exist
        try:
            # Get the first graduate
            graduate = GraduateStudent.objects.first()
            if not graduate:
                self.stdout.write(self.style.ERROR('No graduates found in database'))
                return
            
            self.stdout.write(f'Found graduate: {graduate}')
            
            # Check if soft delete fields exist
            if hasattr(graduate, 'is_deleted'):
                self.stdout.write(self.style.SUCCESS('is_deleted field exists'))
                self.stdout.write(f'Current is_deleted value: {graduate.is_deleted}')
            else:
                self.stdout.write(self.style.ERROR('is_deleted field does not exist'))
                
            if hasattr(graduate, 'deleted_at'):
                self.stdout.write(self.style.SUCCESS('deleted_at field exists'))
                self.stdout.write(f'Current deleted_at value: {graduate.deleted_at}')
            else:
                self.stdout.write(self.style.ERROR('deleted_at field does not exist'))
                
            if hasattr(graduate, 'deleted_by'):
                self.stdout.write(self.style.SUCCESS('deleted_by field exists'))
                self.stdout.write(f'Current deleted_by value: {graduate.deleted_by}')
            else:
                self.stdout.write(self.style.ERROR('deleted_by field does not exist'))
            
            # Test custom manager
            try:
                all_count = GraduateStudent.objects.all().count()
                self.stdout.write(f'All graduates count: {all_count}')
                
                deleted_count = GraduateStudent.objects.filter(is_deleted=True).count()
                self.stdout.write(f'Deleted graduates count: {deleted_count}')
                
                # Test custom manager methods
                try:
                    all_with_deleted_count = GraduateStudent.objects.all_with_deleted().count()
                    self.stdout.write(f'All with deleted count: {all_with_deleted_count}')
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'all_with_deleted() failed: {e}'))
                
                try:
                    deleted_only_count = GraduateStudent.objects.deleted_only().count()
                    self.stdout.write(f'Deleted only count: {deleted_only_count}')
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'deleted_only() failed: {e}'))
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Query failed: {e}'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {e}'))
