import axios from 'axios';

export interface College {
  id: number;
  name: string;
  code?: string;
  description?: string;
  status?: boolean;
  created_at?: string;
  updated_at?: string;
}

const API_BASE_URL = 'http://localhost:8000/api/';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

export const collegeAPI = {
  getColleges: async () => {
    const response = await api.get('colleges/');
    return response.data;
  },

  getCollegeById: async (id: number) => {
    const response = await api.get(`colleges/${id}/`);
    return response.data;
  },

  createCollege: async (data: Partial<College>) => {
    const response = await api.post('verification/colleges/', data);
    return response.data;
  },

  updateCollege: async (id: number, data: Partial<College>) => {
    const response = await api.put(`verification/colleges/${id}/`, data);
    return response.data;
  },

  deleteCollege: async (id: number) => {
    const response = await api.delete(`verification/colleges/${id}/`);
    return response.data;
  }
};

// React Query hook for compatibility with existing code
export const useGetCollegesQuery = () => {
  return { data: [] }; // Placeholder until we implement React Query
};
