from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import ServiceRequest, DocumentUpload


class DocumentUploadInline(admin.TabularInline):
    """Inline admin for document uploads."""
    
    model = DocumentUpload
    extra = 0
    readonly_fields = ['id', 'original_filename', 'file_size', 'content_type', 'created_at', 'uploaded_by']
    fields = [
        'document_type', 'file', 'original_filename', 'file_size',
        'is_verified', 'verification_notes', 'uploaded_by', 'verified_by'
    ]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('document_type', 'uploaded_by', 'verified_by')


@admin.register(ServiceRequest)
class ServiceRequestAdmin(admin.ModelAdmin):
    """Admin interface for ServiceRequest model with conditional field display."""
    
    list_display = [
        'full_name', 'email', 'service_type', 'status',
        'college_display', 'department_display', 'created_at', 'created_by'
    ]
    list_filter = [
        'status', 'service_type', 'admission_type', 'student_status',
        'year_type', 'mailing_destination', 'is_college_other',
        'is_department_other', 'created_at', 'is_deleted'
    ]
    search_fields = [
        'first_name', 'last_name', 'middle_name', 'email', 'mobile',
        'college__name', 'department__name', 'college_other', 'department_other'
    ]
    readonly_fields = [
        'id', 'full_name', 'college_name', 'department_name',
        'requires_mailing_address', 'requires_graduation_year', 'requires_student_status',
        'created_at', 'updated_at', 'deleted_at'
    ]
    
    inlines = [DocumentUploadInline]
    
    fieldsets = (
        ('Personal Information', {
            'fields': (
                'id', 'first_name', 'middle_name', 'last_name', 'full_name',
                'email', 'mobile'
            )
        }),
        ('Service Details', {
            'fields': (
                'service_type', 'admission_type', 'degree', 'status'
            )
        }),
        ('College Information', {
            'fields': (
                'college', 'college_other', 'is_college_other', 'college_name'
            )
        }),
        ('Department Information', {
            'fields': (
                'department', 'department_other', 'is_department_other', 'department_name'
            )
        }),
        ('Student Status (Conditional)', {
            'fields': (
                'student_status', 'year_ec', 'year_gc', 'year_type'
            ),
            'classes': ('collapse',),
            'description': 'Required for most services except Original Degree'
        }),
        ('Graduation Information (Original Degree Only)', {
            'fields': (
                'graduation_year_ec', 'graduation_year_gc'
            ),
            'classes': ('collapse',),
            'description': 'Required only for Original Degree service'
        }),
        ('Mailing Address (Official Transcript Only)', {
            'fields': (
                'mailing_destination', 'mailing_college', 'mailing_department',
                'institute_name', 'institute_country', 'institute_address',
                'mailing_agent', 'mailing_agent_other'
            ),
            'classes': ('collapse',),
            'description': 'Required only for Official Transcript service'
        }),
        ('Service Requirements', {
            'fields': (
                'requires_mailing_address', 'requires_graduation_year', 'requires_student_status'
            ),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': (
                'created_at', 'updated_at', 'created_by', 'updated_by',
                'is_deleted', 'deleted_at', 'deleted_by'
            ),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'service_type', 'admission_type', 'degree', 'college', 'department',
            'mailing_college', 'mailing_department', 'created_by', 'updated_by'
        ).prefetch_related('document_uploads')
    
    def college_display(self, obj):
        """Display college name (either selected or other)."""
        return obj.college_name or '-'
    college_display.short_description = 'College'
    
    def department_display(self, obj):
        """Display department name (either selected or other)."""
        return obj.department_name or '-'
    department_display.short_description = 'Department'
    
    def save_model(self, request, obj, form, change):
        """Set created_by or updated_by based on whether it's a new object."""
        if not change:  # Creating new object
            obj.created_by = request.user
        else:  # Updating existing object
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)
    
    def get_form(self, request, obj=None, **kwargs):
        """Customize form based on service type."""
        form = super().get_form(request, obj, **kwargs)
        
        # Add JavaScript for conditional field display
        form.Media.js = form.Media.js + ('admin/js/service_request_conditional.js',)
        
        return form
    
    actions = ['mark_as_pending', 'mark_as_processing', 'mark_as_completed', 'soft_delete_selected']
    
    def mark_as_pending(self, request, queryset):
        """Mark selected requests as pending."""
        updated = queryset.update(status='pending', updated_by=request.user)
        self.message_user(request, f'{updated} requests marked as pending.')
    mark_as_pending.short_description = 'Mark selected requests as pending'
    
    def mark_as_processing(self, request, queryset):
        """Mark selected requests as processing."""
        updated = queryset.update(status='processing', updated_by=request.user)
        self.message_user(request, f'{updated} requests marked as processing.')
    mark_as_processing.short_description = 'Mark selected requests as processing'
    
    def mark_as_completed(self, request, queryset):
        """Mark selected requests as completed."""
        updated = queryset.update(status='completed', updated_by=request.user)
        self.message_user(request, f'{updated} requests marked as completed.')
    mark_as_completed.short_description = 'Mark selected requests as completed'
    
    def soft_delete_selected(self, request, queryset):
        """Soft delete selected requests."""
        for obj in queryset:
            obj.soft_delete(user=request.user)
        self.message_user(request, f'{queryset.count()} requests soft deleted.')
    soft_delete_selected.short_description = 'Soft delete selected requests'


@admin.register(DocumentUpload)
class DocumentUploadAdmin(admin.ModelAdmin):
    """Admin interface for DocumentUpload model."""
    
    list_display = [
        'service_request_link', 'document_type', 'original_filename',
        'file_size_display', 'is_verified', 'uploaded_by', 'created_at'
    ]
    list_filter = [
        'document_type', 'is_verified', 'created_at', 'content_type'
    ]
    search_fields = [
        'service_request__first_name', 'service_request__last_name',
        'service_request__email', 'document_type__name', 'original_filename'
    ]
    readonly_fields = [
        'id', 'original_filename', 'file_size', 'file_size_display',
        'content_type', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Document Information', {
            'fields': (
                'id', 'service_request', 'document_type', 'file'
            )
        }),
        ('File Metadata', {
            'fields': (
                'original_filename', 'file_size', 'file_size_display', 'content_type'
            ),
            'classes': ('collapse',)
        }),
        ('Verification', {
            'fields': (
                'is_verified', 'verification_notes', 'verified_by'
            )
        }),
        ('Audit Information', {
            'fields': (
                'created_at', 'updated_at', 'uploaded_by'
            ),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'service_request', 'document_type', 'uploaded_by', 'verified_by'
        )
    
    def service_request_link(self, obj):
        """Create a link to the related service request."""
        url = reverse('admin:service_requests_servicerequest_change', args=[obj.service_request.pk])
        return format_html('<a href="{}">{}</a>', url, obj.service_request.full_name)
    service_request_link.short_description = 'Service Request'
    
    def file_size_display(self, obj):
        """Display file size in human-readable format."""
        if obj.file_size:
            if obj.file_size < 1024:
                return f'{obj.file_size} bytes'
            elif obj.file_size < 1024 * 1024:
                return f'{obj.file_size / 1024:.1f} KB'
            else:
                return f'{obj.file_size / (1024 * 1024):.1f} MB'
        return '-'
    file_size_display.short_description = 'File Size'
    
    def save_model(self, request, obj, form, change):
        """Set uploaded_by when creating a new document."""
        if not change:  # Creating new object
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)
    
    actions = ['mark_as_verified', 'mark_as_unverified']
    
    def mark_as_verified(self, request, queryset):
        """Mark selected documents as verified."""
        updated = queryset.update(is_verified=True, verified_by=request.user)
        self.message_user(request, f'{updated} documents marked as verified.')
    mark_as_verified.short_description = 'Mark selected documents as verified'
    
    def mark_as_unverified(self, request, queryset):
        """Mark selected documents as unverified."""
        updated = queryset.update(is_verified=False, verified_by=None)
        self.message_user(request, f'{updated} documents marked as unverified.')
    mark_as_unverified.short_description = 'Mark selected documents as unverified'
