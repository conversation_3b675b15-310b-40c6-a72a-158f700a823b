#!/usr/bin/env python
"""
Test script for ServiceRequest model functionality.
"""
import os
import django
import sys

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from service_requests.models import ServiceRequest, DocumentUpload
from setups.service_type.models import ServiceType
from setups.admission_type.models import AdmissionType
from setups.study_program.models import StudyProgram
from setups.college.models import College
from setups.department.models import Department
from setups.certificate_type.models import CertificateType
from decimal import Decimal

def test_service_request_creation():
    """Test creating a ServiceRequest with all required fields."""
    print("🧪 Testing ServiceRequest model creation...")
    
    try:
        # Get or create required lookup data
        service_type, _ = ServiceType.objects.get_or_create(
            name='Official Transcript',
            defaults={'fee': Decimal('50.00'), 'is_active': True}
        )
        
        admission_type, _ = AdmissionType.objects.get_or_create(
            name='Regular',
            defaults={'description': 'Regular admission'}
        )
        
        study_program, _ = StudyProgram.objects.get_or_create(
            program_code='MSC',
            defaults={'program_name': 'Master of Science'}
        )
        
        college, _ = College.objects.get_or_create(
            name='College of Natural Sciences',
            defaults={'description': 'Natural Sciences College'}
        )
        
        department, _ = Department.objects.get_or_create(
            name='Computer Science',
            college=college,
            defaults={'description': 'Computer Science Department'}
        )
        
        # Create a test user
        user, _ = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        
        # Create a ServiceRequest
        service_request = ServiceRequest.objects.create(
            first_name='John',
            middle_name='Michael',
            last_name='Doe',
            email='<EMAIL>',
            mobile='+251911123456',
            service_type=service_type,
            admission_type=admission_type,
            degree=study_program,
            college=college,
            department=department,
            student_status='graduate',
            year_ec=2016,
            year_gc=2024,
            year_type='graduation',
            mailing_destination='external',
            institute_name='Harvard University',
            institute_country='USA',
            institute_address='Cambridge, MA 02138',
            mailing_agent='dhl',
            created_by=user
        )
        
        print(f"✅ ServiceRequest created successfully: {service_request}")
        print(f"   - Full Name: {service_request.full_name}")
        print(f"   - College: {service_request.college_name}")
        print(f"   - Department: {service_request.department_name}")
        print(f"   - Requires Mailing: {service_request.requires_mailing_address}")
        print(f"   - Requires Graduation Year: {service_request.requires_graduation_year}")
        print(f"   - Requires Student Status: {service_request.requires_student_status}")
        
        return service_request
        
    except Exception as e:
        print(f"❌ Error creating ServiceRequest: {e}")
        return None

def test_document_upload():
    """Test creating a DocumentUpload."""
    print("\n🧪 Testing DocumentUpload model creation...")
    
    try:
        # Get the service request from previous test
        service_request = ServiceRequest.objects.first()
        if not service_request:
            print("❌ No ServiceRequest found for testing DocumentUpload")
            return
        
        # Get or create a certificate type
        cert_type, _ = CertificateType.objects.get_or_create(
            name='Academic Transcript',
            defaults={'description': 'Official academic transcript', 'is_active': True}
        )
        
        # Associate the certificate type with the service type
        service_request.service_type.document_types.add(cert_type)
        
        # Create a DocumentUpload (without actual file for testing)
        document_upload = DocumentUpload(
            service_request=service_request,
            document_type=cert_type,
            original_filename='transcript.pdf',
            file_size=1024000,  # 1MB
            content_type='application/pdf',
            uploaded_by=service_request.created_by
        )
        
        # Validate the document upload
        document_upload.clean()
        document_upload.save()
        
        print(f"✅ DocumentUpload created successfully: {document_upload}")
        print(f"   - Service Request: {document_upload.service_request.full_name}")
        print(f"   - Document Type: {document_upload.document_type.name}")
        print(f"   - File Size: {document_upload.file_size} bytes")
        
    except Exception as e:
        print(f"❌ Error creating DocumentUpload: {e}")

def test_validation():
    """Test model validation."""
    print("\n🧪 Testing ServiceRequest validation...")
    
    try:
        # Test validation for Official Transcript service
        service_type = ServiceType.objects.get(name='Official Transcript')
        admission_type = AdmissionType.objects.first()
        study_program = StudyProgram.objects.first()
        
        # Create a request without required mailing fields (should fail)
        invalid_request = ServiceRequest(
            first_name='Jane',
            last_name='Smith',
            email='<EMAIL>',
            mobile='+251911654321',
            service_type=service_type,
            admission_type=admission_type,
            degree=study_program,
            student_status='active',
            year_type='current',
            year_ec=2017,
            # Missing mailing_destination and other required fields
        )
        
        try:
            invalid_request.clean()
            print("❌ Validation should have failed but didn't")
        except Exception as validation_error:
            print(f"✅ Validation correctly failed: {validation_error}")
        
    except Exception as e:
        print(f"❌ Error during validation test: {e}")

def test_properties():
    """Test model properties and methods."""
    print("\n🧪 Testing ServiceRequest properties...")
    
    try:
        service_request = ServiceRequest.objects.first()
        if not service_request:
            print("❌ No ServiceRequest found for testing properties")
            return
        
        print(f"✅ Properties test:")
        print(f"   - Full Name: {service_request.full_name}")
        print(f"   - College Name: {service_request.college_name}")
        print(f"   - Department Name: {service_request.department_name}")
        print(f"   - Requires Mailing Address: {service_request.requires_mailing_address}")
        print(f"   - Requires Graduation Year: {service_request.requires_graduation_year}")
        print(f"   - Requires Student Status: {service_request.requires_student_status}")
        
    except Exception as e:
        print(f"❌ Error testing properties: {e}")

def main():
    """Run all tests."""
    print("🚀 Starting ServiceRequest Model Tests")
    print("=" * 50)
    
    # Run tests
    service_request = test_service_request_creation()
    if service_request:
        test_document_upload()
        test_validation()
        test_properties()
    
    print("\n" + "=" * 50)
    print("🏁 ServiceRequest Model Tests Completed")

if __name__ == '__main__':
    main()
