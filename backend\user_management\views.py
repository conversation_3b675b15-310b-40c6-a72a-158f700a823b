from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from rest_framework import viewsets, status, filters, generics
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON>ted, IsAdminUser, AllowAny
from django_filters.rest_framework import DjangoFilterBackend

from .models import UserProfile, Role, PermissionCategory
from .serializers import (
    UserSerializer,
    UserDetailSerializer,
    UserRegistrationSerializer,
    StaffSerializer,
    GroupSerializer,
    PermissionSerializer,
    ContentTypeSerializer,
    UserProfileSerializer,
    RoleSerializer,
    PermissionCategorySerializer
)
from .permissions import (
    RoleBasedPermission, AdminOnlyPermission, StaffOnlyPermission,
    UserManagementPermission, RoleManagementPermission
)


class UserViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing users
    """
    queryset = User.objects.all().order_by('-date_joined')
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'is_staff', 'is_superuser', 'groups']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering_fields = ['username', 'email', 'date_joined', 'last_login']

    def get_queryset(self):
        """
        Optionally restricts the returned users,
        by filtering against query parameters in the URL.
        """
        queryset = User.objects.all().order_by('-date_joined')
        username = self.request.query_params.get('username', None)
        email = self.request.query_params.get('email', None)

        if username:
            queryset = queryset.filter(username__icontains=username)
        if email:
            queryset = queryset.filter(email__icontains=email)

        return queryset

    @action(detail=False, methods=['get'])
    def me(self, request):
        """
        Get the current authenticated user
        """
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)


class StaffViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing staff members (users with is_staff=True)
    """
    queryset = User.objects.filter(is_staff=True).order_by('-date_joined')
    serializer_class = StaffSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'is_superuser', 'groups']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering_fields = ['username', 'email', 'date_joined', 'last_login']


class GroupViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing groups
    """
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']


class ContentTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing content types
    """
    queryset = ContentType.objects.all()
    serializer_class = ContentTypeSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['app_label', 'model']
    search_fields = ['app_label', 'model']


class PermissionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing permissions
    """
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['content_type']
    search_fields = ['name', 'codename']

    @action(detail=False, methods=['get'])
    def by_content_type(self, request):
        """
        Get permissions grouped by content type
        """
        content_type_id = request.query_params.get('content_type_id', None)

        if content_type_id:
            permissions = Permission.objects.filter(content_type_id=content_type_id)
        else:
            permissions = Permission.objects.all()

        serializer = self.get_serializer(permissions, many=True)
        return Response(serializer.data)


# Additional user-related views for compatibility with existing frontend
class CreateUserView(generics.CreateAPIView):
    """
    API endpoint for creating new users (registration)
    """
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]


class CheckUserView(APIView):
    """
    API endpoint to check if a user exists by username or email
    """
    permission_classes = [AllowAny]

    def post(self, request):
        username = request.data.get('username')
        email = request.data.get('email')

        if not username and not email:
            return Response(
                {'error': 'Please provide either username or email'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if username:
            user = User.objects.filter(username=username).first()
        else:
            user = User.objects.filter(email=email).first()

        if user:
            return Response({
                'exists': True,
                'username': user.username,
                'email': user.email
            })
        else:
            return Response({'exists': False})


class CurrentUserView(APIView):
    """
    API endpoint to retrieve the current authenticated user's details with RBAC info
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserDetailSerializer(request.user)
        data = serializer.data

        # Add RBAC information
        data['roles'] = [group.name for group in request.user.groups.all()]
        data['permissions'] = list(request.user.get_all_permissions())
        data['is_admin'] = request.user.is_superuser or request.user.groups.filter(name__in=['Admin', 'Super Admin']).exists()

        return Response(data)


class RBACInfoView(APIView):
    """
    API endpoint to retrieve comprehensive RBAC information for the current user
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        # Get user roles (groups)
        roles = list(user.groups.values_list('name', flat=True))

        # Get all permissions (direct + group permissions)
        permissions = list(user.get_all_permissions())

        # Check staff status with proper group validation
        is_staff = user.is_staff
        has_staff_roles = len(roles) > 0
        is_staff_with_groups = is_staff and has_staff_roles

        # Determine access levels
        is_superuser = user.is_superuser
        is_admin = is_superuser or user.groups.filter(name__in=['Administrator', 'Super Admin']).exists()
        can_access_admin = is_superuser or is_staff_with_groups

        # Get department info if available
        department = None
        if hasattr(user, 'profile') and user.profile.department:
            department = user.profile.department

        return Response({
            'user_id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_active': user.is_active,
            'is_staff': is_staff,
            'is_superuser': is_superuser,
            'is_admin': is_admin,
            'roles': roles,
            'permissions': permissions,
            'has_staff_roles': has_staff_roles,
            'is_staff_with_groups': is_staff_with_groups,
            'can_access_admin': can_access_admin,
            'department': department,
            'last_login': user.last_login,
            'date_joined': user.date_joined,
        })


class PermissionCheckView(APIView):
    """
    API endpoint to check specific permissions for the current user
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        permission = request.data.get('permission')
        role = request.data.get('role')

        if not permission and not role:
            return Response(
                {'error': 'Either permission or role must be provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        result = {}

        if permission:
            result['has_permission'] = user.has_perm(permission)
            result['permission'] = permission

        if role:
            result['has_role'] = user.groups.filter(name=role).exists()
            result['role'] = role

        return Response(result)


class RoleValidationView(APIView):
    """
    API endpoint to validate user roles and permissions for security auditing
    """
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        # Get all staff users
        staff_users = User.objects.filter(is_staff=True).select_related('profile').prefetch_related('groups')

        validation_results = []

        for user in staff_users:
            roles = list(user.groups.values_list('name', flat=True))
            permissions = list(user.get_all_permissions())

            # Check for security issues
            issues = []
            if user.is_staff and not roles:
                issues.append('Staff user without assigned roles')

            if not user.is_active:
                issues.append('Inactive staff account')

            if user.is_staff and not permissions:
                issues.append('Staff user without permissions')

            validation_results.append({
                'user_id': user.id,
                'username': user.username,
                'email': user.email,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'is_active': user.is_active,
                'roles': roles,
                'permission_count': len(permissions),
                'issues': issues,
                'last_login': user.last_login,
                'department': getattr(user.profile, 'department', None) if hasattr(user, 'profile') else None,
            })

        return Response({
            'total_staff': len(staff_users),
            'users_with_issues': len([u for u in validation_results if u['issues']]),
            'validation_results': validation_results
        })


class UserProfileViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user profiles
    """
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated, UserManagementPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['department']
    search_fields = ['user__username', 'user__email', 'user__first_name', 'user__last_name', 'department']

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user's profile"""
        profile, created = UserProfile.objects.get_or_create(user=request.user)
        serializer = self.get_serializer(profile)
        return Response(serializer.data)

    @action(detail=False, methods=['put', 'patch'])
    def update_me(self, request):
        """Update current user's profile"""
        profile, created = UserProfile.objects.get_or_create(user=request.user)
        serializer = self.get_serializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RoleViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing roles with enhanced RBAC functionality
    """
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated, RoleManagementPermission]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['group__name', 'description']
    ordering_fields = ['group__name', 'created_at']

    @action(detail=True, methods=['post'])
    def assign_users(self, request, pk=None):
        """Assign users to this role"""
        role = self.get_object()
        user_ids = request.data.get('user_ids', [])

        users = User.objects.filter(id__in=user_ids)
        for user in users:
            user.groups.add(role.group)

        return Response({'message': f'Assigned {len(users)} users to role {role.name}'})

    @action(detail=True, methods=['post'])
    def remove_users(self, request, pk=None):
        """Remove users from this role"""
        role = self.get_object()
        user_ids = request.data.get('user_ids', [])

        users = User.objects.filter(id__in=user_ids)
        for user in users:
            user.groups.remove(role.group)

        return Response({'message': f'Removed {len(users)} users from role {role.name}'})

    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """Get all users with this role"""
        role = self.get_object()
        users = User.objects.filter(groups=role.group)
        serializer = UserSerializer(users, many=True)
        return Response(serializer.data)


class PermissionCategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing permission categories
    """
    queryset = PermissionCategory.objects.all()
    serializer_class = PermissionCategorySerializer
    permission_classes = [IsAuthenticated, AdminOnlyPermission]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description']


class RBACUtilityView(APIView):
    """
    Utility view for RBAC operations
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get RBAC information for current user"""
        user = request.user

        data = {
            'user_id': user.id,
            'username': user.username,
            'roles': [group.name for group in user.groups.all()],
            'permissions': list(user.get_all_permissions()),
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'is_admin': user.is_superuser or user.groups.filter(name__in=['Admin', 'Super Admin']).exists(),
            'department': getattr(user.profile, 'department', None) if hasattr(user, 'profile') else None,
        }

        return Response(data)

    @action(detail=False, methods=['post'])
    def check_permission(self, request):
        """Check if current user has specific permission"""
        permission = request.data.get('permission')
        if not permission:
            return Response({'error': 'Permission parameter required'}, status=status.HTTP_400_BAD_REQUEST)

        has_permission = request.user.has_perm(permission)
        return Response({'has_permission': has_permission})

    @action(detail=False, methods=['post'])
    def check_role(self, request):
        """Check if current user has specific role"""
        role = request.data.get('role')
        if not role:
            return Response({'error': 'Role parameter required'}, status=status.HTTP_400_BAD_REQUEST)

        has_role = request.user.groups.filter(name=role).exists()
        return Response({'has_role': has_role})
