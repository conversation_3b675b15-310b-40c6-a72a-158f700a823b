import axios from 'axios';
import { toast } from 'sonner';

// Helper function to infer program type from program code or name
const inferProgramType = (program: any): string => {
  // If program_type is already defined, use it
  if (program.program_type) {
    return program.program_type;
  }

  // If program_code is 'Undergraduate' or 'Postgraduate', use it directly
  if (program.program_code) {
    const code = program.program_code.toLowerCase();
    if (code === 'undergraduate' || code === 'postgraduate') {
      return code;
    }
  }

  const programCode = program.program_code?.toLowerCase() || '';
  const programName = program.program_name?.toLowerCase() || '';

  // Check for undergraduate indicators
  if (
    programCode.includes('undergraduate') ||
    programName.includes('undergraduate') ||
    programName.includes('bachelor') ||
    programName.includes('diploma') ||
    programCode.includes('bsc') ||
    programCode.includes('ba')
  ) {
    return 'undergraduate';
  }

  // Check for postgraduate indicators
  if (
    programCode.includes('postgraduate') ||
    programName.includes('postgraduate') ||
    programName.includes('master') ||
    programName.includes('phd') ||
    programName.includes('doctorate') ||
    programCode.includes('msc') ||
    programCode.includes('ma') ||
    programCode.includes('phd')
  ) {
    return 'postgraduate';
  }

  // Default to undergraduate if we can't determine
  return 'undergraduate';
};

import { API_BASE_URL } from '../config';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token if available
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Token ${token}`;
  }
  return config;
});

// Define interfaces
export interface Program {
  id: number;
  program_code: string;
  program_name: string;
  program_type?: string;
  registration_fee?: number;
  created_at: string;
  updated_at: string;
}

export interface RegistrationPeriod {
  id: number;
  program: number;
  program_name?: string;
  open_date: string;
  close_date: string;
  is_active: boolean | string;
  created_at: string;
  updated_at?: string;
}

export interface ApplicationInfo {
  id: number;
  college: {
    id: number;
    name: string;
  };
  department: {
    id: number;
    name: string;
  };
  program: {
    id: number;
    program_code: string;
    program_name: string;
    program_type?: string;
    registration_fee?: number | string;
  };
  field_of_study: {
    id: number;
    name: string;
  };
  study_program: {
    id: number;
    name: string;
  };
  admission_type: {
    id: number;
    name: string;
  };
  duration?: number;
  status: boolean;
}

export interface StudyField {
  id: number;
  name: string;
  code?: string;
}

export interface College {
  id: number;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// Program APIs
const programAPI = {
  // Get all programs (public endpoint)
  getPrograms: async () => {
    try {
      console.log('Attempting to fetch programs...');
      const response = await api.get('/programs/public/');
      console.log('Successfully fetched programs:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching programs:', error);
      console.warn('No programs found or API error occurred');
      return [];
    }
  },

  // Get program by ID
  getProgramById: async (id: number) => {
    try {
      const response = await api.get(`/programs/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching program with ID ${id}:`, error);
      return null;
    }
  },

  // Get application information for a program
  getApplicationInfo: async (programId: number) => {
    try {
      // Add a timestamp parameter to prevent caching
      const timestamp = new Date().getTime();
      console.log('Attempting to fetch application information...');
      // Only use the timestamp parameter to prevent caching, no custom headers
      const response = await api.get('/application-information/filter/', {
        params: programId ? { program: programId, _t: timestamp } : { _t: timestamp }
      });
      console.log('Successfully fetched application info:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching application info for program ${programId}:`, error);
      console.warn('No application information found or API error occurred');
      return [];
    }
  },

  // Get program application information with registration status
  getProgramApplication: async (programId: number) => {
    try {
      const timestamp = new Date().getTime();
      console.log(`Fetching program application info for program ID ${programId}...`);
      const response = await api.get('/program-application/', {
        params: { program_id: programId, _t: timestamp }
      });
      console.log('Successfully fetched program application info:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching program application info for program ${programId}:`, error);
      console.warn('No program application information found or API error occurred');
      return {
        application_info: [],
        registration_active: false,
        registration_message: 'Error fetching registration information.',
        registration_period: null
      };
    }
  },

  // Get all programs with their registration status and application info
  getProgramsWithStatus: async () => {
    try {
      const timestamp = new Date().getTime();
      console.log('Fetching all programs with registration status...');
      console.log('Request URL:', `${api.defaults.baseURL}/programs-with-status/`);

      try {
        const response = await api.get('/programs-with-status/', {
          params: { _t: timestamp },
          timeout: 5000 // Add timeout to prevent long-running requests
        });

        console.log('Response status:', response.status);
        console.log('Successfully fetched programs with status');

        // Map the response to include the correct structure
        const programs = response.data.map((program: any) => ({
          ...program,
          registration_status: {
            active: program.registration_active,
            message: program.registration_message
          }
        }));

        return programs;
      } catch (primaryError) {
        console.error('Error with primary endpoint, trying fallback:', primaryError);

        // Fallback to regular programs endpoint
        console.log('Using fallback endpoint: /programs/public/');
        const fallbackResponse = await api.get('/programs/public/', {
          params: { _t: timestamp }
        });

        // Transform the response to match the expected format
        const fallbackPrograms = fallbackResponse.data.map((program: any) => ({
          ...program,
          program_type: program.program_type || inferProgramType(program),
          registration_active: false,
          registration_message: "Registration status information is temporarily unavailable.",
          registration_status: {
            active: false,
            message: "Registration status information is temporarily unavailable."
          },
          application_info: []
        }));

        console.log('Successfully fetched programs from fallback endpoint');
        return fallbackPrograms;
      }

    } catch (error: any) {
      console.error('Error fetching programs with status:', error);
      console.error('Error details:', error.response?.data || 'No response data');
      console.error('Status code:', error.response?.status || 'No status code');
      console.error('Error message:', error.message);

      // Try one more fallback - just get basic programs
      try {
        console.log('Attempting final fallback to basic programs endpoint');
        const basicResponse = await api.get('/programs/public/');

        // Transform to match expected format
        const basicPrograms = basicResponse.data.map((program: any) => ({
          ...program,
          program_type: program.program_type || inferProgramType(program),
          registration_active: false,
          registration_message: "Registration status information is unavailable.",
          registration_status: {
            active: false,
            message: "Registration status information is unavailable."
          },
          application_info: []
        }));

        console.log('Successfully fetched basic programs as final fallback');
        return basicPrograms;
      } catch (finalError) {
        console.error('All fallbacks failed:', finalError);
        // Show a toast notification with the error
        toast.error(`Failed to fetch programs: ${error.message}`);
        // Return empty array as last resort
        console.warn('No programs could be fetched from any endpoint');
        return [];
      }
    }
  },

  // Get active registration periods
  getRegistrationPeriods: async () => {
    try {
      const timestamp = new Date().getTime();
      console.log('Fetching registration periods from public API...');
      const response = await api.get('/registration-periods/public/', {
        params: { _t: timestamp }
      });
      console.log('Successfully fetched registration periods from API:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching registration periods:', error);
      // Return empty array instead of mock data
      console.warn('No registration periods found or API error occurred');
      return [];
    }
  },

  // Get program types
  getProgramTypes: async () => {
    try {
      const timestamp = new Date().getTime();
      console.log('Attempting to fetch program types...');
      // Use the correct endpoint for programs
      const response = await api.get('/programs/public/', {
        params: { _t: timestamp }
      });
      console.log('Fetched programs:', response.data);

      // Define the type for the accumulator and program
      interface ProgramTypeItem {
        id: number;
        name: string;
        code: string;
      }

      // Extract unique program types from the programs data
      const programTypes = response.data.reduce((types: ProgramTypeItem[], program: Program) => {
        if (program.program_type && !types.some((t: ProgramTypeItem) => t.code === program.program_type?.toLowerCase())) {
          types.push({
            id: types.length + 1,
            name: program.program_type.charAt(0).toUpperCase() + program.program_type.slice(1),
            code: program.program_type.toLowerCase()
          });
        }
        return types;
      }, [] as ProgramTypeItem[]);

      console.log('Extracted program types:', programTypes);
      return programTypes;
    } catch (error) {
      console.error('Error fetching program types:', error);
      // Return empty array instead of default program types
      console.warn('No program types found or API error occurred');
      return [];
    }
  },

  // Get all colleges
  getColleges: async () => {
    try {
      const timestamp = new Date().getTime();
      console.log('Attempting to fetch colleges from API...');
      // Use the public endpoint that doesn't require authentication
      const response = await api.get('/colleges/public/', {
        params: { _t: timestamp }
      });
      console.log('Successfully fetched colleges from API:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching colleges:', error);
      console.warn('No colleges found or API error occurred');
      return [];
    }
  }
};

export default programAPI;
