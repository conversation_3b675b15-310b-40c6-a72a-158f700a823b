from django.contrib import admin
from .models import Service

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('service_name', 'service_fee', 'icon_name', 'is_active', 'order', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('service_name', 'description')
    ordering = ('order', 'service_name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Service Information', {
            'fields': ('service_name', 'description', 'service_fee')
        }),
        ('Display Settings', {
            'fields': ('icon_name', 'is_active', 'order')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
