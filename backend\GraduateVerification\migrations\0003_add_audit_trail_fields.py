# Generated manually to add audit trail fields

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('GraduateVerification', '0002_make_description_optional'),
    ]

    operations = [
        migrations.AddField(
            model_name='graduatestudent',
            name='created_by',
            field=models.ForeignKey(
                blank=True,
                help_text='User who created this record',
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='created_graduate_students',
                to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='graduatestudent',
            name='updated_by',
            field=models.ForeignKey(
                blank=True,
                help_text='User who last updated this record',
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='updated_graduate_students',
                to=settings.AUTH_USER_MODEL
            ),
        ),
    ]
