from rest_framework import serializers
from .models import Service

class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for the Service model.
    """
    class Meta:
        model = Service
        fields = [
            'id', 'service_name', 'description', 'service_fee', 
            'icon_name', 'is_active', 'order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class PublicServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for public access to Service model (read-only).
    """
    class Meta:
        model = Service
        fields = ['id', 'service_name', 'description', 'service_fee', 'icon_name']
