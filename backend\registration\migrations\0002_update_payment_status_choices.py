# Generated manually to update payment status choices

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('registration', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='applicantprogramselection',
            name='payment_status',
            field=models.CharField(
                choices=[
                    ('Pending', 'Pending'),
                    ('Initiated', 'Initiated'),
                    ('Processing', 'Processing'),
                    ('Completed', 'Completed'),
                    ('Failed', 'Failed'),
                    ('Verified', 'Verified'),
                    ('Expired', 'Expired'),
                    ('Refunded', 'Refunded'),
                    ('Cancelled', 'Cancelled'),
                ],
                default='Pending',
                max_length=50
            ),
        ),
    ]
