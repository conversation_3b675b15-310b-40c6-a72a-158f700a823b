from rest_framework import generics
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connection
from .models import (
    ApplicantInformation,
    ApplicantGAT,
    ApplicantProgramSelection,
    ApplicantDocumentation,
    ApplicantPayment
)
from .serializers import (
    ApplicantInformationSerializer,
    ApplicantGATSerializer,
    ApplicantProgramSelectionSerializer,
    ApplicantDocumentationSerializer,
    ApplicantPaymentSerializer
)

# ApplicantInformation views
class ApplicantInformationList(generics.ListCreateAPIView):
    serializer_class = ApplicantInformationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Filter by the current user
        return ApplicantInformation.objects.filter(author=self.request.user)

    def perform_create(self, serializer):
        # Set the author to the current user
        print(f"Creating ApplicantInformation for user: {self.request.user}")
        print(f"Serializer data: {serializer.validated_data}")

        # Check if the user already has an ApplicantInformation record
        try:
            existing_info = ApplicantInformation.objects.get(author=self.request.user)
            print(f"User already has an ApplicantInformation record with ID: {existing_info.id}")

            # Update the existing record instead of creating a new one
            for key, value in serializer.validated_data.items():
                setattr(existing_info, key, value)
            existing_info.save()
            print(f"Updated existing ApplicantInformation record")
            return existing_info
        except ApplicantInformation.DoesNotExist:
            # Create a new record if one doesn't exist
            try:
                instance = serializer.save(author=self.request.user)
                print(f"Successfully created new ApplicantInformation with ID: {instance.id}")
                return instance
            except Exception as e:
                print(f"Error creating ApplicantInformation: {str(e)}")
                raise

class ApplicantInformationDetail(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ApplicantInformationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantInformation.objects.none()
        # Filter by the current user for security
        return ApplicantInformation.objects.filter(author=self.request.user)

# ApplicantGAT views
class ApplicantGATList(generics.ListCreateAPIView):
    serializer_class = ApplicantGATSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Filter by the current user for security
        return ApplicantGAT.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        # Set the user to the current user
        print(f"Creating ApplicantGAT for user: {self.request.user}")
        print(f"Serializer data: {serializer.validated_data}")
        try:
            instance = serializer.save(user=self.request.user)
            print(f"Successfully created ApplicantGAT with ID: {instance.id}")
        except Exception as e:
            print(f"Error creating ApplicantGAT: {str(e)}")
            raise

class ApplicantGATDetail(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ApplicantGATSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantGAT.objects.none()
        # Filter by the current user for security
        return ApplicantGAT.objects.filter(user=self.request.user)

# ApplicantProgramSelection views
class ApplicantProgramSelectionList(generics.ListCreateAPIView):
    serializer_class = ApplicantProgramSelectionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantProgramSelection.objects.none()

        # Get the base queryset filtered by the current user
        queryset = ApplicantProgramSelection.objects.filter(user=self.request.user)

        # Check if we're filtering by GAT ID
        gat_id = self.request.query_params.get('gat')
        if gat_id:
            try:
                # Convert to int if it's a string
                if isinstance(gat_id, str) and gat_id.isdigit():
                    gat_id = int(gat_id)

                # Filter by GAT ID
                print(f"Filtering program selections by GAT ID: {gat_id}")
                queryset = queryset.filter(gat_id=gat_id)

                # Log the results
                print(f"Found {queryset.count()} program selections for GAT ID {gat_id}")

                # Double check the results with a direct database query
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute(
                        "SELECT id, user_id, gat_id FROM registration_applicantprogramselection WHERE user_id = %s AND gat_id = %s",
                        [self.request.user.id, gat_id]
                    )
                    rows = cursor.fetchall()
                    print(f"Database check - Found {len(rows)} program selections for user {self.request.user.id} and GAT ID {gat_id}")
                    for row in rows:
                        print(f"  - id: {row[0]}, user_id: {row[1]}, gat_id: {row[2]}")
            except Exception as e:
                print(f"Error filtering by GAT ID: {str(e)}")

        return queryset

    def perform_create(self, serializer):
        print(f"Creating program selection for user: {self.request.user}")
        print(f"Request data: {self.request.data}")
        print(f"Serializer validated data: {serializer.validated_data}")

        try:
            # Always create a new program selection record for a new application
            print("Creating new program selection record")

            # Check if gat is in the request data
            gat_id = self.request.data.get('gat')
            print(f"View - GAT ID from request data: {gat_id}")

            # Log the user for debugging
            print(f"User being passed to serializer: {self.request.user.id} - {self.request.user.username}")

            # Pass user to serializer but don't override any other fields
            instance = serializer.save(user=self.request.user)
            print(f"Successfully created new program selection record with ID: {instance.id}")

            # Log the final state of the instance
            print(f"Final instance state - user: {instance.user}, gat: {instance.gat}")

            # If gat is not set but should be, log a warning
            if 'gat' in self.request.data and not instance.gat:
                print(f"WARNING: gat field was not set despite being in request data: {self.request.data.get('gat')}")

            # Check the database directly to see what was actually saved
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT id, user_id, gat_id FROM registration_applicantprogramselection WHERE id = %s",
                    [instance.id]
                )
                row = cursor.fetchone()
                if row:
                    print(f"Database check - id: {row[0]}, user_id: {row[1]}, gat_id: {row[2]}")

                    # Always check and update the gat_id directly to ensure it's correct
                    if 'gat' in self.request.data:
                        gat_id = self.request.data.get('gat')
                        if gat_id and str(gat_id).isdigit():
                            try:
                                # Verify the GAT record exists
                                gat = ApplicantGAT.objects.get(id=gat_id, user=self.request.user)
                                print(f"Found GAT record with ID {gat.id} for user {self.request.user.id}")

                                # Always update the database directly to ensure the correct GAT ID is set
                                cursor.execute(
                                    "UPDATE registration_applicantprogramselection SET gat_id = %s WHERE id = %s",
                                    [gat.id, instance.id]
                                )
                                print(f"Direct database update - Set gat_id to {gat.id} for program selection {instance.id}")

                                # Refresh the instance from the database
                                instance.refresh_from_db()
                                print(f"Refreshed instance - gat: {instance.gat}")

                                # Double check that gat_id is set correctly
                                cursor.execute(
                                    "SELECT gat_id FROM registration_applicantprogramselection WHERE id = %s",
                                    [instance.id]
                                )
                                row = cursor.fetchone()
                                if row and row[0] == gat.id:
                                    print(f"Confirmed gat_id is correctly set to {gat.id}")
                                else:
                                    print(f"WARNING: gat_id is not set correctly. Expected {gat.id}, got {row[0] if row else 'None'}")
                            except ApplicantGAT.DoesNotExist:
                                print(f"Cannot update gat_id directly - GAT record {gat_id} not found for user {self.request.user}")

            return instance
        except Exception as e:
            print(f"Error in program selection view: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

class ApplicantProgramSelectionDetail(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ApplicantProgramSelectionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantProgramSelection.objects.none()
        # Filter by the current user for security
        return ApplicantProgramSelection.objects.filter(user=self.request.user)

    def perform_update(self, serializer):
        print(f"Updating program selection for user: {self.request.user}")
        print(f"Request data: {self.request.data}")
        print(f"Serializer validated data: {serializer.validated_data}")

        try:
            # Update the program selection record
            print("Updating program selection record")

            # Check if gat is in the request data
            gat_id = self.request.data.get('gat')
            print(f"Update View - GAT ID from request data: {gat_id}")

            # Update the instance
            instance = serializer.save()
            print(f"Successfully updated program selection record with ID: {instance.id}")

            # Log the final state of the instance
            print(f"Final updated instance state - user: {instance.user}, gat: {instance.gat}")

            # Check the database directly to see what was actually saved
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT id, user_id, gat_id FROM registration_applicantprogramselection WHERE id = %s",
                    [instance.id]
                )
                row = cursor.fetchone()
                if row:
                    print(f"Database check after update - id: {row[0]}, user_id: {row[1]}, gat_id: {row[2]}")

                    # Always check and update the gat_id directly to ensure it's correct
                    if 'gat' in self.request.data:
                        gat_id = self.request.data.get('gat')
                        if gat_id and str(gat_id).isdigit():
                            try:
                                # Verify the GAT record exists
                                gat = ApplicantGAT.objects.get(id=gat_id, user=self.request.user)
                                print(f"Update - Found GAT record with ID {gat.id} for user {self.request.user.id}")

                                # Always update the database directly to ensure the correct GAT ID is set
                                cursor.execute(
                                    "UPDATE registration_applicantprogramselection SET gat_id = %s WHERE id = %s",
                                    [gat.id, instance.id]
                                )
                                print(f"Direct database update after update - Set gat_id to {gat.id} for program selection {instance.id}")

                                # Refresh the instance from the database
                                instance.refresh_from_db()
                                print(f"Refreshed instance after update - gat: {instance.gat}")

                                # Double check that gat_id is set correctly
                                cursor.execute(
                                    "SELECT gat_id FROM registration_applicantprogramselection WHERE id = %s",
                                    [instance.id]
                                )
                                row = cursor.fetchone()
                                if row and row[0] == gat.id:
                                    print(f"Update - Confirmed gat_id is correctly set to {gat.id}")
                                else:
                                    print(f"WARNING: Update - gat_id is not set correctly. Expected {gat.id}, got {row[0] if row else 'None'}")
                            except ApplicantGAT.DoesNotExist:
                                print(f"Cannot update gat_id directly - GAT record {gat_id} not found for user {self.request.user}")

            return instance
        except Exception as e:
            print(f"Error in program selection update view: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

# ApplicantDocumentation views
class ApplicantDocumentationList(generics.ListCreateAPIView):
    serializer_class = ApplicantDocumentationSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantDocumentation.objects.none()

        # Get the queryset filtered by the current user
        # Since we've changed to a OneToOneField, there will be at most one record per user
        queryset = ApplicantDocumentation.objects.filter(user=self.request.user)

        print(f"Found {queryset.count()} documentation record(s) for user {self.request.user.id}")

        return queryset

    def perform_create(self, serializer):
        print(f"Documentation upload for user: {self.request.user}")
        try:
            # Check if a documentation record already exists for this user
            existing_doc = ApplicantDocumentation.objects.filter(user=self.request.user).first()

            if existing_doc:
                print(f"Found existing documentation record with ID: {existing_doc.id} for user {self.request.user.id}")
                print(f"Updating existing documentation record for user {self.request.user.id}")

                # Get the validated data from the serializer
                data = serializer.validated_data.copy()

                # Update only the fields that are provided in the request
                for field_name, value in data.items():
                    if value is not None:
                        print(f"Updating field '{field_name}' with new value")
                        setattr(existing_doc, field_name, value)

                # Save the updated record
                existing_doc.save()
                print(f"Successfully updated documentation record with ID: {existing_doc.id}")
                return existing_doc
            else:
                print(f"No existing documentation record found for user {self.request.user.id}")
                print(f"Creating new documentation record for user {self.request.user.id}")

                # Create a new record with the current user
                instance = serializer.save(user=self.request.user)
                print(f"Successfully created new documentation record with ID: {instance.id}")
                return instance

        except Exception as e:
            print(f"Error in documentation view: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

class ApplicantDocumentationDetail(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ApplicantDocumentationSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantDocumentation.objects.none()
        # Filter by the current user for security
        return ApplicantDocumentation.objects.filter(user=self.request.user)

    def perform_update(self, serializer):
        print(f"Updating documentation for user: {self.request.user}")
        try:
            # Get the instance being updated
            instance = self.get_object()
            print(f"Updating documentation record with ID: {instance.id}")

            # Get the validated data from the serializer
            data = serializer.validated_data.copy()

            # Update only the fields that are provided in the request
            for field_name, value in data.items():
                if value is not None:
                    print(f"Updating field '{field_name}' with new value")
                    setattr(instance, field_name, value)

            # Save the updated record
            instance.save()
            print(f"Successfully updated documentation record with ID: {instance.id}")

            return instance

        except Exception as e:
            print(f"Error in documentation update view: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

# ApplicantPayment views
class ApplicantPaymentList(generics.ListCreateAPIView):
    serializer_class = ApplicantPaymentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantPayment.objects.none()

        # Get the base queryset filtered by the current user
        queryset = ApplicantPayment.objects.filter(user=self.request.user)

        # Check if we're filtering by GAT ID
        gat_id = self.request.query_params.get('applicant_gat')
        if gat_id:
            try:
                # Convert to int if it's a string
                if isinstance(gat_id, str) and gat_id.isdigit():
                    gat_id = int(gat_id)

                # Filter by GAT ID
                print(f"Filtering payments by GAT ID: {gat_id}")
                queryset = queryset.filter(applicant_gat_id=gat_id)

                # Log the results
                print(f"Found {queryset.count()} payments for GAT ID {gat_id}")
            except Exception as e:
                print(f"Error filtering by GAT ID: {str(e)}")

        return queryset

    def perform_create(self, serializer):
        print(f"Creating payment for user: {self.request.user}")
        try:
            # Always create a new payment record for a new application
            print("Creating new payment record")
            instance = serializer.save(user=self.request.user)
            print(f"Successfully created new payment record with ID: {instance.id}")
            return instance
        except Exception as e:
            print(f"Error in payment view: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

class ApplicantPaymentDetail(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = ApplicantPaymentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Check if this is a swagger fake view
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return ApplicantPayment.objects.none()

        # Filter by the current user for security
        return ApplicantPayment.objects.filter(user=self.request.user)

# Payment by GAT ID API endpoint
class PaymentByGATView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, gat_id):
        """
        Get payment information for a specific GAT ID
        """
        try:
            # Verify the GAT record exists and belongs to the current user
            try:
                gat = ApplicantGAT.objects.get(id=gat_id, user=request.user)
            except ApplicantGAT.DoesNotExist:
                return Response(
                    {"error": f"GAT record with ID {gat_id} not found for this user."},
                    status=404
                )

            # Get the payment associated with this GAT ID
            try:
                payment = ApplicantPayment.objects.get(applicant_gat=gat)

                # Get the serialized payment data
                serializer = ApplicantPaymentSerializer(payment)
                payment_data = serializer.data

                # Add additional program information if available
                try:
                    # Try to get program selection through GAT
                    program_selection = ApplicantProgramSelection.objects.filter(gat=gat).first()

                    if program_selection and program_selection.application_info:
                        # Get application info
                        app_info = program_selection.application_info

                        # Log program and registration fee information
                        if app_info.program:
                            print(f"Program: {app_info.program.program_name}, ID: {app_info.program.id}, Code: {app_info.program.program_code}")
                            print(f"Registration Fee: {app_info.program.registration_fee}, Type: {type(app_info.program.registration_fee)}")

                            # Query the program directly to verify
                            from setups.program.models import Program
                            try:
                                program = Program.objects.get(id=app_info.program.id)
                                print(f"Direct Program Query - ID: {program.id}, Code: {program.program_code}, Name: {program.program_name}")
                                print(f"Direct Registration Fee: {program.registration_fee}, Type: {type(program.registration_fee)}")
                            except Exception as e:
                                print(f"Error querying program directly: {str(e)}")
                        else:
                            print("No program information available")

                        # Add program information to the response
                        payment_data['program_info'] = {
                            'college': app_info.college.name if app_info.college else None,
                            'department': app_info.department.name if app_info.department else None,
                            'program': app_info.program.program_name if app_info.program else None,
                            'program_id': app_info.program.id if app_info.program else None,
                            'program_code': app_info.program.program_code if app_info.program else None,
                            'admission_type': app_info.admission_type.name if app_info.admission_type else None,
                            'field_of_study': app_info.field_of_study.name if app_info.field_of_study else None,
                            'study_program': app_info.study_program.name if app_info.study_program else None,
                            'duration': app_info.duration,
                            'special_case': app_info.spacial_case,
                            'registration_fee': app_info.program.registration_fee if app_info.program else None
                        }

                        print(f"Added program info to payment response: {payment_data['program_info']}")
                except Exception as e:
                    print(f"Error getting program info: {str(e)}")
                    # Continue without program info

                return Response(payment_data)
            except ApplicantPayment.DoesNotExist:
                # If no payment exists, create a dummy payment object with program info
                try:
                    # Try to get program selection through GAT
                    program_selection = ApplicantProgramSelection.objects.filter(gat=gat).first()

                    if program_selection and program_selection.application_info:
                        # Get application info
                        app_info = program_selection.application_info

                        # Create a dummy payment response with program info
                        payment_data = {
                            'id': None,
                            'user': request.user.id,
                            'applicant_gat': gat.id,
                            'payment_amount': app_info.program.registration_fee if app_info.program else None,
                            'payment_method': 'TeleBirr',
                            'payment_status': 'Pending',
                            'payment_date': None,
                            'payment_time': None,
                            'telebirr_id': None,
                            'created_at': None,
                            'updated_at': None,
                            'program_info': {
                                'college': app_info.college.name if app_info.college else None,
                                'department': app_info.department.name if app_info.department else None,
                                'program': app_info.program.program_name if app_info.program else None,
                                'program_id': app_info.program.id if app_info.program else None,
                                'program_code': app_info.program.program_code if app_info.program else None,
                                'admission_type': app_info.admission_type.name if app_info.admission_type else None,
                                'field_of_study': app_info.field_of_study.name if app_info.field_of_study else None,
                                'study_program': app_info.study_program.name if app_info.study_program else None,
                                'duration': app_info.duration,
                                'special_case': app_info.spacial_case,
                                'registration_fee': app_info.program.registration_fee if app_info.program else None
                            }
                        }

                        print(f"Created dummy payment data with program info: {payment_data}")
                        return Response(payment_data)
                    else:
                        return Response(
                            {"message": f"No program selection found for GAT ID {gat_id}"},
                            status=404
                        )
                except Exception as e:
                    print(f"Error creating dummy payment data: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    # Return a structured response with a message but with 200 status code
                    # This allows the frontend to handle the case gracefully
                    return Response(
                        {
                            "message": f"No payment found for GAT ID {gat_id}",
                            "id": None,
                            "user": request.user.id,
                            "applicant_gat": gat.id,
                            "payment_amount": None,
                            "payment_method": None,
                            "payment_status": "Pending",
                            "exists": False
                        },
                        status=200
                    )

        except Exception as e:
            print(f"Error getting payment by GAT ID: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {"error": str(e)},
                status=500
            )

    def post(self, request, gat_id):
        """
        Create a new payment record for a specific GAT ID
        """
        try:
            # Verify the GAT record exists and belongs to the current user
            try:
                gat = ApplicantGAT.objects.get(id=gat_id, user=request.user)
            except ApplicantGAT.DoesNotExist:
                return Response(
                    {"error": f"GAT record with ID {gat_id} not found for this user."},
                    status=404
                )

            # Check if a payment already exists for this GAT
            existing_payment = ApplicantPayment.objects.filter(applicant_gat=gat).first()
            if existing_payment:
                return Response(
                    {"error": f"Payment already exists for GAT ID {gat_id}"},
                    status=400
                )

            # Get program selection and application info
            program_selection = ApplicantProgramSelection.objects.filter(gat=gat).first()
            if not program_selection or not program_selection.application_info:
                return Response(
                    {"error": "No program selection or application info found for this GAT ID"},
                    status=400
                )

            app_info = program_selection.application_info

            # Create payment data
            payment_data = {
                'user': request.user.id,
                'applicant_gat': gat.id,
                'payment_amount': app_info.program.registration_fee if app_info.program else request.data.get('payment_amount'),
                'payment_method': request.data.get('payment_method', 'TeleBirr'),
                'payment_status': request.data.get('payment_status', 'Completed'),
                'payment_date': request.data.get('payment_date'),
                'payment_time': request.data.get('payment_time'),
                'telebirr_id': request.data.get('telebirr_id')
            }

            # Create the payment record
            serializer = ApplicantPaymentSerializer(data=payment_data)
            if serializer.is_valid():
                payment = serializer.save()

                # Add program info to the response
                response_data = serializer.data
                response_data['program_info'] = {
                    'college': app_info.college.name if app_info.college else None,
                    'department': app_info.department.name if app_info.department else None,
                    'program': app_info.program.program_name if app_info.program else None,
                    'program_id': app_info.program.id if app_info.program else None,
                    'program_code': app_info.program.program_code if app_info.program else None,
                    'admission_type': app_info.admission_type.name if app_info.admission_type else None,
                    'field_of_study': app_info.field_of_study.name if app_info.field_of_study else None,
                    'study_program': app_info.study_program.name if app_info.study_program else None,
                    'duration': app_info.duration,
                    'special_case': app_info.spacial_case,
                    'registration_fee': app_info.program.registration_fee if app_info.program else None
                }

                return Response(response_data, status=201)
            else:
                return Response(serializer.errors, status=400)

        except Exception as e:
            print(f"Error creating payment: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {"error": str(e)},
                status=500
            )

# Phone Number API endpoint
class GetPhoneNumberView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get the phone number for the current user from various sources
        """
        user = request.user
        phone_number = None

        try:
            # Try to get phone number from ApplicantInformation
            applicant_info = ApplicantInformation.objects.filter(author=user).first()
            if applicant_info and hasattr(applicant_info, 'phone_number') and applicant_info.phone_number:
                phone_number = applicant_info.phone_number
                print(f"Found phone number in ApplicantInformation: {phone_number}")

            # If not found, try to get from user profile
            if not phone_number and hasattr(user, 'phone_number') and user.phone_number:
                phone_number = user.phone_number
                print(f"Found phone number in User profile: {phone_number}")

            # If still not found, try direct database query to find any phone number field
            if not phone_number:
                with connection.cursor() as cursor:
                    # Try to find phone number in applicant_information table
                    cursor.execute(
                        "SELECT phone_number FROM registration_applicantinformation WHERE author_id = %s",
                        [user.id]
                    )
                    row = cursor.fetchone()
                    if row and row[0]:
                        phone_number = row[0]
                        print(f"Found phone number in database query: {phone_number}")

            # Return the phone number if found
            return Response({
                'phone_number': phone_number or '',
                'user_id': user.id,
                'username': user.username
            })
        except Exception as e:
            print(f"Error getting phone number: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({
                'error': str(e),
                'phone_number': '',
                'user_id': user.id,
                'username': user.username
            })