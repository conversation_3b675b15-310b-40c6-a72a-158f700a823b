import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2, Search, RefreshCw, Send, AlertCircle, User, Building, Calendar, Package, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { officialSentAPI } from '@/services/officialAPI';
import { certificateTypeAPI } from '@/services/api';
import { cn } from '@/lib/utils';
import type { OfficialSent, OfficialSentCreateUpdate } from '@/services/officialAPI';

interface CertificateType {
  uuid: string;
  name: string;
  description: string;
  is_active: boolean;
}

const OfficialSentManagement = () => {
  const [sentCertificates, setSentCertificates] = useState<OfficialSent[]>([]);
  const [certificateTypes, setCertificateTypes] = useState<CertificateType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [genderFilter, setGenderFilter] = useState<string>('all');
  const [certificateTypeFilter, setCertificateTypeFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentCertificate, setCurrentCertificate] = useState<OfficialSent | null>(null);
  const [formData, setFormData] = useState<OfficialSentCreateUpdate>({
    first_name: '',
    second_name: '',
    last_name: '',
    gender: 'M',
    receiver_institute: '',
    send_date: '',
    courier: '',
    certificate_type: '',
    tracking_number: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Fetch data on component mount
  useEffect(() => {
    fetchSentCertificates();
    fetchCertificateTypes();
  }, []);

  const fetchSentCertificates = async () => {
    setLoading(true);
    try {
      // Build search parameters for server-side filtering and pagination
      const params: any = {
        page: currentPage,
        page_size: itemsPerPage,
      };

      // Add search parameter if there's a search term
      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }

      // Add gender filter if not 'all'
      if (genderFilter !== 'all') {
        params.gender = genderFilter;
      }

      // Add certificate type filter if not 'all'
      if (certificateTypeFilter !== 'all') {
        params.certificate_type = certificateTypeFilter;
      }

      const response = await officialSentAPI.getAll(params);

      // Handle paginated response
      if (response.data && typeof response.data === 'object' && 'results' in response.data) {
        setSentCertificates(response.data.results || []);
        setTotalPages(response.data.total_pages || 1);
        setTotalCount(response.data.count || 0);
      } else {
        // Fallback for non-paginated response
        setSentCertificates(response.data || []);
        setTotalPages(1);
        setTotalCount(response.data?.length || 0);
      }
    } catch (error) {
      console.error('Error fetching sent certificates:', error);
      toast.error('Failed to fetch sent certificates');
      setSentCertificates([]);
      setTotalPages(1);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  // Fetch certificates when pagination or filters change
  useEffect(() => {
    fetchSentCertificates();
  }, [currentPage, itemsPerPage, searchTerm, genderFilter, certificateTypeFilter]);

  const fetchCertificateTypes = async () => {
    try {
      const response = await certificateTypeAPI.getActiveCertificateTypes();
      setCertificateTypes(response.data || []);
    } catch (error) {
      console.error('Error fetching certificate types:', error);
      toast.error('Failed to fetch certificate types');
      setCertificateTypes([]);
    }
  };

  // Server-side filtering and pagination - data is already filtered and paginated from API
  const filteredCertificates = sentCertificates;
  const currentItems = sentCertificates;
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Reset to first page when search term or filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, genderFilter, certificateTypeFilter]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  // Calculate pagination info for display
  const indexOfFirstItem = (currentPage - 1) * itemsPerPage + 1;
  const indexOfLastItem = Math.min(currentPage * itemsPerPage, totalCount);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.first_name.trim()) {
      errors.first_name = 'First name is required';
    }

    if (!formData.second_name || !formData.second_name.trim()) {
      errors.second_name = 'Second name is required';
    }

    if (!formData.last_name.trim()) {
      errors.last_name = 'Last name is required';
    }

    if (!formData.gender || formData.gender.trim() === '') {
      errors.gender = 'Gender is required';
    }

    if (!formData.receiver_institute.trim()) {
      errors.receiver_institute = 'Receiver institute is required';
    }

    if (!formData.send_date) {
      errors.send_date = 'Send date is required';
    }

    if (!formData.courier.trim()) {
      errors.courier = 'Courier is required';
    }

    if (!formData.certificate_type || formData.certificate_type.trim() === '') {
      errors.certificate_type = 'Certificate type is required';
    }

    if (!formData.tracking_number.trim()) {
      errors.tracking_number = 'Tracking number is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      first_name: '',
      second_name: '',
      last_name: '',
      gender: 'M',
      receiver_institute: '',
      send_date: '',
      courier: '',
      certificate_type: '',
      tracking_number: '',
    });
    setFormErrors({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleAddCertificate = async () => {
    if (!validateForm()) return;

    try {
      await officialSentAPI.create(formData);
      toast.success('Sent certificate added successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchSentCertificates();
    } catch (error: any) {
      console.error('Error adding sent certificate:', error);
      if (error.response?.data) {
        const serverErrors = error.response.data;
        setFormErrors(serverErrors);
      } else {
        toast.error('Failed to add sent certificate');
      }
    }
  };

  const handleEditCertificate = async () => {
    if (!validateForm() || !currentCertificate) return;

    try {
      await officialSentAPI.update(currentCertificate.id, formData);
      toast.success('Sent certificate updated successfully');
      setIsEditDialogOpen(false);
      setCurrentCertificate(null);
      resetForm();
      fetchSentCertificates();
    } catch (error: any) {
      console.error('Error updating sent certificate:', error);
      if (error.response?.data) {
        const serverErrors = error.response.data;
        setFormErrors(serverErrors);
      } else {
        toast.error('Failed to update sent certificate');
      }
    }
  };

  const handleDeleteCertificate = async (certificate: OfficialSent) => {
    if (!confirm(`Are you sure you want to delete the certificate for ${certificate.full_name}?`)) {
      return;
    }

    try {
      await officialSentAPI.delete(certificate.id);
      toast.success('Sent certificate deleted successfully');
      fetchSentCertificates();
    } catch (error) {
      console.error('Error deleting sent certificate:', error);
      toast.error('Failed to delete sent certificate');
    }
  };

  const openEditDialog = (certificate: OfficialSent) => {
    setCurrentCertificate(certificate);
    setFormData({
      first_name: certificate.first_name,
      second_name: certificate.second_name || '',
      last_name: certificate.last_name,
      gender: certificate.gender,
      receiver_institute: certificate.receiver_institute,
      send_date: certificate.send_date,
      courier: certificate.courier,
      certificate_type: certificate.certificate_type,
      tracking_number: certificate.tracking_number,
    });
    setFormErrors({});
    setIsEditDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Send className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Sent Certificates Management</CardTitle>
                <CardDescription className="mt-1">
                  Track and manage certificates sent to external institutions
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#1a73c0] hover:bg-[#1557a0] transition-all duration-200 shadow-sm" onClick={resetForm}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Sent Certificate
                  </Button>
                </DialogTrigger>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name, institute, tracking number, or courier..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-blue-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Select value={genderFilter} onValueChange={setGenderFilter}>
                <SelectTrigger className="w-32 border-blue-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]">
                  <SelectValue placeholder="Gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Genders</SelectItem>
                  <SelectItem value="M">Male</SelectItem>
                  <SelectItem value="F">Female</SelectItem>
                </SelectContent>
              </Select>

              <Select value={certificateTypeFilter} onValueChange={setCertificateTypeFilter}>
                <SelectTrigger className="w-48 border-blue-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]">
                  <SelectValue placeholder="Certificate Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {certificateTypes.map((type) => (
                    <SelectItem key={type.uuid} value={type.uuid}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters Display */}
          {(searchTerm || genderFilter !== 'all' || certificateTypeFilter !== 'all') && (
            <div className="mb-4">
              <div className="flex flex-wrap items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="text-sm font-medium text-[#1a73c0]">Active Filters:</span>
                
                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Search: {searchTerm}
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove search filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                {genderFilter !== 'all' && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Gender: {genderFilter === 'M' ? 'Male' : 'Female'}
                    <button
                      onClick={() => setGenderFilter('all')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove gender filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                {certificateTypeFilter !== 'all' && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Type: {certificateTypes.find(t => t.uuid === certificateTypeFilter)?.name}
                    <button
                      onClick={() => setCertificateTypeFilter('all')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove certificate type filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('');
                    setGenderFilter('all');
                    setCertificateTypeFilter('all');
                  }}
                  className="text-xs h-7 px-3 ml-auto border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Clear All
                </Button>
              </div>
            </div>
          )}

          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="w-[20%] text-[#1a73c0] font-medium">Full Name</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">Gender</TableHead>
                    <TableHead className="w-[20%] text-[#1a73c0] font-medium">Receiver Institute</TableHead>
                    <TableHead className="w-[12%] text-[#1a73c0] font-medium">Send Date</TableHead>
                    <TableHead className="w-[15%] text-[#1a73c0] font-medium">Certificate Type</TableHead>
                    <TableHead className="w-[10%] text-[#1a73c0] font-medium">Tracking #</TableHead>
                    <TableHead className="w-[8%] text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-blue-100 p-3 rounded-full">
                            <RefreshCw className="h-8 w-8 text-[#1a73c0] animate-spin" />
                          </div>
                          <div className="text-[#1a73c0] font-medium">Loading sent certificates...</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">Please wait while we fetch the data from the server.</div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredCertificates.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <Send className="h-8 w-8 text-gray-500" />
                          </div>
                          <div className="text-gray-700 font-medium">No sent certificates found</div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {searchTerm || genderFilter !== 'all' || certificateTypeFilter !== 'all' ?
                              'Try adjusting your search criteria to find what you\'re looking for.' :
                              'There are no sent certificates available. Click the "Add Sent Certificate" button to create one.'}
                          </div>
                          {(searchTerm || genderFilter !== 'all' || certificateTypeFilter !== 'all') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSearchTerm('');
                                setGenderFilter('all');
                                setCertificateTypeFilter('all');
                              }}
                              className="mt-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                            >
                              Clear Filters
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentItems.map((certificate) => (
                      <TableRow key={certificate.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{certificate.full_name}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <span className={cn(
                              "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium shadow-sm border",
                              certificate.gender === 'M' 
                                ? "bg-blue-100 text-blue-800 border-blue-200" 
                                : "bg-pink-100 text-pink-800 border-pink-200"
                            )}>
                              <span className={cn(
                                "w-1.5 h-1.5 rounded-full mr-1.5",
                                certificate.gender === 'M' ? "bg-blue-600" : "bg-pink-600"
                              )}></span>
                              {certificate.gender_display}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-700">{certificate.receiver_institute}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1 text-blue-500" />
                            {new Date(certificate.send_date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                            })}
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-700">{certificate.certificate_type_name}</TableCell>
                        <TableCell>
                          <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded border">
                            {certificate.tracking_number}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(certificate)}
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                              title="Edit sent certificate"
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteCertificate(certificate)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete sent certificate"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          {totalCount > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem} - {indexOfLastItem}
                  </span> of <span className="font-medium text-[#1a73c0]">{totalCount}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Page number display */}
                  <div className="h-9 px-3 flex items-center justify-center border-r border-blue-200 bg-white text-sm">
                    <span className="text-[#1a73c0] font-medium">{currentPage}</span>
                    <span className="text-gray-500 mx-1">of</span>
                    <span className="text-[#1a73c0] font-medium">{totalPages}</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Add Sent Certificate Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Plus className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">Add New Sent Certificate</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Record a new certificate that has been sent to an external institution.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="p-6 space-y-8">
            {/* Personal Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <User className="h-4 w-4 text-[#1a73c0]" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Personal Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="first_name" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    First Name
                  </Label>
                  <Input
                    id="first_name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    placeholder="Enter first name..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.first_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.first_name ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.first_name}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter the recipient's first name</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="second_name" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Second Name
                  </Label>
                  <Input
                    id="second_name"
                    name="second_name"
                    value={formData.second_name}
                    onChange={handleInputChange}
                    placeholder="Enter second name..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.second_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.second_name ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.second_name}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter the recipient's second name</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="last_name" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Last Name
                  </Label>
                  <Input
                    id="last_name"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    placeholder="Enter last name..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.last_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.last_name ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.last_name}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Enter the recipient's last name</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="gender" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Gender
                  </Label>
                  <Select value={formData.gender} onValueChange={(value) => handleSelectChange('gender', value)}>
                    <SelectTrigger
                      id="gender"
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.gender ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    >
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="M">Male</SelectItem>
                      <SelectItem value="F">Female</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.gender ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.gender}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Select the recipient's gender</p>
                  )}
                </div>
              </div>
            </div>

            {/* Certificate Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-3 border-b border-gray-100">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Send className="h-4 w-4 text-green-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Certificate & Delivery Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="receiver_institute" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Receiver Institute
                  </Label>
                  <Input
                    id="receiver_institute"
                    name="receiver_institute"
                    value={formData.receiver_institute}
                    onChange={handleInputChange}
                    placeholder="Enter receiving institution..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.receiver_institute ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.receiver_institute ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.receiver_institute}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Name of the institution receiving the certificate</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="send_date" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Send Date
                  </Label>
                  <Input
                    id="send_date"
                    name="send_date"
                    type="date"
                    value={formData.send_date}
                    onChange={handleInputChange}
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.send_date ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.send_date ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.send_date}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Date when the certificate was sent</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="courier" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Courier
                  </Label>
                  <Input
                    id="courier"
                    name="courier"
                    value={formData.courier}
                    onChange={handleInputChange}
                    placeholder="Enter courier service..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.courier ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.courier ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.courier}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Courier service used for delivery</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="certificate_type" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Certificate Type
                  </Label>
                  <Select
                    value={formData.certificate_type}
                    onValueChange={(value) => handleSelectChange('certificate_type', value)}
                  >
                    <SelectTrigger
                      id="certificate_type"
                      className={cn(
                        "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                        formErrors.certificate_type ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                      )}
                    >
                      <SelectValue placeholder="Select certificate type" />
                    </SelectTrigger>
                    <SelectContent>
                      {certificateTypes.map((type) => (
                        <SelectItem key={type.uuid} value={type.uuid}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.certificate_type ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.certificate_type}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Type of certificate being sent</p>
                  )}
                </div>

                <div className="space-y-3 md:col-span-2">
                  <Label htmlFor="tracking_number" className="text-sm font-semibold text-gray-700 flex items-center">
                    <span className="text-red-500 mr-1">*</span>
                    Tracking Number
                  </Label>
                  <Input
                    id="tracking_number"
                    name="tracking_number"
                    value={formData.tracking_number}
                    onChange={handleInputChange}
                    placeholder="Enter tracking number..."
                    className={cn(
                      "h-11 border-gray-300 focus:border-[#1a73c0] focus:ring-[#1a73c0] transition-all duration-200",
                      formErrors.tracking_number ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                    )}
                  />
                  {formErrors.tracking_number ? (
                    <p className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.tracking_number}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500">Unique tracking number for this shipment</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-b-lg border-t border-gray-200">
            <div className="flex items-center justify-between w-full">
              <div className="text-xs text-gray-500">
                <span className="text-red-500">*</span> Required fields
              </div>
              <div className="flex space-x-3">
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="border-gray-300 hover:bg-gray-100 transition-all duration-200 px-6"
                    onClick={resetForm}
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  onClick={handleAddCertificate}
                  className="bg-[#1a73c0] hover:bg-[#1557a0] transition-all duration-200 px-6 shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Sent Certificate
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OfficialSentManagement;
