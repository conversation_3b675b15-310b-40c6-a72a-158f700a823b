# Generated by Django 5.2.1 on 2025-05-31 15:05

import django.db.models.deletion
import uuid
from django.db import migrations, models


def get_default_year():
    return '2fdb393f-75a1-4113-bb8f-08ace465de7a'


class Migration(migrations.Migration):

    dependencies = [
        ('registration_period', '0001_initial'),
        ('year', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='registrationperiod',
            name='year',
            field=models.ForeignKey(default=get_default_year, on_delete=django.db.models.deletion.CASCADE, related_name='registration_periods', to='year.year'),
            preserve_default=False,
        ),
    ]
