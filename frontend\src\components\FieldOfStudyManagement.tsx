import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Pencil, Trash2, Search, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface Department {
  id: number;
  name: string;
  code: string;
  college: number;
}

interface FieldOfStudy {
  id: number;
  name: string;
  code: string;
  department: number;
  department_name?: string;
}

const FieldOfStudyManagement = () => {
  const [fieldsOfStudy, setFieldsOfStudy] = useState<FieldOfStudy[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentFieldOfStudy, setCurrentFieldOfStudy] = useState<FieldOfStudy | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    department: '',
  });
  const [formErrors, setFormErrors] = useState({
    name: '',
    code: '',
    department: '',
  });

  // Fetch fields of study and departments on component mount
  useEffect(() => {
    fetchFieldsOfStudy();
    fetchDepartments();
  }, []);

  // Debug log to check fields of study data structure
  useEffect(() => {
    console.log('Fields of Study state:', fieldsOfStudy);
    console.log('Is Array?', Array.isArray(fieldsOfStudy));
  }, [fieldsOfStudy]);

  // Debug log to check departments data structure
  useEffect(() => {
    console.log('Departments state:', departments);
    console.log('Is Array?', Array.isArray(departments));
  }, [departments]);

  const fetchFieldsOfStudy = async () => {
    setLoading(true);
    try {
      console.log('Attempting to fetch fields of study data from all possible endpoints');

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try verification-fields-of-study endpoints
        { url: 'http://localhost:8000/api/verification-fields-of-study/', auth: true },
        { url: 'http://localhost:8000/api/verification-fields-of-study/public/', auth: false },
        // Try regular fields-of-study endpoints
        { url: 'http://localhost:8000/api/fields-of-study/', auth: true },
        { url: 'http://localhost:8000/api/fields-of-study/public/', auth: false },
      ];

      let fieldsOfStudyData = null;

      // Try each endpoint until one works
      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint.url}`);

          const headers: Record<string, string> = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            const token = localStorage.getItem('token');
            if (token) {
              headers['Authorization'] = `Bearer ${token}`;
            }
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`Successfully fetched data from ${endpoint.url}:`, data);
            fieldsOfStudyData = data;
            break; // Exit the loop if we got data successfully
          } else {
            console.log(`Endpoint ${endpoint.url} failed with status: ${response.status}`);
          }
        } catch (endpointError) {
          console.error(`Error fetching from ${endpoint.url}:`, endpointError);
        }
      }

      if (!fieldsOfStudyData) {
        throw new Error('All endpoints failed to return data');
      }

      // Store the raw response data and set fields of study
      setFieldsOfStudy(fieldsOfStudyData);
      console.log('Fields of Study set with real data, length:', fieldsOfStudyData.length);

      // If no fields of study were found, show a message to the user
      if (fieldsOfStudyData.length === 0) {
        toast.info('No fields of study found in the database.');
      }
    } catch (error: any) {
      console.error('Error fetching fields of study:', error);
      toast.error(`Failed to fetch fields of study: ${error.message}`);
      setFieldsOfStudy([]); // Ensure fields of study is always an array
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      console.log('Attempting to fetch department data for dropdown');

      // Try all possible endpoints in sequence
      const endpoints = [
        // Try verification-departments endpoints
        { url: 'http://localhost:8000/api/verification-departments/', auth: true },
        { url: 'http://localhost:8000/api/verification-departments/public/', auth: false },
        // Try regular departments endpoints
        { url: 'http://localhost:8000/api/departments/', auth: true },
        { url: 'http://localhost:8000/api/departments/public/', auth: false },
      ];

      let departmentData = null;

      // Try each endpoint until one works
      for (const endpoint of endpoints) {
        try {
          console.log(`Trying department endpoint: ${endpoint.url}`);

          const headers: Record<string, string> = {
            'Accept': 'application/json'
          };

          if (endpoint.auth) {
            const token = localStorage.getItem('token');
            if (token) {
              headers['Authorization'] = `Bearer ${token}`;
            }
          }

          const response = await fetch(endpoint.url, {
            method: 'GET',
            headers
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`Successfully fetched department data from ${endpoint.url}:`, data);
            departmentData = data;
            break; // Exit the loop if we got data successfully
          } else {
            console.log(`Department endpoint ${endpoint.url} failed with status: ${response.status}`);
          }
        } catch (endpointError) {
          console.error(`Error fetching from ${endpoint.url}:`, endpointError);
        }
      }

      if (!departmentData) {
        throw new Error('All department endpoints failed to return data');
      }

      // Set departments data
      setDepartments(departmentData);
      console.log('Departments set for dropdown, length:', departmentData.length);
    } catch (error: any) {
      console.error('Error fetching departments for dropdown:', error);
      toast.error(`Failed to fetch departments: ${error.message}`);
      setDepartments([]); // Ensure departments is always an array
    }
  };

  // Helper functions for form handling
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    // Auto-format code to uppercase
    let newValue = value;
    if (name === 'code') {
      newValue = value.toUpperCase();
    }

    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : newValue,
    });

    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: ''
      });
    }
  };

  const handleSelectChange = (value: string) => {
    setFormData({
      ...formData,
      department: value,
    });

    // Clear error when user selects a department
    if (formErrors.department) {
      setFormErrors({
        ...formErrors,
        department: ''
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    let valid = true;
    const newErrors = { name: '', code: '', department: '' };

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
      valid = false;
    } else if (formData.name.length > 200) {
      newErrors.name = 'Name must be less than 200 characters';
      valid = false;
    }

    // Validate code
    if (!formData.code.trim()) {
      newErrors.code = 'Code is required';
      valid = false;
    } else if (formData.code.length > 20) {
      newErrors.code = 'Code must be less than 20 characters';
      valid = false;
    } else if (!/^[A-Za-z0-9]+$/.test(formData.code.trim())) {
      newErrors.code = 'Code must contain only letters and numbers';
      valid = false;
    } else if (formData.code.length < 2) {
      newErrors.code = 'Code must be at least 2 characters';
      valid = false;
    } else {
      // Check for duplicate code within the same department
      if (!currentFieldOfStudy && Array.isArray(fieldsOfStudy)) {
        const existingFieldOfStudy = fieldsOfStudy.find(
          (fos) =>
            fos.code.toUpperCase() === formData.code.toUpperCase() &&
            fos.department.toString() === formData.department
        );
        if (existingFieldOfStudy) {
          newErrors.code = 'This code is already in use for this department. Codes must be unique within a department.';
          valid = false;
        }
      }
      // When editing, check if the code is changed and if it's already in use
      else if (currentFieldOfStudy && Array.isArray(fieldsOfStudy)) {
        if (currentFieldOfStudy.code.toUpperCase() !== formData.code.toUpperCase() ||
            currentFieldOfStudy.department.toString() !== formData.department) {
          const existingFieldOfStudy = fieldsOfStudy.find(
            (fos) =>
              fos.id !== currentFieldOfStudy.id &&
              fos.code.toUpperCase() === formData.code.toUpperCase() &&
              fos.department.toString() === formData.department
          );
          if (existingFieldOfStudy) {
            newErrors.code = 'This code is already in use for this department. Codes must be unique within a department.';
            valid = false;
          }
        }
      }
    }

    // Validate department
    if (!formData.department) {
      newErrors.department = 'Department is required';
      valid = false;
    }

    setFormErrors(newErrors);
    return valid;
  };

  // Helper function to determine the correct API endpoint
  const getApiEndpoint = async () => {
    const token = localStorage.getItem('token');
    if (!token) return null;

    // Try all possible endpoints to find which one works
    const endpoints = [
      'http://localhost:8000/api/verification-fields-of-study/',
      'http://localhost:8000/api/fields-of-study/'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          console.log(`Found working endpoint: ${endpoint}`);
          return endpoint;
        }
      } catch (error) {
        console.error(`Error testing endpoint ${endpoint}:`, error);
      }
    }

    return null;
  };

  const handleAddFieldOfStudy = async () => {
    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to add a field of study');
        return;
      }

      // Determine the correct endpoint
      const endpoint = await getApiEndpoint();

      if (!endpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                newErrors[field as keyof typeof newErrors] = Array.isArray(errors) ? errors[0] : errors.toString();
                hasFieldErrors = true;
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Field of study added successfully');
      setIsAddDialogOpen(false);
      resetForm();
      fetchFieldsOfStudy(); // Refresh the list after adding
    } catch (error) {
      console.error('Error adding field of study:', error);
      toast.error(`Failed to add field of study: ${error.message}`);
    }
  };

  const handleEditFieldOfStudy = async () => {
    if (!currentFieldOfStudy) return;

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to edit a field of study');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentFieldOfStudy.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        // Try to parse error response
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();

          // Handle field-specific errors
          if (typeof errorData === 'object') {
            const newErrors = { ...formErrors };
            let hasFieldErrors = false;

            // Check for field-specific errors
            for (const [field, errors] of Object.entries(errorData)) {
              if (field in newErrors) {
                newErrors[field as keyof typeof newErrors] = Array.isArray(errors) ? errors[0] : errors.toString();
                hasFieldErrors = true;
              }
            }

            if (hasFieldErrors) {
              setFormErrors(newErrors);
              throw new Error('Please fix the errors in the form');
            } else if (errorData.detail) {
              throw new Error(errorData.detail);
            }
          }
        }

        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      toast.success('Field of study updated successfully');
      setIsEditDialogOpen(false);
      resetForm();
      fetchFieldsOfStudy(); // Refresh the list after updating
    } catch (error) {
      console.error('Error updating field of study:', error);
      toast.error(`Failed to update field of study: ${error.message}`);
    }
  };

  const handleDeleteFieldOfStudy = async () => {
    if (!currentFieldOfStudy) return;

    try {
      // Use the current user's token from localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        toast.error('You must be logged in to delete a field of study');
        return;
      }

      // Determine the correct endpoint
      const baseEndpoint = await getApiEndpoint();

      if (!baseEndpoint) {
        toast.error('Could not find a working API endpoint');
        return;
      }

      // Make the request with the current token
      const response = await fetch(`${baseEndpoint}${currentFieldOfStudy.id}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `HTTP error! Status: ${response.status}`);
      }

      toast.success('Field of study deleted successfully');
      setIsDeleteDialogOpen(false);
      fetchFieldsOfStudy(); // Refresh the list after deleting
    } catch (error) {
      console.error('Error deleting field of study:', error);
      toast.error(`Failed to delete field of study: ${error.message}`);
    }
  };

  const openEditDialog = (fieldOfStudy: FieldOfStudy) => {
    setCurrentFieldOfStudy(fieldOfStudy);
    setFormData({
      name: fieldOfStudy.name,
      code: fieldOfStudy.code,
      department: fieldOfStudy.department.toString(),
    });
    setFormErrors({
      name: '',
      code: '',
      department: '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (fieldOfStudy: FieldOfStudy) => {
    setCurrentFieldOfStudy(fieldOfStudy);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      department: '',
    });
    setFormErrors({
      name: '',
      code: '',
      department: '',
    });
    setCurrentFieldOfStudy(null);
  };

  // Get department name by ID
  const getDepartmentName = (departmentId: number) => {
    const department = departments.find(d => d.id === departmentId);
    return department ? department.name : 'Unknown Department';
  };

  // Safely filter fields of study, ensuring fieldsOfStudy is an array
  const filteredFieldsOfStudy = Array.isArray(fieldsOfStudy)
    ? fieldsOfStudy.filter(
        (fieldOfStudy) =>
          fieldOfStudy && (
            (fieldOfStudy.name && fieldOfStudy.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (fieldOfStudy.code && fieldOfStudy.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (fieldOfStudy.department_name && fieldOfStudy.department_name.toLowerCase().includes(searchTerm.toLowerCase()))
          )
      )
    : [];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Fields of Study Management</CardTitle>
              <CardDescription>
                View fields of study for graduate verification
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={fetchFieldsOfStudy}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Field of Study
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Field of Study</DialogTitle>
                    <DialogDescription>
                      Enter the details for the new field of study
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="department" className="text-right">
                        Department
                      </Label>
                      <div className="col-span-3 space-y-1">
                        <Select
                          value={formData.department}
                          onValueChange={handleSelectChange}
                        >
                          <SelectTrigger className={cn(formErrors.department ? 'border-red-500' : '')}>
                            <SelectValue placeholder="Select a department" />
                          </SelectTrigger>
                          <SelectContent>
                            {departments.map((department) => (
                              <SelectItem key={department.id} value={department.id.toString()}>
                                {department.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {formErrors.department ? (
                          <p className="text-red-500 text-xs">{formErrors.department}</p>
                        ) : (
                          <p className="text-gray-500 text-xs">Select the department this field of study belongs to</p>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">
                        Name
                      </Label>
                      <div className="col-span-3 space-y-1">
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className={formErrors.name ? 'border-red-500' : ''}
                          placeholder="e.g. Computer Science"
                        />
                        {formErrors.name ? (
                          <p className="text-red-500 text-xs">{formErrors.name}</p>
                        ) : (
                          <p className="text-gray-500 text-xs">Enter the full name of the field of study (max 200 characters)</p>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="code" className="text-right">
                        Code
                      </Label>
                      <div className="col-span-3 space-y-1">
                        <Input
                          id="code"
                          name="code"
                          value={formData.code}
                          onChange={handleInputChange}
                          className={formErrors.code ? 'border-red-500' : ''}
                          placeholder="e.g. CS, ENG, BIO"
                        />
                        {formErrors.code ? (
                          <p className="text-red-500 text-xs">{formErrors.code}</p>
                        ) : (
                          <p className="text-gray-500 text-xs">Use 2-20 alphanumeric characters (letters and numbers only)</p>
                        )}
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button variant="outline">Cancel</Button>
                    </DialogClose>
                    <Button onClick={handleAddFieldOfStudy}>Save</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search fields of study..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Department</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8">
                      <div className="flex justify-center items-center">
                        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                        Loading fields of study...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredFieldsOfStudy.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8">
                      No fields of study found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredFieldsOfStudy.map((fieldOfStudy) => (
                    <TableRow key={fieldOfStudy.id}>
                      <TableCell>{fieldOfStudy.department_name || getDepartmentName(fieldOfStudy.department)}</TableCell>
                      <TableCell className="font-medium">{fieldOfStudy.name}</TableCell>
                      <TableCell>{fieldOfStudy.code}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(fieldOfStudy)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => openDeleteDialog(fieldOfStudy)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {filteredFieldsOfStudy.length} of {Array.isArray(fieldsOfStudy) ? fieldsOfStudy.length : 0} fields of study
          </div>
        </CardFooter>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Field of Study</DialogTitle>
            <DialogDescription>
              Update the field of study information
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-department" className="text-right">
                Department
              </Label>
              <div className="col-span-3 space-y-1">
                <Select
                  value={formData.department}
                  onValueChange={handleSelectChange}
                >
                  <SelectTrigger className={cn(formErrors.department ? 'border-red-500' : '')}>
                    <SelectValue placeholder="Select a department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((department) => (
                      <SelectItem key={department.id} value={department.id.toString()}>
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formErrors.department ? (
                  <p className="text-red-500 text-xs">{formErrors.department}</p>
                ) : (
                  <p className="text-gray-500 text-xs">Select the department this field of study belongs to</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Name
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={formErrors.name ? 'border-red-500' : ''}
                  placeholder="e.g. Computer Science"
                />
                {formErrors.name ? (
                  <p className="text-red-500 text-xs">{formErrors.name}</p>
                ) : (
                  <p className="text-gray-500 text-xs">Enter the full name of the field of study (max 200 characters)</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-code" className="text-right">
                Code
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="edit-code"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  className={formErrors.code ? 'border-red-500' : ''}
                  placeholder="e.g. CS, ENG, BIO"
                />
                {formErrors.code ? (
                  <p className="text-red-500 text-xs">{formErrors.code}</p>
                ) : (
                  <p className="text-gray-500 text-xs">Use 2-20 alphanumeric characters (letters and numbers only)</p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button onClick={handleEditFieldOfStudy}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the field of study "{currentFieldOfStudy?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteFieldOfStudy}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FieldOfStudyManagement;
