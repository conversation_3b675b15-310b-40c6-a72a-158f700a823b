/**
 * Authentication utility functions
 */

/**
 * Get the authentication header for API requests
 * @returns Object with Authorization header or empty object if no token
 */
export const getAuthHeader = () => {
  const token = localStorage.getItem('token');
  if (token) {
    return {
      Authorization: `Bear<PERSON> ${token}`
    };
  }
  return {};
};

/**
 * Check if the user is authenticated
 * @returns Boolean indicating if the user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token');
  return !!token;
};

/**
 * Get the current user's role
 * @returns String representing the user's role or null if not authenticated
 */
export const getUserRole = (): string | null => {
  const userString = localStorage.getItem('user');
  if (userString) {
    try {
      const user = JSON.parse(userString);
      return user.role || null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }
  return null;
};

/**
 * Check if the current user is staff
 * @returns Boolean indicating if the user is staff
 */
export const isStaff = (): boolean => {
  const userString = localStorage.getItem('user');
  if (userString) {
    try {
      const user = JSON.parse(userString);
      return user.is_staff === true;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return false;
    }
  }
  return false;
};

/**
 * Get the current user's ID
 * @returns User ID or null if not authenticated
 */
export const getUserId = (): number | null => {
  const userString = localStorage.getItem('user');
  if (userString) {
    try {
      const user = JSON.parse(userString);
      return user.id || null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }
  return null;
};

/**
 * Get the current user's data
 * @returns User object or null if not authenticated
 */
export const getUser = () => {
  const userString = localStorage.getItem('user');
  if (userString) {
    try {
      return JSON.parse(userString);
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }
  return null;
};

/**
 * Log out the current user
 */
export const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('refreshToken');
};
