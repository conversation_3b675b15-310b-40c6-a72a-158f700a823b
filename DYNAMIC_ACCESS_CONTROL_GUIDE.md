# 🎛️ Dynamic Access Control System Guide

## Overview

This guide demonstrates how to control page access and features dynamically in your React frontend based on user authentication status and roles/permissions. The system provides granular, component-level control over what users can see and do.

## 🏗️ System Architecture

### 1. **PermissionGate Component** - Conditional Rendering
Controls whether components are rendered based on user permissions.

### 2. **Dynamic Hooks** - Access Logic
Provides reusable logic for checking permissions and controlling behavior.

### 3. **Dynamic Components** - Smart UI Elements
UI components that automatically adapt based on user permissions.

### 4. **Page Access Control** - Route Protection
Automatic page-level access control with redirects.

## 🔧 Core Components

### 1. PermissionGate Component

**Basic Usage:**
```tsx
import { PermissionGate } from '@/components/PermissionGate';

// Simple permission check
<PermissionGate permissions={['user.view_user']}>
  <UserList />
</PermissionGate>

// Group-based access
<PermissionGate groups={['Administrator']}>
  <AdminPanel />
</PermissionGate>

// Multiple requirements
<PermissionGate 
  permissions={['user.view_user', 'user.change_user']}
  groups={['Admin', 'Manager']}
  requireAll={false} // ANY permission OR group
>
  <UserManagement />
</PermissionGate>
```

**Advanced Options:**
```tsx
<PermissionGate
  permissions={['sensitive.data']}
  fallback={<div>Access Denied</div>}
  hideWhenDenied={false}
  showPlaceholder={true}
  placeholderText="Restricted Content"
  feature="sensitive-data"
  logAccess={true}
  customCheck={(user) => user.department === 'IT'}
>
  <SensitiveData />
</PermissionGate>
```

### 2. Convenience Components

```tsx
import { 
  AuthRequired, 
  StaffOnly, 
  SuperuserOnly, 
  HasPermission, 
  HasRole,
  FeatureFlag 
} from '@/components/PermissionGate';

// Authentication required
<AuthRequired fallback={<LoginPrompt />}>
  <Dashboard />
</AuthRequired>

// Staff only content
<StaffOnly fallback={<div>Staff access required</div>}>
  <AdminTools />
</StaffOnly>

// Specific permissions
<HasPermission permissions={['user.delete_user']}>
  <DeleteButton />
</HasPermission>

// Role-based access
<HasRole roles={['Manager', 'Admin']}>
  <ManagementPanel />
</HasRole>

// Feature flags
<FeatureFlag 
  feature="beta-features" 
  groups={['Beta Testers']}
  fallback={<div>Feature not available</div>}
>
  <BetaFeature />
</FeatureFlag>
```

## 🎯 Page Access Control

### usePageAccess Hook

**Basic Page Protection:**
```tsx
import { usePageAccess } from '@/hooks/usePageAccess';

const AdminPage = () => {
  const { hasAccess, isLoading, redirecting } = usePageAccess({
    requireStaff: true,
    permissions: ['admin.access'],
    redirectTo: '/dashboard'
  });

  if (isLoading) return <LoadingSpinner />;
  if (redirecting) return <div>Redirecting...</div>;
  if (!hasAccess) return <AccessDenied />;

  return <AdminContent />;
};
```

**Advanced Configuration:**
```tsx
const { hasAccess, accessDeniedReason } = usePageAccess({
  requireAuth: true,
  requireActive: true,
  requireStaff: true,
  permissions: ['user.view_user', 'user.change_user'],
  groups: ['Admin', 'Manager'],
  requireAll: false,
  redirectWhenDenied: true,
  redirectTo: '/unauthorized',
  customCheck: (user) => user.department === 'HR',
  showLoadingWhileChecking: true
});
```

**Convenience Hooks:**
```tsx
import { 
  useAuthRequired, 
  useStaffRequired, 
  useSuperuserRequired,
  usePermissionRequired,
  useRoleRequired 
} from '@/hooks/usePageAccess';

// Simple authentication check
const { hasAccess } = useAuthRequired('/login');

// Staff requirement
const { hasAccess } = useStaffRequired('/dashboard');

// Specific permissions
const { hasAccess } = usePermissionRequired(['user.manage'], false, '/admin');

// Role requirement
const { hasAccess } = useRoleRequired(['Admin'], true, '/dashboard');
```

## 🎨 Dynamic UI Components

### Dynamic Buttons

**Action-Based Buttons:**
```tsx
import { 
  DynamicButton, 
  ViewButton, 
  EditButton, 
  DeleteButton, 
  CreateButton 
} from '@/components/DynamicButton';

// Automatically checks 'edit' permissions for 'user' feature
<EditButton 
  feature="user"
  onClick={() => editUser(user)}
  permissions={['user.change_user']}
/>

// Custom dynamic button
<DynamicButton
  feature="graduate"
  action="delete"
  permissions={['graduate.delete']}
  groups={['Admin']}
  hideWhenDenied={false}
  showTooltipWhenDenied={true}
  deniedTooltip="You need admin access to delete"
  onClick={() => deleteGraduate()}
>
  Delete Graduate
</DynamicButton>
```

### Dynamic Tables

**Permission-Controlled Tables:**
```tsx
import { DynamicTable } from '@/components/DynamicTable';

const columns = [
  {
    key: 'name',
    label: 'Name',
    permissions: ['user.view_user'] // Column only visible with permission
  },
  {
    key: 'email',
    label: 'Email',
    groups: ['Admin', 'HR'], // Column only visible to specific groups
    hideWhenDenied: true
  }
];

const actions = [
  {
    label: 'Edit',
    action: 'edit',
    onClick: (row) => editUser(row),
    permissions: ['user.change_user']
  },
  {
    label: 'Delete',
    action: 'delete',
    onClick: (row) => deleteUser(row),
    permissions: ['user.delete_user'],
    customCheck: (row, user) => row.department === user.department
  }
];

<DynamicTable
  data={users}
  columns={columns}
  actions={actions}
  feature="user"
  permissions={['user.view_user']}
  rowPermissionCheck={(row, user) => row.visible_to_user}
/>
```

## 🎛️ Feature Access Control

### useFeatureAccess Hook

**Granular Feature Control:**
```tsx
import { useFeatureAccess } from '@/hooks/useFeatureAccess';

const UserManagement = () => {
  const userAccess = useFeatureAccess('user', {
    permissions: ['user.view_user'],
    groups: ['Admin', 'HR']
  });

  return (
    <div>
      {userAccess.canView && <UserList />}
      {userAccess.canCreate && <CreateUserButton />}
      {userAccess.canEdit && <EditUserButton />}
      {userAccess.canDelete && <DeleteUserButton />}
      
      <div>Access Level: {userAccess.accessLevel}</div>
      {userAccess.deniedReason && (
        <div>Restriction: {userAccess.deniedReason}</div>
      )}
    </div>
  );
};
```

**Predefined Feature Hooks:**
```tsx
import { 
  useGraduateAccess, 
  useCollegeAccess, 
  useUserManagementAccess 
} from '@/hooks/useFeatureAccess';

const Dashboard = () => {
  const graduateAccess = useGraduateAccess();
  const collegeAccess = useCollegeAccess();
  const userAccess = useUserManagementAccess();

  return (
    <div>
      {graduateAccess.canView && <GraduateSection />}
      {collegeAccess.canManage && <CollegeManagement />}
      {userAccess.hasFullAccess && <UserAdministration />}
    </div>
  );
};
```

## 📋 Real-World Examples

### 1. Graduate Management Page

```tsx
const GraduateManagement = () => {
  // Page-level access control
  const { hasAccess, isLoading } = usePageAccess({
    requireStaff: true,
    groups: ['Verification Clerk', 'Registrar', 'Admin'],
    redirectTo: '/dashboard'
  });

  // Feature-level access control
  const graduateAccess = useGraduateAccess();

  if (isLoading) return <LoadingSpinner />;
  if (!hasAccess) return <AccessDenied />;

  return (
    <div>
      <h1>Graduate Management</h1>
      
      {/* Create button - only for users who can create */}
      <CreateButton 
        feature="graduate"
        permissions={['graduate.add']}
        onClick={() => createGraduate()}
      />

      {/* Data table with permission-controlled columns and actions */}
      <DynamicTable
        data={graduates}
        columns={graduateColumns}
        actions={graduateActions}
        feature="graduate"
      />

      {/* Advanced features for admins only */}
      <PermissionGate groups={['Admin']}>
        <AdvancedGraduateTools />
      </PermissionGate>
    </div>
  );
};
```

### 2. User Profile with Conditional Features

```tsx
const UserProfile = ({ userId }) => {
  const userAccess = useFeatureAccess('user');
  const isOwnProfile = user?.id === userId;

  return (
    <div>
      <UserBasicInfo userId={userId} />
      
      {/* Edit button - own profile OR has edit permission */}
      <PermissionGate
        customCheck={(user) => isOwnProfile || userAccess.canEdit}
        fallback={<div>Cannot edit this profile</div>}
      >
        <EditProfileButton />
      </PermissionGate>

      {/* Sensitive information - admin only */}
      <HasRole roles={['Admin']}>
        <UserSensitiveData userId={userId} />
      </HasRole>

      {/* Delete button - admin only, not own profile */}
      <PermissionGate
        permissions={['user.delete_user']}
        customCheck={(user) => !isOwnProfile}
      >
        <DeleteUserButton userId={userId} />
      </PermissionGate>
    </div>
  );
};
```

## 🧪 Testing Your Access Control

### Demo Page
Visit `/graduate-admin?tab=dynamic-access-demo` to see:

1. **Permission Components** - See how different gates work
2. **Dynamic Buttons** - Test action-based button controls
3. **Dynamic Tables** - View permission-controlled data tables
4. **Feature Access** - Check granular feature permissions

### Debug Tools
- **User Debug** (`?tab=user-debug`) - View user object and permissions
- **Permission Matrix** (`?tab=permission-matrix`) - See all menu access requirements
- **Access Control Demo** (`?tab=access-control-demo`) - Current user status

## 🎯 Best Practices

### 1. **Layered Security**
```tsx
// Page level
const { hasAccess } = usePageAccess({ requireStaff: true });

// Component level
<PermissionGate permissions={['sensitive.data']}>
  // Feature level
  const access = useFeatureAccess('sensitive');
  
  // Action level
  <DynamicButton action="delete" feature="sensitive">
    Delete
  </DynamicButton>
</PermissionGate>
```

### 2. **Graceful Degradation**
```tsx
<PermissionGate 
  permissions={['advanced.features']}
  fallback={<BasicFeatures />}
>
  <AdvancedFeatures />
</PermissionGate>
```

### 3. **User Feedback**
```tsx
<DynamicButton
  permissions={['admin.action']}
  showTooltipWhenDenied={true}
  deniedTooltip="Contact your administrator for access"
>
  Admin Action
</DynamicButton>
```

## 🚀 Benefits

✅ **Granular Control** - Component-level permission checking  
✅ **Automatic UI Updates** - UI adapts to user permissions  
✅ **Consistent Security** - Centralized permission logic  
✅ **Developer Friendly** - Easy to implement and maintain  
✅ **User Experience** - Clear feedback and graceful degradation  
✅ **Performance** - Efficient permission checking with memoization  

The dynamic access control system provides comprehensive, flexible, and user-friendly permission management for your React application! 🎉
