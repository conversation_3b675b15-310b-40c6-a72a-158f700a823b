import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { GraduationCap, Award, BookOpen } from 'lucide-react';

interface StatsCardsProps {
  totalGraduates: number;
  graduatesThisYear: number;
  averageGPA: number;
  currentYear: number;
  genderData: any[];
  genderGpaData: any[];
  colors: {
    gender: string[];
  };
}

const StatsCards: React.FC<StatsCardsProps> = ({
  totalGraduates,
  graduatesThisYear,
  averageGPA,
  currentYear,
  genderData,
  genderGpaData,
  colors
}) => {
  // Memoize calculations to avoid unnecessary re-renders
  const maleGraduates = React.useMemo(() => 
    genderData.find(g => g.name === 'Male')?.value || 0, 
    [genderData]
  );
  
  const femaleGraduates = React.useMemo(() => 
    genderData.find(g => g.name === 'Female')?.value || 0, 
    [genderData]
  );
  
  const malePercentage = React.useMemo(() => 
    totalGraduates > 0 ? (maleGraduates / totalGraduates * 100) : 0, 
    [maleGraduates, totalGraduates]
  );
  
  const femalePercentage = React.useMemo(() => 
    totalGraduates > 0 ? (femaleGraduates / totalGraduates * 100) : 0, 
    [femaleGraduates, totalGraduates]
  );
  
  const maleGPA = React.useMemo(() => 
    genderGpaData.find(g => g.gender === 'Male')?.avg_gpa || 0, 
    [genderGpaData]
  );
  
  const femaleGPA = React.useMemo(() => 
    genderGpaData.find(g => g.gender === 'Female')?.avg_gpa || 0, 
    [genderGpaData]
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Total Graduates Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Total Graduates</CardTitle>
          <CardDescription>All graduates in the system by gender</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div>
                <div className="text-sm text-gray-500">Total</div>
                <div className="text-3xl font-bold text-[#1a73c0]">{totalGraduates.toLocaleString()}</div>
              </div>
            </div>

            <div className="pt-2 border-t">
              <div className="text-sm font-medium mb-2">By Gender</div>
              <div className="flex flex-col space-y-3">
                {/* Male Graduates Bar */}
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full mr-2" style={{ backgroundColor: colors.gender[0] }}></div>
                      <span>Male</span>
                    </div>
                    <div className="font-medium">
                      {maleGraduates.toLocaleString()}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${malePercentage}%`,
                        backgroundColor: colors.gender[0]
                      }}
                    ></div>
                  </div>
                </div>

                {/* Female Graduates Bar */}
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full mr-2" style={{ backgroundColor: colors.gender[1] }}></div>
                      <span>Female</span>
                    </div>
                    <div className="font-medium">
                      {femaleGraduates.toLocaleString()}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${femalePercentage}%`,
                        backgroundColor: colors.gender[1]
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Graduates This Year Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Graduates This Year</CardTitle>
          <CardDescription>Class of {currentYear} by gender</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div>
                <div className="text-sm text-gray-500">Total</div>
                <div className="text-3xl font-bold text-[#1a73c0]">{graduatesThisYear}</div>
              </div>
            </div>

            <div className="pt-2 border-t">
              <div className="text-sm font-medium mb-2">By Gender</div>
              <div className="flex flex-col space-y-3">
                {/* Male Graduates Bar */}
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full mr-2" style={{ backgroundColor: colors.gender[0] }}></div>
                      <span>Male</span>
                    </div>
                    <div className="font-medium">
                      {Math.round(graduatesThisYear * (malePercentage / 100)).toLocaleString()}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${malePercentage}%`,
                        backgroundColor: colors.gender[0]
                      }}
                    ></div>
                  </div>
                </div>

                {/* Female Graduates Bar */}
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full mr-2" style={{ backgroundColor: colors.gender[1] }}></div>
                      <span>Female</span>
                    </div>
                    <div className="font-medium">
                      {Math.round(graduatesThisYear * (femalePercentage / 100)).toLocaleString()}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${femalePercentage}%`,
                        backgroundColor: colors.gender[1]
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Average GPA Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Average GPA</CardTitle>
          <CardDescription>System-wide academic performance by gender</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-[#1a73c0] mr-3" />
              <div>
                <div className="text-sm text-gray-500">System-wide</div>
                <div className="text-3xl font-bold text-[#1a73c0]">{averageGPA.toFixed(2)}</div>
              </div>
            </div>

            <div className="pt-2 border-t">
              <div className="text-sm font-medium mb-2">By Gender</div>
              <div className="flex flex-col space-y-3">
                {/* Male GPA Bar */}
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full mr-2" style={{ backgroundColor: colors.gender[0] }}></div>
                      <span>Male</span>
                    </div>
                    <div className="font-medium">
                      {maleGPA.toFixed(2)}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${(maleGPA / 4) * 100}%`,
                        backgroundColor: colors.gender[0]
                      }}
                    ></div>
                  </div>
                </div>

                {/* Female GPA Bar */}
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <div className="flex items-center">
                      <div className="h-3 w-3 rounded-full mr-2" style={{ backgroundColor: colors.gender[1] }}></div>
                      <span>Female</span>
                    </div>
                    <div className="font-medium">
                      {femaleGPA.toFixed(2)}
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${(femaleGPA / 4) * 100}%`,
                        backgroundColor: colors.gender[1]
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StatsCards;
