import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Users, 
  Shield, 
  Key, 
  Plus, 
  Search, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Code,
  Database,
  Server
} from 'lucide-react';

const DjangoAuthDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('users');

  // Demo data
  const demoUsers = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: 'Administrator',
      is_active: true,
      is_staff: true,
      is_superuser: true,
      groups: ['Administrators'],
      last_login: '2025-01-24'
    },
    {
      id: 2,
      username: 'registrar',
      email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Doe',
      is_active: true,
      is_staff: true,
      is_superuser: false,
      groups: ['Registrar Staff'],
      last_login: '2025-01-24'
    },
    {
      id: 3,
      username: 'student',
      email: '<EMAIL>',
      first_name: 'Jane',
      last_name: 'Smith',
      is_active: true,
      is_staff: false,
      is_superuser: false,
      groups: ['Students'],
      last_login: '2025-01-23'
    }
  ];

  const demoGroups = [
    { id: 1, name: 'Administrators', permissions: 24 },
    { id: 2, name: 'Registrar Staff', permissions: 12 },
    { id: 3, name: 'Department Heads', permissions: 8 },
    { id: 4, name: 'Students', permissions: 3 }
  ];

  const demoPermissions = [
    { id: 1, name: 'Can add user', codename: 'add_user', app: 'auth', model: 'user' },
    { id: 2, name: 'Can change user', codename: 'change_user', app: 'auth', model: 'user' },
    { id: 3, name: 'Can delete user', codename: 'delete_user', app: 'auth', model: 'user' },
    { id: 4, name: 'Can view user', codename: 'view_user', app: 'auth', model: 'user' },
    { id: 5, name: 'Can add group', codename: 'add_group', app: 'auth', model: 'group' },
    { id: 6, name: 'Can change group', codename: 'change_group', app: 'auth', model: 'group' }
  ];

  const getStatusBadge = (user: any) => {
    if (user.is_superuser) {
      return <Badge variant="destructive">Superuser</Badge>;
    }
    if (user.is_staff) {
      return <Badge variant="default">Staff</Badge>;
    }
    return <Badge variant="secondary">User</Badge>;
  };

  const getActiveBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="outline" className="bg-green-100 text-green-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-red-100 text-red-800">
        <XCircle className="h-3 w-3 mr-1" />
        Inactive
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Backend API Notice */}
      <Card className="border-orange-200 bg-orange-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-orange-800">
            <Server className="h-5 w-5" />
            <span>Backend APIs Not Available</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-orange-700 text-sm space-y-3">
            <p>
              The Django authentication APIs are not yet implemented. This demo shows what the interface will look like once the backend is ready.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Required API Endpoints:</h4>
                <ul className="text-xs space-y-1">
                  <li><code>GET /api/auth/users/</code></li>
                  <li><code>POST /api/auth/users/</code></li>
                  <li><code>GET /api/auth/groups/</code></li>
                  <li><code>POST /api/auth/groups/</code></li>
                  <li><code>GET /api/auth/permissions/</code></li>
                  <li><code>GET /api/auth/stats/</code></li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Django Models Used:</h4>
                <ul className="text-xs space-y-1">
                  <li><code>django.contrib.auth.models.User</code></li>
                  <li><code>django.contrib.auth.models.Group</code></li>
                  <li><code>django.contrib.auth.models.Permission</code></li>
                  <li><code>django.contrib.contenttypes.models.ContentType</code></li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoUsers.length}</div>
            <p className="text-xs text-muted-foreground">
              {demoUsers.filter(u => u.is_active).length} active, {demoUsers.filter(u => u.is_staff).length} staff
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Groups</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoGroups.length}</div>
            <p className="text-xs text-muted-foreground">
              Permission groups
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permissions</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoPermissions.length}</div>
            <p className="text-xs text-muted-foreground">
              System permissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Users</span>
          </TabsTrigger>
          <TabsTrigger value="groups" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Groups</span>
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center space-x-2">
            <Key className="h-4 w-4" />
            <span>Permissions</span>
          </TabsTrigger>
        </TabsList>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="h-5 w-5" />
                    <span>User Management (Demo)</span>
                  </CardTitle>
                  <CardDescription>
                    Django User model with built-in authentication fields
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search users..."
                      className="pl-10 w-64"
                      disabled
                    />
                  </div>
                  <Button disabled>
                    <Plus className="h-4 w-4 mr-2" />
                    Add User
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Groups</TableHead>
                      <TableHead>Last Login</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {demoUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {user.first_name} {user.last_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              @{user.username}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{getActiveBadge(user.is_active)}</TableCell>
                        <TableCell>{getStatusBadge(user)}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {user.groups.map((group, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {group}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>{user.last_login}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Groups Tab */}
        <TabsContent value="groups" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Group Management (Demo)</span>
                  </CardTitle>
                  <CardDescription>
                    Django Group model for role-based permissions
                  </CardDescription>
                </div>
                <Button disabled>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Group
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Group Name</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Description</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {demoGroups.map((group) => (
                      <TableRow key={group.id}>
                        <TableCell>
                          <div className="font-medium">{group.name}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{group.permissions} permissions</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            Django group with assigned permissions
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Permission Management (Demo)</span>
              </CardTitle>
              <CardDescription>
                Django Permission model with content types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Permission Name</TableHead>
                      <TableHead>Codename</TableHead>
                      <TableHead>Content Type</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {demoPermissions.map((permission) => (
                      <TableRow key={permission.id}>
                        <TableCell>
                          <div className="font-medium">{permission.name}</div>
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            {permission.codename}
                          </code>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {permission.app}.{permission.model}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DjangoAuthDemo;
