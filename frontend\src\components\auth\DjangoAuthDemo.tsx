import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Users,
  Shield,
  Key,
  Plus,
  Search,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Code,
  Database,
  Server
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { PermissionGate, LoginRequired, StaffRequired, SuperuserRequired, GroupRequired } from '@/components/DjangoPermissionGate';
import { createDjangoAuth, django } from '@/utils/djangoAuth';

const DjangoAuthDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('django-auth');
  const auth = useAuth();

  // Demo data
  const demoUsers = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      first_name: 'System',
      last_name: 'Administrator',
      is_active: true,
      is_staff: true,
      is_superuser: true,
      groups: ['Administrators'],
      last_login: '2025-01-24'
    },
    {
      id: 2,
      username: 'registrar',
      email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Doe',
      is_active: true,
      is_staff: true,
      is_superuser: false,
      groups: ['Registrar Staff'],
      last_login: '2025-01-24'
    },
    {
      id: 3,
      username: 'student',
      email: '<EMAIL>',
      first_name: 'Jane',
      last_name: 'Smith',
      is_active: true,
      is_staff: false,
      is_superuser: false,
      groups: ['Students'],
      last_login: '2025-01-23'
    }
  ];

  const demoGroups = [
    { id: 1, name: 'Administrators', permissions: 24 },
    { id: 2, name: 'Registrar Staff', permissions: 12 },
    { id: 3, name: 'Department Heads', permissions: 8 },
    { id: 4, name: 'Students', permissions: 3 }
  ];

  const demoPermissions = [
    { id: 1, name: 'Can add user', codename: 'add_user', app: 'auth', model: 'user' },
    { id: 2, name: 'Can change user', codename: 'change_user', app: 'auth', model: 'user' },
    { id: 3, name: 'Can delete user', codename: 'delete_user', app: 'auth', model: 'user' },
    { id: 4, name: 'Can view user', codename: 'view_user', app: 'auth', model: 'user' },
    { id: 5, name: 'Can add group', codename: 'add_group', app: 'auth', model: 'group' },
    { id: 6, name: 'Can change group', codename: 'change_group', app: 'auth', model: 'group' }
  ];

  const getStatusBadge = (user: any) => {
    if (user.is_superuser) {
      return <Badge variant="destructive">Superuser</Badge>;
    }
    if (user.is_staff) {
      return <Badge variant="default">Staff</Badge>;
    }
    return <Badge variant="secondary">User</Badge>;
  };

  const getActiveBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="outline" className="bg-green-100 text-green-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-red-100 text-red-800">
        <XCircle className="h-3 w-3 mr-1" />
        Inactive
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Backend API Notice */}
      <Card className="border-orange-200 bg-orange-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-orange-800">
            <Server className="h-5 w-5" />
            <span>Backend APIs Not Available</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-orange-700 text-sm space-y-3">
            <p>
              The Django authentication APIs are not yet implemented. This demo shows what the interface will look like once the backend is ready.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Required API Endpoints:</h4>
                <ul className="text-xs space-y-1">
                  <li><code>GET /api/auth/users/</code></li>
                  <li><code>POST /api/auth/users/</code></li>
                  <li><code>GET /api/auth/groups/</code></li>
                  <li><code>POST /api/auth/groups/</code></li>
                  <li><code>GET /api/auth/permissions/</code></li>
                  <li><code>GET /api/auth/stats/</code></li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Django Models Used:</h4>
                <ul className="text-xs space-y-1">
                  <li><code>django.contrib.auth.models.User</code></li>
                  <li><code>django.contrib.auth.models.Group</code></li>
                  <li><code>django.contrib.auth.models.Permission</code></li>
                  <li><code>django.contrib.contenttypes.models.ContentType</code></li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoUsers.length}</div>
            <p className="text-xs text-muted-foreground">
              {demoUsers.filter(u => u.is_active).length} active, {demoUsers.filter(u => u.is_staff).length} staff
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Groups</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoGroups.length}</div>
            <p className="text-xs text-muted-foreground">
              Permission groups
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permissions</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoPermissions.length}</div>
            <p className="text-xs text-muted-foreground">
              System permissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="django-auth" className="flex items-center space-x-2">
            <Code className="h-4 w-4" />
            <span>Django Auth</span>
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Users</span>
          </TabsTrigger>
          <TabsTrigger value="groups" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Groups</span>
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center space-x-2">
            <Key className="h-4 w-4" />
            <span>Permissions</span>
          </TabsTrigger>
        </TabsList>

        {/* Django Authentication Tab */}
        <TabsContent value="django-auth" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Current User Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Current User Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Username:</span>
                    <Badge variant="outline">{auth.user?.username || 'Anonymous'}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Authenticated:</span>
                    {auth.isAuthenticated ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Yes
                      </Badge>
                    ) : (
                      <Badge variant="destructive">
                        <XCircle className="h-3 w-3 mr-1" />
                        No
                      </Badge>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span>Staff:</span>
                    {auth.isStaff() ? (
                      <Badge variant="default" className="bg-blue-100 text-blue-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Yes
                      </Badge>
                    ) : (
                      <Badge variant="secondary">
                        <XCircle className="h-3 w-3 mr-1" />
                        No
                      </Badge>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span>Superuser:</span>
                    {auth.isSuperuser() ? (
                      <Badge variant="default" className="bg-purple-100 text-purple-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Yes
                      </Badge>
                    ) : (
                      <Badge variant="secondary">
                        <XCircle className="h-3 w-3 mr-1" />
                        No
                      </Badge>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span>Active:</span>
                    {auth.isActive() ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Yes
                      </Badge>
                    ) : (
                      <Badge variant="destructive">
                        <XCircle className="h-3 w-3 mr-1" />
                        No
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Groups:</h4>
                  <div className="flex flex-wrap gap-1">
                    {auth.getGroups().length > 0 ? (
                      auth.getGroups().map((group, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {group}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-gray-500">No groups assigned</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Permission Testing */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Permission Testing
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium mb-2">Common Django Permissions:</h4>
                    <div className="space-y-2">
                      {[
                        'auth.view_user',
                        'auth.change_user',
                        'auth.add_user',
                        'auth.delete_user',
                        'auth.view_group',
                        'auth.change_group'
                      ].map(permission => (
                        <div key={permission} className="flex justify-between items-center">
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {permission}
                          </code>
                          {auth.hasPerm(permission) ? (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Granted
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              <XCircle className="h-3 w-3 mr-1" />
                              Denied
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Django Permission Components Demo */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Django Permission Components Demo
              </CardTitle>
              <CardDescription>
                These components work exactly like Django's permission decorators
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Login Required Demo */}
                <div className="space-y-2">
                  <h4 className="font-medium">LoginRequired Component:</h4>
                  <div className="border rounded p-3 bg-gray-50">
                    <LoginRequired>
                      <div className="text-green-600 flex items-center gap-2">
                        <CheckCircle className="h-4 w-4" />
                        You are logged in! This content is visible.
                      </div>
                    </LoginRequired>
                  </div>
                </div>

                {/* Staff Required Demo */}
                <div className="space-y-2">
                  <h4 className="font-medium">StaffRequired Component:</h4>
                  <div className="border rounded p-3 bg-gray-50">
                    <StaffRequired>
                      <div className="text-blue-600 flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        You are staff! This content is visible.
                      </div>
                    </StaffRequired>
                  </div>
                </div>

                {/* Permission Required Demo */}
                <div className="space-y-2">
                  <h4 className="font-medium">PermissionGate Component:</h4>
                  <div className="border rounded p-3 bg-gray-50">
                    <PermissionGate permissions={['auth.view_user']}>
                      <div className="text-purple-600 flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        You have auth.view_user permission!
                      </div>
                    </PermissionGate>
                  </div>
                </div>

                {/* Superuser Required Demo */}
                <div className="space-y-2">
                  <h4 className="font-medium">SuperuserRequired Component:</h4>
                  <div className="border rounded p-3 bg-gray-50">
                    <SuperuserRequired>
                      <div className="text-red-600 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4" />
                        You are a superuser! This content is visible.
                      </div>
                    </SuperuserRequired>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="h-5 w-5" />
                    <span>User Management (Demo)</span>
                  </CardTitle>
                  <CardDescription>
                    Django User model with built-in authentication fields
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search users..."
                      className="pl-10 w-64"
                      disabled
                    />
                  </div>
                  <Button disabled>
                    <Plus className="h-4 w-4 mr-2" />
                    Add User
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Groups</TableHead>
                      <TableHead>Last Login</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {demoUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {user.first_name} {user.last_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              @{user.username}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{getActiveBadge(user.is_active)}</TableCell>
                        <TableCell>{getStatusBadge(user)}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {user.groups.map((group, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {group}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>{user.last_login}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Groups Tab */}
        <TabsContent value="groups" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Group Management (Demo)</span>
                  </CardTitle>
                  <CardDescription>
                    Django Group model for role-based permissions
                  </CardDescription>
                </div>
                <Button disabled>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Group
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Group Name</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Description</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {demoGroups.map((group) => (
                      <TableRow key={group.id}>
                        <TableCell>
                          <div className="font-medium">{group.name}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{group.permissions} permissions</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            Django group with assigned permissions
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Permission Management (Demo)</span>
              </CardTitle>
              <CardDescription>
                Django Permission model with content types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Permission Name</TableHead>
                      <TableHead>Codename</TableHead>
                      <TableHead>Content Type</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {demoPermissions.map((permission) => (
                      <TableRow key={permission.id}>
                        <TableCell>
                          <div className="font-medium">{permission.name}</div>
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-muted px-1 py-0.5 rounded">
                            {permission.codename}
                          </code>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {permission.app}.{permission.model}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DjangoAuthDemo;
