import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { 
  Users, 
  Shield, 
  Plus, 
  Minus,
  Search, 
  Filter, 
  RefreshCw,
  UserPlus,
  UserMinus,
  ArrowRight,
  ArrowLeft,
  Settings,
  Eye,
  Edit,
  Trash2,
  Copy
} from 'lucide-react';
import { toast } from 'sonner';
import { userAPI, groupAPI, statsAPI } from '@/services/authAPI';
import { User, Group, RoleHierarchy } from '@/types/auth';

const RoleAssignmentInterface: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [roleHierarchy, setRoleHierarchy] = useState<RoleHierarchy[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [showBulkAssignDialog, setShowBulkAssignDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [assignmentMode, setAssignmentMode] = useState<'assign' | 'remove'>('assign');

  // Assignment form state
  const [assignmentForm, setAssignmentForm] = useState({
    userIds: [] as number[],
    groupIds: [] as number[],
    mode: 'assign' as 'assign' | 'remove'
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [usersResponse, groupsResponse, hierarchyResponse] = await Promise.all([
        userAPI.getUsers(),
        groupAPI.getGroups(),
        statsAPI.getRoleHierarchy()
      ]);
      
      setUsers(usersResponse.data.results);
      setGroups(groupsResponse.data.results);
      setRoleHierarchy(hierarchyResponse.data);
    } catch (error) {
      toast.error('Failed to load data');
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignUserToGroups = async (userId: number, groupIds: number[]) => {
    try {
      await userAPI.addUserToGroups(userId, groupIds);
      toast.success('User assigned to groups successfully');
      loadData();
    } catch (error) {
      toast.error('Failed to assign user to groups');
      console.error('Error assigning user:', error);
    }
  };

  const handleRemoveUserFromGroups = async (userId: number, groupIds: number[]) => {
    try {
      await userAPI.removeUserFromGroups(userId, groupIds);
      toast.success('User removed from groups successfully');
      loadData();
    } catch (error) {
      toast.error('Failed to remove user from groups');
      console.error('Error removing user:', error);
    }
  };

  const handleBulkAssignment = async () => {
    try {
      const { userIds, groupIds, mode } = assignmentForm;
      
      if (userIds.length === 0 || groupIds.length === 0) {
        toast.error('Please select users and groups');
        return;
      }

      for (const userId of userIds) {
        if (mode === 'assign') {
          await userAPI.addUserToGroups(userId, groupIds);
        } else {
          await userAPI.removeUserFromGroups(userId, groupIds);
        }
      }

      toast.success(`Users ${mode === 'assign' ? 'assigned to' : 'removed from'} groups successfully`);
      setShowBulkAssignDialog(false);
      setAssignmentForm({ userIds: [], groupIds: [], mode: 'assign' });
      loadData();
    } catch (error) {
      toast.error('Failed to perform bulk assignment');
      console.error('Error in bulk assignment:', error);
    }
  };

  const openAssignDialog = (user: User) => {
    setSelectedUser(user);
    setShowAssignDialog(true);
  };

  const openBulkAssignDialog = () => {
    setAssignmentForm({
      userIds: selectedUsers,
      groupIds: [],
      mode: 'assign'
    });
    setShowBulkAssignDialog(true);
  };

  const getUserGroups = (user: User) => {
    return user.groups || [];
  };

  const getGroupUsers = (group: Group) => {
    return users.filter(user => 
      user.groups.some(userGroup => userGroup.id === group.id)
    );
  };

  const getRoleHierarchyDisplay = () => {
    return roleHierarchy.map(role => (
      <div key={role.id} className="border rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span className="font-medium">{role.name}</span>
            <Badge variant="outline">Level {role.level}</Badge>
          </div>
          <Badge variant="secondary">{role.users_count} users</Badge>
        </div>
        
        <div className="text-sm text-muted-foreground mb-2">
          Permissions: {role.permissions.length}
        </div>
        
        {role.children.length > 0 && (
          <div className="ml-4 border-l-2 border-gray-200 pl-4">
            <div className="text-sm font-medium mb-2">Child Roles:</div>
            {role.children.map(child => (
              <div key={child.id} className="flex items-center space-x-2 mb-1">
                <ArrowRight className="h-4 w-4" />
                <span>{child.name}</span>
                <Badge variant="outline" className="text-xs">{child.users_count} users</Badge>
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = !filterRole || user.groups.some(group => group.name === filterRole);
    
    return matchesSearch && matchesRole;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Role Assignment Interface</h1>
          <p className="text-muted-foreground">
            Manage user-group assignments and role hierarchies
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {selectedUsers.length > 0 && (
            <Button onClick={openBulkAssignDialog}>
              <UserPlus className="h-4 w-4 mr-2" />
              Bulk Assign ({selectedUsers.length})
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search Users</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name, username, or email..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="role-filter">Filter by Role</Label>
              <Select value={filterRole} onValueChange={setFilterRole}>
                <SelectTrigger>
                  <SelectValue placeholder="All roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All roles</SelectItem>
                  {groups.map(group => (
                    <SelectItem key={group.id} value={group.name}>
                      {group.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setFilterRole('');
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="user-assignments" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="user-assignments">User Assignments</TabsTrigger>
          <TabsTrigger value="group-members">Group Members</TabsTrigger>
          <TabsTrigger value="role-hierarchy">Role Hierarchy</TabsTrigger>
        </TabsList>

        {/* User Assignments Tab */}
        <TabsContent value="user-assignments">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>User Role Assignments ({filteredUsers.length})</span>
              </CardTitle>
              <CardDescription>
                Assign users to groups and manage their roles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedUsers(filteredUsers.map(u => u.id));
                            } else {
                              setSelectedUsers([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Current Groups</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <div className="flex items-center justify-center space-x-2">
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            <span>Loading users...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <div className="text-muted-foreground">
                            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No users found</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedUsers.includes(user.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedUsers([...selectedUsers, user.id]);
                                } else {
                                  setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {user.first_name} {user.last_name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                @{user.username} • {user.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {getUserGroups(user).map((group) => (
                                <Badge key={group.id} variant="outline" className="text-xs">
                                  {group.name}
                                </Badge>
                              ))}
                              {getUserGroups(user).length === 0 && (
                                <span className="text-muted-foreground text-sm">No groups</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {user.is_superuser ? (
                              <Badge variant="default">Superuser</Badge>
                            ) : user.is_staff ? (
                              <Badge variant="secondary">Staff</Badge>
                            ) : (
                              <Badge variant="outline">User</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openAssignDialog(user)}
                            >
                              <Settings className="h-4 w-4 mr-2" />
                              Manage Roles
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Group Members Tab */}
        <TabsContent value="group-members">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Group Members</span>
              </CardTitle>
              <CardDescription>
                View and manage members of each group
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {groups.map((group) => {
                  const groupUsers = getGroupUsers(group);
                  return (
                    <Card key={group.id} className="border">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center justify-between">
                          <span>{group.name}</span>
                          <Badge variant="secondary">{groupUsers.length} members</Badge>
                        </CardTitle>
                        {group.description && (
                          <CardDescription className="text-sm">
                            {group.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="text-sm font-medium">Members:</div>
                          <div className="max-h-32 overflow-y-auto">
                            {groupUsers.length > 0 ? (
                              groupUsers.map((user) => (
                                <div key={user.id} className="flex items-center justify-between py-1">
                                  <span className="text-sm">{user.username}</span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveUserFromGroups(user.id, [group.id])}
                                  >
                                    <UserMinus className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))
                            ) : (
                              <div className="text-sm text-muted-foreground">No members</div>
                            )}
                          </div>
                          <div className="pt-2 border-t">
                            <div className="text-sm font-medium mb-1">Permissions:</div>
                            <div className="text-xs text-muted-foreground">
                              {group.permissions.length} permissions assigned
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Role Hierarchy Tab */}
        <TabsContent value="role-hierarchy">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Role Hierarchy</span>
              </CardTitle>
              <CardDescription>
                View the hierarchical structure of roles and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {roleHierarchy.length > 0 ? (
                <div className="space-y-4">
                  {getRoleHierarchyDisplay()}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-muted-foreground">No role hierarchy defined</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Assign User to Groups Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Manage User Roles</DialogTitle>
            <DialogDescription>
              Assign or remove groups for {selectedUser?.username}
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Current Groups</Label>
                  <div className="border rounded-md p-3 min-h-32 max-h-48 overflow-y-auto">
                    {getUserGroups(selectedUser).map((group) => (
                      <div key={group.id} className="flex items-center justify-between py-1">
                        <span className="text-sm">{group.name}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveUserFromGroups(selectedUser.id, [group.id])}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                    {getUserGroups(selectedUser).length === 0 && (
                      <div className="text-sm text-muted-foreground">No groups assigned</div>
                    )}
                  </div>
                </div>

                <div>
                  <Label>Available Groups</Label>
                  <div className="border rounded-md p-3 min-h-32 max-h-48 overflow-y-auto">
                    {groups
                      .filter(group => !getUserGroups(selectedUser).some(ug => ug.id === group.id))
                      .map((group) => (
                        <div key={group.id} className="flex items-center justify-between py-1">
                          <span className="text-sm">{group.name}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAssignUserToGroups(selectedUser.id, [group.id])}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Assignment Dialog */}
      <Dialog open={showBulkAssignDialog} onOpenChange={setShowBulkAssignDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Bulk Role Assignment</DialogTitle>
            <DialogDescription>
              Assign or remove groups for multiple users at once
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label>Selected Users ({assignmentForm.userIds.length})</Label>
              <div className="border rounded-md p-3 max-h-32 overflow-y-auto">
                {users
                  .filter(user => assignmentForm.userIds.includes(user.id))
                  .map(user => (
                    <div key={user.id} className="text-sm py-1">
                      {user.username} ({user.first_name} {user.last_name})
                    </div>
                  ))}
              </div>
            </div>

            <div>
              <Label>Action</Label>
              <Select
                value={assignmentForm.mode}
                onValueChange={(value: 'assign' | 'remove') =>
                  setAssignmentForm({ ...assignmentForm, mode: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="assign">Assign to Groups</SelectItem>
                  <SelectItem value="remove">Remove from Groups</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Select Groups</Label>
              <div className="border rounded-md p-3 max-h-48 overflow-y-auto">
                {groups.map((group) => (
                  <div key={group.id} className="flex items-center space-x-2 py-1">
                    <Checkbox
                      id={`bulk-group-${group.id}`}
                      checked={assignmentForm.groupIds.includes(group.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setAssignmentForm({
                            ...assignmentForm,
                            groupIds: [...assignmentForm.groupIds, group.id]
                          });
                        } else {
                          setAssignmentForm({
                            ...assignmentForm,
                            groupIds: assignmentForm.groupIds.filter(id => id !== group.id)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={`bulk-group-${group.id}`} className="text-sm">
                      {group.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkAssignDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkAssignment}>
              {assignmentForm.mode === 'assign' ? 'Assign' : 'Remove'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RoleAssignmentInterface;
