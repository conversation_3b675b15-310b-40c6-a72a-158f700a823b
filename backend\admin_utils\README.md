# Database Sequence Management Tools

This package provides tools to manage database sequences in PostgreSQL, particularly to fix the "duplicate key value violates unique constraint" errors that can occur when sequences get out of sync with the actual data.

## Quick Fix for Department Sequence Issues

If you're experiencing issues with the Department model, run this script to immediately fix the sequence:

```bash
python fix_department_sequence.py
```

## Available Tools

### 1. Management Commands

#### Reset Sequence for a Specific Table

```bash
python manage.py reset_sequence <app_label> <model_name>
```

Example:
```bash
python manage.py reset_sequence setups department
```

Add `--dry-run` to see what would happen without making changes:
```bash
python manage.py reset_sequence setups department --dry-run
```

#### Fix All Sequences in the Database

```bash
python manage.py fix_all_sequences
```

Add `--dry-run` to see what would happen without making changes:
```bash
python manage.py fix_all_sequences --dry-run
```

### 2. Admin Actions

The Department admin interface includes a custom action to reset the sequence. To use it:

1. Go to the Department admin page
2. Select one or more departments (the selection doesn't matter)
3. Choose "Reset auto-increment sequence" from the action dropdown
4. Click "Go"
5. **Important**: Refresh the page after the action completes

### 3. Automatic Sequence Handling

The application includes two middleware components that work together to handle sequence issues:

1. **SequenceFixerMiddleware**: Logs sequence issues when they occur
2. **SequenceResetMiddleware**: Resets sequences after requests complete

This approach avoids transaction issues by performing sequence resets outside of any transaction.

### 4. Smart ID Assignment

When adding new departments, the system will:

1. Automatically assign a safe ID to new departments (current max ID + 100)
2. If a conflict still occurs, it will retry with an even higher ID (conflict ID + 200)
3. Schedule a sequence reset after the save completes

### 5. Quick Fix Scripts

For immediate fixes, you can run:

```bash
python fix_sequences.py        # Fix all sequences
python fix_department_sequence.py  # Fix only the department sequence
```

These scripts will fix sequences without requiring Django's management command infrastructure.

## Troubleshooting

If you continue to experience sequence issues:

1. Run `python fix_department_sequence.py` to immediately fix the department sequence
2. Restart the Django server to ensure middleware changes take effect
3. Check the PostgreSQL logs for more detailed error information
4. If all else fails, you can manually set IDs when creating new departments

## How It Works

The sequence fixing tools work by:

1. Finding the maximum ID currently in use for a table
2. Setting the sequence to start from (max_id + 100) to provide a safety buffer
3. This ensures that new records will use IDs that don't conflict with existing data

For the Department model specifically, we've implemented a multi-layered approach:

1. Smart ID assignment for new departments
2. Automatic retry with higher IDs if conflicts occur
3. Post-request sequence resets to fix the underlying issue
4. Admin actions for manual intervention when needed
