# Generated by Django 5.2.1 on 2025-06-09 20:04

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('certificate_type', '0002_remove_certificatetype_id_alter_certificatetype_uuid'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('fee', models.DecimalField(decimal_places=2, help_text='Service fee amount', max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_types', models.ManyToManyField(blank=True, help_text='Certificate types associated with this service', related_name='service_types', to='certificate_type.certificatetype')),
            ],
            options={
                'verbose_name': 'Service Type',
                'verbose_name_plural': 'Service Types',
                'ordering': ['name'],
            },
        ),
    ]
