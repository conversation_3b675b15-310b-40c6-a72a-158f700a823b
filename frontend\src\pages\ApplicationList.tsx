import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { applicationAPI } from '@/services/api';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Clock, XCircle, FileText, AlertCircle, ChevronRight, Loader2, Plus, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface AdmissionType {
  id: number;
  name: string;
  description: string;
}

interface ApplicationItem {
  id: string;
  program_name?: string;
  application_num?: string;
  created_at: string;
  updated_at: string;
  status?: string;
  registrar_off_status?: string;
  department_status?: string;
  payment_status?: string;
  admission_type?: string;
  admission_type_id?: number;
  hasPersonalInfo: boolean;
  hasGat: boolean;
  hasProgramSelection: boolean;
  hasPayment: boolean;
}

interface ApplicationData {
  personalInfo: any[];
  gat: any[];
  programSelection: any[];
  payment: any[];
  applications: ApplicationItem[];
  admissionTypes: AdmissionType[];
}

const ApplicationList = () => {
  const [applicationData, setApplicationData] = useState<ApplicationData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchApplicationData = async () => {
      try {
        setLoading(true);

        // Fetch all application data for the current user, handling payment errors
        let personalInfoResponse, gatResponse, programSelectionResponse, admissionTypesResponse;
        let paymentResponse = { data: [] }; // Default empty payment data

        try {
          // Fetch all data except payment in parallel
          [personalInfoResponse, gatResponse, programSelectionResponse, admissionTypesResponse] = await Promise.all([
            applicationAPI.getCurrentApplicantInfo(),
            applicationAPI.getCurrentGAT(),
            applicationAPI.getCurrentProgramSelection(),
            applicationAPI.getAdmissionTypes()
          ]);

          // Skip payment fetch entirely - we know it's causing errors
          console.log('Skipping payment data fetch due to known database schema issues');
        } catch (error) {
          console.error('Error fetching application data:', error);
          throw error; // Re-throw to be caught by the outer try-catch
        }

        const personalInfo = personalInfoResponse.data || [];
        const gat = gatResponse.data || [];
        const programSelection = programSelectionResponse.data || [];
        const payment = []; // Empty payment array since we're skipping the fetch
        const admissionTypes = admissionTypesResponse.data || [];

        // Process the data to create a list of applications
        const applications: ApplicationItem[] = [];

        // If there's program selection data, use it as the base for applications
        if (programSelection.length > 0) {
          programSelection.forEach((program: any) => {
            // Find related payment for this program selection
            const relatedPayment = payment.find((p: any) =>
              p.application_num && p.application_num.id === program.id
            );

            // Get admission type from application_info if available
            const admissionTypeId = program.application_info?.admission_type?.id;
            const admissionTypeName = program.application_info?.admission_type?.name ||
                                     (admissionTypeId ?
                                      admissionTypes.find(at => at.id === admissionTypeId)?.name :
                                      'Regular');

            applications.push({
              id: program.id,
              program_name: program.application_info?.program_name || 'Unknown Program',
              application_num: program.application_num,
              created_at: program.created_at,
              updated_at: program.updated_at,
              registrar_off_status: program.registrar_off_status,
              department_status: program.department_status,
              payment_status: relatedPayment?.payment_status || 'Not Submitted',
              admission_type: admissionTypeName,
              admission_type_id: admissionTypeId,
              hasPersonalInfo: personalInfo.length > 0,
              hasGat: gat.length > 0,
              hasProgramSelection: true,
              hasPayment: !!relatedPayment
            });
          });
        }
        // If there's no program selection but there's GAT data
        else if (gat.length > 0) {
          applications.push({
            id: gat[0].id,
            created_at: gat[0].created_at,
            updated_at: gat[0].updated_at,
            status: 'Incomplete',
            admission_type: 'Regular', // Default value
            hasPersonalInfo: personalInfo.length > 0,
            hasGat: true,
            hasProgramSelection: false,
            hasPayment: false
          });
        }
        // If there's only personal info
        else if (personalInfo.length > 0) {
          applications.push({
            id: personalInfo[0].id,
            created_at: personalInfo[0].created_at,
            updated_at: personalInfo[0].updated_at,
            status: 'Incomplete',
            admission_type: 'Regular', // Default value
            hasPersonalInfo: true,
            hasGat: false,
            hasProgramSelection: false,
            hasPayment: false
          });
        }

        setApplicationData({
          personalInfo,
          gat,
          programSelection,
          payment,
          applications,
          admissionTypes
        });
      } catch (error) {
        console.error('Error fetching application data:', error);
        toast.error('Failed to load application data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchApplicationData();
  }, []);

  const getStatusBadge = (status: string | undefined) => {
    if (!status) return <Badge variant="outline" className="bg-gray-100 text-gray-800">Not Started</Badge>;

    switch (status.toLowerCase()) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'verified':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Verified</Badge>;
      default:
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string | undefined) => {
    if (!status) return <AlertCircle className="h-5 w-5 text-gray-400" />;

    switch (status.toLowerCase()) {
      case 'pending':
        return (
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center">
            <Clock className="h-3.5 w-3.5 text-amber-600" />
          </div>
        );
      case 'approved':
        return (
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
            <CheckCircle className="h-3.5 w-3.5 text-green-600" />
          </div>
        );
      case 'rejected':
        return (
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-red-100 flex items-center justify-center">
            <XCircle className="h-3.5 w-3.5 text-red-600" />
          </div>
        );
      case 'verified':
        return (
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
            <CheckCircle className="h-3.5 w-3.5 text-green-600" />
          </div>
        );
      default:
        return (
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
            <FileText className="h-3.5 w-3.5 text-blue-600" />
          </div>
        );
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="max-w-6xl mx-auto">
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Application Status</CardTitle>
              <CardDescription>View the status of your application components</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-64">
                <div className="flex flex-col items-center">
                  <Loader2 className="h-8 w-8 text-blue-500 animate-spin mb-4" />
                  <p className="text-gray-500">Loading application data...</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  const hasPersonalInfo = applicationData?.personalInfo && applicationData.personalInfo.length > 0;
  const hasGat = applicationData?.gat && applicationData.gat.length > 0;
  const hasProgramSelection = applicationData?.programSelection && applicationData.programSelection.length > 0;
  const hasPayment = applicationData?.payment && applicationData.payment.length > 0;

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg mb-6 shadow-sm border border-blue-100">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800 mb-1">My Applications</h1>
              <p className="text-gray-600">View and manage your graduate program applications</p>
            </div>
            <Button asChild className="bg-[#1a73c0] hover:bg-[#0e4a7d] shadow-md transition-all duration-200 transform hover:scale-105">
              <Link to="/application/gat?new=true" className="flex items-center">
                <Plus className="mr-2 h-4 w-4" />
                Start New Application
              </Link>
            </Button>
          </div>
        </div>

        <Card className="mb-8 shadow-md border-0 overflow-hidden">
          <CardContent className="p-0">
            {!applicationData?.applications || applicationData.applications.length === 0 ? (
              <div className="text-center py-16 px-6 bg-gray-50">
                <div className="bg-white p-8 rounded-lg shadow-sm max-w-md mx-auto border border-gray-100">
                  <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No Applications Found</h3>
                  <p className="text-gray-600 mb-6">You haven't started any application process yet.</p>
                  <Button asChild className="bg-[#1a73c0] hover:bg-[#0e4a7d] shadow-md transition-all duration-200">
                    <Link to="/application/new" className="flex items-center justify-center">
                      <Plus className="mr-2 h-4 w-4" />
                      Start Application
                    </Link>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">GAT Info</th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">Program</th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">Admission Type</th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">Date</th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">Registrar Status</th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">Department Status</th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">Payment Status</th>
                      <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-100">
                    {applicationData.applications.map((app, index) => (
                      <tr key={app.id} className={`hover:bg-blue-50 transition-colors duration-150 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="px-6 py-5 text-sm">
                          {app.hasGat ? (
                            <div className="flex flex-col">
                              <span className="font-medium text-gray-900">GAT No: {applicationData?.gat[0]?.GAT_No || 'N/A'}</span>
                              <span className="text-gray-600 mt-1">Result: {applicationData?.gat[0]?.GAT_Result || 'N/A'}</span>
                            </div>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Not Submitted
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-5 text-sm">
                          {app.program_name ? (
                            <div className="font-medium text-gray-900">{app.program_name}</div>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              Not Selected
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-5 text-sm">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {app.admission_type || 'Regular'}
                          </span>
                        </td>
                        <td className="px-6 py-5 text-sm text-gray-600">
                          {new Date(app.created_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </td>
                        <td className="px-6 py-5 text-sm">
                          <div className="flex items-center">
                            {app.registrar_off_status ? (
                              <>
                                {getStatusIcon(app.registrar_off_status)}
                                <span className={`ml-2 font-medium ${
                                  app.registrar_off_status.toLowerCase() === 'approved' ? 'text-green-700' :
                                  app.registrar_off_status.toLowerCase() === 'rejected' ? 'text-red-700' :
                                  app.registrar_off_status.toLowerCase() === 'pending' ? 'text-amber-700' :
                                  'text-gray-700'
                                }`}>
                                  {app.registrar_off_status}
                                </span>
                              </>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Not Submitted
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-5 text-sm">
                          <div className="flex items-center">
                            {app.department_status ? (
                              <>
                                {getStatusIcon(app.department_status)}
                                <span className={`ml-2 font-medium ${
                                  app.department_status.toLowerCase() === 'approved' ? 'text-green-700' :
                                  app.department_status.toLowerCase() === 'rejected' ? 'text-red-700' :
                                  app.department_status.toLowerCase() === 'pending' ? 'text-amber-700' :
                                  'text-gray-700'
                                }`}>
                                  {app.department_status}
                                </span>
                              </>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Not Submitted
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-5 text-sm">
                          <div className="flex items-center">
                            {app.payment_status ? (
                              <>
                                {getStatusIcon(app.payment_status)}
                                <span className={`ml-2 font-medium ${
                                  app.payment_status.toLowerCase() === 'approved' ? 'text-green-700' :
                                  app.payment_status.toLowerCase() === 'rejected' ? 'text-red-700' :
                                  app.payment_status.toLowerCase() === 'pending' ? 'text-amber-700' :
                                  'text-gray-700'
                                }`}>
                                  {app.payment_status}
                                </span>
                              </>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Not Submitted
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-5 text-sm font-medium">
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm" asChild className="border-blue-200 text-blue-700 hover:bg-blue-50 hover:text-blue-800 transition-colors">
                              <Link to={`/application/status/${app.id}`} className="flex items-center">
                                <Eye className="mr-1 h-4 w-4" />
                                View
                              </Link>
                            </Button>
                            {!app.hasProgramSelection && (
                              <Button size="sm" asChild className="bg-[#1a73c0] hover:bg-[#0e4a7d] shadow-sm transition-colors">
                                <Link to="/application/gat?new=true">Continue</Link>
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ApplicationList;
