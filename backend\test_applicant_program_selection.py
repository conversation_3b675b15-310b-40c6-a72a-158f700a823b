#!/usr/bin/env python
"""
Test script for ApplicantProgramSelection with year foreign key
"""

import os
import sys
import django

# Add the backend directory to the Python path
backend_path = os.path.dirname(__file__)
sys.path.insert(0, backend_path)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from registration.models import ApplicantProgramSelection
from registration.serializers import ApplicantProgramSelectionSerializer
from setups.year.models import Year
from django.contrib.auth.models import User

def test_applicant_program_selection():
    """Test the ApplicantProgramSelection model with year foreign key"""
    try:
        print("=== Testing ApplicantProgramSelection with Year Foreign Key ===")
        
        # Check existing program selections
        selections = ApplicantProgramSelection.objects.all()
        print(f"Total program selections: {selections.count()}")
        
        # Check available years
        years = Year.objects.all()
        print(f"Available years: {[y.year for y in years]}")
        
        # Test serializer with existing data
        if selections.exists():
            print("\n=== Testing Serializer ===")
            for selection in selections:
                print(f"\nSelection ID: {selection.id}")
                print(f"User: {selection.user.username if selection.user else 'None'}")
                print(f"Year: {selection.year.year if selection.year else 'None'}")
                print(f"Year UUID: {selection.year.uuid if selection.year else 'None'}")
                print(f"Application Number: {selection.application_num}")
                print(f"Sponsorship: {selection.sponsorship}")
                
                # Test serializer
                serializer = ApplicantProgramSelectionSerializer(selection)
                print(f"Serialized data: {serializer.data}")
                print("---")
        else:
            print("No program selections found")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_applicant_program_selection()
