import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface FeatureConfig {
  permissions?: string[];
  groups?: string[];
  requireAll?: boolean;
  requireAuth?: boolean;
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  customCheck?: (user: any) => boolean;
  enabled?: boolean; // Global feature flag
}

interface FeatureAccessResult {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canCreate: boolean;
  canManage: boolean;
  hasFullAccess: boolean;
  accessLevel: 'none' | 'read' | 'write' | 'admin' | 'full';
  deniedReason?: string;
}

/**
 * Hook for dynamic feature access control
 * Provides granular control over what users can do with specific features
 */
export const useFeatureAccess = (
  feature: string,
  config: FeatureConfig = {}
): FeatureAccessResult => {
  const {
    permissions = [],
    groups = [],
    requireAll = false,
    requireAuth = true,
    requireStaff = false,
    requireSuperuser = false,
    customCheck,
    enabled = true
  } = config;

  const { user, isAuthenticated, hasPermission, hasRole, hasAnyRole } = useAuth();

  return useMemo(() => {
    // Feature globally disabled
    if (!enabled) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'Feature disabled'
      };
    }

    // Check authentication
    if (requireAuth && !isAuthenticated) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'Authentication required'
      };
    }

    // Check if user exists
    if (requireAuth && !user) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'User not found'
      };
    }

    // Check active status
    if (user && !user.is_active) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'Account inactive'
      };
    }

    // Check staff requirement
    if (requireStaff && user && !user.is_staff) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'Staff access required'
      };
    }

    // Check superuser requirement
    if (requireSuperuser && user && !user.is_superuser) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'Superuser access required'
      };
    }

    // Superuser gets full access (unless custom check overrides)
    if (user?.is_superuser && !customCheck) {
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canCreate: true,
        canManage: true,
        hasFullAccess: true,
        accessLevel: 'full' as const
      };
    }

    // Custom validation check
    if (customCheck && !customCheck(user)) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'Custom validation failed'
      };
    }

    // Check permissions and groups
    let hasBasicAccess = true;
    let deniedReason: string | undefined;

    if (permissions.length > 0) {
      const permissionCheck = requireAll 
        ? permissions.every(permission => hasPermission(permission))
        : permissions.some(permission => hasPermission(permission));
      
      if (!permissionCheck) {
        hasBasicAccess = false;
        deniedReason = `Missing permissions: ${permissions.join(', ')}`;
      }
    }

    if (groups.length > 0 && hasBasicAccess) {
      const groupCheck = requireAll
        ? groups.every(group => hasRole(group))
        : hasAnyRole(groups);
      
      if (!groupCheck) {
        hasBasicAccess = false;
        deniedReason = `Missing groups: ${groups.join(', ')}`;
      }
    }

    if (!hasBasicAccess) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason
      };
    }

    // Determine specific capabilities based on permissions
    const canView = hasBasicAccess && (
      hasPermission(`${feature}.view`) ||
      hasPermission(`view_${feature}`) ||
      permissions.some(p => p.includes('view')) ||
      hasBasicAccess
    );

    const canEdit = hasBasicAccess && (
      hasPermission(`${feature}.change`) ||
      hasPermission(`change_${feature}`) ||
      permissions.some(p => p.includes('change') || p.includes('edit'))
    );

    const canDelete = hasBasicAccess && (
      hasPermission(`${feature}.delete`) ||
      hasPermission(`delete_${feature}`) ||
      permissions.some(p => p.includes('delete'))
    );

    const canCreate = hasBasicAccess && (
      hasPermission(`${feature}.add`) ||
      hasPermission(`add_${feature}`) ||
      permissions.some(p => p.includes('add') || p.includes('create'))
    );

    const canManage = hasBasicAccess && (
      hasPermission(`${feature}.manage`) ||
      hasPermission(`manage_${feature}`) ||
      (canEdit && canDelete && canCreate)
    );

    const hasFullAccess = canView && canEdit && canDelete && canCreate && canManage;

    // Determine access level
    let accessLevel: 'none' | 'read' | 'write' | 'admin' | 'full';
    if (!canView) {
      accessLevel = 'none';
    } else if (canView && !canEdit && !canCreate && !canDelete) {
      accessLevel = 'read';
    } else if (canView && (canEdit || canCreate) && !canDelete) {
      accessLevel = 'write';
    } else if (canView && canEdit && canCreate && canDelete && !canManage) {
      accessLevel = 'admin';
    } else {
      accessLevel = 'full';
    }

    return {
      canView,
      canEdit,
      canDelete,
      canCreate,
      canManage,
      hasFullAccess,
      accessLevel,
      deniedReason
    };
  }, [
    feature,
    enabled,
    requireAuth,
    requireStaff,
    requireSuperuser,
    permissions,
    groups,
    requireAll,
    customCheck,
    isAuthenticated,
    user,
    hasPermission,
    hasRole,
    hasAnyRole
  ]);
};

// Convenience hooks for specific features
export const useGraduateAccess = () => {
  return useFeatureAccess('graduate', {
    permissions: ['unknown.view_graduatestudent', 'unknown.change_graduatestudent'],
    groups: ['Verification Clerk', 'Registrar', 'Administrator']
  });
};

export const useCollegeAccess = () => {
  return useFeatureAccess('college', {
    permissions: ['unknown.view_verificationcollege', 'unknown.change_verificationcollege'],
    groups: ['Verification Clerk', 'Main Registrar', 'Administrator']
  });
};

export const useDepartmentAccess = () => {
  return useFeatureAccess('department', {
    permissions: ['unknown.view_verificationdepartment', 'unknown.change_verificationdepartment'],
    groups: ['Verification Clerk', 'Department Head', 'Administrator']
  });
};

export const useProgramAccess = () => {
  return useFeatureAccess('program', {
    permissions: ['unknown.view_verificationprogram', 'unknown.change_verificationprogram'],
    groups: ['Verification Clerk', 'Department Head', 'Administrator']
  });
};

export const useUserManagementAccess = () => {
  return useFeatureAccess('user', {
    permissions: ['auth.view_user', 'auth.change_user'],
    groups: ['Administrator'],
    requireStaff: true
  });
};

export default useFeatureAccess;
