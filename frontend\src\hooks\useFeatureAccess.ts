import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { createPermissionChecker } from '@/utils/permissionChecker';

interface FeatureConfig {
  permissions?: string[];
  requireAll?: boolean;
  requireAuth?: boolean;
  requireStaff?: boolean;
  requireSuperuser?: boolean;
  customCheck?: (user: any) => boolean;
  enabled?: boolean; // Global feature flag

  // DEPRECATED: Groups are no longer used for access control
  groups?: string[];
}

interface FeatureAccessResult {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canCreate: boolean;
  canManage: boolean;
  hasFullAccess: boolean;
  accessLevel: 'none' | 'read' | 'write' | 'admin' | 'full';
  deniedReason?: string;
}

/**
 * Hook for dynamic feature access control
 * Provides granular control over what users can do with specific features
 */
export const useFeatureAccess = (
  feature: string,
  config: FeatureConfig = {}
): FeatureAccessResult => {
  const {
    permissions = [],
    groups = [], // DEPRECATED: Ignored in new system
    requireAll = false,
    requireAuth = true,
    requireStaff = false,
    requireSuperuser = false,
    customCheck,
    enabled = true
  } = config;

  const { user } = useAuth();

  return useMemo(() => {
    // Feature globally disabled
    if (!enabled) {
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canCreate: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'none' as const,
        deniedReason: 'Feature disabled'
      };
    }

    // Use the new permission checker
    const checker = createPermissionChecker(user);

    // If specific permissions provided, use them; otherwise use feature-based permissions
    const featurePermissions = permissions.length > 0 ? permissions : undefined;
    const accessDetails = checker.getFeatureAccessDetails(feature);

    // If we have custom permissions, override the feature-based check
    if (featurePermissions) {
      const customAccessResult = checker.checkAccess({
        permissions: featurePermissions,
        requireAll,
        requireAuth,
        requireStaff,
        requireSuperuser,
        customCheck
      });

      if (!customAccessResult.hasAccess) {
        return {
          canView: false,
          canEdit: false,
          canDelete: false,
          canCreate: false,
          canManage: false,
          hasFullAccess: false,
          accessLevel: 'none' as const,
          deniedReason: customAccessResult.reason
        };
      }
    }

    return accessDetails;
  }, [
    feature,
    enabled,
    permissions,
    requireAll,
    requireAuth,
    requireStaff,
    requireSuperuser,
    customCheck,
    user
  ]);
};

// Convenience hooks for specific features - Updated to use permission-only system
export const useGraduateAccess = () => {
  return useFeatureAccess('graduate');
};

export const useCollegeAccess = () => {
  return useFeatureAccess('college');
};

export const useDepartmentAccess = () => {
  return useFeatureAccess('department');
};

export const useProgramAccess = () => {
  return useFeatureAccess('program');
};

export const useServiceAccess = () => {
  return useFeatureAccess('service');
};

export const useUserManagementAccess = () => {
  return useFeatureAccess('user', {
    requireStaff: true
  });
};

export const useDocumentAccess = () => {
  return useFeatureAccess('document');
};

export const useCertificateAccess = () => {
  return useFeatureAccess('certificate');
};

export default useFeatureAccess;
