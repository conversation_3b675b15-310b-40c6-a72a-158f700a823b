from django.db import models
import uuid
from django.utils.translation import gettext_lazy as _

# Create your models here.
class Year(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    year = models.CharField(max_length=10, verbose_name=_("Year"), unique=True, help_text=_("Year"))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Year"
        verbose_name_plural = "Years"
        ordering = ['-year']

    def __str__(self):
        return f"{self.year}"