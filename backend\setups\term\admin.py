from django.contrib import admin
from .models import Term


@admin.register(Term)
class TermAdmin(admin.ModelAdmin):
    """Admin interface for Term model"""

    list_display = ['name', 'description_preview', 'updated_at']
    list_filter = ['updated_at']
    search_fields = ['name', 'description']
    ordering = ['name']
    readonly_fields = ['id', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'name', 'description')
        }),
        ('Timestamps', {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )

    def description_preview(self, obj):
        """Show a preview of the description"""
        if obj.description:
            return obj.description[:50] + "..." if len(obj.description) > 50 else obj.description
        return "No description"
    description_preview.short_description = "Description"

    def get_queryset(self, request):
        """Optimize queryset for admin"""
        return super().get_queryset(request)


