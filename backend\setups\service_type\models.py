from django.db import models
from django.core.exceptions import ValidationError
from decimal import Decimal
import uuid

class ServiceType(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True, help_text="Detailed description of the service")
    fee = models.DecimalField(max_digits=10, decimal_places=2, help_text="Service fee amount")
    url = models.URLField(blank=True, null=True, help_text="URL link for this service (optional)")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Many-to-many relationship with DocumentType
    document_types = models.ManyToManyField(
        'document_type.DocumentType',
        related_name='service_types',
        blank=True,
        help_text="Document types associated with this service"
    )

    class Meta:
        ordering = ['name']
        verbose_name = 'Service Type'
        verbose_name_plural = 'Service Types'

    def clean(self):
        """Validate the service type data."""
        super().clean()

        # Validate name
        if not self.name or not self.name.strip():
            raise ValidationError({'name': 'Service type name is required.'})

        # Clean and validate name format
        self.name = self.name.strip()
        if len(self.name) < 2:
            raise ValidationError({'name': 'Service type name must be at least 2 characters long.'})

        # Check for duplicate names (case-insensitive)
        existing = ServiceType.objects.filter(
            name__iexact=self.name
        ).exclude(pk=self.pk if self.pk else None)

        if existing.exists():
            raise ValidationError({'name': 'A service type with this name already exists.'})

        # Validate fee
        if self.fee is not None:
            if self.fee < Decimal('0.00'):
                raise ValidationError({'fee': 'Service fee cannot be negative.'})
            if self.fee > Decimal('99999999.99'):
                raise ValidationError({'fee': 'Service fee is too large.'})

        # Validate URL
        if self.url and self.url.strip():
            self.url = self.url.strip()
            # Basic URL validation (Django URLField will handle the rest)
            if len(self.url) > 200:
                raise ValidationError({'url': 'URL is too long (maximum 200 characters).'})
        elif self.url == '':
            # Convert empty string to None for consistency
            self.url = None

    def save(self, *args, **kwargs):
        """Run full validation before saving."""
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} (${self.fee})"

    @property
    def document_types_count(self):
        """Return the number of associated document types."""
        return self.document_types.count()

    @property
    def active_document_types_count(self):
        """Return the number of active associated document types."""
        return self.document_types.filter(is_active=True).count()
