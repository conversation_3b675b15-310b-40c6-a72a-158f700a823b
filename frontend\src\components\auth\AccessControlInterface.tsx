import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Shield, 
  Plus, 
  Edit, 
  Trash2,
  Search, 
  Filter, 
  RefreshCw,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Settings,
  Eye,
  Play,
  Code,
  FileText,
  Database,
  Globe,
  Users,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';
import { accessControlAPI } from '@/services/authAPI';
import { AccessControlRule } from '@/types/auth';

const AccessControlInterface: React.FC = () => {
  const [rules, setRules] = useState<AccessControlRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showTestDialog, setShowTestDialog] = useState(false);
  const [editingRule, setEditingRule] = useState<AccessControlRule | null>(null);
  const [testingRule, setTestingRule] = useState<AccessControlRule | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEffect, setFilterEffect] = useState('');
  const [filterActive, setFilterActive] = useState<boolean | undefined>(undefined);

  // Form states
  const [ruleForm, setRuleForm] = useState<Omit<AccessControlRule, 'id' | 'created_at' | 'updated_at'>>({
    name: '',
    resource: '',
    action: '',
    conditions: {},
    effect: 'allow',
    priority: 1,
    is_active: true
  });

  const [testContext, setTestContext] = useState({
    user_id: '',
    resource: '',
    action: '',
    ip_address: '',
    user_agent: '',
    additional_context: '{}'
  });

  const [testResult, setTestResult] = useState<any>(null);

  useEffect(() => {
    loadRules();
  }, []);

  const loadRules = async () => {
    try {
      setLoading(true);
      const response = await accessControlAPI.getRules();
      setRules(response.data.results);
    } catch (error) {
      toast.error('Failed to load access control rules');
      console.error('Error loading rules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRule = async () => {
    try {
      await accessControlAPI.createRule(ruleForm);
      toast.success('Access control rule created successfully');
      setShowCreateDialog(false);
      setRuleForm({
        name: '',
        resource: '',
        action: '',
        conditions: {},
        effect: 'allow',
        priority: 1,
        is_active: true
      });
      loadRules();
    } catch (error) {
      toast.error('Failed to create access control rule');
      console.error('Error creating rule:', error);
    }
  };

  const handleUpdateRule = async () => {
    if (!editingRule) return;
    
    try {
      await accessControlAPI.updateRule(editingRule.id, ruleForm);
      toast.success('Access control rule updated successfully');
      setShowEditDialog(false);
      setEditingRule(null);
      setRuleForm({
        name: '',
        resource: '',
        action: '',
        conditions: {},
        effect: 'allow',
        priority: 1,
        is_active: true
      });
      loadRules();
    } catch (error) {
      toast.error('Failed to update access control rule');
      console.error('Error updating rule:', error);
    }
  };

  const handleDeleteRule = async (ruleId: number) => {
    if (!confirm('Are you sure you want to delete this access control rule?')) return;
    
    try {
      await accessControlAPI.deleteRule(ruleId);
      toast.success('Access control rule deleted successfully');
      loadRules();
    } catch (error) {
      toast.error('Failed to delete access control rule');
      console.error('Error deleting rule:', error);
    }
  };

  const handleTestRule = async () => {
    if (!testingRule) return;
    
    try {
      let additionalContext = {};
      try {
        additionalContext = JSON.parse(testContext.additional_context);
      } catch (e) {
        toast.error('Invalid JSON in additional context');
        return;
      }

      const context = {
        user_id: parseInt(testContext.user_id) || null,
        resource: testContext.resource,
        action: testContext.action,
        ip_address: testContext.ip_address,
        user_agent: testContext.user_agent,
        ...additionalContext
      };

      const response = await accessControlAPI.testRule(testingRule, context);
      setTestResult(response.data);
      toast.success('Rule test completed');
    } catch (error) {
      toast.error('Failed to test access control rule');
      console.error('Error testing rule:', error);
    }
  };

  const openEditDialog = (rule: AccessControlRule) => {
    setEditingRule(rule);
    setRuleForm({
      name: rule.name,
      resource: rule.resource,
      action: rule.action,
      conditions: rule.conditions,
      effect: rule.effect,
      priority: rule.priority,
      is_active: rule.is_active
    });
    setShowEditDialog(true);
  };

  const openTestDialog = (rule: AccessControlRule) => {
    setTestingRule(rule);
    setTestContext({
      user_id: '',
      resource: rule.resource,
      action: rule.action,
      ip_address: '',
      user_agent: '',
      additional_context: '{}'
    });
    setTestResult(null);
    setShowTestDialog(true);
  };

  const getEffectBadge = (effect: string) => {
    return effect === 'allow' ? (
      <Badge variant="outline" className="bg-green-100 text-green-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        Allow
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-red-100 text-red-800">
        <XCircle className="h-3 w-3 mr-1" />
        Deny
      </Badge>
    );
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="outline" className="bg-blue-100 text-blue-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-gray-100 text-gray-800">
        <XCircle className="h-3 w-3 mr-1" />
        Inactive
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const filteredRules = rules.filter(rule => {
    const matchesSearch = rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rule.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rule.action.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesEffect = !filterEffect || rule.effect === filterEffect;
    const matchesActive = filterActive === undefined || rule.is_active === filterActive;
    
    return matchesSearch && matchesEffect && matchesActive;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Access Control Interface</h1>
          <p className="text-muted-foreground">
            Manage access control rules and security policies
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadRules}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Rule
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search Rules</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name, resource, or action..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="effect-filter">Effect</Label>
              <Select value={filterEffect} onValueChange={setFilterEffect}>
                <SelectTrigger>
                  <SelectValue placeholder="All effects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All effects</SelectItem>
                  <SelectItem value="allow">Allow</SelectItem>
                  <SelectItem value="deny">Deny</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="status-filter">Status</Label>
              <Select 
                value={filterActive === undefined ? '' : filterActive.toString()} 
                onValueChange={(value) => setFilterActive(value === '' ? undefined : value === 'true')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setFilterEffect('');
                  setFilterActive(undefined);
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Access Control Rules Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Access Control Rules ({filteredRules.length})</span>
          </CardTitle>
          <CardDescription>
            Manage security policies and access control rules
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Rule Name</TableHead>
                  <TableHead>Resource</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Effect</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading rules...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredRules.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="text-muted-foreground">
                        <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No access control rules found</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRules.map((rule) => (
                    <TableRow key={rule.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{rule.name}</div>
                          <div className="text-sm text-muted-foreground">
                            ID: {rule.id}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-1 py-0.5 rounded">
                          {rule.resource}
                        </code>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-muted px-1 py-0.5 rounded">
                          {rule.action}
                        </code>
                      </TableCell>
                      <TableCell>{getEffectBadge(rule.effect)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{rule.priority}</Badge>
                      </TableCell>
                      <TableCell>{getStatusBadge(rule.is_active)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatDate(rule.created_at)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openTestDialog(rule)}
                          >
                            <Play className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(rule)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteRule(rule.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create Rule Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Access Control Rule</DialogTitle>
            <DialogDescription>
              Define a new access control rule with conditions and effects.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="create-name">Rule Name *</Label>
                <Input
                  id="create-name"
                  value={ruleForm.name}
                  onChange={(e) => setRuleForm({ ...ruleForm, name: e.target.value })}
                  placeholder="Enter rule name"
                />
              </div>

              <div>
                <Label htmlFor="create-priority">Priority</Label>
                <Input
                  id="create-priority"
                  type="number"
                  value={ruleForm.priority}
                  onChange={(e) => setRuleForm({ ...ruleForm, priority: parseInt(e.target.value) || 1 })}
                  min="1"
                  max="100"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="create-resource">Resource *</Label>
                <Input
                  id="create-resource"
                  value={ruleForm.resource}
                  onChange={(e) => setRuleForm({ ...ruleForm, resource: e.target.value })}
                  placeholder="e.g., /api/users/*"
                />
              </div>

              <div>
                <Label htmlFor="create-action">Action *</Label>
                <Input
                  id="create-action"
                  value={ruleForm.action}
                  onChange={(e) => setRuleForm({ ...ruleForm, action: e.target.value })}
                  placeholder="e.g., read, write, delete"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="create-effect">Effect</Label>
              <Select
                value={ruleForm.effect}
                onValueChange={(value: 'allow' | 'deny') => setRuleForm({ ...ruleForm, effect: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="allow">Allow</SelectItem>
                  <SelectItem value="deny">Deny</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="create-conditions">Conditions (JSON)</Label>
              <Textarea
                id="create-conditions"
                value={JSON.stringify(ruleForm.conditions, null, 2)}
                onChange={(e) => {
                  try {
                    const conditions = JSON.parse(e.target.value);
                    setRuleForm({ ...ruleForm, conditions });
                  } catch (error) {
                    // Invalid JSON, keep the text but don't update the form
                  }
                }}
                rows={4}
                placeholder='{"ip_range": "***********/24", "time_range": "09:00-17:00"}'
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="create-is-active"
                checked={ruleForm.is_active}
                onCheckedChange={(checked) => setRuleForm({ ...ruleForm, is_active: checked })}
              />
              <Label htmlFor="create-is-active">Active</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateRule}>
              Create Rule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Rule Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Access Control Rule</DialogTitle>
            <DialogDescription>
              Modify the access control rule settings.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-name">Rule Name</Label>
                <Input
                  id="edit-name"
                  value={ruleForm.name}
                  onChange={(e) => setRuleForm({ ...ruleForm, name: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-priority">Priority</Label>
                <Input
                  id="edit-priority"
                  type="number"
                  value={ruleForm.priority}
                  onChange={(e) => setRuleForm({ ...ruleForm, priority: parseInt(e.target.value) || 1 })}
                  min="1"
                  max="100"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-resource">Resource</Label>
                <Input
                  id="edit-resource"
                  value={ruleForm.resource}
                  onChange={(e) => setRuleForm({ ...ruleForm, resource: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-action">Action</Label>
                <Input
                  id="edit-action"
                  value={ruleForm.action}
                  onChange={(e) => setRuleForm({ ...ruleForm, action: e.target.value })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="edit-effect">Effect</Label>
              <Select
                value={ruleForm.effect}
                onValueChange={(value: 'allow' | 'deny') => setRuleForm({ ...ruleForm, effect: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="allow">Allow</SelectItem>
                  <SelectItem value="deny">Deny</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="edit-conditions">Conditions (JSON)</Label>
              <Textarea
                id="edit-conditions"
                value={JSON.stringify(ruleForm.conditions, null, 2)}
                onChange={(e) => {
                  try {
                    const conditions = JSON.parse(e.target.value);
                    setRuleForm({ ...ruleForm, conditions });
                  } catch (error) {
                    // Invalid JSON, keep the text but don't update the form
                  }
                }}
                rows={4}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="edit-is-active"
                checked={ruleForm.is_active}
                onCheckedChange={(checked) => setRuleForm({ ...ruleForm, is_active: checked })}
              />
              <Label htmlFor="edit-is-active">Active</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateRule}>
              Update Rule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AccessControlInterface;
