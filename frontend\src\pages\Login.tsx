import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, User, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from 'sonner';
import { authAPI, settingsAPI } from '@/services/api';
import Layout from '@/components/Layout';
import SessionExpiredMessage from '@/components/SessionExpiredMessage';

// Define the login form schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(6, 'Password must be at least 6 characters')
});

type LoginFormValues = z.infer<typeof loginSchema>;

const Login = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Session expiration state
  const [showSessionExpired, setShowSessionExpired] = useState(false);

  // Rate limiting state
  const [rateLimitedUntil, setRateLimitedUntil] = useState<number | null>(null);
  const [countdown, setCountdown] = useState<number>(0);

  // State for organization settings
  const [settings, setSettings] = useState<{
    systemName: string;
    organizationName: string;
  }>({
    systemName: '',
    organizationName: ''
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Check for session expiration
  useEffect(() => {
    const reason = searchParams.get('reason');
    if (reason === 'session_expired') {
      setShowSessionExpired(true);
    }
  }, [searchParams]);

  // Check if user is already authenticated
  useEffect(() => {
    const token = localStorage.getItem('token');
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    const userData = localStorage.getItem('user');
    const user = userData ? JSON.parse(userData) : null;

    if (token && isAuthenticated && user) {
      // User is already logged in, redirect to appropriate dashboard
      if (user.is_staff || user.is_superuser) {
        navigate('/graduate-admin');
      } else {
        navigate('/dashboard');
      }
    }
  }, [navigate]);

  // Rate limiting countdown timer
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (rateLimitedUntil) {
      interval = setInterval(() => {
        const now = Date.now();
        const timeLeft = Math.max(0, Math.ceil((rateLimitedUntil - now) / 1000));

        if (timeLeft <= 0) {
          setRateLimitedUntil(null);
          setCountdown(0);
        } else {
          setCountdown(timeLeft);
        }
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [rateLimitedUntil]);

  // Fetch organization settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          setSettings({
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || ''
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    try {
      // Use AuthContext login method for better integration
      const success = await login(data.username, data.password);

      if (!success) {
        toast.error('Authentication failed', {
          description: 'Please check your credentials and try again'
        });
        setIsLoading(false);
        return;
      }

      // Login successful - AuthContext handles user data fetching
      toast.success('Login successful!', {
        description: 'Welcome back!'
      });

      // Redirect based on user role (user data is available from AuthContext)
      // Small delay to ensure user data is loaded
      setTimeout(() => {
        if (user?.is_staff || user?.is_superuser) {
          navigate('/graduate-admin');
        } else {
          navigate('/dashboard');
        }
      }, 100);
    } catch (error: any) {
      console.error('Login error:', error);

      // Clear any existing authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('user');

      // Show appropriate error message
      if (error.response) {
        // Handle rate limiting (429 error)
        if (error.response.status === 429) {
          const retryAfter = error.response.data?.retry_after || 60; // Default to 60 seconds
          const rateLimitEnd = Date.now() + (retryAfter * 1000);

          setRateLimitedUntil(rateLimitEnd);
          setCountdown(retryAfter);

          toast.error('Too many login attempts', {
            description: `Please wait ${retryAfter} seconds before trying again.`,
            duration: 5000,
          });
        } else if (error.response.status === 403 && error.response.data?.error?.includes('CSRF')) {
          // Handle CSRF token errors
          toast.error('Security token error', {
            description: 'Please refresh the page and try again.',
            duration: 5000,
          });
          // Optionally refresh the page after a short delay
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          // Other server errors
          const errorMessage = error.response.data?.detail || error.response.data?.error || 'Invalid username or password';
          toast.error(`Authentication failed: ${errorMessage}`);
        }
      } else if (error.request) {
        // The request was made but no response was received
        toast.error('Authentication failed: No response from server');
      } else {
        // Something happened in setting up the request
        toast.error(`Authentication failed: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Custom pattern background for hero section
  const heroPatternImage = '/images/university-pattern.svg';

  return (
    <Layout>
      {/* Session Expired Message */}
      <SessionExpiredMessage
        reason={searchParams.get('reason') || undefined}
        onDismiss={() => setShowSessionExpired(false)}
      />

      {/* Hero Section with Animated Patterned Background - Half Height */}
      <section className="relative py-4 md:py-6 h-[175px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          {isSettingsLoading ? (
            <div className="h-8 w-64 bg-white/20 animate-pulse rounded mx-auto"></div>
          ) : (
            <h1 className="text-xl md:text-2xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
              {settings.organizationName} {settings.systemName} Login
            </h1>
          )}
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Sign in to access your account and manage your applications
          </p>
        </div>
      </section>

      {/* Main Content */}
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gondar mb-4">Account Login</h2>
          <div className="h-1 w-20 bg-gondar-accent mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-600 max-w-3xl mx-auto text-lg">
            Sign in to your {isSettingsLoading ? (
              <span className="inline-block h-5 w-32 bg-gray-200 animate-pulse rounded align-middle"></span>
            ) : (
              settings.organizationName
            )} account
          </p>
        </div>

        <div className="max-w-lg mx-auto">
          {/* Login Form */}
            <Card className="shadow-lg overflow-hidden">
              <CardHeader className="bg-gray-50">
                <CardTitle className="text-lg">Sign In</CardTitle>
                <CardDescription>Enter your credentials to access your account</CardDescription>
              </CardHeader>
              <CardContent className="p-6 pt-4">
                {/* Rate Limiting Warning */}
                {rateLimitedUntil && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-red-800">Too many login attempts</p>
                        <p className="text-xs text-red-600 mt-1">
                          Please wait {countdown} seconds before trying again. This is a security measure to protect your account.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                    <FormField
                      control={form.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Username <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                placeholder="Enter your username"
                                className="pl-10 bg-white"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500 text-xs mt-1" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-gray-700">
                            Password <span className="text-red-500">*</span>
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Lock className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                type={showPassword ? 'text' : 'password'}
                                placeholder="••••••••"
                                className="pl-10 bg-white"
                                {...field}
                              />
                              <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
                                tabIndex={-1}
                                aria-label={showPassword ? "Hide password" : "Show password"}
                              >
                                {showPassword ? (
                                  <EyeOff className="h-5 w-5" />
                                ) : (
                                  <Eye className="h-5 w-5" />
                                )}
                              </button>
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500 text-xs mt-1" />
                        </FormItem>
                      )}
                    />

                    <div className="flex items-center justify-between pt-2">
                      <div className="flex items-center">
                        <div className="relative inline-flex items-center">
                          <input
                            id="remember-me"
                            name="remember-me"
                            type="checkbox"
                            className="h-4 w-4 text-[#1a73c0] focus:ring-[#1a73c0]/20 border-gray-300 rounded"
                          />
                          <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 select-none">
                            Remember me
                          </label>
                        </div>
                      </div>
                      <Link
                        to="/forgot-password"
                        className="text-sm font-medium text-[#1a73c0] hover:text-[#145da1] transition-colors flex items-center"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                        </svg>
                        Forgot password?
                      </Link>
                    </div>

                    <div className="pt-4">
                      <Button
                        type="submit"
                        className={`w-full font-medium py-2 h-10 transition-all duration-200 ${
                          rateLimitedUntil
                            ? 'bg-red-500 hover:bg-red-600 text-white cursor-not-allowed'
                            : 'bg-[#1a73c0] hover:bg-[#145da1] text-white'
                        }`}
                        disabled={isLoading || !!rateLimitedUntil}
                      >
                        {rateLimitedUntil ? (
                          <div className="flex items-center justify-center">
                            <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                            Rate limited - Wait {countdown}s
                          </div>
                        ) : isLoading ? (
                          <div className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Signing in...
                          </div>
                        ) : (
                          'Sign in'
                        )}
                      </Button>

                      <div className="relative flex items-center justify-center mt-6 mb-2">
                        <div className="absolute border-t border-gray-200 w-full"></div>
                        <div className="relative bg-white px-4 text-sm text-gray-500">Or</div>
                      </div>

                      <p className="text-center text-sm text-gray-600 mb-2">
                        Don't have an account?{' '}
                        <Link to="/register" className="text-[#1a73c0] hover:text-[#145da1] font-medium">
                          Create an account
                        </Link>
                      </p>
                    </div>
                  </form>
                </Form>


              </CardContent>
              <div className="bg-gray-50 px-6 py-4 border-t border-gray-100 text-center">
                <p className="text-xs text-gray-600 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Protected by industry standard security protocols
                </p>
              </div>
            </Card>
        </div>
      </div>

      {/* BackToTop button is already included in the Layout component */}
    </Layout>
  );
};

export default Login;
