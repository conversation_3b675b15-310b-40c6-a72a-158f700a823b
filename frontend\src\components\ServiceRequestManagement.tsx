import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Plus,
  Search,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Filter,
  Download,
  FileText,
  Calendar,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  GraduationCap,
  MapPin
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

import {
  ServiceRequest,
  ServiceRequestListResponse,
  ServiceRequestFilters,
  ServiceRequestStatistics,
  STATUS_OPTIONS
} from '@/types/serviceRequest';

import {
  serviceRequestAPI,
  serviceRequestUtils
} from '@/services/serviceRequestAPI';

import ServiceRequestForm from './ServiceRequestForm';

const ServiceRequestManagement: React.FC = () => {
  // State management
  const [serviceRequests, setServiceRequests] = useState<ServiceRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [statistics, setStatistics] = useState<ServiceRequestStatistics | null>(null);

  // Dialog states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<ServiceRequest | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // Load service requests
  const fetchServiceRequests = async () => {
    setLoading(true);
    try {
      const filters: ServiceRequestFilters = {
        name: searchTerm || undefined,
        status: (statusFilter && statusFilter !== 'all') ? statusFilter : undefined,
      };

      const response = await serviceRequestAPI.getAll(filters);

      // Handle both paginated and non-paginated response structures
      if (response && typeof response === 'object') {
        let requests = [];
        let count = 0;

        // Check if it's a paginated response (has results and count)
        if (response.results && Array.isArray(response.results)) {
          requests = response.results;
          count = typeof response.count === 'number' ? response.count : response.results.length;
        }
        // Check if it's a direct array response
        else if (Array.isArray(response)) {
          requests = response;
          count = response.length;
        }
        // Handle other object structures
        else {
          requests = [];
          count = 0;
        }



        setServiceRequests(requests);
        setTotalCount(count);
      } else {

        setServiceRequests([]);
        setTotalCount(0);
      }
    } catch (error: any) {
      console.error('Error fetching service requests:', error);
      setServiceRequests([]);
      setTotalCount(0);

      // Check if it's a network error (server not running)
      if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        toast.error('Cannot connect to server. Please ensure the backend server is running on port 8000.');
      } else if (error.response?.status === 401) {
        toast.error('Authentication required. Please log in to access service requests.');
      } else {
        toast.error('Failed to load service requests. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const fetchStatistics = async () => {
    try {
      const stats = await serviceRequestAPI.getStatistics();
      if (stats && typeof stats === 'object') {
        setStatistics(stats);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
      // Don't show error toast for statistics as it's not critical
      setStatistics(null);
    }
  };

  // Initial load
  useEffect(() => {
    // Add a small delay to ensure component is mounted
    const timer = setTimeout(() => {
      fetchServiceRequests();
      fetchStatistics();
    }, 100);

    return () => clearTimeout(timer);
  }, [searchTerm, statusFilter, currentPage, itemsPerPage]);

  // Filter service requests based on search term and status
  const filteredServiceRequests = serviceRequests.filter((request) => {
    const matchesSearch = request.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.mobile.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.service_type_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || statusFilter === '' ||
      request.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredServiceRequests.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredServiceRequests.length / itemsPerPage);



  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  // Dialog handlers
  const openCreateDialog = () => {
    setSelectedRequest(null);
    setFormMode('create');
    setIsFormOpen(true);
  };

  const openEditDialog = (request: ServiceRequest) => {
    setSelectedRequest(request);
    setFormMode('edit');
    setIsFormOpen(true);
  };

  const openViewDialog = (request: ServiceRequest) => {
    setSelectedRequest(request);
    setIsViewOpen(true);
  };

  const openDeleteDialog = (request: ServiceRequest) => {
    setSelectedRequest(request);
    setIsDeleteOpen(true);
  };

  const handleDelete = async () => {
    if (!selectedRequest) return;

    try {
      await serviceRequestAPI.softDelete(selectedRequest.id);
      toast.success('Service request deleted successfully');
      fetchServiceRequests();
      setIsDeleteOpen(false);
      setSelectedRequest(null);
    } catch (error) {
      console.error('Error deleting service request:', error);
      toast.error('Failed to delete service request');
    }
  };

  const handleStatusUpdate = async (requestId: string, newStatus: string) => {
    try {
      await serviceRequestAPI.updateStatus(requestId, newStatus);
      toast.success('Status updated successfully');
      fetchServiceRequests();
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = STATUS_OPTIONS.find(s => s.value === status);
    if (!statusConfig) return null;

    return (
      <Badge
        variant="outline"
        className={cn(
          "flex items-center space-x-1",
          status === 'pending' && "border-orange-200 text-orange-700 bg-orange-50",
          status === 'processing' && "border-blue-200 text-blue-700 bg-blue-50",
          status === 'completed' && "border-green-200 text-green-700 bg-green-50",
          status === 'rejected' && "border-red-200 text-red-700 bg-red-50"
        )}
      >
        {getStatusIcon(status)}
        <span>{statusConfig.label}</span>
      </Badge>
    );
  };

  if (loading && (!Array.isArray(serviceRequests) || serviceRequests.length === 0)) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0] mx-auto mb-4"></div>
            <div className="text-center">
              <p className="text-gray-600 font-medium">Loading service requests...</p>
              <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-[#1a73c0]" />
                <div>
                  <p className="text-sm text-gray-600">Total Requests</p>
                  <p className="text-2xl font-bold text-[#1a73c0]">{statistics.total_requests}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-sm text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-orange-500">{statistics.by_status.pending || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">Processing</p>
                  <p className="text-2xl font-bold text-blue-500">{statistics.by_status.processing || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-green-500">{statistics.by_status.completed || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Card */}
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Service Request Management</CardTitle>
                <CardDescription className="mt-1">
                  Manage and track all service requests
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={fetchServiceRequests}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 transition-all duration-200"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                onClick={openCreateDialog}
                className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Request
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          {/* Search & Filter Section */}
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
            <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Search & Filter
            </h3>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                <Input
                  placeholder="Search by name, email, or mobile..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="border-blue-200 focus:ring-blue-400 shadow-sm">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {STATUS_OPTIONS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          {(searchTerm || statusFilter !== 'all') && (
            <div className="p-3 border border-blue-100 rounded-md bg-white shadow-sm mb-6">
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm font-medium text-[#1a73c0] flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                  Active Filters:
                </span>

                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Search: {searchTerm}
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove search filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                {statusFilter !== 'all' && statusFilter !== '' && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                    Status: {STATUS_OPTIONS.find(s => s.value === statusFilter)?.label || statusFilter}
                    <button
                      onClick={() => setStatusFilter('all')}
                      className="ml-2 hover:text-blue-900 transition-colors"
                      aria-label="Remove status filter"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </span>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                  }}
                  className="h-7 px-3 text-xs border-blue-200 text-blue-600 hover:bg-blue-50 transition-colors"
                >
                  Clear All
                </Button>
              </div>
            </div>
          )}

          {/* Service Requests Table */}
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="text-[#1a73c0] font-medium">Requester</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Service Type</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Status</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">College</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Created</TableHead>
                    <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0]"></div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">Loading service requests...</p>
                            <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : !Array.isArray(currentItems) || currentItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="p-4 bg-blue-50 rounded-full">
                            <FileText className="h-8 w-8 text-blue-400" />
                          </div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">No service requests found</p>
                            <p className="text-sm text-gray-500">
                              {searchTerm || statusFilter ? 'Try adjusting your search criteria' : 'Get started by creating your first service request'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    Array.isArray(currentItems) && currentItems.map((request) => (
                      <TableRow key={request.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell>
                          <div>
                            <p className="font-medium text-[#1a73c0]">{request.full_name}</p>
                            <p className="text-sm text-gray-500">{request.email}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{request.service_type_name}</p>
                            <p className="text-sm text-gray-500">{request.degree_name}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(request.status)}
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">{request.college_display}</p>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1 text-sm text-gray-500">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(request.created_at).toLocaleDateString()}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openViewDialog(request)}
                              title="View Details"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(request)}
                              title="Edit"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openDeleteDialog(request)}
                              className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>

        <CardFooter>
          {filteredServiceRequests.length > 0 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm w-full">
              <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(e.target.value)}
                  className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  id="page-size"
                  name="page-size"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm font-medium text-[#1a73c0]">per page</span>
                <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                  <span className="font-medium text-[#1a73c0]">
                    {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredServiceRequests.length)}
                  </span> of <span className="font-medium text-[#1a73c0]">{filteredServiceRequests.length}</span> records
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="First Page"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage === 1
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Previous Page"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  {/* Dynamic page number buttons with ellipsis for large page counts */}
                  {totalPages <= 7 ? (
                    // If we have 7 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                      <Button
                        key={number}
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(number)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === number
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {number}
                      </Button>
                    ))
                  ) : (
                    // For more than 7 pages, show a condensed version with ellipsis
                    <>
                      {/* Always show first page */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === 1
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        1
                      </Button>

                      {/* Show ellipsis if not showing first few pages */}
                      {currentPage > 3 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Show current page and adjacent pages */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter(number =>
                          number > 1 &&
                          number < totalPages &&
                          (
                            number === currentPage - 1 ||
                            number === currentPage ||
                            number === currentPage + 1 ||
                            (currentPage <= 3 && number <= 4) ||
                            (currentPage >= totalPages - 2 && number >= totalPages - 3)
                          )
                        )
                        .map(number => (
                          <Button
                            key={number}
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePageChange(number)}
                            className={cn(
                              "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                              currentPage === number
                                ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                                : "bg-white text-gray-700 hover:bg-blue-50"
                            )}
                          >
                            {number}
                          </Button>
                        ))
                      }

                      {/* Show ellipsis if not showing last few pages */}
                      {currentPage < totalPages - 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                        >
                          ...
                        </Button>
                      )}

                      {/* Always show last page */}
                      {totalPages > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === totalPages
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {totalPages}
                        </Button>
                      )}
                    </>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Next Page"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage >= totalPages}
                    className={cn(
                      "h-9 w-9 p-0 rounded-none",
                      currentPage >= totalPages
                        ? "text-gray-400 bg-gray-50"
                        : "text-[#1a73c0] bg-white hover:bg-blue-50"
                    )}
                    title="Last Page"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Service Request Form Dialog */}
      <ServiceRequestForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSuccess={() => {
          fetchServiceRequests();
          fetchStatistics();
        }}
        editData={selectedRequest}
        mode={formMode}
      />

      {/* View Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">
                  Service Request Details
                </DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Complete information for {selectedRequest?.full_name}'s service request
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {selectedRequest && (
            <div className="p-6 space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-[#1a73c0] flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Personal Information
                  </h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-600">Full Name:</span>
                      <p className="text-sm">{selectedRequest.full_name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Email:</span>
                      <p className="text-sm">{selectedRequest.email}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Mobile:</span>
                      <p className="text-sm">{selectedRequest.mobile}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-[#1a73c0] flex items-center">
                    <GraduationCap className="h-5 w-5 mr-2" />
                    Academic Information
                  </h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-600">Service Type:</span>
                      <p className="text-sm">{selectedRequest.service_type_name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Degree:</span>
                      <p className="text-sm">{selectedRequest.degree_name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">College:</span>
                      <p className="text-sm">{selectedRequest.college_display}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600">Department:</span>
                      <p className="text-sm">{selectedRequest.department_display}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Information */}
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-medium text-[#1a73c0] mb-3">Status Information</h3>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-600">Current Status:</span>
                    {getStatusBadge(selectedRequest.status)}
                  </div>
                  <div className="text-sm text-gray-500">
                    Created: {new Date(selectedRequest.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>

              {/* Additional Information based on service type */}
              {selectedRequest.requires_mailing_address && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-[#1a73c0] flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Mailing Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium text-gray-600">Destination:</span>
                      <p className="text-sm">{selectedRequest.mailing_destination}</p>
                    </div>
                    {selectedRequest.institute_name && (
                      <>
                        <div>
                          <span className="text-sm font-medium text-gray-600">Institution:</span>
                          <p className="text-sm">{selectedRequest.institute_name}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-600">Country:</span>
                          <p className="text-sm">{selectedRequest.institute_country}</p>
                        </div>
                        <div className="md:col-span-2">
                          <span className="text-sm font-medium text-gray-600">Address:</span>
                          <p className="text-sm">{selectedRequest.institute_address}</p>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => setIsViewOpen(false)}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setIsViewOpen(false);
                    openEditDialog(selectedRequest);
                  }}
                  className="bg-[#1a73c0] hover:bg-blue-700 text-white transition-all duration-200"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Request
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader className="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-t-lg border-b border-red-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-red-500 rounded-xl shadow-lg">
                <Trash2 className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-red-600 mb-2">Delete Service Request</DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Are you sure you want to delete the service request from "{selectedRequest?.full_name}"? This action cannot be undone.
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          <div className="p-6">
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setIsDeleteOpen(false);
                  setSelectedRequest(null);
                }}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                className="bg-red-500 hover:bg-red-600 text-white transition-all duration-200"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Request
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ServiceRequestManagement;
