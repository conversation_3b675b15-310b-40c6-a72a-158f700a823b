/**
 * Permission Mappings for Access Control
 * 
 * This file defines the mapping between menu items/features and their required permissions.
 * Access control is based ONLY on Django permissions, not group membership.
 * 
 * When Django permissions are assigned to users (either directly or through groups),
 * the frontend will automatically grant access to corresponding features.
 */

export interface PermissionRequirement {
  // Required permissions (user needs ANY of these permissions)
  permissions: string[];
  
  // If true, user must have ALL permissions listed (default: false - ANY permission)
  requireAll?: boolean;
  
  // Additional requirements
  requireAuth?: boolean;
  requireStaff?: boolean;
  requireActive?: boolean;
  
  // Custom validation function (optional)
  customCheck?: (user: any) => boolean;
  
  // Description for debugging/documentation
  description?: string;
}

/**
 * Menu Permission Mappings
 * Maps menu keys to their permission requirements
 */
export const MENU_PERMISSIONS: Record<string, PermissionRequirement> = {
  // ===== BASIC USER MENUS =====
  'dashboard': {
    permissions: [],
    requireAuth: true,
    description: 'Basic dashboard access for authenticated users'
  },
  
  'personal-information': {
    permissions: [],
    requireAuth: true,
    description: 'Personal information management'
  },
  
  'application': {
    permissions: [],
    requireAuth: true,
    description: 'Application submission and management'
  },
  
  'application-status': {
    permissions: [],
    requireAuth: true,
    description: 'View application status'
  },
  
  'notifications': {
    permissions: [],
    requireAuth: true,
    description: 'User notifications'
  },

  // ===== DASHBOARD MENUS =====
  // Temporary: Using staff-only access for testing (no specific permissions required)
  'graduation-dashboard': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Graduate verification and management dashboard'
  },

  'application-dashboard': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Application management dashboard'
  },

  'service-fee-dashboard': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Service and fee management dashboard'
  },

  // ===== GRADUATE VERIFICATION =====
  'manage-graduates': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Graduate student management'
  },

  'manage-colleges': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'College management for verification'
  },

  'manage-departments': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Department management for verification'
  },

  'graduate-fields-of-study': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Graduate fields of study management'
  },
  
  'manage-programs': {
    permissions: [
      'view_program',
      'view_verificationprogram',
      'change_program',
      'change_verificationprogram'
    ],
    requireStaff: true,
    description: 'Academic program management'
  },

  // ===== APPLICATION PORTAL =====
  'manage-colleges-app': {
    permissions: [
      'view_college',
      'change_college',
      'view_applicationcollege'
    ],
    requireStaff: true,
    description: 'College management for applications'
  },
  
  'manage-departments-app': {
    permissions: [
      'view_department',
      'change_department',
      'view_applicationdepartment'
    ],
    requireStaff: true,
    description: 'Department management for applications'
  },
  
  'manage-programs-app': {
    permissions: [
      'view_program',
      'change_program',
      'view_applicationprogram'
    ],
    requireStaff: true,
    description: 'Program management for applications'
  },
  
  'manage-study-programs': {
    permissions: [
      'view_studyprogram',
      'change_studyprogram'
    ],
    requireStaff: true,
    description: 'Study program management'
  },
  
  'manage-admission-types': {
    permissions: [
      'view_admissiontype',
      'change_admissiontype'
    ],
    requireStaff: true,
    description: 'Admission type management'
  },
  
  'manage-registration-periods': {
    permissions: [
      'view_registrationperiod',
      'change_registrationperiod'
    ],
    requireStaff: true,
    description: 'Registration period management'
  },
  
  'manage-fields-of-study-app': {
    permissions: [
      'view_fieldofstudy',
      'change_fieldofstudy',
      'view_applicationfield'
    ],
    requireStaff: true,
    description: 'Fields of study for applications'
  },
  
  'manage-years': {
    permissions: [
      'view_academicyear',
      'change_academicyear'
    ],
    requireStaff: true,
    description: 'Academic year management'
  },
  
  'manage-terms': {
    permissions: [
      'view_term',
      'change_term'
    ],
    requireStaff: true,
    description: 'Academic term management'
  },
  
  'application-information': {
    permissions: [
      'view_application',
      'view_applicationinfo'
    ],
    requireStaff: true,
    description: 'Application information management'
  },
  
  'manage-applicants': {
    permissions: [
      'view_applicant',
      'change_applicant'
    ],
    requireStaff: true,
    description: 'Applicant management'
  },

  // ===== SERVICES =====
  'service-types': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Service type management'
  },

  'document-types': {
    permissions: [], // No permissions required for testing
    requireStaff: false, // Remove staff requirement for testing
    description: 'Document type management'
  },

  'alumni-applications-service': {
    permissions: [], // No permissions required for testing
    requireStaff: true,
    description: 'Alumni service management'
  },
  
  'downloadable-content': {
    permissions: [
      'view_document',
      'change_document',
      'view_content'
    ],
    requireStaff: true,
    description: 'Downloadable content management'
  },

  // ===== OFFICIALS =====
  'certificate-types': {
    permissions: [
      'view_certificatetype',
      'change_certificatetype'
    ],
    requireStaff: true,
    description: 'Certificate type management'
  },
  
  'official-certificates': {
    permissions: [
      'view_certificate',
      'change_certificate'
    ],
    requireStaff: true,
    description: 'Official certificate management'
  },

  // ===== COMMUNICATION =====
  'announcements': {
    permissions: [
      'view_announcement',
      'add_announcement',
      'change_announcement'
    ],
    requireStaff: true,
    description: 'Announcement management'
  },
  
  'official-management': {
    permissions: [
      'view_official',
      'change_official'
    ],
    requireStaff: true,
    description: 'Official management'
  },

  // ===== ADMINISTRATIVE =====
  'user-management': {
    permissions: [
      'view_user',
      'change_user',
      'add_user',
      'delete_user'
    ],
    requireStaff: true,
    description: 'User account management'
  },
  
  'general-settings': {
    permissions: [
      'view_settings',
      'change_settings'
    ],
    requireStaff: true,
    description: 'General system settings'
  },

  // ===== REPORTS & ANALYTICS =====
  'reports': {
    permissions: [
      'view_report',
      'generate_report'
    ],
    requireStaff: true,
    description: 'Report generation and viewing'
  },
  
  'analytics': {
    permissions: [
      'view_analytics',
      'view_statistics'
    ],
    requireStaff: true,
    description: 'Analytics and statistics'
  }
};

/**
 * Feature Permission Mappings
 * Maps feature names to their permission requirements
 */
export const FEATURE_PERMISSIONS: Record<string, PermissionRequirement> = {
  // Graduate management
  'graduate': {
    permissions: [
      'view_graduatestudent',
      'change_graduatestudent',
      'add_graduatestudent',
      'delete_graduatestudent'
    ],
    description: 'Graduate student management features'
  },
  
  // College management
  'college': {
    permissions: [
      'view_college',
      'change_college',
      'add_college',
      'delete_college',
      'view_verificationcollege',
      'change_verificationcollege'
    ],
    description: 'College management features'
  },
  
  // Department management
  'department': {
    permissions: [
      'view_department',
      'change_department',
      'add_department',
      'delete_department',
      'view_verificationdepartment',
      'change_verificationdepartment'
    ],
    description: 'Department management features'
  },
  
  // Program management
  'program': {
    permissions: [
      'view_program',
      'change_program',
      'add_program',
      'delete_program',
      'view_verificationprogram',
      'change_verificationprogram'
    ],
    description: 'Program management features'
  },
  
  // Service management
  'service': {
    permissions: [
      'view_service',
      'change_service',
      'add_service',
      'delete_service',
      'view_servicetype',
      'change_servicetype'
    ],
    description: 'Service management features'
  },
  
  // User management
  'user': {
    permissions: [
      'view_user',
      'change_user',
      'add_user',
      'delete_user'
    ],
    description: 'User management features'
  },
  
  // Document management
  'document': {
    permissions: [
      'view_document',
      'change_document',
      'add_document',
      'delete_document',
      'view_documenttype',
      'change_documenttype'
    ],
    description: 'Document management features'
  },
  
  // Certificate management
  'certificate': {
    permissions: [
      'view_certificate',
      'change_certificate',
      'add_certificate',
      'delete_certificate',
      'view_certificatetype',
      'change_certificatetype'
    ],
    description: 'Certificate management features'
  }
};

export default { MENU_PERMISSIONS, FEATURE_PERMISSIONS };
