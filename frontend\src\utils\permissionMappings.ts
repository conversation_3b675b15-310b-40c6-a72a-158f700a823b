/**
 * Django-Compatible Permission Mappings
 *
 * This file defines Django-style permission mappings that work exactly like Django's
 * built-in permission system. Each menu item maps to specific Django permissions
 * using the standard Django format: 'app_label.permission_codename'
 *
 * DJANGO PERMISSION FORMAT:
 * - 'auth.view_user' - View users in auth app
 * - 'auth.add_user' - Add users in auth app
 * - 'auth.change_user' - Change users in auth app
 * - 'auth.delete_user' - Delete users in auth app
 * - 'contenttypes.view_contenttype' - View content types
 * - 'admin.view_logentry' - View admin log entries
 *
 * PERMISSION CHECKING:
 * - Uses Django's user.has_perm() logic exactly
 * - Supports both direct permissions and group permissions
 * - Superusers automatically have all permissions
 * - Inactive users have no permissions
 *
 * CUSTOMIZATION:
 * - Update permission names to match your Django models
 * - Add new menu items with their required permissions
 * - Use OR logic (user needs ANY of the listed permissions)
 */

export interface PermissionRequirement {
  // Required permissions (user needs ANY of these permissions)
  permissions: string[];
  
  // If true, user must have ALL permissions listed (default: false - ANY permission)
  requireAll?: boolean;
  
  // Additional requirements
  requireAuth?: boolean;
  requireStaff?: boolean;
  requireActive?: boolean;
  
  // Custom validation function (optional)
  customCheck?: (user: any) => boolean;
  
  // Description for debugging/documentation
  description?: string;
}

/**
 * Menu Permission Mappings
 * Maps menu keys to their permission requirements
 */
export const MENU_PERMISSIONS: Record<string, PermissionRequirement> = {
  // ===== BASIC USER MENUS =====
  'dashboard': {
    permissions: [],
    requireAuth: true,
    description: 'Basic dashboard access for authenticated users'
  },
  
  'personal-information': {
    permissions: [],
    requireAuth: true,
    description: 'Personal information management'
  },
  
  'application': {
    permissions: [],
    requireAuth: true,
    description: 'Application submission and management'
  },
  
  'application-status': {
    permissions: [],
    requireAuth: true,
    description: 'View application status'
  },
  
  'notifications': {
    permissions: [],
    requireAuth: true,
    description: 'User notifications'
  },

  // ===== DASHBOARD MENUS =====
  // Comprehensive permission mapping with realistic Django permissions + fallbacks
  'graduation-dashboard': {
    permissions: [
      // Specific graduate-related permissions (if they exist)
      'graduation.view_graduatestudent',
      'verification.view_graduatestudent',
      'students.view_graduatestudent',
      'graduate.view_student',
      'academic.view_graduate',
      // Common Django auth permissions as fallbacks
      'auth.view_user',
      'auth.change_user'
    ],
    requireStaff: true,
    description: 'Graduate verification and management dashboard'
  },

  'application-dashboard': {
    permissions: [
      // Specific application-related permissions
      'applications.view_application',
      'admissions.view_application',
      'portal.view_applicant',
      'academic.view_applicant',
      'applications.view_applicant',
      // Fallback permissions
      'auth.view_user',
      'auth.view_group'
    ],
    requireStaff: true,
    description: 'Application management dashboard'
  },

  'service-fee-dashboard': {
    permissions: [
      // Service and fee related permissions
      'services.view_service',
      'fees.view_servicefee',
      'payments.view_payment',
      'services.view_servicetype',
      'finance.view_fee',
      // Fallback permissions
      'auth.view_user',
      'auth.change_user'
    ],
    requireStaff: true,
    description: 'Service and fee management dashboard'
  },

  // ===== GRADUATE VERIFICATION =====
  'manage-graduates': {
    permissions: [
      // Graduate student management permissions
      'verification.view_graduatestudent',
      'verification.change_graduatestudent',
      'students.view_graduatestudent',
      'students.change_graduatestudent',
      'academic.view_graduate',
      'academic.change_graduate',
      'graduation.view_student',
      'graduation.change_student',
      // Fallback permissions
      'auth.view_user',
      'auth.change_user'
    ],
    requireStaff: true,
    description: 'Graduate student management'
  },

  'manage-colleges': {
    permissions: [
      // College management permissions
      'verification.view_college',
      'verification.change_college',
      'academic.view_college',
      'academic.change_college',
      'institutions.view_college',
      'institutions.change_college',
      'colleges.view_college',
      'colleges.change_college',
      // Fallback permissions
      'auth.view_user',
      'auth.view_group'
    ],
    requireStaff: true,
    description: 'College management for verification'
  },

  'manage-departments': {
    permissions: [
      // Department management permissions
      'verification.view_department',
      'verification.change_department',
      'academic.view_department',
      'academic.change_department',
      'institutions.view_department',
      'institutions.change_department',
      'departments.view_department',
      'departments.change_department',
      // Fallback permissions
      'auth.view_user',
      'auth.view_group'
    ],
    requireStaff: true,
    description: 'Department management for verification'
  },

  'graduate-fields-of-study': {
    permissions: [
      // Field of study permissions
      'verification.view_fieldofstudy',
      'verification.change_fieldofstudy',
      'academic.view_fieldofstudy',
      'academic.change_fieldofstudy',
      'programs.view_fieldofstudy',
      'programs.change_fieldofstudy',
      // Fallback permissions
      'auth.view_user',
      'auth.view_group'
    ],
    requireStaff: true,
    description: 'Graduate fields of study management'
  },
  
  'manage-programs': {
    permissions: [
      'view_program',
      'view_verificationprogram',
      'change_program',
      'change_verificationprogram'
    ],
    requireStaff: true,
    description: 'Academic program management'
  },

  // ===== APPLICATION PORTAL =====
  'manage-colleges-app': {
    permissions: [
      'view_college',
      'change_college',
      'view_applicationcollege'
    ],
    requireStaff: true,
    description: 'College management for applications'
  },
  
  'manage-departments-app': {
    permissions: [
      'view_department',
      'change_department',
      'view_applicationdepartment'
    ],
    requireStaff: true,
    description: 'Department management for applications'
  },
  
  'manage-programs-app': {
    permissions: [
      'view_program',
      'change_program',
      'view_applicationprogram'
    ],
    requireStaff: true,
    description: 'Program management for applications'
  },
  
  'manage-study-programs': {
    permissions: [
      'view_studyprogram',
      'change_studyprogram'
    ],
    requireStaff: true,
    description: 'Study program management'
  },
  
  'manage-admission-types': {
    permissions: [
      'view_admissiontype',
      'change_admissiontype'
    ],
    requireStaff: true,
    description: 'Admission type management'
  },
  
  'manage-registration-periods': {
    permissions: [
      'view_registrationperiod',
      'change_registrationperiod'
    ],
    requireStaff: true,
    description: 'Registration period management'
  },
  
  'manage-fields-of-study-app': {
    permissions: [
      'view_fieldofstudy',
      'change_fieldofstudy',
      'view_applicationfield'
    ],
    requireStaff: true,
    description: 'Fields of study for applications'
  },
  
  'manage-years': {
    permissions: [
      'view_academicyear',
      'change_academicyear'
    ],
    requireStaff: true,
    description: 'Academic year management'
  },
  
  'manage-terms': {
    permissions: [
      'view_term',
      'change_term'
    ],
    requireStaff: true,
    description: 'Academic term management'
  },
  
  'application-information': {
    permissions: [
      'view_application',
      'view_applicationinfo'
    ],
    requireStaff: true,
    description: 'Application information management'
  },
  
  'manage-applicants': {
    permissions: [
      'view_applicant',
      'change_applicant'
    ],
    requireStaff: true,
    description: 'Applicant management'
  },

  // ===== SERVICES =====
  'service-types': {
    permissions: [
      // Service type management permissions
      'services.view_servicetype',
      'services.change_servicetype',
      'services.add_servicetype',
      'alumni.view_servicetype',
      'alumni.change_servicetype',
      'portal.view_servicetype',
      'portal.change_servicetype',
      // Fallback permissions
      'auth.view_user',
      'auth.change_user'
    ],
    requireStaff: true,
    description: 'Service type management'
  },

  'document-types': {
    permissions: [
      // Document type management permissions
      'documents.view_documenttype',
      'documents.change_documenttype',
      'documents.add_documenttype',
      'services.view_documenttype',
      'services.change_documenttype',
      'alumni.view_documenttype',
      'alumni.change_documenttype',
      // Fallback permissions
      'auth.view_user',
      'auth.change_user'
    ],
    requireStaff: true,
    description: 'Document type management'
  },

  'alumni-applications-service': {
    permissions: [
      // Alumni service permissions
      'alumni.view_service',
      'alumni.change_service',
      'alumni.view_application',
      'alumni.change_application',
      'services.view_alumniservice',
      'services.change_alumniservice',
      'portal.view_alumniservice',
      'portal.change_alumniservice',
      // Fallback permissions
      'auth.view_user',
      'auth.view_group'
    ],
    requireStaff: true,
    description: 'Alumni service management'
  },
  
  'downloadable-content': {
    permissions: [
      'view_document',
      'change_document',
      'view_content'
    ],
    requireStaff: true,
    description: 'Downloadable content management'
  },

  // ===== OFFICIALS =====
  'certificate-types': {
    permissions: [
      'view_certificatetype',
      'change_certificatetype'
    ],
    requireStaff: true,
    description: 'Certificate type management'
  },
  
  'official-certificates': {
    permissions: [
      'view_certificate',
      'change_certificate'
    ],
    requireStaff: true,
    description: 'Official certificate management'
  },

  // ===== COMMUNICATION =====
  'announcements': {
    permissions: [
      'view_announcement',
      'add_announcement',
      'change_announcement'
    ],
    requireStaff: true,
    description: 'Announcement management'
  },
  
  'official-management': {
    permissions: [
      'view_official',
      'change_official'
    ],
    requireStaff: true,
    description: 'Official management'
  },

  // ===== ADMINISTRATIVE =====
  'user-management': {
    permissions: [
      'view_user',
      'change_user',
      'add_user',
      'delete_user'
    ],
    requireStaff: true,
    description: 'User account management'
  },
  
  'general-settings': {
    permissions: [
      'view_settings',
      'change_settings'
    ],
    requireStaff: true,
    description: 'General system settings'
  },

  // ===== REPORTS & ANALYTICS =====
  'reports': {
    permissions: [
      'view_report',
      'generate_report'
    ],
    requireStaff: true,
    description: 'Report generation and viewing'
  },
  
  'analytics': {
    permissions: [
      'view_analytics',
      'view_statistics'
    ],
    requireStaff: true,
    description: 'Analytics and statistics'
  }
};

/**
 * Feature Permission Mappings
 * Maps feature names to their permission requirements
 */
export const FEATURE_PERMISSIONS: Record<string, PermissionRequirement> = {
  // Graduate management
  'graduate': {
    permissions: [
      'view_graduatestudent',
      'change_graduatestudent',
      'add_graduatestudent',
      'delete_graduatestudent'
    ],
    description: 'Graduate student management features'
  },
  
  // College management
  'college': {
    permissions: [
      'view_college',
      'change_college',
      'add_college',
      'delete_college',
      'view_verificationcollege',
      'change_verificationcollege'
    ],
    description: 'College management features'
  },
  
  // Department management
  'department': {
    permissions: [
      'view_department',
      'change_department',
      'add_department',
      'delete_department',
      'view_verificationdepartment',
      'change_verificationdepartment'
    ],
    description: 'Department management features'
  },
  
  // Program management
  'program': {
    permissions: [
      'view_program',
      'change_program',
      'add_program',
      'delete_program',
      'view_verificationprogram',
      'change_verificationprogram'
    ],
    description: 'Program management features'
  },
  
  // Service management
  'service': {
    permissions: [
      'view_service',
      'change_service',
      'add_service',
      'delete_service',
      'view_servicetype',
      'change_servicetype'
    ],
    description: 'Service management features'
  },
  
  // User management
  'user': {
    permissions: [
      'view_user',
      'change_user',
      'add_user',
      'delete_user'
    ],
    description: 'User management features'
  },
  
  // Document management
  'document': {
    permissions: [
      'view_document',
      'change_document',
      'add_document',
      'delete_document',
      'view_documenttype',
      'change_documenttype'
    ],
    description: 'Document management features'
  },
  
  // Certificate management
  'certificate': {
    permissions: [
      'view_certificate',
      'change_certificate',
      'add_certificate',
      'delete_certificate',
      'view_certificatetype',
      'change_certificatetype'
    ],
    description: 'Certificate management features'
  }
};

/**
 * TESTING AND DEBUGGING UTILITIES
 */

/**
 * Get all unique permissions referenced in menu mappings
 */
export const getAllReferencedPermissions = (): string[] => {
  const allPermissions = new Set<string>();

  Object.values(MENU_PERMISSIONS).forEach(requirement => {
    requirement.permissions.forEach(perm => allPermissions.add(perm));
  });

  return Array.from(allPermissions).sort();
};

/**
 * Get menu items that require specific permission
 */
export const getMenusForPermission = (permission: string): string[] => {
  return Object.entries(MENU_PERMISSIONS)
    .filter(([_, requirement]) => requirement.permissions.includes(permission))
    .map(([menuKey, _]) => menuKey);
};

/**
 * Validate permission mappings
 */
export const validatePermissionMappings = (): {
  valid: boolean;
  issues: string[];
  stats: {
    totalMenus: number;
    totalPermissions: number;
    menusWithFallbacks: number;
    menusWithSpecificPerms: number;
  };
} => {
  const issues: string[] = [];
  let menusWithFallbacks = 0;
  let menusWithSpecificPerms = 0;

  Object.entries(MENU_PERMISSIONS).forEach(([menuKey, requirement]) => {
    // Check if menu has any permissions
    if (!requirement.permissions || requirement.permissions.length === 0) {
      issues.push(`Menu '${menuKey}' has no permissions defined`);
    }

    // Check for fallback permissions
    const hasFallbacks = requirement.permissions.some(perm =>
      perm.startsWith('auth.') || perm === 'view_user' || perm === 'change_user'
    );

    if (hasFallbacks) {
      menusWithFallbacks++;
    }

    // Check for specific permissions
    const hasSpecific = requirement.permissions.some(perm =>
      !perm.startsWith('auth.') && perm !== 'view_user' && perm !== 'change_user'
    );

    if (hasSpecific) {
      menusWithSpecificPerms++;
    }

    // Check if menu only has fallback permissions
    if (hasFallbacks && !hasSpecific) {
      issues.push(`Menu '${menuKey}' only has fallback permissions - consider adding specific permissions`);
    }
  });

  return {
    valid: issues.length === 0,
    issues,
    stats: {
      totalMenus: Object.keys(MENU_PERMISSIONS).length,
      totalPermissions: getAllReferencedPermissions().length,
      menusWithFallbacks,
      menusWithSpecificPerms
    }
  };
};

/**
 * Debug function to log permission mapping analysis
 */
export const debugPermissionMappings = (): void => {
  console.log('🔍 PERMISSION MAPPING ANALYSIS');
  console.log('==============================');

  const validation = validatePermissionMappings();
  console.log('📊 Statistics:', validation.stats);

  if (validation.issues.length > 0) {
    console.log('⚠️ Issues found:');
    validation.issues.forEach(issue => console.log(`  - ${issue}`));
  } else {
    console.log('✅ All permission mappings are valid');
  }

  console.log('\n📋 All Referenced Permissions:');
  getAllReferencedPermissions().forEach(perm => {
    const menus = getMenusForPermission(perm);
    console.log(`  🔑 ${perm} → ${menus.length} menus: ${menus.join(', ')}`);
  });
};

export default { MENU_PERMISSIONS, FEATURE_PERMISSIONS };
