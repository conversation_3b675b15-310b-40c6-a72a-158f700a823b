#!/usr/bin/env python
"""
Real SMTP test script - tests actual email sending
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from settings_manager.smtp_models import SMTPSettings
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

def test_smtp_real():
    """Test real SMTP connection and email sending"""
    print("=== Real SMTP Test ===")
    
    # Get SMTP settings
    smtp_settings = SMTPSettings.load()
    
    print(f"Host: {smtp_settings.host}")
    print(f"Port: {smtp_settings.port}")
    print(f"Username: {smtp_settings.username}")
    print(f"From Email: {smtp_settings.from_email}")
    print(f"Use TLS: {smtp_settings.use_tls}")
    print(f"Use SSL: {smtp_settings.use_ssl}")
    print(f"Has Password: {bool(smtp_settings.password)}")
    print(f"Password Length: {len(smtp_settings.password) if smtp_settings.password else 0}")
    
    if not smtp_settings.host:
        print("❌ No SMTP host configured")
        return False
    
    if not smtp_settings.from_email:
        print("❌ No from email configured")
        return False
    
    # Test recipient
    test_recipient = input("\nEnter test email address: ").strip()
    if not test_recipient:
        print("❌ No recipient provided")
        return False
    
    try:
        print(f"\n🔄 Testing SMTP connection to {smtp_settings.host}:{smtp_settings.port}...")
        
        # Create SMTP connection
        if smtp_settings.use_ssl:
            print("📡 Using SSL connection...")
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(
                smtp_settings.host, 
                smtp_settings.port, 
                context=context,
                timeout=smtp_settings.timeout
            )
        else:
            print("📡 Using regular connection...")
            server = smtplib.SMTP(
                smtp_settings.host, 
                smtp_settings.port,
                timeout=smtp_settings.timeout
            )
            
            if smtp_settings.use_tls:
                print("🔒 Starting TLS...")
                server.starttls()
        
        print("✅ SMTP connection established")
        
        # Authenticate if credentials provided
        if smtp_settings.username and smtp_settings.password:
            print(f"🔐 Authenticating as {smtp_settings.username}...")
            server.login(smtp_settings.username, smtp_settings.password)
            print("✅ Authentication successful")
        else:
            print("⚠️ No authentication credentials provided")
        
        # Create test email
        print("📧 Creating test email...")
        msg = MIMEMultipart()
        msg['From'] = smtp_settings.from_email
        msg['To'] = test_recipient
        msg['Subject'] = "SMTP Test - University Portal"
        
        body = f"""
Hello,

This is a test email from the University Portal SMTP configuration.

Test Details:
- Sent at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- SMTP Host: {smtp_settings.host}
- SMTP Port: {smtp_settings.port}
- From Email: {smtp_settings.from_email}
- TLS: {'Enabled' if smtp_settings.use_tls else 'Disabled'}
- SSL: {'Enabled' if smtp_settings.use_ssl else 'Disabled'}

If you received this email, your SMTP configuration is working correctly!

Best regards,
University Portal System
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        print(f"📤 Sending email to {test_recipient}...")
        text = msg.as_string()
        server.sendmail(smtp_settings.from_email, [test_recipient], text)
        server.quit()
        
        print("✅ Email sent successfully!")
        print(f"📧 Check {test_recipient} for the test email")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Authentication failed: {e}")
        print("💡 Check your username and password")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ Connection failed: {e}")
        print("💡 Check your host and port settings")
        return False
        
    except smtplib.SMTPRecipientsRefused as e:
        print(f"❌ Recipient refused: {e}")
        print("💡 Check the recipient email address")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def configure_smtp_interactive():
    """Interactive SMTP configuration"""
    print("\n=== SMTP Configuration ===")
    
    smtp_settings = SMTPSettings.load()
    
    print("Current settings:")
    print(f"Host: {smtp_settings.host}")
    print(f"Port: {smtp_settings.port}")
    print(f"Username: {smtp_settings.username}")
    print(f"From Email: {smtp_settings.from_email}")
    
    configure = input("\nDo you want to configure SMTP settings? (y/n): ").strip().lower()
    if configure != 'y':
        return
    
    # Configure settings
    host = input(f"SMTP Host [{smtp_settings.host}]: ").strip() or smtp_settings.host
    port = input(f"SMTP Port [{smtp_settings.port}]: ").strip()
    port = int(port) if port else smtp_settings.port
    
    username = input(f"Username [{smtp_settings.username}]: ").strip() or smtp_settings.username
    password = input("Password (leave empty to keep current): ").strip()
    from_email = input(f"From Email [{smtp_settings.from_email}]: ").strip() or smtp_settings.from_email
    
    use_tls = input(f"Use TLS? [{smtp_settings.use_tls}] (y/n): ").strip().lower()
    use_tls = use_tls == 'y' if use_tls else smtp_settings.use_tls
    
    use_ssl = input(f"Use SSL? [{smtp_settings.use_ssl}] (y/n): ").strip().lower()
    use_ssl = use_ssl == 'y' if use_ssl else smtp_settings.use_ssl
    
    # Update settings
    smtp_settings.host = host
    smtp_settings.port = port
    smtp_settings.username = username
    if password:
        smtp_settings.password = password
    smtp_settings.from_email = from_email
    smtp_settings.use_tls = use_tls
    smtp_settings.use_ssl = use_ssl
    
    smtp_settings.save()
    print("✅ SMTP settings saved!")

if __name__ == '__main__':
    print("SMTP Real Test Script")
    print("====================")
    
    # Configure if needed
    configure_smtp_interactive()
    
    # Test SMTP
    success = test_smtp_real()
    
    if success:
        print("\n🎉 SMTP test completed successfully!")
    else:
        print("\n❌ SMTP test failed. Please check your configuration.")
