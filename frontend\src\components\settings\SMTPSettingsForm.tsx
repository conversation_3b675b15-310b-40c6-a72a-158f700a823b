import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Mail, Save, TestTube, Eye, EyeOff, Shield, Server } from 'lucide-react';
import settingsAPI from '@/services/settingsAPI';

interface SMTPSettings {
  provider: string;
  host: string;
  port: number;
  username: string;
  password: string;
  use_tls: boolean;
  use_ssl: boolean;
  from_email: string;
  timeout: number;
}

interface ProviderConfig {
  name: string;
  host: string;
  port: number;
  use_tls: boolean;
  use_ssl: boolean;
  timeout: number;
  instructions: {
    title: string;
    steps: string[];
    notes: string;
  };
}

const SMTPSettingsForm: React.FC = () => {
  const [settings, setSettings] = useState<SMTPSettings>({
    provider: 'custom',
    host: '',
    port: 587,
    username: '',
    password: '',
    use_tls: true,
    use_ssl: false,
    from_email: '',
    timeout: 60,
  });

  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [providers, setProviders] = useState<Record<string, ProviderConfig>>({});
  const [showInstructions, setShowInstructions] = useState(false);

  // Load SMTP settings and provider configs on component mount
  useEffect(() => {
    loadSMTPSettings();
    loadProviderConfigs();
  }, []);

  const loadSMTPSettings = async () => {
    try {
      setLoading(true);
      const response = await settingsAPI.getSMTPSettings();
      if (response.data) {
        // Don't overwrite password if it's masked
        const loadedSettings = { ...response.data };
        if (loadedSettings.password === '***') {
          loadedSettings.password = '';
        }
        setSettings(loadedSettings);
      }
    } catch (error) {
      console.error('Error loading SMTP settings:', error);
      // Load from localStorage as fallback
      loadFromLocalStorage();
    } finally {
      setLoading(false);
    }
  };

  const loadFromLocalStorage = () => {
    const savedSettings = localStorage.getItem('smtpSettings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error parsing saved SMTP settings:', error);
      }
    }
  };

  const handleInputChange = (field: keyof SMTPSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      // Validate required fields
      if (!settings.host || !settings.from_email) {
        toast.error('SMTP Host and From Email are required');
        return;
      }

      // Prepare settings for saving
      const settingsToSave = { ...settings };

      // If password is empty, don't send it (keep existing password)
      if (!settingsToSave.password || settingsToSave.password.trim() === '') {
        delete settingsToSave.password;
      }

      // Save to backend
      try {
        console.log('Saving SMTP settings:', settingsToSave);
        const response = await settingsAPI.updateSMTPSettings(settingsToSave);
        console.log('Save response:', response);
        toast.success('SMTP settings saved successfully');

        // Reload settings to get the latest data
        await loadSMTPSettings();
      } catch (error) {
        console.error('Error saving to backend:', error);
        console.error('Save error response:', error?.response?.data);
        // Save to localStorage as fallback
        localStorage.setItem('smtpSettings', JSON.stringify(settings));
        toast.success('SMTP settings saved locally');
      }
    } catch (error) {
      console.error('Error saving SMTP settings:', error);
      toast.error('Failed to save SMTP settings');
    } finally {
      setLoading(false);
    }
  };

  const handleTestEmail = async () => {
    if (!testEmail) {
      toast.error('Please enter a test email address');
      return;
    }

    if (!settings.host || !settings.from_email) {
      toast.error('Please configure SMTP host and from email first.');
      return;
    }

    try {
      setTesting(true);

      // First save the current settings to ensure they're up to date
      await handleSave();

      console.log('Testing SMTP with recipient:', testEmail);
      const response = await settingsAPI.testSMTPConnection({
        recipient: testEmail
      });

      // Show success message with details
      const successMessage = response.data?.message || 'Test email sent successfully!';
      const details = response.data?.details;

      if (details) {
        toast.success(`${successMessage}\nSent to: ${details.recipient}\nFrom: ${details.from_email}\nSMTP: ${details.smtp_host}:${details.smtp_port}`);
      } else {
        toast.success(successMessage);
      }
    } catch (error: any) {
      console.error('Error testing SMTP:', error);
      console.error('Error response:', error?.response);
      console.error('Error data:', error?.response?.data);

      // Show detailed error message
      const errorData = error?.response?.data;
      let errorMessage = 'Failed to send test email. Please check your SMTP settings.';

      if (errorData?.error) {
        errorMessage = errorData.error;
      } else if (errorData?.recipient) {
        errorMessage = `Invalid recipient: ${errorData.recipient[0]}`;
      } else if (errorData) {
        errorMessage = JSON.stringify(errorData);
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setTesting(false);
    }
  };

  const loadProviderConfigs = async () => {
    try {
      const response = await settingsAPI.getSMTPProviders();
      setProviders(response.data);
    } catch (error) {
      console.error('Error loading provider configs:', error);
    }
  };

  const handleProviderChange = async (provider: string) => {
    try {
      setLoading(true);
      const response = await settingsAPI.applySMTPProvider({ provider });

      if (response.data?.settings) {
        // Clear credentials when switching providers
        const newSettings = {
          ...response.data.settings,
          username: '',
          password: '',
          from_email: ''
        };

        setSettings(prev => ({
          ...prev,
          ...newSettings
        }));

        toast.success(response.data.message || `${provider} configuration applied`);
        setShowInstructions(true);
      }
    } catch (error) {
      console.error('Error applying provider config:', error);
      toast.error('Failed to apply provider configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleDebugInfo = async () => {
    try {
      const response = await settingsAPI.getSMTPDebugInfo();
      console.log('SMTP Debug Info:', response.data);
      toast.info('Debug info logged to console');
    } catch (error) {
      console.error('Error getting debug info:', error);
      toast.error('Failed to get debug info');
    }
  };

  const resetToDefaults = () => {
    setSettings({
      provider: 'custom',
      host: '',
      port: 587,
      username: '',
      password: '',
      use_tls: true,
      use_ssl: false,
      from_email: '',
      timeout: 60,
    });
    toast.info('Settings reset to defaults');
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Mail className="h-5 w-5 text-[#1a73c0]" />
          <CardTitle>SMTP Configuration</CardTitle>
        </div>
        <CardDescription>
          Configure SMTP settings for sending emails from the system
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Provider Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-[#1a73c0]" />
              <h3 className="text-lg font-medium">Email Provider</h3>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowInstructions(!showInstructions)}
              className="text-xs"
            >
              {showInstructions ? 'Hide' : 'Show'} Instructions
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(providers).map(([key, provider]) => (
              <div
                key={key}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  settings.provider === key
                    ? 'border-[#1a73c0] bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleProviderChange(key)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{provider.name}</h4>
                  {settings.provider === key && (
                    <div className="w-2 h-2 bg-[#1a73c0] rounded-full"></div>
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  {key === 'gmail' && 'Google Gmail SMTP'}
                  {key === 'office365' && 'Microsoft Office 365 SMTP'}
                  {key === 'custom' && 'Custom SMTP server configuration'}
                </p>
              </div>
            ))}
          </div>

          {/* Provider Instructions */}
          {showInstructions && settings.provider && providers[settings.provider] && (
            <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
              <h4 className="font-medium text-amber-800 mb-2">
                {providers[settings.provider].instructions.title}
              </h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-amber-700">
                {providers[settings.provider].instructions.steps.map((step, index) => (
                  <li key={index}>{step}</li>
                ))}
              </ol>
              <p className="text-xs text-amber-600 mt-2 font-medium">
                💡 {providers[settings.provider].instructions.notes}
              </p>
            </div>
          )}
        </div>

        {/* Server Configuration */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <Server className="h-4 w-4 text-[#1a73c0]" />
            <h3 className="text-lg font-medium">Server Configuration</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="host">SMTP Host *</Label>
              <Input
                id="host"
                value={settings.host}
                onChange={(e) => handleInputChange('host', e.target.value)}
                placeholder={
                  settings.provider === 'gmail' ? 'smtp.gmail.com' :
                  settings.provider === 'office365' ? 'smtp.office365.com' :
                  'smtp.yourprovider.com'
                }
              />
              <p className="text-xs text-gray-500">
                {settings.provider === 'gmail' && 'Gmail SMTP server (auto-configured)'}
                {settings.provider === 'office365' && 'Office 365 SMTP server (auto-configured)'}
                {settings.provider === 'custom' && 'Your SMTP server hostname'}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="port">SMTP Port</Label>
              <Input
                id="port"
                type="number"
                value={settings.port}
                onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 587)}
                placeholder="587"
              />
              <p className="text-xs text-gray-500">Common ports: 587 (TLS), 465 (SSL), 25 (Plain)</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                value={settings.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder={
                  settings.provider === 'gmail' ? '<EMAIL>' :
                  settings.provider === 'office365' ? '<EMAIL>' :
                  'your-smtp-username'
                }
              />
              <p className="text-xs text-gray-500">
                {settings.provider === 'gmail' && 'Your Gmail email address'}
                {settings.provider === 'office365' && 'Your Office 365 email address'}
                {settings.provider === 'custom' && 'SMTP authentication username'}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={settings.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder={
                    settings.password ? "Password is set (leave empty to keep current)" :
                    settings.provider === 'gmail' ? 'Gmail App Password (16 characters)' :
                    settings.provider === 'office365' ? 'Office 365 password or App Password' :
                    'Your SMTP password'
                  }
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500">
                {settings.provider === 'gmail' && 'Use App Password (not regular Gmail password)'}
                {settings.provider === 'office365' && 'Use Office 365 password or App Password'}
                {settings.provider === 'custom' && 'SMTP authentication password'}
              </p>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <Shield className="h-4 w-4 text-[#1a73c0]" />
            <h3 className="text-lg font-medium">Security Settings</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <Label className="text-sm font-medium">Use TLS</Label>
                <p className="text-xs text-gray-500">Recommended for port 587</p>
              </div>
              <Switch
                checked={settings.use_tls}
                onCheckedChange={(checked) => handleInputChange('use_tls', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <Label className="text-sm font-medium">Use SSL</Label>
                <p className="text-xs text-gray-500">Recommended for port 465</p>
              </div>
              <Switch
                checked={settings.use_ssl}
                onCheckedChange={(checked) => handleInputChange('use_ssl', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeout">Timeout (seconds)</Label>
              <Input
                id="timeout"
                type="number"
                value={settings.timeout}
                onChange={(e) => handleInputChange('timeout', parseInt(e.target.value) || 60)}
                placeholder="60"
              />
            </div>
          </div>
        </div>

        {/* Email Settings */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-3">
            <Mail className="h-4 w-4 text-[#1a73c0]" />
            <h3 className="text-lg font-medium">Email Settings</h3>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="from_email">From Email *</Label>
            <Input
              id="from_email"
              type="email"
              value={settings.from_email}
              onChange={(e) => handleInputChange('from_email', e.target.value)}
              placeholder={
                settings.provider === 'gmail' ? '<EMAIL>' :
                settings.provider === 'office365' ? '<EMAIL>' :
                '<EMAIL>'
              }
            />
            <p className="text-xs text-gray-500">
              {settings.provider === 'gmail' && 'Same as your Gmail address'}
              {settings.provider === 'office365' && 'Same as your Office 365 email'}
              {settings.provider === 'custom' && 'Email address used as sender'}
            </p>
          </div>
        </div>

        {/* Test Email */}
        <div className="space-y-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-center space-x-2 mb-3">
            <TestTube className="h-4 w-4 text-yellow-600" />
            <h3 className="text-lg font-medium text-yellow-800">Test Configuration</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2 space-y-2">
              <Label htmlFor="test_email">Test Email Address</Label>
              <Input
                id="test_email"
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"

              />
            </div>
            <div className="flex items-end">
              <Button
                onClick={handleTestEmail}
                disabled={testing || !testEmail}
                className="w-full bg-yellow-600 hover:bg-yellow-700"
              >
                {testing ? (
                  <>
                    <TestTube className="h-4 w-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <TestTube className="h-4 w-4 mr-2" />
                    Send Test
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={resetToDefaults}
              disabled={loading}
            >
              Reset to Defaults
            </Button>
            <Button
              variant="outline"
              onClick={handleDebugInfo}
              disabled={loading}
              className="text-xs"
            >
              Debug Info
            </Button>
          </div>

          <div className="flex space-x-2">
            <Button
              onClick={handleSave}
              disabled={loading}
              className="bg-[#1a73c0] hover:bg-[#145da1]"
            >
              {loading ? (
                <>
                  <Save className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SMTPSettingsForm;
