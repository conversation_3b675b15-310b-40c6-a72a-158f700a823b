# Generated manually

from django.db import migrations, models
import django.db.models.deletion


def set_default_term(apps, schema_editor):
    """Set a default term for existing records"""
    RegistrationPeriod = apps.get_model('registration_period', 'RegistrationPeriod')
    Term = apps.get_model('term', 'Term')
    
    # Get the first available term or create a default one
    default_term = Term.objects.first()
    if not default_term:
        default_term = Term.objects.create(
            name='Default Term',
            description='Default term for existing records'
        )
    
    # Update all existing records to use the default term
    RegistrationPeriod.objects.filter(term__isnull=True).update(term=default_term)


def reverse_set_default_term(apps, schema_editor):
    """Reverse operation - no action needed"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('term', '0002_remove_created_at_and_is_active'),
        ('registration_period', '0001_initial'),
    ]

    operations = [
        # Step 1: Add the field as nullable
        migrations.AddField(
            model_name='registrationperiod',
            name='term',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='registration_periods',
                to='term.term',
                null=True,
                blank=True
            ),
        ),
        # Step 2: Set default values for existing records
        migrations.RunPython(set_default_term, reverse_set_default_term),
        # Step 3: Make the field non-nullable
        migrations.AlterField(
            model_name='registrationperiod',
            name='term',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='registration_periods',
                to='term.term'
            ),
        ),
    ]
