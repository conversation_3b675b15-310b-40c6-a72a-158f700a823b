from rest_framework import viewsets, permissions, status, filters, serializers
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db import models
from django_filters.rest_framework import DjangoFilterBackend
from .models import Announcement, EmailNotification, SMSNotification
from .serializers import (
    AnnouncementSerializer,
    EmailNotificationSerializer,
    SMSNotificationSerializer
)


class AnnouncementViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing announcements.
    """
    queryset = Announcement.objects.all()
    serializer_class = AnnouncementSerializer
    permission_classes = [permissions.AllowAny]  # Changed to AllowAny for testing
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['priority', 'target_audience', 'is_active']
    search_fields = ['title', 'content']
    ordering_fields = ['created_at', 'start_date', 'end_date', 'priority']
    ordering = ['-created_at']

    def create(self, request, *args, **kwargs):
        """Create a new announcement with better error handling."""
        try:
            # Log the incoming data for debugging
            print(f"Creating announcement with data: {request.data}")

            # Clean the data before validation
            data = request.data.copy()

            # Handle empty end_date
            if 'end_date' in data and (data['end_date'] == '' or data['end_date'] is None):
                data['end_date'] = None

            # Ensure datetime fields are properly formatted and timezone-aware
            for field in ['start_date', 'end_date']:
                if field in data and data[field] and isinstance(data[field], str):
                    # If it's a datetime-local format, convert to ISO format
                    if 'T' in data[field] and len(data[field]) == 16:  # YYYY-MM-DDTHH:MM
                        data[field] = data[field] + ':00'  # Add seconds

                    # Ensure timezone information is present
                    if 'T' in data[field] and not data[field].endswith('Z') and '+' not in data[field]:
                        # Add timezone offset for local time
                        from django.utils import timezone
                        import datetime
                        try:
                            # Parse the datetime and make it timezone-aware
                            dt = datetime.datetime.fromisoformat(data[field])
                            dt_aware = timezone.make_aware(dt)
                            data[field] = dt_aware.isoformat()
                        except (ValueError, TypeError):
                            # If conversion fails, leave as is
                            pass

            print(f"Cleaned data: {data}")

            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)

            # Get the user for the author field
            from django.contrib.auth.models import User
            user = request.user if request.user.is_authenticated else User.objects.first()
            if not user:
                # Create a test user if none exists
                user = User.objects.create_user(username='testuser', password='testpassword')

            # Save with the author
            announcement = serializer.save(author=user)

            # Call the model's clean method for additional validation
            try:
                announcement.clean()
            except Exception as clean_error:
                # If model validation fails, delete the created object and return error
                announcement.delete()
                return Response(
                    {'error': str(clean_error), 'details': 'Model validation failed'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Return the created announcement
            response_serializer = self.get_serializer(announcement)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except serializers.ValidationError as e:
            print(f"Validation error: {e}")
            return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            print(f"Error creating announcement: {str(e)}")
            print(f"Request data: {request.data}")
            import traceback
            traceback.print_exc()
            return Response(
                {'error': str(e), 'details': 'Failed to create announcement'},
                status=status.HTTP_400_BAD_REQUEST
            )

    def perform_create(self, serializer):
        """Set the author to the current user when creating an announcement."""
        # Get the first user if not authenticated (for testing)
        from django.contrib.auth.models import User
        user = self.request.user if self.request.user.is_authenticated else User.objects.first()
        if not user:
            # Create a test user if none exists
            user = User.objects.create_user(username='testuser', password='testpassword')
        serializer.save(author=user)

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def active(self, request):
        """
        Get all active announcements for the current time.
        """
        now = timezone.now()
        queryset = Announcement.objects.filter(
            is_active=True,
            start_date__lte=now
        ).filter(
            models.Q(end_date__isnull=True) | models.Q(end_date__gt=now)
        )

        # Filter by target audience if specified
        audience = request.query_params.get('audience', None)
        if audience:
            queryset = queryset.filter(
                models.Q(target_audience=audience) | models.Q(target_audience='all')
            )

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class EmailNotificationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing email notifications.
    """
    queryset = EmailNotification.objects.all()
    serializer_class = EmailNotificationSerializer
    permission_classes = [permissions.AllowAny]  # Changed to AllowAny for testing
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['subject', 'content', 'recipients']
    ordering_fields = ['created_at', 'scheduled_time', 'sent_time']
    ordering = ['-created_at']

    def perform_create(self, serializer):
        """Set the sender to the current user when creating an email notification."""
        # Get the first user if not authenticated (for testing)
        from django.contrib.auth.models import User
        user = self.request.user if self.request.user.is_authenticated else User.objects.first()
        if not user:
            # Create a test user if none exists
            user = User.objects.create_user(username='testuser', password='testpassword')
        serializer.save(sender=user)

    def perform_update(self, serializer):
        """Handle email notification updates without changing the sender."""
        # Don't change the sender on update, just save the other fields
        serializer.save()

    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        """
        Send the email notification immediately.
        """
        notification = self.get_object()

        # Check if the notification is already sent
        if notification.status == 'sent':
            return Response(
                {"detail": "This email has already been sent."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Import email service
            from alumni_applications.email_service import AlumniApplicationEmailService

            # Get SMTP settings and apply them
            smtp_settings = AlumniApplicationEmailService.get_smtp_settings()

            # Parse recipients
            recipients = [email.strip() for email in notification.recipients.split(',') if email.strip()]

            if not recipients:
                return Response(
                    {"detail": "No valid recipients found."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Send email using Django's email system
            from django.core.mail import EmailMultiAlternatives
            from django.conf import settings

            # Determine from email
            if smtp_settings and smtp_settings.from_email:
                from_email = smtp_settings.from_email
            else:
                from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')

            # Create email
            email = EmailMultiAlternatives(
                subject=notification.subject,
                body=notification.content,  # Plain text version
                from_email=from_email,
                to=recipients
            )

            # Add HTML version if content contains HTML
            if '<' in notification.content and '>' in notification.content:
                email.attach_alternative(notification.content, "text/html")

            # Send email
            result = email.send()

            if result > 0:
                # Update notification status
                notification.status = 'sent'
                notification.sent_time = timezone.now()
                notification.save()

                serializer = self.get_serializer(notification)
                return Response({
                    'detail': f'Email sent successfully to {len(recipients)} recipients.',
                    'notification': serializer.data
                })
            else:
                # Email sending failed
                notification.status = 'failed'
                notification.save()

                return Response(
                    {"detail": "Failed to send email. Check SMTP configuration."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        except Exception as e:
            # Update status to failed
            notification.status = 'failed'
            notification.save()

            return Response(
                {"detail": f"Error sending email: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_send(self, request):
        """
        Send multiple email notifications at once.
        """
        notification_ids = request.data.get('notification_ids', [])

        if not notification_ids:
            return Response(
                {"detail": "No notification IDs provided."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all requested notifications to check their status
        all_notifications = EmailNotification.objects.filter(id__in=notification_ids)

        # Filter only those that can be sent (draft or failed)
        notifications = all_notifications.filter(status__in=['draft', 'failed'])

        # Count notifications by status for better error reporting
        status_counts = {}
        for notification in all_notifications:
            status_counts[notification.status] = status_counts.get(notification.status, 0) + 1

        if not notifications.exists():
            error_details = []
            if status_counts.get('sent', 0) > 0:
                error_details.append(f"{status_counts['sent']} already sent")
            if status_counts.get('scheduled', 0) > 0:
                error_details.append(f"{status_counts['scheduled']} scheduled")

            error_message = "No valid notifications found to send."
            if error_details:
                error_message += f" Found: {', '.join(error_details)}. Only draft or failed emails can be sent."

            return Response(
                {"detail": error_message},
                status=status.HTTP_400_BAD_REQUEST
            )

        sent_count = 0
        failed_count = 0
        results = []

        for notification in notifications:
            try:
                # Use the same sending logic as the single send action
                from alumni_applications.email_service import AlumniApplicationEmailService
                smtp_settings = AlumniApplicationEmailService.get_smtp_settings()

                recipients = [email.strip() for email in notification.recipients.split(',') if email.strip()]

                if not recipients:
                    failed_count += 1
                    results.append({
                        'id': notification.id,
                        'status': 'failed',
                        'message': 'No valid recipients'
                    })
                    continue

                from django.core.mail import EmailMultiAlternatives
                from django.conf import settings

                from_email = smtp_settings.from_email if smtp_settings else getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')

                email = EmailMultiAlternatives(
                    subject=notification.subject,
                    body=notification.content,
                    from_email=from_email,
                    to=recipients
                )

                if '<' in notification.content and '>' in notification.content:
                    email.attach_alternative(notification.content, "text/html")

                result = email.send()

                if result > 0:
                    notification.status = 'sent'
                    notification.sent_time = timezone.now()
                    notification.save()
                    sent_count += 1
                    results.append({
                        'id': notification.id,
                        'status': 'sent',
                        'message': f'Sent to {len(recipients)} recipients'
                    })
                else:
                    notification.status = 'failed'
                    notification.save()
                    failed_count += 1
                    results.append({
                        'id': notification.id,
                        'status': 'failed',
                        'message': 'Email send returned 0'
                    })

            except Exception as e:
                notification.status = 'failed'
                notification.save()
                failed_count += 1
                results.append({
                    'id': notification.id,
                    'status': 'failed',
                    'message': str(e)
                })

        return Response({
            'detail': f'Bulk send completed. {sent_count} sent, {failed_count} failed.',
            'sent_count': sent_count,
            'failed_count': failed_count,
            'results': results
        })

    @action(detail=False, methods=['delete'])
    def bulk_delete(self, request):
        """
        Delete multiple email notifications at once.
        """
        notification_ids = request.data.get('notification_ids', [])

        if not notification_ids:
            return Response(
                {"detail": "No notification IDs provided."},
                status=status.HTTP_400_BAD_REQUEST
            )

        deleted_count = EmailNotification.objects.filter(id__in=notification_ids).delete()[0]

        return Response({
            'detail': f'Successfully deleted {deleted_count} notifications.',
            'deleted_count': deleted_count
        })

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        Get email notification statistics.
        """
        from django.db.models import Count
        from datetime import timedelta

        # Get status counts
        status_counts = EmailNotification.objects.values('status').annotate(
            count=Count('id')
        )

        # Get recent statistics (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_count = EmailNotification.objects.filter(
            created_at__gte=thirty_days_ago
        ).count()

        # Get today's statistics
        today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_count = EmailNotification.objects.filter(
            created_at__gte=today_start
        ).count()

        # Format status counts
        status_summary = {}
        for item in status_counts:
            status_summary[item['status']] = item['count']

        return Response({
            'total_notifications': EmailNotification.objects.count(),
            'status_summary': status_summary,
            'recent_notifications': recent_count,
            'today_notifications': today_count
        })


class SMSNotificationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing SMS notifications.
    """
    queryset = SMSNotification.objects.all()
    serializer_class = SMSNotificationSerializer
    permission_classes = [permissions.AllowAny]  # Changed to AllowAny for testing
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status']
    search_fields = ['message', 'recipients']
    ordering_fields = ['created_at', 'scheduled_time', 'sent_time']
    ordering = ['-created_at']

    def perform_create(self, serializer):
        """Set the sender to the current user when creating an SMS notification."""
        # Get the first user if not authenticated (for testing)
        from django.contrib.auth.models import User
        user = self.request.user if self.request.user.is_authenticated else User.objects.first()
        if not user:
            # Create a test user if none exists
            user = User.objects.create_user(username='testuser', password='testpassword')
        serializer.save(sender=user)

    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        """
        Send the SMS notification immediately.
        """
        notification = self.get_object()

        # Check if the notification is already sent
        if notification.status == 'sent':
            return Response(
                {"detail": "This SMS has already been sent."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # In a real implementation, you would send the SMS here
        # For now, we'll just update the status
        notification.status = 'sent'
        notification.sent_time = timezone.now()
        notification.save()

        serializer = self.get_serializer(notification)
        return Response(serializer.data)
