from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Prefetch
from django.shortcuts import get_object_or_404
from django.utils import timezone


from .models import ServiceRequest, DocumentUpload
from .serializers import (
    ServiceRequestListSerializer,
    ServiceRequestDetailSerializer,
    ServiceRequestCreateSerializer,
    ServiceRequestUpdateSerializer,
    ServiceRequestStatusSerializer,
    DocumentUploadSerializer,
    ServiceTypeLookupSerializer,
    AdmissionTypeLookupSerializer,
    StudyProgramLookupSerializer,
    CollegeLookupSerializer,
    DepartmentLookupSerializer,
    CertificateTypeLookupSerializer
)
from .filters import ServiceRequestFilter
from setups.service_type.models import ServiceType
from setups.admission_type.models import AdmissionType
from setups.study_program.models import StudyProgram
from setups.college.models import College
from setups.department.models import Department
from setups.certificate_type.models import CertificateType


class ServiceRequestViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ServiceRequest with full CRUD operations and advanced filtering.
    """
    
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceRequestFilter
    search_fields = ['first_name', 'last_name', 'email', 'mobile']
    ordering_fields = ['created_at', 'updated_at', 'status', 'service_type__name']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Return queryset with optimized queries and soft delete filtering."""
        queryset = ServiceRequest.objects.filter(is_deleted=False).select_related(
            'service_type',
            'admission_type',
            'degree',
            'college',
            'department',
            'mailing_college',
            'mailing_department',
            'created_by',
            'updated_by'
        ).prefetch_related(
            Prefetch(
                'document_uploads',
                queryset=DocumentUpload.objects.select_related('document_type', 'uploaded_by', 'verified_by')
            )
        )
        
        # Filter by user permissions
        user = self.request.user
        if not user.is_staff:
            # Non-staff users can only see their own requests
            queryset = queryset.filter(created_by=user)
        
        return queryset
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return ServiceRequestListSerializer
        elif self.action == 'create':
            return ServiceRequestCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return ServiceRequestUpdateSerializer
        elif self.action == 'update_status':
            return ServiceRequestStatusSerializer
        else:
            return ServiceRequestDetailSerializer
    
    def get_permissions(self):
        """Set permissions based on action."""
        if self.action == 'create':
            # Allow anyone to create service requests
            permission_classes = [AllowAny]
        else:
            # Require authentication for other actions
            permission_classes = [IsAuthenticated]
        
        return [permission() for permission in permission_classes]
    
    def perform_create(self, serializer):
        """Set created_by when creating a service request."""
        if self.request.user.is_authenticated:
            serializer.save(created_by=self.request.user)
        else:
            serializer.save()
    
    def perform_update(self, serializer):
        """Set updated_by when updating a service request."""
        if self.request.user.is_authenticated:
            serializer.save(updated_by=self.request.user)
        else:
            serializer.save()
    
    @action(detail=True, methods=['patch'], permission_classes=[IsAuthenticated])
    def update_status(self, request, pk=None):
        """Update only the status of a service request."""
        service_request = self.get_object()
        
        # Check permissions
        if not request.user.is_staff:
            return Response(
                {'error': 'Only staff members can update request status.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = ServiceRequestStatusSerializer(
            service_request,
            data=request.data,
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def soft_delete(self, request, pk=None):
        """Soft delete a service request."""
        service_request = self.get_object()
        
        # Check permissions
        if not request.user.is_staff and service_request.created_by != request.user:
            return Response(
                {'error': 'You can only delete your own requests.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        service_request.soft_delete(user=request.user)
        
        return Response(
            {'message': 'Service request deleted successfully.'},
            status=status.HTTP_200_OK
        )
    
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def restore(self, request, pk=None):
        """Restore a soft-deleted service request."""
        # Get the object including soft-deleted ones
        service_request = get_object_or_404(
            ServiceRequest.objects.all(),
            pk=pk
        )
        
        # Check permissions
        if not request.user.is_staff:
            return Response(
                {'error': 'Only staff members can restore deleted requests.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if not service_request.is_deleted:
            return Response(
                {'error': 'Service request is not deleted.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        service_request.restore(user=request.user)
        
        return Response(
            {'message': 'Service request restored successfully.'},
            status=status.HTTP_200_OK
        )
    
    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def statistics(self, request):
        """Get statistics about service requests."""
        queryset = self.get_queryset()
        
        stats = {
            'total_requests': queryset.count(),
            'by_status': dict(
                queryset.values('status').annotate(count=Count('id')).values_list('status', 'count')
            ),
            'by_service_type': dict(
                queryset.values('service_type__name').annotate(count=Count('id')).values_list('service_type__name', 'count')
            ),
            'recent_requests': queryset.filter(
                created_at__gte=timezone.now() - timezone.timedelta(days=30)
            ).count()
        }
        
        return Response(stats)
    
    @action(detail=True, methods=['get'])
    def required_documents(self, request, pk=None):
        """Get required document types for a service request."""
        service_request = self.get_object()
        
        if not service_request.service_type:
            return Response(
                {'error': 'Service type not specified.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        required_docs = service_request.service_type.document_types.filter(is_active=True)
        serializer = CertificateTypeLookupSerializer(required_docs, many=True)
        
        return Response(serializer.data)


class DocumentUploadViewSet(viewsets.ModelViewSet):
    """
    ViewSet for DocumentUpload with file upload capabilities.
    """
    
    serializer_class = DocumentUploadSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['service_request', 'document_type', 'is_verified']
    ordering_fields = ['created_at', 'is_verified']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Return queryset with proper permissions."""
        queryset = DocumentUpload.objects.select_related(
            'service_request',
            'document_type',
            'uploaded_by',
            'verified_by'
        )
        
        user = self.request.user
        if not user.is_staff:
            # Non-staff users can only see documents for their own requests
            queryset = queryset.filter(service_request__created_by=user)
        
        return queryset
    
    def perform_create(self, serializer):
        """Set uploaded_by when creating a document upload."""
        serializer.save(uploaded_by=self.request.user)
    
    @action(detail=True, methods=['patch'], permission_classes=[IsAuthenticated])
    def verify(self, request, pk=None):
        """Verify a document upload."""
        document = self.get_object()
        
        # Check permissions
        if not request.user.is_staff:
            return Response(
                {'error': 'Only staff members can verify documents.'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        document.is_verified = True
        document.verified_by = request.user
        document.verification_notes = request.data.get('verification_notes', '')
        document.save()
        
        serializer = self.get_serializer(document)
        return Response(serializer.data)


# Lookup ViewSets for dropdown population
class ServiceTypeLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for service type lookups."""
    
    queryset = ServiceType.objects.filter(is_active=True)
    serializer_class = ServiceTypeLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None


class AdmissionTypeLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for admission type lookups."""
    
    queryset = AdmissionType.objects.all()
    serializer_class = AdmissionTypeLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None


class StudyProgramLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for study program lookups."""
    
    queryset = StudyProgram.objects.all()
    serializer_class = StudyProgramLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None


class CollegeLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for college lookups."""
    
    queryset = College.objects.all()
    serializer_class = CollegeLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None


class DepartmentLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for department lookups."""
    
    queryset = Department.objects.select_related('college')
    serializer_class = DepartmentLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['college']


class CertificateTypeLookupViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for certificate type lookups."""

    queryset = CertificateType.objects.filter(is_active=True)
    serializer_class = CertificateTypeLookupSerializer
    permission_classes = [AllowAny]
    pagination_class = None


# Public API Views for Alumni (No Authentication Required) - REMOVED
