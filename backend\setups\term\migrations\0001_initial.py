# Generated by Django 5.2.1 on 2025-06-06 09:03

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Term',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for the term', primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Name of the term', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the term (optional)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the term was created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the term was last updated')),
                ('is_active', models.BooleanField(default=True, help_text='Whether the term is currently active')),
            ],
            options={
                'verbose_name': 'Term',
                'verbose_name_plural': 'Terms',
                'ordering': ['name'],
            },
        ),
    ]
