/**
 * Menu Access Control Hook
 * Provides utilities for checking menu access based on user permissions and roles
 */

import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRBAC } from '@/contexts/SimpleRBACContext';
import { hasMenuAccess, getAccessibleMenus, hasAnyAdminAccess, getUserAccessLevel } from '@/utils/menuPermissions';
import { 
  MenuItem, 
  MenuCategory, 
  APPLICANT_MENU_ITEMS, 
  STAFF_MENU_CATEGORIES, 
  ADMIN_MENU_CATEGORIES,
  getAllMenuItems,
  findMenuItemById,
  findMenuItemByPath
} from '@/config/menuConfig';

export interface MenuAccessInfo {
  // User access levels
  isAuthenticated: boolean;
  isStaff: boolean;
  isSuperuser: boolean;
  isRegularUser: boolean;
  accessLevel: string;
  hasAdminAccess: boolean;

  // Menu access functions
  hasMenuAccess: (menuKey: string) => boolean;
  getAccessibleMenus: () => string[];
  
  // Filtered menu items
  applicantMenuItems: MenuItem[];
  staffMenuCategories: MenuCategory[];
  adminMenuCategories: MenuCategory[];
  allAccessibleMenuItems: MenuItem[];
  
  // Utility functions
  findAccessibleMenuItem: (id: string) => MenuItem | undefined;
  findMenuItemByPath: (path: string) => MenuItem | undefined;
  isMenuItemAccessible: (item: MenuItem) => boolean;
  
  // Debug function
  debugPermissions: () => void;
}

/**
 * Hook for menu access control
 */
export const useMenuAccess = (): MenuAccessInfo => {
  const { user, isAuthenticated } = useAuth();
  const { isSuperuser, isStaff, isRegularUser } = useRBAC();

  // Memoized access level information
  const accessLevel = useMemo(() => getUserAccessLevel(user), [user]);
  const hasAdminAccess = useMemo(() => hasAnyAdminAccess(user), [user]);

  // Memoized menu access function
  const checkMenuAccess = useMemo(() => {
    return (menuKey: string) => hasMenuAccess(menuKey, user);
  }, [user]);

  // Memoized accessible menus
  const accessibleMenus = useMemo(() => {
    return getAccessibleMenus(user);
  }, [user]);

  // Filter menu items based on user permissions
  const applicantMenuItems = useMemo(() => {
    return APPLICANT_MENU_ITEMS.filter(item => 
      hasMenuAccess(item.permissionKey, user)
    );
  }, [user]);

  const staffMenuCategories = useMemo(() => {
    return STAFF_MENU_CATEGORIES.map(category => ({
      ...category,
      items: category.items.filter(item => 
        hasMenuAccess(item.permissionKey, user)
      )
    })).filter(category => category.items.length > 0); // Remove empty categories
  }, [user]);

  const adminMenuCategories = useMemo(() => {
    return ADMIN_MENU_CATEGORIES.map(category => ({
      ...category,
      items: category.items.filter(item => 
        hasMenuAccess(item.permissionKey, user)
      )
    })).filter(category => category.items.length > 0); // Remove empty categories
  }, [user]);

  // All accessible menu items (flat array)
  const allAccessibleMenuItems = useMemo(() => {
    const staffItems = staffMenuCategories.flatMap(category => category.items);
    const adminItems = adminMenuCategories.flatMap(category => category.items);
    return [...applicantMenuItems, ...staffItems, ...adminItems];
  }, [applicantMenuItems, staffMenuCategories, adminMenuCategories]);

  // Utility functions
  const findAccessibleMenuItem = useMemo(() => {
    return (id: string): MenuItem | undefined => {
      return allAccessibleMenuItems.find(item => item.id === id);
    };
  }, [allAccessibleMenuItems]);

  const isMenuItemAccessible = useMemo(() => {
    return (item: MenuItem): boolean => {
      return hasMenuAccess(item.permissionKey, user);
    };
  }, [user]);

  const debugPermissions = () => {
    if (!user) {
      console.log('🔐 No user authenticated');
      return;
    }

    console.group('🔐 Menu Access Debug');
    console.log('User:', user.username);
    console.log('Access Level:', accessLevel);
    console.log('Is Staff:', isStaff);
    console.log('Is Superuser:', isSuperuser);
    console.log('Has Admin Access:', hasAdminAccess);
    console.log('Permissions:', user.permissions?.length || 0, 'permissions');
    console.log('Role Names:', user.role_names || []);

    console.group('📋 Accessible Menus');
    console.log('Applicant Menus:', applicantMenuItems.map(item => item.name));
    console.log('Staff Menu Categories:', staffMenuCategories.length);
    staffMenuCategories.forEach(category => {
      console.log(`  ${category.name}:`, category.items.map(item => item.name));
    });
    console.log('Admin Menu Categories:', adminMenuCategories.length);
    adminMenuCategories.forEach(category => {
      console.log(`  ${category.name}:`, category.items.map(item => item.name));
    });
    console.groupEnd();

    console.group('🔍 All Menu Access Check');
    getAllMenuItems().forEach(item => {
      const hasAccess = hasMenuAccess(item.permissionKey, user);
      console.log(`${hasAccess ? '✅' : '❌'} ${item.name} (${item.permissionKey})`);
    });
    console.groupEnd();

    console.groupEnd();
  };

  return {
    // User access levels
    isAuthenticated,
    isStaff,
    isSuperuser,
    isRegularUser,
    accessLevel,
    hasAdminAccess,

    // Menu access functions
    hasMenuAccess: checkMenuAccess,
    getAccessibleMenus: () => accessibleMenus,

    // Filtered menu items
    applicantMenuItems,
    staffMenuCategories,
    adminMenuCategories,
    allAccessibleMenuItems,

    // Utility functions
    findAccessibleMenuItem,
    findMenuItemByPath,
    isMenuItemAccessible,

    // Debug function
    debugPermissions
  };
};

/**
 * Hook to check if current route is accessible
 */
export const useRouteAccess = (path: string) => {
  const { findMenuItemByPath, isMenuItemAccessible } = useMenuAccess();
  
  const menuItem = useMemo(() => {
    return findMenuItemByPath(path);
  }, [path, findMenuItemByPath]);

  const hasAccess = useMemo(() => {
    if (!menuItem) return true; // Allow access to routes not defined in menu
    return isMenuItemAccessible(menuItem);
  }, [menuItem, isMenuItemAccessible]);

  return {
    menuItem,
    hasAccess,
    isProtectedRoute: !!menuItem
  };
};

/**
 * Hook for menu item status (active, accessible, etc.)
 */
export const useMenuItemStatus = (item: MenuItem, currentPath: string) => {
  const { isMenuItemAccessible } = useMenuAccess();

  const isAccessible = useMemo(() => {
    return isMenuItemAccessible(item);
  }, [item, isMenuItemAccessible]);

  const isActive = useMemo(() => {
    return currentPath === item.path || 
           (item.subPaths && item.subPaths.some(subPath => currentPath.startsWith(subPath)));
  }, [item, currentPath]);

  return {
    isAccessible,
    isActive,
    shouldShow: isAccessible
  };
};
