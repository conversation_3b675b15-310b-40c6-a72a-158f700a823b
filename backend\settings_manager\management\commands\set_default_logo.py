from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from settings_manager.models import OrganizationSetting
import requests
import os

class Command(BaseCommand):
    help = 'Set a default logo for the organization settings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--logo-url',
            type=str,
            help='URL of the logo to download and set as default',
            default='https://res.cloudinary.com/dtlkgxx8l/image/upload/v1744978060/gondar-logo_uhm6rl.png'
        )
        parser.add_argument(
            '--logo-path',
            type=str,
            help='Local path to logo file'
        )

    def handle(self, *args, **options):
        try:
            # Get or create organization settings
            settings = OrganizationSetting.get_settings()
            
            self.stdout.write(f"Current organization: {settings.system_name}")
            self.stdout.write(f"Current header logo: {settings.header_logo}")
            
            if settings.header_logo:
                self.stdout.write(
                    self.style.WARNING(
                        f"Header logo already exists: {settings.header_logo.url}"
                    )
                )
                confirm = input("Do you want to replace it? (y/N): ")
                if confirm.lower() != 'y':
                    self.stdout.write("Operation cancelled.")
                    return
            
            logo_content = None
            filename = None
            
            # Option 1: Download from URL
            if options['logo_url']:
                self.stdout.write(f"Downloading logo from: {options['logo_url']}")
                try:
                    response = requests.get(options['logo_url'], timeout=30)
                    response.raise_for_status()
                    logo_content = response.content
                    filename = 'gondar-logo.png'
                    self.stdout.write(self.style.SUCCESS("Logo downloaded successfully"))
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Failed to download logo: {e}")
                    )
                    return
            
            # Option 2: Use local file
            elif options['logo_path']:
                logo_path = options['logo_path']
                if not os.path.exists(logo_path):
                    self.stdout.write(
                        self.style.ERROR(f"Logo file not found: {logo_path}")
                    )
                    return
                
                with open(logo_path, 'rb') as f:
                    logo_content = f.read()
                filename = os.path.basename(logo_path)
                self.stdout.write(f"Using local logo file: {logo_path}")
            
            else:
                self.stdout.write(
                    self.style.ERROR("Please provide either --logo-url or --logo-path")
                )
                return
            
            # Save the logo
            if logo_content and filename:
                # Remove old logo if exists
                if settings.header_logo:
                    settings.header_logo.delete(save=False)
                
                # Save new logo
                settings.header_logo.save(
                    filename,
                    ContentFile(logo_content),
                    save=True
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Header logo set successfully: {settings.header_logo.url}"
                    )
                )
                
                # Also set footer logo if it doesn't exist
                if not settings.footer_logo:
                    settings.footer_logo.save(
                        f"footer_{filename}",
                        ContentFile(logo_content),
                        save=True
                    )
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Footer logo also set: {settings.footer_logo.url}"
                        )
                    )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting logo: {e}")
            )
            import traceback
            traceback.print_exc()
