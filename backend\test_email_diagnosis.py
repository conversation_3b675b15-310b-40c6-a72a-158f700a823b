#!/usr/bin/env python3
"""
Email Diagnosis Script for Alumni Applications
Tests email functionality and diagnoses issues
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from settings_manager.smtp_models import SMTPSettings
from communication.models import EmailNotification
from alumni_applications.email_service import AlumniApplicationEmailService
from alumni_applications.models import AlumniApplication, AlumniApplicationMini
from setups.service_type.models import ServiceType
from setups.college.models import College
from decimal import Decimal
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailDiagnosisTest:
    def __init__(self):
        self.test_results = []
        
    def log_result(self, test_name, success, message):
        """Log test result"""
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}: {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })

    def test_smtp_configuration(self):
        """Test SMTP configuration"""
        print("\n🔧 Testing SMTP Configuration...")
        
        try:
            smtp_settings = SMTPSettings.load()
            
            if not smtp_settings.host:
                self.log_result("SMTP Host", False, "No SMTP host configured")
                return False
            
            self.log_result("SMTP Host", True, f"Host: {smtp_settings.host}")
            self.log_result("SMTP Port", True, f"Port: {smtp_settings.port}")
            self.log_result("SMTP Username", True, f"Username: {smtp_settings.username}")
            self.log_result("SMTP From Email", True, f"From: {smtp_settings.from_email}")
            self.log_result("SMTP TLS", True, f"TLS: {smtp_settings.use_tls}")
            self.log_result("SMTP SSL", True, f"SSL: {smtp_settings.use_ssl}")
            
            return True
            
        except Exception as e:
            self.log_result("SMTP Configuration", False, f"Error: {e}")
            return False

    def test_django_email_settings(self):
        """Test Django email settings"""
        print("\n⚙️ Testing Django Email Settings...")
        
        try:
            self.log_result("EMAIL_BACKEND", True, f"{settings.EMAIL_BACKEND}")
            self.log_result("EMAIL_HOST", True, f"{getattr(settings, 'EMAIL_HOST', 'Not set')}")
            self.log_result("EMAIL_PORT", True, f"{getattr(settings, 'EMAIL_PORT', 'Not set')}")
            self.log_result("EMAIL_USE_TLS", True, f"{getattr(settings, 'EMAIL_USE_TLS', 'Not set')}")
            self.log_result("EMAIL_USE_SSL", True, f"{getattr(settings, 'EMAIL_USE_SSL', 'Not set')}")
            self.log_result("DEFAULT_FROM_EMAIL", True, f"{getattr(settings, 'DEFAULT_FROM_EMAIL', 'Not set')}")
            
            return True
            
        except Exception as e:
            self.log_result("Django Email Settings", False, f"Error: {e}")
            return False

    def test_email_templates(self):
        """Test email template rendering"""
        print("\n📧 Testing Email Templates...")
        
        try:
            # Test context data
            context = {
                'applicant_name': 'Test User',
                'transaction_id': 'TEST123456',
                'service_type': 'Official Transcript',
                'service_fee': '150.00',
                'application_type': 'Complete Application',
                'form_type': 'form1',
                'application_id': 'test-app-id',
                'submission_date': 'January 15, 2024 at 10:30 AM',
                'support_email': '<EMAIL>',
                'portal_url': 'http://localhost:8080',
            }
            
            # Test HTML template
            try:
                html_content = render_to_string('alumni_applications/confirmation_email.html', context)
                self.log_result("HTML Template", True, f"Rendered {len(html_content)} characters")
            except Exception as e:
                self.log_result("HTML Template", False, f"Error: {e}")
            
            # Test text template
            try:
                text_content = render_to_string('alumni_applications/confirmation_email.txt', context)
                self.log_result("Text Template", True, f"Rendered {len(text_content)} characters")
            except Exception as e:
                self.log_result("Text Template", False, f"Error: {e}")
            
            return True
            
        except Exception as e:
            self.log_result("Email Templates", False, f"Error: {e}")
            return False

    def test_simple_email_send(self):
        """Test simple email sending"""
        print("\n📤 Testing Simple Email Send...")
        
        try:
            # Apply SMTP settings
            smtp_settings = SMTPSettings.load()
            if smtp_settings and smtp_settings.host:
                smtp_settings.apply_to_settings()
                settings.EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
            else:
                settings.EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
                self.log_result("Email Backend", True, "Using console backend (no SMTP configured)")
            
            # Send test email
            result = send_mail(
                subject='Test Email - Alumni Portal',
                message='This is a test email from the Alumni Portal diagnosis script.',
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=['<EMAIL>'],
                fail_silently=False
            )
            
            self.log_result("Simple Email Send", True, f"Email sent successfully (result: {result})")
            return True
            
        except Exception as e:
            self.log_result("Simple Email Send", False, f"Error: {e}")
            return False

    def test_alumni_email_service(self):
        """Test alumni application email service"""
        print("\n🎓 Testing Alumni Email Service...")
        
        try:
            # Create test service type
            service_type, created = ServiceType.objects.get_or_create(
                name='Test Email Service',
                defaults={
                    'fee': Decimal('150.00'),
                    'description': 'Test service for email diagnosis'
                }
            )
            
            # Create test college
            college, created = College.objects.get_or_create(
                name='Test College Email',
                defaults={'description': 'Test college for email diagnosis'}
            )
            
            # Create test application
            test_app = AlumniApplication.objects.create(
                first_name='Test',
                father_name='Email',
                last_name='User',
                email='<EMAIL>',
                phone_number='+251912345678',
                admission_type='Regular',
                degree_type='Degree',
                college=college,
                other_department_name='Computer Science',
                is_other_college=False,
                student_status='Graduated',
                year_of_graduation_ethiopian='2015',
                year_of_graduation_gregorian=2023,
                service_type=service_type,
                payment_status='Pending',
                application_status='Pending',
                is_uog_destination=True,
                uog_college=college,
                transaction_id='TEST_EMAIL_123456'
            )
            
            # Test email service
            result = AlumniApplicationEmailService.send_application_confirmation(
                application=test_app,
                is_form1=True
            )
            
            self.log_result("Alumni Email Service", result, 
                          "Email sent successfully" if result else "Email sending failed")
            
            # Clean up test data
            test_app.delete()
            
            return result
            
        except Exception as e:
            self.log_result("Alumni Email Service", False, f"Error: {e}")
            return False

    def test_email_notification_logging(self):
        """Test email notification logging"""
        print("\n📝 Testing Email Notification Logging...")
        
        try:
            # Check if EmailNotification model is working
            initial_count = EmailNotification.objects.count()
            self.log_result("Email Notification Model", True, f"Found {initial_count} existing notifications")
            
            # Test creating a notification
            from django.contrib.auth.models import User
            from django.utils import timezone
            
            system_user, created = User.objects.get_or_create(
                username='system_email_test',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'System',
                    'last_name': 'Test',
                    'is_active': False,
                }
            )
            
            notification = EmailNotification.objects.create(
                subject='Test Email Notification',
                content='This is a test email notification for diagnosis.',
                sender=system_user,
                recipients='<EMAIL>',
                status='sent',
                sent_time=timezone.now()
            )
            
            self.log_result("Email Notification Creation", True, f"Created notification ID: {notification.id}")
            
            # Clean up
            notification.delete()
            
            return True
            
        except Exception as e:
            self.log_result("Email Notification Logging", False, f"Error: {e}")
            return False

    def check_email_failures(self):
        """Check for email failures in the system"""
        print("\n🔍 Checking Email Failures...")
        
        try:
            # Check failed email notifications
            failed_notifications = EmailNotification.objects.filter(status='failed')
            self.log_result("Failed Email Count", True, f"Found {failed_notifications.count()} failed emails")
            
            # Check draft email notifications
            draft_notifications = EmailNotification.objects.filter(status='draft')
            self.log_result("Draft Email Count", True, f"Found {draft_notifications.count()} draft emails")
            
            # Check recent applications without email notifications
            recent_apps = AlumniApplication.objects.filter(
                created_at__gte=django.utils.timezone.now() - django.utils.timezone.timedelta(days=7)
            )
            
            recent_mini_apps = AlumniApplicationMini.objects.filter(
                created_at__gte=django.utils.timezone.now() - django.utils.timezone.timedelta(days=7)
            )
            
            self.log_result("Recent Form1 Applications", True, f"Found {recent_apps.count()} recent applications")
            self.log_result("Recent Form2 Applications", True, f"Found {recent_mini_apps.count()} recent applications")
            
            return True
            
        except Exception as e:
            self.log_result("Email Failure Check", False, f"Error: {e}")
            return False

    def run_diagnosis(self):
        """Run complete email diagnosis"""
        print("🚀 Starting Email Diagnosis for Alumni Applications...")
        print("=" * 60)
        
        # Run all tests
        tests = [
            self.test_smtp_configuration,
            self.test_django_email_settings,
            self.test_email_templates,
            self.test_simple_email_send,
            self.test_alumni_email_service,
            self.test_email_notification_logging,
            self.check_email_failures
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"  ❌ Test failed with exception: {e}")
        
        # Generate report
        print("\n" + "=" * 60)
        print("📊 EMAIL DIAGNOSIS REPORT")
        print("=" * 60)
        
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed < total:
            print("\n❌ ISSUES FOUND:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🔧 RECOMMENDATIONS:")
        
        # Check SMTP configuration
        try:
            smtp_settings = SMTPSettings.load()
            if not smtp_settings.host:
                print("  1. Configure SMTP settings in Django admin")
                print("     - Go to /admin/settings_manager/smtpsettings/")
                print("     - Set up your email provider (Gmail, Office365, etc.)")
        except:
            print("  1. SMTP settings model not accessible")
        
        print("  2. Check Django email backend configuration")
        print("  3. Verify email templates exist and render correctly")
        print("  4. Test email sending with a real email address")
        print("  5. Check server logs for detailed error messages")
        
        print("\n📧 EMAIL SENDING PROCESS:")
        print("  1. Application submitted → perform_create() called")
        print("  2. AlumniApplicationEmailService.send_application_confirmation() called")
        print("  3. SMTP settings loaded and applied")
        print("  4. Email templates rendered with application data")
        print("  5. Email sent via Django's email backend")
        print("  6. Email notification logged in communication system")
        
        return passed == total

if __name__ == "__main__":
    diagnosis = EmailDiagnosisTest()
    success = diagnosis.run_diagnosis()
    sys.exit(0 if success else 1)
