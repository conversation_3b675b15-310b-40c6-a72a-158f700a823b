import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Users, 
  Shield, 
  Activity, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  Calendar,
  Clock,
  UserCheck,
  UserX,
  Lock,
  Unlock,
  Eye,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { statsAPI } from '@/services/authAPI';
import { AuthStats, UserActivity, SecurityEvent } from '@/types/auth';

const AuthDashboard: React.FC = () => {
  const [stats, setStats] = useState<AuthStats | null>(null);
  const [userActivity, setUserActivity] = useState<UserActivity[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [activityLoading, setActivityLoading] = useState(false);
  const [securityLoading, setSecurityLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('7');
  const [eventTypeFilter, setEventTypeFilter] = useState('');
  const [severityFilter, setSeverityFilter] = useState('');

  useEffect(() => {
    loadStats();
    loadUserActivity();
    loadSecurityEvents();
  }, [timeRange, eventTypeFilter, severityFilter]);

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await statsAPI.getAuthStats();
      setStats(response.data);
    } catch (error) {
      console.warn('Auth stats API not available:', error);
      // Set default stats when API is not available
      setStats({
        total_users: 0,
        active_users: 0,
        total_groups: 0,
        total_permissions: 0,
        recent_logins: 0,
        failed_logins: 0
      });
      // Don't show error toast for missing API endpoints
    } finally {
      setLoading(false);
    }
  };

  const loadUserActivity = async () => {
    try {
      setActivityLoading(true);
      const response = await statsAPI.getUserActivity({
        days: parseInt(timeRange),
        page: 1
      });
      setUserActivity(response.data.results || []);
    } catch (error) {
      console.warn('User activity API not available:', error);
      setUserActivity([]); // Ensure it's always an array
      // Don't show error toast for missing API endpoints
    } finally {
      setActivityLoading(false);
    }
  };

  const loadSecurityEvents = async () => {
    try {
      setSecurityLoading(true);
      const response = await statsAPI.getSecurityEvents({
        event_type: eventTypeFilter || undefined,
        severity: severityFilter || undefined,
        days: parseInt(timeRange),
        page: 1
      });
      setSecurityEvents(response.data.results || []);
    } catch (error) {
      console.warn('Security events API not available:', error);
      setSecurityEvents([]); // Ensure it's always an array
      // Don't show error toast for missing API endpoints
    } finally {
      setSecurityLoading(false);
    }
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'login_success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'login_failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'logout':
        return <UserX className="h-4 w-4 text-blue-600" />;
      case 'password_change':
        return <Lock className="h-4 w-4 text-yellow-600" />;
      case 'permission_change':
        return <Shield className="h-4 w-4 text-purple-600" />;
      case 'account_locked':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      case 'high':
        return <Badge variant="destructive" className="bg-orange-100 text-orange-800">High</Badge>;
      case 'medium':
        return <Badge variant="secondary">Medium</Badge>;
      case 'low':
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Authentication Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor user authentication and security events
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Last 24h</SelectItem>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={loadStats}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* API Status Notice */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-blue-800">
            <AlertTriangle className="h-5 w-5" />
            <span>Backend API Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-blue-700 text-sm">
            The authentication dashboard is displaying demo data. The backend APIs for authentication statistics,
            user activity, and security events are not yet implemented. This interface shows the complete frontend
            implementation that will work once the corresponding Django REST API endpoints are created.
          </p>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_users}</div>
              <p className="text-xs text-muted-foreground">
                {stats.active_users} active users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Staff Users</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.staff_users}</div>
              <p className="text-xs text-muted-foreground">
                {stats.superusers} superusers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Groups</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_groups}</div>
              <p className="text-xs text-muted-foreground">
                {stats.total_permissions} permissions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recent Logins</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.recent_logins}</div>
              <p className="text-xs text-muted-foreground">
                {stats.inactive_users} inactive users
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Activity and Security Tabs */}
      <Tabs defaultValue="activity" className="space-y-4">
        <TabsList>
          <TabsTrigger value="activity">User Activity</TabsTrigger>
          <TabsTrigger value="security">Security Events</TabsTrigger>
        </TabsList>

        {/* User Activity Tab */}
        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Recent User Activity</span>
              </CardTitle>
              <CardDescription>
                Latest user actions and system interactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {activityLoading ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <div className="flex items-center justify-center space-x-2">
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            <span>Loading activity...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : userActivity.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          <div className="text-muted-foreground">
                            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No recent activity</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      userActivity.map((activity) => (
                        <TableRow key={activity.id}>
                          <TableCell>
                            <div className="font-medium">User #{activity.user}</div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{activity.action}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatRelativeTime(activity.timestamp)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatDate(activity.timestamp)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <code className="text-xs bg-muted px-1 py-0.5 rounded">
                              {activity.ip_address || 'Unknown'}
                            </code>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-muted-foreground max-w-xs truncate">
                              {activity.details ? JSON.stringify(activity.details) : 'No details'}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Events Tab */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5" />
                <span>Security Events</span>
              </CardTitle>
              <CardDescription>
                Security-related events and alerts
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Security Event Filters */}
              <div className="flex items-center space-x-4 mb-4">
                <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="All event types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All event types</SelectItem>
                    <SelectItem value="login_success">Login Success</SelectItem>
                    <SelectItem value="login_failed">Login Failed</SelectItem>
                    <SelectItem value="logout">Logout</SelectItem>
                    <SelectItem value="password_change">Password Change</SelectItem>
                    <SelectItem value="permission_change">Permission Change</SelectItem>
                    <SelectItem value="account_locked">Account Locked</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={severityFilter} onValueChange={setSeverityFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="All severities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All severities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {securityLoading ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="flex items-center justify-center space-x-2">
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            <span>Loading security events...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : securityEvents.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="text-muted-foreground">
                            <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No security events found</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      securityEvents.map((event) => (
                        <TableRow key={event.id}>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {getEventIcon(event.event_type)}
                              <span className="font-medium">
                                {event.event_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {event.user ? (
                              <div className="font-medium">User #{event.user}</div>
                            ) : (
                              <span className="text-muted-foreground">System</span>
                            )}
                          </TableCell>
                          <TableCell>
                            {getSeverityBadge(event.severity)}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatRelativeTime(event.timestamp)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatDate(event.timestamp)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <code className="text-xs bg-muted px-1 py-0.5 rounded">
                              {event.ip_address || 'Unknown'}
                            </code>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-muted-foreground max-w-xs truncate">
                              {event.details ? JSON.stringify(event.details) : 'No details'}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AuthDashboard;
