import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Pencil, Trash2, Search, Loader2, RefreshCw, Plus, FileUp, FileSpreadsheet, Filter, Building, GraduationCap, Users, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Download, BarChart3, AlertTriangle } from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import GraduateForm from './GraduateForm';
import GraduateCsvImport from './GraduateCsvImport';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import RecentGraduatesDashboard from './RecentGraduatesDashboard';
import { toast } from 'sonner';
import { graduateVerificationAPI } from '@/services/api';

// Define interfaces for our data models
interface Graduate {
  id: number;
  student_id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  year_of_entry: number;
  year_of_graduation: number;
  gpa: number | string;
  gender: 'Male' | 'Female';
  college: {
    id: number;
    name: string;
    code: string;
  };
  department: {
    id: number;
    name: string;
    code: string;
  };
  field_of_study: {
    id: number;
    name: string;
  };
  program: {
    id: number;
    name: string;
    code: string;
  };
  admission_classification: {
    id: number;
    name: string;
    description?: string; // Optional description
  };
  photo?: string;
  full_name: string;
  // Audit trail fields
  created_by?: number;
  updated_by?: number;
  created_by_name?: string;
  updated_by_name?: string;
  created_at?: string;
  updated_at?: string;
  // Soft delete fields
  is_deleted?: boolean;
  deleted_at?: string;
  deleted_by?: number;
  deleted_by_name?: string;
  // Additional properties that might come from API
  college_name?: string;
  college_id?: number;
  college_code?: string;
  department_name?: string;
  department_id?: number;
  department_code?: string;
  field_of_study_name?: string;
  field_of_study_id?: number;
  program_name?: string;
  program_id?: number;
  program_code?: string;
  admission_classification_name?: string;
  admission_classification_id?: number;
}

interface VerificationCollege {
  id: number;
  name: string;
  code: string;
}

interface VerificationDepartment {
  id: number;
  name: string;
  code: string;
  college: number;
}

interface VerificationFieldOfStudy {
  id: number;
  name: string;
  department: number;
}

interface VerificationProgram {
  id: number;
  name: string;
  code: string;
}

interface VerificationAdmissionClassification {
  id: number;
  name: string;
  description?: string; // Optional description
}

const GraduateManagement = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState('statistics');

  // State for graduates data
  const [graduates, setGraduates] = useState<Graduate[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const itemsPerPageOptions = [10, 20, 50, 100];
  const [hasSearched, setHasSearched] = useState(false);

  // Pagination state from API response
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [nextPage, setNextPage] = useState<string | null>(null);
  const [previousPage, setPreviousPage] = useState<string | null>(null);

  // Current search parameters for pagination
  const [currentSearchParams, setCurrentSearchParams] = useState<any>({});

  // State for filters
  const [studentId, setStudentId] = useState('');
  const [collegeId, setCollegeId] = useState('all');
  const [departmentId, setDepartmentId] = useState('all');
  const [fieldOfStudyId, setFieldOfStudyId] = useState('all');
  const [programId, setProgramId] = useState('all');
  const [admissionClassificationId, setAdmissionClassificationId] = useState('all');

  // State for dropdown options
  const [colleges, setColleges] = useState<VerificationCollege[]>([]);
  const [departments, setDepartments] = useState<VerificationDepartment[]>([]);
  const [fieldsOfStudy, setFieldsOfStudy] = useState<VerificationFieldOfStudy[]>([]);
  const [programs, setPrograms] = useState<VerificationProgram[]>([]);
  const [admissionClassifications, setAdmissionClassifications] = useState<VerificationAdmissionClassification[]>([]);
  const [filteredDepartments, setFilteredDepartments] = useState<VerificationDepartment[]>([]);
  const [filteredFieldsOfStudy, setFilteredFieldsOfStudy] = useState<VerificationFieldOfStudy[]>([]);

  // State for modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [selectedGraduate, setSelectedGraduate] = useState<Graduate | null>(null);

  // Soft delete states
  const [showDeleted, setShowDeleted] = useState(false);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);

  // Load dropdown options on component mount, but don't fetch graduates yet
  useEffect(() => {
    // Only fetch dropdown options for the search form
    fetchDropdownOptions();

    // Cleanup function to clear any pending timers when component unmounts
    return () => {
      if (debouncedFilterTimer) {
        clearTimeout(debouncedFilterTimer);
      }
    };
  }, []);

  // Update filtered departments when college changes
  useEffect(() => {
    if (collegeId && collegeId !== 'all') {
      const filtered = departments.filter(dept => dept.college === parseInt(collegeId));
      setFilteredDepartments(filtered);

      // Reset department selection if the current selection doesn't belong to the selected college
      if (departmentId && departmentId !== 'all' && !filtered.some(dept => dept.id === parseInt(departmentId))) {
        setDepartmentId('all');
      }
    } else {
      setFilteredDepartments(departments);
    }

    // Reset field of study when college changes
    setFieldOfStudyId('all');
  }, [collegeId, departments, departmentId]);

  // Update filtered fields of study when department changes
  useEffect(() => {
    if (departmentId && departmentId !== 'all') {
      const filtered = fieldsOfStudy.filter(field => field.department === parseInt(departmentId));
      setFilteredFieldsOfStudy(filtered);

      // Reset field of study selection if the current selection doesn't belong to the selected department
      if (fieldOfStudyId && fieldOfStudyId !== 'all' && !filtered.some(field => field.id === parseInt(fieldOfStudyId))) {
        setFieldOfStudyId('all');
      }
    } else {
      setFilteredFieldsOfStudy(fieldsOfStudy);
    }
  }, [departmentId, fieldsOfStudy, fieldOfStudyId]);

  // Fetch graduates data with pagination
  const fetchGraduates = async (params = {}) => {
    setLoading(true);
    try {
      // Add pagination parameters
      const paginatedParams = {
        page: currentPage,
        page_size: itemsPerPage,
        ...params
      };

      const response = await graduateVerificationAPI.getAllGraduates(paginatedParams);
      if (response && response.data) {
        console.log('Paginated graduates response:', response.data);

        // Handle paginated response structure
        let graduatesData = [];
        let paginationInfo: any = {};

        if (response.data.results) {
          // Paginated response
          graduatesData = response.data.results;
          paginationInfo = {
            count: response.data.count,
            totalPages: response.data.total_pages,
            currentPage: response.data.current_page,
            next: response.data.next,
            previous: response.data.previous
          };
        } else {
          // Non-paginated response (fallback)
          graduatesData = Array.isArray(response.data) ? response.data : [];
        }

        // Transform the data to ensure proper structure
        const transformedData = graduatesData.map((graduate: any) => {
          return {
            ...graduate,
            // Ensure college has the right structure
            college: graduate.college && typeof graduate.college === 'object' ? graduate.college : {
              id: graduate.college_id || 0,
              name: graduate.college_name || 'Unknown College',
              code: graduate.college_code || ''
            },
            // Ensure department has the right structure
            department: graduate.department && typeof graduate.department === 'object' ? graduate.department : {
              id: graduate.department_id || 0,
              name: graduate.department_name || 'Unknown Department',
              code: graduate.department_code || ''
            },
            // Ensure field_of_study has the right structure
            field_of_study: graduate.field_of_study && typeof graduate.field_of_study === 'object' ? graduate.field_of_study : {
              id: graduate.field_of_study_id || 0,
              name: graduate.field_of_study_name || 'Unknown Field of Study'
            },
            // Ensure program has the right structure
            program: graduate.program && typeof graduate.program === 'object' ? graduate.program : {
              id: graduate.program_id || 0,
              name: graduate.program_name || 'Unknown Program',
              code: graduate.program_code || ''
            },
            // Ensure admission_classification has the right structure
            admission_classification: graduate.admission_classification && typeof graduate.admission_classification === 'object' ? graduate.admission_classification : {
              id: graduate.admission_classification_id || 0,
              name: graduate.admission_classification_name || 'Unknown Admission Classification',
              description: graduate.admission_classification_description || ''
            }
          };
        });

        setGraduates(transformedData);

        // Update pagination state
        if (paginationInfo.count !== undefined) {
          setTotalCount(paginationInfo.count);
          setTotalPages(paginationInfo.totalPages || 0);
          setNextPage(paginationInfo.next);
          setPreviousPage(paginationInfo.previous);
        }

        // Store current search params for pagination
        setCurrentSearchParams(params);
      }
    } catch (error) {
      console.error('Error fetching graduates:', error);
      toast.error('Failed to load graduates data');
    } finally {
      setLoading(false);
    }
  };

  // Fetch dropdown options
  const fetchDropdownOptions = async () => {
    try {
      console.log('Fetching dropdown options...');
      const [collegesResponse, departmentsResponse, fieldsOfStudyResponse, programsResponse, admissionClassificationsResponse] = await Promise.all([
        graduateVerificationAPI.getColleges(),
        graduateVerificationAPI.getDepartments(),
        graduateVerificationAPI.getFieldsOfStudy(),
        graduateVerificationAPI.getPrograms(),
        graduateVerificationAPI.getAdmissionClassifications()
      ]);

      console.log('Colleges data:', collegesResponse.data);
      console.log('Departments data:', departmentsResponse.data);
      console.log('Fields of Study data:', fieldsOfStudyResponse.data);
      console.log('Programs data:', programsResponse.data);
      console.log('Admission Classifications data:', admissionClassificationsResponse.data);

      // Log college IDs for debugging
      if (collegesResponse.data && collegesResponse.data.length > 0) {
        console.log('College IDs:', collegesResponse.data.map((college: VerificationCollege) => ({ id: college.id, name: college.name })));
      }

      setColleges(collegesResponse.data || []);
      setDepartments(departmentsResponse.data || []);
      setFieldsOfStudy(fieldsOfStudyResponse.data || []);
      setPrograms(programsResponse.data || []);
      setAdmissionClassifications(admissionClassificationsResponse.data || []);
      setFilteredDepartments(departmentsResponse.data || []);
      setFilteredFieldsOfStudy(fieldsOfStudyResponse.data || []);
    } catch (error) {
      console.error('Error fetching dropdown options:', error);
      toast.error('Failed to load filter options');
    }
  };

  // Create a debounced version of the filter function
  const [debouncedFilterTimer, setDebouncedFilterTimer] = useState<NodeJS.Timeout | null>(null);

  // Handle search button click
  const handleSearch = () => {
    // Reset to first page when searching
    setCurrentPage(1);
    // Apply search criteria
    applyFilters();
    // Set hasSearched to true to show results
    setHasSearched(true);
  };

  // Apply filters to fetch filtered data from the server
  const applyFilters = async () => {
    // Prepare filter parameters
    const params: any = {};

    if (studentId) params.student_id__icontains = studentId;

    // Convert string IDs to integers for proper filtering
    try {
      if (collegeId && collegeId !== 'all') {
        const parsedCollegeId = parseInt(collegeId);
        console.log('Parsed College ID:', parsedCollegeId);

        // Find the college in our list to verify it exists
        const selectedCollege = colleges.find(c => c.id === parsedCollegeId);
        console.log('Selected College:', selectedCollege);

        params.college_id = parsedCollegeId;
      }

      if (departmentId && departmentId !== 'all') {
        params.department_id = parseInt(departmentId);
      }

      if (fieldOfStudyId && fieldOfStudyId !== 'all') {
        params.field_of_study_id = parseInt(fieldOfStudyId);
      }

      if (programId && programId !== 'all') {
        params.program_id = parseInt(programId);
      }

      if (admissionClassificationId && admissionClassificationId !== 'all') {
        params.admission_classification_id = parseInt(admissionClassificationId);
      }
    } catch (error) {
      console.error('Error parsing ID values:', error);
      toast.error('Invalid filter values');
      return;
    }

    console.log('Applying filters with params:', params);

    // Call the fetchGraduates function with the filter parameters
    await fetchGraduates(params);
  };

  // Reset filters
  const resetFilters = () => {
    // Clear any pending filter timer
    if (debouncedFilterTimer) {
      clearTimeout(debouncedFilterTimer);
      setDebouncedFilterTimer(null);
    }

    // Reset all filter values
    setStudentId('');
    setCollegeId('all');
    setDepartmentId('all');
    setFieldOfStudyId('all');
    setProgramId('all');
    setAdmissionClassificationId('all');
    setCurrentPage(1);

    // Clear graduates list and reset search state
    setGraduates([]);
    setHasSearched(false);
  };

  // Handle opening the create modal
  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };

  // Handle opening the edit modal
  const handleOpenEditModal = async (graduate: Graduate) => {
    try {
      console.log('Opening edit modal for graduate:', graduate);
      // Fetch detailed graduate data for editing
      const detailedGraduateResponse = await graduateVerificationAPI.getGraduateById(graduate.id);
      console.log('Detailed graduate data for editing:', detailedGraduateResponse.data);
      setSelectedGraduate(detailedGraduateResponse.data);
      setIsEditModalOpen(true);
    } catch (error) {
      console.error('Error fetching detailed graduate data:', error);
      toast.error('Failed to load graduate details for editing');
    }
  };

  // Export to CSV
  const exportToCSV = async () => {
    try {
      // Use filtered graduates if search has been performed, otherwise fetch all
      let graduatesToExport = [];

      if (hasSearched && filteredGraduates.length > 0) {
        // For export, get all results from current search
        const response = await graduateVerificationAPI.getGraduatesForExport(currentSearchParams);
        graduatesToExport = response.data?.results || response.data || [];
      } else {
        // Fetch all graduates data for export
        const response = await graduateVerificationAPI.getGraduatesForExport();
        graduatesToExport = response.data?.results || response.data || [];
      }

      if (graduatesToExport.length === 0) {
        toast.error('No graduate data found to export.');
        return;
      }

      // Prepare CSV headers
      const headers = [
        'S.No',
        'Student ID',
        'First Name',
        'Middle Name',
        'Last Name',
        'Full Name',
        'Year of Entry',
        'Year of Graduation',
        'GPA',
        'Gender',
        'College',
        'Department',
        'Field of Study',
        'Program',
        'Admission Classification',
        'Created Date'
      ];

      // Prepare CSV data
      const csvData = graduatesToExport.map((graduate: any, index: number) => [
        index + 1,
        graduate.student_id || '',
        graduate.first_name || '',
        graduate.middle_name || '',
        graduate.last_name || '',
        `${graduate.first_name || ''} ${graduate.middle_name ? graduate.middle_name + ' ' : ''}${graduate.last_name || ''}`.trim(),
        graduate.year_of_entry || '',
        graduate.year_of_graduation || '',
        graduate.gpa || '',
        graduate.gender || '',
        graduate.college?.name || graduate.college_name || '',
        graduate.department?.name || graduate.department_name || '',
        graduate.field_of_study?.name || graduate.field_of_study_name || '',
        graduate.program?.name || graduate.program_name || '',
        graduate.admission_classification?.name || graduate.admission_classification_name || '',
        graduate.created_at ? new Date(graduate.created_at).toLocaleDateString() : ''
      ]);

      // Convert to CSV format
      const csvContent = [
        headers.join(','),
        ...csvData.map(row =>
          row.map(field => {
            // Escape fields that contain commas, quotes, or newlines
            const stringField = String(field);
            if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
              return `"${stringField.replace(/"/g, '""')}"`;
            }
            return stringField;
          }).join(',')
        )
      ].join('\n');

      // Create and download CSV file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `graduates_export_${timestamp}.csv`;

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      toast.success(`CSV file exported successfully: ${filename}`);
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      toast.error('Failed to export to CSV. Please try again.');
    }
  };

  // Export to PDF (Landscape)
  const exportToPDF = async () => {
    try {
      // Use filtered graduates if search has been performed, otherwise fetch all
      let graduatesToExport = [];

      if (hasSearched && filteredGraduates.length > 0) {
        // For export, get all results from current search
        const response = await graduateVerificationAPI.getGraduatesForExport(currentSearchParams);
        graduatesToExport = response.data?.results || response.data || [];
      } else {
        // Fetch all graduates data for export
        const response = await graduateVerificationAPI.getGraduatesForExport();
        graduatesToExport = response.data?.results || response.data || [];
      }

      if (graduatesToExport.length === 0) {
        toast.error('No graduate data found to export.');
        return;
      }

      // Create PDF in landscape orientation
      const doc = new jsPDF('landscape', 'mm', 'a4');

      // Add title
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Graduate Records Report', 148, 20, { align: 'center' });

      // Add generation info
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 148, 28, { align: 'center' });
      doc.text(`Total Records: ${graduatesToExport.length}`, 148, 34, { align: 'center' });

      // Prepare table data
      const tableData = graduatesToExport.map((graduate: any, index: number) => [
        index + 1,
        graduate.student_id || '',
        `${graduate.first_name || ''} ${graduate.middle_name ? graduate.middle_name + ' ' : ''}${graduate.last_name || ''}`.trim(),
        graduate.college?.name || graduate.college_name || '',
        graduate.department?.name || graduate.department_name || '',
        graduate.field_of_study?.name || graduate.field_of_study_name || '',
        graduate.program?.name || graduate.program_name || '',
        graduate.admission_classification?.name || graduate.admission_classification_name || '',
        graduate.year_of_entry || '',
        graduate.year_of_graduation || '',
        graduate.gpa || '',
        graduate.gender || ''
      ]);

      // Add table
      autoTable(doc, {
        head: [['S.No', 'Student ID', 'Full Name', 'College', 'Department', 'Field of Study', 'Program', 'Admission Class', 'Entry Year', 'Grad Year', 'GPA', 'Gender']],
        body: tableData,
        startY: 45,
        styles: {
          fontSize: 7,
          cellPadding: 1.5,
          overflow: 'linebreak',
          halign: 'left'
        },
        headStyles: {
          fillColor: [26, 115, 192], // #1a73c0
          textColor: 255,
          fontStyle: 'bold',
          fontSize: 8
        },
        alternateRowStyles: {
          fillColor: [245, 248, 250]
        },
        columnStyles: {
          0: { cellWidth: 12, halign: 'center' }, // S.No
          1: { cellWidth: 20 }, // Student ID
          2: { cellWidth: 30 }, // Full Name
          3: { cellWidth: 25 }, // College
          4: { cellWidth: 25 }, // Department
          5: { cellWidth: 25 }, // Field of Study
          6: { cellWidth: 20 }, // Program
          7: { cellWidth: 20 }, // Admission Class
          8: { cellWidth: 15, halign: 'center' }, // Entry Year
          9: { cellWidth: 15, halign: 'center' }, // Grad Year
          10: { cellWidth: 12, halign: 'center' }, // GPA
          11: { cellWidth: 12, halign: 'center' }  // Gender
        },
        margin: { top: 45, left: 5, right: 5 },
        tableWidth: 'auto',
        theme: 'striped'
      });

      // Add footer
      const pageCount = (doc as any).internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.setFont('helvetica', 'normal');
        doc.text(`Page ${i} of ${pageCount}`, 148, 200, { align: 'center' });
        doc.text('This report was generated from the Graduate Management System', 148, 205, { align: 'center' });
      }

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `graduates_report_${timestamp}.pdf`;

      // Save file
      doc.save(filename);
      toast.success(`PDF report exported successfully: ${filename}`);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('Failed to export to PDF. Please try again.');
    }
  };

  // Handle opening the delete confirmation dialog
  const handleOpenDeleteDialog = (graduate: Graduate) => {
    setSelectedGraduate(graduate);
    setIsDeleteDialogOpen(true);
  };

  // Handle opening the restore confirmation dialog
  const handleOpenRestoreDialog = (graduate: Graduate) => {
    setSelectedGraduate(graduate);
    setIsRestoreDialogOpen(true);
  };

  // Handle soft delete
  const handleSoftDelete = async () => {
    if (!selectedGraduate) return;

    try {
      await graduateVerificationAPI.softDeleteGraduate(selectedGraduate.id);
      toast.success('Graduate record moved to trash successfully');
      setIsDeleteDialogOpen(false);
      setSelectedGraduate(null);

      // Refresh the current view
      if (showDeleted) {
        await fetchDeletedGraduates();
      } else if (hasSearched) {
        await applyFilters();
      }
    } catch (error) {
      console.error('Error soft deleting graduate:', error);
      toast.error('Failed to delete graduate record');
    }
  };

  // Handle restore
  const handleRestore = async () => {
    if (!selectedGraduate) return;

    try {
      await graduateVerificationAPI.restoreGraduate(selectedGraduate.id);
      toast.success('Graduate record restored successfully');
      setIsRestoreDialogOpen(false);
      setSelectedGraduate(null);

      // Refresh the deleted view
      await fetchDeletedGraduates();
    } catch (error) {
      console.error('Error restoring graduate:', error);
      toast.error('Failed to restore graduate record');
    }
  };

  // Fetch deleted graduates
  const fetchDeletedGraduates = async () => {
    console.log('🗑️ fetchDeletedGraduates called');
    setLoading(true);
    try {
      console.log('📡 Making API call...');
      const response = await graduateVerificationAPI.getDeletedGraduates();
      console.log('📥 API Response:', response);
      console.log('📊 Response data:', response?.data);

      if (response && response.data) {
        const deletedGraduates = Array.isArray(response.data) ? response.data : response.data.results || [];
        console.log('📈 Deleted graduates array:', deletedGraduates);
        console.log('📈 Array length:', deletedGraduates.length);

        // Transform the data to ensure proper structure
        const transformedData = deletedGraduates.map((graduate: any) => {
          return {
            ...graduate,
            college: graduate.college && typeof graduate.college === 'object' ? graduate.college : {
              id: graduate.college_id || 0,
              name: graduate.college_name || 'Unknown College',
              code: graduate.college_code || ''
            },
            department: graduate.department && typeof graduate.department === 'object' ? graduate.department : {
              id: graduate.department_id || 0,
              name: graduate.department_name || 'Unknown Department',
              code: graduate.department_code || ''
            },
            field_of_study: graduate.field_of_study && typeof graduate.field_of_study === 'object' ? graduate.field_of_study : {
              id: graduate.field_of_study_id || 0,
              name: graduate.field_of_study_name || 'Unknown Field of Study'
            },
            program: graduate.program && typeof graduate.program === 'object' ? graduate.program : {
              id: graduate.program_id || 0,
              name: graduate.program_name || 'Unknown Program',
              code: graduate.program_code || ''
            },
            admission_classification: graduate.admission_classification && typeof graduate.admission_classification === 'object' ? graduate.admission_classification : {
              id: graduate.admission_classification_id || 0,
              name: graduate.admission_classification_name || 'Unknown Admission Classification',
              description: graduate.admission_classification_description || ''
            }
          };
        });

        console.log('✅ Setting graduates to:', transformedData);
        setGraduates(transformedData);
        setHasSearched(true);
        console.log('✅ Set hasSearched to true');

        // Show success message with count
        if (transformedData.length > 0) {
          console.log('✅ Found deleted records, showing success toast');
          toast.success(`Found ${transformedData.length} deleted graduate record${transformedData.length === 1 ? '' : 's'}`);
        } else {
          console.log('ℹ️ No deleted records found');
          toast.info('No deleted graduate records found');
        }
      } else {
        console.log('❌ No data in response:', response);
        setGraduates([]);
        setHasSearched(true);
        toast.info('No deleted graduate records found');
      }
    } catch (error: any) {
      console.error('❌ Error fetching deleted graduates:', error);
      console.error('❌ Error details:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);

      // Clear graduates and show error
      setGraduates([]);
      setHasSearched(true);

      // Show specific error message based on error type
      if (error.response?.status === 401) {
        toast.error('Authentication required. Please log in again.');
      } else if (error.response?.status === 403) {
        toast.error('You do not have permission to view deleted records.');
      } else if (error.response?.status === 404) {
        toast.error('Deleted records endpoint not found.');
      } else {
        toast.error('Failed to load deleted graduates. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle opening the import modal
  const handleOpenImportModal = () => {
    setIsImportModalOpen(true);
  };

  // Handle delete confirmation (uses soft delete)
  const handleDeleteConfirm = async () => {
    if (!selectedGraduate) return;

    try {
      console.log('Soft deleting graduate:', selectedGraduate.id);
      const response = await graduateVerificationAPI.softDeleteGraduate(selectedGraduate.id);
      console.log('Soft delete response:', response);

      toast.success('Graduate moved to trash successfully');
      setIsDeleteDialogOpen(false);
      setSelectedGraduate(null);

      // Refresh the list after deletion
      if (hasSearched) {
        await applyFilters();
      }
    } catch (error) {
      console.error('Error deleting graduate:', error);
      toast.error('Failed to delete graduate');
    }
  };

  // Handle closing the import modal
  const handleCloseImportModal = () => {
    setIsImportModalOpen(false);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page !== currentPage && page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      // Re-fetch data with new page
      fetchGraduates(currentSearchParams);
    }
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
    // Re-fetch data with new page size
    const updatedParams = { ...currentSearchParams };
    fetchGraduates(updatedParams);
  };

  // Handle deleting a graduate
  const handleDeleteGraduate = async () => {
    if (!selectedGraduate) return;

    try {
      await graduateVerificationAPI.deleteGraduate(selectedGraduate.id);
      toast.success('Graduate deleted successfully');
      // Refresh the data and ensure hasSearched is true to show the updated list
      if (hasSearched) {
        fetchGraduates();
      } else {
        // If we weren't showing the list before, don't show it after deletion
        setGraduates([]);
      }
    } catch (error) {
      console.error('Error deleting graduate:', error);
      toast.error('Failed to delete graduate');
    }
  };

  // Server-side pagination - no need for client-side slicing
  const filteredGraduates = graduates;
  const currentItems = graduates; // All items from current page

  // Generate page numbers for pagination controls
  const pageNumbers = [];
  const maxVisiblePages = 5;
  const startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Graduate Management</CardTitle>
                <CardDescription className="mt-1">
                  Comprehensive graduate statistics and management system
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger
                value="statistics"
                className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
              >
                <BarChart3 className="h-4 w-4" />
                <span>Statistics</span>
              </TabsTrigger>
              <TabsTrigger
                value="manage"
                className="flex items-center space-x-2 data-[state=active]:bg-[#1a73c0] data-[state=active]:text-white"
              >
                <Users className="h-4 w-4" />
                <span>Graduate Management</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="statistics" className="space-y-4">
              {/* Recent Graduates Dashboard */}
              <RecentGraduatesDashboard />
            </TabsContent>

            <TabsContent value="manage" className="space-y-4">
              {/* Main Graduate Management Card */}
              <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Graduate Management</CardTitle>
                <CardDescription className="mt-1">
                  Search, view, and manage graduate verification records
                </CardDescription>
              </div>
            </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleOpenImportModal}
              className="border-blue-200 text-blue-700 hover:bg-blue-50 hover:text-blue-800 transition-all duration-200"
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Import CSV
            </Button>
            <Button
              onClick={handleOpenCreateModal}
              className="bg-[#1a73c0] hover:bg-blue-700 transition-all duration-200 shadow-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Graduate
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        {/* Search Section */}
        <div className="mb-6">
          <div className="p-5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
              <div className="flex items-center">
                <div className="bg-[#1a73c0] p-1.5 rounded-md shadow-sm mr-3">
                  <Filter className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-base font-medium text-[#1a73c0]">Search & Filter Options</h3>
                  <p className="text-xs text-gray-500 mt-0.5">Find graduates using search criteria</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 mt-3 md:mt-0">
                {/* Enhanced Show Deleted Toggle */}
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowDeleted(!showDeleted);
                      if (!showDeleted) {
                        fetchDeletedGraduates();
                      } else {
                        resetFilters();
                      }
                    }}
                    className={`transition-all duration-300 shadow-sm ${
                      showDeleted
                        ? 'border-red-300 text-red-700 bg-red-50 hover:bg-red-100 hover:border-red-400'
                        : 'border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'
                    }`}
                  >
                    <Trash2 className="h-4 w-4 mr-1.5" />
                    {showDeleted ? 'Show Active' : 'Show Deleted'}
                    {showDeleted && graduates.length > 0 && (
                      <span className="ml-2 px-2 py-0.5 bg-red-200 text-red-800 rounded-full text-xs font-medium">
                        {graduates.length}
                      </span>
                    )}
                  </Button>


                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportToCSV}
                  className="border-green-200 text-green-700 hover:bg-green-50 hover:text-green-800 transition-all duration-200"
                >
                  <FileSpreadsheet className="h-4 w-4 mr-1.5" />
                  CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportToPDF}
                  className="border-red-200 text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200"
                >
                  <Download className="h-4 w-4 mr-1.5" />
                  PDF
                </Button>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg border border-blue-200 shadow-sm">
              {/* First Row - Basic Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                <div className="space-y-2 group">
                  <Label htmlFor="studentId" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                    <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                      <Search className="h-4 w-4 text-blue-500" />
                    </div>
                    Student ID
                  </Label>
                  <Input
                    id="studentId"
                    placeholder="Enter student ID"
                    value={studentId}
                    onChange={(e) => setStudentId(e.target.value)}
                    className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                  />
                </div>
                <div className="space-y-2 group">
                  <Label htmlFor="college" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                    <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                      <Building className="h-4 w-4 text-blue-500" />
                    </div>
                    College
                  </Label>
                  <Select
                    value={collegeId}
                    onValueChange={(value) => setCollegeId(value)}
                  >
                    <SelectTrigger
                      id="college"
                      className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                    >
                      <SelectValue placeholder="All Colleges" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Colleges</SelectItem>
                      {colleges.map((college) => (
                        <SelectItem key={college.id} value={college.id.toString()}>
                          {college.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2 group">
                  <Label htmlFor="department" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                    <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                      <Building className="h-4 w-4 text-blue-500" />
                    </div>
                    Department
                  </Label>
                  <Select
                    value={departmentId}
                    onValueChange={(value) => setDepartmentId(value)}
                    disabled={filteredDepartments.length === 0}
                  >
                    <SelectTrigger
                      id="department"
                      className={`border-blue-200 focus:ring-blue-400 shadow-sm transition-all rounded-md h-9 text-sm ${
                        filteredDepartments.length === 0 ? "opacity-70 cursor-not-allowed" : "hover:border-blue-300"
                      }`}
                    >
                      <SelectValue placeholder="All Departments" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      {filteredDepartments.map((department) => (
                        <SelectItem key={department.id} value={department.id.toString()}>
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Second Row - Academic Classifications */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2 group">
                  <Label htmlFor="fieldOfStudy" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                    <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                      <GraduationCap className="h-4 w-4 text-blue-500" />
                    </div>
                    Field of Study
                  </Label>
                  <Select
                    value={fieldOfStudyId}
                    onValueChange={(value) => setFieldOfStudyId(value)}
                    disabled={filteredFieldsOfStudy.length === 0}
                  >
                    <SelectTrigger
                      id="fieldOfStudy"
                      className={`border-blue-200 focus:ring-blue-400 shadow-sm transition-all rounded-md h-9 text-sm ${
                        filteredFieldsOfStudy.length === 0 ? "opacity-70 cursor-not-allowed" : "hover:border-blue-300"
                      }`}
                    >
                      <SelectValue placeholder="All Fields of Study" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Fields of Study</SelectItem>
                      {filteredFieldsOfStudy.map((field) => (
                        <SelectItem key={field.id} value={field.id.toString()}>
                          {field.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2 group">
                  <Label htmlFor="program" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                    <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                      <GraduationCap className="h-4 w-4 text-blue-500" />
                    </div>
                    Program
                  </Label>
                  <Select
                    value={programId}
                    onValueChange={(value) => setProgramId(value)}
                  >
                    <SelectTrigger
                      id="program"
                      className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                    >
                      <SelectValue placeholder="All Programs" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Programs</SelectItem>
                      {programs.map((program) => (
                        <SelectItem key={program.id} value={program.id.toString()}>
                          {program.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2 group">
                  <Label htmlFor="admissionClassification" className="text-sm font-medium text-gray-700 flex items-center group-hover:text-[#1a73c0] transition-colors">
                    <div className="bg-blue-50 p-1.5 rounded-md mr-2 group-hover:bg-blue-100 transition-colors">
                      <Users className="h-4 w-4 text-blue-500" />
                    </div>
                    Admission Class
                  </Label>
                  <Select
                    value={admissionClassificationId}
                    onValueChange={(value) => setAdmissionClassificationId(value)}
                  >
                    <SelectTrigger
                      id="admissionClassification"
                      className="border-blue-200 focus:ring-blue-400 shadow-sm transition-all hover:border-blue-300 rounded-md h-9 text-sm"
                    >
                      <SelectValue placeholder="All Admission Classes" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Admission Classes</SelectItem>
                      {admissionClassifications.map((classification) => (
                        <SelectItem key={classification.id} value={classification.id.toString()}>
                          {classification.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end mt-6 space-x-3">
                <Button
                  variant="outline"
                  onClick={resetFilters}
                  className="text-blue-600 border-blue-200 hover:bg-blue-50 transition-all"
                >
                  <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                  Clear Filters
                </Button>
                <Button
                  onClick={handleSearch}
                  disabled={showDeleted}
                  className="bg-[#1a73c0] hover:bg-[#145da1] px-6 transition-all duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Search className="h-4 w-4 mr-2" />
                  {showDeleted ? 'Search Disabled' : 'Search Graduates'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Graduates Table - Only show when search has been performed */}
        {hasSearched && (
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="text-[#1a73c0] font-medium">Student ID</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Name</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Gender</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">College</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Department</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Field of Study</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Program</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Admission Class</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Entry Year</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Graduation Year</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">GPA</TableHead>
                    {showDeleted && <TableHead className="text-[#1a73c0] font-medium">Deleted Date</TableHead>}
                    <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={11} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-blue-100 p-3 rounded-full">
                            <Loader2 className="h-8 w-8 text-[#1a73c0] animate-spin" />
                          </div>
                          <div className="text-[#1a73c0] font-medium">
                            {showDeleted ? 'Loading deleted graduates...' : 'Searching graduates...'}
                          </div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {showDeleted
                              ? 'Please wait while we load deleted graduate records.'
                              : 'Please wait while we search for graduate records.'
                            }
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : currentItems.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={11} className="text-center py-12">
                        <div className="flex flex-col justify-center items-center space-y-3">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <Search className="h-8 w-8 text-gray-500" />
                          </div>
                          <div className="text-gray-700 font-medium">
                            {showDeleted ? 'No deleted graduates found' : 'No graduates found'}
                          </div>
                          <div className="text-sm text-gray-500 max-w-sm text-center">
                            {showDeleted
                              ? 'There are no deleted graduate records to display. This means no graduates have been moved to trash yet.'
                              : 'Try adjusting your search criteria or check if the data exists.'
                            }
                          </div>
                          {showDeleted && (
                            <div className="flex items-center space-x-2 text-xs text-green-600 bg-green-50 px-3 py-2 rounded-full border border-green-200">
                              <span>✅ This is good - no records have been accidentally deleted!</span>
                            </div>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={resetFilters}
                            className="mt-2 border-blue-200 text-blue-600 hover:bg-blue-50"
                          >
                            Reset Filters
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentItems.map((graduate) => (
                      <TableRow key={graduate.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell className="font-medium text-[#1a73c0]">{graduate.student_id}</TableCell>
                        <TableCell>
                          {graduate.first_name} {graduate.middle_name ? graduate.middle_name + ' ' : ''}
                          {graduate.last_name}
                        </TableCell>
                        <TableCell>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            graduate.gender === 'Male'
                              ? 'bg-blue-100 text-blue-800'
                              : graduate.gender === 'Female'
                              ? 'bg-pink-100 text-pink-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {graduate.gender || 'N/A'}
                          </span>
                        </TableCell>
                        <TableCell>
                          {graduate.college && graduate.college.name ? graduate.college.name :
                           graduate.college_name ? graduate.college_name : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {graduate.department && graduate.department.name ? graduate.department.name :
                           graduate.department_name ? graduate.department_name : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {graduate.field_of_study && graduate.field_of_study.name ? graduate.field_of_study.name :
                           graduate.field_of_study_name ? graduate.field_of_study_name : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {graduate.program && graduate.program.name ? graduate.program.name :
                           graduate.program_name ? graduate.program_name : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {graduate.admission_classification && graduate.admission_classification.name ? graduate.admission_classification.name :
                           graduate.admission_classification_name ? graduate.admission_classification_name : 'N/A'}
                        </TableCell>
                        <TableCell>{graduate.year_of_entry}</TableCell>
                        <TableCell>{graduate.year_of_graduation}</TableCell>
                        <TableCell>{graduate.gpa}</TableCell>
                        {showDeleted && (
                          <TableCell>
                            {graduate.deleted_at ? new Date(graduate.deleted_at).toLocaleDateString() : 'N/A'}
                          </TableCell>
                        )}
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            {showDeleted ? (
                              // Actions for deleted records
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleOpenRestoreDialog(graduate)}
                                className="h-8 w-8 p-0 border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors"
                                title="Restore"
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                            ) : (
                              // Actions for active records
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleOpenEditModal(graduate)}
                                  title="Edit"
                                  className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleOpenDeleteDialog(graduate)}
                                  className="h-8 w-8 p-0 border-red-200 text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors"
                                  title="Delete"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        )}

        {/* Show message when no search has been performed */}
        {!hasSearched && !loading && !showDeleted && (
          <div className="flex flex-col items-center justify-center py-12 text-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 shadow-sm">
            <div className="bg-white p-4 rounded-full shadow-sm mb-4">
              <Search className="h-16 w-16 text-[#1a73c0]" />
            </div>
            <h3 className="text-xl font-medium text-[#1a73c0] mb-2">Search for Graduates</h3>
            <p className="text-gray-600 max-w-md mb-4">
              Use the search criteria above to find graduate records. Enter a student ID or select from the dropdown options, then click the Search Graduates button.
            </p>

          </div>
        )}

        {/* Pagination - Only show when search has been performed and there are results */}
        {!loading && hasSearched && filteredGraduates.length > 0 && (
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
            <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
              <span className="text-sm font-medium text-[#1a73c0]">Show</span>
              <select
                value={itemsPerPage}
                onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
              >
                {itemsPerPageOptions.map(option => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
              <span className="text-sm font-medium text-[#1a73c0]">per page</span>
              <div className="text-sm text-gray-500 ml-2 border-l border-gray-200 pl-3">
                <span className="font-medium text-[#1a73c0]">
                  {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalCount)}
                </span> of <span className="font-medium text-[#1a73c0]">{totalCount}</span> graduates
              </div>
            </div>
            <div className="flex items-center">
              <div className="flex rounded-md overflow-hidden border border-blue-200 shadow-sm">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                    currentPage === 1
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="First Page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                    currentPage === 1
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="Previous Page"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* Dynamic page number buttons with ellipsis for large page counts */}
                {totalPages <= 7 ? (
                  // If we have 7 or fewer pages, show all page numbers
                  Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                    <Button
                      key={number}
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePageChange(number)}
                      className={cn(
                        "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                        currentPage === number
                          ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                          : "bg-white text-gray-700 hover:bg-blue-50"
                      )}
                    >
                      {number}
                    </Button>
                  ))
                ) : (
                  // For more than 7 pages, show a condensed version with ellipsis
                  <>
                    {/* Always show first page */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePageChange(1)}
                      className={cn(
                        "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                        currentPage === 1
                          ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                          : "bg-white text-gray-700 hover:bg-blue-50"
                      )}
                    >
                      1
                    </Button>

                    {/* Show ellipsis if not showing first few pages */}
                    {currentPage > 3 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled
                        className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                      >
                        ...
                      </Button>
                    )}

                    {/* Show current page and adjacent pages */}
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter(number =>
                        number > 1 &&
                        number < totalPages &&
                        (
                          number === currentPage - 1 ||
                          number === currentPage ||
                          number === currentPage + 1 ||
                          (currentPage <= 3 && number <= 4) ||
                          (currentPage >= totalPages - 2 && number >= totalPages - 3)
                        )
                      )
                      .map(number => (
                        <Button
                          key={number}
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePageChange(number)}
                          className={cn(
                            "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                            currentPage === number
                              ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                              : "bg-white text-gray-700 hover:bg-blue-50"
                          )}
                        >
                          {number}
                        </Button>
                      ))
                    }

                    {/* Show ellipsis if not showing last few pages */}
                    {currentPage < totalPages - 2 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled
                        className="h-9 w-9 p-0 rounded-none border-r border-blue-200 bg-white text-gray-400"
                      >
                        ...
                      </Button>
                    )}

                    {/* Always show last page */}
                    {totalPages > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePageChange(totalPages)}
                        className={cn(
                          "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                          currentPage === totalPages
                            ? "bg-[#1a73c0] text-white font-medium hover:bg-[#145da1]"
                            : "bg-white text-gray-700 hover:bg-blue-50"
                        )}
                      >
                        {totalPages}
                      </Button>
                    )}
                  </>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none border-r border-blue-200",
                    currentPage >= totalPages
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="Next Page"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage >= totalPages}
                  className={cn(
                    "h-9 w-9 p-0 rounded-none",
                    currentPage >= totalPages
                      ? "text-gray-400 bg-gray-50"
                      : "text-[#1a73c0] bg-white hover:bg-blue-50"
                  )}
                  title="Last Page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Create/Edit/Delete Modals */}
        <GraduateForm
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSuccess={() => {
            // Don't fetch graduates to avoid showing the list
            // Just close the modal and show a success message
            toast.success('Graduate created successfully. Use the search to view graduates.');
          }}
          isEditMode={false}
        />

        <GraduateForm
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSuccess={() => {
            // Don't fetch graduates to avoid showing the list
            // Just close the modal and show a success message
            toast.success('Graduate updated successfully. Use the search to view graduates.');
          }}
          graduateData={selectedGraduate}
          isEditMode={true}
        />

        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onClose={() => setIsDeleteDialogOpen(false)}
          onConfirm={handleDeleteConfirm}
          title="Move to Trash"
          description={`Are you sure you want to move ${selectedGraduate?.first_name} ${selectedGraduate?.last_name} to trash? This record can be restored later.`}
        />

        <DeleteConfirmationDialog
          isOpen={isRestoreDialogOpen}
          onClose={() => setIsRestoreDialogOpen(false)}
          onConfirm={handleRestore}
          title="Restore Graduate"
          description={`Are you sure you want to restore ${selectedGraduate?.first_name} ${selectedGraduate?.last_name}? This will make the record active again.`}
          confirmText="Restore"
          confirmButtonClass="bg-green-600 hover:bg-green-700"
        />

        {/* CSV Import Modal */}
        <Dialog
          open={isImportModalOpen}
          onOpenChange={(open) => {
            if (!open) {
              handleCloseImportModal();
            }
          }}
        >
          <DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] p-0 overflow-hidden bg-gradient-to-br from-white to-blue-50 border-2 border-blue-100 shadow-2xl">
            {/* Enhanced Header */}
            <DialogHeader className="relative px-6 sm:px-8 pt-6 sm:pt-8 pb-6 bg-gradient-to-r from-[#1a73c0] to-blue-600 text-white">
              <div className="absolute inset-0 bg-gradient-to-r from-[#1a73c0]/90 to-blue-600/90"></div>
              <div className="relative z-10">
                <DialogTitle className="text-xl sm:text-2xl font-bold flex items-center">
                  <div className="bg-white/20 p-2 rounded-lg mr-3 backdrop-blur-sm">
                    <FileSpreadsheet className="h-6 w-6 sm:h-7 sm:w-7 text-white" />
                  </div>
                  <div>
                    <div className="text-xl sm:text-2xl font-bold">Import Graduate Records</div>
                    <div className="text-blue-100 text-sm font-normal mt-1 hidden sm:block">
                      Bulk upload graduate data from CSV files
                    </div>
                  </div>
                </DialogTitle>

              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
            </DialogHeader>

            {/* Enhanced Content Area */}
            <div className="relative">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                  backgroundSize: '30px 30px'
                }}></div>
              </div>

              {/* Content */}
              <div className="relative z-10 px-4 sm:px-6 lg:px-8 py-6 sm:py-8 max-h-[calc(90vh-200px)] overflow-y-auto">
                <div className="bg-white rounded-xl shadow-lg border border-blue-100 p-4 sm:p-6">
                  <GraduateCsvImport onSuccess={() => {
                    // Close the modal but don't show the list after import
                    handleCloseImportModal();
                    // Don't set hasSearched to true, so the list remains hidden
                    // Don't fetch graduates to avoid loading the list
                    // Note: The toast message is now handled in the GraduateCsvImport component
                    // based on the actual import results
                  }} />
                </div>
              </div>


            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default GraduateManagement;
