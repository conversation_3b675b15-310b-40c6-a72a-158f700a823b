# Generated by Django 5.2.1 on 2025-05-31 12:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('college', '0001_initial'),
        ('department', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudyField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_of_study', models.CharField(max_length=255, verbose_name='Field of Study')),
                ('code', models.CharField(blank=True, max_length=20, null=True, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('status', models.BooleanField(default=True, verbose_name='Active Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('college', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_of_studies', to='college.college', verbose_name='College')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_of_studies', to='department.department', verbose_name='Department')),
            ],
            options={
                'verbose_name': 'Field of Study',
                'verbose_name_plural': 'Fields of Study',
                'ordering': ['field_of_study'],
            },
        ),
    ]
