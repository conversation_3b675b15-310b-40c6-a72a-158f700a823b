/**
 * Comprehensive Authentication and Authorization API Services
 */

import api from './api';
import axios from 'axios';
import { API_BASE_URL } from '../config';

// Create a separate axios instance for auth requests to avoid interceptor loops
const authAxios = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  timeout: 10000,
});
import {
  User,
  Group,
  Permission,
  CreateUserRequest,
  UpdateUserRequest,
  CreateGroupRequest,
  UpdateGroupRequest,
  UserFilters,
  GroupFilters,
  PermissionFilters,
  PaginatedResponse,
  AuthStats,
  UserActivity,
  SecurityEvent,
  RoleHierarchy,
  AccessControlRule,
  UserProfile
} from '@/types/auth';

// User Management API
const userAPI = {
  // Get all users with filtering and pagination
  getUsers: (filters?: UserFilters) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    return api.get<PaginatedResponse<User>>(`/auth/users/?${params.toString()}`);
  },

  // Get single user by ID
  getUser: (id: number) =>
    api.get<User>(`/auth/users/${id}/`),

  // Create new user
  createUser: (data: CreateUserRequest) =>
    api.post<User>('/auth/users/', data),

  // Update user
  updateUser: (id: number, data: UpdateUserRequest) =>
    api.patch<User>(`/auth/users/${id}/`, data),

  // Delete user
  deleteUser: (id: number) =>
    api.delete(`/auth/users/${id}/`),

  // Activate/Deactivate user
  toggleUserStatus: (id: number, is_active: boolean) =>
    api.patch<User>(`/auth/users/${id}/`, { is_active }),

  // Reset user password
  resetPassword: (id: number, new_password: string) =>
    api.post(`/auth/users/${id}/reset_password/`, { new_password }),

  // Get user permissions
  getUserPermissions: (id: number) =>
    api.get<Permission[]>(`/auth/users/${id}/permissions/`),

  // Get user groups
  getUserGroups: (id: number) =>
    api.get<Group[]>(`/auth/users/${id}/groups/`),

  // Add user to groups
  addUserToGroups: (id: number, group_ids: number[]) =>
    api.post(`/auth/users/${id}/add_groups/`, { group_ids }),

  // Remove user from groups
  removeUserFromGroups: (id: number, group_ids: number[]) =>
    api.post(`/auth/users/${id}/remove_groups/`, { group_ids }),

  // Assign permissions to user
  assignPermissions: (id: number, permission_ids: number[]) =>
    api.post(`/auth/users/${id}/assign_permissions/`, { permission_ids }),

  // Remove permissions from user
  removePermissions: (id: number, permission_ids: number[]) =>
    api.post(`/auth/users/${id}/remove_permissions/`, { permission_ids }),

  // Get user activity log
  getUserActivity: (id: number, page?: number) =>
    api.get<PaginatedResponse<UserActivity>>(`/auth/users/${id}/activity/?page=${page || 1}`),

  // Bulk operations
  bulkActivate: (user_ids: number[]) =>
    api.post('/auth/users/bulk_activate/', { user_ids }),

  bulkDeactivate: (user_ids: number[]) =>
    api.post('/auth/users/bulk_deactivate/', { user_ids }),

  bulkDelete: (user_ids: number[]) =>
    api.post('/auth/users/bulk_delete/', { user_ids }),

  // Export users
  exportUsers: (filters?: UserFilters, format: 'csv' | 'xlsx' = 'csv') => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    params.append('format', format);
    return api.get(`/auth/users/export/?${params.toString()}`, { responseType: 'blob' });
  },
};

// Group Management API
const groupAPI = {
  // Get all groups with filtering and pagination
  getGroups: (filters?: GroupFilters) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    return api.get<PaginatedResponse<Group>>(`/auth/groups/?${params.toString()}`);
  },

  // Get single group by ID
  getGroup: (id: number) =>
    api.get<Group>(`/auth/groups/${id}/`),

  // Create new group
  createGroup: (data: CreateGroupRequest) =>
    api.post<Group>('/auth/groups/', data),

  // Update group
  updateGroup: (id: number, data: UpdateGroupRequest) =>
    api.patch<Group>(`/auth/groups/${id}/`, data),

  // Delete group
  deleteGroup: (id: number) =>
    api.delete(`/auth/groups/${id}/`),

  // Get group members
  getGroupMembers: (id: number, page?: number) =>
    api.get<PaginatedResponse<User>>(`/auth/groups/${id}/members/?page=${page || 1}`),

  // Add members to group
  addMembers: (id: number, user_ids: number[]) =>
    api.post(`/auth/groups/${id}/add_members/`, { user_ids }),

  // Remove members from group
  removeMembers: (id: number, user_ids: number[]) =>
    api.post(`/auth/groups/${id}/remove_members/`, { user_ids }),

  // Get group permissions
  getGroupPermissions: (id: number) =>
    api.get<Permission[]>(`/auth/groups/${id}/permissions/`),

  // Assign permissions to group
  assignPermissions: (id: number, permission_ids: number[]) =>
    api.post(`/auth/groups/${id}/assign_permissions/`, { permission_ids }),

  // Remove permissions from group
  removePermissions: (id: number, permission_ids: number[]) =>
    api.post(`/auth/groups/${id}/remove_permissions/`, { permission_ids }),

  // Clone group
  cloneGroup: (id: number, new_name: string) =>
    api.post<Group>(`/auth/groups/${id}/clone/`, { name: new_name }),
};

// Permission Management API
const permissionAPI = {
  // Get all permissions with filtering and pagination
  getPermissions: (filters?: PermissionFilters) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    return api.get<PaginatedResponse<Permission>>(`/auth/permissions/?${params.toString()}`);
  },

  // Get single permission by ID
  getPermission: (id: number) =>
    api.get<Permission>(`/auth/permissions/${id}/`),

  // Get permissions by content type
  getPermissionsByContentType: (content_type: number) =>
    api.get<Permission[]>(`/auth/permissions/by_content_type/${content_type}/`),

  // Get permissions by app
  getPermissionsByApp: (app_label: string) =>
    api.get<Permission[]>(`/auth/permissions/by_app/${app_label}/`),

  // Get content types
  getContentTypes: () =>
    api.get<Array<{ id: number; app_label: string; model: string; name: string }>>('/auth/content_types/'),
};

// User Profile API
const profileAPI = {
  // Get user profile
  getProfile: (user_id: number) =>
    api.get<UserProfile>(`/auth/profiles/${user_id}/`),

  // Update user profile
  updateProfile: (user_id: number, data: Partial<UserProfile>) =>
    api.patch<UserProfile>(`/auth/profiles/${user_id}/`, data),

  // Upload avatar
  uploadAvatar: (user_id: number, file: File) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return api.post(`/auth/profiles/${user_id}/upload_avatar/`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
};

// Statistics and Analytics API
const statsAPI = {
  // Get authentication statistics
  getAuthStats: () =>
    api.get<AuthStats>('/auth/stats/'),

  // Get user activity
  getUserActivity: (filters?: { user_id?: number; days?: number; page?: number }) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    return api.get<PaginatedResponse<UserActivity>>(`/auth/activity/?${params.toString()}`);
  },

  // Get security events
  getSecurityEvents: (filters?: { event_type?: string; severity?: string; days?: number; page?: number }) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    return api.get<PaginatedResponse<SecurityEvent>>(`/auth/security_events/?${params.toString()}`);
  },

  // Get role hierarchy
  getRoleHierarchy: () =>
    api.get<RoleHierarchy[]>('/auth/role_hierarchy/'),
};

// Access Control API
const accessControlAPI = {
  // Get access control rules
  getRules: (page?: number) =>
    api.get<PaginatedResponse<AccessControlRule>>(`/auth/access_rules/?page=${page || 1}`),

  // Create access control rule
  createRule: (data: Omit<AccessControlRule, 'id' | 'created_at' | 'updated_at'>) =>
    api.post<AccessControlRule>('/auth/access_rules/', data),

  // Update access control rule
  updateRule: (id: number, data: Partial<AccessControlRule>) =>
    api.patch<AccessControlRule>(`/auth/access_rules/${id}/`, data),

  // Delete access control rule
  deleteRule: (id: number) =>
    api.delete(`/auth/access_rules/${id}/`),

  // Test access control rule
  testRule: (rule_data: any, test_context: any) =>
    api.post('/auth/access_rules/test/', { rule: rule_data, context: test_context }),
};

// Core Authentication API
const authAPI = {
  // Login with username and password
  login: async (credentials: { username: string; password: string }) => {
    try {
      const response = await authAxios.post('/token/', credentials);
      return { data: response.data };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  // Refresh JWT token
  refreshToken: async (refreshToken: string) => {
    try {
      const response = await authAxios.post('/token/refresh/', {
        refresh: refreshToken
      });
      return { data: response.data };
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  },

  // Get current user information
  getCurrentUser: async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await authAxios.get('/user/me/', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return { data: response.data };
    } catch (error) {
      console.error('Get current user error:', error);
      throw error;
    }
  },

  // Logout and blacklist refresh token
  logout: async (refreshToken?: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await authAxios.post('/user/auth/logout/',
        { refresh_token: refreshToken },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      return { data: response.data };
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  },

  // Validate JWT token
  validateToken: async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await authAxios.post('/user/auth/token/validate/', {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return { data: response.data };
    } catch (error) {
      console.error('Token validation error:', error);
      throw error;
    }
  },

  // Check specific permission
  checkPermission: async (permission: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await authAxios.get(`/user/auth/check-permission/?permission=${permission}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return { data: response.data };
    } catch (error) {
      console.error('Permission check error:', error);
      throw error;
    }
  },

  // Get authentication status
  getAuthStatus: async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await authAxios.get('/user/auth/status/', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return { data: response.data };
    } catch (error) {
      console.error('Auth status error:', error);
      throw error;
    }
  }
};

// Export individual APIs for named imports
export {
  userAPI,
  groupAPI,
  permissionAPI,
  profileAPI,
  statsAPI,
  accessControlAPI,
  authAPI,
};

// Default export
export default {
  userAPI,
  groupAPI,
  permissionAPI,
  profileAPI,
  statsAPI,
  accessControlAPI,
  authAPI,
};
