from django.http import JsonResponse
from django.middleware.csrf import get_token
from django.views.decorators.csrf import ensure_csrf_cookie
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([AllowAny])
@ensure_csrf_cookie
def get_csrf_token(request):
    """
    API endpoint to get CSRF token
    """
    try:
        csrf_token = get_token(request)
        logger.info(f"CSRF token requested from IP: {request.META.get('REMOTE_ADDR', 'unknown')}")
        
        return Response({
            'csrf_token': csrf_token,
            'success': True
        })
    except Exception as e:
        logger.error(f"Error generating CSRF token: {e}")
        return Response({
            'error': 'Failed to generate CSRF token',
            'success': False
        }, status=500)

@method_decorator(ensure_csrf_cookie, name='dispatch')
class CSRFTokenView(View):
    """
    Class-based view to get CSRF token
    """
    
    def get(self, request):
        """
        Return CSRF token in JSON format
        """
        try:
            csrf_token = get_token(request)
            logger.info(f"CSRF token requested via class view from IP: {request.META.get('REMOTE_ADDR', 'unknown')}")
            
            return JsonResponse({
                'csrf_token': csrf_token,
                'success': True
            })
        except Exception as e:
            logger.error(f"Error generating CSRF token in class view: {e}")
            return JsonResponse({
                'error': 'Failed to generate CSRF token',
                'success': False
            }, status=500)

@require_http_methods(["GET"])
@ensure_csrf_cookie
def csrf_token_simple(request):
    """
    Simple function-based view to get CSRF token
    """
    try:
        csrf_token = get_token(request)
        logger.info(f"CSRF token requested via simple view from IP: {request.META.get('REMOTE_ADDR', 'unknown')}")
        
        return JsonResponse({
            'csrftoken': csrf_token,
            'csrf_token': csrf_token,
            'success': True
        })
    except Exception as e:
        logger.error(f"Error generating CSRF token in simple view: {e}")
        return JsonResponse({
            'error': 'Failed to generate CSRF token',
            'success': False
        }, status=500)
