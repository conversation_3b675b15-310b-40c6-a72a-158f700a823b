import { useEffect, useState } from 'react';
import Layout from '@/components/Layout';
import programAPI, { ApplicationInfo, RegistrationPeriod } from '@/services/programService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Info, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface CollegeGroup {
  id: number;
  name: string;
  applications: ApplicationInfo[];
}

const Programs = () => {
  // State for application information
  const [applicationInfo, setApplicationInfo] = useState<ApplicationInfo[]>([]);
  const [collegeGroups, setCollegeGroups] = useState<CollegeGroup[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for registration periods
  const [registrationPeriods, setRegistrationPeriods] = useState<RegistrationPeriod[]>([]);
  const [registrationStatus, setRegistrationStatus] = useState<{[key: number]: {active: boolean, futureOpening: boolean}}>({});

  // Fetch application info and registration periods on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch all application information
        const appInfoData = await programAPI.getApplicationInfo(0); // 0 means get all
        setApplicationInfo(appInfoData);
        
        // Fetch registration periods
        const periodsData = await programAPI.getRegistrationPeriods();
        setRegistrationPeriods(periodsData);
        
        // Group application info by college
        const groupedByCollege = groupByCollege(appInfoData);
        setCollegeGroups(groupedByCollege);
        
        // Check registration status for each application
        const statusMap = checkRegistrationStatus(appInfoData, periodsData);
        setRegistrationStatus(statusMap);
        
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load application information');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  
  // Function to group application info by college
  const groupByCollege = (applications: ApplicationInfo[]): CollegeGroup[] => {
    const collegeMap: {[key: number]: CollegeGroup} = {};
    
    applications.forEach(app => {
      const collegeId = app.college.id;
      
      if (!collegeMap[collegeId]) {
        collegeMap[collegeId] = {
          id: collegeId,
          name: app.college.name,
          applications: []
        };
      }
      
      collegeMap[collegeId].applications.push(app);
    });
    
    return Object.values(collegeMap);
  };
  
  // Function to check registration status for each application
  const checkRegistrationStatus = (
    applications: ApplicationInfo[], 
    periods: RegistrationPeriod[]
  ): {[key: number]: {active: boolean, futureOpening: boolean}} => {
    const statusMap: {[key: number]: {active: boolean, futureOpening: boolean}} = {};
    const now = new Date();
    
    applications.forEach(app => {
      // Default status
      statusMap[app.id] = { active: false, futureOpening: false };
      
      // Find relevant registration periods
      for (const period of periods) {
        if (!period.is_active) continue;
        
        const openDate = new Date(period.open_date);
        const closeDate = new Date(period.close_date);
        
        // Check if registration is active (current date is between open and close dates)
        if (now >= openDate && now < closeDate) {
          statusMap[app.id].active = true;
        }
        
        // Check if opening date is in the future
        if (now < openDate) {
          statusMap[app.id].futureOpening = true;
        }
      }
    });
    
    return statusMap;
  };

  return (
    <Layout>
      <div className="container mx-auto py-12 px-4">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gondar mb-4">Available Programs</h1>
          <div className="h-1 w-20 bg-gondar-accent mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-600 max-w-3xl mx-auto text-lg">
            Explore our diverse range of academic programs designed to help you achieve your educational and career goals
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-12 w-12 text-gondar animate-spin" />
            <span className="ml-3 text-xl text-gondar">Loading programs...</span>
          </div>
        )}
        
        {/* Error State */}
        {error && (
          <Alert variant="destructive" className="my-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {/* No Programs State */}
        {!loading && !error && applicationInfo.length === 0 && (
          <div className="text-center py-20">
            <p className="text-xl text-gray-500">No programs available at the moment.</p>
          </div>
        )}
        
        {/* Display Programs Grouped by College */}
        {!loading && !error && collegeGroups.map((college) => (
          <Card key={college.id} className="mb-8 shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl text-gondar">{college.name}</CardTitle>
              <CardDescription>
                {college.applications.length} program(s) available
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Department</TableHead>
                    <TableHead>Field of Study</TableHead>
                    <TableHead>Program</TableHead>
                    <TableHead>Admission Type</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {college.applications.map((app) => (
                    <TableRow key={app.id}>
                      <TableCell>{app.department.name}</TableCell>
                      <TableCell>{app.field_of_study.name}</TableCell>
                      <TableCell>
                        {app.program.program_code ? 
                          `${app.program.program_code} - ${app.program.program_name}` : 
                          app.program.program_name}
                      </TableCell>
                      <TableCell>{app.admission_type.name}</TableCell>
                      <TableCell>{app.duration || '2'} Years</TableCell>
                      <TableCell>
                        {registrationStatus[app.id]?.futureOpening ? (
                          <Alert variant="warning" className="p-2">
                            <Info className="h-4 w-4" />
                            <AlertTitle className="text-xs">Registration Not Open Yet</AlertTitle>
                          </Alert>
                        ) : registrationStatus[app.id]?.active ? (
                          <Alert variant="default" className="p-2">
                            <CheckCircle className="h-4 w-4" />
                            <AlertTitle className="text-xs">Registration Active</AlertTitle>
                          </Alert>
                        ) : (
                          <Alert variant="destructive" className="p-2">
                            <XCircle className="h-4 w-4" />
                            <AlertTitle className="text-xs">No Active Registration</AlertTitle>
                          </Alert>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        ))}
      </div>
    </Layout>
  );
};

export default Programs;
