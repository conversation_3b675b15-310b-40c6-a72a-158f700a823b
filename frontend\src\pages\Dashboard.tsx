
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import DashboardLayout from '@/components/DashboardLayout';
import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardT<PERSON>le, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  FileText, Clock, Bell, CheckCircle, AlertTriangle, XCircle,
  GraduationCap, BookOpen, CreditCard, Calendar, User, School,
  BarChart, Award, Bookmark, ArrowRight, RefreshCw
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { applicationAPI } from '@/services/api';
import { toast } from 'sonner';
import HeaderStyleFooter from '@/components/HeaderStyleFooter';

interface Application {
  id: string;
  application_num: string;
  application_info: {
    id: number;
    program?: {
      id: number;
      program_code?: string;
      program_name: string;
      registration_fee?: number;
    };
    college?: {
      id: number;
      name: string;
    };
    department?: {
      id: number;
      name: string;
    };
    field_of_study?: {
      id: number;
      name: string;
    };
    study_program?: {
      id: number;
      name: string;
    };
    admission_type?: {
      id: number;
      name: string;
    };
    // Legacy fields for backward compatibility
    program_name?: string;
    application_fee?: number;
    college_name?: string;
    department_name?: string;
  };
  registrar_off_status: string;
  department_status: string;
  payment_status: string;
  created_at: string;
  updated_at: string;
  progress: number;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  date: string;
  read: boolean;
  type: 'info' | 'success' | 'warning' | 'error';
}

const Dashboard = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const userData = localStorage.getItem('user');
  const user = userData ? JSON.parse(userData) : null;

  useEffect(() => {
    const fetchApplications = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch program selections
        const programResponse = await applicationAPI.getCurrentProgramSelection();

        if (programResponse.data && programResponse.data.length > 0) {
          // Transform the data to match our Application interface
          const transformedApplications = programResponse.data.map((app: any) => ({
            id: app.id,
            application_num: app.application_num,
            application_info: app.application_info,
            registrar_off_status: app.registrar_off_status,
            department_status: app.department_status,
            payment_status: app.payment_status,
            created_at: app.created_at,
            updated_at: app.updated_at,
            // Calculate progress based on statuses
            progress: calculateProgress(app)
          }));

          setApplications(transformedApplications);

          // Generate notifications based on application status
          const newNotifications = generateNotifications(transformedApplications);
          setNotifications(newNotifications);
        }
      } catch (err) {
        console.error('Error fetching applications:', err);
        setError('Failed to load your applications. Please try again later.');
        toast.error('Failed to load your applications');
      } finally {
        setLoading(false);
      }
    };

    fetchApplications();
  }, []);

  // Calculate application progress based on statuses
  const calculateProgress = (application: any): number => {
    let progress = 0;

    // Application submitted - 25%
    progress += 25;

    // Payment status - 25%
    if (application.payment_status === 'Verified') {
      progress += 25;
    }

    // Registrar status - 25%
    if (application.registrar_off_status === 'Approved') {
      progress += 25;
    } else if (application.registrar_off_status === 'Rejected') {
      progress += 0;
    }

    // Department status - 25%
    if (application.department_status === 'Approved') {
      progress += 25;
    } else if (application.department_status === 'Rejected') {
      progress += 0;
    }

    return progress;
  };

  // Generate notifications based on application status
  const generateNotifications = (applications: Application[]): Notification[] => {
    const notifications: Notification[] = [];

    applications.forEach(app => {
      // Application submission notification
      notifications.push({
        id: `app-${app.id}-submission`,
        title: 'Application Submitted',
        message: `Your application for ${app.application_info?.program?.program_name || app.application_info?.program_name || 'a program'} has been successfully submitted.`,
        date: app.created_at,
        read: true,
        type: 'info'
      });

      // Payment status notification
      if (app.payment_status === 'Verified') {
        notifications.push({
          id: `app-${app.id}-payment`,
          title: 'Payment Verified',
          message: `Your payment for application ${app.application_num} has been verified.`,
          date: app.updated_at,
          read: false,
          type: 'success'
        });
      } else if (app.payment_status === 'Rejected') {
        notifications.push({
          id: `app-${app.id}-payment-rejected`,
          title: 'Payment Rejected',
          message: `Your payment for application ${app.application_num} has been rejected. Please check and update your payment information.`,
          date: app.updated_at,
          read: false,
          type: 'error'
        });
      }

      // Registrar status notification
      if (app.registrar_off_status === 'Approved') {
        notifications.push({
          id: `app-${app.id}-registrar`,
          title: 'Registrar Approval',
          message: `Your application ${app.application_num} has been approved by the registrar's office.`,
          date: app.updated_at,
          read: false,
          type: 'success'
        });
      } else if (app.registrar_off_status === 'Rejected') {
        notifications.push({
          id: `app-${app.id}-registrar-rejected`,
          title: 'Registrar Rejection',
          message: `Your application ${app.application_num} has been rejected by the registrar's office.`,
          date: app.updated_at,
          read: false,
          type: 'error'
        });
      }

      // Department status notification
      if (app.department_status === 'Approved') {
        notifications.push({
          id: `app-${app.id}-department`,
          title: 'Department Approval',
          message: `Your application ${app.application_num} has been approved by the department.`,
          date: app.updated_at,
          read: false,
          type: 'success'
        });
      } else if (app.department_status === 'Rejected') {
        notifications.push({
          id: `app-${app.id}-department-rejected`,
          title: 'Department Rejection',
          message: `Your application ${app.application_num} has been rejected by the department.`,
          date: app.updated_at,
          read: false,
          type: 'error'
        });
      }
    });

    // Sort notifications by date (newest first)
    return notifications.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'Rejected':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'Verified':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'Pending':
        return <Clock className="h-5 w-5 text-amber-500" />;
      default:
        return <FileText className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusText = (status: string) => {
    return status || 'Unknown';
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'Approved':
      case 'Verified':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'Rejected':
        return 'bg-red-100 text-red-800 border-red-300';
      case 'Pending':
        return 'bg-amber-100 text-amber-800 border-amber-300';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-300';
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      default:
        return <Bell className="h-5 w-5 text-blue-500" />;
    }
  };

  const getNotificationClass = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-300 bg-green-50';
      case 'error':
        return 'border-red-300 bg-red-50';
      case 'warning':
        return 'border-amber-300 bg-amber-50';
      default:
        return 'border-blue-300 bg-blue-50';
    }
  };

  return (
    <DashboardLayout>
      {/* Welcome Section with User Info */}
      <div className="mb-8 bg-gradient-to-r from-gondar/20 to-gondar-light/10 p-8 rounded-xl shadow-md border border-gondar/10 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-gondar/5 to-gondar-light/20 rounded-full -mr-32 -mt-32 opacity-70"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-gondar/10 to-gondar-light/10 rounded-full -ml-20 -mb-20"></div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center relative z-10">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gondar">
              Welcome back, {user?.first_name || user?.firstName || 'Applicant'} {user?.last_name || user?.lastName || ''}
            </h1>
            <p className="text-gray-600 mt-3 text-lg">
              Manage your applications and track your progress at University of Gondar.
            </p>
            <div className="flex items-center mt-4 text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Today is {new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</span>
            </div>
          </div>

        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-10 w-10" />
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </div>
                  <Skeleton className="h-4 w-full mt-2" />
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-40" />
                  <Skeleton className="h-4 w-64 mt-2" />
                </CardHeader>
                <CardContent>
                  {[1, 2].map((i) => (
                    <div key={i} className="border rounded-lg p-4 mb-4">
                      <div className="flex justify-between mb-2">
                        <Skeleton className="h-5 w-40" />
                        <Skeleton className="h-5 w-20" />
                      </div>
                      <Skeleton className="h-4 w-32 mb-2" />
                      <Skeleton className="h-2 w-full mb-4" />
                      <Skeleton className="h-8 w-24" />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48 mt-2" />
              </CardHeader>
              <CardContent>
                {[1, 2, 3].map((i) => (
                  <div key={i} className="border-l-4 border-gray-200 p-3 rounded-r-lg mb-4">
                    <Skeleton className="h-5 w-32 mb-2" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Error State */}
      {!loading && error && (
        <Card className="border-red-300 mb-8">
          <CardHeader className="pb-2">
            <CardTitle className="text-red-600 flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Error Loading Dashboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Content */}
      {!loading && !error && (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
            <Card className="bg-gradient-to-br from-gondar/10 to-gondar-light/5 border-gondar/20 shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gondar/5 rounded-full -mr-16 -mt-16"></div>
              <CardHeader className="pb-2 relative z-10">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gondar text-lg">Applications</h3>
                  <div className="h-10 w-10 rounded-full bg-gondar/10 flex items-center justify-center">
                    <FileText className="h-5 w-5 text-gondar" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-4xl font-bold text-gondar">{applications.length}</span>
                    <Badge variant="outline" className="ml-2 bg-gondar/10 text-gondar border-gondar/20">
                      Total
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-3">Total applications submitted</p>

                {/* Progress indicator */}
                <div className="mt-4">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>Progress</span>
                    <span>{applications.length > 0 ? '100%' : '0%'}</span>
                  </div>
                  <Progress value={applications.length > 0 ? 100 : 0} className="h-2 bg-gondar/10" indicatorClassName="bg-gondar" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-blue-100/5 border-blue-200 shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500/5 rounded-full -mr-16 -mt-16"></div>
              <CardHeader className="pb-2 relative z-10">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-blue-600 text-lg">Notifications</h3>
                  <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <Bell className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-4xl font-bold text-blue-600">{notifications.filter(n => !n.read).length}</span>
                    <Badge variant="outline" className="ml-2 bg-blue-100 text-blue-700 border-blue-200">
                      Unread
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-3">New notifications and updates</p>

                {/* Notification indicator */}
                <div className="mt-4 flex items-center">
                  <div className="flex-1">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Read status</span>
                      <span>{notifications.length > 0 ? Math.round((notifications.filter(n => !n.read).length / notifications.length) * 100) + '%' : '0%'}</span>
                    </div>
                    <Progress
                      value={notifications.length > 0 ? (notifications.filter(n => !n.read).length / notifications.length) * 100 : 0}
                      className="h-2 bg-blue-100/50"
                      indicatorClassName="bg-blue-500"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Applications Section */}
          {applications.length > 0 && (
            <div className="mb-10">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-800">Recent Applications</h2>
                <Link to="/application/status" className="text-gondar hover:text-gondar-dark flex items-center text-sm font-medium">
                  View all applications
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>

              <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-100">
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application ID</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      {applications.slice(0, 3).map((app, index) => (
                        <tr key={index} className="hover:bg-gray-50 transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{app.application_num}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge className={
                              app.registrar_off_status === 'Approved' ? 'bg-green-100 text-green-800 border-green-200' :
                              app.registrar_off_status === 'Rejected' ? 'bg-red-100 text-red-800 border-red-200' :
                              'bg-amber-100 text-amber-800 border-amber-200'
                            }>
                              {app.registrar_off_status}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {new Date(app.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <Link to={`/application/status/${app.id}`}>
                              <Button variant="outline" size="sm" className="text-gondar hover:bg-gondar/5 border-gondar/20">
                                View Details
                              </Button>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {applications.length > 3 && (
                  <div className="px-6 py-3 bg-gray-50 border-t border-gray-100 text-center">
                    <Link to="/application/status" className="text-gondar hover:text-gondar-dark text-sm font-medium">
                      View all {applications.length} applications
                    </Link>
                  </div>
                )}
              </div>
            </div>
          )}


        </>
      )}
    </DashboardLayout>
  );
};

export default Dashboard;
