import React, { useEffect, useState } from 'react';
import { settingsAPI } from '@/services/api';

interface BrandingProviderProps {
  children: React.ReactNode;
}

const BrandingProvider: React.FC<BrandingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [primaryColor, setPrimaryColor] = useState('#1a73c0'); // Default blue
  const [secondaryColor, setSecondaryColor] = useState('#f5f5f5'); // Default light gray

  useEffect(() => {
    const fetchBrandingColors = async () => {
      // Try to get colors from localStorage first
      try {
        const storedColors = localStorage.getItem('brandingColors');
        if (storedColors) {
          const parsedColors = JSON.parse(storedColors);
          // Check if the stored value has a timestamp and it's not expired (5 minutes)
          if (parsedColors.timestamp && (Date.now() - parsedColors.timestamp < 5 * 60 * 1000)) {
            const primary = parsedColors.data.primary_color || '#1a73c0';
            const secondary = parsedColors.data.secondary_color || '#f5f5f5';

            // Update state
            setPrimaryColor(primary);
            setSecondaryColor(secondary);

            // Apply colors immediately from cache
            applyBrandingColors(primary, secondary);

            // Don't set loading to true if we have cached data
            return;
          }
        }
      } catch (error) {
        console.error('Error reading branding colors from localStorage:', error);
      }

      // If we get here, we need to fetch from API
      setIsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          // Get colors from API
          const primary = response.data.primary_color || '#1a73c0';
          const secondary = response.data.secondary_color || '#f5f5f5';

          // Update state
          setPrimaryColor(primary);
          setSecondaryColor(secondary);

          // Apply colors to CSS variables
          applyBrandingColors(primary, secondary);

          // Save to localStorage
          try {
            localStorage.setItem('brandingColors', JSON.stringify({
              data: {
                primary_color: primary,
                secondary_color: secondary
              },
              timestamp: Date.now()
            }));
          } catch (error) {
            console.error('Error saving branding colors to localStorage:', error);
          }
        }
      } catch (error) {
        console.error('Error fetching branding colors:', error);
        // Apply default colors if there's an error
        applyBrandingColors(primaryColor, secondaryColor);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBrandingColors();
  }, []);

  // Function to convert hex to HSL for Tailwind CSS variables
  const hexToHSL = (hex: string): string => {
    // Remove the # if present
    hex = hex.replace('#', '');

    // Convert hex to RGB
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;

    // Find the maximum and minimum values to calculate the lightness
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    // Calculate the lightness
    let l = (max + min) / 2;

    let h = 0;
    let s = 0;

    if (max !== min) {
      // Calculate the saturation
      s = l > 0.5 ? (max - min) / (2 - max - min) : (max - min) / (max + min);

      // Calculate the hue
      if (max === r) {
        h = (g - b) / (max - min) + (g < b ? 6 : 0);
      } else if (max === g) {
        h = (b - r) / (max - min) + 2;
      } else {
        h = (r - g) / (max - min) + 4;
      }

      h = h * 60;
    }

    // Round the values
    h = Math.round(h);
    s = Math.round(s * 100);
    l = Math.round(l * 100);

    return `${h} ${s}% ${l}%`;
  };

  // Apply branding colors to CSS variables
  const applyBrandingColors = (primary: string, secondary: string) => {
    // Convert hex colors to HSL for CSS variables
    const primaryHSL = hexToHSL(primary);
    const secondaryHSL = hexToHSL(secondary);

    // Calculate contrasting text colors
    const primaryIsDark = isColorDark(primary);
    const secondaryIsDark = isColorDark(secondary);

    // Set primary foreground color based on contrast
    const primaryForeground = primaryIsDark ? '210 40% 98%' : '222.2 47.4% 11.2%';

    // Set secondary foreground color based on contrast
    const secondaryForeground = secondaryIsDark ? '210 40% 98%' : '222.2 47.4% 11.2%';

    // Apply to CSS variables
    document.documentElement.style.setProperty('--primary', primaryHSL);
    document.documentElement.style.setProperty('--primary-foreground', primaryForeground);
    document.documentElement.style.setProperty('--secondary', secondaryHSL);
    document.documentElement.style.setProperty('--secondary-foreground', secondaryForeground);
    document.documentElement.style.setProperty('--ring', primaryHSL);

    // Also set some direct CSS variables for components that don't use the HSL format
    document.documentElement.style.setProperty('--brand-primary', primary);
    document.documentElement.style.setProperty('--brand-secondary', secondary);
    document.documentElement.style.setProperty('--brand-primary-dark', getDarkerShade(primary));
    document.documentElement.style.setProperty('--brand-primary-light', getLighterShade(primary));
    document.documentElement.style.setProperty('--brand-secondary-dark', getDarkerShade(secondary));
    document.documentElement.style.setProperty('--brand-secondary-light', getLighterShade(secondary));

    // Update sidebar colors
    document.documentElement.style.setProperty('--sidebar-primary', primaryHSL);
    document.documentElement.style.setProperty('--sidebar-ring', primaryHSL);

    // Update scrollbar colors to default
    updateScrollbarColors();

    // Update back-to-top button colors
    updateBackToTopColors(primary);

    // Update header and footer colors
    updateHeaderFooterColors(primary);

    // Update custom Tailwind classes
    updateCustomTailwindClasses(primary, secondary);

    // Update heading styles to use secondary color
    updateHeadingStyles(secondary);
  };

  // Check if a color is dark (to determine appropriate text color)
  const isColorDark = (hex: string): boolean => {
    // Remove the # if present
    hex = hex.replace('#', '');

    // Convert hex to RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Calculate relative luminance
    // Formula: 0.299*R + 0.587*G + 0.114*B
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // If luminance is less than 0.5, color is considered dark
    return luminance < 0.5;
  };

  // Update scrollbar colors to default
  const updateScrollbarColors = () => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.id = 'custom-scrollbar-styles';

    // Remove any existing style element with the same ID
    const existingStyle = document.getElementById('custom-scrollbar-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Set the CSS content to use default scrollbar colors
    styleElement.textContent = `
      /* Custom scrollbar styles */
      ::-webkit-scrollbar-thumb {
        background: #888 !important;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #555 !important;
      }

      /* Firefox */
      * {
        scrollbar-color: #888 #f1f1f1 !important;
      }
    `;

    // Append to document head
    document.head.appendChild(styleElement);
  };

  // Update back-to-top button colors
  const updateBackToTopColors = (primaryColor: string) => {
    // Calculate a darker shade for hover state
    const darkerShade = getDarkerShade(primaryColor);

    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.id = 'back-to-top-styles';

    // Remove any existing style element with the same ID
    const existingStyle = document.getElementById('back-to-top-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Set the CSS content
    styleElement.textContent = `
      /* Back to top button styles */
      .back-to-top__button {
        background: ${primaryColor} !important;
        background-image: linear-gradient(to right, ${primaryColor}, ${getLighterShade(primaryColor)}) !important;
        border-color: ${getLighterShade(primaryColor, 0.3)} !important;
      }

      .back-to-top__button:hover {
        background: ${darkerShade} !important;
        background-image: linear-gradient(to right, ${darkerShade}, ${primaryColor}) !important;
      }

      @keyframes shimmer {
        0% { box-shadow: 0 0 5px ${primaryColor}4D; }
        50% { box-shadow: 0 0 15px ${primaryColor}80; }
        100% { box-shadow: 0 0 5px ${primaryColor}4D; }
      }
    `;

    // Append to document head
    document.head.appendChild(styleElement);
  };

  // Get a darker shade of a color
  const getDarkerShade = (hex: string, amount: number = 0.2): string => {
    // Remove the # if present
    hex = hex.replace('#', '');

    // Convert hex to RGB
    let r = parseInt(hex.substring(0, 2), 16);
    let g = parseInt(hex.substring(2, 4), 16);
    let b = parseInt(hex.substring(4, 6), 16);

    // Darken the color
    r = Math.max(0, Math.floor(r * (1 - amount)));
    g = Math.max(0, Math.floor(g * (1 - amount)));
    b = Math.max(0, Math.floor(b * (1 - amount)));

    // Convert back to hex
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  };

  // Get a lighter shade of a color
  const getLighterShade = (hex: string, amount: number = 0.2): string => {
    // Remove the # if present
    hex = hex.replace('#', '');

    // Convert hex to RGB
    let r = parseInt(hex.substring(0, 2), 16);
    let g = parseInt(hex.substring(2, 4), 16);
    let b = parseInt(hex.substring(4, 6), 16);

    // Lighten the color
    r = Math.min(255, Math.floor(r + (255 - r) * amount));
    g = Math.min(255, Math.floor(g + (255 - g) * amount));
    b = Math.min(255, Math.floor(b + (255 - b) * amount));

    // Convert back to hex
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  };

  // Update header and footer colors
  const updateHeaderFooterColors = (primaryColor: string) => {
    // Calculate a darker shade for hover state
    const darkerShade = getDarkerShade(primaryColor);

    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.id = 'header-footer-styles';

    // Remove any existing style element with the same ID
    const existingStyle = document.getElementById('header-footer-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Set the CSS content
    styleElement.textContent = `
      /* Header styles */
      header.bg-\\[\\#1a73c0\\] {
        background-color: ${primaryColor} !important;
      }

      /* Header hover states */
      .hover\\:bg-\\[\\#0e4a7d\\]:hover {
        background-color: ${darkerShade} !important;
      }

      /* Active navigation items */
      .bg-\\[\\#0e4a7d\\] {
        background-color: ${darkerShade} !important;
      }

      /* Footer styles */
      footer.bg-\\[\\#1a73c0\\] {
        background-color: ${primaryColor} !important;
      }

      /* Footer border color */
      .border-blue-400 {
        border-color: ${getLighterShade(primaryColor)} !important;
      }

      /* Dashboard header */
      header.bg-\\[\\#1a73c0\\] {
        background-color: ${primaryColor} !important;
      }
    `;

    // Append to document head
    document.head.appendChild(styleElement);
  };

  // Update heading styles to use secondary color
  const updateHeadingStyles = (secondaryColor: string) => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.id = 'heading-styles';

    // Remove any existing style element with the same ID
    const existingStyle = document.getElementById('heading-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Set the CSS content
    styleElement.textContent = `
      /* Apply secondary color to all headings except those in hero sections */
      h1:not([class*="text-white"]),
      h2:not([class*="text-white"]),
      h3:not([class*="text-white"]),
      h4:not([class*="text-white"]),
      h5:not([class*="text-white"]),
      h6:not([class*="text-white"]) {
        color: ${secondaryColor} !important;
      }

      /* Apply to specific title classes */
      .card-title:not([class*="text-white"]) {
        color: ${secondaryColor} !important;
      }

      /* Apply to section titles */
      section h2:not([class*="text-white"]) {
        color: ${secondaryColor} !important;
      }

      /* Apply to form titles */
      .form-step-title:not([class*="text-white"]) {
        color: ${secondaryColor} !important;
      }

      /* Exclude hero section headings */
      section[class*="relative py-4 md:py-6 h-[175px]"] h1,
      section[class*="relative py-8 md:py-12 h-[350px]"] h1,
      section[class*="overflow-hidden"] h1[class*="text-white"],
      section[class*="overflow-hidden"] p[class*="text-white"] {
        color: white !important;
      }
    `;

    // Append to document head
    document.head.appendChild(styleElement);
  };

  // Update custom Tailwind classes that use hardcoded colors
  const updateCustomTailwindClasses = (primaryColor: string, secondaryColor: string) => {
    // Create a style element
    const styleElement = document.createElement('style');
    styleElement.id = 'custom-tailwind-styles';

    // Remove any existing style element with the same ID
    const existingStyle = document.getElementById('custom-tailwind-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Calculate darker and lighter shades
    const primaryDark = getDarkerShade(primaryColor);
    const primaryLight = getLighterShade(primaryColor);

    // Set the CSS content to override Tailwind classes
    styleElement.textContent = `
      /* Override Tailwind classes with custom colors */
      .bg-gondar {
        background-color: ${primaryColor} !important;
      }
      .bg-gondar-dark {
        background-color: ${primaryDark} !important;
      }
      .bg-gondar-light {
        background-color: ${primaryLight} !important;
      }
      .text-gondar {
        color: ${primaryColor} !important;
      }
      .text-gondar-dark {
        color: ${primaryDark} !important;
      }
      .text-gondar-light {
        color: ${primaryLight} !important;
      }
      .border-gondar {
        border-color: ${primaryColor} !important;
      }
      .border-gondar-light {
        border-color: ${primaryLight} !important;
      }
      .hover\\:bg-gondar:hover {
        background-color: ${primaryColor} !important;
      }
      .hover\\:bg-gondar-dark:hover {
        background-color: ${primaryDark} !important;
      }
      .hover\\:text-gondar:hover {
        color: ${primaryColor} !important;
      }
      .hover\\:border-gondar:hover {
        border-color: ${primaryColor} !important;
      }
      .hover\\:border-gondar-light\\/50:hover {
        border-color: ${primaryLight}80 !important;
      }
      .from-gondar {
        --tw-gradient-from: ${primaryColor} !important;
      }
      .to-gondar-accent {
        --tw-gradient-to: ${primaryLight} !important;
      }
      .bg-gondar-accent {
        background-color: #FFD700 !important;
      }
      .bg-gradient-to-r.from-gondar.to-gondar-accent {
        background-image: linear-gradient(to right, ${primaryColor}, #FFD700) !important;
      }
      .focus\\:ring-gondar:focus {
        --tw-ring-color: ${primaryColor} !important;
      }
      .form-progress-bar {
        background-color: ${primaryColor} !important;
      }
      .flow-number {
        background-color: ${primaryColor} !important;
      }

      /* Dashboard menu items */
      .bg-\\[\\#1a73c0\\]\\/10 {
        background-color: ${primaryColor}1A !important;
      }
      .text-\\[\\#1a73c0\\] {
        color: ${primaryColor} !important;
      }
      .border-\\[\\#1a73c0\\] {
        border-color: ${primaryColor} !important;
      }
      .hover\\:bg-\\[\\#1a73c0\\]\\/10:hover {
        background-color: ${primaryColor}1A !important;
      }
      .hover\\:text-\\[\\#1a73c0\\]:hover {
        color: ${primaryColor} !important;
      }
      .group-hover\\:text-\\[\\#1a73c0\\] {
        color: ${primaryColor} !important;
      }
    `;

    // Append to document head
    document.head.appendChild(styleElement);
  };

  return (
    <>
      {children}
    </>
  );
};

export default BrandingProvider;
