import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Bookmark,
  Plus,
  Trash2,
  Edit3,
  Clock,
  Share2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

export interface SavedFilter {
  id: string
  name: string
  description?: string
  years: string[]
  colleges: string[]
  departments?: string[]
  createdAt: Date
  lastUsed?: Date
}

interface SavedFiltersProps {
  savedFilters: SavedFilter[]
  currentYears: string[]
  currentColleges: string[]
  currentDepartments?: string[]
  availableYears: Array<{ label: string; value: string }>
  availableColleges: Array<{ label: string; value: string }>
  availableDepartments?: Array<{ label: string; value: string }>
  onSaveFilter: (filter: Omit<SavedFilter, 'id' | 'createdAt'>) => void
  onLoadFilter: (filter: SavedFilter) => void
  onDeleteFilter: (filterId: string) => void
  onShareFilter: (filter: SavedFilter) => void
  className?: string
}

export function SavedFilters({
  savedFilters,
  currentYears,
  currentColleges,
  currentDepartments = [],
  availableYears,
  availableColleges,
  availableDepartments = [],
  onSaveFilter,
  onLoadFilter,
  onDeleteFilter,
  onShareFilter,
  className
}: SavedFiltersProps) {
  const [isDialogOpen, setIsDialogOpen] = React.useState(false)
  const [filterName, setFilterName] = React.useState("")
  const [filterDescription, setFilterDescription] = React.useState("")

  const hasActiveFilters = currentYears.length > 0 || currentColleges.length > 0 || currentDepartments.length > 0

  const handleSaveFilter = () => {
    const trimmedName = filterName.trim();
    const trimmedDescription = filterDescription.trim();

    // Enhanced input validation
    if (!trimmedName) {
      toast.error("Please enter a filter name");
      return;
    }

    if (trimmedName.length < 3) {
      toast.error("Filter name must be at least 3 characters long");
      return;
    }

    if (trimmedName.length > 50) {
      toast.error("Filter name must be less than 50 characters");
      return;
    }

    if (trimmedDescription && trimmedDescription.length > 200) {
      toast.error("Description must be less than 200 characters");
      return;
    }

    // Check for duplicate names
    const isDuplicate = savedFilters.some(filter =>
      filter.name.toLowerCase() === trimmedName.toLowerCase()
    );

    if (isDuplicate) {
      toast.error("A filter with this name already exists");
      return;
    }

    if (!hasActiveFilters) {
      toast.error("No active filters to save");
      return;
    }

    // Validate filter content
    const totalFilters = currentYears.length + currentColleges.length + currentDepartments.length;
    if (totalFilters === 0) {
      toast.error("Please select at least one filter option");
      return;
    }

    onSaveFilter({
      name: trimmedName,
      description: trimmedDescription || undefined,
      years: currentYears,
      colleges: currentColleges,
      departments: currentDepartments,
      lastUsed: new Date()
    });

    setFilterName("");
    setFilterDescription("");
    setIsDialogOpen(false);
    toast.success("Filter saved successfully");
  }

  const getFilterSummary = (filter: SavedFilter) => {
    const parts = []

    if (filter.years.length > 0) {
      const yearLabels = filter.years.map(y =>
        availableYears.find(ay => ay.value === y)?.label || y
      )
      parts.push(`Years: ${yearLabels.join(", ")}`)
    }

    if (filter.colleges.length > 0) {
      const collegeLabels = filter.colleges.map(c =>
        availableColleges.find(ac => ac.value === c)?.label || c
      )
      parts.push(`Colleges: ${collegeLabels.join(", ")}`)
    }

    if (filter.departments && filter.departments.length > 0) {
      const departmentLabels = filter.departments.map(d =>
        availableDepartments.find(ad => ad.value === d)?.label || d
      )
      parts.push(`Departments: ${departmentLabels.join(", ")}`)
    }

    return parts.join(" | ")
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date)
  }

  return (
    <Card className={cn("border-0 shadow-lg", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
            <Bookmark className="h-5 w-5 mr-2 text-blue-500" />
            Saved Filters
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button 
                size="sm" 
                disabled={!hasActiveFilters}
                className="flex items-center space-x-1"
              >
                <Plus className="h-4 w-4" />
                <span>Save Current</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Save Filter</DialogTitle>
                <DialogDescription>
                  Save your current filter settings for quick access later.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label htmlFor="filter-name" className="text-sm font-medium text-gray-700">Filter Name</label>
                  <Input
                    id="filter-name"
                    name="filter-name"
                    placeholder="e.g., Recent Engineering Graduates"
                    value={filterName}
                    onChange={(e) => setFilterName(e.target.value)}
                    className="mt-1"
                    required
                    aria-describedby="filter-name-help"
                  />
                  <p id="filter-name-help" className="text-xs text-gray-500 mt-1">
                    Enter a descriptive name for this filter (3-50 characters)
                  </p>
                </div>
                <div>
                  <label htmlFor="filter-description" className="text-sm font-medium text-gray-700">Description (Optional)</label>
                  <Input
                    id="filter-description"
                    name="filter-description"
                    placeholder="Brief description of this filter"
                    value={filterDescription}
                    onChange={(e) => setFilterDescription(e.target.value)}
                    className="mt-1"
                    aria-describedby="filter-description-help"
                  />
                  <p id="filter-description-help" className="text-xs text-gray-500 mt-1">
                    Optional description (max 200 characters)
                  </p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-700 mb-2">Current Filters:</p>
                  <div className="space-y-1">
                    {currentYears.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-gray-600">Years:</span>
                        {currentYears.map(year => (
                          <Badge key={year} variant="secondary" className="text-xs">
                            {availableYears.find(y => y.value === year)?.label || year}
                          </Badge>
                        ))}
                      </div>
                    )}
                    {currentColleges.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-gray-600">Colleges:</span>
                        {currentColleges.map(college => (
                          <Badge key={college} variant="secondary" className="text-xs">
                            {availableColleges.find(c => c.value === college)?.label || college}
                          </Badge>
                        ))}
                      </div>
                    )}
                    {currentDepartments.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-gray-600">Departments:</span>
                        {currentDepartments.map(department => (
                          <Badge key={department} variant="secondary" className="text-xs">
                            {availableDepartments.find(d => d.value === department)?.label || department}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveFilter}>
                  Save Filter
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {savedFilters.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Bookmark className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p className="text-sm">No saved filters yet</p>
            <p className="text-xs">Apply some filters and save them for quick access</p>
          </div>
        ) : (
          <div className="space-y-3">
            {savedFilters.map((filter) => (
              <div
                key={filter.id}
                className="p-3 border rounded-lg hover:shadow-md transition-all duration-200 group"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-sm text-gray-900 truncate">
                        {filter.name}
                      </h4>
                      {filter.lastUsed && (
                        <Badge variant="outline" className="text-xs">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(filter.lastUsed)}
                        </Badge>
                      )}
                    </div>
                    {filter.description && (
                      <p className="text-xs text-gray-600 mb-2">{filter.description}</p>
                    )}
                    <p className="text-xs text-gray-500 truncate">
                      {getFilterSummary(filter)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onLoadFilter(filter)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onShareFilter(filter)}
                      className="h-8 w-8 p-0"
                    >
                      <Share2 className="h-3 w-3" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Saved Filter</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete the filter "{filter.name}"?
                            This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => onDeleteFilter(filter.id)}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
