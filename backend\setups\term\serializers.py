from rest_framework import serializers
from .models import Term


class TermSerializer(serializers.ModelSerializer):
    """Serializer for Term model"""

    class Meta:
        model = Term
        fields = ['id', 'name', 'description', 'updated_at']
        read_only_fields = ['id', 'updated_at']

    def validate_name(self, value):
        """Validate term name"""
        if not value or not value.strip():
            raise serializers.ValidationError("Term name cannot be empty.")

        # Check for uniqueness (excluding current instance during updates)
        queryset = Term.objects.filter(name__iexact=value.strip())
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)

        if queryset.exists():
            raise serializers.ValidationError("A term with this name already exists.")

        return value.strip()

    def validate(self, attrs):
        """Perform object-level validation"""
        # Ensure description is properly handled
        if 'description' in attrs and attrs['description'] is not None:
            attrs['description'] = attrs['description'].strip()
            if not attrs['description']:
                attrs['description'] = None

        return attrs
