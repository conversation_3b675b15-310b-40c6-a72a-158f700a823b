from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Clear rate limiting cache to resolve rate limit exceeded errors'

    def add_arguments(self, parser):
        parser.add_argument(
            '--ip',
            type=str,
            help='Clear rate limits for specific IP address',
        )
        parser.add_argument(
            '--category',
            type=str,
            help='Clear rate limits for specific category (default, auth, api, admin, etc.)',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Clear all rate limiting cache entries',
        )

    def handle(self, *args, **options):
        ip_address = options.get('ip')
        category = options.get('category')
        clear_all = options.get('all')

        if clear_all:
            self.clear_all_rate_limits()
        elif ip_address and category:
            self.clear_specific_rate_limit(ip_address, category)
        elif ip_address:
            self.clear_ip_rate_limits(ip_address)
        elif category:
            self.clear_category_rate_limits(category)
        else:
            self.stdout.write(
                self.style.WARNING(
                    'No specific options provided. Use --all to clear all rate limits, '
                    '--ip <ip_address> to clear for specific IP, or --category <category> '
                    'to clear for specific category.'
                )
            )
            return

        self.stdout.write(
            self.style.SUCCESS('Rate limit cache cleared successfully!')
        )

    def clear_all_rate_limits(self):
        """Clear all rate limiting cache entries"""
        try:
            # Get all cache keys that start with 'rate_limit:'
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'keys'):
                # For Redis cache
                keys = [key for key in cache._cache.keys() if key.startswith('rate_limit:')]
            else:
                # For other cache backends, we'll clear common patterns
                categories = ['default', 'auth', 'api', 'admin', 'upload', 'token_refresh']
                keys = []
                
                # Generate common IP patterns (localhost and common private IPs)
                common_ips = ['127.0.0.1', '::1', '***********', '********']
                
                for category in categories:
                    for ip in common_ips:
                        keys.append(f'rate_limit:{category}:{ip}')

            # Delete the keys
            if keys:
                cache.delete_many(keys)
                self.stdout.write(f'Cleared {len(keys)} rate limit cache entries')
            else:
                self.stdout.write('No rate limit cache entries found to clear')

        except Exception as e:
            logger.error(f'Error clearing rate limit cache: {e}')
            self.stdout.write(
                self.style.ERROR(f'Error clearing rate limit cache: {e}')
            )

    def clear_specific_rate_limit(self, ip_address, category):
        """Clear rate limit for specific IP and category"""
        cache_key = f"rate_limit:{category}:{ip_address}"
        cache.delete(cache_key)
        self.stdout.write(f'Cleared rate limit for IP {ip_address} in category {category}')

    def clear_ip_rate_limits(self, ip_address):
        """Clear all rate limits for specific IP"""
        categories = ['default', 'auth', 'api', 'admin', 'upload', 'token_refresh']
        keys = [f"rate_limit:{category}:{ip_address}" for category in categories]
        cache.delete_many(keys)
        self.stdout.write(f'Cleared all rate limits for IP {ip_address}')

    def clear_category_rate_limits(self, category):
        """Clear rate limits for specific category (all IPs)"""
        try:
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'keys'):
                # For Redis cache
                keys = [key for key in cache._cache.keys() if key.startswith(f'rate_limit:{category}:')]
            else:
                # For other cache backends, clear common IP patterns
                common_ips = ['127.0.0.1', '::1', '***********', '********']
                keys = [f"rate_limit:{category}:{ip}" for ip in common_ips]

            if keys:
                cache.delete_many(keys)
                self.stdout.write(f'Cleared {len(keys)} rate limit entries for category {category}')
            else:
                self.stdout.write(f'No rate limit entries found for category {category}')

        except Exception as e:
            logger.error(f'Error clearing category rate limits: {e}')
            self.stdout.write(
                self.style.ERROR(f'Error clearing category rate limits: {e}')
            )
