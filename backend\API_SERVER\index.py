# from service import applyFabricToken
# from config import env
# enviroment = env.ENV_VARIABLES
# applyFabricTokenService =applyFabricToken.ApplyFabricToken(enviroment["baseUrl"],enviroment['fabricAppId'],enviroment['appSecret'],enviroment['merchantAppId'])
# print(applyFabricTokenService.applyFabricToken())

# Python 3 server example
from http.server import BaseHTTPRequestHandler, HTTPServer
import time
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

hostName = os.getenv('API_SERVER_HOST', 'localhost')
serverPort = int(os.getenv('API_SERVER_PORT', '8080'))

class MyServer(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header("Content-type", "text/html")
        self.end_headers()
        self.wfile.write(bytes("<html><head><title>https://pythonbasics.org</title></head>", "utf-8"))
        self.wfile.write(bytes("<p>Request: %s</p>" % self.path, "utf-8"))
        self.wfile.write(bytes("<body>", "utf-8"))
        self.wfile.write(bytes("<p>This is an example web server.</p>", "utf-8"))
        self.wfile.write(bytes("</body></html>", "utf-8"))

if __name__ == "__main__":
    webServer = HTTPServer((hostName, serverPort), MyServer)
    print("Server started http://%s:%s" % (hostName, serverPort))
    try:
        webServer.serve_forever()
    except KeyboardInterrupt:
        pass

    webServer.server_close()
    print("Server stopped.")