#!/usr/bin/env python
"""
Create a test service type for frontend testing.
"""
import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.service_type.models import ServiceType
from setups.certificate_type.models import CertificateType
from decimal import Decimal

def create_test_service_type():
    # Create a test service type
    service_type, created = ServiceType.objects.get_or_create(
        name='Test Academic Verification',
        defaults={
            'fee': Decimal('25.00'),
            'is_active': True
        }
    )
    
    if created:
        print(f'✅ Created service type: {service_type.name} with fee ${service_type.fee}')
    else:
        print(f'📋 Service type already exists: {service_type.name}')
    
    # Add some certificate types if they exist
    cert_types = CertificateType.objects.filter(is_active=True)[:2]
    if cert_types:
        service_type.document_types.add(*cert_types)
        print(f'✅ Associated {len(cert_types)} document types')
    
    print(f'📊 Total document types: {service_type.document_types_count}')
    print(f'📊 Active document types: {service_type.active_document_types_count}')
    
    return service_type

if __name__ == '__main__':
    create_test_service_type()
