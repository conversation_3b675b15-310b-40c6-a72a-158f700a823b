import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Search, Filter, BookOpen, School, Building, GraduationCap, Info, Clock, Briefcase, FileText, AlertCircle, Calendar } from 'lucide-react';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'sonner';
import { applicationAPI, yearAPI, termAPI } from '@/services/api';
import programAP<PERSON> from '@/services/programService';

// Define the schema for the form
const programSelectionSchema = z.object({
  program_id: z.string().min(1, 'Please select a program'),
  sponsorship: z.string().min(1, 'Please select a sponsorship type').nonempty('Sponsorship is required'),
  year: z.string().optional(), // Made optional since it can be auto-filled from registration period
  term: z.string().optional(), // Made optional since it can be auto-filled from registration period
});

type ProgramSelectionFormValues = z.infer<typeof programSelectionSchema>;

// Define the filter schema
const filterSchema = z.object({
  college: z.string(),
  department: z.string(),
  program: z.string(),
  admission_type: z.string(),
});

type FilterFormValues = z.infer<typeof filterSchema>;

// Interface matching the ApplicationInformation model from the backend
interface ApplicationInfo {
  id: number;
  college: { id: number; name: string };
  department: { id: number; name: string };
  program: { id: number; program_code?: string; program_name: string };
  field_of_study: { id: number; name: string };
  study_program: { id: number; name: string };
  admission_type: { id: number; name: string };
  spacial_case?: string;
  duration?: string;
  status: boolean;
  created_at?: string;
  updated_at?: string;
  registration_active?: boolean;
  registration_message?: string;
}

const ProgramSelection = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingExistingData, setIsLoadingExistingData] = useState(false);
  const [existingProgramSelection, setExistingProgramSelection] = useState<any>(null);
  const [colleges, setColleges] = useState<Array<{id: number, name: string}>>([]);
  const [departments, setDepartments] = useState<Array<{id: number, name: string}>>([]);
  const [programs, setPrograms] = useState<Array<{id: number, program_code: string, program_name: string}>>([]);
  const [admissionTypes, setAdmissionTypes] = useState<Array<{id: number, name: string, description: string}>>([]);
  const [applicationInfos, setApplicationInfos] = useState<ApplicationInfo[]>([]);
  const [selectedProgram, setSelectedProgram] = useState<ApplicationInfo | null>(null);
  const [currentGAT, setCurrentGAT] = useState<any>(null);
  const [years, setYears] = useState<Array<{uuid: string, year: string}>>([]);
  const [terms, setTerms] = useState<Array<{id: string, name: string}>>([]);
  const [registrationPeriods, setRegistrationPeriods] = useState<Array<{id: number, program: number, term_name?: string, year_name?: string}>>([]);
  // State to track which college sections are expanded
  const [expandedColleges, setExpandedColleges] = useState<Record<string, boolean>>({});
  const navigate = useNavigate();

  // Check if user has a registered ApplicationGat
  useEffect(() => {
    const checkGatRegistration = async () => {
      try {
        const response = await applicationAPI.getCurrentGAT();
        if (!response.data || response.data.length === 0) {
          // User doesn't have a registered ApplicationGat
          // Silently redirect to the GAT page without showing a toast notification
          navigate('/application/gat');
        }
      } catch (error) {
        console.error('Error checking GAT registration:', error);
        // Silently redirect to the GAT page without showing a toast notification
        navigate('/application/gat');
      }
    };

    checkGatRegistration();
  }, [navigate]);

  // Sponsorship options based on the model
  const sponsorshipOptions = [
    { value: 'Ministry of Education', label: 'Ministry of Education' },
    { value: 'Other Government Office', label: 'Other Government Office' },
    { value: 'University of Gondar', label: 'University of Gondar' },
    { value: 'Private Office ', label: 'Private Office' }, // Note the space after "Office"
    { value: 'Self', label: 'Self' },
  ];

  // Initialize program selection form
  const form = useForm<ProgramSelectionFormValues>({
    resolver: zodResolver(programSelectionSchema),
    defaultValues: {
      program_id: '',
      sponsorship: 'Self', // Default to 'Self' to ensure it's always set
      year: '',
      term: '',
    },
  });

  // Initialize filter form
  const filterForm = useForm<FilterFormValues>({
    resolver: zodResolver(filterSchema),
    defaultValues: {
      college: '0',
      department: '0',
      program: '0',
      admission_type: '0',
    },
  });

  // We're now using the native form reset functionality
  // This function is kept for reference but is no longer used

  // We don't set a default sponsorship value anymore since it's required
  // and the user must explicitly select one

  // Fetch current GAT information
  useEffect(() => {
    const fetchCurrentGAT = async () => {
      try {
        const response = await applicationAPI.getCurrentGAT();
        console.log('Fetched current GAT:', response.data);
        if (response.data && response.data.length > 0) {
          // Get the most recent GAT record
          const latestGAT = response.data[0];
          setCurrentGAT(latestGAT);
          console.log('Current GAT set to:', latestGAT);
        } else {
          console.log('No GAT records found');
        }
      } catch (error) {
        console.error('Error fetching current GAT:', error);
      }
    };

    fetchCurrentGAT();
  }, []);

  // Check URL parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isNewApplication = queryParams.get('new') === 'true';
  const isEditing = queryParams.get('edit') === 'true';
  const gatIdFromUrl = queryParams.get('gat');

  // State to track if a program selection exists for the current GAT
  const [programSelectionExists, setProgramSelectionExists] = useState<boolean>(false);

  // State to store the GAT details when editing a specific GAT
  const [editingGAT, setEditingGAT] = useState<any>(null);

  // Fetch existing program selection if available
  useEffect(() => {
    const fetchExistingProgramSelection = async () => {
      setIsLoadingExistingData(true);
      try {
        // If this is a new application, don't load any existing program selection
        if (isNewApplication) {
          console.log('New application detected, skipping loading of existing program selection');
          setProgramSelectionExists(false);
          setExistingProgramSelection(null); // Clear any existing data
          setSelectedProgram(null); // Clear any selected program
          form.reset(); // Reset the form
          setIsLoadingExistingData(false);
          return;
        }

        // Determine which GAT ID to use
        let gatIdToUse: string | number | null = null;

        // If we're editing a specific GAT from the URL, prioritize that
        if (isEditing && gatIdFromUrl) {
          gatIdToUse = gatIdFromUrl;
          console.log(`Using GAT ID from URL: ${gatIdToUse}`);

          // Clear any existing data when editing a specific GAT
          setExistingProgramSelection(null);
          setSelectedProgram(null);
          form.reset();

          // Fetch the GAT details for display
          try {
            const gatResponse = await applicationAPI.getGAT(gatIdFromUrl);
            if (gatResponse.data) {
              console.log('Fetched GAT details:', gatResponse.data);
              setEditingGAT(gatResponse.data);
            }
          } catch (error) {
            console.error(`Error fetching GAT details for ID ${gatIdFromUrl}:`, error);
            setEditingGAT(null);
          }
        }
        // Otherwise use the current GAT if available
        else if (currentGAT?.id) {
          gatIdToUse = currentGAT.id;
          console.log(`Using current GAT ID: ${gatIdToUse}`);
        }

        // If we have a GAT ID to use, check if a program selection exists for it
        if (gatIdToUse) {
          console.log(`Fetching program selection for GAT ID: ${gatIdToUse}`);
          try {
            // Use the GAT ID to fetch the program selection
            const response = await applicationAPI.getProgramSelectionByGAT(Number(gatIdToUse));
            console.log('Fetched program selection by GAT ID:', response.data);

            if (response.data && response.data.length > 0) {
              // Program selection exists for this GAT
              setProgramSelectionExists(true);
              const programSelection = response.data[0];
              setExistingProgramSelection(programSelection);
              console.log('Existing program selection by GAT ID:', programSelection);
              console.log('Existing sponsorship value:', programSelection.sponsorship);

              // Set the selected program based on the existing selection
              if (programSelection.application_info) {
                // Fetch the application info details
                try {
                  const appInfoResponse = await applicationAPI.getApplicationInfo(programSelection.application_info);
                  if (appInfoResponse.data) {
                    setSelectedProgram(appInfoResponse.data);
                    console.log('Selected program set from existing data:', appInfoResponse.data);

                    // Update the form with the existing program ID and sponsorship
                    const sponsorshipValue = programSelection.sponsorship || '';
                    console.log('Setting sponsorship value in form:', sponsorshipValue);

                    // First set the program_id
                    form.setValue('program_id', programSelection.application_info.toString());

                    // Then explicitly set the sponsorship value if it exists
                    if (sponsorshipValue) {
                      form.setValue('sponsorship', sponsorshipValue);
                    }

                    // Set the year value if it exists
                    if (programSelection.year) {
                      form.setValue('year', programSelection.year);
                    }

                    // Set the term value if it exists
                    if (programSelection.term) {
                      form.setValue('term', programSelection.term);
                    }

                    console.log('Form values after setting:', {
                      program_id: form.getValues('program_id'),
                      sponsorship: form.getValues('sponsorship'),
                      year: form.getValues('year'),
                      term: form.getValues('term')
                    });
                  }
                } catch (error) {
                  console.error('Error fetching application info details:', error);
                }
              }
            } else {
              // No program selection exists for this GAT
              setProgramSelectionExists(false);
              setExistingProgramSelection(null);
              console.log(`No program selection found for GAT ID: ${gatIdToUse}`);

              // If we're editing a specific GAT that doesn't have a program selection,
              // make sure the form is reset
              if (isEditing && gatIdFromUrl) {
                form.reset();
                setSelectedProgram(null);
              }
            }
          } catch (error) {
            // Handle the case where the GAT ID doesn't exist
            console.error(`Error fetching program selection for GAT ID ${gatIdToUse}:`, error);
            setProgramSelectionExists(false);
            setExistingProgramSelection(null);
            form.reset();
            setSelectedProgram(null);
          }
        } else {
          // No GAT ID available
          setProgramSelectionExists(false);
          setExistingProgramSelection(null);
          console.log('No GAT ID available to fetch program selection');
        }

        // If no program selection found by GAT ID, fall back to getting all program selections
        // But only if this is not a new application and we're not editing a specific GAT
        if (!isNewApplication && !isEditing && !programSelectionExists && !existingProgramSelection) {
          try {
            const response = await applicationAPI.getCurrentProgramSelection();
            console.log('Fetched all program selections:', response.data);
            if (response.data && response.data.length > 0) {
              const programSelection = response.data[0];
              setExistingProgramSelection(programSelection);
              console.log('Existing program selection:', programSelection);
              console.log('Existing sponsorship value:', programSelection.sponsorship);

              // Set the selected program based on the existing selection
              if (programSelection.application_info) {
                // Fetch the application info details
                try {
                  const appInfoResponse = await applicationAPI.getApplicationInfo(programSelection.application_info);
                  if (appInfoResponse.data) {
                    setSelectedProgram(appInfoResponse.data);
                    console.log('Selected program set from existing data:', appInfoResponse.data);

                    // Update the form with the existing program ID and sponsorship
                    const sponsorshipValue = programSelection.sponsorship || '';
                    console.log('Setting sponsorship value in form:', sponsorshipValue);

                    // First set the program_id
                    form.setValue('program_id', programSelection.application_info.toString());

                    // Then explicitly set the sponsorship value if it exists
                    if (sponsorshipValue) {
                      form.setValue('sponsorship', sponsorshipValue);
                    }

                    // Set the year value if it exists
                    if (programSelection.year) {
                      form.setValue('year', programSelection.year);
                    }

                    // Set the term value if it exists
                    if (programSelection.term) {
                      form.setValue('term', programSelection.term);
                    }

                    console.log('Form values after setting:', {
                      program_id: form.getValues('program_id'),
                      sponsorship: form.getValues('sponsorship'),
                      year: form.getValues('year'),
                      term: form.getValues('term')
                    });
                  }
                } catch (error) {
                  console.error('Error fetching application info details:', error);
                }
              }
            }
          } catch (error) {
            console.error('Error fetching current program selections:', error);
          }
        }
      } catch (error) {
        console.error('Error fetching existing program selection:', error);
      } finally {
        setIsLoadingExistingData(false);
      }
    };

    // Fetch program selection when component mounts or when dependencies change
    fetchExistingProgramSelection();

  }, [form, currentGAT, isNewApplication, isEditing, gatIdFromUrl]);

  // Load colleges, departments, programs, and admission types on component mount
  useEffect(() => {
    const loadFilterData = async () => {
      try {
        console.log('Loading filter data...');

        // Load colleges
        const collegesResponse = await applicationAPI.getPublicColleges();
        console.log('Colleges loaded:', collegesResponse.data);
        setColleges(collegesResponse.data || []);

        // Load programs
        const programsResponse = await applicationAPI.getPublicPrograms();
        console.log('Programs loaded:', programsResponse.data);
        setPrograms(programsResponse.data || []);

        // Load admission types
        const admissionTypesResponse = await applicationAPI.getAdmissionTypes();
        console.log('Admission types loaded:', admissionTypesResponse.data);
        setAdmissionTypes(admissionTypesResponse.data || []);

        // Load years
        const yearsResponse = await yearAPI.getAllYears();
        console.log('Years loaded:', yearsResponse.data);
        setYears(yearsResponse.data || []);

        // Load terms
        const termsResponse = await termAPI.getTerms();
        console.log('Terms loaded:', termsResponse.data);
        setTerms(termsResponse.data || []);

        // Load registration periods
        const registrationPeriodsResponse = await programAPI.getRegistrationPeriods();
        console.log('Registration periods loaded:', registrationPeriodsResponse);
        setRegistrationPeriods(registrationPeriodsResponse || []);

        // If no data is loaded, show a message to the user
        if (!collegesResponse.data?.length) {
          toast.warning('No colleges found. Please contact an administrator.');
        }

        if (!programsResponse.data?.length) {
          toast.warning('No programs found. Please contact an administrator.');
        }

        // Only show a warning if no admission types were loaded from the API
        if (!admissionTypesResponse.data?.length) {
          toast.warning('No admission types found. Please contact an administrator.');
        }

        if (!yearsResponse.data?.length) {
          toast.warning('No academic years found. Please contact an administrator.');
        }

        if (!termsResponse.data?.length) {
          toast.warning('No terms found. Please contact an administrator.');
        }
      } catch (error) {
        console.error('Error loading filter data:', error);
        toast.error('Failed to load filter data. Please check your connection and try again.');

        // Set empty arrays for all data
        setColleges([]);
        setPrograms([]);
        setAdmissionTypes([]);
        setYears([]);
        setTerms([]);
      }
    };

    loadFilterData();
  }, []);

  // Load departments when college changes
  const onCollegeChange = async (collegeId: string) => {
    if (!collegeId || collegeId === '0') {
      setDepartments([]);
      return;
    }

    try {
      console.log('Loading departments for college ID:', collegeId);
      const response = await applicationAPI.getPublicDepartments(parseInt(collegeId));
      console.log('Departments loaded:', response.data);

      if (response.data && response.data.length > 0) {
        setDepartments(response.data);
      } else {
        // No departments found for this college
        console.log('No departments found for college ID:', collegeId);
        setDepartments([]);
        toast.warning('No departments found for the selected college. Please contact an administrator.');
      }
    } catch (error) {
      console.error('Error loading departments:', error);
      toast.error('Failed to load departments. Please check your connection and try again.');
      setDepartments([]);
    }
  };

  // Search for programs based on filter criteria
  const onSearch = async (data: FilterFormValues) => {
    setIsSearching(true);
    setSelectedProgram(null);

    try {
      console.log('Searching with filters:', data);

      // Build filter parameters - simplify the logic to just pass the values directly
      const filters: Record<string, string> = {};

      // Add all filter parameters, even if they're '0' (the backend will handle this)
      if (data.college && data.college !== '0') filters.college = data.college;
      if (data.department && data.department !== '0') filters.department = data.department;
      if (data.program && data.program !== '0') filters.program = data.program;
      if (data.admission_type && data.admission_type !== '0') filters.admission_type = data.admission_type;

      // Log the filters being sent to the API
      console.log('Sending filters to API:', filters);

      // Call the API to filter application information
      const response = await applicationAPI.filterApplicationInformation(filters);
      console.log('Search results from ApplicationInformation model:', response.data);

      if (response.data && response.data.length > 0) {
        console.log('Using real data from ApplicationInformation model');

        // Filter programs based on registration period
        const now = new Date();
        const filteredPrograms = await Promise.all(response.data.map(async (program: ApplicationInfo) => {
          try {
            // Get registration period information for this program
            const programResponse = await applicationAPI.getProgramApplicationInfo(program.program.id);
            console.log(`Registration info for program ${program.program.id}:`, programResponse.data);

            // Check if registration is active
            const isActive = programResponse.data.registration_active === true;
            const registrationMessage = programResponse.data.registration_message || 'Registration information unavailable';

            // Add registration info to the program
            return {
              ...program,
              registration_active: isActive,
              registration_message: registrationMessage
            };
          } catch (error) {
            console.error(`Error checking registration for program ${program.program.id}:`, error);
            return {
              ...program,
              registration_active: false,
              registration_message: 'Unable to verify registration status'
            };
          }
        }));

        // Filter out programs with inactive registration periods
        const activePrograms = filteredPrograms.filter(program => program.registration_active);

        if (activePrograms.length > 0) {
          setApplicationInfos(activePrograms);
          toast.success(`Found ${activePrograms.length} active program(s) from the database`);
        } else {
          setApplicationInfos([]);
          toast.info('No programs with active registration periods found');
        }
      } else {
        // Don't use mock data, just show an empty result
        console.log('No data found in ApplicationInformation model');
        setApplicationInfos([]);
        toast.info('No programs found matching your criteria');
      }
    } catch (error) {
      console.error('Error searching for programs:', error);
      toast.error('Failed to search for programs');

      // Don't use mock data, just show an empty result
      setApplicationInfos([]);
    } finally {
      setIsSearching(false);
    }
  };



  // Watch for changes to the sponsorship field
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'sponsorship') {
        console.log('Sponsorship changed to:', value.sponsorship);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Initialize expanded colleges when search results change - only first college expanded
  useEffect(() => {
    if (applicationInfos.length > 0) {
      // Create a map of all colleges in the search results
      const newExpandedState: Record<string, boolean> = {};

      // Get unique college names
      const uniqueColleges = Array.from(
        new Set(applicationInfos.map(info => info.college.name))
      ).sort();

      // Set only the first college to be expanded, all others collapsed
      uniqueColleges.forEach((collegeName, index) => {
        newExpandedState[collegeName] = index === 0; // Only first college is expanded
      });

      // Update the expanded colleges state
      setExpandedColleges(newExpandedState);
    }
  }, [applicationInfos]);

  // Select a program for application
  const selectProgram = (program: ApplicationInfo) => {
    setSelectedProgram(program);
    form.setValue('program_id', program.id.toString());

    // Find the registration period for this program and auto-fill year and term
    const period = registrationPeriods.find(p => p.program === program.program.id);
    if (period) {
      // Auto-fill academic year if available
      if (period.year_name) {
        const matchingYear = years.find(y => y.year === period.year_name);
        if (matchingYear) {
          form.setValue('year', matchingYear.uuid);
        }
      }

      // Auto-fill term if available
      if (period.term_name) {
        const matchingTerm = terms.find(t => t.name === period.term_name);
        if (matchingTerm) {
          form.setValue('term', matchingTerm.id);
        }
      }
    }

    // Add a small delay to ensure the DOM has updated with the selected program details
    setTimeout(() => {
      // Create a reference to the program details section
      const programDetailsSection = document.querySelector('.program-details-section');

      // Scroll to the program details section if it exists
      if (programDetailsSection) {
        // Scroll to the program details section
        programDetailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // Add a highlight effect to the program details section
        programDetailsSection.classList.add('highlight-section');

        // Remove the highlight effect after 1.5 seconds
        setTimeout(() => {
          programDetailsSection.classList.remove('highlight-section');
        }, 1500);
      }
    }, 100);
  };

  // Submit program selection
  const onSubmit = async (data: ProgramSelectionFormValues) => {
    if (!selectedProgram) {
      toast.error('Please select a program first');
      return;
    }

    if (!data.sponsorship || data.sponsorship.trim() === '') {
      toast.error('Sponsorship is required. Please select a sponsorship type.');
      form.setError('sponsorship', {
        type: 'manual',
        message: 'Sponsorship is required'
      });
      return;
    }

    // Check if year and term are available from registration period or form data
    const period = registrationPeriods.find(p => p.program === selectedProgram.program.id);
    const hasRegistrationPeriodData = period && period.year_name && period.term_name;

    if (!hasRegistrationPeriodData) {
      // Only validate form fields if registration period data is not available
      if (!data.year || data.year.trim() === '') {
        toast.error('Academic Year is required. Please select an academic year.');
        form.setError('year', {
          type: 'manual',
          message: 'Academic Year is required'
        });
        return;
      }

      if (!data.term || data.term.trim() === '') {
        toast.error('Term is required. Please select a term.');
        form.setError('term', {
          type: 'manual',
          message: 'Term is required'
        });
        return;
      }
    }

    // Check if the user has a GAT record
    if (!currentGAT) {
      toast.error(
        <div className="flex flex-col space-y-2">
          <span className="font-semibold text-red-700">GAT Information Required</span>
          <span className="text-sm">
            You must enter your GAT information before selecting a program. Please go to the GAT page first.
          </span>
        </div>,
        {
          duration: 5000,
          position: 'top-center'
        }
      );
      return;
    }

    console.log('Form data being submitted:', data);
    setIsLoading(true);

    try {
      // Check if user is authenticated
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You are not authenticated. Please log in again.');
        navigate('/login');
        return;
      }

      // Get the current form values directly
      const currentSponsorship = form.getValues('sponsorship');
      console.log('Current form sponsorship value:', currentSponsorship);

      // Get year and term from registration period if available, otherwise use form data
      let yearValue = data.year;
      let termValue = data.term;

      const period = registrationPeriods.find(p => p.program === selectedProgram.program.id);
      if (period) {
        // Use registration period data if available
        if (period.year_name) {
          const matchingYear = years.find(y => y.year === period.year_name);
          if (matchingYear) {
            yearValue = matchingYear.uuid;
          }
        }
        if (period.term_name) {
          const matchingTerm = terms.find(t => t.name === period.term_name);
          if (matchingTerm) {
            termValue = matchingTerm.id;
          }
        }
      }

      // Format the data for the API
      const formattedData: {
        application_info: number;
        sponsorship: string;
        year: string;
        term: string;
        gat?: number;
      } = {
        application_info: selectedProgram.id,
        // Ensure sponsorship is always provided with a default value if not set
        sponsorship: currentSponsorship || data.sponsorship || 'Self', // Default to 'Self' if not provided
        year: yearValue,
        term: termValue,
      };

      // Add the GAT ID to the request if available
      // Prioritize the GAT ID from the URL if it's available
      if (gatIdFromUrl) {
        formattedData.gat = parseInt(gatIdFromUrl, 10);
        console.log(`Adding GAT ID ${gatIdFromUrl} from URL to the request`);
      } else if (currentGAT?.id) {
        formattedData.gat = currentGAT.id;
        console.log(`Adding GAT ID ${currentGAT.id} to the request`);
      } else {
        console.warn('No GAT ID available for the request');
      }

      // Log the formatted data to verify sponsorship is included
      console.log('Formatted data with sponsorship and GAT:', formattedData);

      // Double check that sponsorship is set and valid
      if (!formattedData.sponsorship || formattedData.sponsorship === '') {
        console.log('Sponsorship is still empty, forcing it to Self');
        formattedData.sponsorship = 'Self';
      }

      // Verify the sponsorship value is one of the valid options
      const validSponsorships = sponsorshipOptions.map(option => option.value);
      if (!validSponsorships.includes(formattedData.sponsorship)) {
        console.log(`Sponsorship value "${formattedData.sponsorship}" is not valid, setting to Self`);
        formattedData.sponsorship = 'Self';
      }

      console.log('Final formatted data:', formattedData);

      console.log('Submitting program selection data with GAT ID:', formattedData);

      try {
        let response: any;

        // If this is a new application, always create a new program selection
        if (isNewApplication) {
          console.log('Creating new program selection for new application');
          try {
            // Always create a new program selection for a new application
            response = await applicationAPI.submitProgramSelection(formattedData);
            console.log('Create API response for new application:', response);

            // Check if the response is successful (status 2xx)
            if (response.status >= 200 && response.status < 300) {
              toast.success('Program selection created successfully!');
              // Navigate to the next step in the application process with the GAT ID
              navigate(`/application/documentation?gat=${formattedData.gat}`);
            } else {
              console.warn('Unexpected response status for create:', response.status);
              console.warn('Response data:', response.data);

              // Handle 400 Bad Request errors
              if (response.status === 400 && response.data) {
                // Log the error data for debugging
                console.error('Bad Request Error Data:', response.data);

                // Check for the specific "gat already exists" error
                if (response.data.gat &&
                    Array.isArray(response.data.gat) &&
                    response.data.gat[0].includes("already exists")) {
                  console.log('GAT already has a program selection, proceeding to the next step');

                  // Show a more informative message to the user
                  const gatNo = gatIdFromUrl ? `GAT ${gatIdFromUrl}` : 'This GAT';
                  toast.info(`${gatNo} already has a program selection. Proceeding to the next step.`, {
                    duration: 5000,
                    position: 'top-center',
                    icon: <Info className="h-5 w-5 text-blue-500" />
                  });

                  // Navigate to the next step in the application process with the GAT ID
                  navigate(`/application/documentation?gat=${gatIdFromUrl || formattedData.gat || ''}`);
                  return; // Exit early to prevent further processing
                }
                // Check for the specific "gat does not exist" error
                else if (response.data.gat &&
                    Array.isArray(response.data.gat) &&
                    (response.data.gat[0].includes("does not exist") ||
                     response.data.gat[0].includes("Invalid pk"))) {
                  console.log('GAT does not exist, creating a new program selection without GAT association');

                  // Create a new copy of the data without the GAT ID
                  const newFormattedData = {
                    application_info: formattedData.application_info,
                    sponsorship: formattedData.sponsorship,
                    year: formattedData.year,
                    term: formattedData.term
                  };

                  console.log('Creating new program selection without GAT association:', newFormattedData);

                  try {
                    // Submit the new data without GAT ID
                    const newResponse = await applicationAPI.submitProgramSelection(newFormattedData);

                    if (newResponse.status >= 200 && newResponse.status < 300) {
                      toast.success('Program selection created successfully!');
                      // Navigate to the next step in the application process with the GAT ID
                      // Since we're creating without GAT, we don't pass a GAT ID
                      navigate('/application/documentation');
                    } else {
                      toast.error('Failed to create program selection');
                      console.error('Error creating program selection:', newResponse);
                    }
                  } catch (createError) {
                    console.error('Error creating program selection without GAT:', createError);

                    // Check if this is a database integrity error (duplicate application_num)
                    if (createError.response && createError.response.status === 500 &&
                        createError.response.data &&
                        createError.response.data.includes('duplicate key value violates unique constraint') &&
                        createError.response.data.includes('application_num')) {

                      console.log('Detected database integrity error for application_num, proceeding to next step');

                      // Extract the application number from the error message if possible
                      let appNum = 'UOG number';
                      const match = createError.response.data.match(/Key \(application_num\)=\(([^)]+)\)/);
                      if (match && match[1]) {
                        appNum = match[1];
                      }

                      // Show a more informative message
                      toast.info(`An application with number ${appNum} already exists. Proceeding to the next step.`);

                      // Navigate to the next step in the application process
                      // No GAT ID to pass since we're handling a database integrity error
                      navigate('/application/documentation');
                    } else {
                      toast.error('Failed to create program selection');
                    }
                  }

                  return; // Exit early to prevent further processing

                } else {
                  // Handle other field errors
                  if (typeof response.data === 'object') {
                    Object.entries(response.data).forEach(([field, errors]) => {
                      if (Array.isArray(errors) && errors.length > 0) {
                        toast.error(`${field}: ${errors[0]}`);
                      } else if (typeof errors === 'string') {
                        toast.error(`${field}: ${errors}`);
                      }
                    });
                  } else if (typeof response.data === 'string') {
                    toast.error(response.data);
                  } else {
                    toast.error('Invalid data submitted. Please check your form and try again.');
                  }
                }
              } else {
                toast.warning('Received an unexpected response from the server. Please try again.');
              }
            }
          } catch (error) {
            console.error('Error creating program selection:', error);

            // Check if this is a database integrity error (duplicate application_num)
            if (error.response && error.response.status === 500 &&
                error.response.data &&
                error.response.data.includes('duplicate key value violates unique constraint') &&
                error.response.data.includes('application_num')) {

              console.log('Detected database integrity error for application_num, proceeding to next step');

              // Extract the application number from the error message if possible
              let appNum = 'UOG number';
              const match = error.response.data.match(/Key \(application_num\)=\(([^)]+)\)/);
              if (match && match[1]) {
                appNum = match[1];
              }

              // Show a more informative message
              toast.info(`An application with number ${appNum} already exists. Proceeding to the next step.`);

              // Navigate to the next step in the application process
              // No GAT ID to pass since we're handling a database integrity error
              navigate('/application/documentation');
            } else {
              // Handle other errors
              toast.error('An error occurred while creating the program selection');
            }
          }
        }
        // If a program selection exists for this GAT, update it
        else if (programSelectionExists && existingProgramSelection?.id) {
          console.log(`Updating program selection with ID ${existingProgramSelection.id}`);
          // Use the updateProgramSelection method
          response = await applicationAPI.updateProgramSelection(existingProgramSelection.id, formattedData);
          console.log('Update API response:', response);

          // Check if the response is successful (status 2xx)
          if (response.status >= 200 && response.status < 300) {
            toast.success('Program selection updated successfully!');
            // Navigate to the next step in the application process with the GAT ID
            navigate(`/application/documentation?gat=${formattedData.gat}`);
          } else {
            console.warn('Unexpected response status for update:', response.status);
            console.warn('Response data:', response.data);

            // Handle 400 Bad Request errors
            if (response.status === 400 && response.data) {
              // Log the error data for debugging
              console.error('Bad Request Error Data:', response.data);

              // Check if there are specific field errors
              if (typeof response.data === 'object') {
                Object.entries(response.data).forEach(([field, errors]) => {
                  if (Array.isArray(errors) && errors.length > 0) {
                    toast.error(`${field}: ${errors[0]}`);
                  } else if (typeof errors === 'string') {
                    toast.error(`${field}: ${errors}`);
                  }
                });
              } else if (typeof response.data === 'string') {
                toast.error(response.data);
              } else {
                toast.error('Invalid data submitted. Please check your form and try again.');
              }
            } else {
              toast.warning('Received an unexpected response from the server. Please try again.');
            }
          }
        } else {
          // No existing program selection - create a new one
          // Submit new data to the API
          console.log('Creating new program selection record');
          try {
            response = await applicationAPI.submitProgramSelection(formattedData);
            console.log('Create API response:', response);

            // Check if the response is successful (status 2xx)
            if (response.status >= 200 && response.status < 300) {
              toast.success('Program selection saved successfully!');
              // Navigate to the next step in the application process with the GAT ID from the response
              const createdGatId = response.data?.gat || formattedData.gat;
              navigate(`/application/documentation?gat=${createdGatId}`);
            } else {
              console.warn('Unexpected response status for create:', response.status);
              console.warn('Response data:', response.data);

              // Handle 400 Bad Request errors
              if (response.status === 400 && response.data) {
                // Log the error data for debugging
                console.error('Bad Request Error Data:', response.data);

                // Check for the specific "gat already exists" error
                if (response.data.gat &&
                    Array.isArray(response.data.gat) &&
                    response.data.gat[0].includes("already exists")) {
                  console.log('GAT already has a program selection, proceeding to the next step');

                  // Show a more informative message to the user
                  const gatNo = formattedData.gat ? `GAT ${formattedData.gat}` : 'This GAT';
                  toast.info(`${gatNo} already has a program selection. Proceeding to the next step.`, {
                    duration: 5000,
                    position: 'top-center',
                    icon: <Info className="h-5 w-5 text-blue-500" />
                  });

                  // Navigate to the next step in the application process with the GAT ID
                  navigate(`/application/documentation?gat=${formattedData.gat || ''}`);
                  return; // Exit early to prevent further processing
                }
                // Check for the specific "gat does not exist" error
                else if (response.data.gat &&
                    Array.isArray(response.data.gat) &&
                    (response.data.gat[0].includes("does not exist") ||
                     response.data.gat[0].includes("Invalid pk"))) {
                  console.log('GAT does not exist, creating a new program selection without GAT association');

                  // Create a new copy of the data without the GAT ID
                  const newFormattedData = {
                    application_info: formattedData.application_info,
                    sponsorship: formattedData.sponsorship,
                    year: formattedData.year,
                    term: formattedData.term
                  };

                  console.log('Creating new program selection without GAT association:', newFormattedData);

                  try {
                    // Submit the new data without GAT ID
                    const newResponse = await applicationAPI.submitProgramSelection(newFormattedData);

                    if (newResponse.status >= 200 && newResponse.status < 300) {
                      toast.success('Program selection created successfully!');
                      // Navigate to the next step in the application process
                      // Since we're creating without GAT, we don't pass a GAT ID
                      navigate('/application/documentation');
                    } else {
                      toast.error('Failed to create program selection');
                      console.error('Error creating program selection:', newResponse);
                    }
                  } catch (createError) {
                    console.error('Error creating program selection without GAT:', createError);

                    // Check if this is a database integrity error (duplicate application_num)
                    if (createError.response && createError.response.status === 500 &&
                        createError.response.data &&
                        createError.response.data.includes('duplicate key value violates unique constraint') &&
                        createError.response.data.includes('application_num')) {

                      console.log('Detected database integrity error for application_num, proceeding to next step');

                      // Extract the application number from the error message if possible
                      let appNum = 'UOG number';
                      const match = createError.response.data.match(/Key \(application_num\)=\(([^)]+)\)/);
                      if (match && match[1]) {
                        appNum = match[1];
                      }

                      // Show a more informative message
                      toast.info(`An application with number ${appNum} already exists. Proceeding to the next step.`);

                      // Navigate to the next step in the application process
                      // No GAT ID to pass since we're handling a database integrity error
                      navigate('/application/documentation');
                    } else {
                      toast.error('Failed to create program selection');
                    }
                  }

                  return; // Exit early to prevent further processing
                } else {
                  // Handle other field errors
                  if (typeof response.data === 'object') {
                    Object.entries(response.data).forEach(([field, errors]) => {
                      if (Array.isArray(errors) && errors.length > 0) {
                        toast.error(`${field}: ${errors[0]}`);
                      } else if (typeof errors === 'string') {
                        toast.error(`${field}: ${errors}`);
                      }
                    });
                  } else if (typeof response.data === 'string') {
                    toast.error(response.data);
                  } else {
                    toast.error('Invalid data submitted. Please check your form and try again.');
                  }
                }
              } else {
                toast.warning('Received an unexpected response from the server. Please try again.');
              }
            }
          } catch (error) {
            console.error('Error creating program selection:', error);

            // Check if this is a database integrity error (duplicate application_num)
            if (error.response && error.response.status === 500 &&
                error.response.data &&
                error.response.data.includes('duplicate key value violates unique constraint') &&
                error.response.data.includes('application_num')) {

              console.log('Detected database integrity error for application_num, proceeding to next step');

              // Extract the application number from the error message if possible
              let appNum = 'UOG number';
              const match = error.response.data.match(/Key \(application_num\)=\(([^)]+)\)/);
              if (match && match[1]) {
                appNum = match[1];
              }

              // Check if we're trying to create a program selection with a non-existent GAT ID
              if (isEditing && gatIdFromUrl) {
                // Since we're getting a database integrity error, it means a program selection already exists
                // Just show a message and proceed to the next step
                toast.warning(`GAT ID ${gatIdFromUrl} doesn't exist or is invalid, but an application already exists. Proceeding to the next step.`);

                // Extract the application number from the error message if possible
                let appNum = 'UOG number';
                const match = error.response.data.match(/Key \(application_num\)=\(([^)]+)\)/);
                if (match && match[1]) {
                  appNum = match[1];
                }

                // Show the application number in the message
                toast.info(`Using existing application with number ${appNum}.`);

                // Navigate to the next step in the application process with the GAT ID
                navigate(`/application/documentation?gat=${gatIdFromUrl}`);
              } else {
                // Show a more informative message for duplicate application number
                toast.info(`An application with number ${appNum} already exists. Proceeding to the next step.`);
                // Navigate to the next step in the application process with the GAT ID
                navigate(`/application/documentation?gat=${gatIdFromUrl}`);
              }
            } else {
              // Handle other errors
              toast.error('An error occurred while creating the program selection');
            }
          }
        }
      } catch (apiError: any) {
        console.error('API submission error:', apiError);

        // Handle specific error messages
        if (apiError.response?.data) {
          const errorData = apiError.response.data;
          console.error('Error data:', errorData);

          // If the error is a string
          if (typeof errorData === 'string') {
            toast.error(errorData);
          }
          // If the error is an object with field-specific errors
          else if (typeof errorData === 'object') {
            // Display each field error
            Object.entries(errorData).forEach(([field, errors]) => {
              console.log(`Field error for ${field}:`, errors);

              if (Array.isArray(errors) && errors.length > 0) {
                toast.error(`${field}: ${errors[0]}`);

                // If sponsorship is missing, set it to 'Self' and try again
                if (field === 'sponsorship' && errors[0].includes('required')) {
                  console.log('Sponsorship is required but missing, setting to Self');
                  form.setValue('sponsorship', 'Self');
                }
              } else if (typeof errors === 'string') {
                toast.error(`${field}: ${errors}`);

                // If sponsorship is missing, set it to 'Self' and try again
                if (field === 'sponsorship' && errors.includes('required')) {
                  console.log('Sponsorship is required but missing, setting to Self');
                  form.setValue('sponsorship', 'Self');
                }
              }
            });

            // If sponsorship was the only error, try submitting again
            if (Object.keys(errorData).length === 1 && errorData.sponsorship) {
              console.log('Only sponsorship error detected, trying to submit again');
              setTimeout(() => {
                form.handleSubmit(onSubmit)();
              }, 1000);
            }
          }
        } else {
          toast.error('Failed to save program selection. Please try again.');
        }
      }
    } catch (error: any) {
      // This catch block will handle any errors not caught by the inner try-catch
      console.error('Unhandled error submitting program selection:', error);

      // Only show a generic error if we haven't already shown a specific one
      if (!error.response) {
        toast.error('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        {/* Hero Section */}
        <Card className="shadow-2xl border-0 overflow-hidden mb-6">
          <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
            <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                 style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
            <div className="relative z-10 flex items-center">
              <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                <BookOpen className="h-8 w-8 text-[#1a73c0]" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-white">Program Selection</CardTitle>
                <CardDescription className="text-blue-100 mt-1">
                  Search and select a program for your application
                </CardDescription>
                {isLoadingExistingData ? (
                  <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading your information...
                  </div>
                ) : programSelectionExists && existingProgramSelection ? (
                  <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    You are editing your previously submitted program selection
                  </div>
                ) : null}
              </div>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 gap-6">
            {/* Search Filters */}
            <Card className="shadow-lg border-0 overflow-hidden">
              <CardHeader className="bg-gray-50 border-b">
                <div className="flex items-center">
                  <div className="bg-[#1a73c0] p-2 rounded-full mr-3">
                    <Filter className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl text-[#1a73c0]">Search Programs</CardTitle>
                    <CardDescription className="text-gray-600">
                      Filter programs by college, department, program, and admission type
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <Form {...filterForm}>
                  <form
                    onSubmit={filterForm.handleSubmit(onSearch)}
                    className="space-y-4"
                  >
                    <div className="space-y-6">
                      <div className="flex items-center">
                        <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                          <Filter className="h-5 w-5 text-[#1a73c0]" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-800">Filter Options</h3>
                        <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <FormField
                          control={filterForm.control}
                          name="college"
                          render={({ field }) => (
                            <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                              <FormLabel className="text-gray-700 font-medium">College</FormLabel>
                              <FormControl>
                                <div className="relative mt-1.5">
                                  <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                    <Building className="h-5 w-5 text-[#1a73c0]" />
                                  </div>
                                  <Select
                                    onValueChange={(value) => {
                                      field.onChange(value);
                                      onCollegeChange(value);
                                    }}
                                    defaultValue={field.value}
                                    name="college"
                                  >
                                    <SelectTrigger
                                      id="college"
                                      className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                      aria-label="College selection"
                                    >
                                      <SelectValue placeholder="Select college" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="0">All Colleges</SelectItem>
                                      {colleges.map((college) => (
                                        <SelectItem key={college.id} value={college.id.toString()}>
                                          {college.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-500 text-sm mt-1" />
                              <p className="text-xs text-gray-500 mt-2">Select a college to filter programs</p>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={filterForm.control}
                          name="department"
                          render={({ field }) => (
                            <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                              <FormLabel className="text-gray-700 font-medium">Department</FormLabel>
                              <FormControl>
                                <div className="relative mt-1.5">
                                  <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                    <School className="h-5 w-5 text-[#1a73c0]" />
                                  </div>
                                  <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    name="department"
                                  >
                                    <SelectTrigger
                                      id="department"
                                      className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                      aria-label="Department selection"
                                    >
                                      <SelectValue placeholder="Select department" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="0">All Departments</SelectItem>
                                      {departments.map((department) => (
                                        <SelectItem key={department.id} value={department.id.toString()}>
                                          {department.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-500 text-sm mt-1" />
                              <p className="text-xs text-gray-500 mt-2">Select a department to narrow your search</p>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={filterForm.control}
                          name="program"
                          render={({ field }) => (
                            <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                              <FormLabel className="text-gray-700 font-medium">Program</FormLabel>
                              <FormControl>
                                <div className="relative mt-1.5">
                                  <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                    <BookOpen className="h-5 w-5 text-[#1a73c0]" />
                                  </div>
                                  <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    name="program"
                                  >
                                    <SelectTrigger
                                      id="program"
                                      className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                      aria-label="Program selection"
                                    >
                                      <SelectValue placeholder="Select program" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="0">All Programs</SelectItem>
                                      {programs.map((program) => (
                                        <SelectItem key={program.id} value={program.id.toString()}>
                                          {program.program_code ? `${program.program_code} - ${program.program_name}` : program.program_name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-500 text-sm mt-1" />
                              <p className="text-xs text-gray-500 mt-2">Select a specific program type</p>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={filterForm.control}
                          name="admission_type"
                          render={({ field }) => (
                            <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                              <FormLabel className="text-gray-700 font-medium">Admission Type</FormLabel>
                              <FormControl>
                                <div className="relative mt-1.5">
                                  <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                    <GraduationCap className="h-5 w-5 text-[#1a73c0]" />
                                  </div>
                                  <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    name="admission_type"
                                  >
                                    <SelectTrigger
                                      id="admission_type"
                                      className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                      aria-label="Admission type selection"
                                    >
                                      <SelectValue placeholder="Select admission type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="0">All Types</SelectItem>
                                      {admissionTypes.map((type) => (
                                        <SelectItem key={type.id} value={type.id.toString()}>
                                          {type.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-500 text-sm mt-1" />
                              <p className="text-xs text-gray-500 mt-2">Select the type of admission you're applying for</p>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <div className="flex justify-end space-x-6 pt-10 border-t mt-10 bg-gray-50 -mx-6 px-6 py-6 rounded-b-lg">
                      <input
                        type="submit"
                        value={isSearching ? "Searching..." : "Search Programs"}
                        disabled={isSearching}
                        className={`px-8 py-3 rounded-md bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-base cursor-pointer ${isSearching ? 'opacity-70 cursor-not-allowed' : ''}`}
                      />
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* Search Results */}
            {applicationInfos.length > 0 ? (
              <Card className="shadow-lg border-0 overflow-hidden">
                <CardHeader className="bg-gray-50 border-b">
                  <div className="flex items-center">
                    <div className="bg-[#1a73c0] p-2 rounded-full mr-3">
                      <Search className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-[#1a73c0]">Search Results</CardTitle>
                      <CardDescription className="text-gray-600">
                        Found {applicationInfos.length} program(s) matching your criteria
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  {/* Group programs by college with collapsible sections */}
                  {(() => {
                    // Create a map of colleges to their programs
                    const collegeMap = new Map<string, ApplicationInfo[]>();

                    // Group programs by college
                    applicationInfos.forEach(info => {
                      const collegeName = info.college.name;
                      if (!collegeMap.has(collegeName)) {
                        collegeMap.set(collegeName, []);
                        // Don't set expanded state here - it's handled by the useEffect
                      }
                      collegeMap.get(collegeName)!.push(info);
                    });

                    // Sort colleges alphabetically
                    const sortedColleges = Array.from(collegeMap.keys()).sort();

                    // Toggle college expansion
                    const toggleCollege = (collegeName: string) => {
                      setExpandedColleges(prev => ({
                        ...prev,
                        [collegeName]: !prev[collegeName]
                      }));
                    };

                    return sortedColleges.map(collegeName => (
                      <div key={collegeName} className={`mb-6 last:mb-0 rounded-lg overflow-hidden transition-all duration-200 ${
                        expandedColleges[collegeName]
                          ? "border border-blue-300 shadow-md"
                          : "border border-gray-200"
                      }`}>
                        <div
                          className={`flex items-center p-4 cursor-pointer transition-colors ${
                            expandedColleges[collegeName]
                              ? "bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200"
                              : "bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-100"
                          }`}
                          onClick={() => toggleCollege(collegeName)}
                        >
                          <div className={`p-2 rounded-full mr-3 ${
                            expandedColleges[collegeName]
                              ? "bg-blue-200"
                              : "bg-[#1a73c0]/10"
                          }`}>
                            <Building className="h-5 w-5 text-[#1a73c0]" />
                          </div>
                          <h3 className={`text-lg font-medium ${
                            expandedColleges[collegeName]
                              ? "text-blue-700"
                              : "text-[#1a73c0]"
                          }`}>{collegeName}</h3>
                          <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                          <span className={`ml-3 text-sm px-2 py-1 rounded-full ${
                            expandedColleges[collegeName]
                              ? "text-blue-700 bg-blue-100"
                              : "text-gray-500 bg-gray-100"
                          }`}>
                            {collegeMap.get(collegeName)!.length} program(s)
                          </span>
                          <div className={`ml-3 ${
                            expandedColleges[collegeName]
                              ? "text-blue-700"
                              : "text-gray-500"
                          }`}>
                            {expandedColleges[collegeName] ? (
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                                <path d="m18 15-6-6-6 6"/>
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
                                <path d="m6 9 6 6 6-6"/>
                              </svg>
                            )}
                          </div>
                        </div>

                        {expandedColleges[collegeName] && (
                          <div className="overflow-x-auto">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className="py-2 px-3 text-sm">Department</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Program</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Field of Study</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Study Program</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Duration (Years)</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Academic Year</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Term</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Admission Type</TableHead>
                                  <TableHead className="py-2 px-3 text-sm">Action</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {collegeMap.get(collegeName)!.map((info) => (
                                  <TableRow key={info.id} className={selectedProgram?.id === info.id ? 'bg-blue-50' : ''}>
                                    <TableCell className="py-2 px-3 text-sm">{info.department.name}</TableCell>
                                    <TableCell className="py-2 px-3 text-sm">
                                      {info.program.program_code ?
                                        `${info.program.program_code}` :
                                        ''}
                                    </TableCell>
                                    <TableCell className="py-2 px-3 text-sm">{info.field_of_study.name}</TableCell>
                                    <TableCell className="py-2 px-3 text-sm">{info.study_program.name}</TableCell>
                                    <TableCell className="py-2 px-3 text-sm">{info.duration || '2'}</TableCell>
                                    <TableCell className="py-2 px-3 text-sm">
                                      {(() => {
                                        // Find the registration period for this program
                                        const period = registrationPeriods.find(p => p.program === info.program.id);
                                        return period?.year_name || 'N/A';
                                      })()}
                                    </TableCell>
                                    <TableCell className="py-2 px-3 text-sm">
                                      {(() => {
                                        // Find the registration period for this program
                                        const period = registrationPeriods.find(p => p.program === info.program.id);
                                        return period?.term_name || 'N/A';
                                      })()}
                                    </TableCell>
                                    <TableCell className="py-2 px-3 text-sm">{info.admission_type.name}</TableCell>
                                    <TableCell className="py-2 px-3">
                                      <Button
                                        variant={selectedProgram?.id === info.id ? "secondary" : "default"}
                                        className={selectedProgram?.id === info.id ? "bg-gondar hover:bg-gondar/90" : "bg-blue-600 hover:bg-blue-700 text-white"}
                                        size="sm"
                                        onClick={() => selectProgram(info)}
                                      >
                                        {selectedProgram?.id === info.id ? "Selected" : "Select"}
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        )}
                      </div>
                    ));
                  })()}
                </CardContent>
              </Card>
            ) : isSearching ? null : (
              <Card className="shadow-lg border-0 overflow-hidden">
                <CardHeader className="bg-gray-50 border-b">
                  <div className="flex items-center">
                    <div className="bg-[#1a73c0] p-2 rounded-full mr-3">
                      <Clock className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-[#1a73c0]">No Programs Available</CardTitle>
                      <CardDescription className="text-gray-600">
                        No programs with active registration periods were found
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="p-6 bg-blue-50 rounded-lg border border-blue-100 text-center">
                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-white shadow-md mb-4">
                      <Clock className="h-8 w-8 text-[#1a73c0]" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900">Registration Period Information</h3>
                    <p className="mt-2 text-sm text-gray-600 max-w-lg mx-auto">
                      Programs are only available for selection during their active registration periods.
                      Please check back later or contact the admissions office for more information.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Program Selection Form */}
            {selectedProgram && (
              <Card className="shadow-lg border-0 overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
                  <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                       style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
                  <div className="relative z-10 flex items-center">
                    <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                      <GraduationCap className="h-8 w-8 text-[#1a73c0]" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl font-bold text-white">Complete Your Selection</CardTitle>
                      <CardDescription className="text-blue-100 mt-1">
                        {programSelectionExists && existingProgramSelection
                          ? 'Update your program selection'
                          : 'Program details are shown below'}
                      </CardDescription>
                      {programSelectionExists && existingProgramSelection && (
                        <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                          <Info className="h-4 w-4 mr-2" />
                          You are editing your previously submitted program selection
                        </div>
                      )}
                      {isLoadingExistingData && (
                        <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Loading your information...
                        </div>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="mb-8 bg-blue-50 p-4 rounded-lg border border-blue-100 program-details-section">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 text-[#1a73c0] mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-[#1a73c0] mb-1">Selected Program Details</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mt-2">
                          <div>Field of Study: <span className="font-semibold">{selectedProgram.field_of_study.name}</span></div>
                          <div>Department: <span className="font-semibold">{selectedProgram.department.name}</span></div>
                          <div>College: <span className="font-semibold">{selectedProgram.college.name}</span></div>
                          <div>Program: <span className="font-semibold">
                            {selectedProgram.program.program_code ? selectedProgram.program.program_code : ''}
                          </span></div>
                          <div>Study Program: <span className="font-semibold">{selectedProgram.study_program.name}</span></div>
                          <div>Duration: <span className="font-semibold">{selectedProgram.duration || '2'} Years</span></div>
                          <div>Admission Type: <span className="font-semibold">{selectedProgram.admission_type.name}</span></div>
                          <div>Sponsorship: <span className="font-semibold">{form.getValues().sponsorship || 'Not selected'}</span></div>
                          {(() => {
                            // Find the registration period for the selected program
                            const period = registrationPeriods.find(p => p.program === selectedProgram.program.id);
                            if (period && (period.year_name || period.term_name)) {
                              return (
                                <>
                                  {period.year_name && (
                                    <div>Program Academic Year: <span className="font-semibold text-blue-600">{period.year_name}</span></div>
                                  )}
                                  {period.term_name && (
                                    <div>Program Term: <span className="font-semibold text-blue-600">{period.term_name}</span></div>
                                  )}
                                </>
                              );
                            }
                            return null;
                          })()}
                          <div>GAT Number: <span className="font-semibold">
                            {isEditing && gatIdFromUrl && editingGAT
                              ? editingGAT.GAT_No || `GAT ID: ${gatIdFromUrl}`
                              : currentGAT?.GAT_No || 'Not available'}
                          </span></div>
                          <div>GAT Result: <span className="font-semibold">
                            {isEditing && gatIdFromUrl && editingGAT
                              ? editingGAT.GAT_Result || 'Not available'
                              : currentGAT?.GAT_Result || 'Not available'}
                          </span></div>
                          <div>Application Number: <span className="font-semibold">{existingProgramSelection?.application_num || 'Will be generated'}</span></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <div className="space-y-6">
                        <div className="flex items-center">
                          <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                            <FileText className="h-5 w-5 text-[#1a73c0]" />
                          </div>
                          <h3 className="text-lg font-medium text-gray-800">Application Details</h3>
                          <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                        </div>

                        <FormField
                          control={form.control}
                          name="sponsorship"
                          render={({ field }) => (
                            <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                              <FormLabel className="text-gray-700 font-medium">
                                Sponsorship <span className="text-red-500">*</span>
                                <span className="ml-1 text-xs text-red-500">(Required)</span>
                              </FormLabel>
                              <FormControl>
                                <div className="relative mt-1.5">
                                  <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                    <Briefcase className="h-5 w-5 text-[#1a73c0]" />
                                  </div>
                                  <Select
                                    onValueChange={(value) => {
                                      field.onChange(value);
                                      console.log('Sponsorship changed to:', value);
                                    }}
                                    value={field.value}
                                    name="sponsorship"
                                  >
                                    <SelectTrigger
                                      id="sponsorship"
                                      className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                      aria-label="Sponsorship type"
                                    >
                                      <SelectValue placeholder="Select sponsorship type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {sponsorshipOptions.map((option) => (
                                        <SelectItem key={option.value} value={option.value}>
                                          {option.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </FormControl>
                              <FormMessage className="text-red-500 text-sm mt-1" />
                              <p className="text-xs text-gray-500 mt-2">
                                Select the organization sponsoring your education. This field is required.
                              </p>
                            </FormItem>
                          )}
                        />

                        {/* Only show Academic Year and Term inputs if no program is selected or registration period data is missing */}
                        {(() => {
                          if (!selectedProgram) return true; // Show inputs if no program selected
                          const period = registrationPeriods.find(p => p.program === selectedProgram.program.id);
                          return !period || !period.year_name || !period.term_name; // Show inputs if registration period data is missing
                        })() && (
                          <>
                            <FormField
                              control={form.control}
                              name="year"
                              render={({ field }) => (
                                <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                  <FormLabel className="text-gray-700 font-medium">
                                    Academic Year <span className="text-red-500">*</span>
                                    <span className="ml-1 text-xs text-red-500">(Required)</span>
                                  </FormLabel>
                                  <FormControl>
                                    <div className="relative mt-1.5">
                                      <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                        <Clock className="h-5 w-5 text-[#1a73c0]" />
                                      </div>
                                      <Select
                                        onValueChange={(value) => {
                                          field.onChange(value);
                                          console.log('Year changed to:', value);
                                        }}
                                        value={field.value}
                                        name="year"
                                      >
                                        <SelectTrigger
                                          id="year"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          aria-label="Academic year"
                                        >
                                          <SelectValue placeholder="Select academic year" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {years.map((year) => (
                                            <SelectItem key={year.uuid} value={year.uuid}>
                                              {year.year}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </FormControl>
                                  <FormMessage className="text-red-500 text-sm mt-1" />
                                  <p className="text-xs text-gray-500 mt-2">
                                    Select the academic year for your application. This field is required.
                                  </p>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="term"
                              render={({ field }) => (
                                <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                                  <FormLabel className="text-gray-700 font-medium">
                                    Term <span className="text-red-500">*</span>
                                    <span className="ml-1 text-xs text-red-500">(Required)</span>
                                  </FormLabel>
                                  <FormControl>
                                    <div className="relative mt-1.5">
                                      <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                        <Calendar className="h-5 w-5 text-[#1a73c0]" />
                                      </div>
                                      <Select
                                        onValueChange={(value) => {
                                          field.onChange(value);
                                          console.log('Term changed to:', value);
                                        }}
                                        value={field.value}
                                        name="term"
                                      >
                                        <SelectTrigger
                                          id="term"
                                          className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                          aria-label="Term"
                                        >
                                          <SelectValue placeholder="Select term" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {terms.map((term) => (
                                            <SelectItem key={term.id} value={term.id}>
                                              {term.name}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </FormControl>
                                  <FormMessage className="text-red-500 text-sm mt-1" />
                                  <p className="text-xs text-gray-500 mt-2">
                                    Select the term for your application. This field is required.
                                  </p>
                                </FormItem>
                              )}
                            />
                          </>
                        )}
                      </div>

                      <div className="flex justify-end space-x-6 pt-10 border-t mt-10 bg-gray-50 -mx-8 -mb-8 px-8 py-6 rounded-b-lg">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => navigate('/application/gat')}
                          className="px-8 py-3 border-gray-300 hover:bg-gray-50 transition-colors text-base"
                        >
                          Back
                        </Button>
                        <Button
                          type="submit"
                          disabled={isLoading}
                          className="px-8 py-3 bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-base"
                        >
                          {isLoading ? (
                            <span className="flex items-center">
                              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                              Saving...
                            </span>
                          ) : programSelectionExists && existingProgramSelection ? (
                            <span className="flex items-center">
                              Update and Continue
                              <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </span>
                          ) : (
                            <span className="flex items-center">
                              Save and Continue
                              <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </span>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
    </DashboardLayout>
  );
};

export default ProgramSelection;
