import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Loader2, FileText, ArrowLeft } from 'lucide-react';
import {
  publicAlumniApplicationsAPI,
  AlumniApplication,
  AlumniApplicationMini,
  ServiceType,
  College,
  Department
} from '@/services/alumniApplicationsAPI';

interface PublicAlumniApplicationFormProps {
  formType: 'form1' | 'form2';
}

const PublicAlumniApplicationFormSimple: React.FC<PublicAlumniApplicationFormProps> = ({ formType }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // Form data state
  const [formData, setFormData] = useState<Partial<AlumniApplication | AlumniApplicationMini>>({
    first_name: '',
    father_name: '',
    last_name: '',
    student_id: '',
    phone_number: '',
    email: '',
    admission_type: '',
    degree_type: '',
    college: '',
    department: '',
    is_other_college: false,
    other_college_name: '',
    other_department_name: '',
    student_status: '',
    current_year: '',
    year_of_leaving_ethiopian: '',
    year_of_leaving_gregorian: '',
    year_of_graduation_ethiopian: '',
    year_of_graduation_gregorian: '',
    service_type: '',
    application_status: 'Pending',
    payment_status: 'Pending',
  });

  // Lookup data
  const [serviceTypes, setServiceTypes] = useState<ServiceType[]>([]);
  const [colleges, setColleges] = useState<College[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);

  const isForm1 = formType === 'form1';

  // Load lookup data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [serviceTypesRes, collegesRes] = await Promise.all([
          publicAlumniApplicationsAPI.getServiceTypes(),
          publicAlumniApplicationsAPI.getColleges()
        ]);
        
        setServiceTypes(serviceTypesRes.data || []);
        setColleges(collegesRes.data || []);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load form data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Load departments when college changes
  useEffect(() => {
    const loadDepartments = async () => {
      if (formData.college && !formData.is_other_college) {
        try {
          const response = await publicAlumniApplicationsAPI.getDepartments(formData.college as string);
          setDepartments(response.data || []);
        } catch (error) {
          console.error('Error loading departments:', error);
          setDepartments([]);
        }
      } else {
        setDepartments([]);
      }
    };

    loadDepartments();
  }, [formData.college, formData.is_other_college]);

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);

      // Basic validation
      if (!formData.first_name || !formData.last_name || !formData.email || !formData.phone_number) {
        toast.error('Please fill in all required fields');
        return;
      }

      if (!formData.service_type) {
        toast.error('Please select a service type');
        return;
      }

      // Prepare data for submission
      const submissionData = { ...formData };

      // Submit application
      if (isForm1) {
        await publicAlumniApplicationsAPI.createApplication(submissionData);
      } else {
        await publicAlumniApplicationsAPI.createMiniApplication(submissionData);
      }

      toast.success('Application submitted successfully!');
      navigate('/services?success=true');

    } catch (error: any) {
      console.error('Error submitting application:', error);
      toast.error('Failed to submit application. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="bg-gradient-to-br from-[#1a73c0] to-[#155a9c] p-4 rounded-2xl w-20 h-20 mx-auto mb-6 flex items-center justify-center">
            <Loader2 className="h-10 w-10 animate-spin text-white" />
          </div>
          <p className="text-gray-600 text-lg font-medium">Loading application form...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 max-w-4xl py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="outline"
            onClick={() => navigate('/services')}
            className="mb-6 border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0] hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Button>

          <Card className="border-0 shadow-xl">
            <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#155a9c] text-white">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-white bg-opacity-20 rounded-xl">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold">Alumni Application</CardTitle>
                  <CardDescription className="text-blue-100">
                    {isForm1 ? 'Complete Application Form' : 'Simplified Application Form'}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Form */}
        <Card className="border-0 shadow-xl">
          <CardContent className="p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-[#1a73c0]">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="first_name">First Name *</Label>
                    <Input
                      id="first_name"
                      value={formData.first_name || ''}
                      onChange={(e) => handleInputChange('first_name', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="father_name">Father Name *</Label>
                    <Input
                      id="father_name"
                      value={formData.father_name || ''}
                      onChange={(e) => handleInputChange('father_name', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="last_name">Last Name *</Label>
                    <Input
                      id="last_name"
                      value={formData.last_name || ''}
                      onChange={(e) => handleInputChange('last_name', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email || ''}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone_number">Phone Number *</Label>
                    <Input
                      id="phone_number"
                      value={formData.phone_number || ''}
                      onChange={(e) => handleInputChange('phone_number', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="student_id">Student ID</Label>
                    <Input
                      id="student_id"
                      value={formData.student_id || ''}
                      onChange={(e) => handleInputChange('student_id', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Academic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-[#1a73c0]">Academic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="admission_type">Admission Type *</Label>
                    <Select value={formData.admission_type || ''} onValueChange={(value) => handleInputChange('admission_type', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select admission type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Regular">Regular</SelectItem>
                        <SelectItem value="Evening">Evening</SelectItem>
                        <SelectItem value="Summer">Summer</SelectItem>
                        <SelectItem value="Distance">Distance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="degree_type">Degree Type *</Label>
                    <Select value={formData.degree_type || ''} onValueChange={(value) => handleInputChange('degree_type', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select degree type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Diploma">Diploma</SelectItem>
                        <SelectItem value="Degree">Degree</SelectItem>
                        <SelectItem value="Master's">Master's</SelectItem>
                        <SelectItem value="PHD">PHD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="college">College *</Label>
                    <Select value={formData.college || ''} onValueChange={(value) => handleInputChange('college', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select college" />
                      </SelectTrigger>
                      <SelectContent>
                        {colleges.map((college) => (
                          <SelectItem key={college.id} value={college.id.toString()}>
                            {college.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="department">Department *</Label>
                    <Select value={formData.department || ''} onValueChange={(value) => handleInputChange('department', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((department) => (
                          <SelectItem key={department.id} value={department.id.toString()}>
                            {department.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Service Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-[#1a73c0]">Service Information</h3>
                <div>
                  <Label htmlFor="service_type">Service Type *</Label>
                  <Select value={formData.service_type || ''} onValueChange={(value) => handleInputChange('service_type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select service type" />
                    </SelectTrigger>
                    <SelectContent>
                      {serviceTypes.map((serviceType) => (
                        <SelectItem key={serviceType.id} value={serviceType.id.toString()}>
                          {serviceType.name} - ${serviceType.fee}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Submit Button */}
              <div className="pt-6">
                <Button
                  type="submit"
                  disabled={submitting}
                  className="w-full bg-gradient-to-r from-[#1a73c0] to-[#155a9c] hover:from-[#155a9c] hover:to-[#1a73c0] text-white py-3 text-lg font-semibold"
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Submitting Application...
                    </>
                  ) : (
                    'Submit Application'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PublicAlumniApplicationFormSimple;
