from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static

from user_management.views import CreateUserView, CurrentUserView
from rest_framework_simplejwt.views import TokenRefreshView
from user_management.jwt_serializers import CustomTokenObtainPairView
from user_management.csrf_views import get_csrf_token
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

# Swagger imports
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# Create schema view for Swagger documentation
schema_view = get_schema_view(
    openapi.Info(
        title="Online Application Portal API",
        default_version='v1',
        description="API documentation for the Online Application Portal",
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    # Admin URLs
    path('admin/', admin.site.urls),

    # Swagger URLs
    re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

    # API URLs
    path('api/csrf/', get_csrf_token, name='api-csrf-token'),  # Direct CSRF endpoint

    # Authentication endpoints
    path('api/auth/', include('djoser.urls')),  # Djoser user management endpoints
    path('api/auth/', include('djoser.urls.jwt')),  # Djoser JWT endpoints
    path('api/user/register/', CreateUserView.as_view(), name='user-register'),
    path('api/user/me/', CurrentUserView.as_view(), name='current-user'),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', csrf_exempt(TokenRefreshView.as_view()), name='token_refresh'),
    path('api-auth/', include('rest_framework.urls')),
    path('api/', include('setups.college.urls')),
    path('api/', include('setups.department.urls')),
    path('api/', include('setups.admission_type.urls')),
    path('api/', include('setups.application_information.urls')),
    path('api/', include('setups.program.urls')),
    path('api/', include('setups.registration_period.urls')),
    path('api/', include('setups.study_field.urls')),
    path('api/', include('setups.study_program.urls')),
    path('api/', include('setups.year.urls')),
    path('api/', include('setups.term.urls')),
    path('api/', include('setups.certificate_type.urls')),
    path('api/', include('setups.document_type.urls')),
    path('api/', include('setups.service_type.urls')),
    path('api/', include('official.urls')),
    path('api/', include('registration.urls')),
    path('api/', include('GraduateVerification.urls')),
    path('api/admin/', include('admin_utils.urls')),
    path('api/user/', include('user_management.urls')),
    path('api/settings/', include('settings_manager.urls')),
    path('api/', include('services.urls')),
    path('api/', include('service_requests.urls')),
    path('api/communication/', include('communication.urls')),
    path('api/', include('alumni_applications.urls')),
    path('api/', include('downloadable.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)



