from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuth<PERSON>icated, IsAdminUser
from rest_framework.response import Response
from django.core.mail import send_mail
from django.conf import settings
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from datetime import datetime

from .smtp_models import SMTPSettings
from .smtp_serializers import SMTPSettingsSerializer, SMTPTestSerializer

logger = logging.getLogger(__name__)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def smtp_settings_view(request):
    """Get or update SMTP settings"""
    if request.method == 'GET':
        try:
            smtp_settings = SMTPSettings.load()
            serializer = SMTPSettingsSerializer(smtp_settings)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error getting SMTP settings: {str(e)}")
            return Response(
                {'error': f'Failed to retrieve SMTP settings: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    elif request.method == 'POST':
        try:
            smtp_settings = SMTPSettings.load()
            serializer = SMTPSettingsSerializer(smtp_settings, data=request.data, partial=True)

            if serializer.is_valid():
                updated_settings = serializer.save()
                # Apply settings to Django configuration
                updated_settings.apply_to_settings()

                logger.info(f"SMTP settings updated by user {request.user.username}")
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error updating SMTP settings: {str(e)}")
            return Response(
                {'error': f'Failed to update SMTP settings: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def test_smtp_connection(request):
    """Test SMTP connection and send test email"""
    try:
        logger.info(f"SMTP test request data: {request.data}")
        serializer = SMTPTestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"SMTP test serializer errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Get current SMTP settings
        smtp_settings = SMTPSettings.load()
        recipient = serializer.validated_data['recipient']

        logger.info(f"SMTP settings loaded - Host: {smtp_settings.host}, Port: {smtp_settings.port}, From: {smtp_settings.from_email}, Username: {smtp_settings.username}, Has Password: {bool(smtp_settings.password)}")

        # Validate SMTP settings
        if not smtp_settings.host:
            logger.error("SMTP host is not configured")
            return Response(
                {'error': 'SMTP host is not configured'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not smtp_settings.from_email:
            logger.error("SMTP from_email is not configured")
            return Response(
                {'error': 'From email is not configured'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Test SMTP connection manually first
        import smtplib
        import ssl
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        try:
            # Create SMTP connection
            if smtp_settings.use_ssl:
                # SSL connection (port 465)
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(
                    smtp_settings.host,
                    smtp_settings.port,
                    context=context,
                    timeout=smtp_settings.timeout
                )
            else:
                # Regular connection (port 587 with TLS)
                server = smtplib.SMTP(
                    smtp_settings.host,
                    smtp_settings.port,
                    timeout=smtp_settings.timeout
                )

                if smtp_settings.use_tls:
                    server.starttls()

            # Authenticate if credentials provided
            if smtp_settings.username and smtp_settings.password:
                server.login(smtp_settings.username, smtp_settings.password)

            # Create test email
            msg = MIMEMultipart()
            msg['From'] = smtp_settings.from_email
            msg['To'] = recipient
            msg['Subject'] = "SMTP Configuration Test - University Portal"

            body = f"""
Hello,

This is a test email to verify your SMTP configuration for the University Portal.

SMTP Configuration Details:
- Host: {smtp_settings.host}
- Port: {smtp_settings.port}
- Username: {smtp_settings.username}
- From Email: {smtp_settings.from_email}
- TLS Enabled: {'Yes' if smtp_settings.use_tls else 'No'}
- SSL Enabled: {'Yes' if smtp_settings.use_ssl else 'No'}
- Timeout: {smtp_settings.timeout} seconds

If you received this email, your SMTP configuration is working correctly!

This email was sent on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by user: {request.user.username}

Best regards,
University Portal System
            """

            msg.attach(MIMEText(body, 'plain'))

            # Send email
            text = msg.as_string()
            server.sendmail(smtp_settings.from_email, [recipient], text)
            server.quit()

            logger.info(f"SMTP test successful for user {request.user.username} to {recipient}")
            return Response(
                {
                    'message': 'Test email sent successfully!',
                    'details': {
                        'recipient': recipient,
                        'from_email': smtp_settings.from_email,
                        'smtp_host': smtp_settings.host,
                        'smtp_port': smtp_settings.port
                    }
                },
                status=status.HTTP_200_OK
            )

        except smtplib.SMTPAuthenticationError as e:
            error_msg = f"SMTP Authentication failed: {str(e)}. Please check your username and password."
            logger.error(f"SMTP auth error for user {request.user.username}: {error_msg}")
            return Response({'error': error_msg}, status=status.HTTP_400_BAD_REQUEST)

        except smtplib.SMTPConnectError as e:
            error_msg = f"SMTP Connection failed: {str(e)}. Please check your host and port settings."
            logger.error(f"SMTP connection error for user {request.user.username}: {error_msg}")
            return Response({'error': error_msg}, status=status.HTTP_400_BAD_REQUEST)

        except smtplib.SMTPRecipientsRefused as e:
            error_msg = f"Recipient refused: {str(e)}. Please check the recipient email address."
            logger.error(f"SMTP recipient error for user {request.user.username}: {error_msg}")
            return Response({'error': error_msg}, status=status.HTTP_400_BAD_REQUEST)

        except smtplib.SMTPServerDisconnected as e:
            error_msg = f"SMTP Server disconnected: {str(e)}. Please try again."
            logger.error(f"SMTP disconnect error for user {request.user.username}: {error_msg}")
            return Response({'error': error_msg}, status=status.HTTP_400_BAD_REQUEST)

        except smtplib.SMTPException as e:
            error_msg = f"SMTP error: {str(e)}"
            logger.error(f"SMTP general error for user {request.user.username}: {error_msg}")
            return Response({'error': error_msg}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(f"SMTP unexpected error for user {request.user.username}: {error_msg}")
            return Response({'error': error_msg}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error in test_smtp_connection: {str(e)}")
        return Response(
            {'error': f'Failed to test SMTP connection: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def smtp_debug_info(request):
    """Get SMTP debug information"""
    try:
        smtp_settings = SMTPSettings.load()
        logger.info(f"Debug info requested - SMTP settings: Host={smtp_settings.host}, Port={smtp_settings.port}, Provider={smtp_settings.provider}")

        debug_info = {
            'smtp_configured': bool(smtp_settings.host and smtp_settings.from_email),
            'provider': smtp_settings.provider,
            'host': smtp_settings.host,
            'port': smtp_settings.port,
            'username': smtp_settings.username,
            'from_email': smtp_settings.from_email,
            'use_tls': smtp_settings.use_tls,
            'use_ssl': smtp_settings.use_ssl,
            'timeout': smtp_settings.timeout,
            'has_password': bool(smtp_settings.password),
            'password_length': len(smtp_settings.password) if smtp_settings.password else 0,
            'created_at': smtp_settings.created_at,
            'updated_at': smtp_settings.updated_at,
        }

        return Response(debug_info, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error getting SMTP debug info: {str(e)}")
        return Response(
            {'error': f'Failed to get SMTP debug info: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def smtp_provider_configs(request):
    """Get available SMTP provider configurations"""
    try:
        providers = {
            'gmail': {
                'name': 'Gmail',
                'host': 'smtp.gmail.com',
                'port': 587,
                'use_tls': True,
                'use_ssl': False,
                'timeout': 60,
                'instructions': {
                    'title': 'Gmail SMTP Setup',
                    'steps': [
                        'Enable 2-Factor Authentication on your Gmail account',
                        'Generate an App Password for this application',
                        'Use your Gmail address as username',
                        'Use the App Password (not your regular password)',
                        'Ensure "Less secure app access" is enabled if not using App Password'
                    ],
                    'notes': 'Gmail requires App Passwords for enhanced security'
                }
            },
            'office365': {
                'name': 'Office 365',
                'host': 'smtp.office365.com',
                'port': 587,
                'use_tls': True,
                'use_ssl': False,
                'timeout': 60,
                'instructions': {
                    'title': 'Office 365 SMTP Setup',
                    'steps': [
                        'Use your full Office 365 email address as username',
                        'Use your Office 365 password or App Password',
                        'Ensure SMTP authentication is enabled in your Office 365 admin',
                        'Check that your account has permission to send via SMTP',
                        'Consider using App Passwords for enhanced security'
                    ],
                    'notes': 'Office 365 may require modern authentication for some accounts'
                }
            },
            'custom': {
                'name': 'Custom SMTP',
                'host': '',
                'port': 587,
                'use_tls': True,
                'use_ssl': False,
                'timeout': 60,
                'instructions': {
                    'title': 'Custom SMTP Setup',
                    'steps': [
                        'Enter your SMTP server hostname',
                        'Configure the appropriate port (587 for TLS, 465 for SSL, 25 for plain)',
                        'Set up authentication credentials',
                        'Choose appropriate security settings (TLS/SSL)',
                        'Test the configuration before saving'
                    ],
                    'notes': 'Contact your email provider for specific SMTP settings'
                }
            }
        }

        return Response(providers, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error getting SMTP provider configs: {str(e)}")
        return Response(
            {'error': f'Failed to get provider configurations: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated, IsAdminUser])
def apply_provider_config(request):
    """Apply provider-specific configuration"""
    try:
        provider = request.data.get('provider')
        if not provider:
            return Response(
                {'error': 'Provider is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        smtp_settings = SMTPSettings.load()
        smtp_settings.provider = provider
        smtp_settings.apply_provider_defaults()
        smtp_settings.save()

        serializer = SMTPSettingsSerializer(smtp_settings)
        logger.info(f"Provider configuration applied: {provider} by user {request.user.username}")

        return Response({
            'message': f'{provider.title()} configuration applied successfully',
            'settings': serializer.data,
            'instructions': smtp_settings.get_provider_instructions()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error applying provider config: {str(e)}")
        return Response(
            {'error': f'Failed to apply provider configuration: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

