import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Info, FileText, Calculator, Hash, Award, User, AlertTriangle } from 'lucide-react';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from 'sonner';
import { applicationAPI } from '@/services/api';

// Define the schema for the form
const gatSchema = z.object({
  GAT_No: z.string()
    .min(1, 'GAT No is required')
    .max(12, 'GAT No must be at most 12 characters')
    .refine(val => /^[A-Za-z0-9-]+$/.test(val), {
      message: 'GAT No should only contain letters, numbers, and hyphens'
    }),
  GAT_Result: z.string().refine(score => {
    const numScore = parseFloat(score);
    return !isNaN(numScore) && numScore >= 0 && numScore <= 100;
  }, { message: 'GAT score must be between 0 and 100' }),
});

type GATFormValues = z.infer<typeof gatSchema>;

const GAT = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [existingGAT, setExistingGAT] = useState<any>(null);
  const [isNewApplication, setIsNewApplication] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Parse query parameters
  const queryParams = new URLSearchParams(location.search);
  const isNew = queryParams.get('new') === 'true';
  const gatId = queryParams.get('id');
  const isEdit = queryParams.get('edit') === 'true';

  // Track if this is an edit operation for a specific GAT record
  const isEditingSpecificGAT = isEdit && gatId;

  // Initialize form with default values
  const form = useForm<GATFormValues>({
    resolver: zodResolver(gatSchema),
    defaultValues: {
      GAT_No: '',
      GAT_Result: '',
    },
  });

  // Fetch existing GAT information if available
  useEffect(() => {
    const fetchGAT = async () => {
      try {
        // If this is editing a specific GAT record
        if (isEditingSpecificGAT) {
          console.log('Editing specific GAT with ID:', gatId);

          try {
            // Get the specific GAT entry by ID
            const response = await applicationAPI.getGAT(gatId);
            console.log('Fetched specific GAT for editing:', response.data);

            if (response.data) {
              const gat = response.data;
              setExistingGAT(gat);

              // Update form with the values from the specific GAT record
              form.reset({
                GAT_No: gat.GAT_No || '',
                GAT_Result: gat.GAT_Result ? gat.GAT_Result.toString() : '',
              });
            }
          } catch (error) {
            console.error('Error fetching specific GAT for editing:', error);
            toast.error('Failed to load GAT information for editing.');
            navigate('/application/status');
          }
        }
        // If this is a new application without a specific GAT ID
        else if (isNew) {
          console.log('Creating new application without GAT ID - using empty form');
          setIsNewApplication(true);

          // Always use empty values for a new application, regardless of previous GAT records
          form.reset({
            GAT_No: '',
            GAT_Result: '',
          });

          // Don't set existingGAT for new applications
          setExistingGAT(null);
        }
        // If this is not explicitly a new application, check for existing GAT
        else {
          console.log('Checking for existing GAT');
          const response = await applicationAPI.getCurrentGAT();
          console.log('Fetched GAT info:', response.data);

          if (response.data && response.data.length > 0) {
            const gat = response.data[0];
            setExistingGAT(gat);
            console.log('Existing GAT:', gat);

            // Update form values
            form.reset({
              GAT_No: gat.GAT_No || '',
              GAT_Result: gat.GAT_Result ? gat.GAT_Result.toString() : '',
            });
          }
        }
      } catch (error) {
        console.error('Error in GAT initialization:', error);
        toast.error('Failed to initialize GAT form. Please try again.');
      }
    };

    fetchGAT();
  }, [form, isNew, gatId, isEditingSpecificGAT, navigate]);

  const onSubmit = async (data: GATFormValues) => {
    setIsLoading(true);

    try {
      // Check if user is authenticated
      const token = localStorage.getItem('token');
      if (!token) {
        toast.error('You are not authenticated. Please log in again.');
        navigate('/login');
        return;
      }

      // Format the data for the API
      const formattedData = {
        GAT_No: data.GAT_No.trim(),
        // Convert to integer as the backend expects a PositiveIntegerField
        GAT_Result: Math.round(parseFloat(data.GAT_Result)),
      };

      // Validate GAT number format
      if (!formattedData.GAT_No.match(/^[A-Za-z0-9-]+$/)) {
        toast.error('GAT Number should only contain letters, numbers, and hyphens.');
        setIsLoading(false);
        return;
      }

      console.log('Formatted GAT data:', formattedData);

      console.log('Submitting GAT data:', formattedData);

      try {
        let response;

        // If this is editing a specific GAT record
        if (isEditingSpecificGAT && gatId) {
          console.log(`Updating specific GAT with ID ${gatId}`);
          try {
            response = await applicationAPI.updateGAT(gatId, formattedData);
            console.log('Updated specific GAT:', response);

            if (response.status === 200 || response.status === 201) {
              toast.success('GAT information updated successfully!');
              // For editing a specific GAT, continue to the program selection page
              // Pass the GAT ID to the program selection page
              navigate(`/application/program-selection?edit=true&gat=${gatId}`);
              return; // Exit early to prevent further navigation
            }
          } catch (error: any) {
            console.error('Error updating GAT record:', error);
            setIsLoading(false);

            // Check if the error is due to a duplicate GAT number
            if (error.response?.data?.GAT_No) {
              const errorMessage = Array.isArray(error.response.data.GAT_No)
                ? error.response.data.GAT_No[0]
                : error.response.data.GAT_No;

              console.log('GAT Number error message:', errorMessage);

              // Show the exact error message from the backend
              toast.error(
                <div className="flex flex-col space-y-2">
                  <span className="font-semibold text-red-700">GAT Number Error</span>
                  <span className="text-sm">{errorMessage}</span>
                </div>,
                {
                  duration: 5000,
                  position: 'top-center',
                  icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                }
              );

              // Focus the GAT_No field for easier correction
              setTimeout(() => {
                const gatNoInput = document.querySelector('input[name="GAT_No"]');
                if (gatNoInput) {
                  (gatNoInput as HTMLInputElement).focus();
                  (gatNoInput as HTMLInputElement).select();
                }
              }, 500);

              return; // Exit early to prevent further processing
            } else if (error.response?.status === 400) {
              // Handle other 400 Bad Request errors
              toast.error(
                <div className="flex flex-col space-y-2">
                  <span className="font-semibold text-red-700">Application Error</span>
                  <span className="text-sm">
                    There was a problem updating your application. Please check your information and try again.
                  </span>
                </div>,
                {
                  duration: 5000,
                  position: 'top-center',
                  icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                }
              );

              return; // Exit early to prevent further processing
            }

            // For other errors, show a generic message
            toast.error(
              <div className="flex flex-col space-y-2">
                <span className="font-semibold text-red-700">Failed to Update Application</span>
                <span className="text-sm">
                  There was a problem updating your application. Please check your information and try again.
                </span>
              </div>,
              {
                duration: 5000,
                position: 'top-center',
                icon: <AlertTriangle className="h-5 w-5 text-red-600" />
              }
            );
            return; // Exit early to prevent further processing
          }
        }
        // If this is a new application
        else if (isNewApplication || isNew) {
          console.log('Creating a new GAT record for a new application');

          try {
            // Add metadata to ensure uniqueness
            const newApplicationData = {
              ...formattedData,
              _timestamp: Date.now(),
              _new_application: true
            };

            // Always create a new GAT record for a new application, never update an existing one
            response = await applicationAPI.submitGAT(newApplicationData);
            console.log('Created new GAT record for application:', response);
          } catch (error: any) {
            console.error('Error creating GAT record:', error);
            setIsLoading(false);

            // Check if the error is due to a duplicate GAT number
            if (error.response?.data?.GAT_No) {
              const errorMessage = Array.isArray(error.response.data.GAT_No)
                ? error.response.data.GAT_No[0]
                : error.response.data.GAT_No;

              console.log('GAT Number error message:', errorMessage);

              // Show the exact error message from the backend
              toast.error(
                <div className="flex flex-col space-y-2">
                  <span className="font-semibold text-red-700">GAT Number Error</span>
                  <span className="text-sm">{errorMessage}</span>
                </div>,
                {
                  duration: 5000,
                  position: 'top-center',
                  icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                }
              );

              // Focus the GAT_No field for easier correction
              setTimeout(() => {
                const gatNoInput = document.querySelector('input[name="GAT_No"]');
                if (gatNoInput) {
                  (gatNoInput as HTMLInputElement).focus();
                  (gatNoInput as HTMLInputElement).select();
                }
              }, 500);

              return; // Exit the function to prevent further processing
            } else if (error.response?.status === 400) {
              // Handle other 400 Bad Request errors
              toast.error(
                <div className="flex flex-col space-y-2">
                  <span className="font-semibold text-red-700">Application Error</span>
                  <span className="text-sm">
                    There was a problem with your application. Please check your information and try again.
                  </span>
                </div>,
                {
                  duration: 5000,
                  position: 'top-center',
                  icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                }
              );

              return; // Exit the function to prevent further processing
            } else {
              toast.error(
                <div className="flex flex-col space-y-2">
                  <span className="font-semibold text-red-700">Failed to Create Application</span>
                  <span className="text-sm">
                    There was a problem creating your application. Please check your information and try again.
                  </span>
                </div>,
                {
                  duration: 5000,
                  position: 'top-center',
                  icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                }
              );
              return; // Exit the function to prevent further processing
            }
          }
        }
        // If this is updating an existing application
        else if (existingGAT) {
          // Update existing GAT
          console.log(`Updating existing GAT with ID ${existingGAT.id}`);
          response = await applicationAPI.updateGAT(existingGAT.id, formattedData);
          console.log('Updated existing GAT:', response);
        }
        // Fallback case - create new GAT
        else {
          // Create new GAT
          console.log('Creating new GAT record (fallback case)');
          response = await applicationAPI.submitGAT(formattedData);
          console.log('Created new GAT (fallback case):', response);
        }

        if (response.status === 200 || response.status === 201) {
          // If this is a new application, redirect back to the application status page
          if (isNewApplication || isNew) {
            toast.success('New application created successfully!');
            // Redirect to the application status page
            navigate('/application/status');
          } else {
            toast.success('GAT information updated successfully!');
            // For updates, continue to the program selection page
            navigate('/application/program-selection');
          }
        } else {
          toast.error('Failed to submit GAT information. Please try again.');
        }
      } catch (apiError: any) {
        console.error('API submission error:', apiError);
        setIsLoading(false);

        // Handle specific error messages
        if (apiError.response?.data) {
          const errorData = apiError.response.data;
          console.error('Error data:', errorData);

          // If the error is a string
          if (typeof errorData === 'string') {
            toast.error(errorData, {
              position: 'top-center',
              icon: <AlertTriangle className="h-5 w-5 text-red-600" />
            });
          }
          // If the error is an object with field-specific errors
          else if (typeof errorData === 'object') {
            // Check specifically for GAT_No errors first
            if (errorData.GAT_No) {
              const gatError = Array.isArray(errorData.GAT_No) ? errorData.GAT_No[0] : errorData.GAT_No;

              console.log('GAT Number error message from main catch block:', gatError);

              // Show the exact error message from the backend
              toast.error(
                <div className="flex flex-col space-y-2">
                  <span className="font-semibold text-red-700">GAT Number Error</span>
                  <span className="text-sm">{gatError}</span>
                </div>,
                {
                  duration: 5000,
                  position: 'top-center',
                  icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                }
              );

              // Focus the GAT_No field
              setTimeout(() => {
                const gatNoInput = document.querySelector('input[name="GAT_No"]');
                if (gatNoInput) {
                  (gatNoInput as HTMLInputElement).focus();
                  (gatNoInput as HTMLInputElement).select();
                }
              }, 500);
            } else {
              // Display each field error
              Object.entries(errorData).forEach(([field, errors]) => {
                console.log(`Field error: ${field}`, errors);
                if (Array.isArray(errors) && errors.length > 0) {
                  toast.error(`${field}: ${errors[0]}`, {
                    position: 'top-center',
                    icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                  });
                } else if (typeof errors === 'string') {
                  toast.error(`${field}: ${errors}`, {
                    position: 'top-center',
                    icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                  });
                } else if (errors && typeof errors === 'object') {
                  // Handle nested error objects
                  toast.error(`${field}: ${JSON.stringify(errors)}`, {
                    position: 'top-center',
                    icon: <AlertTriangle className="h-5 w-5 text-red-600" />
                  });
                }
              });
            }
          }
        } else if (apiError.response?.status === 400) {
          // Handle 400 Bad Request errors
          toast.error(
            <div className="flex flex-col space-y-2">
              <span className="font-semibold text-red-700">Application Error</span>
              <span className="text-sm">
                There was a problem with your application. The GAT number may already be in use. Please check your information and try again.
              </span>
            </div>,
            {
              duration: 5000,
              position: 'top-center',
              icon: <AlertTriangle className="h-5 w-5 text-red-600" />
            }
          );
        } else {
          toast.error(
            <div className="flex flex-col space-y-2">
              <span className="font-semibold text-red-700">GAT Information Error</span>
              <span className="text-sm">
                There was a problem with your GAT information. The GAT number may already be in use. Please check your information and try again with a unique GAT number.
              </span>
            </div>,
            {
              duration: 5000,
              position: 'top-center',
              icon: <AlertTriangle className="h-5 w-5 text-red-600" />
            }
          );
        }
        return; // Exit the function to prevent further processing
      }
    } catch (error: any) {
      // This catch block will handle any errors not caught by the inner try-catch
      console.error('Unhandled error submitting GAT information:', error);
      setIsLoading(false);

      // Check if this is a response with data
      if (error.response?.data) {
        // Check for GAT_No errors
        if (error.response.data.GAT_No) {
          const errorMessage = Array.isArray(error.response.data.GAT_No)
            ? error.response.data.GAT_No[0]
            : error.response.data.GAT_No;

          console.log('GAT Number error message from outer catch block:', errorMessage);

          toast.error(
            <div className="flex flex-col space-y-2">
              <span className="font-semibold text-red-700">GAT Number Error</span>
              <span className="text-sm">{errorMessage}</span>
            </div>,
            {
              duration: 5000,
              position: 'top-center',
              icon: <AlertTriangle className="h-5 w-5 text-red-600" />
            }
          );
        } else {
          // Generic error with response data
          toast.error(
            <div className="flex flex-col space-y-2">
              <span className="font-semibold text-red-700">Application Error</span>
              <span className="text-sm">
                There was a problem with your application. Please check your information and try again.
              </span>
            </div>,
            {
              duration: 5000,
              position: 'top-center',
              icon: <AlertTriangle className="h-5 w-5 text-red-600" />
            }
          );
        }
      } else {
        // Completely unhandled error
        toast.error(
          <div className="flex flex-col space-y-2">
            <span className="font-semibold text-red-700">Application Error</span>
            <span className="text-sm">
              An unexpected error occurred while processing your GAT information. This may be due to a duplicate GAT number. Please check your information and try again with a unique GAT number.
            </span>
          </div>,
          {
            duration: 5000,
            position: 'top-center',
            icon: <AlertTriangle className="h-5 w-5 text-red-600" />
          }
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        {/* Hero Section */}
        <Card className="shadow-2xl border-0 overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] p-6 relative">
            <div className="absolute top-0 left-0 w-full h-full opacity-10 animate-pattern-shift"
                 style={{ backgroundImage: "url('/images/pattern-bg.svg')", backgroundSize: "cover" }}></div>
            <div className="relative z-10 flex items-center">
              <div className="bg-white p-3 rounded-full shadow-md mr-4 ring-4 ring-white/30">
                <Award className="h-8 w-8 text-[#1a73c0]" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-white">Graduate Admission Test (GAT)</CardTitle>
                <CardDescription className="text-blue-100 mt-1">
                  {isNewApplication || isNew
                    ? 'Create a new application'
                    : existingGAT
                      ? 'Update your GAT information'
                      : 'Please provide your GAT information'}
                </CardDescription>
                {isNewApplication || isNew ? (
                  <div className="mt-2 text-sm bg-green-500/30 text-white p-2 rounded-md inline-flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    You are creating a new application
                  </div>
                ) : existingGAT && (
                  <div className="mt-2 text-sm bg-white/20 text-white p-2 rounded-md inline-flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    You are editing your previously submitted information
                  </div>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <div className="space-y-6">
                  <div className="flex items-center">
                    <div className="bg-[#1a73c0]/10 p-2 rounded-full mr-2">
                      <FileText className="h-5 w-5 text-[#1a73c0]" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800">GAT Details</h3>
                    <div className="h-px flex-1 bg-gray-200 ml-3"></div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <FormField
                      control={form.control}
                      name="GAT_No"
                      render={({ field }) => (
                        <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                          <FormLabel className="text-gray-700 font-medium">GAT Number *</FormLabel>
                          <FormControl>
                            <div className="relative mt-1.5">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                <Hash className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <Input
                                placeholder="Enter your GAT Number"
                                className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                maxLength={12}
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500 text-sm mt-1" />
                          <p className="text-xs text-gray-500 mt-2">Enter the unique identification number from your GAT certificate</p>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="GAT_Result"
                      render={({ field }) => (
                        <FormItem className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 transition-all">
                          <FormLabel className="text-gray-700 font-medium">GAT Score *</FormLabel>
                          <FormControl>
                            <div className="relative mt-1.5">
                              <div className="absolute left-0 top-0 bottom-0 w-10 flex items-center justify-center bg-blue-50 border-r border-gray-200 rounded-l-md">
                                <Calculator className="h-5 w-5 text-[#1a73c0]" />
                              </div>
                              <Input
                                placeholder="Enter your GAT score (0-100)"
                                type="number"
                                min="0"
                                max="100"
                                step="0.01"
                                className="pl-12 border-gray-200 focus:border-[#1a73c0] focus:ring-[#1a73c0]/20"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-red-500 text-sm mt-1" />
                          <p className="text-xs text-gray-500 mt-2">Enter your score as a number between 0 and 100</p>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-6 pt-10 border-t mt-10 bg-gray-50 -mx-8 -mb-8 px-8 py-6 rounded-b-lg">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/application/status')}
                    className="px-8 py-3 border-gray-300 hover:bg-gray-50 transition-colors text-base"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="px-8 py-3 bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-base"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <span className="flex items-center">
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Saving...
                      </span>
                    ) : isNewApplication || isNew ? (
                      <span className="flex items-center">
                        Create New Application
                        <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </span>
                    ) : existingGAT ? (
                      <span className="flex items-center">
                        Update and Continue
                        <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </span>
                    ) : (
                      <span className="flex items-center">
                        Save and Continue
                        <svg className="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </span>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default GAT;
