from django.contrib import admin
from .models import OfficialSent, OfficialReceived


@admin.register(OfficialSent)
class OfficialSentAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'gender', 'receiver_institute', 'send_date', 'certificate_type', 'tracking_number']
    list_filter = ['gender', 'certificate_type', 'send_date', 'courier']
    search_fields = ['first_name', 'last_name', 'receiver_institute', 'tracking_number']
    ordering = ['-send_date']
    date_hierarchy = 'send_date'

    def full_name(self, obj):
        names = [obj.first_name]
        if obj.second_name:
            names.append(obj.second_name)
        names.append(obj.last_name)
        return ' '.join(names)
    full_name.short_description = 'Full Name'


@admin.register(OfficialReceived)
class OfficialReceivedAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'gender', 'sender_institute', 'arival_date', 'certificate_type', 'tracking_number']
    list_filter = ['gender', 'certificate_type', 'arival_date', 'courier']
    search_fields = ['first_name', 'last_name', 'sender_institute', 'tracking_number']
    ordering = ['-arival_date']
    date_hierarchy = 'arival_date'

    def full_name(self, obj):
        names = [obj.first_name]
        if obj.second_name:
            names.append(obj.second_name)
        names.append(obj.last_name)
        return ' '.join(names)
    full_name.short_description = 'Full Name'
