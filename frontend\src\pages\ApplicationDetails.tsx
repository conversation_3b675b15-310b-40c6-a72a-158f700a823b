import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import {
  FileText,
  User,
  Calendar,
  GraduationCap,
  Building,
  Building2,
  BookOpen,
  Award,
  ArrowLeft,
  Download,
  ExternalLink,
  Loader2
} from 'lucide-react';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { applicationAPI } from '@/services/api';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";

interface ApplicationDetailsProps {}

const ApplicationDetails: React.FC<ApplicationDetailsProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [programSelection, setProgramSelection] = useState<any>(null);
  const [applicationInfo, setApplicationInfo] = useState<any>(null);
  const [gatInfo, setGatInfo] = useState<any>(null);
  const [applicantInfo, setApplicantInfo] = useState<any>(null);
  const [documentation, setDocumentation] = useState<any>(null);
  const [payment, setPayment] = useState<any>(null);

  useEffect(() => {
    const fetchApplicationDetails = async () => {
      if (!id) {
        setError('No application ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch program selection details
        const programSelectionResponse = await applicationAPI.getProgramSelection(id);
        const programSelectionData = programSelectionResponse.data;
        setProgramSelection(programSelectionData);

        // Fetch related data in parallel, with error handling for payment
        let applicationInfoResponse, gatInfoResponse, applicantInfoResponse, documentationResponse, paymentResponse;

        try {
          // Fetch all data except payment in parallel
          [
            applicationInfoResponse,
            gatInfoResponse,
            applicantInfoResponse,
            documentationResponse
          ] = await Promise.all([
            // Fetch application info if available
            programSelectionData.application_info
              ? applicationAPI.getApplicationInfo(programSelectionData.application_info)
              : Promise.resolve({ data: null }),

            // Fetch GAT info if available
            programSelectionData.gat
              ? applicationAPI.getGAT(programSelectionData.gat)
              : Promise.resolve({ data: null }),

            // Fetch applicant personal info
            applicationAPI.getCurrentApplicantInfo(),

            // Fetch documentation
            applicationAPI.getCurrentDocumentation()
          ]);

          // Set default payment response
          paymentResponse = { data: null };

          // Skip payment fetch entirely - we know it's causing errors
          // This is a temporary fix until the database schema is updated
          console.log('Skipping payment data fetch due to known database schema issues');
        } catch (dataError) {
          throw dataError; // Re-throw to be caught by the outer try-catch
        }

        // Set the fetched data
        setApplicationInfo(applicationInfoResponse.data);
        setGatInfo(gatInfoResponse.data);

        // For applicant info, get the first item if it's an array
        if (applicantInfoResponse.data && Array.isArray(applicantInfoResponse.data) && applicantInfoResponse.data.length > 0) {
          setApplicantInfo(applicantInfoResponse.data[0]);
        } else {
          setApplicantInfo(applicantInfoResponse.data);
        }

        // For documentation, use the first record since each user can have at most one documentation record
        if (documentationResponse.data && Array.isArray(documentationResponse.data) && documentationResponse.data.length > 0) {
          // Use the first documentation record
          setDocumentation(documentationResponse.data[0]);
          console.log('Using documentation record:', documentationResponse.data[0]);
        } else if (documentationResponse.data && !Array.isArray(documentationResponse.data)) {
          // If it's not an array, use it directly
          setDocumentation(documentationResponse.data);
          console.log('Using non-array documentation:', documentationResponse.data);
        } else {
          // No documentation found
          setDocumentation(null);
          console.log('No documentation records found');
        }

        // For payment, find the one matching this application's application number
        if (paymentResponse.data && Array.isArray(paymentResponse.data)) {
          const relevantPayment = paymentResponse.data.find(
            (pay: any) => pay.application_num && pay.application_num.toString() === programSelectionData.id?.toString()
          );
          setPayment(relevantPayment || null);
        } else {
          setPayment(paymentResponse.data);
        }

      } catch (error) {
        console.error('Error fetching application details:', error);
        setError('Failed to load application details. Please try again later.');
        toast.error('Failed to load application details');
      } finally {
        setLoading(false);
      }
    };

    fetchApplicationDetails();
  }, [id]);

  // Helper function to format dates
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Helper function to get status badge color
  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">{status}</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">{status}</Badge>;
      case 'pending':
      default:
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">{status || 'Pending'}</Badge>;
    }
  };

  // Helper function to get file name from URL
  const getFileNameFromUrl = (url: string) => {
    if (!url) return '';
    const parts = url.split('/');
    return parts[parts.length - 1];
  };

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">


        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-[#1a73c0] mr-2" />
            <span className="text-lg text-gray-600">Loading application details...</span>
          </div>
        ) : error ? (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-700">Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-600">{error}</p>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                onClick={() => navigate('/application/status')}
              >
                Return to Applications
              </Button>
            </CardFooter>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Page Header */}
            <div className="mb-8 overflow-hidden rounded-lg shadow-md">
              <div className="bg-gradient-to-r from-[#1a73c0] to-[#0d4a8b] px-6 py-8 text-white">
                <div className="flex justify-between items-center">
                  <div>
                    <h1 className="text-2xl font-bold">Application #{programSelection?.application_num}</h1>
                    <div className="flex items-center mt-2 text-blue-100">
                      <Calendar className="h-4 w-4 mr-2" />
                      Submitted on {formatDate(programSelection?.created_at)}
                    </div>
                  </div>
                  <div>
                    {getStatusBadge(programSelection?.registrar_off_status || 'Pending')}
                  </div>
                </div>
              </div>
            </div>

            {/* Application Overview Card */}
            <Card className="shadow-md border-blue-100">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                <CardTitle className="flex items-center">
                  <GraduationCap className="h-5 w-5 mr-2 text-[#1a73c0]" />
                  Application Overview
                </CardTitle>
                <CardDescription>
                  Summary of your application details
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Program Information */}
                  <div>
                    <h3 className="text-lg font-medium mb-4 flex items-center">
                      <GraduationCap className="h-5 w-5 mr-2 text-[#1a73c0]" />
                      Program Information
                    </h3>
                    {applicationInfo ? (
                      <div className="space-y-4">
                        {/* Program Details */}
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
                          <h4 className="font-medium text-[#1a73c0] mb-3">Program Details</h4>
                          <div className="space-y-2">
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-600">Program:</span>
                              <span className="col-span-2 font-medium">{applicationInfo.program?.program_name}</span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-600">Admission Type:</span>
                              <span className="col-span-2">{applicationInfo.admission_type?.name}</span>
                            </div>
                          </div>
                        </div>

                        {/* Institution Details */}
                        <div className="border-l-4 border-blue-300 pl-4">
                          <h4 className="font-medium text-gray-700 mb-3">Institution Details</h4>
                          <div className="space-y-2">
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-500">College:</span>
                              <span className="col-span-2">{applicationInfo.college?.name}</span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-500">Department:</span>
                              <span className="col-span-2">{applicationInfo.department?.name}</span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-500">Field of Study:</span>
                              <span className="col-span-2">{applicationInfo.field_of_study?.name}</span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-500">Study Program:</span>
                              <span className="col-span-2">{applicationInfo.study_program?.name}</span>
                            </div>
                          </div>
                        </div>

                        {/* Application Details */}
                        <div className="border-l-4 border-green-300 pl-4">
                          <h4 className="font-medium text-gray-700 mb-3">Application Details</h4>
                          <div className="space-y-2">
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-500">Sponsorship:</span>
                              <span className="col-span-2 font-medium">{programSelection?.sponsorship || 'N/A'}</span>
                            </div>
                            {programSelection?.year_name && (
                              <div className="grid grid-cols-3 gap-2">
                                <span className="text-gray-500">Academic Year:</span>
                                <span className="col-span-2">{programSelection.year_name}</span>
                              </div>
                            )}
                            {programSelection?.term_name && (
                              <div className="grid grid-cols-3 gap-2">
                                <span className="text-gray-500">Term:</span>
                                <span className="col-span-2">{programSelection.term_name}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="p-6 text-center">
                        <GraduationCap className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500 italic">Program information not available</p>
                        <p className="text-sm text-gray-400 mt-2">The program information for this application could not be found.</p>
                      </div>
                    )}
                  </div>

                  {/* Applicant Information */}
                  <div>
                    <h3 className="text-lg font-medium mb-4 flex items-center">
                      <User className="h-5 w-5 mr-2 text-[#1a73c0]" />
                      Applicant Information
                    </h3>
                    {applicantInfo ? (
                      <div className="space-y-4">
                        {/* Personal Information */}
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                          <h4 className="font-medium text-[#1a73c0] mb-3">Personal Details</h4>
                          <div className="space-y-2">
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-600">Name:</span>
                              <span className="col-span-2 font-medium">
                                {applicantInfo.first_name} {applicantInfo.last_name} {applicantInfo.grandfather_name}
                              </span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-600">Gender:</span>
                              <span className="col-span-2">{applicantInfo.gender}</span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-600">Date of Birth:</span>
                              <span className="col-span-2">{formatDate(applicantInfo.dob)}</span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-600">Mobile:</span>
                              <span className="col-span-2">{applicantInfo.mobile}</span>
                            </div>
                            <div className="grid grid-cols-3 gap-2">
                              <span className="text-gray-600">Program Level:</span>
                              <span className="col-span-2 font-medium">{applicantInfo.program_level}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="p-6 text-center">
                        <User className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500 italic">Applicant information not available</p>
                        <p className="text-sm text-gray-400 mt-2">The applicant's personal information could not be found.</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* GAT Information */}
            <Card className="shadow-md border-green-100">
              <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50 border-b border-green-100">
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2 text-green-700" />
                  Graduate Admission Test (GAT) Information
                </CardTitle>
                <CardDescription>
                  Details of your Graduate Admission Test record
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {gatInfo ? (
                  <div className="space-y-4">
                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h4 className="font-medium text-green-700 mb-3">GAT Details</h4>
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">GAT Number:</span>
                          <span className="col-span-2 font-medium">{gatInfo.GAT_No}</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">GAT Score:</span>
                          <div className="col-span-2 flex items-center">
                            <span className="font-medium">{gatInfo.GAT_Result}</span>
                            <div className="ml-2 bg-green-100 px-2 py-1 rounded-full text-xs text-green-800">
                              out of 100
                            </div>
                            {gatInfo.GAT_Result >= 70 && (
                              <div className="ml-2 bg-green-500 px-2 py-1 rounded-full text-xs text-white">
                                Excellent
                              </div>
                            )}
                            {gatInfo.GAT_Result >= 50 && gatInfo.GAT_Result < 70 && (
                              <div className="ml-2 bg-blue-500 px-2 py-1 rounded-full text-xs text-white">
                                Good
                              </div>
                            )}
                            {gatInfo.GAT_Result < 50 && (
                              <div className="ml-2 bg-yellow-500 px-2 py-1 rounded-full text-xs text-white">
                                Passing
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Score Visualization */}
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium text-gray-700 mb-3">Score Visualization</h4>
                      <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
                        <div
                          className={`h-4 rounded-full ${
                            gatInfo.GAT_Result >= 70 ? 'bg-green-500' :
                            gatInfo.GAT_Result >= 50 ? 'bg-blue-500' : 'bg-yellow-500'
                          }`}
                          style={{ width: `${gatInfo.GAT_Result}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>0</span>
                        <span>25</span>
                        <span>50</span>
                        <span>75</span>
                        <span>100</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-6 text-center">
                    <Award className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 italic">GAT information not available</p>
                    <p className="text-sm text-gray-400 mt-2">The Graduate Admission Test record for this application could not be found.</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Undergraduate Information */}
            <Card className="shadow-md border-blue-100">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                <CardTitle className="flex items-center">
                  <GraduationCap className="h-5 w-5 mr-2 text-[#1a73c0]" />
                  Undergraduate Information
                </CardTitle>
                <CardDescription>
                  Details of your undergraduate education
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {applicantInfo?.ug_university ? (
                  <div className="space-y-4">
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h4 className="font-medium text-[#1a73c0] mb-3">University Details</h4>
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">University:</span>
                          <span className="col-span-2 font-medium">{applicantInfo.ug_university}</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">Field of Study:</span>
                          <span className="col-span-2">{applicantInfo.ug_field_of_study || 'N/A'}</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">CGPA:</span>
                          <div className="col-span-2 flex items-center">
                            <span className="font-medium">{applicantInfo.ug_CGPA || 'N/A'}</span>
                            {applicantInfo.ug_CGPA && (
                              <div className="ml-2 bg-blue-100 px-2 py-1 rounded-full text-xs text-blue-800">
                                out of 4.0
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-6 text-center">
                    <GraduationCap className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 italic">Undergraduate information not available</p>
                    <p className="text-sm text-gray-400 mt-2">No undergraduate education details were provided for this application.</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Postgraduate Information - Only show if data exists */}
            {(applicantInfo?.pg_university || applicantInfo?.pg_field_of_study || applicantInfo?.pg_CGPA) && (
              <Card className="shadow-md border-purple-100">
                <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-purple-100">
                  <CardTitle className="flex items-center">
                    <GraduationCap className="h-5 w-5 mr-2 text-purple-700" />
                    Postgraduate Information
                  </CardTitle>
                  <CardDescription>
                    Details of your postgraduate education
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h4 className="font-medium text-purple-700 mb-3">University Details</h4>
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">University:</span>
                          <span className="col-span-2 font-medium">{applicantInfo.pg_university || 'N/A'}</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">Field of Study:</span>
                          <span className="col-span-2">{applicantInfo.pg_field_of_study || 'N/A'}</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <span className="text-gray-600">CGPA:</span>
                          <div className="col-span-2 flex items-center">
                            <span className="font-medium">{applicantInfo.pg_CGPA || 'N/A'}</span>
                            {applicantInfo.pg_CGPA && (
                              <div className="ml-2 bg-purple-100 px-2 py-1 rounded-full text-xs text-purple-800">
                                out of 4.0
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Documentation */}
            <Card className="shadow-md border-blue-100">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-[#1a73c0]" />
                  Submitted Documents
                </CardTitle>
                <CardDescription>
                  Documents uploaded for your application
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {documentation ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {documentation.degree && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Degree Certificate</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.degree)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.degree, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}

                    {documentation.sponsorship && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Sponsorship Letter</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.sponsorship)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.sponsorship, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}

                    {documentation.student_copy && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Student Copy</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.student_copy)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.student_copy, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}

                    {documentation.recommendation && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Recommendation Letter</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.recommendation)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.recommendation, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}

                    {documentation.publication && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Publication</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.publication)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.publication, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}

                    {documentation.conceptnote && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Concept Note</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.conceptnote)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.conceptnote, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}

                    {documentation.grade_12_certificate && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Grade 12 Certificate</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.grade_12_certificate)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.grade_12_certificate, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}

                    {documentation.grade_9_12_transcript && (
                      <div className="p-3 border rounded-md flex justify-between items-center">
                        <div>
                          <p className="font-medium">Grade 9-12 Transcript</p>
                          <p className="text-sm text-gray-500">{getFileNameFromUrl(documentation.grade_9_12_transcript)}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(documentation.grade_9_12_transcript, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-1" /> View
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No documents have been submitted for this application</p>
                )}
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card className="shadow-md border-blue-100">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-[#1a73c0]" />
                  Payment Information
                </CardTitle>
                <CardDescription>
                  Details of your application payment
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {payment ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="grid grid-cols-3 gap-2">
                        <span className="text-gray-500">Payment Amount:</span>
                        <span className="col-span-2 font-medium">{payment.payment_amount} ETB</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="text-gray-500">Payment Method:</span>
                        <span className="col-span-2">{payment.payment_method}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="text-gray-500">Transaction ID:</span>
                        <span className="col-span-2">{payment.transaction_id}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="text-gray-500">Payment Date:</span>
                        <span className="col-span-2">{formatDate(payment.created_at)}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="text-gray-500">Status:</span>
                        <span className="col-span-2">
                          {getStatusBadge(payment.payment_status)}
                        </span>
                      </div>
                    </div>
                    {payment.receipt && (
                      <div className="flex flex-col items-center justify-center p-4 border rounded-md">
                        <p className="mb-2 font-medium">Payment Receipt</p>
                        <Button
                          variant="outline"
                          onClick={() => window.open(payment.receipt, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Receipt
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">Payment information not available</p>
                )}
              </CardContent>
            </Card>

            {/* Navigation Button */}
            <div className="flex justify-end mt-8 mb-4">
              <Button
                variant="outline"
                className="flex items-center"
                onClick={() => navigate('/application/status')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Applications
              </Button>
            </div>

          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ApplicationDetails;
