
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Mail } from 'lucide-react';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from 'sonner';

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address')
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

const ForgotPassword = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const navigate = useNavigate();

  // Check if user is already authenticated
  useEffect(() => {
    const token = localStorage.getItem('token');
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    const userData = localStorage.getItem('user');
    const user = userData ? JSON.parse(userData) : null;

    if (token && isAuthenticated && user) {
      // User is already logged in, redirect to appropriate dashboard
      if (user.is_staff || user.is_superuser) {
        navigate('/graduate-admin');
      } else {
        navigate('/dashboard');
      }
    }
  }, [navigate]);

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: ''
    }
  });

  const onSubmit = (data: ForgotPasswordFormValues) => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      // Check if user exists (in a real app, this would be a backend API call)
      const usersData = localStorage.getItem('users');
      const users = usersData ? JSON.parse(usersData) : [];

      const userExists = users.some((u: any) => u.email === data.email);

      if (userExists) {
        setSubmitted(true);
        toast.success('Password reset instructions sent to your email');
      } else {
        toast.error('No account found with that email address');
      }

      setIsLoading(false);
    }, 1500);
  };

  if (submitted) {
    return (
      <Layout>
        <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-[calc(100vh-64px-152px)] flex items-center justify-center">
          <Card className="w-full max-w-md shadow-lg">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center text-gondar">
                Check Your Email
              </CardTitle>
              <CardDescription className="text-center">
                We've sent password reset instructions to your email
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-gondar-light/20 flex items-center justify-center mb-4">
                <Mail className="h-6 w-6 text-gondar" />
              </div>
              <p className="mb-4">
                If an account exists with the email you provided, you will receive an email with instructions on how to reset your password.
              </p>
              <p className="text-sm text-gray-600 mb-4">
                Didn't receive the email? Check your spam folder or try again.
              </p>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setSubmitted(false)}
              >
                Try again
              </Button>
            </CardContent>
            <CardFooter className="flex justify-center">
              <p className="text-center text-sm text-gray-600">
                Remember your password?{' '}
                <Link to="/login" className="text-gondar hover:underline font-medium">
                  Back to login
                </Link>
              </p>
            </CardFooter>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-[calc(100vh-64px-152px)] flex items-center justify-center">
        <Card className="w-full max-w-md shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center text-gondar">
              Forgot Password
            </CardTitle>
            <CardDescription className="text-center">
              Enter your email address to reset your password
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                          <Input
                            placeholder="<EMAIL>"
                            className="pl-10"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full bg-gondar hover:bg-gondar-dark"
                  disabled={isLoading}
                >
                  {isLoading ? 'Processing...' : 'Reset Password'}
                </Button>
              </form>
            </Form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <p className="text-center text-sm text-gray-600">
              Remember your password?{' '}
              <Link to="/login" className="text-gondar hover:underline font-medium">
                Back to login
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </Layout>
  );
};

export default ForgotPassword;
