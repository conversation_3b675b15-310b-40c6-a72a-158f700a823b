import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  RefreshCw, 
  Eye, 
  Download,
  FileText,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  User
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

import {
  DocumentUpload
} from '@/types/serviceRequest';

import {
  documentUploadAPI,
  serviceRequestUtils
} from '@/services/serviceRequestAPI';

const DocumentUploadManagement: React.FC = () => {
  // State management
  const [documentUploads, setDocumentUploads] = useState<DocumentUpload[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [verificationFilter, setVerificationFilter] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<DocumentUpload | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);

  // Load document uploads
  const fetchDocumentUploads = async () => {
    setLoading(true);
    try {
      const uploads = await documentUploadAPI.getAll();
      setDocumentUploads(uploads);
    } catch (error) {
      console.error('Error fetching document uploads:', error);
      toast.error('Failed to load document uploads');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchDocumentUploads();
  }, []);

  // Filter documents based on search and verification status
  const filteredDocuments = documentUploads.filter(doc => {
    const matchesSearch = !searchTerm || 
      doc.original_filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.document_type_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesVerification = !verificationFilter || verificationFilter === 'all' ||
      (verificationFilter === 'verified' && doc.is_verified) ||
      (verificationFilter === 'unverified' && !doc.is_verified);
    
    return matchesSearch && matchesVerification;
  });

  const handleVerifyDocument = async (documentId: string, verificationNotes?: string) => {
    try {
      await documentUploadAPI.verify(documentId, verificationNotes);
      toast.success('Document verified successfully');
      fetchDocumentUploads();
    } catch (error) {
      console.error('Error verifying document:', error);
      toast.error('Failed to verify document');
    }
  };

  const openViewDialog = (document: DocumentUpload) => {
    setSelectedDocument(document);
    setIsViewOpen(true);
  };

  const getVerificationBadge = (isVerified: boolean) => {
    return (
      <Badge
        variant="outline"
        className={cn(
          "flex items-center space-x-1",
          isVerified 
            ? "border-green-200 text-green-700 bg-green-50"
            : "border-orange-200 text-orange-700 bg-orange-50"
        )}
      >
        {isVerified ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
        <span>{isVerified ? 'Verified' : 'Pending'}</span>
      </Badge>
    );
  };

  if (loading && documentUploads.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0] mx-auto mb-4"></div>
            <div className="text-center">
              <p className="text-gray-600 font-medium">Loading document uploads...</p>
              <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Content Card */}
      <Card>
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-[#1a73c0] rounded-lg shadow-sm">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl text-[#1a73c0]">Document Upload Management</CardTitle>
                <CardDescription className="mt-1">
                  Manage and verify document uploads for service requests
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={fetchDocumentUploads}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 transition-all duration-200"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          {/* Search & Filter Section */}
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
            <h3 className="text-sm font-medium text-[#1a73c0] mb-3 flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Search & Filter
            </h3>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-blue-500" />
                <Input
                  placeholder="Search by filename or document type..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={verificationFilter} onValueChange={setVerificationFilter}>
                  <SelectTrigger className="border-blue-200 focus:ring-blue-400 shadow-sm">
                    <SelectValue placeholder="Filter by verification" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Documents</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="unverified">Pending Verification</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Document Uploads Table */}
          <div className="rounded-md border border-blue-100 overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <TableRow>
                    <TableHead className="text-[#1a73c0] font-medium">Document</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Type</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Verification</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Uploaded By</TableHead>
                    <TableHead className="text-[#1a73c0] font-medium">Upload Date</TableHead>
                    <TableHead className="text-right text-[#1a73c0] font-medium">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1a73c0]"></div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">Loading documents...</p>
                            <p className="text-sm text-gray-500">Please wait while we fetch the data</p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredDocuments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="p-4 bg-blue-50 rounded-full">
                            <FileText className="h-8 w-8 text-blue-400" />
                          </div>
                          <div className="text-center">
                            <p className="text-gray-600 font-medium">No documents found</p>
                            <p className="text-sm text-gray-500">
                              {searchTerm || verificationFilter ? 'Try adjusting your search criteria' : 'No documents have been uploaded yet'}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredDocuments.map((document) => (
                      <TableRow key={document.id} className="hover:bg-blue-50 transition-colors">
                        <TableCell>
                          <div>
                            <p className="font-medium text-[#1a73c0]">{document.original_filename}</p>
                            <p className="text-sm text-gray-500">
                              {serviceRequestUtils.formatFileSize(document.file_size)}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="text-sm">{document.document_type_name}</p>
                        </TableCell>
                        <TableCell>
                          {getVerificationBadge(document.is_verified)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1 text-sm">
                            <User className="h-4 w-4 text-gray-400" />
                            <span>{document.uploaded_by_name || 'Unknown'}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1 text-sm text-gray-500">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(document.created_at).toLocaleDateString()}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openViewDialog(document)}
                              title="View Details"
                              className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {!document.is_verified && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleVerifyDocument(document.id)}
                                className="h-8 w-8 p-0 border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors"
                                title="Verify Document"
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* View Dialog */}
      <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-t-lg border-b border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="p-3 bg-[#1a73c0] rounded-xl shadow-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-xl font-semibold text-[#1a73c0] mb-2">
                  Document Details
                </DialogTitle>
                <DialogDescription className="text-gray-600 leading-relaxed">
                  Complete information for the uploaded document
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {selectedDocument && (
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm font-medium text-gray-600">Filename:</span>
                  <p className="text-sm">{selectedDocument.original_filename}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">Document Type:</span>
                  <p className="text-sm">{selectedDocument.document_type_name}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">File Size:</span>
                  <p className="text-sm">{serviceRequestUtils.formatFileSize(selectedDocument.file_size)}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">Content Type:</span>
                  <p className="text-sm">{selectedDocument.content_type}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">Uploaded By:</span>
                  <p className="text-sm">{selectedDocument.uploaded_by_name || 'Unknown'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">Upload Date:</span>
                  <p className="text-sm">{new Date(selectedDocument.created_at).toLocaleDateString()}</p>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-600">Verification Status:</span>
                    {getVerificationBadge(selectedDocument.is_verified)}
                  </div>
                  {selectedDocument.is_verified && selectedDocument.verified_by_name && (
                    <div className="text-sm text-gray-500">
                      Verified by: {selectedDocument.verified_by_name}
                    </div>
                  )}
                </div>
                {selectedDocument.verification_notes && (
                  <div className="mt-2">
                    <span className="text-sm font-medium text-gray-600">Verification Notes:</span>
                    <p className="text-sm text-gray-700 mt-1">{selectedDocument.verification_notes}</p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => setIsViewOpen(false)}
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Close
                </Button>
                {!selectedDocument.is_verified && (
                  <Button
                    onClick={() => {
                      handleVerifyDocument(selectedDocument.id);
                      setIsViewOpen(false);
                    }}
                    className="bg-green-500 hover:bg-green-600 text-white transition-all duration-200"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Verify Document
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DocumentUploadManagement;
