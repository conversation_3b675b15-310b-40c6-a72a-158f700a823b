#!/usr/bin/env python
"""
Script to test the document_type API endpoints.
"""
import os
import sys
import django
import json

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from setups.document_type.models import DocumentType

def get_auth_headers():
    """Create a test user and get JWT token for authentication."""
    try:
        # Try to get existing test user
        user = User.objects.get(username='testuser')
    except User.DoesNotExist:
        # Create test user
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    # Generate JWT token
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    return {
        'HTTP_AUTHORIZATION': f'Bearer {access_token}',
        'content_type': 'application/json'
    }

def main():
    print("=" * 60)
    print("DOCUMENT TYPE API ENDPOINT TESTING")
    print("=" * 60)
    
    client = Client()
    auth_headers = get_auth_headers()
    
    # Test 1: GET /api/document-types/ (List all document types)
    try:
        print("\n1. Testing GET /api/document-types/ (List all)")
        response = client.get('/api/document-types/', **auth_headers)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Found {len(data)} document types")
            if data:
                print(f"   ✓ Sample: {data[0]['name']}")
        else:
            print(f"   ✗ Error: {response.content}")
            
    except Exception as e:
        print(f"   ✗ Exception: {e}")
    
    # Test 2: GET /api/document-types/active/ (List active only)
    try:
        print("\n2. Testing GET /api/document-types/active/ (Active only)")
        response = client.get('/api/document-types/active/', **auth_headers)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Found {len(data)} active document types")
        else:
            print(f"   ✗ Error: {response.content}")
            
    except Exception as e:
        print(f"   ✗ Exception: {e}")
    
    # Test 3: POST /api/document-types/ (Create new document type)
    try:
        print("\n3. Testing POST /api/document-types/ (Create new)")
        new_doc_data = {
            'name': 'Test API Document',
            'description': 'Created via API test',
            'is_active': True
        }
        
        response = client.post(
            '/api/document-types/',
            data=json.dumps(new_doc_data),
            **auth_headers
        )
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            created_id = data['id']
            print(f"   ✓ Created document type: {data['name']}")
            print(f"   ✓ ID: {created_id}")
            
            # Test 4: GET /api/document-types/{id}/ (Get specific document type)
            print("\n4. Testing GET /api/document-types/{id}/ (Get specific)")
            response = client.get(f'/api/document-types/{created_id}/', **auth_headers)
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✓ Retrieved: {data['name']}")
            else:
                print(f"   ✗ Error: {response.content}")
            
            # Test 5: PUT /api/document-types/{id}/ (Update document type)
            print("\n5. Testing PUT /api/document-types/{id}/ (Update)")
            update_data = {
                'name': 'Updated Test API Document',
                'description': 'Updated via API test',
                'is_active': True
            }
            
            response = client.put(
                f'/api/document-types/{created_id}/',
                data=json.dumps(update_data),
                **auth_headers
            )
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✓ Updated: {data['name']}")
            else:
                print(f"   ✗ Error: {response.content}")
            
            # Test 6: POST /api/document-types/{id}/toggle_status/ (Toggle status)
            print("\n6. Testing POST /api/document-types/{id}/toggle_status/")
            response = client.post(f'/api/document-types/{created_id}/toggle_status/', **auth_headers)
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✓ Status toggled: {data['message']}")
            else:
                print(f"   ✗ Error: {response.content}")
            
            # Test 7: DELETE /api/document-types/{id}/ (Delete document type)
            print("\n7. Testing DELETE /api/document-types/{id}/ (Delete)")
            response = client.delete(f'/api/document-types/{created_id}/', **auth_headers)
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 204:
                print(f"   ✓ Document type deleted successfully")
            else:
                print(f"   ✗ Error: {response.content}")
                
        else:
            print(f"   ✗ Error creating: {response.content}")
            
    except Exception as e:
        print(f"   ✗ Exception: {e}")
    
    # Test 8: GET /api/document-types/?search=passport (Search functionality)
    try:
        print("\n8. Testing GET /api/document-types/?search=passport (Search)")
        response = client.get('/api/document-types/?search=passport', **auth_headers)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Search returned {len(data)} results")
            if data:
                print(f"   ✓ Found: {data[0]['name']}")
        else:
            print(f"   ✗ Error: {response.content}")
            
    except Exception as e:
        print(f"   ✗ Exception: {e}")
    
    # Test 9: GET /api/document-types/?is_active=true (Filter by status)
    try:
        print("\n9. Testing GET /api/document-types/?is_active=true (Filter)")
        response = client.get('/api/document-types/?is_active=true', **auth_headers)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Filter returned {len(data)} active document types")
        else:
            print(f"   ✗ Error: {response.content}")
            
    except Exception as e:
        print(f"   ✗ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("✓ API ENDPOINT TESTING COMPLETED!")
    print("=" * 60)

if __name__ == "__main__":
    main()
