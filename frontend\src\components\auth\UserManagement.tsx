import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Shield, 
  UserCheck, 
  UserX,
  Download,
  Upload,
  RefreshCw,
  Eye,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import { userAPI, groupAPI } from '@/services/authAPI';
import { User, Group, UserFilters, CreateUserRequest, UpdateUserRequest } from '@/types/auth';
import { useSimpleRBAC } from '@/contexts/SimpleRBACContext';

const UserManagement: React.FC = () => {
  const { isSuperuser, isStaff } = useSimpleRBAC();
  const [users, setUsers] = useState<User[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [filters, setFilters] = useState<UserFilters>({});
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  // Form states
  const [createForm, setCreateForm] = useState<CreateUserRequest>({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    is_active: true,
    is_staff: false,
    is_superuser: false,
    groups: [],
    user_permissions: []
  });

  const [editForm, setEditForm] = useState<UpdateUserRequest>({});

  useEffect(() => {
    loadUsers();
    loadGroups();
  }, [filters, pagination.page]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getUsers({
        ...filters,
        page: pagination.page,
        page_size: pagination.pageSize
      });

      setUsers(response.data.results || []);
      setPagination(prev => ({
        ...prev,
        total: response.data.count || 0,
        totalPages: Math.ceil((response.data.count || 0) / pagination.pageSize)
      }));
    } catch (error) {
      console.warn('Users API not available:', error);
      setUsers([]); // Ensure it's always an array
      setSelectedUsers([]); // Reset selected users
      setPagination(prev => ({
        ...prev,
        total: 0,
        totalPages: 0
      }));
      // Don't show error toast for missing/rate-limited API endpoints
    } finally {
      setLoading(false);
    }
  };

  const loadGroups = async () => {
    try {
      const response = await groupAPI.getGroups();
      setGroups(response.data.results || []);
    } catch (error) {
      console.warn('Groups API not available:', error);
      setGroups([]); // Ensure it's always an array
    }
  };

  const handleCreateUser = async () => {
    try {
      await userAPI.createUser(createForm);
      toast.success('User created successfully');
      setShowCreateDialog(false);
      setCreateForm({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        password: '',
        is_active: true,
        is_staff: false,
        is_superuser: false,
        groups: [],
        user_permissions: []
      });
      loadUsers();
    } catch (error) {
      toast.error('Failed to create user');
      console.error('Error creating user:', error);
    }
  };

  const handleUpdateUser = async () => {
    if (!editingUser) return;
    
    try {
      await userAPI.updateUser(editingUser.id, editForm);
      toast.success('User updated successfully');
      setShowEditDialog(false);
      setEditingUser(null);
      setEditForm({});
      loadUsers();
    } catch (error) {
      toast.error('Failed to update user');
      console.error('Error updating user:', error);
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user?')) return;
    
    try {
      await userAPI.deleteUser(userId);
      toast.success('User deleted successfully');
      loadUsers();
    } catch (error) {
      toast.error('Failed to delete user');
      console.error('Error deleting user:', error);
    }
  };

  const handleToggleUserStatus = async (userId: number, isActive: boolean) => {
    try {
      await userAPI.toggleUserStatus(userId, isActive);
      toast.success(`User ${isActive ? 'activated' : 'deactivated'} successfully`);
      loadUsers();
    } catch (error) {
      toast.error('Failed to update user status');
      console.error('Error updating user status:', error);
    }
  };

  const handleBulkAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedUsers.length === 0) {
      toast.error('Please select users first');
      return;
    }

    if (action === 'delete' && !confirm('Are you sure you want to delete selected users?')) {
      return;
    }

    try {
      switch (action) {
        case 'activate':
          await userAPI.bulkActivate(selectedUsers);
          toast.success('Users activated successfully');
          break;
        case 'deactivate':
          await userAPI.bulkDeactivate(selectedUsers);
          toast.success('Users deactivated successfully');
          break;
        case 'delete':
          await userAPI.bulkDelete(selectedUsers);
          toast.success('Users deleted successfully');
          break;
      }
      setSelectedUsers([]);
      loadUsers();
    } catch (error) {
      toast.error(`Failed to ${action} users`);
      console.error(`Error ${action} users:`, error);
    }
  };

  const handleExportUsers = async (format: 'csv' | 'xlsx' = 'csv') => {
    try {
      const response = await userAPI.exportUsers(filters, format);
      const blob = new Blob([response.data], { 
        type: format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Users exported successfully');
    } catch (error) {
      toast.error('Failed to export users');
      console.error('Error exporting users:', error);
    }
  };

  const openEditDialog = (user: User) => {
    setEditingUser(user);
    setEditForm({
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      is_active: user.is_active,
      is_staff: user.is_staff,
      is_superuser: user.is_superuser,
      groups: user.groups.map(g => g.id),
    });
    setShowEditDialog(true);
  };

  const getUserStatusBadge = (user: User) => {
    if (!user.is_active) {
      return <Badge variant="destructive">Inactive</Badge>;
    }
    if (user.is_superuser) {
      return <Badge variant="default">Superuser</Badge>;
    }
    if (user.is_staff) {
      return <Badge variant="secondary">Staff</Badge>;
    }
    return <Badge variant="outline">User</Badge>;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground">
            Manage users, roles, and permissions
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => handleExportUsers('csv')}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" onClick={() => handleExportUsers('xlsx')}>
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {/* API Status Notice */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-blue-800">
            <AlertTriangle className="h-5 w-5" />
            <span>Backend API Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-blue-700 text-sm">
            The user management interface is displaying demo data. The backend APIs for user management
            are not yet implemented or are being rate-limited. This interface shows the complete frontend
            implementation that will work once the corresponding Django REST API endpoints are created.
          </p>
        </CardContent>
      </Card>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search users..."
                  className="pl-10"
                  value={filters.search || ''}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_active"
                checked={filters.is_active}
                onCheckedChange={(checked) => 
                  setFilters({ ...filters, is_active: checked as boolean })
                }
              />
              <Label htmlFor="is_active">Active only</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_staff"
                checked={filters.is_staff}
                onCheckedChange={(checked) => 
                  setFilters({ ...filters, is_staff: checked as boolean })
                }
              />
              <Label htmlFor="is_staff">Staff only</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_superuser"
                checked={filters.is_superuser}
                onCheckedChange={(checked) => 
                  setFilters({ ...filters, is_superuser: checked as boolean })
                }
              />
              <Label htmlFor="is_superuser">Superusers only</Label>
            </div>
          </div>
          
          <div className="flex justify-between items-center mt-4">
            <div className="flex space-x-2">
              {selectedUsers.length > 0 && (
                <>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleBulkAction('activate')}
                  >
                    <UserCheck className="h-4 w-4 mr-2" />
                    Activate ({selectedUsers.length})
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleBulkAction('deactivate')}
                  >
                    <UserX className="h-4 w-4 mr-2" />
                    Deactivate ({selectedUsers.length})
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => handleBulkAction('delete')}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete ({selectedUsers.length})
                  </Button>
                </>
              )}
            </div>
            
            <Button variant="outline" size="sm" onClick={loadUsers}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Users ({pagination.total})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedUsers.length === users.length && users.length > 0}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedUsers(users.map(u => u.id));
                        } else {
                          setSelectedUsers([]);
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Groups</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead>Date Joined</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading users...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="text-muted-foreground">
                        <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No users found</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedUsers([...selectedUsers, user.id]);
                            } else {
                              setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {user.first_name} {user.last_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            @{user.username}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{getUserStatusBadge(user)}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.groups.slice(0, 2).map((group) => (
                            <Badge key={group.id} variant="outline" className="text-xs">
                              {group.name}
                            </Badge>
                          ))}
                          {user.groups.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{user.groups.length - 2} more
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(user.last_login)}</TableCell>
                      <TableCell>{formatDate(user.date_joined)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openEditDialog(user)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleToggleUserStatus(user.id, !user.is_active)}
                            >
                              {user.is_active ? (
                                <>
                                  <UserX className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <UserCheck className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
                {pagination.total} users
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page === 1}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page === pagination.totalPages}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create User Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New User</DialogTitle>
            <DialogDescription>
              Add a new user to the system with appropriate roles and permissions.
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="create-username">Username *</Label>
              <Input
                id="create-username"
                value={createForm.username}
                onChange={(e) => setCreateForm({ ...createForm, username: e.target.value })}
                placeholder="Enter username"
              />
            </div>

            <div>
              <Label htmlFor="create-email">Email *</Label>
              <Input
                id="create-email"
                type="email"
                value={createForm.email}
                onChange={(e) => setCreateForm({ ...createForm, email: e.target.value })}
                placeholder="Enter email"
              />
            </div>

            <div>
              <Label htmlFor="create-first-name">First Name *</Label>
              <Input
                id="create-first-name"
                value={createForm.first_name}
                onChange={(e) => setCreateForm({ ...createForm, first_name: e.target.value })}
                placeholder="Enter first name"
              />
            </div>

            <div>
              <Label htmlFor="create-last-name">Last Name *</Label>
              <Input
                id="create-last-name"
                value={createForm.last_name}
                onChange={(e) => setCreateForm({ ...createForm, last_name: e.target.value })}
                placeholder="Enter last name"
              />
            </div>

            <div className="col-span-2">
              <Label htmlFor="create-password">Password *</Label>
              <Input
                id="create-password"
                type="password"
                value={createForm.password}
                onChange={(e) => setCreateForm({ ...createForm, password: e.target.value })}
                placeholder="Enter password"
              />
            </div>

            <div className="col-span-2">
              <Label>Groups</Label>
              <div className="grid grid-cols-2 gap-2 mt-2 max-h-32 overflow-y-auto">
                {groups.map((group) => (
                  <div key={group.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`create-group-${group.id}`}
                      checked={createForm.groups?.includes(group.id)}
                      onCheckedChange={(checked) => {
                        const currentGroups = createForm.groups || [];
                        if (checked) {
                          setCreateForm({
                            ...createForm,
                            groups: [...currentGroups, group.id]
                          });
                        } else {
                          setCreateForm({
                            ...createForm,
                            groups: currentGroups.filter(id => id !== group.id)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={`create-group-${group.id}`} className="text-sm">
                      {group.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-span-2 space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="create-is-active"
                  checked={createForm.is_active}
                  onCheckedChange={(checked) => setCreateForm({ ...createForm, is_active: checked })}
                />
                <Label htmlFor="create-is-active">Active</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="create-is-staff"
                  checked={createForm.is_staff}
                  onCheckedChange={(checked) => setCreateForm({ ...createForm, is_staff: checked })}
                />
                <Label htmlFor="create-is-staff">Staff Status</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="create-is-superuser"
                  checked={createForm.is_superuser}
                  onCheckedChange={(checked) => setCreateForm({ ...createForm, is_superuser: checked })}
                />
                <Label htmlFor="create-is-superuser">Superuser Status</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateUser}>
              Create User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information, roles, and permissions.
            </DialogDescription>
          </DialogHeader>

          {editingUser && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-username">Username</Label>
                <Input
                  id="edit-username"
                  value={editForm.username || ''}
                  onChange={(e) => setEditForm({ ...editForm, username: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-email">Email</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={editForm.email || ''}
                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-first-name">First Name</Label>
                <Input
                  id="edit-first-name"
                  value={editForm.first_name || ''}
                  onChange={(e) => setEditForm({ ...editForm, first_name: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-last-name">Last Name</Label>
                <Input
                  id="edit-last-name"
                  value={editForm.last_name || ''}
                  onChange={(e) => setEditForm({ ...editForm, last_name: e.target.value })}
                />
              </div>

              <div className="col-span-2">
                <Label>Groups</Label>
                <div className="grid grid-cols-2 gap-2 mt-2 max-h-32 overflow-y-auto">
                  {groups.map((group) => (
                    <div key={group.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`edit-group-${group.id}`}
                        checked={editForm.groups?.includes(group.id)}
                        onCheckedChange={(checked) => {
                          const currentGroups = editForm.groups || [];
                          if (checked) {
                            setEditForm({
                              ...editForm,
                              groups: [...currentGroups, group.id]
                            });
                          } else {
                            setEditForm({
                              ...editForm,
                              groups: currentGroups.filter(id => id !== group.id)
                            });
                          }
                        }}
                      />
                      <Label htmlFor={`edit-group-${group.id}`} className="text-sm">
                        {group.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="col-span-2 space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-is-active"
                    checked={editForm.is_active}
                    onCheckedChange={(checked) => setEditForm({ ...editForm, is_active: checked })}
                  />
                  <Label htmlFor="edit-is-active">Active</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-is-staff"
                    checked={editForm.is_staff}
                    onCheckedChange={(checked) => setEditForm({ ...editForm, is_staff: checked })}
                  />
                  <Label htmlFor="edit-is-staff">Staff Status</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-is-superuser"
                    checked={editForm.is_superuser}
                    onCheckedChange={(checked) => setEditForm({ ...editForm, is_superuser: checked })}
                  />
                  <Label htmlFor="edit-is-superuser">Superuser Status</Label>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateUser}>
              Update User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserManagement;
