#!/usr/bin/env python
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from setups.certificate_type.models import CertificateType
from setups.service_type.models import ServiceType
from decimal import Decimal

# Create certificate types
cert1, _ = CertificateType.objects.get_or_create(name='Academic Transcript', defaults={'is_active': True})
cert2, _ = CertificateType.objects.get_or_create(name='Diploma Certificate', defaults={'is_active': True})

# Create service type
service, created = ServiceType.objects.get_or_create(
    name='Test Service',
    defaults={'fee': Decimal('25.00'), 'is_active': True}
)

if created:
    service.document_types.add(cert1, cert2)

print(f"Certificate types: {CertificateType.objects.count()}")
print(f"Service types: {ServiceType.objects.count()}")
print("Test data ready!")
