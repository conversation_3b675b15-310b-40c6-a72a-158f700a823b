from rest_framework import serializers
from .models import Downloadable


class DownloadableSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    file_size = serializers.SerializerMethodField()
    file_name = serializers.SerializerMethodField()

    class Meta:
        model = Downloadable
        fields = [
            'id', 'title', 'file', 'file_url', 'file_size', 'file_name',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_file_url(self, obj):
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None

    def get_file_size(self, obj):
        if obj.file:
            try:
                return obj.file.size
            except (ValueError, OSError):
                return None
        return None

    def get_file_name(self, obj):
        if obj.file:
            return obj.file.name.split('/')[-1]
        return None


class DownloadableCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Downloadable
        fields = ['title', 'file', 'is_active']

    def validate_file(self, value):
        # Add file validation if needed
        max_size = 2 * 1024 * 1024  # 2MB
        if value.size > max_size:
            raise serializers.ValidationError("File size cannot exceed 2MB.")
        return value


class DownloadableUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Downloadable
        fields = ['title', 'file', 'is_active']

    def validate_file(self, value):
        if value:
            max_size = 2 * 1024 * 1024  # 2MB
            if value.size > max_size:
                raise serializers.ValidationError("File size cannot exceed 2MB.")
        return value
