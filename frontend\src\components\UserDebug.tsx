import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { logPermissionAnalysis, analyzeUserPermissions } from '@/utils/permissionAnalyzer';
import { debugUserMenuAccess } from '@/utils/menuPermissionsNew';
import { debugPermissionMappings, validatePermissionMappings } from '@/utils/permissionMappings';
import { Bug, User, Shield, Users, Search, Download, Code } from 'lucide-react';

/**
 * User Debug Component
 * Shows the actual user object structure for debugging permissions
 */
export const UserDebug: React.FC = () => {
  const { user, hasPermission, hasRole, hasAnyRole } = useAuth();

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            User Debug - No User
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>No user is currently logged in.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* User Object Structure */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            User Object Debug
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Raw User Object:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Properties */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Properties
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Basic Info</h4>
              <div className="space-y-1 text-sm">
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Username:</strong> {user.username}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>First Name:</strong> {user.first_name}</p>
                <p><strong>Last Name:</strong> {user.last_name}</p>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Status</h4>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Badge variant={user.is_active ? "default" : "destructive"}>
                    {user.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={user.is_staff ? "default" : "secondary"}>
                    {user.is_staff ? "Staff" : "Regular User"}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={user.is_superuser ? "default" : "outline"}>
                    {user.is_superuser ? "Superuser" : "Regular User"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Debug */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permissions Debug
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Permissions Property Structure:</h4>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-48">
                {JSON.stringify(user.permissions, null, 2)}
              </pre>
            </div>

            <div>
              <h4 className="font-medium mb-2">Permission Strings (if available):</h4>
              {user.permissions?.permission_strings ? (
                <div className="space-y-1">
                  {user.permissions.permission_strings.map((perm: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs mr-1 mb-1">
                      {perm}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No permission_strings found</p>
              )}
            </div>

            <div>
              <h4 className="font-medium mb-2">Direct Permissions Array (if available):</h4>
              {Array.isArray(user.permissions) ? (
                <div className="space-y-1">
                  {user.permissions.map((perm: any, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs mr-1 mb-1">
                      {typeof perm === 'string' ? perm : JSON.stringify(perm)}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">Permissions is not an array</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Roles Debug */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Roles/Groups Debug
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Role Names:</h4>
              {user.role_names && user.role_names.length > 0 ? (
                <div className="space-y-1">
                  {user.role_names.map((role: string, index: number) => (
                    <Badge key={index} variant="default" className="text-xs mr-1 mb-1">
                      {role}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No role_names found</p>
              )}
            </div>

            <div>
              <h4 className="font-medium mb-2">Roles Property Structure:</h4>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-48">
                {JSON.stringify(user.roles, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permission Testing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permission Testing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Test Verification Permissions:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {[
                  'unknown.view_graduatestudent',
                  'unknown.change_graduatestudent',
                  'unknown.add_graduatestudent',
                  'unknown.view_verificationcollege',
                  'unknown.change_verificationcollege'
                ].map((perm) => (
                  <div key={perm} className="flex items-center gap-2">
                    <Badge variant={hasPermission(perm) ? "default" : "destructive"} className="text-xs">
                      {hasPermission(perm) ? "✓" : "✗"}
                    </Badge>
                    <span className="text-sm font-mono">{perm}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Test Group Membership:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {[
                  'Verification Clerk',
                  'Registrar',
                  'Administrator',
                  'Department Head'
                ].map((role) => (
                  <div key={role} className="flex items-center gap-2">
                    <Badge variant={hasRole(role) ? "default" : "destructive"} className="text-xs">
                      {hasRole(role) ? "✓" : "✗"}
                    </Badge>
                    <span className="text-sm">{role}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permission Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Permission Analysis & Mapping Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2 flex-wrap">
              <Button
                onClick={() => logPermissionAnalysis(user)}
                className="flex items-center gap-2"
              >
                <Code className="h-4 w-4" />
                Analyze Permissions
              </Button>

              <Button
                onClick={() => debugUserMenuAccess(user)}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Bug className="h-4 w-4" />
                Debug Menu Access
              </Button>

              <Button
                onClick={() => {
                  const analysis = analyzeUserPermissions(user);
                  console.log('📋 Permission Analysis:', analysis);
                  console.log('🎯 Copy this to update permissionMappings.ts:');
                  console.log(JSON.stringify(analysis.suggestedMappings, null, 2));
                }}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Generate Mappings
              </Button>

              <Button
                onClick={() => debugPermissionMappings()}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Search className="h-4 w-4" />
                Validate Mappings
              </Button>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2 text-blue-800">How to Use:</h4>
              <ol className="text-sm text-blue-700 space-y-1">
                <li>1. Click "Analyze Permissions" to see detailed permission analysis in console</li>
                <li>2. Click "Generate Mappings" to get suggested permission mappings</li>
                <li>3. Copy the generated mappings to update permissionMappings.ts</li>
                <li>4. Click "Debug Menu Access" to test menu access with current permissions</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserDebug;
