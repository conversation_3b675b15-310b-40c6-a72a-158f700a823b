import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface GraduatesByYearChartProps {
  data: any[];
}

const GraduatesByYearChart: React.FC<GraduatesByYearChartProps> = ({ data }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Graduates by Year</CardTitle>
        <CardDescription>Number of graduates per year</CardDescription>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="year" />
            <YAxis />
            <Tooltip
              wrapperStyle={{ maxWidth: '250px' }}
              formatter={(value: any) => [`${value} Graduates`, 'Count']}
            />
            <Legend />
            <Bar dataKey="count" name="Graduates" fill="#1a73c0" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default GraduatesByYearChart;
