#!/usr/bin/env python
"""
Test script to verify payment status choices
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from registration.models import ApplicantProgramSelection

def test_payment_status_choices():
    """Test the payment status choices"""
    print("=== Testing Payment Status Choices ===")
    
    # Get the payment status choices from the model
    choices = ApplicantProgramSelection.PAYMENT_STATUS_CHOICES
    print(f"Available payment status choices: {len(choices)}")
    
    for choice_value, choice_label in choices:
        print(f"  - {choice_value}: {choice_label}")
    
    print("\n=== Expected Choices ===")
    expected_choices = [
        'Pending', 'Initiated', 'Processing', 'Completed', 
        'Failed', 'Verified', 'Expired', 'Refunded', 'Cancelled'
    ]
    
    actual_choices = [choice[0] for choice in choices]
    
    print("Checking if all expected choices are present:")
    for expected in expected_choices:
        if expected in actual_choices:
            print(f"  ✓ {expected}")
        else:
            print(f"  ✗ {expected} - MISSING")
    
    print(f"\nTotal expected: {len(expected_choices)}")
    print(f"Total actual: {len(actual_choices)}")
    
    if set(expected_choices) == set(actual_choices):
        print("✅ All payment status choices are correct!")
    else:
        print("❌ Payment status choices don't match expected values")
        print(f"Missing: {set(expected_choices) - set(actual_choices)}")
        print(f"Extra: {set(actual_choices) - set(expected_choices)}")

if __name__ == '__main__':
    test_payment_status_choices()
