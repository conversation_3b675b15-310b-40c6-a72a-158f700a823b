from django.shortcuts import render
from rest_framework import generics, filters
from rest_framework.permissions import IsAuthenticated, AllowAny
from .models import Year
from .serializers import YearSerializer

# Create your views here.

# List and Create view for Year (GET, POST)
class YearList(generics.ListCreateAPIView):
    serializer_class = YearSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Year.objects.all()

    def perform_create(self, serializer):
        if serializer.is_valid():
            serializer.save()
        else:
            print(serializer.errors)

# Retrieve, Update and Delete view for Year (GET, PUT, DELETE)
class YearDetail(generics.RetrieveUpdateAPIView):
    serializer_class = YearSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Year.objects.all()

# Delete view for Year (DELETE)
class YearDelete(generics.DestroyAPIView):
    serializer_class = YearSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Year.objects.all()

# Public list view for Year (GET)
class PublicYearList(generics.ListAPIView):
    serializer_class = YearSerializer
    permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter]
    search_fields = ['year']

    def get_queryset(self):
        return Year.objects.all()
