from rest_framework import generics, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from django.core.cache import cache
from .models import OfficialSent, OfficialReceived
from .serializers import (
    OfficialSentSerializer,
    OfficialReceivedSerializer,
    OfficialSentCreateUpdateSerializer,
    OfficialReceivedCreateUpdateSerializer
)


class LargeDatasetPagination(PageNumberPagination):
    """Custom pagination for handling large datasets efficiently"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'total_pages': self.page.paginator.num_pages,
            'current_page': self.page.number,
            'page_size': self.get_page_size(self.request),
            'results': data
        })


class OfficialSentListCreateView(generics.ListCreateAPIView):
    """List all sent certificates or create a new one - with pagination for admin"""
    queryset = OfficialSent.objects.all().select_related('certificate_type').order_by('-send_date')
    permission_classes = [IsAuthenticated]
    pagination_class = LargeDatasetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['gender', 'certificate_type', 'courier']
    search_fields = ['first_name', 'last_name', 'receiver_institute', 'tracking_number']
    ordering_fields = ['send_date', 'first_name', 'last_name', 'receiver_institute']
    ordering = ['-send_date']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return OfficialSentCreateUpdateSerializer
        return OfficialSentSerializer

    def get_queryset(self):
        """Optimized queryset with select_related for better performance"""
        return OfficialSent.objects.select_related('certificate_type').order_by('-send_date')


class OfficialSentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a sent certificate"""
    queryset = OfficialSent.objects.all().select_related('certificate_type')
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return OfficialSentCreateUpdateSerializer
        return OfficialSentSerializer


class OfficialReceivedListCreateView(generics.ListCreateAPIView):
    """List all received certificates or create a new one - with pagination for admin"""
    queryset = OfficialReceived.objects.all().select_related('certificate_type').order_by('-arival_date')
    permission_classes = [IsAuthenticated]
    pagination_class = LargeDatasetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['gender', 'certificate_type', 'courier']
    search_fields = ['first_name', 'last_name', 'sender_institute', 'tracking_number']
    ordering_fields = ['arival_date', 'first_name', 'last_name', 'sender_institute']
    ordering = ['-arival_date']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return OfficialReceivedCreateUpdateSerializer
        return OfficialReceivedSerializer

    def get_queryset(self):
        """Optimized queryset with select_related for better performance"""
        return OfficialReceived.objects.select_related('certificate_type').order_by('-arival_date')


class OfficialReceivedDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a received certificate"""
    queryset = OfficialReceived.objects.all().select_related('certificate_type')
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return OfficialReceivedCreateUpdateSerializer
        return OfficialReceivedSerializer


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def official_statistics(request):
    """Get statistics for official certificates"""
    try:
        sent_total = OfficialSent.objects.count()
        received_total = OfficialReceived.objects.count()

        # Get statistics by certificate type
        from django.db.models import Count
        sent_by_type = OfficialSent.objects.values(
            'certificate_type__name'
        ).annotate(count=Count('id')).order_by('-count')

        received_by_type = OfficialReceived.objects.values(
            'certificate_type__name'
        ).annotate(count=Count('id')).order_by('-count')

        # Get statistics by gender
        sent_by_gender = OfficialSent.objects.values(
            'gender'
        ).annotate(count=Count('id')).order_by('-count')

        received_by_gender = OfficialReceived.objects.values(
            'gender'
        ).annotate(count=Count('id')).order_by('-count')

        return Response({
            'sent': {
                'total': sent_total,
                'by_certificate_type': list(sent_by_type),
                'by_gender': list(sent_by_gender)
            },
            'received': {
                'total': received_total,
                'by_certificate_type': list(received_by_type),
                'by_gender': list(received_by_gender)
            }
        })
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_by_tracking_number(request):
    """Search certificates by tracking number"""
    tracking_number = request.GET.get('tracking_number', '').strip()

    if not tracking_number:
        return Response(
            {'error': 'Tracking number is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Search in both sent and received certificates
        sent_cert = OfficialSent.objects.filter(
            tracking_number__iexact=tracking_number
        ).select_related('certificate_type').first()

        received_cert = OfficialReceived.objects.filter(
            tracking_number__iexact=tracking_number
        ).select_related('certificate_type').first()

        result = {
            'tracking_number': tracking_number,
            'sent': None,
            'received': None
        }

        if sent_cert:
            result['sent'] = OfficialSentSerializer(sent_cert).data

        if received_cert:
            result['received'] = OfficialReceivedSerializer(received_cert).data

        if not sent_cert and not received_cert:
            return Response(
                {'error': 'No certificate found with this tracking number'},
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(result)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Public views for tracking certificates (no authentication required)
class PublicOfficialSentListView(generics.ListAPIView):
    """Public list of sent certificates for tracking purposes - optimized for large datasets"""
    serializer_class = OfficialSentSerializer
    permission_classes = [AllowAny]
    pagination_class = LargeDatasetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['certificate_type', 'courier']
    search_fields = ['tracking_number', 'first_name', 'last_name', 'receiver_institute']
    ordering_fields = ['send_date', 'first_name', 'last_name', 'receiver_institute']
    ordering = ['-send_date']

    def get_queryset(self):
        """Optimized queryset with select_related and only necessary fields"""
        return OfficialSent.objects.select_related('certificate_type').only(
            'id', 'tracking_number', 'first_name', 'last_name', 'gender',
            'receiver_institute', 'send_date', 'courier', 'created_at',
            'certificate_type__name'
        ).order_by('-send_date')


class PublicOfficialReceivedListView(generics.ListAPIView):
    """Public list of received certificates for tracking purposes - optimized for large datasets"""
    serializer_class = OfficialReceivedSerializer
    permission_classes = [AllowAny]
    pagination_class = LargeDatasetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['certificate_type', 'courier']
    search_fields = ['tracking_number', 'first_name', 'last_name', 'sender_institute']
    ordering_fields = ['arival_date', 'first_name', 'last_name', 'sender_institute']
    ordering = ['-arival_date']

    def get_queryset(self):
        """Optimized queryset with select_related and only necessary fields"""
        return OfficialReceived.objects.select_related('certificate_type').only(
            'id', 'tracking_number', 'first_name', 'last_name', 'gender',
            'sender_institute', 'arival_date', 'courier', 'created_at',
            'certificate_type__name'
        ).order_by('-arival_date')


@api_view(['GET'])
@permission_classes([AllowAny])
def public_certificate_stats(request):
    """Get cached statistics for public certificate tracking"""
    cache_key = 'public_certificate_stats'
    stats = cache.get(cache_key)

    if stats is None:
        try:
            # Use efficient counting with database aggregation
            sent_count = OfficialSent.objects.count()
            received_count = OfficialReceived.objects.count()

            stats = {
                'sent_count': sent_count,
                'received_count': received_count,
                'total_count': sent_count + received_count,
                'last_updated': cache.get('stats_last_updated', 'Never')
            }

            # Cache for 5 minutes
            cache.set(cache_key, stats, 300)

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    return Response(stats)


@api_view(['GET'])
@permission_classes([AllowAny])
def public_search_by_tracking_number(request):
    """Public search certificates by tracking number"""
    tracking_number = request.GET.get('tracking_number', '').strip()

    if not tracking_number:
        return Response(
            {'error': 'Tracking number is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Search in both sent and received certificates
        sent_cert = OfficialSent.objects.filter(
            tracking_number__iexact=tracking_number
        ).select_related('certificate_type').first()

        received_cert = OfficialReceived.objects.filter(
            tracking_number__iexact=tracking_number
        ).select_related('certificate_type').first()

        result = {
            'tracking_number': tracking_number,
            'sent': None,
            'received': None
        }

        if sent_cert:
            result['sent'] = OfficialSentSerializer(sent_cert).data

        if received_cert:
            result['received'] = OfficialReceivedSerializer(received_cert).data

        if not sent_cert and not received_cert:
            return Response(
                {'error': 'No certificate found with this tracking number'},
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(result)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
