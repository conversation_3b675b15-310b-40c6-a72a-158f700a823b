#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix existing status values in the database to match the new choices.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from alumni_applications.models import AlumniApplication, AlumniApplicationMini

def fix_application_status_values():
    """Fix application status values to match new choices."""
    
    # Mapping from old values to new values
    application_status_mapping = {
        'Under Review': 'On Review',
        'Approved': 'Processing',
        'Completed': 'Complete',
        'Rejected': 'Complete',  # Map rejected to complete for now
        # Keep existing valid values
        'Pending': 'Pending',
        'On Review': 'On Review',
        'Processing': 'Processing',
        'Complete': 'Complete',
    }
    
    payment_status_mapping = {
        'Unpaid': 'Pending',
        'Paid': 'Completed',
        'Pending Payment': 'Initiated',
        # Keep existing valid values
        'Pending': 'Pending',
        'Initiated': 'Initiated',
        'Processing': 'Processing',
        'Completed': 'Completed',
        'Failed': 'Failed',
        'Expired': 'Expired',
        'Refunded': 'Refunded',
        'Cancelled': 'Cancelled',
    }
    
    print("🔄 Fixing status values in database...")
    print("=" * 50)
    
    # Fix Form1 applications
    form1_updated = 0
    form1_apps = AlumniApplication.objects.all()
    
    print(f"📊 Found {form1_apps.count()} Form1 applications")
    
    for app in form1_apps:
        updated = False
        old_app_status = app.application_status
        old_payment_status = app.payment_status
        
        # Fix application status
        if app.application_status in application_status_mapping:
            new_status = application_status_mapping[app.application_status]
            if new_status != app.application_status:
                app.application_status = new_status
                updated = True
                print(f"  📝 App {app.transaction_id}: application_status '{old_app_status}' → '{new_status}'")
        
        # Fix payment status
        if app.payment_status in payment_status_mapping:
            new_status = payment_status_mapping[app.payment_status]
            if new_status != app.payment_status:
                app.payment_status = new_status
                updated = True
                print(f"  💳 App {app.transaction_id}: payment_status '{old_payment_status}' → '{new_status}'")
        
        if updated:
            app.save()
            form1_updated += 1
    
    # Fix Form2 applications
    form2_updated = 0
    form2_apps = AlumniApplicationMini.objects.all()
    
    print(f"\n📊 Found {form2_apps.count()} Form2 applications")
    
    for app in form2_apps:
        updated = False
        old_app_status = app.application_status
        old_payment_status = app.payment_status
        
        # Fix application status
        if app.application_status in application_status_mapping:
            new_status = application_status_mapping[app.application_status]
            if new_status != app.application_status:
                app.application_status = new_status
                updated = True
                print(f"  📝 App {app.transaction_id}: application_status '{old_app_status}' → '{new_status}'")
        
        # Fix payment status
        if app.payment_status in payment_status_mapping:
            new_status = payment_status_mapping[app.payment_status]
            if new_status != app.payment_status:
                app.payment_status = new_status
                updated = True
                print(f"  💳 App {app.transaction_id}: payment_status '{old_payment_status}' → '{new_status}'")
        
        if updated:
            app.save()
            form2_updated += 1
    
    print("\n" + "=" * 50)
    print(f"✅ Status fix complete!")
    print(f"   Form1 applications updated: {form1_updated}")
    print(f"   Form2 applications updated: {form2_updated}")
    print(f"   Total applications updated: {form1_updated + form2_updated}")
    
    # Show current status distribution
    print("\n📊 Current Status Distribution:")
    print("-" * 30)
    
    # Application statuses
    app_statuses = {}
    for app in AlumniApplication.objects.all():
        status = app.application_status
        app_statuses[status] = app_statuses.get(status, 0) + 1
    
    for app in AlumniApplicationMini.objects.all():
        status = app.application_status
        app_statuses[status] = app_statuses.get(status, 0) + 1
    
    print("Application Statuses:")
    for status, count in app_statuses.items():
        print(f"  {status}: {count}")
    
    # Payment statuses
    payment_statuses = {}
    for app in AlumniApplication.objects.all():
        status = app.payment_status
        payment_statuses[status] = payment_statuses.get(status, 0) + 1
    
    for app in AlumniApplicationMini.objects.all():
        status = app.payment_status
        payment_statuses[status] = payment_statuses.get(status, 0) + 1
    
    print("\nPayment Statuses:")
    for status, count in payment_statuses.items():
        print(f"  {status}: {count}")

def main():
    """Main function."""
    print("🚀 Status Values Fix Script")
    print("This script will update existing status values to match the new choices.")
    print()
    
    # Confirm before proceeding
    response = input("Do you want to proceed with fixing status values? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Fix cancelled.")
        return
    
    # Run the fix
    fix_application_status_values()

if __name__ == '__main__':
    main()
