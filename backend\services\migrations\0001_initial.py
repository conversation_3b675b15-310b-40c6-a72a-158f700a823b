# Generated by Django 5.2.1 on 2025-05-31 12:42

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_name', models.CharField(help_text='Name of the service offered', max_length=255, verbose_name='Service Name')),
                ('description', models.TextField(help_text='Detailed description of the service', verbose_name='Description')),
                ('service_fee', models.DecimalField(decimal_places=2, help_text='Fee charged for this service (in ETB)', max_digits=10, verbose_name='Service Fee')),
                ('icon_name', models.CharField(default='FileText', help_text="Name of the Lucide icon to display (e.g., 'FileText', 'GraduationCap')", max_length=50, verbose_name='Icon Name')),
                ('is_active', models.<PERSON>olean<PERSON>ield(default=True, help_text='Whether this service is currently available', verbose_name='Active')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order in which to display this service (lower numbers first)', verbose_name='Display Order')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'ordering': ['order', 'service_name'],
            },
        ),
    ]
