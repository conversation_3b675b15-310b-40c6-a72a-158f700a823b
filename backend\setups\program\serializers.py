from rest_framework import serializers
from .models import Program

class ProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = Program
        fields = ['id', 'program_code', 'program_name', 'registration_fee']
        read_only_fields = ['created_at', 'updated_at']
        ref_name = 'Program'

    def validate_program_code(self, value):
        # Check if the program_code already exists in the database
        instance = getattr(self, 'instance', None)

        # If this is an update (instance exists) and the code hasn't changed, it's valid
        if instance and instance.program_code == value:
            return value

        # Otherwise, check if the code is already in use
        if Program.objects.filter(program_code=value).exists():
            raise serializers.ValidationError(f"The program code {value} is already in use.")

        return value