from django.core.management.base import BaseCommand, CommandError
from django.db import connection, models
from django.apps import apps


class Command(BaseCommand):
    help = 'Reset the auto-increment sequence for a specific table'

    def add_arguments(self, parser):
        parser.add_argument('app_label', type=str, help='The app label (e.g., "setups")')
        parser.add_argument('model_name', type=str, help='The model name (e.g., "department")')
        parser.add_argument('--dry-run', action='store_true', help='Show SQL without executing')

    def handle(self, *args, **options):
        app_label = options['app_label']
        model_name = options['model_name']
        dry_run = options['dry_run']

        try:
            # Get the model
            model = apps.get_model(app_label, model_name)

            # Get the table name
            table_name = model._meta.db_table

            # Get the primary key field name
            pk_field = model._meta.pk.name

            # Get the maximum ID
            max_id = model.objects.aggregate(max_id=models.Max(pk_field))['max_id'] or 0

            # Prepare the SQL to reset the sequence
            sql = f"SELECT setval(pg_get_serial_sequence('{table_name}', '{pk_field}'), {max_id + 1}, false);"

            if dry_run:
                self.stdout.write(self.style.SUCCESS(f"SQL (not executed): {sql}"))
                self.stdout.write(self.style.SUCCESS(f"Current max ID: {max_id}"))
                self.stdout.write(self.style.SUCCESS(f"Next ID will be: {max_id + 1}"))
            else:
                # Execute the SQL
                with connection.cursor() as cursor:
                    cursor.execute(sql)
                    result = cursor.fetchone()[0]

                self.stdout.write(self.style.SUCCESS(f"Successfully reset sequence for {app_label}.{model_name}"))
                self.stdout.write(self.style.SUCCESS(f"Current max ID: {max_id}"))
                self.stdout.write(self.style.SUCCESS(f"Next ID will be: {result}"))

        except LookupError:
            raise CommandError(f"Model '{model_name}' in app '{app_label}' not found")
        except Exception as e:
            raise CommandError(f"Error resetting sequence: {str(e)}")
