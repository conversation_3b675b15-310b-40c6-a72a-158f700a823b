// Notification types
export interface Notification {
  id: string;
  title: string;
  message: string;
  date: string;
  read: boolean;
  type: 'info' | 'success' | 'warning' | 'error';
}

// Local storage key for notifications
const NOTIFICATIONS_STORAGE_KEY = 'app_notifications';

// Get notifications from local storage
const getNotifications = (): Notification[] => {
  const storedNotifications = localStorage.getItem(NOTIFICATIONS_STORAGE_KEY);
  return storedNotifications ? JSON.parse(storedNotifications) : [];
};

// Save notifications to local storage
const saveNotifications = (notifications: Notification[]): void => {
  localStorage.setItem(NOTIFICATIONS_STORAGE_KEY, JSON.stringify(notifications));
};

// Add a new notification
const addNotification = (notification: Omit<Notification, 'id' | 'date' | 'read'>): Notification => {
  const notifications = getNotifications();
  
  // Create a new notification with generated ID and current date
  const newNotification: Notification = {
    id: Date.now().toString(),
    date: new Date().toISOString(),
    read: false,
    ...notification
  };
  
  // Add to the beginning of the array (newest first)
  const updatedNotifications = [newNotification, ...notifications];
  saveNotifications(updatedNotifications);
  
  return newNotification;
};

// Mark a notification as read
const markAsRead = (id: string): void => {
  const notifications = getNotifications();
  const updatedNotifications = notifications.map(notification => 
    notification.id === id ? { ...notification, read: true } : notification
  );
  saveNotifications(updatedNotifications);
};

// Mark all notifications as read
const markAllAsRead = (): void => {
  const notifications = getNotifications();
  const updatedNotifications = notifications.map(notification => ({ ...notification, read: true }));
  saveNotifications(updatedNotifications);
};

// Delete a notification
const deleteNotification = (id: string): void => {
  const notifications = getNotifications();
  const updatedNotifications = notifications.filter(notification => notification.id !== id);
  saveNotifications(updatedNotifications);
};

// Clear all notifications
const clearAllNotifications = (): void => {
  saveNotifications([]);
};

// Add a personal information submission notification
const addPersonalInfoSubmissionNotification = (isUpdate: boolean = false): Notification => {
  return addNotification({
    title: isUpdate ? 'Personal Information Updated' : 'Personal Information Submitted',
    message: isUpdate
      ? 'Your personal information has been successfully updated.'
      : 'Your personal information has been successfully submitted.',
    type: 'success'
  });
};

// Export the notification service
const notificationService = {
  getNotifications,
  addNotification,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  clearAllNotifications,
  addPersonalInfoSubmissionNotification
};

export default notificationService;
