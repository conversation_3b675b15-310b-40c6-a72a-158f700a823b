# Django Backend Example for Authentication & Authorization
# This shows the simple Django REST API endpoints needed for the frontend

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import authenticate
from rest_framework.serializers import ModelSerializer
from django.db.models import Q

# Serializers for Django's built-in models
class UserSerializer(ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 
                 'is_active', 'is_staff', 'is_superuser', 'date_joined', 
                 'last_login', 'groups', 'user_permissions']
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user

class GroupSerializer(ModelSerializer):
    class Meta:
        model = Group
        fields = ['id', 'name', 'permissions']

class PermissionSerializer(ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'name', 'content_type', 'codename']

class ContentTypeSerializer(ModelSerializer):
    class Meta:
        model = ContentType
        fields = ['id', 'app_label', 'model']

# ViewSets for Django's built-in models
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = User.objects.all()
        search = self.request.query_params.get('search', None)
        is_active = self.request.query_params.get('is_active', None)
        is_staff = self.request.query_params.get('is_staff', None)
        
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(email__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search)
            )
        
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
            
        if is_staff is not None:
            queryset = queryset.filter(is_staff=is_staff.lower() == 'true')
            
        return queryset

    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        user = self.get_object()
        new_password = request.data.get('new_password')
        
        if new_password:
            user.set_password(new_password)
            user.save()
            return Response({'status': 'password changed'})
        
        return Response({'error': 'new_password required'}, 
                       status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def groups(self, request, pk=None):
        user = self.get_object()
        group_ids = request.data.get('groups', [])
        
        user.groups.clear()
        for group_id in group_ids:
            try:
                group = Group.objects.get(id=group_id)
                user.groups.add(group)
            except Group.DoesNotExist:
                pass
                
        return Response({'status': 'groups updated'})

    @action(detail=True, methods=['post'])
    def permissions(self, request, pk=None):
        user = self.get_object()
        permission_ids = request.data.get('permissions', [])
        
        user.user_permissions.clear()
        for perm_id in permission_ids:
            try:
                permission = Permission.objects.get(id=perm_id)
                user.user_permissions.add(permission)
            except Permission.DoesNotExist:
                pass
                
        return Response({'status': 'permissions updated'})

class GroupViewSet(viewsets.ModelViewSet):
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Group.objects.all()
        search = self.request.query_params.get('search', None)
        
        if search:
            queryset = queryset.filter(name__icontains=search)
            
        return queryset

    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        group = self.get_object()
        users = group.user_set.all()
        serializer = UserSerializer(users, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def users(self, request, pk=None):
        group = self.get_object()
        user_ids = request.data.get('users', [])
        
        for user_id in user_ids:
            try:
                user = User.objects.get(id=user_id)
                group.user_set.add(user)
            except User.DoesNotExist:
                pass
                
        return Response({'status': 'users added to group'})

    @action(detail=True, methods=['delete'])
    def users(self, request, pk=None):
        group = self.get_object()
        user_ids = request.data.get('users', [])
        
        for user_id in user_ids:
            try:
                user = User.objects.get(id=user_id)
                group.user_set.remove(user)
            except User.DoesNotExist:
                pass
                
        return Response({'status': 'users removed from group'})

class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Permission.objects.all()
        search = self.request.query_params.get('search', None)
        content_type = self.request.query_params.get('content_type', None)
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(codename__icontains=search)
            )
            
        if content_type:
            queryset = queryset.filter(content_type_id=content_type)
            
        return queryset

    @action(detail=False, methods=['get'])
    def by_content_type(self, request):
        permissions_by_type = {}
        content_types = ContentType.objects.all()
        
        for ct in content_types:
            permissions = Permission.objects.filter(content_type=ct)
            permissions_by_type[f"{ct.app_label}.{ct.model}"] = PermissionSerializer(permissions, many=True).data
            
        return Response(permissions_by_type)

class ContentTypeViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ContentType.objects.all()
    serializer_class = ContentTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

# Simple stats view
from rest_framework.views import APIView

class AuthStatsView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        stats = {
            'total_users': User.objects.count(),
            'active_users': User.objects.filter(is_active=True).count(),
            'staff_users': User.objects.filter(is_staff=True).count(),
            'superusers': User.objects.filter(is_superuser=True).count(),
            'total_groups': Group.objects.count(),
            'total_permissions': Permission.objects.count(),
        }
        return Response(stats)

# URL Configuration (urls.py)
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'users', views.UserViewSet)
router.register(r'groups', views.GroupViewSet)
router.register(r'permissions', views.PermissionViewSet)
router.register(r'content_types', views.ContentTypeViewSet)

urlpatterns = [
    path('auth/', include(router.urls)),
    path('auth/stats/', views.AuthStatsView.as_view(), name='auth-stats'),
]
"""

# Settings.py additions
"""
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    # your apps
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20
}
"""
