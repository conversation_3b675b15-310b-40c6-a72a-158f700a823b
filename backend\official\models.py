from django.db import models
from setups.certificate_type.models import CertificateType

# Constants
GENDER_CHOICES = [
    ('M', 'Male'),
    ('F', 'Female'),
]

class OfficialSent(models.Model):
    """Model for tracking certificates sent to external institutions"""
    first_name = models.Char<PERSON><PERSON>(max_length=100, help_text="Recipient's first name")
    second_name = models.CharField(max_length=100, help_text="Recipient's second/middle name")
    last_name = models.Char<PERSON>ield(max_length=100, help_text="Recipient's last name")
    gender = models.Char<PERSON>ield(max_length=1, choices=GENDER_CHOICES, help_text="Recipient's gender")
    receiver_institute = models.Char<PERSON>ield(max_length=255, help_text="Institution receiving the certificate")
    send_date = models.DateField(help_text="Date when the certificate was sent")
    courier = models.Char<PERSON>ield(max_length=100, help_text="Courier service used for delivery")
    certificate_type = models.ForeignKey(
        CertificateType,
        on_delete=models.PROTECT,
        related_name='sent_certificates',
        to_field='uuid',
        help_text="Type of certificate being sent"
    )
    tracking_number = models.CharField(
        max_length=100,
        unique=True,
        help_text="Unique tracking number for this shipment"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-send_date', '-created_at']
        verbose_name = 'Official Sent Certificate'
        verbose_name_plural = 'Official Sent Certificates'
        indexes = [
            models.Index(fields=['send_date']),
            models.Index(fields=['tracking_number']),
            models.Index(fields=['certificate_type']),
        ]

    @property
    def full_name(self):
        """Get full name combining first, second, and last names"""
        names = [self.first_name]
        if self.second_name:
            names.append(self.second_name)
        names.append(self.last_name)
        return ' '.join(names)

    @property
    def gender_display(self):
        """Get human-readable gender"""
        return dict(GENDER_CHOICES).get(self.gender, self.gender)

    def __str__(self):
        return f"{self.full_name} - {self.certificate_type.name} (Sent: {self.send_date})"

    def clean(self):
        """Validate the model data"""
        from django.core.exceptions import ValidationError

        # Validate names
        if not self.first_name or not self.first_name.strip():
            raise ValidationError({'first_name': 'First name is required.'})
        if not self.second_name or not self.second_name.strip():
            raise ValidationError({'second_name': 'Second name is required.'})
        if not self.last_name or not self.last_name.strip():
            raise ValidationError({'last_name': 'Last name is required.'})

        # Clean names
        self.first_name = self.first_name.strip()
        self.second_name = self.second_name.strip()
        self.last_name = self.last_name.strip()


class OfficialReceived(models.Model):
    """Model for tracking certificates received from external institutions"""
    first_name = models.CharField(max_length=100, help_text="Recipient's first name")
    second_name = models.CharField(max_length=100, help_text="Recipient's second/middle name")
    last_name = models.CharField(max_length=100, help_text="Recipient's last name")
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, help_text="Recipient's gender")
    sender_institute = models.CharField(max_length=255, help_text="Institution that sent the certificate")
    arival_date = models.DateField(help_text="Date when the certificate was received")  # Note: keeping original typo to match database
    courier = models.CharField(max_length=100, help_text="Courier service used for delivery")
    certificate_type = models.ForeignKey(
        CertificateType,
        on_delete=models.PROTECT,
        related_name='received_certificates',
        to_field='uuid',
        help_text="Type of certificate received"
    )
    tracking_number = models.CharField(
        max_length=100,
        unique=True,
        help_text="Unique tracking number for this shipment"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-arival_date', '-created_at']
        verbose_name = 'Official Received Certificate'
        verbose_name_plural = 'Official Received Certificates'
        indexes = [
            models.Index(fields=['arival_date']),
            models.Index(fields=['tracking_number']),
            models.Index(fields=['certificate_type']),
        ]

    @property
    def full_name(self):
        """Get full name combining first, second, and last names"""
        names = [self.first_name]
        if self.second_name:
            names.append(self.second_name)
        names.append(self.last_name)
        return ' '.join(names)

    @property
    def gender_display(self):
        """Get human-readable gender"""
        return dict(GENDER_CHOICES).get(self.gender, self.gender)

    def __str__(self):
        return f"{self.full_name} - {self.certificate_type.name} (Received: {self.arival_date})"

    def clean(self):
        """Validate the model data"""
        from django.core.exceptions import ValidationError

        # Validate names
        if not self.first_name or not self.first_name.strip():
            raise ValidationError({'first_name': 'First name is required.'})
        if not self.second_name or not self.second_name.strip():
            raise ValidationError({'second_name': 'Second name is required.'})
        if not self.last_name or not self.last_name.strip():
            raise ValidationError({'last_name': 'Last name is required.'})

        # Clean names
        self.first_name = self.first_name.strip()
        self.second_name = self.second_name.strip()
        self.last_name = self.last_name.strip()
