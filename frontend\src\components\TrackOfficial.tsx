import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Package, PackageCheck, Calendar, Building, User, Hash, FileSearch, Truck, MapPin } from 'lucide-react';
import { toast } from 'sonner';
import Layout from './Layout';
import DocumentTitle from './DocumentTitle';

interface CertificateType {
  uuid: string;
  name: string;
  description: string;
  is_active: boolean;
}

interface OfficialSent {
  id: number;
  first_name: string;
  second_name: string;
  last_name: string;
  full_name: string;
  gender: string;
  gender_display: string;
  receiver_institute: string;
  send_date: string;
  courier: string;
  certificate_type: string;
  certificate_type_details: CertificateType;
  certificate_type_name: string;
  tracking_number: string;
  created_at: string;
  updated_at: string;
}

interface OfficialReceived {
  id: number;
  first_name: string;
  second_name: string;
  last_name: string;
  full_name: string;
  gender: string;
  gender_display: string;
  sender_institute: string;
  arival_date: string;
  courier: string;
  certificate_type: string;
  certificate_type_details: CertificateType;
  certificate_type_name: string;
  tracking_number: string;
  created_at: string;
  updated_at: string;
}

const TrackOfficial: React.FC = () => {
  // Hero pattern image
  const heroPatternImage = '/images/university-pattern.svg';

  // State for organization name and loading state
  const [orgName, setOrgName] = useState<string>('');
  const [isOrgLoading, setIsOrgLoading] = useState<boolean>(true);

  const [sentCertificates, setSentCertificates] = useState<OfficialSent[]>([]);
  const [receivedCertificates, setReceivedCertificates] = useState<OfficialReceived[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('sent');

  // Pagination state for server-side pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Statistics state
  const [stats, setStats] = useState({ sent_count: 0, received_count: 0, total_count: 0 });

  // Fetch organization name
  useEffect(() => {
    // Try to get from localStorage first for immediate display
    const cachedOrgName = localStorage.getItem('organizationName');
    if (cachedOrgName) {
      setOrgName(cachedOrgName);
      setIsOrgLoading(false);
    }

    const fetchOrganizationName = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/settings/organization/public/', {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data && data.organization) {
            setOrgName(data.organization);
            localStorage.setItem('organizationName', data.organization);
          }
        }
      } catch (error) {
        console.error('Error fetching organization name:', error);
      } finally {
        setIsOrgLoading(false);
      }
    };

    fetchOrganizationName();
  }, []);

  // Fetch statistics on component mount
  useEffect(() => {
    const loadStats = async () => {
      const statsData = await fetchStats();
      setStats(statsData);
    };
    loadStats();
  }, []);

  // Debounced search to avoid too many API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchCertificates();
    }, searchTerm ? 500 : 0); // 500ms delay for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [currentPage, itemsPerPage, searchTerm, activeTab]);

  // API service for efficient data fetching
  const fetchCertificatesPage = async (type: 'sent' | 'received', page: number, pageSize: number, search?: string) => {
    try {
      const baseUrl = type === 'sent'
        ? 'http://localhost:8000/api/public/official/sent/'
        : 'http://localhost:8000/api/public/official/received/';

      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString(),
      });

      if (search && search.trim()) {
        params.append('search', search.trim());
      }

      const response = await fetch(`${baseUrl}?${params}`);
      if (response.ok) {
        return await response.json();
      } else {
        throw new Error(`Failed to fetch ${type} certificates`);
      }
    } catch (error) {
      console.error(`Error fetching ${type} certificates:`, error);
      throw error;
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/public/official/stats/');
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
    return { sent_count: 0, received_count: 0, total_count: 0 };
  };

  const fetchCertificates = async () => {
    try {
      setLoading(true);

      // Fetch current page data based on active tab
      const data = await fetchCertificatesPage(
        activeTab as 'sent' | 'received',
        currentPage,
        itemsPerPage,
        searchTerm
      );

      if (activeTab === 'sent') {
        setSentCertificates(data.results || []);
      } else {
        setReceivedCertificates(data.results || []);
      }

      // Update pagination info
      setTotalPages(data.total_pages || 1);
      setTotalCount(data.count || 0);

    } catch (error) {
      console.error('Error fetching certificates:', error);
      toast.error('Error loading certificates');
    } finally {
      setLoading(false);
    }
  };

  // Server-side filtering is handled by the API, so we use the data directly
  const filteredSentCertificates = sentCertificates;
  const filteredReceivedCertificates = receivedCertificates;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Server-side pagination - data is already paginated
  const currentSentItems = filteredSentCertificates;
  const currentReceivedItems = filteredReceivedCertificates;

  // Reset to first page when search term or active tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, activeTab]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  // Calculate pagination info for display
  const indexOfFirstItem = (currentPage - 1) * itemsPerPage + 1;
  const indexOfLastItem = Math.min(currentPage * itemsPerPage, totalCount);

  return (
    <Layout>
      <DocumentTitle pageTitle="Track Official Certificates" />

      {/* Hero Section with Animated Patterned Background */}
      <section className="relative py-6 md:py-8 h-[200px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Enhanced for tracking theme */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-3 mr-4">
              <FileSearch className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-2xl md:text-3xl font-bold text-white text-center leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
              Track Official Certificates
            </h1>
          </div>
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-3xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Search and track official certificates from {isOrgLoading ? '' : orgName || 'University of Gondar'}.
            Use tracking numbers, recipient names, or institutions to find certificates.
          </p>
        </div>
      </section>

      {/* Main Content */}
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
        <div className="container mx-auto px-4">

          {/* Quick Stats */}
          <div className="mb-8 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200 shadow-md">
              <div className="flex items-center">
                <div className="bg-green-500 rounded-full p-2 mr-3">
                  <Package className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-sm text-green-600 font-medium">Sent Certificates</p>
                  <p className="text-2xl font-bold text-green-700">{stats.sent_count}</p>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200 shadow-md">
              <div className="flex items-center">
                <div className="bg-blue-500 rounded-full p-2 mr-3">
                  <PackageCheck className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-sm text-blue-600 font-medium">Received Certificates</p>
                  <p className="text-2xl font-bold text-blue-700">{stats.received_count}</p>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200 shadow-md">
              <div className="flex items-center">
                <div className="bg-purple-500 rounded-full p-2 mr-3">
                  <FileSearch className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-sm text-purple-600 font-medium">Total Certificates</p>
                  <p className="text-2xl font-bold text-purple-700">{stats.total_count}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Certificates Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
              <TabsList className="grid grid-cols-2 bg-white shadow-md rounded-lg p-2 w-full lg:w-auto lg:min-w-[600px] h-16">
                <TabsTrigger
                  value="sent"
                  className="flex items-center justify-center py-4 px-8 rounded-md transition-all duration-200 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#1a73c0] data-[state=active]:to-[#0e4a7d] data-[state=active]:text-white data-[state=active]:shadow-md h-12"
                >
                  <div className="flex items-center gap-3">
                    <div className="bg-current/20 rounded-full p-1.5">
                      <Truck className="h-4 w-4" />
                    </div>
                    <span className="font-medium text-sm">Sent Certificates</span>
                    <Badge variant="secondary" className="bg-current/20 text-current border-0 px-2 py-0.5 text-xs font-semibold">
                      {stats.sent_count}
                    </Badge>
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="received"
                  className="flex items-center justify-center py-4 px-8 rounded-md transition-all duration-200 data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#1a73c0] data-[state=active]:to-[#0e4a7d] data-[state=active]:text-white data-[state=active]:shadow-md h-12"
                >
                  <div className="flex items-center gap-3">
                    <div className="bg-current/20 rounded-full p-1.5">
                      <PackageCheck className="h-4 w-4" />
                    </div>
                    <span className="font-medium text-sm">Received Certificates</span>
                    <Badge variant="secondary" className="bg-current/20 text-current border-0 px-2 py-0.5 text-xs font-semibold">
                      {stats.received_count}
                    </Badge>
                  </div>
                </TabsTrigger>
              </TabsList>

              {/* Search Input */}
              <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto lg:min-w-[450px]">
                <div className="flex-1 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by tracking number, name, or institution..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 h-12 border-2 border-gray-200 focus:border-[#1a73c0] transition-colors bg-white shadow-md rounded-lg text-sm"
                  />
                </div>
                <Button
                  onClick={fetchCertificates}
                  disabled={loading}
                  className="h-12 px-6 bg-[#1a73c0] hover:bg-[#0e4a7d] text-white font-medium transition-all duration-200 shadow-md hover:shadow-lg whitespace-nowrap rounded-lg"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      <span className="text-sm">Loading...</span>
                    </>
                  ) : (
                    <>
                      <Package className="mr-2 h-4 w-4" />
                      <span className="text-sm">Refresh</span>
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Sent Certificates Tab */}
            <TabsContent value="sent">
              <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
                <CardContent className="p-6">
                  {loading ? (
                    <div className="text-center py-12">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
                      <p className="text-gray-600">Loading certificates...</p>
                    </div>
                  ) : filteredSentCertificates.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                        <Package className="h-8 w-8 text-gray-400 mx-auto" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Sent Certificates Found</h3>
                      <p className="text-gray-500">No sent certificates found matching your search criteria.</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto bg-white rounded-lg border border-gray-200">
                      <Table>
                        <TableHeader>
                          <TableRow className="bg-gray-50 border-b border-gray-200">
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <Hash className="mr-2 h-4 w-4" />
                                Tracking Number
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <User className="mr-2 h-4 w-4" />
                                Full Name
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">Certificate Type</TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <Building className="mr-2 h-4 w-4" />
                                Institution
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <Calendar className="mr-2 h-4 w-4" />
                                Send Date
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <Truck className="mr-2 h-4 w-4" />
                                Courier
                              </div>
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {currentSentItems.map((cert, index) => (
                            <TableRow
                              key={cert.id}
                              className={`hover:bg-green-50 transition-colors border-b border-gray-100 ${
                                index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
                              }`}
                            >
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-green-100 rounded-full p-1 mr-3">
                                    <Hash className="h-3 w-3 text-green-600" />
                                  </div>
                                  <span className="font-mono font-medium text-green-700 bg-green-50 px-2 py-1 rounded">
                                    {cert.tracking_number}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-blue-100 rounded-full p-1 mr-3">
                                    <User className="h-3 w-3 text-blue-600" />
                                  </div>
                                  <div>
                                    <div className="font-medium text-gray-900">{cert.full_name}</div>
                                    <div className="text-sm text-gray-500 flex items-center mt-1">
                                      <span className="bg-gray-100 px-2 py-0.5 rounded text-xs">
                                        {cert.gender_display}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <Badge
                                  variant="secondary"
                                  className="bg-purple-100 text-purple-700 border-purple-200 hover:bg-purple-200"
                                >
                                  {cert.certificate_type_name}
                                </Badge>
                              </TableCell>
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-orange-100 rounded-full p-1 mr-3">
                                    <Building className="h-3 w-3 text-orange-600" />
                                  </div>
                                  <span className="font-medium text-gray-700">{cert.receiver_institute}</span>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-indigo-100 rounded-full p-1 mr-3">
                                    <Calendar className="h-3 w-3 text-indigo-600" />
                                  </div>
                                  <span className="text-gray-700">{formatDate(cert.send_date)}</span>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <Badge
                                  variant="outline"
                                  className="border-gray-300 text-gray-600 hover:bg-gray-50"
                                >
                                  <Truck className="mr-1 h-3 w-3" />
                                  {cert.courier}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}

                  {/* Pagination for Sent Certificates */}
                  {activeTab === 'sent' && totalCount > 0 && (
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
                      <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                        <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                        <select
                          value={itemsPerPage}
                          onChange={(e) => handleItemsPerPageChange(e.target.value)}
                          className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                        >
                          <option value="5">5</option>
                          <option value="10">10</option>
                          <option value="25">25</option>
                          <option value="50">50</option>
                        </select>
                        <span className="text-sm text-gray-600">entries</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(1)}
                          disabled={currentPage === 1}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                          </svg>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </Button>

                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                          const shouldShowPage = page === 1 ||
                                                page === totalPages ||
                                                Math.abs(page - currentPage) <= 1;

                          if (!shouldShowPage) {
                            if (page === currentPage - 2 || page === currentPage + 2) {
                              return (
                                <span key={`ellipsis-${page}`} className="flex items-center justify-center h-8 px-1 text-xs text-gray-500">
                                  ...
                                </span>
                              );
                            }
                            return null;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className={`px-2 py-1 h-8 min-w-[32px] text-xs border-blue-200 hover:bg-blue-50 ${currentPage === page ? 'bg-[#1a73c0] hover:bg-[#145da1] border-[#1a73c0]' : ''}`}
                            >
                              {page}
                            </Button>
                          );
                        })}

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          disabled={currentPage === totalPages}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                          </svg>
                        </Button>
                      </div>

                      <div className="text-sm text-gray-600 bg-white px-3 py-2 rounded-md border border-blue-200 shadow-sm">
                        Showing {indexOfFirstItem} to {indexOfLastItem} of {totalCount} entries
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Received Certificates Tab */}
            <TabsContent value="received">
              <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
                <CardContent className="p-6">
                  {loading ? (
                    <div className="text-center py-12">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                      <p className="text-gray-600">Loading certificates...</p>
                    </div>
                  ) : filteredReceivedCertificates.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                        <PackageCheck className="h-8 w-8 text-gray-400 mx-auto" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Received Certificates Found</h3>
                      <p className="text-gray-500">No received certificates found matching your search criteria.</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto bg-white rounded-lg border border-gray-200">
                      <Table>
                        <TableHeader>
                          <TableRow className="bg-gray-50 border-b border-gray-200">
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <Hash className="mr-2 h-4 w-4" />
                                Tracking Number
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <User className="mr-2 h-4 w-4" />
                                Full Name
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">Certificate Type</TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <Building className="mr-2 h-4 w-4" />
                                Sending Institution
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <Calendar className="mr-2 h-4 w-4" />
                                Arrival Date
                              </div>
                            </TableHead>
                            <TableHead className="font-semibold text-gray-700 py-4">
                              <div className="flex items-center">
                                <PackageCheck className="mr-2 h-4 w-4" />
                                Courier
                              </div>
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {currentReceivedItems.map((cert, index) => (
                            <TableRow
                              key={cert.id}
                              className={`hover:bg-blue-50 transition-colors border-b border-gray-100 ${
                                index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
                              }`}
                            >
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-blue-100 rounded-full p-1 mr-3">
                                    <Hash className="h-3 w-3 text-blue-600" />
                                  </div>
                                  <span className="font-mono font-medium text-blue-700 bg-blue-50 px-2 py-1 rounded">
                                    {cert.tracking_number}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-green-100 rounded-full p-1 mr-3">
                                    <User className="h-3 w-3 text-green-600" />
                                  </div>
                                  <div>
                                    <div className="font-medium text-gray-900">{cert.full_name}</div>
                                    <div className="text-sm text-gray-500 flex items-center mt-1">
                                      <span className="bg-gray-100 px-2 py-0.5 rounded text-xs">
                                        {cert.gender_display}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <Badge
                                  variant="secondary"
                                  className="bg-purple-100 text-purple-700 border-purple-200 hover:bg-purple-200"
                                >
                                  {cert.certificate_type_name}
                                </Badge>
                              </TableCell>
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-orange-100 rounded-full p-1 mr-3">
                                    <Building className="h-3 w-3 text-orange-600" />
                                  </div>
                                  <span className="font-medium text-gray-700">{cert.sender_institute}</span>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <div className="flex items-center">
                                  <div className="bg-indigo-100 rounded-full p-1 mr-3">
                                    <Calendar className="h-3 w-3 text-indigo-600" />
                                  </div>
                                  <span className="text-gray-700">{formatDate(cert.arival_date)}</span>
                                </div>
                              </TableCell>
                              <TableCell className="py-4">
                                <Badge
                                  variant="outline"
                                  className="border-gray-300 text-gray-600 hover:bg-gray-50"
                                >
                                  <PackageCheck className="mr-1 h-3 w-3" />
                                  {cert.courier}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}

                  {/* Pagination for Received Certificates */}
                  {activeTab === 'received' && totalCount > 0 && (
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg shadow-sm">
                      <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-md border border-blue-200 shadow-sm">
                        <span className="text-sm font-medium text-[#1a73c0]">Show</span>
                        <select
                          value={itemsPerPage}
                          onChange={(e) => handleItemsPerPageChange(e.target.value)}
                          className="border border-blue-200 rounded-md p-1.5 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all"
                        >
                          <option value="5">5</option>
                          <option value="10">10</option>
                          <option value="25">25</option>
                          <option value="50">50</option>
                        </select>
                        <span className="text-sm text-gray-600">entries</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(1)}
                          disabled={currentPage === 1}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                          </svg>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </Button>

                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                          const shouldShowPage = page === 1 ||
                                                page === totalPages ||
                                                Math.abs(page - currentPage) <= 1;

                          if (!shouldShowPage) {
                            if (page === currentPage - 2 || page === currentPage + 2) {
                              return (
                                <span key={`ellipsis-${page}`} className="flex items-center justify-center h-8 px-1 text-xs text-gray-500">
                                  ...
                                </span>
                              );
                            }
                            return null;
                          }

                          return (
                            <Button
                              key={page}
                              variant={currentPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className={`px-2 py-1 h-8 min-w-[32px] text-xs border-blue-200 hover:bg-blue-50 ${currentPage === page ? 'bg-[#1a73c0] hover:bg-[#145da1] border-[#1a73c0]' : ''}`}
                            >
                              {page}
                            </Button>
                          );
                        })}

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(totalPages)}
                          disabled={currentPage === totalPages}
                          className="px-2 py-1 h-8 min-w-[32px] border-blue-200 hover:bg-blue-50"
                        >
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                          </svg>
                        </Button>
                      </div>

                      <div className="text-sm text-gray-600 bg-white px-3 py-2 rounded-md border border-blue-200 shadow-sm">
                        Showing {indexOfFirstItem} to {indexOfLastItem} of {totalCount} entries
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default TrackOfficial;
