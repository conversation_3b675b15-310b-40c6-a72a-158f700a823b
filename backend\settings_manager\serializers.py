from rest_framework import serializers
from .models import OrganizationSetting, QuickLink, SocialMediaLink


class OrganizationSettingSerializer(serializers.ModelSerializer):
    header_logo_url = serializers.SerializerMethodField()
    footer_logo_url = serializers.SerializerMethodField()
    favicon_url = serializers.SerializerMethodField()
    
    class Meta:
        model = OrganizationSetting
        fields = [
            'id', 'system_name', 'organization', 'copyright', 'contact_info',
            'contact_number', 'support_email', 'address', 'po_box',
            'header_logo', 'footer_logo', 'favicon', 'primary_color', 'secondary_color',
            'header_logo_url', 'footer_logo_url', 'favicon_url', 'updated_at'
        ]
        read_only_fields = ['updated_at']
    
    def get_header_logo_url(self, obj):
        if obj.header_logo:
            return obj.header_logo.url
        return None
    
    def get_footer_logo_url(self, obj):
        if obj.footer_logo:
            return obj.footer_logo.url
        return None
    
    def get_favicon_url(self, obj):
        if obj.favicon:
            return obj.favicon.url
        return None


class QuickLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuickLink
        fields = [
            'id', 'name', 'url', 'description', 'is_external',
            'order', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class SocialMediaLinkSerializer(serializers.ModelSerializer):
    platform_display = serializers.CharField(source='get_platform_display', read_only=True)
    icon_url = serializers.SerializerMethodField()
    
    class Meta:
        model = SocialMediaLink
        fields = [
            'id', 'platform', 'platform_display', 'url', 'icon', 'icon_url',
            'display_name', 'order', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'platform_display', 'icon_url']
    
    def get_icon_url(self, obj):
        if obj.icon:
            return obj.icon.url
        return None
