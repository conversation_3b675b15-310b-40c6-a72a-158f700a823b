<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Confirmation - University of Gondar</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
        }
        .header h2 {
            color: #64748b;
            margin: 5px 0 0 0;
            font-size: 18px;
            font-weight: normal;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .details-section {
            background-color: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2563eb;
        }
        .details-section h3 {
            color: #2563eb;
            margin-top: 0;
            font-size: 18px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #475569;
        }
        .detail-value {
            color: #1e293b;
            font-weight: 500;
        }
        .payment-section {
            background-color: #fef3c7;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #f59e0b;
        }
        .payment-section h3 {
            color: #92400e;
            margin-top: 0;
        }
        .payment-steps {
            list-style: none;
            padding: 0;
        }
        .payment-steps li {
            background-color: #ffffff;
            margin: 8px 0;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid #f59e0b;
        }
        .transaction-highlight {
            background-color: #dbeafe;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 15px 0;
            border: 2px solid #2563eb;
        }
        .transaction-highlight strong {
            color: #1d4ed8;
            font-size: 18px;
        }
        .next-steps {
            background-color: #f0fdf4;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #16a34a;
        }
        .next-steps h3 {
            color: #166534;
            margin-top: 0;
        }
        .next-steps ul {
            margin: 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin: 8px 0;
            color: #15803d;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e2e8f0;
            color: #64748b;
            font-size: 14px;
        }
        .footer a {
            color: #2563eb;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .warning {
            background-color: #fef2f2;
            color: #991b1b;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc2626;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>UNIVERSITY OF GONDAR</h1>
            <h2>Alumni Services Portal</h2>
        </div>

        <div class="greeting">
            <p>Dear <strong>{{ applicant_name }}</strong>,</p>
            <p>Thank you for submitting your <strong>{{ application_type }}</strong> through our Alumni Services Portal. We have successfully received your application and all required documents.</p>
        </div>

        <div class="details-section">
            <h3>📋 Application Details</h3>
            <div class="detail-row">
                <span class="detail-label">Applicant Name:</span>
                <span class="detail-value">{{ applicant_name }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Service Type:</span>
                <span class="detail-value">{{ service_type }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Service Fee:</span>
                <span class="detail-value">{{ service_fee }} ETB</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Submission Date:</span>
                <span class="detail-value">{{ submission_date }}</span>
            </div>
        </div>

        <div class="transaction-highlight">
            <p><strong>Transaction ID: {{ transaction_id }}</strong></p>
            <p style="margin: 5px 0; color: #64748b;">Keep this ID for all future communications</p>
        </div>

        <div class="payment-section">
            <h3>💳 Payment Instructions</h3>
            <p>Complete your payment using <strong>TeleBirr</strong> mobile payment service:</p>
            <ol class="payment-steps">
                <li>Open your TeleBirr mobile app</li>
                <li>Select "Pay Bills" or "Service Payment"</li>
                <li>Use Transaction ID: <strong>{{ transaction_id }}</strong></li>
                <li>Enter amount: <strong>{{ service_fee }} ETB</strong></li>
                <li>Complete the payment process</li>
            </ol>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> Complete your payment within 24 hours to avoid application cancellation.
        </div>

        <div class="next-steps">
            <h3>📌 Next Steps & Important Information</h3>
            <ul>
                <li><strong>Payment Deadline:</strong> Complete payment within 24 hours</li>
                <li><strong>Processing Time:</strong> 3-5 business days after payment confirmation</li>
                <li><strong>Status Updates:</strong> You will receive email notifications about your application status</li>
                <li><strong>Reference Number:</strong> Keep your Transaction ID ({{ transaction_id }}) for all future communications</li>
            </ul>
        </div>

        <div class="footer">
            <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:{{ support_email }}">{{ support_email }}</a>.</p>
            <p>Visit our portal: <a href="{{ portal_url }}">{{ portal_url }}</a></p>
            
            <hr style="margin: 20px 0; border: none; border-top: 1px solid #e2e8f0;">
            
            <p><em>This is an automated message from the University of Gondar Alumni Services Portal.<br>
            Please do not reply to this email.</em></p>
            
            <p style="margin-top: 20px;">
                <strong>University of Gondar</strong><br>
                Alumni Services Department<br>
                Email: <a href="mailto:{{ support_email }}">{{ support_email }}</a><br>
                Portal: <a href="{{ portal_url }}">{{ portal_url }}</a>
            </p>
        </div>
    </div>
</body>
</html>
