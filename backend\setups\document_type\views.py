from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from .models import DocumentType
from .serializers import DocumentTypeSerializer

class DocumentTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing document types.
    Provides CRUD operations for document types.
    """
    queryset = DocumentType.objects.all()
    serializer_class = DocumentTypeSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = DocumentType.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            if is_active.lower() in ['true', '1']:
                queryset = queryset.filter(is_active=True)
            elif is_active.lower() in ['false', '0']:
                queryset = queryset.filter(is_active=False)

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.order_by('name')

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get only active document types."""
        active_types = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(active_types, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_status(self, request, id=None):
        """Toggle the active status of a document type."""
        try:
            document_type = self.get_object()
            document_type.is_active = not document_type.is_active
            document_type.save()

            serializer = self.get_serializer(document_type)
            return Response({
                'message': f'Document type {"activated" if document_type.is_active else "deactivated"} successfully.',
                'data': serializer.data
            })
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
