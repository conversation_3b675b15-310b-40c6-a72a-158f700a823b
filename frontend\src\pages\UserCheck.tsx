import { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from 'sonner';
import axios from 'axios';
import Layout from '@/components/Layout';

const API_BASE_URL = 'http://localhost:8000/api';

const userCheckSchema = z.object({
  identifier: z.string().min(1, 'Please enter a username or email')
});

type UserCheckFormValues = z.infer<typeof userCheckSchema>;

const UserCheck = () => {
  const [result, setResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<UserCheckFormValues>({
    resolver: zodResolver(userCheckSchema),
    defaultValues: {
      identifier: '',
    },
  });

  const onSubmit = async (data: UserCheckFormValues) => {
    setIsLoading(true);
    setResult(null);
    
    try {
      // Check if input looks like an email
      const isEmail = data.identifier.includes('@');
      
      const response = await axios.post(`${API_BASE_URL}/check-user/`, {
        [isEmail ? 'email' : 'username']: data.identifier
      });
      
      setResult(response.data);
      
      if (response.data.exists) {
        toast.success('User found!');
      } else {
        toast.error('User not found');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Error checking user');
      console.error('Error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-[calc(100vh-64px-152px)] flex flex-col items-center">
        <Card className="w-full max-w-md shadow-lg mb-8">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">
              Check User
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="identifier"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username or Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter username or email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? 'Checking...' : 'Check User'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
        
        {result && (
          <Card className="w-full max-w-md shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl font-bold">
                Result
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </Layout>
  );
};

export default UserCheck;
