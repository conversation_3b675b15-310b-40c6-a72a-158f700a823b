import { toast } from '@/hooks/use-toast';

/**
 * Helper functions for displaying toast notifications with consistent styling
 */

export const showSuccessToast = (message: string, title: string = 'Success') => {
  toast({
    title,
    description: message,
    variant: 'success',
  });
};

export const showErrorToast = (message: string, title: string = 'Error') => {
  toast({
    title,
    description: message,
    variant: 'error',
  });
};

export const showWarningToast = (message: string, title: string = 'Warning') => {
  toast({
    title,
    description: message,
    variant: 'warning',
  });
};

export const showInfoToast = (message: string, title: string = 'Information') => {
  toast({
    title,
    description: message,
    variant: 'info',
  });
};

/**
 * Show a toast with custom options
 */
export const showCustomToast = (
  message: string,
  options: {
    title?: string;
    variant?: 'default' | 'destructive' | 'success' | 'error' | 'warning' | 'info';
    duration?: number;
  } = {}
) => {
  const {
    title = 'Notification',
    variant = 'default',
    duration
  } = options;

  toast({
    title,
    description: message,
    variant,
    duration,
  });
};
