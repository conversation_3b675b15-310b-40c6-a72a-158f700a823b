#!/usr/bin/env python
"""
Test script to verify recent graduates API works
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.utils import timezone
from GraduateVerification.models import GraduateStudent

def test_recent_graduates_query():
    """Test the recent graduates query without audit trail fields"""
    print("=== Testing Recent Graduates Query ===")
    
    # Get current time
    now = timezone.now()
    threshold = now - timedelta(days=7)
    
    print(f"Current time: {now}")
    print(f"7 days ago: {threshold}")
    
    try:
        # Test basic query first
        print("\n1. Testing basic query...")
        recent_graduates = GraduateStudent.objects.filter(
            created_at__gte=threshold
        ).select_related(
            'college', 'department', 'field_of_study', 'program', 
            'admission_classification'
        ).order_by('-created_at')
        
        print(f"✓ Basic query successful: {recent_graduates.count()} graduates found")
        
        # Test with audit trail fields
        print("\n2. Testing with audit trail fields...")
        try:
            recent_graduates_with_audit = GraduateStudent.objects.filter(
                created_at__gte=threshold
            ).select_related(
                'college', 'department', 'field_of_study', 'program', 
                'admission_classification', 'created_by', 'updated_by'
            ).order_by('-created_at')
            
            print(f"✓ Audit trail query successful: {recent_graduates_with_audit.count()} graduates found")
            audit_fields_exist = True
        except Exception as e:
            print(f"✗ Audit trail query failed: {e}")
            audit_fields_exist = False
        
        # Test serialization
        print("\n3. Testing serialization...")
        if recent_graduates.exists():
            graduate = recent_graduates.first()
            print(f"Sample graduate: {graduate.get_full_name()}")
            print(f"Created at: {graduate.created_at}")
            
            # Test audit trail access
            if audit_fields_exist:
                try:
                    created_by = getattr(graduate, 'created_by', None)
                    updated_by = getattr(graduate, 'updated_by', None)
                    print(f"Created by: {created_by}")
                    print(f"Updated by: {updated_by}")
                except Exception as e:
                    print(f"Audit trail access error: {e}")
        
        # Test counts for different periods
        print("\n4. Testing period counts...")
        periods = {
            'today': now - timedelta(days=1),
            'last_3_days': now - timedelta(days=3),
            'last_7_days': now - timedelta(days=7),
            'last_15_days': now - timedelta(days=15),
            'last_30_days': now - timedelta(days=30),
        }
        
        for period_name, period_threshold in periods.items():
            count = GraduateStudent.objects.filter(created_at__gte=period_threshold).count()
            print(f"  {period_name}: {count} graduates")
        
        print("\n✅ All tests passed!")
        print("\n💡 Next steps:")
        if not audit_fields_exist:
            print("1. Apply the database migration to add audit trail fields:")
            print("   - Run the SQL script: backend/add_audit_fields.sql")
            print("   - Or apply Django migration: python manage.py migrate GraduateVerification")
        print("2. Restart your Django server")
        print("3. Test the API endpoint: GET /graduate-verifications/recent/?period=7")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure Django server is not running")
        print("2. Check database connection")
        print("3. Verify GraduateStudent model exists")

if __name__ == '__main__':
    test_recent_graduates_query()
