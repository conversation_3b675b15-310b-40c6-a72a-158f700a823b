"""
Email service for alumni applications.
Handles sending confirmation emails to applicants.
"""

import logging
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.utils import timezone
from settings_manager.smtp_models import SMTPSettings
from communication.models import EmailNotification
from .models import AlumniApplication, AlumniApplicationMini
from setups.service_type.models import ServiceType

logger = logging.getLogger(__name__)


class AlumniApplicationEmailService:
    """Service for sending emails related to alumni applications."""

    @staticmethod
    def get_smtp_settings():
        """Get current SMTP settings and apply them to Django settings."""
        try:
            smtp_settings = SMTPSettings.load()

            # Apply SMTP settings to Django configuration
            if smtp_settings and smtp_settings.host:
                smtp_settings.apply_to_settings()

                # Force Django to use SMTP backend
                settings.EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
                logger.info(f"Applied SMTP settings: {smtp_settings.host}:{smtp_settings.port}")

                return smtp_settings
            else:
                # Use console backend for development/testing
                settings.EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
                logger.warning("No SMTP host configured, using console backend for email output")
                return None

        except Exception as e:
            logger.error(f"Failed to load SMTP settings: {e}")
            # Fallback to console backend
            settings.EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
            return None

    @staticmethod
    def send_application_confirmation(application, is_form1=True):
        """
        Send confirmation email to applicant after successful application submission.

        Args:
            application: AlumniApplication or AlumniApplicationMini instance
            is_form1: Boolean indicating if this is form1 (complete) or form2 (simplified)

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            logger.info(f"Starting email confirmation for application {application.id}")

            # Validate email address
            if not AlumniApplicationEmailService._is_valid_email(application.email):
                logger.warning(f"Invalid email address: {application.email}")
                AlumniApplicationEmailService._log_email_failure(
                    application, f"Invalid email address: {application.email}", is_form1
                )
                return False

            # Get SMTP settings
            smtp_settings = AlumniApplicationEmailService.get_smtp_settings()

            # Get service type information (service_type is already a ForeignKey)
            service_type = application.service_type
            logger.info(f"Service type: {service_type.name if service_type else 'None'}")

            # Prepare email context
            context = {
                'applicant_name': f"{application.first_name} {application.father_name} {application.last_name}",
                'transaction_id': application.transaction_id,
                'service_type': service_type.name if service_type else 'Unknown Service',
                'service_fee': service_type.fee if service_type else '0.00',
                'application_type': 'Complete Application' if is_form1 else 'Simplified Application',
                'form_type': 'form1' if is_form1 else 'form2',
                'application_id': str(application.id),
                'submission_date': application.created_at.strftime('%B %d, %Y at %I:%M %p'),
                'support_email': '<EMAIL>',
                'portal_url': 'http://localhost:8080',  # TODO: Make this configurable
            }

            logger.info(f"Email context prepared for {context['applicant_name']}")

            # Render email templates
            try:
                html_content = render_to_string('alumni_applications/confirmation_email.html', context)
                text_content = render_to_string('alumni_applications/confirmation_email.txt', context)
                logger.info("Email templates rendered successfully")
            except Exception as template_error:
                logger.error(f"Template rendering failed: {template_error}")
                # Try to send plain text email as fallback
                text_content = f"""
Dear {context['applicant_name']},

Your alumni application has been submitted successfully.

Transaction ID: {context['transaction_id']}
Service Type: {context['service_type']}
Service Fee: {context['service_fee']} ETB

Please keep your transaction ID for future reference.

University of Gondar
Alumni Services
                """
                html_content = None

            # Create email
            subject = f"Application Confirmation - Transaction #{application.transaction_id}"

            # Determine from email
            if smtp_settings and smtp_settings.from_email:
                from_email = smtp_settings.from_email
            else:
                from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')

            to_email = application.email

            logger.info(f"Sending email from {from_email} to {to_email}")

            # Send email using EmailMultiAlternatives for HTML support
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=from_email,
                to=[to_email]
            )

            if html_content:
                email.attach_alternative(html_content, "text/html")

            # Send the email
            try:
                result = email.send()
                logger.info(f"Email send result: {result}")

                if result > 0:
                    # Log successful email in communication system
                    try:
                        AlumniApplicationEmailService._log_email_communication(
                            application=application,
                            subject=subject,
                            content=text_content,
                            recipient_email=to_email,
                            is_form1=is_form1
                        )
                        logger.info("Email logged in communication system")
                    except Exception as log_error:
                        logger.error(f"Failed to log email communication: {log_error}")
                        # Don't fail the email sending if logging fails

                    logger.info(f"Confirmation email sent successfully to {to_email} for application {application.id}")
                    return True
                else:
                    logger.warning(f"Email send returned 0 - no emails sent to {to_email}")
                    AlumniApplicationEmailService._log_email_failure(application, "Email send returned 0", is_form1)
                    return False

            except Exception as send_error:
                logger.error(f"SMTP error sending email to {to_email}: {send_error}")
                AlumniApplicationEmailService._log_email_failure(application, str(send_error), is_form1)
                return False

        except Exception as e:
            logger.error(f"Failed to send confirmation email for application {application.id}: {e}")

            # Try to log the failure
            try:
                AlumniApplicationEmailService._log_email_failure(application, str(e), is_form1)
            except:
                pass  # Don't fail if logging fails

            return False

    @staticmethod
    def _log_email_communication(application, subject, content, recipient_email, is_form1):
        """Log the sent email in the communication system."""
        try:
            from django.contrib.auth.models import User

            # Get or create a system user for automated emails
            system_user, created = User.objects.get_or_create(
                username='system_email',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'System',
                    'last_name': 'Email Service',
                    'is_active': False,  # System user, not for login
                }
            )

            EmailNotification.objects.create(
                subject=subject,
                content=content,
                sender=system_user,
                recipients=recipient_email,
                status='sent',
                sent_time=timezone.now()
            )
            logger.info(f"Email notification logged for application {application.id}")
        except Exception as e:
            logger.error(f"Failed to log email notification for application {application.id}: {e}")
            # Don't fail the email sending if logging fails

    @staticmethod
    def _log_email_failure(application, error_message, is_form1):
        """Log failed email attempt in the communication system."""
        try:
            from django.contrib.auth.models import User

            # Get or create a system user for automated emails
            system_user, created = User.objects.get_or_create(
                username='system_email',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'System',
                    'last_name': 'Email',
                    'is_active': False,
                }
            )

            # Create failed email notification
            EmailNotification.objects.create(
                subject=f"FAILED: Application Confirmation - Transaction #{application.transaction_id}",
                content=f"Failed to send confirmation email to {application.email}. Error: {error_message}",
                sender=system_user,
                recipients=application.email,
                status='failed',
                sent_time=timezone.now()
            )

            logger.info(f"Email failure logged for application {application.id}")
            return True

        except Exception as e:
            logger.error(f"Failed to log email failure: {e}")
            return False

    @staticmethod
    def _is_valid_email(email):
        """Validate email address format and domain."""
        import re

        # Basic email format validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False

        # Check for reserved test domains (RFC 2606)
        test_domains = [
            'example.com', 'example.org', 'example.net',
            'test.com', 'test.org', 'test.net',
            'localhost', '127.0.0.1'
        ]

        domain = email.split('@')[1].lower()
        if domain in test_domains:
            logger.warning(f"Email domain {domain} is reserved for testing")
            return False

        return True

    @staticmethod
    def send_test_email(recipient_email, test_message="This is a test email from the Alumni Portal."):
        """
        Send a test email to verify SMTP configuration.
        
        Args:
            recipient_email: Email address to send test email to
            test_message: Custom test message
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Get SMTP settings
            smtp_settings = AlumniApplicationEmailService.get_smtp_settings()
            if not smtp_settings or not smtp_settings.host:
                logger.warning("SMTP not configured, cannot send test email")
                return False

            subject = "Test Email - Alumni Portal"
            from_email = smtp_settings.from_email or settings.DEFAULT_FROM_EMAIL

            send_mail(
                subject=subject,
                message=test_message,
                from_email=from_email,
                recipient_list=[recipient_email],
                fail_silently=False
            )

            logger.info(f"Test email sent successfully to {recipient_email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send test email to {recipient_email}: {e}")
            return False
