import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Search, X } from 'lucide-react';

interface Option {
  id: number | string;
  name: string;
  [key: string]: any;
}

interface SearchableSelectProps {
  options: Option[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder: string;
  className?: string;
  error?: string;
  id: string;
  searchPlaceholder?: string;
}

const SearchableSelect: React.FC<SearchableSelectProps> = ({
  options,
  value,
  onValueChange,
  placeholder,
  className = '',
  error,
  id,
  searchPlaceholder = 'Search...'
}) => {
  // State for search term
  const [searchTerm, setSearchTerm] = useState('');

  // Get the selected option name for display
  const selectedOption = options.find(option => option.id.toString() === value);
  const selectedName = selectedOption ? selectedOption.name : '';

  // Filter options based on search term
  const filteredOptions = searchTerm.trim() === ''
    ? options
    : options.filter(option =>
        option.name.toLowerCase().includes(searchTerm.toLowerCase())
      );

  return (
    <div className="relative">
      <Select
        value={value}
        onValueChange={(newValue) => {
          onValueChange(newValue);
          setSearchTerm(''); // Reset search when an option is selected
        }}
        onOpenChange={(open) => {
          // Reset search term when dropdown closes
          if (!open) {
            setSearchTerm('');
          }
        }}
      >
        <SelectTrigger
          id={id}
          className={className}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>

        <SelectContent
          position="popper"
          className="p-0"
          sideOffset={5}
        >
          {/* Search box */}
          <div className="p-2 sticky top-0 bg-white border-b border-gray-100 z-10" onClick={(e) => e.stopPropagation()}>
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />

              {searchTerm && (
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setSearchTerm('');
                  }}
                >
                  <X className="h-4 w-4" />
                </button>
              )}

              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={searchPlaceholder}
                className="pl-8 pr-8 py-1 h-8 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === 'Escape') {
                    e.preventDefault();
                    e.stopPropagation();
                  }
                }}
                autoComplete="off"
                spellCheck="false"
              />
            </div>
          </div>

          {/* Options list */}
          <div className="max-h-[200px] overflow-y-auto p-1">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <SelectItem
                  key={option.id}
                  value={option.id.toString()}
                  className="hover:bg-blue-50"
                >
                  {option.name}
                </SelectItem>
              ))
            ) : (
              <div className="py-3 px-2 text-center text-gray-500 italic text-sm">
                No results matching your search
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
};

export default SearchableSelect;
