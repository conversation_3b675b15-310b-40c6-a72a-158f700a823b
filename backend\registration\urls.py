from django.urls import path
from . import views  # Change this line - import views directly from the app
from . import views_status  # Import the views_status module
from . import views_statistics  # Import the views_statistics module
from . import views_staff  # Import the staff views module

urlpatterns = [
    # ApplicantInformation URLs
    path('applicant-info/', views.ApplicantInformationList.as_view(), name='applicant-info-list'),
    path('applicant-info/<uuid:pk>/', views.ApplicantInformationDetail.as_view(), name='applicant-info-detail'),

    # ApplicantGAT URLs
    path('applicant-gat/', views.ApplicantGATList.as_view(), name='applicant-gat-list'),
    path('applicant-gat/<int:pk>/', views.ApplicantGATDetail.as_view(), name='applicant-gat-detail'),

    # ApplicantProgramSelection URLs
    path('program-selection/', views.ApplicantProgramSelectionList.as_view(), name='program-selection-list'),
    path('program-selection/<int:pk>/', views.ApplicantProgramSelectionDetail.as_view(), name='program-selection-detail'),

    # ApplicantDocumentation URLs
    path('documentation/', views.ApplicantDocumentationList.as_view(), name='documentation-list'),
    path('documentation/<int:pk>/', views.ApplicantDocumentationDetail.as_view(), name='documentation-detail'),

    # ApplicantPayment URLs
    path('payment/', views.ApplicantPaymentList.as_view(), name='payment-list'),
    path('payment/gat/<int:gat_id>/', views.PaymentByGATView.as_view(), name='payment-by-gat'),
    path('payment/<int:pk>/', views.ApplicantPaymentDetail.as_view(), name='payment-detail'),

    # Phone Number API
    path('get-phone-number/', views.GetPhoneNumberView.as_view(), name='get-phone-number'),

    # Application Status API (public)
    path('application-status/', views_status.ApplicationStatusView.as_view(), name='application-status'),

    # Application Statistics API (staff only)
    path('application-statistics/', views_statistics.ApplicationStatisticsView.as_view(), name='application-statistics'),

    # Staff Applicant Management URLs
    path('staff/applicants/', views_staff.ApplicantListView.as_view(), name='staff-applicant-list'),
    path('staff/applicants/<int:user_id>/documents/', views_staff.ApplicantDocumentsView.as_view(), name='staff-applicant-documents'),
    path('staff/applicants/<int:pk>/update-status/', views_staff.ApplicantStatusUpdateView.as_view(), name='staff-applicant-status-update'),
    path('staff/applicants/<int:pk>/', views_staff.ApplicantDetailView.as_view(), name='staff-applicant-detail'),
]
