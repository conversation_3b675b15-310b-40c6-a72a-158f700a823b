import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '@/components/Layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loader2, Search, UserCheck, FileSpreadsheet } from 'lucide-react';
import { toast } from 'sonner';
import { graduateVerificationAPI, settingsAPI } from '@/services/api';
import FloatingHexagons from '@/components/FloatingHexagons';
import CustomSearchableSelect from '@/components/CustomSearchableSelect';
import DocumentTitle from '@/components/DocumentTitle';

// Define interfaces for our data models
interface Graduate {
  id: number;
  student_id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  year_of_entry: number;
  year_of_graduation: number;
  gpa: number | string;
  gender: 'Male' | 'Female';
  college: {
    id: number;
    name: string;
    code: string;
  };
  department: {
    id: number;
    name: string;
    code: string;
  };
  field_of_study: {
    id: number;
    name: string;
    duration?: number;
  };
  program: {
    id: number;
    name: string;
    code: string;
  };
  admission_classification: {
    id: number;
    name: string;
  };
  photo?: string;
  full_name: string;
}

interface VerificationCollege {
  id: number;
  name: string;
  code: string;
}

interface VerificationDepartment {
  id: number;
  name: string;
  code: string;
  college: number;
}

interface VerificationFieldOfStudy {
  id: number;
  name: string;
  department: number;
  duration?: number;
}

interface VerificationProgram {
  id: number;
  name: string;
  code: string;
}

interface AdmissionClassification {
  id: number;
  name: string;
}

const GraduateVerification = () => {
  // Initialize navigate function from React Router
  const navigate = useNavigate();

  // Custom pattern background for hero section
  const heroPatternImage = '/images/university-pattern.svg';

  // Check if user is authenticated and staff
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  const userDataString = localStorage.getItem('user');
  const userData = userDataString ? JSON.parse(userDataString) : null;
  const isStaff = userData?.is_staff === true;

  // No tabs needed for public verification page

  // State for organization settings
  const [settings, setSettings] = useState<{
    systemName: string;
    organizationName: string;
  }>({
    systemName: '',
    organizationName: ''
  });

  const [isSettingsLoading, setIsSettingsLoading] = useState(true);

  // Fetch organization settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsSettingsLoading(true);
      try {
        const response = await settingsAPI.getPublicOrganizationSettings();
        if (response.data) {
          setSettings({
            systemName: response.data.system_name || '',
            organizationName: response.data.organization || ''
          });
        }
      } catch (error) {
        console.error('Error fetching organization settings:', error);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // State for form inputs
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [searchType, setSearchType] = useState<'gpa' | 'student_id'>('student_id');
  const [searchValue, setSearchValue] = useState('');
  const [graduationYear, setGraduationYear] = useState('');
  const [fieldOfStudy, setFieldOfStudy] = useState('');
  const [college, setCollege] = useState('');
  const [department, setDepartment] = useState('');
  const [program, setProgram] = useState('');
  const [admissionClassification, setAdmissionClassification] = useState('');

  // State for search results
  const [searchResults, setSearchResults] = useState<Graduate[]>([]);
  const [loading, setLoading] = useState(false);
  const [searched, setSearched] = useState(false);
  const [selectedGraduate, setSelectedGraduate] = useState<Graduate | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [resultsPerPage] = useState(5);

  // Modal states
  const [isResultsModalOpen, setIsResultsModalOpen] = useState(false);
  const [isNoResultsModalOpen, setIsNoResultsModalOpen] = useState(false);
  const [isCertificateModalOpen, setIsCertificateModalOpen] = useState(false);


  // Validation states
  const [errors, setErrors] = useState<{
    firstName?: string;
    lastName?: string;
    searchValue?: string;
    graduationYear?: string;
    fieldOfStudy?: string;
    college?: string;
    department?: string;
    program?: string;
    admissionClassification?: string;
  }>({});

  // We'll use only real data from the API as requested

  // State for dropdown options
  const [colleges, setColleges] = useState<VerificationCollege[]>([]);
  const [departments, setDepartments] = useState<VerificationDepartment[]>([]);
  const [fields, setFields] = useState<VerificationFieldOfStudy[]>([]); // Start with empty array
  const [programs, setPrograms] = useState<VerificationProgram[]>([]);
  const [admissionTypes, setAdmissionTypes] = useState<AdmissionClassification[]>([]);
  const [years, setYears] = useState<number[]>([]);

  // Loading states for different API calls
  const [loadingStates, setLoadingStates] = useState({
    colleges: false,
    departments: false,
    fields: false,
    programs: false,
    admissionTypes: false,
    search: false,
    certificate: false
  });

  // Load dropdown options on component mount
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        // Generate years (from 1990 to current year) - this doesn't depend on API
        const currentYear = new Date().getFullYear();
        const yearsList = Array.from({ length: currentYear - 1989 }, (_, i) => currentYear - i);
        setYears(yearsList);

        // Set all loading states to true
        setLoadingStates(prev => ({
          ...prev,
          colleges: true,
          departments: true,
          fields: true,
          programs: true,
          admissionTypes: true
        }));

        try {
          // We'll use only real data from the API



          // Fetch all data in parallel for better performance
          console.log('Fetching dropdown options from API...');

          // Try to fetch data from API
          let collegesResponse, departmentsResponse, fieldsResponse, programsResponse, admissionResponse;

          try {
            collegesResponse = await graduateVerificationAPI.getColleges();
          } catch (error) {
            console.error('Error fetching colleges:', error);
            collegesResponse = { data: [] };
          }

          try {
            departmentsResponse = await graduateVerificationAPI.getDepartments();
          } catch (error) {
            console.error('Error fetching departments:', error);
            departmentsResponse = { data: [] };
          }

          try {
            fieldsResponse = await graduateVerificationAPI.getFieldsOfStudy();
            // Log the fields response to debug
            console.log('Fields of study response:', fieldsResponse);
            console.log('Fields of study data type:', typeof fieldsResponse.data);
            console.log('Is fields data an array?', Array.isArray(fieldsResponse.data));

            // Check if we got valid data
            if (!fieldsResponse.data || !Array.isArray(fieldsResponse.data) || fieldsResponse.data.length === 0) {
              console.warn('Fields data is empty or not an array');
              toast.error('Failed to load fields of study data from API');
              fieldsResponse = { data: [] };
            } else {
              console.log('Using real fields data from API, count:', fieldsResponse.data.length);

              // Ensure each field has the required properties
              fieldsResponse.data = fieldsResponse.data.map((field: any) => {
                // If the field doesn't have an id or name, skip it
                if (!field.id || !field.name) {
                  console.warn('Field missing required properties:', field);
                  return null;
                }

                // Create a properly formatted field object
                const formattedField: VerificationFieldOfStudy = {
                  id: field.id,
                  name: field.name,
                  department: field.department || 0,
                  duration: field.duration || 4
                };

                return formattedField;
              }).filter((field: VerificationFieldOfStudy | null) => field !== null) as VerificationFieldOfStudy[];

              console.log('Processed fields data:', fieldsResponse.data);
            }
          } catch (error) {
            console.error('Error fetching fields of study:', error);
            toast.error('Failed to load fields of study data from API');
            fieldsResponse = { data: [] };
          }

          try {
            programsResponse = await graduateVerificationAPI.getPrograms();
          } catch (error) {
            console.error('Error fetching programs:', error);
            programsResponse = { data: [] };
          }

          try {
            admissionResponse = await graduateVerificationAPI.getAdmissionClassifications();
          } catch (error) {
            console.error('Error fetching admission classifications:', error);
            admissionResponse = { data: [] };
          }

          console.log('API responses received:', {
            colleges: collegesResponse.data?.length || 0,
            departments: departmentsResponse.data?.length || 0,
            fields: fieldsResponse.data?.length || 0,
            programs: programsResponse.data?.length || 0,
            admissionTypes: admissionResponse.data?.length || 0
          });

          // Set state with the responses, ensuring they are arrays
          setColleges(Array.isArray(collegesResponse.data) ? collegesResponse.data : []);
          setDepartments(Array.isArray(departmentsResponse.data) ? departmentsResponse.data : []);

          // For fields, ensure we have an array with the correct structure
          if (Array.isArray(fieldsResponse.data)) {
            setFields(fieldsResponse.data);
          } else if (fieldsResponse.data && typeof fieldsResponse.data === 'object') {
            // If it's an object with results property (common API pattern)
            if (Array.isArray(fieldsResponse.data.results)) {
              setFields(fieldsResponse.data.results);
            } else {
              // Data is not in expected format
              console.warn('Fields data is not in expected format');
            }
          }

          setPrograms(Array.isArray(programsResponse.data) ? programsResponse.data : []);
          setAdmissionTypes(Array.isArray(admissionResponse.data) ? admissionResponse.data : []);

          // Check if we got data for all dropdowns
          if (!collegesResponse.data?.length || !departmentsResponse.data?.length ||
              !fieldsResponse.data?.length || !programsResponse.data?.length) {
            console.warn('Some dropdown options are empty:', {
              colleges: collegesResponse.data?.length || 0,
              departments: departmentsResponse.data?.length || 0,
              fields: fieldsResponse.data?.length || 0,
              programs: programsResponse.data?.length || 0
            });
            toast.warning('Some form options could not be loaded. Using default values where needed.');
          }
        } catch (error: any) {
          console.error('Error fetching options:', error);

          // Provide more specific error messages
          if (error.response) {
            console.error('Error response:', error.response.data);
            toast.error(`Failed to load options: ${error.response.data.detail || 'Server error'}`);
          } else if (error.request) {
            toast.error('No response from server. Please check your connection.');
          } else {
            toast.error(`An error occurred: ${error.message}`);
          }
        } finally {
          // Set all loading states back to false
          setLoadingStates(prev => ({
            ...prev,
            colleges: false,
            departments: false,
            fields: false,
            programs: false,
            admissionTypes: false
          }));
        }
      } catch (error) {
        console.error('Unexpected error in fetchOptions:', error);
        toast.error('An unexpected error occurred while loading form options');

        // Make sure loading states are reset even if there's an error
        setLoadingStates(prev => ({
          ...prev,
          colleges: false,
          departments: false,
          fields: false,
          programs: false,
          admissionTypes: false
        }));
      }
    };

    fetchOptions();
  }, []);

  // Validate form inputs
  const validateForm = () => {
    const newErrors: {
      firstName?: string;
      lastName?: string;
      searchValue?: string;
      graduationYear?: string;
      fieldOfStudy?: string;
      college?: string;
      department?: string;
      program?: string;
      admissionClassification?: string;
    } = {};

    // Validate name fields - both first name and last name are required
    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    // Validate search value based on type
    if (!searchValue.trim()) {
      newErrors.searchValue = `${searchType === 'gpa' ? 'GPA' : 'Student ID'} is required`;
    } else if (searchType === 'student_id' && !/^\d+$/.test(searchValue.trim())) {
      newErrors.searchValue = 'Student ID must contain only numbers';
    } else if (searchType === 'gpa') {
      const gpa = parseFloat(searchValue);
      if (isNaN(gpa) || gpa < 2.0 || gpa > 4.0) {
        newErrors.searchValue = 'GPA must be between 2.0 and 4.0';
      }
    }

    // Validate graduation year - required for filtering
    if (!graduationYear) {
      newErrors.graduationYear = 'Graduation year is required';
    }

    // Validate field of study - required for filtering
    if (!fieldOfStudy) {
      newErrors.fieldOfStudy = 'Field of study is required';
    }

    // These fields are no longer required for public verification
    // College, department, program, and admission classification
    // have been removed from the form

    // Log validation results
    console.log('Validation results:', {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      searchType,
      searchValue: searchValue.trim(),
      graduationYear,
      fieldOfStudy,
      college,
      department,
      program,
      admissionClassification,
      errors: newErrors
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    // Validate form
    if (!validateForm()) {
      toast.error('Please correct the errors in the form');
      return;
    }

    // Update loading states
    setLoadingStates(prev => ({ ...prev, search: true }));
    setLoading(true);
    setSearched(true);
    setSearchResults([]);
    setCurrentPage(1); // Reset to first page

    try {
      // Build query parameters - include only the basic required fields
      const params = {
        first_name: firstName.trim(),
        last_name: lastName.trim(),
        year_of_graduation: graduationYear,
        field_of_study: fieldOfStudy,
        // Set default values for the removed fields
        college: '',
        department: '',
        program: '',
        admission_classification: ''
      };

      // Add either GPA or student_id to the query parameters based on search type
      if (searchType === 'gpa') {
        params['gpa'] = searchValue.trim();
        // Don't include student_id in this case
      } else {
        params['student_id'] = searchValue.trim();
        // Don't include gpa in this case
      }

      // Log the final parameters for debugging
      console.log('Final search parameters:', params);

      try {
        // Make API request
        const response = await graduateVerificationAPI.searchGraduates(params);

        // Check if we got a valid response
        if (response && response.data) {
          console.log('Search results:', response.data);
          setSearchResults(response.data);

          if (response.data.length === 0) {
            toast.info('No graduates found matching your criteria');
            setIsNoResultsModalOpen(true);
          } else {
            toast.success(`Found ${response.data.length} graduate${response.data.length > 1 ? 's' : ''}`);
            setIsResultsModalOpen(true);
          }
        } else {
          setSearchResults([]);
          toast.error('Failed to search graduates. Please try again later.');
        }
      } catch (apiError: any) {
        console.error('API search error:', apiError);

        // If we have a network error, show an error message
        if (apiError.message === 'Network Error') {
          console.log('Network error occurred');
          toast.error('Network error. Please check your connection and try again.');
          return;
        }

        throw apiError; // Re-throw to be caught by the outer catch block
      }
    } catch (error: any) {
      console.error('Error searching graduates:', error);

      // Provide more specific error messages
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response:', error.response.data);
        toast.error(`Search failed: ${error.response.data.detail || 'Server error'}`);
      } else if (error.request) {
        // The request was made but no response was received
        toast.error('No response from server. Please check your connection.');
      } else {
        // Something happened in setting up the request that triggered an Error
        toast.error(`An error occurred: ${error.message}`);
      }

      // Show the no results modal for any search errors
      setSearchResults([]);
      setIsNoResultsModalOpen(true);
    } finally {
      setLoading(false);
      setLoadingStates(prev => ({ ...prev, search: false }));
    }
  };



  // Reset form
  const handleReset = () => {
    setFirstName('');
    setLastName('');
    setSearchType('student_id');
    setSearchValue('');
    setGraduationYear('');
    setFieldOfStudy('');
    // No need to reset removed fields
    // setCollege('');
    // setDepartment('');
    // setProgram('');
    // setAdmissionClassification('');
    setSearchResults([]);
    setSearched(false);
    setIsResultsModalOpen(false);
    setIsNoResultsModalOpen(false);
    setIsCertificateModalOpen(false);
    setSelectedGraduate(null);
    setCurrentPage(1);
  };

  // Handle college selection and filter departments
  const handleCollegeChange = (value: string) => {
    setCollege(value);
    setDepartment(''); // Reset department when college changes
    setFieldOfStudy(''); // Reset field of study when college changes

    if (errors.college) {
      setErrors({ ...errors, college: undefined });
    }
  };

  // Handle department selection and filter fields of study
  const handleDepartmentChange = (value: string) => {
    setDepartment(value);
    setFieldOfStudy(''); // Reset field of study when department changes

    if (errors.department) {
      setErrors({ ...errors, department: undefined });
    }
  };

  // Get filtered departments based on selected college
  const getFilteredDepartments = () => {
    if (!college) return departments;
    return departments.filter(dept => dept.college === parseInt(college));
  };

  // Get filtered fields of study based on selected department
  const getFilteredFields = () => {
    if (!department) return fields;
    return fields.filter(field => field.department === parseInt(department));
  };

  // Handle pagination
  const indexOfLastResult = currentPage * resultsPerPage;
  const indexOfFirstResult = indexOfLastResult - resultsPerPage;
  const currentResults = searchResults.slice(indexOfFirstResult, indexOfLastResult);
  const totalPages = Math.ceil(searchResults.length / resultsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };



  // Handle certificate preview
  const handleCertificatePreview = (graduate: Graduate) => {
    setSelectedGraduate(graduate);
    setIsCertificateModalOpen(true);
  };





  return (
    <Layout>
      <DocumentTitle pageTitle="Graduate Verification" />
      {/* Hero Section with Animated Patterned Background - Half Height */}
      <section className="relative py-4 md:py-6 h-[175px] overflow-hidden">
        {/* Divi-inspired Background with Pattern Mask */}
        <div className="absolute inset-0">
          {/* Custom SVG Pattern Background with Divi-inspired animation */}
          <div
            className="absolute inset-0 bg-cover bg-center animate-pattern-shift"
            style={{ backgroundImage: `url(${heroPatternImage})` }}
          ></div>
        </div>

        {/* Content - Adjusted for reduced height with enhanced text readability */}
        <div className="container mx-auto px-6 flex flex-col items-center justify-center h-full relative z-10">
          <h1 className="text-xl md:text-2xl font-bold text-white text-center max-w-3xl leading-tight animate-fade-in relative z-10 drop-shadow-xl tracking-wide font-sans [text-shadow:_0_1px_10px_rgba(0,0,0,0.5)]">
            Graduate Verification
          </h1>
          <p className="mt-2 text-sm md:text-base text-white text-center max-w-2xl animate-slide-in relative z-10 drop-shadow-lg tracking-wide font-light [text-shadow:_0_1px_8px_rgba(0,0,0,0.4)]">
            Verify the credentials of our graduates with our secure and reliable verification system.
          </p>
        </div>
      </section>

      <div className="container mx-auto py-4 px-4 sm:px-6">

        {/* Remove tabs since statistics should not be public - only show verification form */}

          {/* Graduate Verification Content - No tabs needed for public access */}
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 md:gap-6">
              {/* Left Column - How to Use */}
              <div className="order-2 xl:order-1 xl:col-span-1">
                <Card className="border-l-4 border-[#1a73c0] shadow-lg h-full bg-gradient-to-br from-blue-50 to-white">
                  <CardHeader className="border-b border-blue-100 p-3 sm:p-6">
                    <div className="flex items-center space-x-2 sm:space-x-3">
                      <div className="bg-[#1a73c0] p-1.5 sm:p-2 rounded-full">
                        <svg className="h-4 w-4 sm:h-5 sm:w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <CardTitle className="text-[#1a73c0] text-base sm:text-lg">How to Use the Verification System</CardTitle>
                        <CardDescription className="text-xs sm:text-sm">
                          Follow these steps to verify graduate credentials
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-4 px-4">
                    <div className="flow-chart-container">
                      {/* Step 1 */}
                      <div className="flow-step">
                        <div className="flow-box bg-[#1a73c0]/10 border-[#1a73c0] hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                          <div className="flow-number">1</div>
                          <div className="flow-content">
                            <h3 className="flow-title">Enter Graduate Details</h3>
                            <p className="flow-description">Fill in the graduate's first and last name</p>
                          </div>
                          <div className="flow-icon">
                            <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                        </div>
                        <div className="flow-arrow">
                          <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>
                      </div>

                      {/* Step 2 */}
                      <div className="flow-step">
                        <div className="flow-box bg-[#1a73c0]/10 border-[#1a73c0] hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                          <div className="flow-number">2</div>
                          <div className="flow-content">
                            <h3 className="flow-title">Provide Identification</h3>
                            <p className="flow-description">Enter Student ID or GPA</p>
                          </div>
                          <div className="flow-icon">
                            <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                            </svg>
                          </div>
                        </div>
                        <div className="flow-arrow">
                          <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>
                      </div>

                      {/* Step 3 */}
                      <div className="flow-step">
                        <div className="flow-box bg-[#1a73c0]/10 border-[#1a73c0] hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                          <div className="flow-number">3</div>
                          <div className="flow-content">
                            <h3 className="flow-title">Select Graduation Year</h3>
                            <p className="flow-description">Choose year of graduation</p>
                          </div>
                          <div className="flow-icon">
                            <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                        </div>
                        <div className="flow-arrow">
                          <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>
                      </div>

                      {/* Step 4 */}
                      <div className="flow-step">
                        <div className="flow-box bg-[#1a73c0]/10 border-[#1a73c0] hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                          <div className="flow-number">4</div>
                          <div className="flow-content">
                            <h3 className="flow-title">Specify Field of Study</h3>
                            <p className="flow-description">Select the field of study</p>
                          </div>
                          <div className="flow-icon">
                            <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                          </div>
                        </div>
                        <div className="flow-arrow">
                          <svg className="h-6 w-6 text-[#1a73c0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>
                      </div>

                      {/* Step 5 - Final */}
                      <div className="flow-step">
                        <div className="flow-box bg-[#1a73c0] text-white hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                          <div className="flow-number bg-white text-[#1a73c0]">5</div>
                          <div className="flow-content">
                            <h3 className="flow-title text-white">View Results</h3>
                            <p className="flow-description text-white/90">Get complete graduate information</p>
                          </div>
                          <div className="flow-icon">
                            <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 p-3 bg-blue-100 rounded-lg border border-blue-200">
                        <h4 className="text-blue-800 font-medium flex items-center mb-2">
                          <svg className="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Important Notes
                        </h4>
                        <ul className="text-sm text-blue-700 space-y-2 pl-7 list-disc">
                          <li>All fields are required for verification</li>
                          <li>Information must match exactly as in our records</li>
                          <li>The system is case-insensitive</li>
                          <li>For privacy reasons, limited information is displayed</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Verification Form */}
              <div className="order-1 xl:order-2 xl:col-span-2">
                <Card className="border-t-4 border-[#1a73c0] shadow-lg">
                  <CardHeader className="bg-gray-50 rounded-t-lg p-3 sm:p-6">
                    <div className="flex items-center space-x-2 sm:space-x-3">
                      <div className="bg-[#1a73c0] p-1.5 sm:p-2 rounded-full">
                        <Search className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-base sm:text-lg">Verify Graduate Information</CardTitle>
                        <CardDescription className="text-xs sm:text-sm">
                          Enter the graduate's information to verify their credentials
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-3 sm:pt-4 px-3 sm:px-4">
                    <form onSubmit={handleSearch} className="space-y-4">
                      <div className="text-xs sm:text-sm bg-gradient-to-br from-gray-50 to-gray-100 p-3 sm:p-4 rounded-lg border-l-4 border-[#1a73c0] shadow-md relative overflow-hidden mb-3 sm:mb-4">
                        {/* Background Pattern */}
                        <div className="absolute inset-0 opacity-5">
                          <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                              <pattern id="dots" width="10" height="10" patternUnits="userSpaceOnUse">
                                <circle cx="5" cy="5" r="1" fill="#1a73c0" />
                              </pattern>
                            </defs>
                            <rect width="100" height="100" fill="url(#dots)" />
                          </svg>
                        </div>

                        {/* Content */}
                        <div className="relative z-10">
                          <h4 className="font-bold text-[#1a73c0] mb-2 sm:mb-3 flex items-center text-sm sm:text-base">
                            <div className="bg-[#1a73c0] p-1 rounded-full mr-1.5 sm:mr-2">
                              <svg className="h-3 w-3 sm:h-4 sm:w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </div>
                            Important Information
                          </h4>

                          <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-4 gap-2">
                            <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex flex-col items-center text-center shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                              <div className="bg-blue-100 p-1.5 rounded-full mb-2 flex-shrink-0">
                                <svg className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <span className="text-gray-700 text-xs">All fields marked with <span className="text-red-500 font-bold">*</span> are required</span>
                            </div>

                            <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex flex-col items-center text-center shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                              <div className="bg-red-100 p-1.5 rounded-full mb-2 flex-shrink-0">
                                <svg className="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                              </div>
                              <span className="text-gray-700 text-xs">Entries must match exactly</span>
                            </div>

                            <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex flex-col items-center text-center shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                              <div className="bg-purple-100 p-1.5 rounded-full mb-2 flex-shrink-0">
                                <svg className="h-4 w-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                              </div>
                              <span className="text-gray-700 text-xs">limited graduate information displayed</span>
                            </div>

                            <div className="bg-white/80 p-2 rounded-lg border border-gray-200 flex flex-col items-center text-center shadow-sm hover:shadow-md hover:-translate-y-0.5 transition-all duration-200">
                              <div className="bg-green-100 p-1.5 rounded-full mb-2 flex-shrink-0">
                                <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                              </div>
                              <span className="text-gray-700 text-xs font-medium">Secure & accurate results</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="firstName" className="text-sm font-medium text-gray-700 flex items-center">
                            <span>First Name</span> <span className="text-red-500 ml-1">*</span>
                            <div className="ml-1 group relative">
                              <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <div className="absolute bottom-full left-0 sm:left-1/2 transform sm:-translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
                                Enter the first name exactly as it appears on official documents
                              </div>
                            </div>
                          </Label>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                            <Input
                              id="firstName"
                              value={firstName}
                              onChange={(e) => {
                                setFirstName(e.target.value);
                                if (errors.firstName) {
                                  setErrors({ ...errors, firstName: undefined });
                                }
                              }}
                              placeholder="Enter first name"
                              className={`pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all ${errors.firstName ? 'border-red-500 shadow-sm shadow-red-200' : 'hover:border-blue-300'}`}
                            />
                            {errors.firstName && (
                              <p className="text-red-500 text-xs mt-1 flex items-center">
                                <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.firstName}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName" className="text-sm font-medium text-gray-700 flex items-center">
                            <span>Last Name</span> <span className="text-red-500 ml-1">*</span>
                            <div className="ml-1 group relative">
                              <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                Enter the last name exactly as it appears on official documents
                              </div>
                            </div>
                          </Label>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                            <Input
                              id="lastName"
                              value={lastName}
                              onChange={(e) => {
                                setLastName(e.target.value);
                                if (errors.lastName) {
                                  setErrors({ ...errors, lastName: undefined });
                                }
                              }}
                              placeholder="Enter last name"
                              className={`pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all ${errors.lastName ? 'border-red-500 shadow-sm shadow-red-200' : 'hover:border-blue-300'}`}
                            />
                            {errors.lastName && (
                              <p className="text-red-500 text-xs mt-1 flex items-center">
                                <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.lastName}
                              </p>
                            )}
                          </div>
                        </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="searchType" className="text-sm font-medium text-gray-700 flex items-center">
                            <span>Search By</span> <span className="text-red-500 ml-1">*</span>
                            <div className="ml-1 group relative">
                              <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                Choose whether to search by Student ID or GPA
                              </div>
                            </div>
                          </Label>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                              </svg>
                            </div>
                            <Select
                              value={searchType}
                              onValueChange={(value) => setSearchType(value as 'gpa' | 'student_id')}
                            >
                              <SelectTrigger id="searchType" className="pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all hover:border-blue-300">
                                <SelectValue placeholder="Select search type" />
                              </SelectTrigger>
                              <SelectContent className="bg-white border border-blue-100 shadow-md">
                                <SelectItem value="student_id" className="hover:bg-blue-50">Student ID</SelectItem>
                                <SelectItem value="gpa" className="hover:bg-blue-50">GPA</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="searchValue" className="text-sm font-medium text-gray-700 flex items-center">
                            <span>{searchType === 'gpa' ? 'GPA (2.0-4.0)' : 'Student ID'}</span> <span className="text-red-500 ml-1">*</span>
                            <div className="ml-1 group relative">
                              <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                {searchType === 'gpa' ? 'Enter GPA between 2.0 and 4.0' : 'Enter the student ID exactly as it appears on records'}
                              </div>
                            </div>
                          </Label>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                {searchType === 'gpa' ? (
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                ) : (
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                )}
                              </svg>
                            </div>
                            <Input
                              id="searchValue"
                              value={searchValue}
                              onChange={(e) => {
                                setSearchValue(e.target.value);
                                if (errors.searchValue) {
                                  setErrors({ ...errors, searchValue: undefined });
                                }
                              }}
                              placeholder={searchType === 'gpa' ? "Enter GPA (e.g., 3.5)" : "Enter student ID"}
                              type={searchType === 'gpa' ? "number" : "text"}
                              step={searchType === 'gpa' ? "0.01" : undefined}
                              min={searchType === 'gpa' ? "2.0" : undefined}
                              max={searchType === 'gpa' ? "4.0" : undefined}
                              className={`pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all ${errors.searchValue ? 'border-red-500 shadow-sm shadow-red-200' : 'hover:border-blue-300'}`}
                            />
                            {errors.searchValue && (
                              <p className="text-red-500 text-xs mt-1 flex items-center">
                                <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.searchValue}
                              </p>
                            )}
                          </div>
                        </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="graduationYear" className="text-sm font-medium text-gray-700 flex items-center">
                            <span>Year of Graduation</span> <span className="text-red-500 ml-1">*</span>
                            <div className="ml-1 group relative">
                              <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                Select the year when the graduate completed their studies
                              </div>
                            </div>
                            <div className="ml-auto flex items-center text-xs text-blue-600 font-medium">
                              <Search className="h-3 w-3 mr-1" />
                              <span>Searchable</span>
                            </div>
                          </Label>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                            </div>
                            <CustomSearchableSelect
                              id="graduationYear"
                              options={years.map(year => ({ id: year, name: year.toString() }))}
                              value={graduationYear}
                              onValueChange={(value) => {
                                setGraduationYear(value);
                                if (errors.graduationYear) {
                                  setErrors({ ...errors, graduationYear: undefined });
                                }
                              }}
                              placeholder="Select year"
                              searchPlaceholder="Search years..."
                              className={`pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all ${errors.graduationYear ? 'border-red-500 shadow-sm shadow-red-200' : 'hover:border-blue-300'}`}
                            />
                            {errors.graduationYear && (
                              <p className="text-red-500 text-xs mt-1 flex items-center">
                                <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.graduationYear}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="fieldOfStudy" className="text-sm font-medium text-gray-700 flex items-center">
                            <span>Field of Study</span> <span className="text-red-500 ml-1">*</span>
                            <div className="ml-1 group relative">
                              <svg className="h-4 w-4 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-800 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                                Select the graduate's specific field of study
                              </div>
                            </div>
                            <div className="ml-auto flex items-center text-xs text-blue-600 font-medium">
                              <Search className="h-3 w-3 mr-1" />
                              <span>Searchable</span>
                            </div>
                          </Label>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                              </svg>
                            </div>
                            <div className="relative">
                              <CustomSearchableSelect
                                id="fieldOfStudy"
                                options={fields}
                                value={fieldOfStudy}
                                onValueChange={(value) => {
                                  setFieldOfStudy(value);
                                  if (errors.fieldOfStudy) {
                                    setErrors({ ...errors, fieldOfStudy: undefined });
                                  }
                                }}
                                placeholder="Select field of study"
                                searchPlaceholder="Search fields..."
                                className={`pl-10 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all ${errors.fieldOfStudy ? 'border-red-500 shadow-sm shadow-red-200' : 'hover:border-blue-300'}`}
                              />
                            </div>
                            {errors.fieldOfStudy && (
                              <p className="text-red-500 text-xs mt-1 flex items-center">
                                <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.fieldOfStudy}
                              </p>
                            )}
                          </div>
                        </div>
                  </div>

                      <div className="pt-3 sm:pt-4 border-t border-gray-100 mt-3 sm:mt-4">
                        <div className="flex flex-col xs:flex-row justify-end gap-2 sm:gap-4">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleReset}
                            disabled={loading}
                            className="border-[#1a73c0]/30 text-[#1a73c0] hover:bg-[#1a73c0]/10 transition-colors group text-xs sm:text-sm w-full xs:w-auto"
                          >
                            <svg className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 transition-transform group-hover:rotate-180 duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Reset Form
                          </Button>
                          <Button
                            type="submit"
                            disabled={loading}
                            className="bg-[#1a73c0] hover:bg-[#145da1] transition-all shadow-md hover:shadow-lg transform hover:-translate-y-0.5 active:translate-y-0 text-xs sm:text-sm w-full xs:w-auto"
                          >
                            {loading ? (
                              <>
                                <Loader2 className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                                Searching...
                              </>
                            ) : (
                              <>
                                <Search className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                                Verify Graduate
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                </form>
              </CardContent>
                </Card>
              </div>
            </div>
            {/* Search Results will be shown in modals */}

          {/* Admin Tab - Removed as requested */}
          {false && (
            <div>
              <Card className="border-t-4 border-[#1a73c0] shadow-lg">
                <CardHeader className="bg-gray-50 rounded-t-lg">
                  <div className="flex items-center space-x-2">
                    <div className="bg-[#1a73c0] p-2 rounded-full">
                      <FileSpreadsheet className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle>Admin Tools</CardTitle>
                      <CardDescription>
                        Import graduate data or manage existing records.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-8 pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Manage Records Card */}
                    <div className="bg-gradient-to-br from-blue-50 to-white border border-blue-100 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#1a73c0] p-2 rounded-full mr-3">
                          <Users className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-lg font-medium text-[#1a73c0]">Manage Graduates</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-6 pl-12">
                        Access the admin dashboard to manage graduate records, add new graduates, or update existing information.
                      </p>
                      <div className="space-y-4">
                        <div className="bg-white p-3 rounded-md border border-gray-100 shadow-sm">
                          <div className="flex items-center text-sm text-gray-700">
                            <svg className="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Create, update, and delete graduate records</span>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100 shadow-sm">
                          <div className="flex items-center text-sm text-gray-700">
                            <svg className="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Manage verification data and credentials</span>
                          </div>
                        </div>
                      </div>
                      <div className="mt-6">
                        <Button
                          className="w-full bg-[#1a73c0] hover:bg-[#145da1] transition-colors shadow-md"
                          onClick={() => navigate('/graduate-admin?tab=manage')}
                        >
                          <Users className="mr-2 h-4 w-4" />
                          Manage Graduates
                        </Button>
                      </div>
                    </div>

                    {/* Manage Data Card */}
                    <div className="bg-gradient-to-br from-purple-50 to-white border border-purple-100 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-[#1a73c0] p-2 rounded-full mr-3">
                          <Building className="h-5 w-5 text-white" />
                        </div>
                        <h3 className="text-lg font-medium text-[#1a73c0]">Manage Data</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-6 pl-12">
                        Manage colleges, departments, fields of study, programs, and admission classifications.
                      </p>
                      <div className="space-y-4">
                        <div className="bg-white p-3 rounded-md border border-gray-100 shadow-sm">
                          <div className="flex items-center text-sm text-gray-700">
                            <svg className="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Update college and department information</span>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-md border border-gray-100 shadow-sm">
                          <div className="flex items-center text-sm text-gray-700">
                            <svg className="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Manage programs and fields of study</span>
                          </div>
                        </div>
                      </div>
                      <div className="mt-6">
                        <Button
                          className="w-full bg-[#1a73c0] hover:bg-[#145da1] transition-colors shadow-md"
                          onClick={() => navigate('/graduate-admin?tab=colleges')}
                        >
                          <Building className="mr-2 h-4 w-4" />
                          Manage Data
                        </Button>
                      </div>
                    </div>
                  </div>


                </CardContent>
              </Card>
            </div>
          )}

          {/* Reports Tab - Removed as requested */}
          {false && (
            <div>
              <Card className="border-t-4 border-[#1a73c0] shadow-lg">
                <CardHeader className="bg-gray-50 rounded-t-lg">
                  <div className="flex items-center space-x-2">
                    <div className="bg-[#1a73c0] p-2 rounded-full">
                      <BarChart3 className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle>Graduate Reports</CardTitle>
                      <CardDescription>
                        View reports and statistics about graduates.
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-8 pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-gradient-to-br from-blue-50 to-white border border-blue-100 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-blue-100 p-2 rounded-full mr-3">
                          <svg className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-blue-800">By Department</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-6">
                        View the distribution of graduates across different departments.
                      </p>
                      <Button className="w-full bg-gondar hover:bg-gondar-dark transition-colors shadow-md">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        Generate Report
                      </Button>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-white border border-purple-100 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-purple-100 p-2 rounded-full mr-3">
                          <svg className="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-purple-800">By Gender</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-6">
                        View the gender distribution of graduates.
                      </p>
                      <Button className="w-full bg-gondar hover:bg-gondar-dark transition-colors shadow-md">
                        <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                        </svg>
                        Generate Report
                      </Button>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-white border border-green-100 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center mb-4">
                        <div className="bg-green-100 p-2 rounded-full mr-3">
                          <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-green-800">By Year</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-6">
                        View the number of graduates per year.
                      </p>
                      <Button className="w-full bg-gondar hover:bg-gondar-dark transition-colors shadow-md">
                        <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                        </svg>
                        Generate Report
                      </Button>
                    </div>
                  </div>

                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
                    <div className="flex items-center mb-4">
                      <svg className="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-700">About Reports</h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      These reports provide valuable insights into graduate demographics and trends. Use them to:
                    </p>
                    <ul className="list-disc pl-6 text-sm text-gray-600 space-y-1">
                      <li>Analyze graduation rates by department</li>
                      <li>Track gender distribution across programs</li>
                      <li>Monitor yearly graduation trends</li>
                      <li>Generate data for institutional reporting</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
      </div>

      {/* Search Results Modal - Enhanced Mobile Responsive */}
<Dialog open={isResultsModalOpen} onOpenChange={setIsResultsModalOpen}>
  <DialogContent className="w-[100vw] h-[100vh] sm:w-[96vw] sm:h-[95vh] sm:max-w-[96vw] md:max-w-[92vw] lg:max-w-[88vw] xl:max-w-[85vw] 2xl:max-w-[80vw] sm:max-h-[95vh] flex flex-col p-0 sm:rounded-lg">
    <DialogHeader className="pb-3 sm:pb-4 border-b border-gray-200 px-3 sm:px-6 pt-3 sm:pt-6 flex-shrink-0 bg-white">
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
        <div className="flex items-center min-w-0 flex-1">
          <div className="bg-[#1a73c0] p-2 rounded-full mr-3 flex-shrink-0">
            <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <div className="min-w-0 flex-1">
            <DialogTitle className="text-base sm:text-lg md:text-xl font-bold text-[#1a73c0] leading-tight">
              Verification Results
            </DialogTitle>
            <DialogDescription className="text-sm sm:text-sm font-medium text-gray-700 mt-1">
              {searchResults.length} graduate{searchResults.length > 1 ? 's' : ''} found
            </DialogDescription>
          </div>
        </div>
        <div className="bg-blue-50 text-[#1a73c0] px-3 py-1.5 rounded-full text-xs sm:text-sm font-bold flex-shrink-0 self-start sm:self-center">
          {new Date().toLocaleDateString()}
        </div>
      </div>
    </DialogHeader>

    <div className="flex-1 overflow-y-auto px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 sm:bg-transparent">
      <div className="flex flex-col gap-3 sm:gap-4">
      {currentResults.map((graduate) => (
        <Card
          key={graduate.id}
          className="overflow-hidden border-0 sm:border border-gray-200 shadow-md sm:shadow-sm hover:shadow-lg transition-all duration-300 bg-white rounded-xl sm:rounded-lg"
        >
          <div className="flex flex-col w-full">
            {/* Mobile Header Section */}
            <div className="sm:hidden bg-gradient-to-r from-[#1a73c0] to-[#2a8bd5] p-4 text-white relative overflow-hidden">
              <div className="absolute inset-0 opacity-10" style={{
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'1\' fill-rule=\'evenodd\'%3E%3Ccircle cx=\'3\' cy=\'3\' r=\'1\'/%3E%3Ccircle cx=\'13\' cy=\'13\' r=\'1\'/%3E%3C/g%3E%3C/svg%3E")'
              }}></div>
              <div className="relative z-10 flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-bold truncate">{graduate.full_name}</h3>
                  <p className="text-sm text-white/90 truncate mt-1">
                    {graduate.program.name}
                  </p>
                </div>
                <div className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-bold ml-3 flex-shrink-0">
                  {graduate.year_of_graduation}
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row w-full">
              {/* Photo Section - Mobile Optimized */}
              <div className="w-full sm:w-32 md:w-40 lg:w-48 relative flex-shrink-0">
                <div className="hidden sm:block absolute top-2 right-2 bg-[#1a73c0] text-white px-2 py-1 text-xs font-medium rounded-md z-10 shadow-sm">
                  {graduate.year_of_graduation}
                </div>
                <div className="aspect-[4/3] sm:aspect-[4/5] w-full overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100">
                  <img
                    src="/images/verification-badge.png"
                    alt="Verified Graduate"
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Program Duration - Bottom of Photo */}
                {graduate.field_of_study.duration && (
                  <div className="mt-3 sm:mt-2 mx-4 sm:mx-0 bg-gradient-to-br from-amber-50 to-amber-100 p-3 sm:p-2 rounded-lg border border-amber-300 shadow-sm">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="bg-gradient-to-br from-amber-500 to-amber-600 p-1.5 sm:p-1 rounded-full mr-2 shadow-sm">
                          <svg className="h-4 w-4 sm:h-3 sm:w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="text-amber-800 text-sm sm:text-xs font-medium">Duration</div>
                      </div>
                      <div className="flex items-baseline">
                        <div className="font-bold text-amber-700 text-base sm:text-sm mr-1">{graduate.field_of_study.duration}</div>
                        <div className="text-amber-600 text-sm sm:text-xs font-medium">years</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Details Section - Mobile Optimized */}
              <div className="flex-1 p-4 sm:p-4 flex flex-col">
                <div className="hidden sm:flex flex-col gap-2 mb-3 pb-3 border-b border-gray-100">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-base sm:text-lg font-bold text-[#1a73c0] mb-1">{graduate.full_name}</h3>
                      <p className="text-sm text-gray-600 font-medium line-clamp-2">
                        {graduate.program.name} in {graduate.field_of_study.name}
                      </p>
                    </div>
                    <div className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-xs font-bold flex items-center self-start flex-shrink-0">
                      <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      Verified
                    </div>
                  </div>
                </div>

                {/* Mobile Program Info */}
                <div className="sm:hidden mb-4 bg-blue-50 p-3 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-semibold text-[#1a73c0]">Field of Study</h4>
                    <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center">
                      <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
                      </svg>
                      Verified
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 font-medium leading-relaxed">
                    {graduate.field_of_study.name}
                  </p>
                </div>

                {/* Mobile-Optimized Details Grid */}
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-2 mb-4 sm:mb-3">
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-3 sm:p-2 rounded-lg sm:rounded-md border-l-4 sm:border-l-3 border-[#1a73c0] shadow-sm">
                    <div className="text-gray-500 text-xs font-medium mb-1">Student ID</div>
                    <div className="font-bold text-gray-800 text-sm sm:text-xs truncate">{graduate.student_id}</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-3 sm:p-2 rounded-lg sm:rounded-md border-l-4 sm:border-l-3 border-[#1a73c0] shadow-sm">
                    <div className="text-gray-500 text-xs font-medium mb-1">Gender</div>
                    <div className="font-bold text-gray-800 text-sm sm:text-xs">{graduate.gender}</div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-green-100 p-3 sm:p-2 rounded-lg sm:rounded-md border-l-4 sm:border-l-3 border-green-500 shadow-sm">
                    <div className="text-green-600 text-xs font-medium mb-1">GPA</div>
                    <div className="font-bold text-green-700 text-sm sm:text-xs">{typeof graduate.gpa === 'number' ? graduate.gpa.toFixed(2) : Number(graduate.gpa).toFixed(2)}</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-3 sm:p-2 rounded-lg sm:rounded-md border-l-4 sm:border-l-3 border-[#1a73c0] shadow-sm">
                    <div className="text-gray-500 text-xs font-medium mb-1">Entry Year</div>
                    <div className="font-bold text-gray-800 text-sm sm:text-xs">{graduate.year_of_entry}</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-3 sm:p-2 rounded-lg sm:rounded-md border-l-4 sm:border-l-3 border-[#1a73c0] shadow-sm col-span-2 sm:col-span-1">
                    <div className="text-gray-500 text-xs font-medium mb-1">Graduation Year</div>
                    <div className="font-bold text-gray-800 text-sm sm:text-xs">{graduate.year_of_graduation}</div>
                  </div>
                </div>

                {/* Program Details - Mobile-Friendly Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-2 mb-4 sm:mb-3">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 sm:p-3 rounded-xl sm:rounded-lg shadow-sm border border-blue-200 hover:border-[#1a73c0] transition-colors duration-200">
                    <div className="flex items-center mb-3 sm:mb-2">
                      <svg className="h-5 w-5 sm:h-4 sm:w-4 text-[#1a73c0] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      <div className="text-[#1a73c0] text-sm sm:text-xs font-bold">Program</div>
                    </div>
                    <div className="font-semibold text-base sm:text-sm text-gray-800 leading-relaxed line-clamp-3">{graduate.program.name}</div>
                  </div>
                  <div className="hidden sm:block bg-gradient-to-br from-purple-50 to-purple-100 p-4 sm:p-3 rounded-xl sm:rounded-lg shadow-sm border border-purple-200 hover:border-[#1a73c0] transition-colors duration-200">
                    <div className="flex items-center mb-3 sm:mb-2">
                      <svg className="h-5 w-5 sm:h-4 sm:w-4 text-purple-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                      <div className="text-purple-600 text-sm sm:text-xs font-bold">Field of Study</div>
                    </div>
                    <div className="font-semibold text-base sm:text-sm text-gray-800 leading-relaxed line-clamp-3">{graduate.field_of_study.name}</div>
                  </div>
                  <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 sm:p-3 rounded-xl sm:rounded-lg shadow-sm border border-indigo-200 hover:border-[#1a73c0] transition-colors duration-200">
                    <div className="flex items-center mb-3 sm:mb-2">
                      <svg className="h-5 w-5 sm:h-4 sm:w-4 text-indigo-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      <div className="text-indigo-600 text-sm sm:text-xs font-bold">College</div>
                    </div>
                    <div className="font-semibold text-base sm:text-sm text-gray-800 leading-relaxed line-clamp-3">{graduate.college.name}</div>
                  </div>
                  <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-4 sm:p-3 rounded-xl sm:rounded-lg shadow-sm border border-teal-200 hover:border-[#1a73c0] transition-colors duration-200">
                    <div className="flex items-center mb-3 sm:mb-2">
                      <svg className="h-5 w-5 sm:h-4 sm:w-4 text-teal-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      <div className="text-teal-600 text-sm sm:text-xs font-bold">Department</div>
                    </div>
                    <div className="font-semibold text-base sm:text-sm text-gray-800 leading-relaxed line-clamp-3">{graduate.department.name}</div>
                  </div>
                </div>

                {/* Bottom section */}
                <div className="mt-auto pt-3 sm:pt-3 border-t border-gray-200 mt-4 sm:mt-4">
                  <div className="bg-gradient-to-r from-[#1a73c0]/10 to-transparent p-4 sm:p-3 rounded-xl sm:rounded-lg border-l-4 border-[#1a73c0] mb-4 sm:mb-3 shadow-sm">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-2">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 sm:h-4 sm:w-4 text-[#1a73c0] mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                        <span className="text-sm sm:text-sm font-semibold text-gray-700">Admission Classification</span>
                      </div>
                      <span className="text-sm sm:text-sm font-bold text-[#1a73c0] bg-white px-3 py-1.5 sm:py-1 rounded-full shadow-sm border border-blue-200 line-clamp-1">
                        {graduate.admission_classification.name}
                      </span>
                    </div>
                  </div>

                  {/* Action Button - Certificate Only */}
                  <div className="mt-4 sm:mt-3">
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => handleCertificatePreview(graduate)}
                      className="text-sm sm:text-xs border-2 border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0] hover:text-white w-full h-12 sm:h-9 font-semibold transition-all duration-300 rounded-xl sm:rounded-lg shadow-md hover:shadow-lg"
                    >
                      <svg className="mr-2 h-5 w-5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      View Certificate
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      ))}
      </div>
    </div>

    {/* Pagination Controls - Mobile Enhanced */}
    {searchResults.length > resultsPerPage && (
      <div className="flex justify-center items-center py-4 sm:py-3 px-3 sm:px-6 border-t border-gray-200 flex-shrink-0 bg-white">
        <div className="flex flex-wrap justify-center gap-2 sm:gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(1)}
            disabled={currentPage === 1}
            className="px-2 sm:px-1.5 py-1 sm:py-0.5 h-8 sm:h-7 min-w-[32px] sm:min-w-[28px] rounded-lg sm:rounded-md"
          >
            <svg className="h-4 w-4 sm:h-3 sm:w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-2 sm:px-1.5 py-1 sm:py-0.5 h-8 sm:h-7 min-w-[32px] sm:min-w-[28px] rounded-lg sm:rounded-md"
          >
            <svg className="h-4 w-4 sm:h-3 sm:w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </Button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
            const shouldShowPage = page === 1 ||
                                  page === totalPages ||
                                  Math.abs(page - currentPage) <= 1;

            if (!shouldShowPage) {
              if (page === currentPage - 2 || page === currentPage + 2) {
                return (
                  <span key={`ellipsis-${page}`} className="flex items-center justify-center h-8 sm:h-7 px-2 text-sm sm:text-xs text-gray-500 font-medium">
                    ...
                  </span>
                );
              }
              return null;
            }

            return (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => handlePageChange(page)}
                className={`px-2 sm:px-2 py-1 sm:py-0.5 h-8 sm:h-7 min-w-[32px] sm:min-w-[28px] text-sm sm:text-xs font-semibold rounded-lg sm:rounded-md ${currentPage === page ? 'bg-[#1a73c0] hover:bg-[#145da1] text-white' : 'hover:bg-gray-50'}`}
              >
                {page}
              </Button>
            );
          })}

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-2 sm:px-1.5 py-1 sm:py-0.5 h-8 sm:h-7 min-w-[32px] sm:min-w-[28px] rounded-lg sm:rounded-md"
          >
            <svg className="h-4 w-4 sm:h-3 sm:w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(totalPages)}
            disabled={currentPage === totalPages}
            className="px-2 sm:px-1.5 py-1 sm:py-0.5 h-8 sm:h-7 min-w-[32px] sm:min-w-[28px] rounded-lg sm:rounded-md"
          >
            <svg className="h-4 w-4 sm:h-3 sm:w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            </svg>
          </Button>
        </div>
      </div>
    )}

    <DialogFooter className="px-3 sm:px-6 py-4 sm:py-4 border-t border-gray-200 flex-shrink-0 bg-white">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between w-full sm:gap-3">
        <div className="text-xs sm:text-xs text-gray-500 order-2 sm:order-1 text-center sm:text-left leading-relaxed">
          <span className="font-semibold">Note:</span> All graduates have been verified through our official records system.
        </div>
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-2 order-1 sm:order-2 w-full sm:w-auto">
          <Button
            variant="outline"
            onClick={() => setIsResultsModalOpen(false)}
            className="border-2 border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0] hover:text-white text-sm sm:text-sm w-full sm:w-auto h-12 sm:h-9 font-semibold rounded-xl sm:rounded-lg transition-all duration-300"
          >
            <svg className="mr-2 h-5 w-5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Close
          </Button>
          <Button
            className="bg-[#1a73c0] hover:bg-[#145da1] text-sm sm:text-sm w-full sm:w-auto h-12 sm:h-9 font-semibold rounded-xl sm:rounded-lg transition-all duration-300 shadow-md hover:shadow-lg"
            onClick={handleReset}
          >
            <svg className="mr-2 h-5 w-5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            New Search
          </Button>
        </div>
      </div>
    </DialogFooter>
  </DialogContent>
</Dialog>

      {/* No Results Modal */}
      <Dialog open={isNoResultsModalOpen} onOpenChange={setIsNoResultsModalOpen}>
        <DialogContent className="sm:max-w-md p-0 overflow-hidden rounded-xl max-h-[90vh]">
          <DialogTitle className="sr-only">No Results Found</DialogTitle>
          <DialogDescription className="sr-only">We couldn't find any graduates matching your search criteria.</DialogDescription>
          {/* Top colored header section */}
          <div className="bg-gradient-to-r from-[#1a73c0] to-[#2a8bd5] p-6 text-center relative overflow-hidden">
            {/* Background pattern */}
            <div className="absolute inset-0 opacity-10" style={{
              backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'20\' height=\'20\' viewBox=\'0 0 20 20\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'1\' fill-rule=\'evenodd\'%3E%3Ccircle cx=\'3\' cy=\'3\' r=\'1\'/%3E%3Ccircle cx=\'13\' cy=\'13\' r=\'1\'/%3E%3C/g%3E%3C/svg%3E")'
            }}></div>

            {/* Emoji and title */}
            <div className="flex flex-col items-center relative z-10">
              <div className="bg-white/20 backdrop-blur-sm p-4 rounded-full mb-4 shadow-lg flex items-center justify-center border-2 border-white/30 animate-pulse">
                <div className="h-16 w-16 animate-bounce" style={{ animationDuration: '2s' }}>
                  <svg viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-full w-full">
                    <circle cx="18" cy="18" r="18" fill="#FFCC4D"/>
                    <path d="M18 21.5c-3.59 0-6.5 2.91-6.5 6.5h13c0-3.59-2.91-6.5-6.5-6.5z" fill="#664500"/>
                    <path d="M18 23.5c-2.485 0-4.5 2.015-4.5 4.5h9c0-2.485-2.015-4.5-4.5-4.5z" fill="#FFF"/>
                    <path d="M25 16c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3zm-8 0c0 1.657-1.343 3-3 3s-3-1.343-3-3 1.343-3 3-3 3 1.343 3 3z" fill="#664500"/>
                    <path d="M9.5 13.5c0 1.933 1.567 3.5 3.5 3.5s3.5-1.567 3.5-3.5S14.433 10 12.5 10s-3 1.567-3 3.5z" fill="#664500"/>
                    <path d="M11 12.5c0 .828.672 1.5 1.5 1.5s1.5-.672 1.5-1.5-.672-1.5-1.5-1.5-1.5.672-1.5 1.5zm12.5-2.5c-1.933 0-3.5 1.567-3.5 3.5s1.567 3.5 3.5 3.5 3.5-1.567 3.5-3.5-1.567-3.5-3.5-3.5z" fill="#664500"/>
                    <path d="M22 12.5c0 .828.672 1.5 1.5 1.5s1.5-.672 1.5-1.5-.672-1.5-1.5-1.5-1.5.672-1.5 1.5z" fill="#664500"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.999 6.5c0-1.934-1.119-3.5-2.5-3.5s-2.5 1.566-2.5 3.5c0 .196.01.391.028.582.345-.113.704-.175 1.079-.175 1.857 0 3.393 1.536 3.393 3.393 0 .558-.136 1.085-.377 1.549.245.031.493.051.744.051 1.381 0 2.5-1.566 2.5-3.5 0-.196-.01-.391-.028-.582-.345.113-.704.175-1.079.175-1.857 0-3.393-1.536-3.393-3.393 0-.558.136-1.085.377-1.549-.245-.031-.493-.051-.744-.051z" fill="#5DADEC"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M24.999 6.5c0-1.934-1.119-3.5-2.5-3.5s-2.5 1.566-2.5 3.5c0 .196.01.391.028.582.345-.113.704-.175 1.079-.175 1.857 0 3.393 1.536 3.393 3.393 0 .558-.136 1.085-.377 1.549.245.031.493.051.744.051 1.381 0 2.5-1.566 2.5-3.5 0-.196-.01-.391-.028-.582-.345.113-.704.175-1.079.175-1.857 0-3.393-1.536-3.393-3.393 0-.558.136-1.085.377-1.549-.245-.031-.493-.051-.744-.051z" fill="#5DADEC"/>
                  </svg>
                </div>
              </div>
              <h2 className="text-2xl font-bold text-white mb-1">No Results Found</h2>
              <p className="text-white/90 max-w-xs">
                We couldn't find any graduates matching your search criteria.
              </p>
            </div>
          </div>

          {/* Content section */}
          <div className="p-6">
            {/* Contact Information */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-6 mb-6 shadow-md relative overflow-hidden">
              {/* Background pattern */}
              <div className="absolute inset-0 opacity-5" style={{
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%231a73c0\' fill-opacity=\'1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")'
              }}></div>

              {/* Content */}
              <div className="relative z-10">
                <h4 className="font-bold text-blue-800 mb-4 flex items-center text-lg">
                  <div className="bg-blue-600 p-2 rounded-full mr-3 shadow-sm">
                    <svg viewBox="0 0 24 24" className="h-5 w-5 text-white">
                      <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
                    </svg>
                  </div>
                  <span>For further investigation use the following contact</span>
                </h4>

                <div className="bg-white rounded-lg shadow-sm border border-blue-100 p-5 transition-all duration-200 hover:shadow-md hover:border-blue-300">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <div className="bg-blue-100 p-2.5 rounded-full mr-3">
                        <svg viewBox="0 0 24 24" className="h-5 w-5 text-blue-600">
                          <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-.4 4.25l-7.07 4.42c-.32.2-.74.2-1.06 0L4.4 8.25c-.25-.16-.4-.43-.4-.72 0-.67.73-1.07 1.3-.72L12 11l6.7-4.19c.57-.35 1.3.05 1.3.72 0 .29-.15.56-.4.72z"/>
                        </svg>
                      </div>
                      <div>
                        <div className="font-semibold text-gray-700 text-sm">Email:</div>
                        <a href="mailto:<EMAIL>" className="text-blue-600 font-medium hover:underline transition-colors"><EMAIL></a>
                      </div>
                    </div>

                    <div className="flex items-center border-t border-blue-100 pt-4">
                      <div className="bg-blue-100 p-2.5 rounded-full mr-3">
                        <svg viewBox="0 0 24 24" className="h-5 w-5 text-blue-600">
                          <path fill="currentColor" d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                        </svg>
                      </div>
                      <div>
                        <div className="font-semibold text-gray-700 text-sm">Phone:</div>
                        <a href="tel:+251581141237" className="text-blue-600 font-medium hover:underline transition-colors">+251-581-141237</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              <Button
                variant="outline"
                onClick={() => setIsNoResultsModalOpen(false)}
                className="border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0]/5 rounded-lg py-2.5 flex-1 transition-all duration-200 hover:scale-105"
              >
                <svg viewBox="0 0 24 24" className="h-5 w-5 mr-2">
                  <path fill="currentColor" d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                </svg>
                Back to Search
              </Button>
              <Button
                className="bg-[#1a73c0] hover:bg-[#145da1] rounded-lg py-2.5 flex-1 transition-all duration-200 hover:scale-105"
                onClick={handleReset}
              >
                <svg viewBox="0 0 24 24" className="h-5 w-5 mr-2">
                  <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
                Reset Search
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Certificate Preview Modal */}
      <Dialog open={isCertificateModalOpen} onOpenChange={setIsCertificateModalOpen}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          {selectedGraduate && (
            <>
              <DialogHeader className="pb-3 sm:pb-4 border-b border-gray-200">
                <div className="flex items-center">
                  <div className="bg-[#1a73c0] p-1.5 sm:p-2 rounded-full mr-2 sm:mr-3">
                    <svg className="h-4 w-4 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div>
                    <DialogTitle className="text-lg sm:text-2xl font-bold text-[#1a73c0]">
                      Certificate of Graduation
                    </DialogTitle>
                    <DialogDescription className="text-xs sm:text-md font-medium text-gray-700">
                      Official verification for {selectedGraduate.full_name}
                    </DialogDescription>
                  </div>
                </div>
              </DialogHeader>

              <div className="p-3 sm:p-6 bg-gradient-to-r from-blue-50 to-white border-4 border-double border-[#1a73c0]/30 rounded-lg my-3 sm:my-6 relative overflow-hidden">
                {/* Background pattern */}
                <div className="absolute inset-0 opacity-5" style={{
                  backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%234a9fea\' fill-opacity=\'1\' fill-rule=\'evenodd\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/svg%3E")'
                }}></div>

                {/* Certificate content */}
                <div className="relative z-10 text-center">
                  <div className="mb-4 sm:mb-6">
                    <img
                      src="/logo.png"
                      alt="University Logo"
                      className="h-12 sm:h-20 mx-auto mb-2"
                    />
                    <h1 className="text-xl sm:text-3xl font-serif font-bold text-[#1a73c0] mb-1">{settings.organizationName}</h1>
                    <p className="text-sm sm:text-base text-gray-600 font-medium">Office of the Registrar</p>
                  </div>

                  <div className="mb-4 sm:mb-8">
                    <h2 className="text-lg sm:text-2xl font-serif font-bold text-gray-800 mb-2">This is to certify that</h2>
                    <p className="text-xl sm:text-3xl font-serif font-bold text-[#1a73c0] mb-2 sm:mb-4">{selectedGraduate.full_name}</p>
                    <p className="text-sm sm:text-lg text-gray-700">
                      has successfully completed all the requirements for the degree of
                    </p>
                    <p className="text-lg sm:text-2xl font-serif font-bold text-gray-800 my-2">
                      {selectedGraduate.program.name} in {selectedGraduate.field_of_study.name}
                    </p>

                    {selectedGraduate.field_of_study.duration && (
                      <div className="inline-block my-3 sm:my-4 relative">
                        <div className="relative overflow-hidden bg-gradient-to-br from-amber-50 to-amber-100 px-4 sm:px-6 py-2 sm:py-3 rounded-lg border border-amber-300 shadow-md hover:shadow-lg transition-all duration-300">
                          <div className="flex items-center justify-between relative z-10">
                            <div className="flex items-center">
                              <div className="bg-gradient-to-br from-amber-500 to-amber-600 p-1.5 sm:p-2.5 rounded-full mr-3 sm:mr-4 shadow-md">
                                <svg className="h-4 w-4 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <div>
                                <div className="text-amber-700 text-sm sm:text-base font-medium">Program Duration</div>
                              </div>
                            </div>
                            <div className="flex items-baseline">
                              <span className="font-bold text-amber-800 text-2xl sm:text-3xl mr-2">{selectedGraduate.field_of_study.duration}</span>
                              <span className="text-amber-700 text-sm sm:text-lg font-medium">years</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    <p className="text-sm sm:text-lg text-gray-700 mb-2">
                      from the {selectedGraduate.department.name}, {selectedGraduate.college.name}
                    </p>
                    <div className="my-2 sm:my-4 py-1 sm:py-2 px-2 sm:px-4 bg-[#1a73c0]/10 inline-block rounded-lg border-2 border-[#1a73c0]/20">
                      <p className="text-sm sm:text-lg font-medium text-[#1a73c0]">
                        Admission Classification: <span className="font-bold">{selectedGraduate.admission_classification.name}</span>
                      </p>
                    </div>
                    <p className="text-sm sm:text-lg text-gray-700 mt-2">
                      with a Grade Point Average of <span className="font-bold">{typeof selectedGraduate.gpa === 'number' ? selectedGraduate.gpa.toFixed(2) : Number(selectedGraduate.gpa).toFixed(2)}</span>
                    </p>
                    <p className="text-sm sm:text-lg text-gray-700 mt-2">
                      entered in <span className="font-bold">{selectedGraduate.year_of_entry}</span> and graduated in <span className="font-bold">{selectedGraduate.year_of_graduation}</span>
                    </p>
                  </div>

                  <div className="flex justify-between items-center mt-6 sm:mt-12">
                    <div className="text-center">
                      <div className="h-px w-24 sm:w-48 bg-gray-400 mb-2"></div>
                      <p className="text-xs sm:text-base text-gray-700 font-medium">University Registrar</p>
                    </div>
                    <div className="rounded-full h-16 w-16 sm:h-24 sm:w-24 border-2 border-[#1a73c0] flex items-center justify-center bg-white/50">
                      <svg className="h-10 w-10 sm:h-16 sm:w-16 text-[#1a73c0]/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <div className="text-center">
                      <div className="h-px w-24 sm:w-48 bg-gray-400 mb-2"></div>
                      <p className="text-xs sm:text-base text-gray-700 font-medium">University President</p>
                    </div>
                  </div>

                  <div className="mt-4 sm:mt-8 pt-2 sm:pt-4 border-t border-gray-300 flex flex-col sm:flex-row justify-between text-xs sm:text-sm text-gray-600">
                    <div className="mb-1 sm:mb-0">Student ID: {selectedGraduate.student_id}</div>
                    <div>Verification Date: {new Date().toLocaleDateString()}</div>
                  </div>
                </div>
              </div>

              <DialogFooter className="flex flex-col xs:flex-row justify-end gap-2 sm:space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsCertificateModalOpen(false)}
                  className="border-[#1a73c0] text-[#1a73c0] hover:bg-[#1a73c0]/5 text-xs sm:text-sm w-full xs:w-auto"
                >
                  Close
                </Button>
                <Button
                  className="bg-green-600 hover:bg-green-700 text-xs sm:text-sm w-full xs:w-auto"
                  onClick={() => window.print()}
                >
                  <svg className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                  </svg>
                  Print Certificate
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>



    </Layout>
  );
};

export default GraduateVerification;
