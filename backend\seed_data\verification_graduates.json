[{"student_id": "GR/1234/12", "first_name": "አበበ", "middle_name": "ታደሰ", "last_name": "ገብረመድህን", "year_of_graduation": 2022, "gpa": 3.75, "gender": "Male", "college_code": "CMHS", "department_code": "MED", "field_of_study": "Medicine and Surgery", "program_code": "MD", "admission_classification": "Regular"}, {"student_id": "GR/1235/12", "first_name": "ሰላም", "middle_name": "ብርሃኑ", "last_name": "ደምሴ", "year_of_graduation": 2022, "gpa": 3.82, "gender": "Female", "college_code": "CMHS", "department_code": "NUR", "field_of_study": "Nursing", "program_code": "BSc-NUR", "admission_classification": "Regular"}, {"student_id": "GR/1236/12", "first_name": "ዳዊት", "middle_name": "አለማየሁ", "last_name": "ታደሰ", "year_of_graduation": 2022, "gpa": 3.65, "gender": "Male", "college_code": "CBE", "department_code": "ACFIN", "field_of_study": "Accounting", "program_code": "BA-ACC", "admission_classification": "Regular"}, {"student_id": "GR/1237/12", "first_name": "ሀና", "middle_name": "ተስፋዬ", "last_name": "ወልደሚካኤል", "year_of_graduation": 2022, "gpa": 3.91, "gender": "Female", "college_code": "CNCS", "department_code": "CS", "field_of_study": "Computer Science", "program_code": "BSc-CS", "admission_classification": "Regular"}, {"student_id": "GR/1238/12", "first_name": "ብሩክ", "middle_name": "ሙሉጌታ", "last_name": "አስፋው", "year_of_graduation": 2022, "gpa": 3.58, "gender": "Male", "college_code": "CSSH", "department_code": "ELL", "field_of_study": "English Language", "program_code": "BA-ELL", "admission_classification": "Regular"}, {"student_id": "GR/1239/12", "first_name": "ሰናይት", "middle_name": "ተክሌ", "last_name": "ሀይሌ", "year_of_graduation": 2022, "gpa": 3.77, "gender": "Female", "college_code": "SOL", "department_code": "LAW", "field_of_study": "Law", "program_code": "LLB", "admission_classification": "Regular"}, {"student_id": "GR/1240/12", "first_name": "ፍቅሩ", "middle_name": "ዘውዴ", "last_name": "ተሾመ", "year_of_graduation": 2022, "gpa": 3.45, "gender": "Male", "college_code": "SOT", "department_code": "CE", "field_of_study": "Civil Engineering", "program_code": "BSc-CE", "admission_classification": "Regular"}, {"student_id": "GR/1241/12", "first_name": "ትዕግስት", "middle_name": "ሰለሞን", "last_name": "ገብረመስቀል", "year_of_graduation": 2022, "gpa": 3.88, "gender": "Female", "college_code": "CAES", "department_code": "PS", "field_of_study": "Plant Science", "program_code": "BSc-PS", "admission_classification": "Regular"}, {"student_id": "GR/1242/12", "first_name": "ሄኖክ", "middle_name": "ዮሐንስ", "last_name": "ወርቁ", "year_of_graduation": 2022, "gpa": 3.62, "gender": "Male", "college_code": "CVMAS", "department_code": "VM", "field_of_study": "Veterinary Medicine", "program_code": "DVM", "admission_classification": "Regular"}, {"student_id": "GR/1243/12", "first_name": "ሄለን", "middle_name": "ዮሴፍ", "last_name": "ተፈራ", "year_of_graduation": 2022, "gpa": 3.79, "gender": "Female", "college_code": "SOE", "department_code": "CI", "field_of_study": "Curriculum Development", "program_code": "BEd-CI", "admission_classification": "Regular"}, {"student_id": "GR/1244/12", "first_name": "ሰሎሞን", "middle_name": "ደረጀ", "last_name": "ጌታቸው", "year_of_graduation": 2021, "gpa": 3.55, "gender": "Male", "college_code": "CMHS", "department_code": "PH", "field_of_study": "Public Health", "program_code": "BSc-PH", "admission_classification": "Regular"}, {"student_id": "GR/1245/12", "first_name": "ሰብለወንጌል", "middle_name": "አሰፋ", "last_name": "ሞላ", "year_of_graduation": 2021, "gpa": 3.92, "gender": "Female", "college_code": "CBE", "department_code": "MGT", "field_of_study": "Management", "program_code": "BA-MGT", "admission_classification": "Regular"}, {"student_id": "GR/1246/12", "first_name": "ዮሐንስ", "middle_name": "ተስፋዬ", "last_name": "ገብረመድህን", "year_of_graduation": 2021, "gpa": 3.68, "gender": "Male", "college_code": "CNCS", "department_code": "MATH", "field_of_study": "Pure Mathematics", "program_code": "BSc-MATH", "admission_classification": "Regular"}, {"student_id": "GR/1247/12", "first_name": "ሙሉነሽ", "middle_name": "ታምሩ", "last_name": "ደምሴ", "year_of_graduation": 2021, "gpa": 3.85, "gender": "Female", "college_code": "CSSH", "department_code": "SOC", "field_of_study": "Sociology", "program_code": "BA-SOC", "admission_classification": "Regular"}, {"student_id": "GR/1248/12", "first_name": "ታደሰ", "middle_name": "ብርሃኑ", "last_name": "ወልደማርያም", "year_of_graduation": 2021, "gpa": 3.47, "gender": "Male", "college_code": "SOT", "department_code": "EE", "field_of_study": "Electrical Engineering", "program_code": "BSc-EE", "admission_classification": "Regular"}, {"student_id": "GR/1249/12", "first_name": "ሰናይት", "middle_name": "ሀይሌ", "last_name": "ገብረሚካኤል", "year_of_graduation": 2021, "gpa": 3.95, "gender": "Female", "college_code": "CMHS", "department_code": "PHARM", "field_of_study": "Pharmacy", "program_code": "B.Pharm", "admission_classification": "Regular"}, {"student_id": "GR/1250/12", "first_name": "ዘውዱ", "middle_name": "ታምራት", "last_name": "ደስታ", "year_of_graduation": 2021, "gpa": 3.72, "gender": "Male", "college_code": "CAES", "department_code": "AS", "field_of_study": "Animal Science", "program_code": "BSc-AS", "admission_classification": "Regular"}, {"student_id": "GR/1251/12", "first_name": "ፍሬህይወት", "middle_name": "ተስፋዬ", "last_name": "ወልደገብርኤል", "year_of_graduation": 2021, "gpa": 3.83, "gender": "Female", "college_code": "SOE", "department_code": "EPM", "field_of_study": "Educational Planning", "program_code": "BEd-EPM", "admission_classification": "Regular"}, {"student_id": "GR/1252/12", "first_name": "ሙሉጌታ", "middle_name": "ታደሰ", "last_name": "ወርቁ", "year_of_graduation": 2021, "gpa": 3.59, "gender": "Male", "college_code": "CSSH", "department_code": "PSY", "field_of_study": "Psychology", "program_code": "BA-PSY", "admission_classification": "Regular"}, {"student_id": "GR/1253/12", "first_name": "ሰላማዊት", "middle_name": "ዮሴፍ", "last_name": "ታደሰ", "year_of_graduation": 2021, "gpa": 3.87, "gender": "Female", "college_code": "CBE", "department_code": "ECON", "field_of_study": "Economics", "program_code": "BA-ECON", "admission_classification": "Regular"}, {"student_id": "GR/1254/12", "first_name": "ብሩክ", "middle_name": "ደረጀ", "last_name": "ታምሩ", "year_of_graduation": 2020, "gpa": 3.65, "gender": "Male", "college_code": "CMHS", "department_code": "MID", "field_of_study": "Midwifery", "program_code": "BSc-MID", "admission_classification": "Regular"}, {"student_id": "GR/1255/12", "first_name": "ሀና", "middle_name": "ሰለሞን", "last_name": "ገብረመድህን", "year_of_graduation": 2020, "gpa": 3.93, "gender": "Female", "college_code": "CNCS", "department_code": "PHYS", "field_of_study": "Physics", "program_code": "BSc-PHYS", "admission_classification": "Regular"}, {"student_id": "GR/1256/12", "first_name": "ዳዊት", "middle_name": "ብርሃኑ", "last_name": "ወልደማርያም", "year_of_graduation": 2020, "gpa": 3.71, "gender": "Male", "college_code": "SOT", "department_code": "ME", "field_of_study": "Mechanical Engineering", "program_code": "BSc-ME", "admission_classification": "Regular"}, {"student_id": "GR/1257/12", "first_name": "ትዕግስት", "middle_name": "ታምራት", "last_name": "ሀይሌ", "year_of_graduation": 2020, "gpa": 3.89, "gender": "Female", "college_code": "CSSH", "department_code": "ALL", "field_of_study": "Amharic Language", "program_code": "BA-ALL", "admission_classification": "Regular"}, {"student_id": "GR/1258/12", "first_name": "ሄኖክ", "middle_name": "ሙሉጌታ", "last_name": "ተፈራ", "year_of_graduation": 2020, "gpa": 3.52, "gender": "Male", "college_code": "CAES", "department_code": "NRM", "field_of_study": "Natural Resource Management", "program_code": "BSc-NRM", "admission_classification": "Regular"}, {"student_id": "GR/1259/12", "first_name": "ሄለን", "middle_name": "ዘውዴ", "last_name": "ገብረመስቀል", "year_of_graduation": 2020, "gpa": 3.81, "gender": "Female", "college_code": "SOE", "department_code": "SNE", "field_of_study": "Special Needs Education", "program_code": "BEd-SNE", "admission_classification": "Regular"}, {"student_id": "GR/1260/12", "first_name": "ሰሎሞን", "middle_name": "አለማየሁ", "last_name": "ተሾመ", "year_of_graduation": 2020, "gpa": 3.67, "gender": "Male", "college_code": "SOL", "department_code": "LAW", "field_of_study": "Law", "program_code": "LLB", "admission_classification": "Regular"}, {"student_id": "GR/1261/12", "first_name": "ሰብለወንጌል", "middle_name": "ዮሐንስ", "last_name": "ደምሴ", "year_of_graduation": 2020, "gpa": 3.94, "gender": "Female", "college_code": "CBE", "department_code": "THM", "field_of_study": "Tourism Management", "program_code": "BA-TM", "admission_classification": "Regular"}, {"student_id": "GR/1262/12", "first_name": "ዮሐንስ", "middle_name": "ደረጀ", "last_name": "አስፋው", "year_of_graduation": 2020, "gpa": 3.58, "gender": "Male", "college_code": "CNCS", "department_code": "CHEM", "field_of_study": "Chemistry", "program_code": "BSc-CHEM", "admission_classification": "Regular"}, {"student_id": "GR/1263/12", "first_name": "ሙሉነሽ", "middle_name": "አሰፋ", "last_name": "ወልደሚካኤል", "year_of_graduation": 2020, "gpa": 3.86, "gender": "Female", "college_code": "CMHS", "department_code": "PH", "field_of_study": "Epidemiology and Biostatistics", "program_code": "MPH", "admission_classification": "Regular"}, {"student_id": "GR/1264/12", "first_name": "ታደሰ", "middle_name": "ተስፋዬ", "last_name": "ጌታቸው", "year_of_graduation": 2019, "gpa": 3.63, "gender": "Male", "college_code": "CNCS", "department_code": "BIO", "field_of_study": "Biology", "program_code": "BSc-BIO", "admission_classification": "Regular"}, {"student_id": "GR/1265/12", "first_name": "ሰናይት", "middle_name": "ታምሩ", "last_name": "ሞላ", "year_of_graduation": 2019, "gpa": 3.91, "gender": "Female", "college_code": "SOT", "department_code": "IT", "field_of_study": "Information Technology", "program_code": "BSc-IT", "admission_classification": "Regular"}, {"student_id": "GR/1266/12", "first_name": "ዘውዱ", "middle_name": "ብርሃኑ", "last_name": "ገብረመድህን", "year_of_graduation": 2019, "gpa": 3.57, "gender": "Male", "college_code": "CSSH", "department_code": "GES", "field_of_study": "Geography", "program_code": "BA-GEO", "admission_classification": "Regular"}, {"student_id": "GR/1267/12", "first_name": "ፍሬህይወት", "middle_name": "ሀይሌ", "last_name": "ወልደማርያም", "year_of_graduation": 2019, "gpa": 3.84, "gender": "Female", "college_code": "CAES", "department_code": "AGECON", "field_of_study": "Agricultural Economics", "program_code": "BSc-AGECON", "admission_classification": "Regular"}, {"student_id": "GR/1268/12", "first_name": "ሙሉጌታ", "middle_name": "ታምራት", "last_name": "ደስታ", "year_of_graduation": 2019, "gpa": 3.69, "gender": "Male", "college_code": "CVMAS", "department_code": "APT", "field_of_study": "Animal Production and Technology", "program_code": "BSc-AS", "admission_classification": "Regular"}, {"student_id": "GR/1269/12", "first_name": "ሰላማዊት", "middle_name": "ተስፋዬ", "last_name": "ገብረሚካኤል", "year_of_graduation": 2019, "gpa": 3.95, "gender": "Female", "college_code": "CBE", "department_code": "ACFIN", "field_of_study": "Finance", "program_code": "BA-FIN", "admission_classification": "Regular"}, {"student_id": "GR/1270/12", "first_name": "ብሩክ", "middle_name": "ዮሴፍ", "last_name": "ወርቁ", "year_of_graduation": 2019, "gpa": 3.61, "gender": "Male", "college_code": "CSSH", "department_code": "PSIR", "field_of_study": "Political Science", "program_code": "BA-PS", "admission_classification": "Regular"}, {"student_id": "GR/1271/12", "first_name": "ሀና", "middle_name": "ደረጀ", "last_name": "ታደሰ", "year_of_graduation": 2019, "gpa": 3.88, "gender": "Female", "college_code": "CNCS", "department_code": "STAT", "field_of_study": "Statistics", "program_code": "BSc-STAT", "admission_classification": "Regular"}, {"student_id": "GR/1272/12", "first_name": "ዳዊት", "middle_name": "ሰለሞን", "last_name": "ታምሩ", "year_of_graduation": 2019, "gpa": 3.54, "gender": "Male", "college_code": "CMHS", "department_code": "NUR", "field_of_study": "Surgical Nursing", "program_code": "MSc-NUR", "admission_classification": "Regular"}, {"student_id": "GR/1273/12", "first_name": "ትዕግስት", "middle_name": "ሙሉጌታ", "last_name": "ገብረመድህን", "year_of_graduation": 2019, "gpa": 3.82, "gender": "Female", "college_code": "SOE", "department_code": "CI", "field_of_study": "Instructional Design", "program_code": "BEd-CI", "admission_classification": "Regular"}, {"student_id": "GR/1274/12", "first_name": "ሄኖክ", "middle_name": "አለማየሁ", "last_name": "ወልደማርያም", "year_of_graduation": 2018, "gpa": 3.66, "gender": "Male", "college_code": "CBE", "department_code": "MGT", "field_of_study": "Human Resource Management", "program_code": "MBA", "admission_classification": "Regular"}, {"student_id": "GR/1275/12", "first_name": "ሄለን", "middle_name": "ብርሃኑ", "last_name": "ሀይሌ", "year_of_graduation": 2018, "gpa": 3.92, "gender": "Female", "college_code": "CNCS", "department_code": "CS", "field_of_study": "Software Engineering", "program_code": "MSc-CS", "admission_classification": "Regular"}, {"student_id": "GR/1276/12", "first_name": "ሰሎሞን", "middle_name": "ታምራት", "last_name": "ተፈራ", "year_of_graduation": 2018, "gpa": 3.59, "gender": "Male", "college_code": "CSSH", "department_code": "HHM", "field_of_study": "History", "program_code": "BA-HIST", "admission_classification": "Regular"}, {"student_id": "GR/1277/12", "first_name": "ሰብለወንጌል", "middle_name": "ዘውዴ", "last_name": "ገብረመስቀል", "year_of_graduation": 2018, "gpa": 3.87, "gender": "Female", "college_code": "SOL", "department_code": "LAW", "field_of_study": "Law", "program_code": "LLM", "admission_classification": "Regular"}, {"student_id": "GR/1278/12", "first_name": "ዮሐንስ", "middle_name": "ዮሐንስ", "last_name": "ተሾመ", "year_of_graduation": 2018, "gpa": 3.73, "gender": "Male", "college_code": "SOT", "department_code": "CE", "field_of_study": "Structural Engineering", "program_code": "BSc-CE", "admission_classification": "Regular"}, {"student_id": "GR/1279/12", "first_name": "ሙሉነሽ", "middle_name": "ተስፋዬ", "last_name": "ደምሴ", "year_of_graduation": 2018, "gpa": 3.96, "gender": "Female", "college_code": "CMHS", "department_code": "PHARM", "field_of_study": "Clinical Pharmacy", "program_code": "<PERSON><PERSON>", "admission_classification": "Regular"}, {"student_id": "GR/1280/12", "first_name": "ታደሰ", "middle_name": "ዮሴፍ", "last_name": "አስፋው", "year_of_graduation": 2018, "gpa": 3.64, "gender": "Male", "college_code": "CAES", "department_code": "PS", "field_of_study": "Horticulture", "program_code": "BSc-PS", "admission_classification": "Regular"}, {"student_id": "GR/1281/12", "first_name": "ሰናይት", "middle_name": "ደረጀ", "last_name": "ወልደሚካኤል", "year_of_graduation": 2018, "gpa": 3.89, "gender": "Female", "college_code": "SOE", "department_code": "EPM", "field_of_study": "Educational Management", "program_code": "MEd-EPM", "admission_classification": "Regular"}, {"student_id": "GR/1282/12", "first_name": "ዘውዱ", "middle_name": "አሰፋ", "last_name": "ጌታቸው", "year_of_graduation": 2018, "gpa": 3.56, "gender": "Male", "college_code": "CNCS", "department_code": "MATH", "field_of_study": "Applied Mathematics", "program_code": "MSc-MATH", "admission_classification": "Regular"}, {"student_id": "GR/1283/12", "first_name": "ፍሬህይወት", "middle_name": "ታምሩ", "last_name": "ሞላ", "year_of_graduation": 2018, "gpa": 3.85, "gender": "Female", "college_code": "CSSH", "department_code": "PSY", "field_of_study": "Clinical Psychology", "program_code": "MA-PSY", "admission_classification": "Regular"}]