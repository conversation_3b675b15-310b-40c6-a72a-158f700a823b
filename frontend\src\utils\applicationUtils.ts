/**
 * Utility functions for application-related operations
 */

/**
 * Checks if a user has completed all required application steps
 * @param personalInfo - User's personal information
 * @param gatInfo - User's GAT information
 * @param programSelection - User's program selection
 * @param documentation - User's documentation
 * @returns boolean indicating if all required information is available
 */
export const hasCompletedAllRequiredSteps = (
  personalInfo: any | null,
  gatInfo: any | null,
  programSelection: any | null,
  documentation: any | null
): boolean => {
  // Check if personal information exists
  const hasPersonalInfo = !!personalInfo;
  
  // Check if GAT information exists
  const hasGatInfo = !!gatInfo;
  
  // Check if program selection exists
  const hasProgramSelection = !!programSelection;
  
  // Check if documentation exists
  const hasDocumentation = !!documentation;
  
  // All steps must be completed
  return hasPersonalInfo && hasGatInfo && hasProgramSelection && hasDocumentation;
};

/**
 * Gets the next incomplete step in the application process
 * @param personalInfo - User's personal information
 * @param gatInfo - User's GAT information
 * @param programSelection - User's program selection
 * @param documentation - User's documentation
 * @returns The route to the next incomplete step
 */
export const getNextIncompleteStep = (
  personalInfo: any | null,
  gatInfo: any | null,
  programSelection: any | null,
  documentation: any | null
): string => {
  if (!personalInfo) {
    return '/application/personal-info';
  }
  
  if (!gatInfo) {
    return '/application/gat';
  }
  
  if (!programSelection) {
    return '/application/program-selection';
  }
  
  if (!documentation) {
    return '/application/documentation';
  }
  
  // If all steps are complete, return the payment page
  return '/application/payment';
};
