#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to assign Administrator role to staff users
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User, Group

def assign_admin_roles():
    """Assign Administrator role to all staff users"""
    try:
        # Get the Administrator group
        admin_group = Group.objects.get(name='Administrator')
        print(f"Found Administrator group: {admin_group.name}")
        
        # Get all staff users
        staff_users = User.objects.filter(is_staff=True)
        print(f"Found {staff_users.count()} staff users")
        
        for user in staff_users:
            # Add user to Administrator group if not already added
            if not user.groups.filter(name='Administrator').exists():
                user.groups.add(admin_group)
                print(f"✅ Added {user.username} to Administrator role")
            else:
                print(f"ℹ️  {user.username} already has Administrator role")
        
        # Also assign Super Admin to superusers
        try:
            super_admin_group = Group.objects.get(name='Super Admin')
            superusers = User.objects.filter(is_superuser=True)
            
            for user in superusers:
                if not user.groups.filter(name='Super Admin').exists():
                    user.groups.add(super_admin_group)
                    print(f"✅ Added {user.username} to Super Admin role")
                else:
                    print(f"ℹ️  {user.username} already has Super Admin role")
                    
        except Group.DoesNotExist:
            print("❌ Super Admin group not found")
        
        print("\n🎯 Role assignment completed!")
        
        # Show final user roles
        print("\nFinal user role assignments:")
        for user in User.objects.all():
            roles = [group.name for group in user.groups.all()]
            print(f"- {user.username}: {', '.join(roles) if roles else 'No roles'}")
            
    except Group.DoesNotExist:
        print("❌ Administrator group not found. Please run 'python manage.py setup_rbac --reset' first.")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == '__main__':
    assign_admin_roles()
