#!/usr/bin/env python
"""
Test Gmail SMTP connection directly
"""
import os
import django
import sys
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from settings_manager.smtp_models import SMTPSettings

def test_gmail_connection():
    """Test Gmail SMTP connection"""
    print("=== Testing Gmail SMTP Connection ===")
    
    try:
        # Load SMTP settings
        settings = SMTPSettings.load()
        print(f"Host: {settings.host}")
        print(f"Port: {settings.port}")
        print(f"Username: {settings.username}")
        print(f"From Email: {settings.from_email}")
        print(f"Use TLS: {settings.use_tls}")
        print(f"Use SSL: {settings.use_ssl}")
        
        # Test SMTP connection
        print("\n🔄 Connecting to Gmail SMTP...")
        
        if settings.use_ssl:
            # SSL connection (port 465)
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(
                settings.host,
                settings.port,
                context=context,
                timeout=settings.timeout
            )
            print("✅ SSL connection established")
        else:
            # Regular connection (port 587 with TLS)
            server = smtplib.SMTP(
                settings.host,
                settings.port,
                timeout=settings.timeout
            )
            print("✅ SMTP connection established")
            
            if settings.use_tls:
                print("🔄 Starting TLS...")
                server.starttls()
                print("✅ TLS started successfully")
        
        # Test authentication
        print("🔄 Testing authentication...")
        if settings.username and settings.password:
            server.login(settings.username, settings.password)
            print("✅ Authentication successful!")
        else:
            print("❌ No username/password provided")
            return False
        
        # Create test email
        print("🔄 Creating test email...")
        recipient = "<EMAIL>"  # Change this to your test email
        
        msg = MIMEMultipart()
        msg['From'] = settings.from_email
        msg['To'] = recipient
        msg['Subject'] = "SMTP Test - Gmail Configuration"
        
        body = f"""
Hello,

This is a test email to verify Gmail SMTP configuration.

Configuration Details:
- Host: {settings.host}
- Port: {settings.port}
- Username: {settings.username}
- From Email: {settings.from_email}
- TLS: {settings.use_tls}
- SSL: {settings.use_ssl}

If you received this email, your Gmail SMTP is working correctly!

Best regards,
SMTP Test Script
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email (commented out to avoid sending actual emails)
        print("📧 Email prepared successfully")
        print("⚠️  Email sending is disabled in test mode")
        # text = msg.as_string()
        # server.sendmail(settings.from_email, [recipient], text)
        # print("✅ Email sent successfully!")
        
        server.quit()
        print("✅ Connection closed successfully")
        
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Authentication failed: {e}")
        print("💡 Solutions:")
        print("   1. Check if you're using an App Password (not regular password)")
        print("   2. Enable 2-Factor Authentication on Gmail")
        print("   3. Generate App Password in Google Account settings")
        print("   4. Use the App Password in SMTP configuration")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ Connection failed: {e}")
        print("💡 Solutions:")
        print("   1. Check internet connection")
        print("   2. Verify host and port settings")
        print("   3. Check firewall settings")
        return False
        
    except smtplib.SMTPException as e:
        print(f"❌ SMTP error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_gmail_connection()
    
    if not success:
        print("\n" + "="*60)
        print("GMAIL SMTP TROUBLESHOOTING GUIDE")
        print("="*60)
        print("1. 🔐 App Password Required:")
        print("   - Go to https://myaccount.google.com/security")
        print("   - Enable 2-Factor Authentication")
        print("   - Generate App Password for 'Mail'")
        print("   - Use App Password (not regular password)")
        print()
        print("2. 🔧 Update SMTP Settings:")
        print("   - Username: <EMAIL>")
        print("   - Password: [16-character App Password]")
        print("   - Host: smtp.gmail.com")
        print("   - Port: 587")
        print("   - TLS: Enabled")
        print("   - SSL: Disabled")
        print()
        print("3. 🧪 Test Configuration:")
        print("   - Save settings in admin panel")
        print("   - Use 'Send Test' button")
        print("   - Check for success message")
