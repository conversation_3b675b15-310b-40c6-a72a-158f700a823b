import React, { createContext, useContext, useState } from 'react';
import useIdleTimeout from '@/hooks/useIdleTimeout';
import IdleTimeoutWarning from '@/components/IdleTimeoutWarning';
import { authAPI } from '@/services/api';

interface IdleTimeoutContextType {
  resetTimeout: () => void;
  isIdle: boolean;
  isWarned: boolean;
  timeRemaining: number | null;
}

const IdleTimeoutContext = createContext<IdleTimeoutContextType | undefined>(undefined);

interface IdleTimeoutProviderProps {
  children: React.ReactNode;
  idleTime?: number; // Time in minutes before logout
  warningTime?: number; // Time in minutes before warning
}

export const IdleTimeoutProvider: React.FC<IdleTimeoutProviderProps> = ({
  children,
  idleTime: defaultIdleTime = 5, // Default: 5 minutes
  warningTime: defaultWarningTime = 1, // Default: 1 minute warning
}) => {
  // Get user settings from localStorage
  const idleTimeoutEnabled = localStorage.getItem('idleTimeoutEnabled') !== 'false'; // Default to true if not set
  const storedIdleTime = localStorage.getItem('idleTimeoutDuration');
  const idleTime = storedIdleTime ? parseInt(storedIdleTime, 10) : defaultIdleTime;

  const warningEnabled = localStorage.getItem('idleTimeoutWarningEnabled') !== 'false'; // Default to true if not set
  const storedWarningTime = localStorage.getItem('idleTimeoutWarningDuration');
  const warningTime = warningEnabled ? (storedWarningTime ? parseFloat(storedWarningTime) : defaultWarningTime) : 0;
  const [manualLogout, setManualLogout] = useState(false);

  // Handle manual logout with server-side session termination
  const handleLogout = async () => {
    setManualLogout(true);

    try {
      // Attempt to logout on server side
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        await authAPI.logout(refreshToken);
      }
    } catch (error) {
      console.error('Error during server-side logout:', error);
      // Continue with client-side logout even if server logout fails
    }

    // Clear all authentication data
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    localStorage.removeItem('lastActivity');

    // Clear any cached data
    localStorage.removeItem('public_certificate_stats');

    window.location.href = '/login?reason=session_expired';
  };

  // Use the idle timeout hook
  const { isIdle, isWarned, timeRemaining, resetTimeout } = useIdleTimeout({
    idleTime,
    warningTime,
    onIdle: async () => {
      // This will be called when the user is idle for the specified time
      if (!manualLogout) {
        try {
          // Attempt to logout on server side
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            await authAPI.logout(refreshToken);
          }
        } catch (error) {
          console.error('Error during server-side logout:', error);
          // Continue with client-side logout even if server logout fails
        }

        // Clear all authentication data
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        localStorage.removeItem('lastActivity');

        // Clear any cached data
        localStorage.removeItem('public_certificate_stats');

        window.location.href = '/login?reason=session_expired';
      }
    },
  });

  // Force disable the warning dialog
  const showWarning = false;

  return (
    <IdleTimeoutContext.Provider value={{ resetTimeout, isIdle, isWarned, timeRemaining }}>
      {children}

      {/* Render the warning dialog */}
      {showWarning && (
        <IdleTimeoutWarning
          isWarned={isWarned}
          timeRemaining={timeRemaining}
          warningTime={warningTime}
          onStayLoggedIn={resetTimeout}
          onLogout={handleLogout}
        />
      )}
    </IdleTimeoutContext.Provider>
  );
};

// Custom hook to use the idle timeout context
export const useIdleTimeoutContext = () => {
  const context = useContext(IdleTimeoutContext);
  if (context === undefined) {
    throw new Error('useIdleTimeoutContext must be used within an IdleTimeoutProvider');
  }
  return context;
};

export default IdleTimeoutProvider;
