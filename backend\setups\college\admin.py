from django.contrib import admin
from setups.college.models import College
# Register your models here.
@admin.register(College)
class CollegeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at', 'updated_at']
    search_fields = ['name', 'description', 'id']
    list_filter = ['created_at', 'updated_at']
    date_hierarchy = 'created_at'
    ordering = ['name']
    list_per_page = 20
    list_max_show_all = 100
    list_display_links = ['name']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = [
        ('College Information', {
            'fields': ['name', 'description']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
