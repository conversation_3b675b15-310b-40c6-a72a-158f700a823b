import axios from 'axios';
import { API_BASE_URL } from '../config';

export interface Program {
  id: number;
  program_name: string;
  program_code: string;
  registration_fee: string;
  department?: number;
  department_name?: string;
  name?: string; // Keep for backward compatibility
  code?: string;
  description?: string;
  status?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL.endsWith('/') ? API_BASE_URL : `${API_BASE_URL}/`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token if available
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRFToken'] = csrfToken;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

export const programAPI = {
  getPrograms: async (params?: { department?: number }) => {
    try {
      console.log('Fetching programs with params:', params);

      // Try different API endpoints to get programs
      const endpoints = ['programs/', 'verification/programs/', 'staff/programs/', 'application/programs/'];

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying to fetch programs from ${endpoint}`);
          const response = await api.get(endpoint, { params });
          console.log(`Programs API response from ${endpoint}:`, response.data);

          if (Array.isArray(response.data) && response.data.length > 0) {
            return response.data;
          }
        } catch (apiError) {
          console.error(`Error fetching from ${endpoint}:`, apiError);
        }
      }

      // If all API endpoints fail, return an empty array
      console.warn('All program API endpoints failed, returning empty array');
      return [];
    } catch (error) {
      console.error('Unexpected error in getPrograms:', error);
      return [];
    }
  },

  getProgramById: async (id: number) => {
    try {
      // Try all endpoints in sequence
      const endpoints = [
        `programs/${id}/`,
        `verification/programs/${id}/`,
        `staff/programs/${id}/`,
        `application/programs/${id}/`
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying to fetch program ${id} from ${endpoint}`);
          const response = await api.get(endpoint);
          console.log(`Program ${id} details from ${endpoint}:`, response.data);
          return response.data;
        } catch (endpointError) {
          console.error(`Error fetching program ${id} from ${endpoint}:`, endpointError);
        }
      }

      // If all endpoints fail, return a placeholder
      console.log(`All endpoints failed for program ${id}, returning placeholder`);
      return { id, program_name: `Program ${id}`, program_code: `P${id}`, registration_fee: "0.00" };
    } catch (error) {
      console.error(`Unexpected error fetching program ${id}:`, error);
      return { id, program_name: `Program ${id}`, program_code: `P${id}`, registration_fee: "0.00" };
    }
  },

  // Function to get program names for a list of IDs
  getProgramNames: async (ids: number[]) => {
    if (!ids || ids.length === 0) return {};

    console.log('Fetching program names for IDs:', ids);

    // Create a map to store program names by ID
    const programMap: Record<number, string> = {};

    // Fetch each program in parallel
    await Promise.all(
      ids.map(async (id) => {
        try {
          const program = await programAPI.getProgramById(id);
          if (program && (program.program_name || program.name)) {
            programMap[id] = program.program_name || program.name;
          } else {
            programMap[id] = `Program ${id}`;
          }
        } catch (error) {
          console.error(`Error fetching program ${id}:`, error);
          programMap[id] = `Program ${id}`;
        }
      })
    );

    console.log('Program names map:', programMap);
    return programMap;
  },

  createProgram: async (data: Partial<Program>) => {
    const response = await api.post('verification/programs/', data);
    return response.data;
  },

  updateProgram: async (id: number, data: Partial<Program>) => {
    const response = await api.put(`verification/programs/${id}/`, data);
    return response.data;
  },

  deleteProgram: async (id: number) => {
    const response = await api.delete(`verification/programs/${id}/`);
    return response.data;
  }
};

// React Query hook for compatibility with existing code
export const useGetProgramsQuery = (params?: { department?: number }) => {
  return { data: [] }; // Placeholder until we implement React Query
};
