from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q

from .models import ApplicantGAT, ApplicantProgramSelection, ApplicantPayment
from django.contrib.auth.models import User

class ApplicationStatusView(APIView):
    """
    API endpoint to search for application status by GAT Number or Application Number.
    This endpoint requires authentication - only logged-in users can access it.
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get search parameters from query string
        gat_number = request.query_params.get('gat_number', None)
        application_number = request.query_params.get('application_number', None)

        # Validate that at least one search parameter is provided
        if not gat_number and not application_number:
            return Response(
                {"error": "Please provide either a GAT Number or Application Number"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Initialize variables to store the results
            gat_record = None
            program_selection = None
            payment_record = None

            # Search by GAT Number
            if gat_number:
                try:
                    # Remove any '#' symbol if present
                    clean_gat_number = gat_number.replace('#', '')

                    # Search for the GAT record
                    gat_record = ApplicantGAT.objects.filter(GAT_No__iexact=clean_gat_number).first()

                    if gat_record:
                        # If GAT record found, get the associated program selection
                        program_selection = ApplicantProgramSelection.objects.filter(gat=gat_record).first()

                        # If program selection found, get the associated payment
                        if program_selection:
                            payment_record = ApplicantPayment.objects.filter(
                                Q(applicant_gat=gat_record) | Q(applicant_program_selection=program_selection)
                            ).first()
                except Exception as e:
                    print(f"Error searching by GAT Number: {e}")

            # Search by Application Number
            elif application_number:
                try:
                    # Search for the program selection
                    program_selection = ApplicantProgramSelection.objects.filter(
                        application_num__iexact=application_number
                    ).first()

                    if program_selection:
                        # If program selection found, get the associated GAT record
                        gat_record = program_selection.gat

                        # Get the associated payment
                        payment_record = ApplicantPayment.objects.filter(
                            Q(applicant_gat=gat_record) | Q(applicant_program_selection=program_selection)
                        ).first()
                except Exception as e:
                    print(f"Error searching by Application Number: {e}")

            # If no records found, return an error
            if not gat_record and not program_selection:
                return Response(
                    {"error": "No application found with the provided information"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Prepare the response data
            response_data = {
                "found": True,
                "gat_info": None,
                "application_info": None,
                "payment_info": None,
                "status": {
                    "registrar": "Pending",  # Default status
                    "department": "Pending",  # Default status
                    "payment": "Pending"      # Default status
                }
            }

            # Add GAT information if available
            if gat_record:
                response_data["gat_info"] = {
                    "gat_number": gat_record.GAT_No,
                    "gat_result": gat_record.GAT_Result,
                    "created_at": gat_record.created_at,
                    "updated_at": gat_record.updated_at
                }

                # Get user information
                user = User.objects.filter(id=gat_record.user_id).first()
                if user:
                    response_data["user_info"] = {
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email
                    }

            # Add program selection information if available
            if program_selection:
                # Get application information details
                application_info = program_selection.application_info
                application_info_details = {}

                if hasattr(application_info, 'program') and application_info.program:
                    application_info_details["program"] = application_info.program.program_name

                if hasattr(application_info, 'department') and application_info.department:
                    application_info_details["department"] = application_info.department.name

                if hasattr(application_info, 'college') and application_info.college:
                    application_info_details["college"] = application_info.college.name

                if hasattr(application_info, 'field_of_study') and application_info.field_of_study:
                    application_info_details["field_of_study"] = application_info.field_of_study.field_of_study

                if hasattr(application_info, 'study_program') and application_info.study_program:
                    application_info_details["study_program"] = application_info.study_program.program_name

                if hasattr(application_info, 'admission_type') and application_info.admission_type:
                    application_info_details["admission_type"] = application_info.admission_type.name

                response_data["application_info"] = {
                    "application_number": program_selection.application_num,
                    "transaction_id": program_selection.transaction_id,
                    "sponsorship": program_selection.sponsorship,
                    "created_at": program_selection.created_at,
                    "updated_at": program_selection.updated_at,
                    "details": application_info_details
                }

                # Update registrar status with actual data from the database
                response_data["status"]["registrar"] = program_selection.registrar_off_status

            # Add payment information if available
            if payment_record:
                response_data["payment_info"] = {
                    "payment_status": payment_record.payment_status,
                    "payment_method": payment_record.payment_method,
                    "amount_paid": str(payment_record.amount_paid),
                    "created_at": payment_record.created_at,
                    "updated_at": payment_record.updated_at
                }

                # Update payment status from payment record, normalize "Completed" to "Approved"
                payment_status = payment_record.payment_status
                if payment_status.lower() == "completed":
                    payment_status = "Approved"
                response_data["status"]["payment"] = payment_status
            elif program_selection:
                # If no payment record but program selection exists, use payment status from program selection
                payment_status = program_selection.payment_status
                if payment_status.lower() == "completed":
                    payment_status = "Approved"
                response_data["status"]["payment"] = payment_status

            # If program selection exists, update department status with actual data from the database
            if program_selection:
                response_data["status"]["department"] = program_selection.department_status

            return Response(response_data)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
