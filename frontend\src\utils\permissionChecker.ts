/**
 * Permission-Based Access Control Utility
 * 
 * This utility provides permission-only access control, eliminating the need for
 * hardcoded group lists. Access is determined solely by Django permissions.
 */

import { MENU_PERMISSIONS, FEATURE_PERMISSIONS, PermissionRequirement } from './permissionMappings';

export interface User {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  permissions: any[];
  role_names?: string[];
}

export interface AccessResult {
  hasAccess: boolean;
  reason?: string;
  matchedPermissions?: string[];
}

/**
 * Enhanced permission checker that handles various Django permission formats
 */
export class PermissionChecker {
  private user: User | null;

  constructor(user: User | null) {
    this.user = user;
  }

  /**
   * Check if user has a specific permission
   */
  hasPermission(permission: string): boolean {
    if (!this.user) return false;
    if (this.user.is_superuser) return true;

    if (!Array.isArray(this.user.permissions)) return false;

    return this.user.permissions.some((perm: any) => {
      // Handle permission objects with codename and app_label
      if (typeof perm === 'object' && perm.codename) {
        const fullPermission = `${perm.app_label || 'unknown'}.${perm.codename}`;
        
        // Exact matches
        if (fullPermission === permission) return true;
        if (perm.codename === permission) return true;
        
        // Handle permission format variations
        if (permission.includes('.')) {
          const [app, codename] = permission.split('.');
          if (perm.codename === codename) return true;
          if (perm.app_label === app && perm.codename === codename) return true;
        }
        
        // Flexible matching for common patterns
        return this.flexiblePermissionMatch(permission, perm);
      }
      
      // Handle string permissions
      if (typeof perm === 'string') {
        return perm === permission;
      }
      
      return false;
    });
  }

  /**
   * Flexible permission matching for common patterns
   */
  private flexiblePermissionMatch(requestedPermission: string, userPermission: any): boolean {
    const requested = requestedPermission.toLowerCase();
    const userCodename = userPermission.codename?.toLowerCase() || '';
    const userAppLabel = userPermission.app_label?.toLowerCase() || '';

    // Direct codename match
    if (userCodename === requested) return true;

    // Pattern matching for related permissions
    const patterns = [
      // View permissions
      { request: /view_?(\w+)/, user: /view_?\1/ },
      { request: /(\w+)\.view_?(\w+)/, user: /view_?\2/ },
      
      // Change permissions
      { request: /change_?(\w+)/, user: /change_?\1/ },
      { request: /(\w+)\.change_?(\w+)/, user: /change_?\2/ },
      
      // Add permissions
      { request: /add_?(\w+)/, user: /add_?\1/ },
      { request: /(\w+)\.add_?(\w+)/, user: /add_?\2/ },
      
      // Delete permissions
      { request: /delete_?(\w+)/, user: /delete_?\1/ },
      { request: /(\w+)\.delete_?(\w+)/, user: /delete_?\2/ },
      
      // Service-related patterns
      { request: /service/, user: /service/ },
      { request: /document/, user: /document/ },
      { request: /certificate/, user: /certificate/ },
      { request: /graduate/, user: /graduate/ },
      { request: /college/, user: /college/ },
      { request: /department/, user: /department/ },
      { request: /program/, user: /program/ },
      { request: /announcement/, user: /announcement/ }
    ];

    return patterns.some(pattern => {
      if (pattern.request.test(requested) && pattern.user.test(userCodename)) {
        return true;
      }
      return false;
    });
  }

  /**
   * Check if user has any of the specified permissions
   */
  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  /**
   * Check if user has all of the specified permissions
   */
  hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(permission));
  }

  /**
   * Check access based on permission requirements
   */
  checkAccess(requirements: PermissionRequirement): AccessResult {
    // Check basic requirements
    if (requirements.requireAuth && !this.user) {
      return { hasAccess: false, reason: 'Authentication required' };
    }

    if (requirements.requireActive && this.user && !this.user.is_active) {
      return { hasAccess: false, reason: 'Account inactive' };
    }

    if (requirements.requireStaff && this.user && !this.user.is_staff) {
      return { hasAccess: false, reason: 'Staff access required' };
    }

    // Superuser bypass (unless custom check is provided)
    if (this.user?.is_superuser && !requirements.customCheck) {
      return { hasAccess: true, reason: 'Superuser access' };
    }

    // Custom validation
    if (requirements.customCheck && !requirements.customCheck(this.user)) {
      return { hasAccess: false, reason: 'Custom validation failed' };
    }

    // Permission checking
    if (requirements.permissions && requirements.permissions.length > 0) {
      const matchedPermissions: string[] = [];
      
      const hasRequiredPermissions = requirements.requireAll
        ? requirements.permissions.every(permission => {
            const hasIt = this.hasPermission(permission);
            if (hasIt) matchedPermissions.push(permission);
            return hasIt;
          })
        : requirements.permissions.some(permission => {
            const hasIt = this.hasPermission(permission);
            if (hasIt) matchedPermissions.push(permission);
            return hasIt;
          });

      if (!hasRequiredPermissions) {
        return {
          hasAccess: false,
          reason: `Missing required permissions: ${requirements.permissions.join(', ')}`,
          matchedPermissions
        };
      }

      return {
        hasAccess: true,
        reason: `Permission granted`,
        matchedPermissions
      };
    }

    // If no specific permissions required, grant access
    return { hasAccess: true, reason: 'No specific permissions required' };
  }

  /**
   * Check menu access
   */
  checkMenuAccess(menuKey: string): AccessResult {
    const requirements = MENU_PERMISSIONS[menuKey];
    
    if (!requirements) {
      // If no requirements defined, allow access for authenticated users
      return this.checkAccess({
        permissions: [],
        requireAuth: true,
        description: `No specific requirements for ${menuKey}`
      });
    }

    return this.checkAccess(requirements);
  }

  /**
   * Check feature access
   */
  checkFeatureAccess(featureKey: string): AccessResult {
    const requirements = FEATURE_PERMISSIONS[featureKey];
    
    if (!requirements) {
      // If no requirements defined, allow access for authenticated users
      return this.checkAccess({
        permissions: [],
        requireAuth: true,
        description: `No specific requirements for ${featureKey}`
      });
    }

    return this.checkAccess(requirements);
  }

  /**
   * Get detailed feature access information
   */
  getFeatureAccessDetails(featureKey: string) {
    const requirements = FEATURE_PERMISSIONS[featureKey];
    if (!requirements) {
      return {
        canView: true,
        canEdit: false,
        canCreate: false,
        canDelete: false,
        canManage: false,
        hasFullAccess: false,
        accessLevel: 'read' as const,
        deniedReason: 'No specific requirements defined'
      };
    }

    const viewPermissions = requirements.permissions.filter(p => 
      p.includes('view') || p.includes('read')
    );
    const editPermissions = requirements.permissions.filter(p => 
      p.includes('change') || p.includes('edit') || p.includes('update')
    );
    const createPermissions = requirements.permissions.filter(p => 
      p.includes('add') || p.includes('create')
    );
    const deletePermissions = requirements.permissions.filter(p => 
      p.includes('delete') || p.includes('remove')
    );

    const canView = viewPermissions.length === 0 || this.hasAnyPermission(viewPermissions);
    const canEdit = editPermissions.length === 0 || this.hasAnyPermission(editPermissions);
    const canCreate = createPermissions.length === 0 || this.hasAnyPermission(createPermissions);
    const canDelete = deletePermissions.length === 0 || this.hasAnyPermission(deletePermissions);
    const canManage = canEdit && canCreate && canDelete;
    const hasFullAccess = canView && canEdit && canCreate && canDelete;

    let accessLevel: 'none' | 'read' | 'write' | 'admin' | 'full';
    if (!canView) {
      accessLevel = 'none';
    } else if (canView && !canEdit && !canCreate && !canDelete) {
      accessLevel = 'read';
    } else if (canView && (canEdit || canCreate) && !canDelete) {
      accessLevel = 'write';
    } else if (canView && canEdit && canCreate && canDelete && !canManage) {
      accessLevel = 'admin';
    } else {
      accessLevel = 'full';
    }

    const accessResult = this.checkFeatureAccess(featureKey);

    return {
      canView,
      canEdit,
      canCreate,
      canDelete,
      canManage,
      hasFullAccess,
      accessLevel,
      deniedReason: accessResult.hasAccess ? undefined : accessResult.reason
    };
  }

  /**
   * Get all accessible menu items
   */
  getAccessibleMenus(): string[] {
    return Object.keys(MENU_PERMISSIONS).filter(menuKey => {
      const result = this.checkMenuAccess(menuKey);
      return result.hasAccess;
    });
  }

  /**
   * Get all accessible features
   */
  getAccessibleFeatures(): string[] {
    return Object.keys(FEATURE_PERMISSIONS).filter(featureKey => {
      const result = this.checkFeatureAccess(featureKey);
      return result.hasAccess;
    });
  }
}

/**
 * Create a permission checker instance
 */
export const createPermissionChecker = (user: User | null): PermissionChecker => {
  return new PermissionChecker(user);
};

export default PermissionChecker;
